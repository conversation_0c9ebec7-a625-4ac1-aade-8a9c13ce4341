{"name": "@graphql-yoga/typed-event-target", "version": "3.0.2", "description": "", "sideEffects": false, "dependencies": {"@repeaterjs/repeater": "^3.0.4", "tslib": "^2.8.1"}, "repository": {"type": "git", "url": "https://github.com/dotansimha/graphql-yoga.git", "directory": "packages/event-target/typed-event-target"}, "keywords": ["pubsub", "graphql", "event", "subscription"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}