import {
  SmallSpinnerIcon
} from "./chunk-XWG3L6QC.mjs";
import {
  __async
} from "./chunk-MRXNTQOX.mjs";

// src/components/chat/Suggestion.tsx
import {
  extract
} from "@copilotkit/react-core";
import { actionParametersToJsonSchema } from "@copilotkit/shared";
import { CopilotRequestType } from "@copilotkit/runtime-client-gql";
import { jsx } from "react/jsx-runtime";
function Suggestion({ title, onClick, partial, className }) {
  return /* @__PURE__ */ jsx(
    "button",
    {
      disabled: partial,
      onClick: (e) => {
        e.preventDefault();
        onClick();
      },
      className: className || (partial ? "suggestion loading" : "suggestion"),
      "data-test-id": "suggestion",
      children: partial ? SmallSpinnerIcon : /* @__PURE__ */ jsx("span", { children: title })
    }
  );
}
var reloadSuggestions = (context, chatSuggestionConfiguration, setCurrentSuggestions, abortControllerRef) => __async(void 0, null, function* () {
  const abortController = abortControllerRef.current;
  const tools = JSON.stringify(
    Object.values(context.actions).map((action) => ({
      name: action.name,
      description: action.description,
      jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters))
    }))
  );
  const allSuggestions = [];
  for (const config of Object.values(chatSuggestionConfiguration)) {
    try {
      const numOfSuggestionsInstructions = config.minSuggestions === 0 ? `Produce up to ${config.maxSuggestions} suggestions. If there are no highly relevant suggestions you can think of, provide an empty array.` : `Produce between ${config.minSuggestions} and ${config.maxSuggestions} suggestions.`;
      const result = yield extract({
        context,
        instructions: "Suggest what the user could say next. Provide clear, highly relevant suggestions. Do not literally suggest function calls. ",
        data: config.instructions + "\n\n" + numOfSuggestionsInstructions + "\n\nAvailable tools: " + tools + "\n\n",
        requestType: CopilotRequestType.Task,
        parameters: [
          {
            name: "suggestions",
            type: "object[]",
            attributes: [
              {
                name: "title",
                description: "The title of the suggestion. This is shown as a button and should be short.",
                type: "string"
              },
              {
                name: "message",
                description: "The message to send when the suggestion is clicked. This should be a clear, complete sentence and will be sent as an instruction to the AI.",
                type: "string"
              }
            ]
          }
        ],
        include: {
          messages: true,
          readable: true
        },
        abortSignal: abortController == null ? void 0 : abortController.signal,
        stream: ({ status, args }) => {
          const suggestions = args.suggestions || [];
          const newSuggestions = [];
          for (let i = 0; i < suggestions.length; i++) {
            if (config.maxSuggestions !== void 0 && i >= config.maxSuggestions) {
              break;
            }
            const { title, message } = suggestions[i];
            const partial = i == suggestions.length - 1 && status !== "complete";
            newSuggestions.push({
              title,
              message,
              partial,
              className: config.className
            });
          }
          setCurrentSuggestions([...allSuggestions, ...newSuggestions]);
        }
      });
      allSuggestions.push(...result.suggestions);
    } catch (error) {
      console.error("Error loading suggestions", error);
    }
  }
  if (abortControllerRef.current === abortController) {
    abortControllerRef.current = null;
  }
});

export {
  Suggestion,
  reloadSuggestions
};
//# sourceMappingURL=chunk-IMBPSLL4.mjs.map