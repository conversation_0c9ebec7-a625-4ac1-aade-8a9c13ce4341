"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useDeferStream = useDeferStream;
const graphql_1 = require("graphql");
const utils_1 = require("@graphql-tools/utils");
const defer_stream_directive_label_js_1 = require("./validations/defer-stream-directive-label.js");
const defer_stream_directive_on_root_field_js_1 = require("./validations/defer-stream-directive-on-root-field.js");
const overlapping_fields_can_be_merged_js_1 = require("./validations/overlapping-fields-can-be-merged.js");
const stream_directive_on_list_field_js_1 = require("./validations/stream-directive-on-list-field.js");
function useDeferStream() {
    return {
        onSchemaChange: ({ schema, replaceSchema, }) => {
            const directives = [];
            const deferInSchema = schema.getDirective('defer');
            if (deferInSchema == null) {
                directives.push(utils_1.GraphQLDeferDirective);
            }
            const streamInSchema = schema.getDirective('stream');
            if (streamInSchema == null) {
                directives.push(utils_1.GraphQLStreamDirective);
            }
            if (directives.length) {
                replaceSchema(new graphql_1.GraphQLSchema({
                    ...schema.toConfig(),
                    directives: [...schema.getDirectives(), ...directives],
                }));
            }
        },
        onValidate: ({ params, addValidationRule, }) => {
            // Just to make TS happy because rules are always defined by useEngine.
            params.rules ||= [];
            params.rules = params.rules.filter(rule => rule.name !== 'OverlappingFieldsCanBeMergedRule');
            addValidationRule(overlapping_fields_can_be_merged_js_1.OverlappingFieldsCanBeMergedRule);
            addValidationRule(defer_stream_directive_label_js_1.DeferStreamDirectiveLabelRule);
            addValidationRule(defer_stream_directive_on_root_field_js_1.DeferStreamDirectiveOnRootFieldRule);
            addValidationRule(stream_directive_on_list_field_js_1.StreamDirectiveOnListFieldRule);
        },
    };
}
