{"version": 3, "sources": ["../../src/types/index.ts"], "sourcesContent": ["export type { DocumentPointer } from \"./document-pointer\";\nexport type { SystemMessageFunction } from \"./system-message\";\nexport type {\n  ActionRenderProps,\n  ActionRenderPropsNoArgs,\n  ActionRenderPropsWait,\n  ActionRenderPropsNoArgsWait,\n  FrontendAction,\n  FrontendActionAvailability,\n  RenderFunctionStatus,\n  CatchAllActionRenderProps,\n  CatchAllFrontendAction,\n} from \"./frontend-action\";\n\nexport type { CopilotChatSuggestionConfiguration } from \"./chat-suggestion-configuration\";\nexport * from \"./crew\";\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}