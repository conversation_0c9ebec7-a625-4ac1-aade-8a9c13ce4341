"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/client/conversion.ts
var conversion_exports = {};
__export(conversion_exports, {
  convertGqlOutputToMessages: () => convertGqlOutputToMessages,
  convertMessagesToGqlInput: () => convertMessagesToGqlInput,
  filterAdjacentAgentStateMessages: () => filterAdjacentAgentStateMessages,
  filterAgentStateMessages: () => filterAgentStateMessages,
  loadMessagesFromJsonRepresentation: () => loadMessagesFromJsonRepresentation
});
module.exports = __toCommonJS(conversion_exports);

// src/client/types.ts
var import_shared = require("@copilotkit/shared");
var import_shared2 = require("@copilotkit/shared");
var Message = class {
  constructor(props) {
    props.id ?? (props.id = (0, import_shared.randomId)());
    props.status ?? (props.status = { code: "Success" /* Success */ });
    props.createdAt ?? (props.createdAt = /* @__PURE__ */ new Date());
    Object.assign(this, props);
  }
  isTextMessage() {
    return this.type === "TextMessage";
  }
  isActionExecutionMessage() {
    return this.type === "ActionExecutionMessage";
  }
  isResultMessage() {
    return this.type === "ResultMessage";
  }
  isAgentStateMessage() {
    return this.type === "AgentStateMessage";
  }
  isImageMessage() {
    return this.type === "ImageMessage";
  }
};
var TextMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "TextMessage";
  }
};
var ActionExecutionMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "ActionExecutionMessage";
  }
};
var ResultMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "ResultMessage";
  }
  static decodeResult(result) {
    return (0, import_shared2.parseJson)(result, result);
  }
  static encodeResult(result) {
    if (result === void 0) {
      return "";
    } else if (typeof result === "string") {
      return result;
    } else {
      return JSON.stringify(result);
    }
  }
};
var AgentStateMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "AgentStateMessage";
  }
};
var ImageMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "ImageMessage";
  }
};

// src/client/conversion.ts
var import_untruncate_json = __toESM(require("untruncate-json"));
var import_shared3 = require("@copilotkit/shared");
function filterAgentStateMessages(messages) {
  return messages.filter((message) => !message.isAgentStateMessage());
}
function convertMessagesToGqlInput(messages) {
  return messages.map((message) => {
    if (message.isTextMessage()) {
      return {
        id: message.id,
        createdAt: message.createdAt,
        textMessage: {
          content: message.content,
          role: message.role,
          parentMessageId: message.parentMessageId
        }
      };
    } else if (message.isActionExecutionMessage()) {
      return {
        id: message.id,
        createdAt: message.createdAt,
        actionExecutionMessage: {
          name: message.name,
          arguments: JSON.stringify(message.arguments),
          parentMessageId: message.parentMessageId
        }
      };
    } else if (message.isResultMessage()) {
      return {
        id: message.id,
        createdAt: message.createdAt,
        resultMessage: {
          result: message.result,
          actionExecutionId: message.actionExecutionId,
          actionName: message.actionName
        }
      };
    } else if (message.isAgentStateMessage()) {
      return {
        id: message.id,
        createdAt: message.createdAt,
        agentStateMessage: {
          threadId: message.threadId,
          role: message.role,
          agentName: message.agentName,
          nodeName: message.nodeName,
          runId: message.runId,
          active: message.active,
          running: message.running,
          state: JSON.stringify(message.state)
        }
      };
    } else if (message.isImageMessage()) {
      return {
        id: message.id,
        createdAt: message.createdAt,
        imageMessage: {
          format: message.format,
          bytes: message.bytes,
          role: message.role,
          parentMessageId: message.parentMessageId
        }
      };
    } else {
      throw new Error("Unknown message type");
    }
  });
}
function filterAdjacentAgentStateMessages(messages) {
  const filteredMessages = [];
  messages.forEach((message, i) => {
    if (message.__typename !== "AgentStateMessageOutput") {
      filteredMessages.push(message);
    } else {
      const prevAgentStateMessageIndex = filteredMessages.findIndex(
        // TODO: also check runId
        (m) => m.__typename === "AgentStateMessageOutput" && m.agentName === message.agentName
      );
      if (prevAgentStateMessageIndex === -1) {
        filteredMessages.push(message);
      } else {
        filteredMessages[prevAgentStateMessageIndex] = message;
      }
    }
  });
  return filteredMessages;
}
function convertGqlOutputToMessages(messages) {
  return messages.map((message) => {
    if (message.__typename === "TextMessageOutput") {
      return new TextMessage({
        id: message.id,
        role: message.role,
        content: message.content.join(""),
        parentMessageId: message.parentMessageId,
        createdAt: /* @__PURE__ */ new Date(),
        status: message.status || { code: "Pending" /* Pending */ }
      });
    } else if (message.__typename === "ActionExecutionMessageOutput") {
      return new ActionExecutionMessage({
        id: message.id,
        name: message.name,
        arguments: getPartialArguments(message.arguments),
        parentMessageId: message.parentMessageId,
        createdAt: /* @__PURE__ */ new Date(),
        status: message.status || { code: "Pending" /* Pending */ }
      });
    } else if (message.__typename === "ResultMessageOutput") {
      return new ResultMessage({
        id: message.id,
        result: message.result,
        actionExecutionId: message.actionExecutionId,
        actionName: message.actionName,
        createdAt: /* @__PURE__ */ new Date(),
        status: message.status || { code: "Pending" /* Pending */ }
      });
    } else if (message.__typename === "AgentStateMessageOutput") {
      return new AgentStateMessage({
        id: message.id,
        threadId: message.threadId,
        role: message.role,
        agentName: message.agentName,
        nodeName: message.nodeName,
        runId: message.runId,
        active: message.active,
        running: message.running,
        state: (0, import_shared3.parseJson)(message.state, {}),
        createdAt: /* @__PURE__ */ new Date()
      });
    } else if (message.__typename === "ImageMessageOutput") {
      return new ImageMessage({
        id: message.id,
        format: message.format,
        bytes: message.bytes,
        role: message.role,
        parentMessageId: message.parentMessageId,
        createdAt: /* @__PURE__ */ new Date(),
        status: message.status || { code: "Pending" /* Pending */ }
      });
    }
    throw new Error("Unknown message type");
  });
}
function loadMessagesFromJsonRepresentation(json) {
  const result = [];
  for (const item of json) {
    if ("content" in item) {
      result.push(
        new TextMessage({
          id: item.id,
          role: item.role,
          content: item.content,
          parentMessageId: item.parentMessageId,
          createdAt: item.createdAt || /* @__PURE__ */ new Date(),
          status: item.status || { code: "Success" /* Success */ }
        })
      );
    } else if ("arguments" in item) {
      result.push(
        new ActionExecutionMessage({
          id: item.id,
          name: item.name,
          arguments: item.arguments,
          parentMessageId: item.parentMessageId,
          createdAt: item.createdAt || /* @__PURE__ */ new Date(),
          status: item.status || { code: "Success" /* Success */ }
        })
      );
    } else if ("result" in item) {
      result.push(
        new ResultMessage({
          id: item.id,
          result: item.result,
          actionExecutionId: item.actionExecutionId,
          actionName: item.actionName,
          createdAt: item.createdAt || /* @__PURE__ */ new Date(),
          status: item.status || { code: "Success" /* Success */ }
        })
      );
    } else if ("state" in item) {
      result.push(
        new AgentStateMessage({
          id: item.id,
          threadId: item.threadId,
          role: item.role,
          agentName: item.agentName,
          nodeName: item.nodeName,
          runId: item.runId,
          active: item.active,
          running: item.running,
          state: item.state,
          createdAt: item.createdAt || /* @__PURE__ */ new Date()
        })
      );
    } else if ("format" in item && "bytes" in item) {
      result.push(
        new ImageMessage({
          id: item.id,
          format: item.format,
          bytes: item.bytes,
          role: item.role,
          parentMessageId: item.parentMessageId,
          createdAt: item.createdAt || /* @__PURE__ */ new Date(),
          status: item.status || { code: "Success" /* Success */ }
        })
      );
    }
  }
  return result;
}
function getPartialArguments(args) {
  try {
    if (!args.length)
      return {};
    return JSON.parse((0, import_untruncate_json.default)(args.join("")));
  } catch (e) {
    return {};
  }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  convertGqlOutputToMessages,
  convertMessagesToGqlInput,
  filterAdjacentAgentStateMessages,
  filterAgentStateMessages,
  loadMessagesFromJsonRepresentation
});
//# sourceMappingURL=conversion.js.map