"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/use-flat-category-store.ts
var use_flat_category_store_exports = {};
__export(use_flat_category_store_exports, {
  default: () => use_flat_category_store_default
});
module.exports = __toCommonJS(use_flat_category_store_exports);
var import_react = require("react");
var import_shared = require("@copilotkit/shared");
var useFlatCategoryStore = () => {
  const [elements, dispatch] = (0, import_react.useReducer)(flatCategoryStoreReducer, /* @__PURE__ */ new Map());
  const addElement = (0, import_react.useCallback)((value, categories) => {
    const newId = (0, import_shared.randomId)();
    dispatch({
      type: "ADD_ELEMENT",
      value,
      id: newId,
      categories
    });
    return newId;
  }, []);
  const removeElement = (0, import_react.useCallback)((id) => {
    dispatch({ type: "REMOVE_ELEMENT", id });
  }, []);
  const allElements = (0, import_react.useCallback)(
    (categories) => {
      const categoriesSet = new Set(categories);
      const result = [];
      elements.forEach((element) => {
        if (setsHaveIntersection(categoriesSet, element.categories)) {
          result.push(element.value);
        }
      });
      return result;
    },
    [elements]
  );
  return { addElement, removeElement, allElements };
};
var use_flat_category_store_default = useFlatCategoryStore;
function flatCategoryStoreReducer(state, action) {
  switch (action.type) {
    case "ADD_ELEMENT": {
      const { value, id, categories } = action;
      const newElement = {
        id,
        value,
        categories: new Set(categories)
      };
      const newState = new Map(state);
      newState.set(id, newElement);
      return newState;
    }
    case "REMOVE_ELEMENT": {
      const newState = new Map(state);
      newState.delete(action.id);
      return newState;
    }
    default:
      return state;
  }
}
function setsHaveIntersection(setA, setB) {
  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];
  for (let item of smallerSet) {
    if (largerSet.has(item)) {
      return true;
    }
  }
  return false;
}
//# sourceMappingURL=use-flat-category-store.js.map