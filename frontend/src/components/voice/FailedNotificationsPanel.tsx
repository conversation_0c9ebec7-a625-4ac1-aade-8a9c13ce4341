'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, AlertTriangle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { MultiTenantTable, TableColumn } from '@/components/ui/MultiTenantTable';
import { useAuthenticatedFetch } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';

export interface FailedNotification {
  id: string;
  tenant_id: string;
  notification_type: 'webhook' | 'email' | 'sms' | 'call_failed' | 'system_error';
  status: 'failed' | 'retrying' | 'abandoned' | 'resolved';
  error_message: string;
  retry_count: number;
  max_retries: number;
  next_retry_at?: string;
  target_endpoint?: string;
  payload?: Record<string, any>;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
}

interface FailedNotificationsPanelProps {
  showTenantColumn?: boolean;
}

export default function FailedNotificationsPanel({ showTenantColumn = false }: FailedNotificationsPanelProps) {
  const [notifications, setNotifications] = useState<FailedNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    failed: 0,
    retrying: 0,
    resolved: 0,
  });
  
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { toast } = useToast();

  const columns: TableColumn<FailedNotification>[] = [
    {
      key: 'notification_type',
      header: 'Type',
      render: (value) => (
        <div className="flex items-center gap-2">
          {value === 'webhook' && <AlertTriangle className="h-4 w-4 text-orange-600" />}
          {value === 'email' && <AlertTriangle className="h-4 w-4 text-blue-600" />}
          {value === 'sms' && <AlertTriangle className="h-4 w-4 text-green-600" />}
          {value === 'call_failed' && <XCircle className="h-4 w-4 text-red-600" />}
          {value === 'system_error' && <AlertTriangle className="h-4 w-4 text-red-600" />}
          <span className="capitalize">{value.replace('_', ' ')}</span>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Status',
      render: (value) => {
        const statusConfig = {
          failed: { color: 'bg-red-100 text-red-800', icon: XCircle },
          retrying: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
          abandoned: { color: 'bg-gray-100 text-gray-800', icon: XCircle },
          resolved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
        };
        const config = statusConfig[value as keyof typeof statusConfig];
        const Icon = config?.icon || AlertTriangle;
        
        return (
          <Badge className={config?.color || 'bg-gray-100 text-gray-800'}>
            <Icon className="h-3 w-3 mr-1" />
            {value}
          </Badge>
        );
      },
    },
    {
      key: 'error_message',
      header: 'Error',
      render: (value) => (
        <div className="max-w-xs truncate" title={value}>
          {value}
        </div>
      ),
    },
    {
      key: 'retry_count',
      header: 'Retries',
      render: (value, row) => `${value}/${row.max_retries}`,
    },
    {
      key: 'next_retry_at',
      header: 'Next Retry',
      render: (value) => {
        if (!value) return '-';
        const nextRetry = new Date(value);
        const now = new Date();
        if (nextRetry < now) return 'Overdue';
        return formatDistanceToNow(nextRetry, { addSuffix: true });
      },
    },
    {
      key: 'created_at',
      header: 'Created',
      render: (value) => (
        <div className="text-sm">
          <div>{new Date(value).toLocaleDateString()}</div>
          <div className="text-muted-foreground">
            {formatDistanceToNow(new Date(value), { addSuffix: true })}
          </div>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex gap-2">
          {row.status === 'failed' && (
            <Button size="sm" variant="outline" onClick={() => handleRetryNotification(row.id)}>
              Retry
            </Button>
          )}
          {row.status !== 'resolved' && (
            <Button size="sm" variant="outline" onClick={() => handleMarkResolved(row.id)}>
              Mark Resolved
            </Button>
          )}
        </div>
      ),
    },
  ];

  const handleRetryNotification = async (notificationId: string) => {
    if (!isReady) return;

    try {
      await authedFetch(`/api/voice/notifications/${notificationId}/retry`, {
        method: 'POST',
      });
      
      toast({
        title: 'Success',
        description: 'Notification retry initiated',
      });
      
      fetchNotifications();
    } catch (error) {
      console.error('Error retrying notification:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to retry notification',
      });
    }
  };

  const handleMarkResolved = async (notificationId: string) => {
    if (!isReady) return;

    try {
      await authedFetch(`/api/voice/notifications/${notificationId}/resolve`, {
        method: 'POST',
      });
      
      toast({
        title: 'Success',
        description: 'Notification marked as resolved',
      });
      
      fetchNotifications();
    } catch (error) {
      console.error('Error marking notification as resolved:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to mark notification as resolved',
      });
    }
  };

  const fetchNotifications = async () => {
    if (!isReady) return;

    try {
      setIsLoading(true);
      const response = await authedFetch<{ 
        notifications: FailedNotification[];
        stats: typeof stats;
      }>('/api/voice/notifications');
      
      setNotifications(response.notifications || []);
      setStats(response.stats || stats);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to fetch failed notifications',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [isReady]);

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Retrying</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.retrying}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
          </CardContent>
        </Card>
      </div>

      {/* Alert for critical failures */}
      {stats.failed > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You have {stats.failed} failed notification{stats.failed !== 1 ? 's' : ''} that require attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Notifications Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Failed Notifications</CardTitle>
              <CardDescription>
                Monitor and manage failed voice system notifications
              </CardDescription>
            </div>
            <Button onClick={fetchNotifications} disabled={isLoading} variant="outline">
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <MultiTenantTable
            rows={notifications}
            columns={columns}
            showTenantColumn={showTenantColumn}
            isLoading={isLoading}
            emptyMessage="No failed notifications found"
          />
        </CardContent>
      </Card>
    </div>
  );
}
