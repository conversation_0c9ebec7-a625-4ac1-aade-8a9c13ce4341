{"version": 3, "sources": ["../../../src/graphql/@generated/fragment-masking.ts"], "sourcesContent": ["/* eslint-disable */\nimport type { ResultOf, DocumentTypeDecoration, TypedDocumentNode } from '@graphql-typed-document-node/core';\nimport type { FragmentDefinitionNode } from 'graphql';\nimport type { Incremental } from './graphql';\n\n\nexport type FragmentType<TDocumentType extends DocumentTypeDecoration<any, any>> = TDocumentType extends DocumentTypeDecoration<\n  infer TType,\n  any\n>\n  ? [TType] extends [{ ' $fragmentName'?: infer TKey }]\n    ? TK<PERSON> extends string\n      ? { ' $fragmentRefs'?: { [key in TKey]: TType } }\n      : never\n    : never\n  : never;\n\n// return non-nullable if `fragmentType` is non-nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>>\n): TType;\n// return nullable if `fragmentType` is undefined\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | undefined\n): TType | undefined;\n// return nullable if `fragmentType` is nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | null\n): TType | null;\n// return nullable if `fragmentType` is nullable or undefined\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | null | undefined\n): TType | null | undefined;\n// return array of non-nullable if `fragmentType` is array of non-nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: Array<FragmentType<DocumentTypeDecoration<TType, any>>>\n): Array<TType>;\n// return array of nullable if `fragmentType` is array of nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: Array<FragmentType<DocumentTypeDecoration<TType, any>>> | null | undefined\n): Array<TType> | null | undefined;\n// return readonly array of non-nullable if `fragmentType` is array of non-nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: ReadonlyArray<FragmentType<DocumentTypeDecoration<TType, any>>>\n): ReadonlyArray<TType>;\n// return readonly array of nullable if `fragmentType` is array of nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: ReadonlyArray<FragmentType<DocumentTypeDecoration<TType, any>>> | null | undefined\n): ReadonlyArray<TType> | null | undefined;\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | Array<FragmentType<DocumentTypeDecoration<TType, any>>> | ReadonlyArray<FragmentType<DocumentTypeDecoration<TType, any>>> | null | undefined\n): TType | Array<TType> | ReadonlyArray<TType> | null | undefined {\n  return fragmentType as any;\n}\n\n\nexport function makeFragmentData<\n  F extends DocumentTypeDecoration<any, any>,\n  FT extends ResultOf<F>\n>(data: FT, _fragment: F): FragmentType<F> {\n  return data as FragmentType<F>;\n}\nexport function isFragmentReady<TQuery, TFrag>(\n  queryNode: DocumentTypeDecoration<TQuery, any>,\n  fragmentNode: TypedDocumentNode<TFrag>,\n  data: FragmentType<TypedDocumentNode<Incremental<TFrag>, any>> | null | undefined\n): data is FragmentType<typeof fragmentNode> {\n  const deferredFields = (queryNode as { __meta__?: { deferredFields: Record<string, (keyof TFrag)[]> } }).__meta__\n    ?.deferredFields;\n\n  if (!deferredFields) return true;\n\n  const fragDef = fragmentNode.definitions[0] as FragmentDefinitionNode | undefined;\n  const fragName = fragDef?.name?.value;\n\n  const fields = (fragName && deferredFields[fragName]) || [];\n  return fields.length > 0 && fields.every(field => data && field in data);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyDO,SAAS,YACd,eACA,cACgE;AAChE,SAAO;AACT;AAGO,SAAS,iBAGd,MAAU,WAA+B;AACzC,SAAO;AACT;AACO,SAAS,gBACd,WACA,cACA,MAC2C;AA3E7C;AA4EE,QAAM,kBAAkB,eAAiF,aAAjF,mBACpB;AAEJ,MAAI,CAAC;AAAgB,WAAO;AAE5B,QAAM,UAAU,aAAa,YAAY,CAAC;AAC1C,QAAM,YAAW,wCAAS,SAAT,mBAAe;AAEhC,QAAM,SAAU,YAAY,eAAe,QAAQ,KAAM,CAAC;AAC1D,SAAO,OAAO,SAAS,KAAK,OAAO,MAAM,WAAS,QAAQ,SAAS,IAAI;AACzE;", "names": []}