import { b as CreateCopilotRuntimeServerOptions, G as GraphQLContext } from '../../shared-0c31d7c5.js';
export { e as CommonConfig, C as CopilotRequestContextProperties, a as addCustomHeaderPlugin, d as buildSchema, c as createContext, g as getCommonConfig } from '../../shared-0c31d7c5.js';
import * as graphql_yoga from 'graphql-yoga';
import { YogaServerInstance } from 'graphql-yoga';
export { copilotRuntimeNodeHttpEndpoint } from './node-http/index.js';
export { copilotRuntimeNodeExpressEndpoint } from './node-express/index.js';
export { copilotRuntimeNestEndpoint } from './nest/index.js';
import 'graphql';
import 'pino';
import '@copilotkit/shared';
import '../../langserve-4a5c9217.js';
import '../../index-d4614f9b.js';
import '../../graphql/types/base/index.js';
import 'rxjs';
import '../cloud/index.js';
import '@ag-ui/client';

declare function copilotRuntimeNextJSAppRouterEndpoint(options: CreateCopilotRuntimeServerOptions): {
    handleRequest: graphql_yoga.YogaServerInstance<{}, Partial<GraphQLContext>>;
    GET: any;
    POST: any;
    OPTIONS: any;
};

declare const config: {
    api: {
        bodyParser: boolean;
    };
};
type CopilotRuntimeServerInstance<T> = YogaServerInstance<T, Partial<GraphQLContext>>;

declare function copilotRuntimeNextJSPagesRouterEndpoint(options: CreateCopilotRuntimeServerOptions): CopilotRuntimeServerInstance<GraphQLContext>;

export { CopilotRuntimeServerInstance, CreateCopilotRuntimeServerOptions, GraphQLContext, config, copilotRuntimeNextJSAppRouterEndpoint, copilotRuntimeNextJSPagesRouterEndpoint };
