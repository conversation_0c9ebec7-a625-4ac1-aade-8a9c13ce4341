{"version": 3, "sources": ["../src/hooks/use-coagent.ts"], "sourcesContent": ["/**\n * <Callout type=\"info\">\n *   Usage of this hook assumes some additional setup in your application, for more information\n *   on that see the CoAgents <span className=\"text-blue-500\">[getting started guide](/coagents/quickstart/langgraph)</span>.\n * </Callout>\n * <Frame className=\"my-12\">\n *   <img\n *     src=\"/images/coagents/SharedStateCoAgents.gif\"\n *     alt=\"CoAgents demonstration\"\n *     className=\"w-auto\"\n *   />\n * </Frame>\n *\n * This hook is used to integrate an agent into your application. With its use, you can\n * render and update the state of an agent, allowing for a dynamic and interactive experience.\n * We call these shared state experiences agentic copilots, or CoAgents for short.\n *\n * ## Usage\n *\n * ### Simple Usage\n *\n * ```tsx\n * import { useCoAgent } from \"@copilotkit/react-core\";\n *\n * type AgentState = {\n *   count: number;\n * }\n *\n * const agent = useCoAgent<AgentState>({\n *   name: \"my-agent\",\n *   initialState: {\n *     count: 0,\n *   },\n * });\n *\n * ```\n *\n * `useCoAgent` returns an object with the following properties:\n *\n * ```tsx\n * const {\n *   name,     // The name of the agent currently being used.\n *   nodeName, // The name of the current LangGraph node.\n *   state,    // The current state of the agent.\n *   setState, // A function to update the state of the agent.\n *   running,  // A boolean indicating if the agent is currently running.\n *   start,    // A function to start the agent.\n *   stop,     // A function to stop the agent.\n *   run,      // A function to re-run the agent. Takes a HintFunction to inform the agent why it is being re-run.\n * } = agent;\n * ```\n *\n * Finally we can leverage these properties to create reactive experiences with the agent!\n *\n * ```tsx\n * const { state, setState } = useCoAgent<AgentState>({\n *   name: \"my-agent\",\n *   initialState: {\n *     count: 0,\n *   },\n * });\n *\n * return (\n *   <div>\n *     <p>Count: {state.count}</p>\n *     <button onClick={() => setState({ count: state.count + 1 })}>Increment</button>\n *   </div>\n * );\n * ```\n *\n * This reactivity is bidirectional, meaning that changes to the state from the agent will be reflected in the UI and vice versa.\n *\n * ## Parameters\n * <PropertyReference name=\"options\" type=\"UseCoagentOptions<T>\" required>\n *   The options to use when creating the coagent.\n *   <PropertyReference name=\"name\" type=\"string\" required>\n *     The name of the agent to use.\n *   </PropertyReference>\n *   <PropertyReference name=\"initialState\" type=\"T | any\">\n *     The initial state of the agent.\n *   </PropertyReference>\n *   <PropertyReference name=\"state\" type=\"T | any\">\n *     State to manage externally if you are using this hook with external state management.\n *   </PropertyReference>\n *   <PropertyReference name=\"setState\" type=\"(newState: T | ((prevState: T | undefined) => T)) => void\">\n *     A function to update the state of the agent if you are using this hook with external state management.\n *   </PropertyReference>\n * </PropertyReference>\n */\n\nimport { useCallback, useEffect, useMemo, useRef } from \"react\";\nimport {\n  CopilotContextParams,\n  CopilotMessagesContextParams,\n  useCopilotContext,\n  useCopilotMessagesContext,\n} from \"../context\";\nimport { CoagentState } from \"../types/coagent-state\";\nimport { useCopilotChat } from \"./use-copilot-chat\";\nimport { Message } from \"@copilotkit/runtime-client-gql\";\nimport { useAsyncCallback } from \"../components/error-boundary/error-utils\";\nimport { useToast } from \"../components/toast/toast-provider\";\nimport { useCopilotRuntimeClient } from \"./use-copilot-runtime-client\";\nimport { parseJson } from \"@copilotkit/shared\";\n\ninterface UseCoagentOptionsBase {\n  /**\n   * The name of the agent being used.\n   */\n  name: string;\n  /**\n   * @deprecated - use \"config.configurable\"\n   * Config to pass to a LangGraph Agent\n   */\n  configurable?: Record<string, any>;\n  /**\n   * Config to pass to a LangGraph Agent\n   */\n  config?: {\n    configurable?: Record<string, any>;\n    [key: string]: any;\n  };\n}\n\ninterface WithInternalStateManagementAndInitial<T> extends UseCoagentOptionsBase {\n  /**\n   * The initial state of the agent.\n   */\n  initialState: T;\n}\n\ninterface WithInternalStateManagement extends UseCoagentOptionsBase {\n  /**\n   * Optional initialState with default type any\n   */\n  initialState?: any;\n}\n\ninterface WithExternalStateManagement<T> extends UseCoagentOptionsBase {\n  /**\n   * The current state of the agent.\n   */\n  state: T;\n  /**\n   * A function to update the state of the agent.\n   */\n  setState: (newState: T | ((prevState: T | undefined) => T)) => void;\n}\n\ntype UseCoagentOptions<T> =\n  | WithInternalStateManagementAndInitial<T>\n  | WithInternalStateManagement\n  | WithExternalStateManagement<T>;\n\nexport interface UseCoagentReturnType<T> {\n  /**\n   * The name of the agent being used.\n   */\n  name: string;\n  /**\n   * The name of the current LangGraph node.\n   */\n  nodeName?: string;\n  /**\n   * The ID of the thread the agent is running in.\n   */\n  threadId?: string;\n  /**\n   * A boolean indicating if the agent is currently running.\n   */\n  running: boolean;\n  /**\n   * The current state of the agent.\n   */\n  state: T;\n  /**\n   * A function to update the state of the agent.\n   */\n  setState: (newState: T | ((prevState: T | undefined) => T)) => void;\n  /**\n   * A function to start the agent.\n   */\n  start: () => void;\n  /**\n   * A function to stop the agent.\n   */\n  stop: () => void;\n  /**\n   * A function to re-run the agent. The hint function can be used to provide a hint to the agent\n   * about why it is being re-run again.\n   */\n  run: (hint?: HintFunction) => Promise<void>;\n}\n\nexport interface HintFunctionParams {\n  /**\n   * The previous state of the agent.\n   */\n  previousState: any;\n  /**\n   * The current state of the agent.\n   */\n  currentState: any;\n}\n\nexport type HintFunction = (params: HintFunctionParams) => Message | undefined;\n\n/**\n * This hook is used to integrate an agent into your application. With its use, you can\n * render and update the state of the agent, allowing for a dynamic and interactive experience.\n * We call these shared state experiences \"agentic copilots\". To get started using agentic copilots, which\n * we refer to as CoAgents, checkout the documentation at https://docs.copilotkit.ai/coagents/quickstart/langgraph.\n */\nexport function useCoAgent<T = any>(options: UseCoagentOptions<T>): UseCoagentReturnType<T> {\n  const generalContext = useCopilotContext();\n  const { availableAgents } = generalContext;\n  const { addToast } = useToast();\n  const lastLoadedThreadId = useRef<string>();\n  const lastLoadedState = useRef<any>();\n\n  const { name } = options;\n  useEffect(() => {\n    if (availableAgents?.length && !availableAgents.some((a) => a.name === name)) {\n      const message = `(useCoAgent): Agent \"${name}\" not found. Make sure the agent exists and is properly configured.`;\n      console.warn(message);\n      addToast({ type: \"warning\", message });\n    }\n  }, [availableAgents]);\n\n  const messagesContext = useCopilotMessagesContext();\n  const context = { ...generalContext, ...messagesContext };\n  const { coagentStates, coagentStatesRef, setCoagentStatesWithRef, threadId, copilotApiConfig } =\n    context;\n  const { appendMessage, runChatCompletion } = useCopilotChat();\n\n  const runtimeClient = useCopilotRuntimeClient({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    credentials: copilotApiConfig.credentials,\n  });\n\n  // if we manage state internally, we need to provide a function to set the state\n  const setState = useCallback(\n    (newState: T | ((prevState: T | undefined) => T)) => {\n      // coagentStatesRef.current || {}\n      let coagentState: CoagentState = getCoagentState({ coagentStates, name, options });\n      const updatedState =\n        typeof newState === \"function\" ? (newState as Function)(coagentState.state) : newState;\n\n      setCoagentStatesWithRef({\n        ...coagentStatesRef.current,\n        [name]: {\n          ...coagentState,\n          state: updatedState,\n        },\n      });\n    },\n    [coagentStates, name],\n  );\n\n  useEffect(() => {\n    const fetchAgentState = async () => {\n      if (!threadId || threadId === lastLoadedThreadId.current) return;\n\n      const result = await runtimeClient.loadAgentState({\n        threadId,\n        agentName: name,\n      });\n\n      const newState = result.data?.loadAgentState?.state;\n      if (newState === lastLoadedState.current) return;\n\n      if (result.data?.loadAgentState?.threadExists && newState && newState != \"{}\") {\n        lastLoadedState.current = newState;\n        lastLoadedThreadId.current = threadId;\n        const fetchedState = parseJson(newState, {});\n        isExternalStateManagement(options)\n          ? options.setState(fetchedState)\n          : setState(fetchedState);\n      }\n    };\n    void fetchAgentState();\n  }, [threadId]);\n\n  // Sync internal state with external state if state management is external\n  useEffect(() => {\n    if (isExternalStateManagement(options)) {\n      setState(options.state);\n    } else if (coagentStates[name] === undefined) {\n      setState(options.initialState === undefined ? {} : options.initialState);\n    }\n  }, [\n    isExternalStateManagement(options) ? JSON.stringify(options.state) : undefined,\n    // reset initialstate on reset\n    coagentStates[name] === undefined,\n  ]);\n\n  const runAgentCallback = useAsyncCallback(\n    async (hint?: HintFunction) => {\n      await runAgent(name, context, appendMessage, runChatCompletion, hint);\n    },\n    [name, context, appendMessage, runChatCompletion],\n  );\n\n  // Return the state and setState function\n  return useMemo(() => {\n    const coagentState = getCoagentState({ coagentStates, name, options });\n    return {\n      name,\n      nodeName: coagentState.nodeName,\n      threadId: coagentState.threadId,\n      running: coagentState.running,\n      state: coagentState.state,\n      setState: isExternalStateManagement(options) ? options.setState : setState,\n      start: () => startAgent(name, context),\n      stop: () => stopAgent(name, context),\n      run: runAgentCallback,\n    };\n  }, [name, coagentStates, options, setState, runAgentCallback]);\n}\n\nexport function startAgent(name: string, context: CopilotContextParams) {\n  const { setAgentSession } = context;\n  setAgentSession({\n    agentName: name,\n  });\n}\n\nexport function stopAgent(name: string, context: CopilotContextParams) {\n  const { agentSession, setAgentSession } = context;\n  if (agentSession && agentSession.agentName === name) {\n    setAgentSession(null);\n    context.setCoagentStates((prevAgentStates) => {\n      return {\n        ...prevAgentStates,\n        [name]: {\n          ...prevAgentStates[name],\n          running: false,\n          active: false,\n          threadId: undefined,\n          nodeName: undefined,\n          runId: undefined,\n        },\n      };\n    });\n  } else {\n    console.warn(`No agent session found for ${name}`);\n  }\n}\n\nexport async function runAgent(\n  name: string,\n  context: CopilotContextParams & CopilotMessagesContextParams,\n  appendMessage: (message: Message) => Promise<void>,\n  runChatCompletion: () => Promise<Message[]>,\n  hint?: HintFunction,\n) {\n  const { agentSession, setAgentSession } = context;\n  if (!agentSession || agentSession.agentName !== name) {\n    setAgentSession({\n      agentName: name,\n    });\n  }\n\n  let previousState: any = null;\n  for (let i = context.messages.length - 1; i >= 0; i--) {\n    const message = context.messages[i];\n    if (message.isAgentStateMessage() && message.agentName === name) {\n      previousState = message.state;\n    }\n  }\n\n  let state = context.coagentStatesRef.current?.[name]?.state || {};\n\n  if (hint) {\n    const hintMessage = hint({ previousState, currentState: state });\n    if (hintMessage) {\n      await appendMessage(hintMessage);\n    } else {\n      await runChatCompletion();\n    }\n  } else {\n    await runChatCompletion();\n  }\n}\n\nconst isExternalStateManagement = <T>(\n  options: UseCoagentOptions<T>,\n): options is WithExternalStateManagement<T> => {\n  return \"state\" in options && \"setState\" in options;\n};\n\nconst isInternalStateManagementWithInitial = <T>(\n  options: UseCoagentOptions<T>,\n): options is WithInternalStateManagementAndInitial<T> => {\n  return \"initialState\" in options;\n};\n\nconst getCoagentState = <T>({\n  coagentStates,\n  name,\n  options,\n}: {\n  coagentStates: Record<string, CoagentState>;\n  name: string;\n  options: UseCoagentOptions<T>;\n}) => {\n  if (coagentStates[name]) {\n    return coagentStates[name];\n  } else {\n    return {\n      name,\n      state: isInternalStateManagementWithInitial<T>(options) ? options.initialState : {},\n      config: options.config\n        ? options.config\n        : options.configurable\n          ? { configurable: options.configurable }\n          : {},\n      running: false,\n      active: false,\n      threadId: undefined,\n      nodeName: undefined,\n      runId: undefined,\n    };\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA0FA,SAAS,aAAa,WAAW,SAAS,cAAc;AAaxD,SAAS,iBAAiB;AA8GnB,SAAS,WAAoB,SAAwD;AAC1F,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,EAAE,gBAAgB,IAAI;AAC5B,QAAM,EAAE,SAAS,IAAI,SAAS;AAC9B,QAAM,qBAAqB,OAAe;AAC1C,QAAM,kBAAkB,OAAY;AAEpC,QAAM,EAAE,KAAK,IAAI;AACjB,YAAU,MAAM;AACd,SAAI,mDAAiB,WAAU,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI,GAAG;AAC5E,YAAM,UAAU,wBAAwB;AACxC,cAAQ,KAAK,OAAO;AACpB,eAAS,EAAE,MAAM,WAAW,QAAQ,CAAC;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,UAAU,kCAAK,iBAAmB;AACxC,QAAM,EAAE,eAAe,kBAAkB,yBAAyB,UAAU,iBAAiB,IAC3F;AACF,QAAM,EAAE,eAAe,kBAAkB,IAAI,eAAe;AAE5D,QAAM,gBAAgB,wBAAwB;AAAA,IAC5C,KAAK,iBAAiB;AAAA,IACtB,cAAc,iBAAiB;AAAA,IAC/B,aAAa,iBAAiB;AAAA,EAChC,CAAC;AAGD,QAAM,WAAW;AAAA,IACf,CAAC,aAAoD;AAEnD,UAAI,eAA6B,gBAAgB,EAAE,eAAe,MAAM,QAAQ,CAAC;AACjF,YAAM,eACJ,OAAO,aAAa,aAAc,SAAsB,aAAa,KAAK,IAAI;AAEhF,8BAAwB,iCACnB,iBAAiB,UADE;AAAA,QAEtB,CAAC,IAAI,GAAG,iCACH,eADG;AAAA,UAEN,OAAO;AAAA,QACT;AAAA,MACF,EAAC;AAAA,IACH;AAAA,IACA,CAAC,eAAe,IAAI;AAAA,EACtB;AAEA,YAAU,MAAM;AACd,UAAM,kBAAkB,MAAY;AArQxC;AAsQM,UAAI,CAAC,YAAY,aAAa,mBAAmB;AAAS;AAE1D,YAAM,SAAS,MAAM,cAAc,eAAe;AAAA,QAChD;AAAA,QACA,WAAW;AAAA,MACb,CAAC;AAED,YAAM,YAAW,kBAAO,SAAP,mBAAa,mBAAb,mBAA6B;AAC9C,UAAI,aAAa,gBAAgB;AAAS;AAE1C,YAAI,kBAAO,SAAP,mBAAa,mBAAb,mBAA6B,iBAAgB,YAAY,YAAY,MAAM;AAC7E,wBAAgB,UAAU;AAC1B,2BAAmB,UAAU;AAC7B,cAAM,eAAe,UAAU,UAAU,CAAC,CAAC;AAC3C,kCAA0B,OAAO,IAC7B,QAAQ,SAAS,YAAY,IAC7B,SAAS,YAAY;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB,GAAG,CAAC,QAAQ,CAAC;AAGb,YAAU,MAAM;AACd,QAAI,0BAA0B,OAAO,GAAG;AACtC,eAAS,QAAQ,KAAK;AAAA,IACxB,WAAW,cAAc,IAAI,MAAM,QAAW;AAC5C,eAAS,QAAQ,iBAAiB,SAAY,CAAC,IAAI,QAAQ,YAAY;AAAA,IACzE;AAAA,EACF,GAAG;AAAA,IACD,0BAA0B,OAAO,IAAI,KAAK,UAAU,QAAQ,KAAK,IAAI;AAAA;AAAA,IAErE,cAAc,IAAI,MAAM;AAAA,EAC1B,CAAC;AAED,QAAM,mBAAmB;AAAA,IACvB,CAAO,SAAwB;AAC7B,YAAM,SAAS,MAAM,SAAS,eAAe,mBAAmB,IAAI;AAAA,IACtE;AAAA,IACA,CAAC,MAAM,SAAS,eAAe,iBAAiB;AAAA,EAClD;AAGA,SAAO,QAAQ,MAAM;AACnB,UAAM,eAAe,gBAAgB,EAAE,eAAe,MAAM,QAAQ,CAAC;AACrE,WAAO;AAAA,MACL;AAAA,MACA,UAAU,aAAa;AAAA,MACvB,UAAU,aAAa;AAAA,MACvB,SAAS,aAAa;AAAA,MACtB,OAAO,aAAa;AAAA,MACpB,UAAU,0BAA0B,OAAO,IAAI,QAAQ,WAAW;AAAA,MAClE,OAAO,MAAM,WAAW,MAAM,OAAO;AAAA,MACrC,MAAM,MAAM,UAAU,MAAM,OAAO;AAAA,MACnC,KAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,MAAM,eAAe,SAAS,UAAU,gBAAgB,CAAC;AAC/D;AAEO,SAAS,WAAW,MAAc,SAA+B;AACtE,QAAM,EAAE,gBAAgB,IAAI;AAC5B,kBAAgB;AAAA,IACd,WAAW;AAAA,EACb,CAAC;AACH;AAEO,SAAS,UAAU,MAAc,SAA+B;AACrE,QAAM,EAAE,cAAc,gBAAgB,IAAI;AAC1C,MAAI,gBAAgB,aAAa,cAAc,MAAM;AACnD,oBAAgB,IAAI;AACpB,YAAQ,iBAAiB,CAAC,oBAAoB;AAC5C,aAAO,iCACF,kBADE;AAAA,QAEL,CAAC,IAAI,GAAG,iCACH,gBAAgB,IAAI,IADjB;AAAA,UAEN,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,OAAO;AACL,YAAQ,KAAK,8BAA8B,MAAM;AAAA,EACnD;AACF;AAEA,SAAsB,SACpB,MACA,SACA,eACA,mBACA,MACA;AAAA;AApWF;AAqWE,UAAM,EAAE,cAAc,gBAAgB,IAAI;AAC1C,QAAI,CAAC,gBAAgB,aAAa,cAAc,MAAM;AACpD,sBAAgB;AAAA,QACd,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAEA,QAAI,gBAAqB;AACzB,aAAS,IAAI,QAAQ,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AACrD,YAAM,UAAU,QAAQ,SAAS,CAAC;AAClC,UAAI,QAAQ,oBAAoB,KAAK,QAAQ,cAAc,MAAM;AAC/D,wBAAgB,QAAQ;AAAA,MAC1B;AAAA,IACF;AAEA,QAAI,UAAQ,mBAAQ,iBAAiB,YAAzB,mBAAmC,UAAnC,mBAA0C,UAAS,CAAC;AAEhE,QAAI,MAAM;AACR,YAAM,cAAc,KAAK,EAAE,eAAe,cAAc,MAAM,CAAC;AAC/D,UAAI,aAAa;AACf,cAAM,cAAc,WAAW;AAAA,MACjC,OAAO;AACL,cAAM,kBAAkB;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,YAAM,kBAAkB;AAAA,IAC1B;AAAA,EACF;AAAA;AAEA,IAAM,4BAA4B,CAChC,YAC8C;AAC9C,SAAO,WAAW,WAAW,cAAc;AAC7C;AAEA,IAAM,uCAAuC,CAC3C,YACwD;AACxD,SAAO,kBAAkB;AAC3B;AAEA,IAAM,kBAAkB,CAAI;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AACF,MAIM;AACJ,MAAI,cAAc,IAAI,GAAG;AACvB,WAAO,cAAc,IAAI;AAAA,EAC3B,OAAO;AACL,WAAO;AAAA,MACL;AAAA,MACA,OAAO,qCAAwC,OAAO,IAAI,QAAQ,eAAe,CAAC;AAAA,MAClF,QAAQ,QAAQ,SACZ,QAAQ,SACR,QAAQ,eACN,EAAE,cAAc,QAAQ,aAAa,IACrC,CAAC;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}