"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeferStreamDirectiveOnRootFieldRule = DeferStreamDirectiveOnRootFieldRule;
const graphql_yoga_1 = require("graphql-yoga");
const utils_1 = require("@graphql-tools/utils");
/**
 * Stream directive on list field
 *
 * A GraphQL document is only valid if defer directives are not used on root mutation or subscription types.
 */
function DeferStreamDirectiveOnRootFieldRule(context) {
    return {
        Directive(node) {
            const mutationType = context.getSchema().getMutationType();
            const subscriptionType = context.getSchema().getSubscriptionType();
            const parentType = context.getParentType();
            if (parentType && node.name.value === utils_1.GraphQLDeferDirective.name) {
                if (mutationType && parentType === mutationType) {
                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Defer directive cannot be used on root mutation type "${parentType.name}".`, { nodes: node }));
                }
                if (subscriptionType && parentType === subscriptionType) {
                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Defer directive cannot be used on root subscription type "${parentType.name}".`, { nodes: node }));
                }
            }
            if (parentType && node.name.value === utils_1.GraphQLStreamDirective.name) {
                if (mutationType && parentType === mutationType) {
                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Stream directive cannot be used on root mutation type "${parentType.name}".`, { nodes: node }));
                }
                if (subscriptionType && parentType === subscriptionType) {
                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Stream directive cannot be used on root subscription type "${parentType.name}".`, { nodes: node }));
                }
            }
        },
    };
}
