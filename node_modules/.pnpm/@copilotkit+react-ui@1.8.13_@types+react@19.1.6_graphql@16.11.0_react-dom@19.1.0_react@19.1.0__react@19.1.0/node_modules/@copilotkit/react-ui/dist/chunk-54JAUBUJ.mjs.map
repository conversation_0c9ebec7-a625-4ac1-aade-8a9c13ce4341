{"version": 3, "sources": ["../src/hooks/use-copy-to-clipboard.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nexport interface useCopyToClipboardProps {\n  timeout?: number;\n}\n\nexport function useCopyToClipboard({ timeout = 2000 }: useCopyToClipboardProps) {\n  const [isCopied, setIsCopied] = React.useState<Boolean>(false);\n\n  const copyToClipboard = (value: string) => {\n    if (typeof window === \"undefined\" || !navigator.clipboard?.writeText) {\n      return;\n    }\n\n    if (!value) {\n      return;\n    }\n\n    navigator.clipboard.writeText(value).then(() => {\n      setIsCopied(true);\n\n      setTimeout(() => {\n        setIsCopied(false);\n      }, timeout);\n    });\n  };\n\n  return { isCopied, copyToClipboard };\n}\n"], "mappings": ";AAAA,YAAY,WAAW;AAMhB,SAAS,mBAAmB,EAAE,UAAU,IAAK,GAA4B;AAC9E,QAAM,CAAC,UAAU,WAAW,IAAU,eAAkB,KAAK;AAE7D,QAAM,kBAAkB,CAAC,UAAkB;AAT7C;AAUI,QAAI,OAAO,WAAW,eAAe,GAAC,eAAU,cAAV,mBAAqB,YAAW;AACpE;AAAA,IACF;AAEA,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAEA,cAAU,UAAU,UAAU,KAAK,EAAE,KAAK,MAAM;AAC9C,kBAAY,IAAI;AAEhB,iBAAW,MAAM;AACf,oBAAY,KAAK;AAAA,MACnB,GAAG,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAO,EAAE,UAAU,gBAAgB;AACrC;", "names": []}