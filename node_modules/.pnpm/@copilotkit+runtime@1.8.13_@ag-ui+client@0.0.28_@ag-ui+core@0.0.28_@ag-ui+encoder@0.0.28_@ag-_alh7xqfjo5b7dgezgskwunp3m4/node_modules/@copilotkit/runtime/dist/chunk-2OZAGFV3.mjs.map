{"version": 3, "sources": ["../src/graphql/types/base/index.ts"], "sourcesContent": ["import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class BaseMessageInput {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => Date)\n  createdAt: Date;\n}\n"], "mappings": ";;;;;AAAA,SAASA,OAAOC,iBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,mBAAN,MAAMA;EAEXC;EAGAC;AACF;AANaF;;EACVG,MAAM,MAAMC,MAAAA;;GADFJ,iBAAAA,WAAAA,MAAAA,MAAAA;;EAIVG,MAAM,MAAME,IAAAA;qCACF,SAAA,cAAA,SAAA,IAAA;GALAL,iBAAAA,WAAAA,aAAAA,MAAAA;AAAAA,mBAAAA,aAAAA;EADZM,UAAAA;GACYN,gBAAAA;", "names": ["Field", "InputType", "BaseMessageInput", "id", "createdAt", "Field", "String", "Date", "InputType"]}