"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/hooks/use-coagent.ts
var use_coagent_exports = {};
__export(use_coagent_exports, {
  runAgent: () => runAgent,
  startAgent: () => startAgent,
  stopAgent: () => stopAgent,
  useCoAgent: () => useCoAgent
});
module.exports = __toCommonJS(use_coagent_exports);
var import_react9 = require("react");

// src/context/copilot-context.tsx
var import_react = __toESM(require("react"));
var emptyCopilotContext = {
  actions: {},
  setAction: () => {
  },
  removeAction: () => {
  },
  coAgentStateRenders: {},
  setCoAgentStateRender: () => {
  },
  removeCoAgentStateRender: () => {
  },
  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },
  getContextString: (documents, categories) => returnAndThrowInDebug(""),
  addContext: () => "",
  removeContext: () => {
  },
  getFunctionCallHandler: () => returnAndThrowInDebug(() => __async(void 0, null, function* () {
  })),
  isLoading: false,
  setIsLoading: () => returnAndThrowInDebug(false),
  chatInstructions: "",
  setChatInstructions: () => returnAndThrowInDebug(""),
  additionalInstructions: [],
  setAdditionalInstructions: () => returnAndThrowInDebug([]),
  getDocumentsContext: (categories) => returnAndThrowInDebug([]),
  addDocumentContext: () => returnAndThrowInDebug(""),
  removeDocumentContext: () => {
  },
  runtimeClient: {},
  copilotApiConfig: new class {
    get chatApiEndpoint() {
      throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
    }
    get headers() {
      return {};
    }
    get body() {
      return {};
    }
  }(),
  chatSuggestionConfiguration: {},
  addChatSuggestionConfiguration: () => {
  },
  removeChatSuggestionConfiguration: () => {
  },
  showDevConsole: "auto",
  coagentStates: {},
  setCoagentStates: () => {
  },
  coagentStatesRef: { current: {} },
  setCoagentStatesWithRef: () => {
  },
  agentSession: null,
  setAgentSession: () => {
  },
  forwardedParameters: {},
  agentLock: null,
  threadId: "",
  setThreadId: () => {
  },
  runId: null,
  setRunId: () => {
  },
  chatAbortControllerRef: { current: null },
  availableAgents: [],
  extensions: {},
  setExtensions: () => {
  },
  langGraphInterruptAction: null,
  setLangGraphInterruptAction: () => null,
  removeLangGraphInterruptAction: () => null
};
var CopilotContext = import_react.default.createContext(emptyCopilotContext);
function useCopilotContext() {
  const context = import_react.default.useContext(CopilotContext);
  if (context === emptyCopilotContext) {
    throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
  }
  return context;
}
function returnAndThrowInDebug(_value) {
  throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
}

// src/context/copilot-messages-context.tsx
var import_react2 = __toESM(require("react"));
var emptyCopilotContext2 = {
  messages: [],
  setMessages: () => []
};
var CopilotMessagesContext = import_react2.default.createContext(emptyCopilotContext2);
function useCopilotMessagesContext() {
  const context = import_react2.default.useContext(CopilotMessagesContext);
  if (context === emptyCopilotContext2) {
    throw new Error(
      "A messages consuming component was not wrapped with `<CopilotMessages> {...} </CopilotMessages>`"
    );
  }
  return context;
}

// src/hooks/use-copilot-chat.ts
var import_react8 = require("react");
var import_runtime_client_gql4 = require("@copilotkit/runtime-client-gql");

// src/hooks/use-chat.ts
var import_react6 = require("react");
var import_shared2 = require("@copilotkit/shared");
var import_runtime_client_gql3 = require("@copilotkit/runtime-client-gql");

// src/types/frontend-action.ts
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_shared = require("@copilotkit/shared");
function processActionsForRuntimeRequest(actions) {
  const filteredActions = actions.filter(
    (action) => action.available !== import_runtime_client_gql.ActionInputAvailability.Disabled && action.disabled !== true && action.name !== "*" && action.available != "frontend" && !action.pairedAction
  ).map((action) => {
    let available = import_runtime_client_gql.ActionInputAvailability.Enabled;
    if (action.disabled) {
      available = import_runtime_client_gql.ActionInputAvailability.Disabled;
    } else if (action.available === "disabled") {
      available = import_runtime_client_gql.ActionInputAvailability.Disabled;
    } else if (action.available === "remote") {
      available = import_runtime_client_gql.ActionInputAvailability.Remote;
    }
    return {
      name: action.name,
      description: action.description || "",
      jsonSchema: JSON.stringify((0, import_shared.actionParametersToJsonSchema)(action.parameters || [])),
      available
    };
  });
  return filteredActions;
}

// src/hooks/use-copilot-runtime-client.ts
var import_runtime_client_gql2 = require("@copilotkit/runtime-client-gql");

// src/components/toast/toast-provider.tsx
var import_react4 = require("react");

// src/components/error-boundary/error-utils.tsx
var import_react3 = require("react");

// src/components/toast/exclamation-mark-icon.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var ExclamationMarkIcon = ({
  className,
  style
}) => /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    className: `lucide lucide-circle-alert ${className ? className : ""}`,
    style,
    children: [
      /* @__PURE__ */ (0, import_jsx_runtime.jsx)("circle", { cx: "12", cy: "12", r: "10" }),
      /* @__PURE__ */ (0, import_jsx_runtime.jsx)("line", { x1: "12", x2: "12", y1: "8", y2: "12" }),
      /* @__PURE__ */ (0, import_jsx_runtime.jsx)("line", { x1: "12", x2: "12.01", y1: "16", y2: "16" })
    ]
  }
);

// src/components/error-boundary/error-utils.tsx
var import_react_markdown = __toESM(require("react-markdown"));
var import_jsx_runtime2 = require("react/jsx-runtime");
function ErrorToast({ errors }) {
  const errorsToRender = errors.map((error, idx) => {
    var _a, _b, _c;
    const originalError = "extensions" in error ? (_a = error.extensions) == null ? void 0 : _a.originalError : {};
    const message = (_b = originalError == null ? void 0 : originalError.message) != null ? _b : error.message;
    const code = "extensions" in error ? (_c = error.extensions) == null ? void 0 : _c.code : null;
    return /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)(
      "div",
      {
        style: {
          marginTop: idx === 0 ? 0 : 10,
          marginBottom: 14
        },
        children: [
          /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(ExclamationMarkIcon, { style: { marginBottom: 4 } }),
          code && /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)(
            "div",
            {
              style: {
                fontWeight: "600",
                marginBottom: 4
              },
              children: [
                "Copilot Cloud Error:",
                " ",
                /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { style: { fontFamily: "monospace", fontWeight: "normal" }, children: code })
              ]
            }
          ),
          /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(import_react_markdown.default, { children: message })
        ]
      },
      idx
    );
  });
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)(
    "div",
    {
      style: {
        fontSize: "13px",
        maxWidth: "600px"
      },
      children: [
        errorsToRender,
        /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("div", { style: { fontSize: "11px", opacity: 0.75 }, children: "NOTE: This error only displays during local development." })
      ]
    }
  );
}
function useErrorToast() {
  const { addToast } = useToast();
  return (0, import_react3.useCallback)(
    (error) => {
      const errorId = error.map((err) => {
        var _a, _b;
        const message = "extensions" in err ? ((_b = (_a = err.extensions) == null ? void 0 : _a.originalError) == null ? void 0 : _b.message) || err.message : err.message;
        const stack = err.stack || "";
        return btoa(message + stack).slice(0, 32);
      }).join("|");
      addToast({
        type: "error",
        id: errorId,
        // Toast libraries typically dedupe by id
        message: /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(ErrorToast, { errors: error })
      });
    },
    [addToast]
  );
}
function useAsyncCallback(callback, deps) {
  const addErrorToast = useErrorToast();
  return (0, import_react3.useCallback)((...args) => __async(this, null, function* () {
    try {
      return yield callback(...args);
    } catch (error) {
      console.error("Error in async callback:", error);
      addErrorToast([error]);
      throw error;
    }
  }), deps);
}

// src/components/toast/toast-provider.tsx
var import_jsx_runtime3 = require("react/jsx-runtime");
var ToastContext = (0, import_react4.createContext)(void 0);
function useToast() {
  const context = (0, import_react4.useContext)(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}

// src/hooks/use-copilot-runtime-client.ts
var import_react5 = require("react");
var useCopilotRuntimeClient = (options) => {
  const { addGraphQLErrorsToast } = useToast();
  const addErrorToast = useErrorToast();
  const { addToast } = useToast();
  const runtimeClient = (0, import_react5.useMemo)(() => {
    return new import_runtime_client_gql2.CopilotRuntimeClient(__spreadProps(__spreadValues({}, options), {
      handleGQLErrors: (error) => {
        if (error.graphQLErrors.length) {
          addGraphQLErrorsToast(error.graphQLErrors);
        } else {
          addErrorToast([error]);
        }
      },
      handleGQLWarning: (message) => {
        console.warn(message);
        addToast({ type: "warning", message });
      }
    }));
  }, [options, addGraphQLErrorsToast, addToast]);
  return runtimeClient;
};

// src/hooks/use-chat.ts
function useChat(options) {
  const {
    messages,
    setMessages,
    makeSystemMessageCallback,
    copilotConfig,
    setIsLoading,
    initialMessages,
    isLoading,
    actions,
    onFunctionCall,
    onCoAgentStateRender,
    setCoagentStatesWithRef,
    coagentStatesRef,
    agentSession,
    setAgentSession,
    threadId,
    setThreadId,
    runId,
    setRunId,
    chatAbortControllerRef,
    agentLock,
    extensions,
    setExtensions,
    langGraphInterruptAction,
    setLangGraphInterruptAction
  } = options;
  const runChatCompletionRef = (0, import_react6.useRef)();
  const addErrorToast = useErrorToast();
  const agentSessionRef = (0, import_react6.useRef)(agentSession);
  agentSessionRef.current = agentSession;
  const runIdRef = (0, import_react6.useRef)(runId);
  runIdRef.current = runId;
  const extensionsRef = (0, import_react6.useRef)(extensions);
  extensionsRef.current = extensions;
  const publicApiKey = copilotConfig.publicApiKey;
  const headers = __spreadValues(__spreadValues({}, copilotConfig.headers || {}), publicApiKey ? { [import_shared2.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey } : {});
  const runtimeClient = useCopilotRuntimeClient({
    url: copilotConfig.chatApiEndpoint,
    publicApiKey: copilotConfig.publicApiKey,
    headers,
    credentials: copilotConfig.credentials
  });
  const runChatCompletion = useAsyncCallback(
    (previousMessages) => __async(this, null, function* () {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o;
      setIsLoading(true);
      const interruptEvent = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event;
      if ((interruptEvent == null ? void 0 : interruptEvent.name) === import_runtime_client_gql3.MetaEventName.LangGraphInterruptEvent && (interruptEvent == null ? void 0 : interruptEvent.value) && !(interruptEvent == null ? void 0 : interruptEvent.response) && agentSessionRef.current) {
        addErrorToast([
          new Error(
            "A message was sent while interrupt is active. This will cause failure on the agent side"
          )
        ]);
      }
      let newMessages = [
        new import_runtime_client_gql3.TextMessage({
          content: "",
          role: import_runtime_client_gql3.Role.Assistant
        })
      ];
      chatAbortControllerRef.current = new AbortController();
      setMessages([...previousMessages, ...newMessages]);
      const systemMessage = makeSystemMessageCallback();
      const messagesWithContext = [systemMessage, ...initialMessages || [], ...previousMessages];
      const finalProperties = __spreadValues({}, copilotConfig.properties || {});
      let mcpServersToUse = null;
      if (copilotConfig.mcpServers && Array.isArray(copilotConfig.mcpServers) && copilotConfig.mcpServers.length > 0) {
        mcpServersToUse = copilotConfig.mcpServers;
      } else if (((_a = copilotConfig.properties) == null ? void 0 : _a.mcpServers) && Array.isArray(copilotConfig.properties.mcpServers) && copilotConfig.properties.mcpServers.length > 0) {
        mcpServersToUse = copilotConfig.properties.mcpServers;
      }
      if (mcpServersToUse) {
        finalProperties.mcpServers = mcpServersToUse;
        copilotConfig.mcpServers = mcpServersToUse;
      }
      const isAgentRun = agentSessionRef.current !== null;
      const stream = runtimeClient.asStream(
        runtimeClient.generateCopilotResponse({
          data: __spreadProps(__spreadValues(__spreadProps(__spreadValues({
            frontend: {
              actions: processActionsForRuntimeRequest(actions),
              url: window.location.href
            },
            threadId,
            runId: runIdRef.current,
            extensions: extensionsRef.current,
            metaEvents: composeAndFlushMetaEventsInput([langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event]),
            messages: (0, import_runtime_client_gql3.convertMessagesToGqlInput)((0, import_runtime_client_gql3.filterAgentStateMessages)(messagesWithContext))
          }, copilotConfig.cloud ? {
            cloud: __spreadValues({}, ((_d = (_c = (_b = copilotConfig.cloud.guardrails) == null ? void 0 : _b.input) == null ? void 0 : _c.restrictToTopic) == null ? void 0 : _d.enabled) ? {
              guardrails: {
                inputValidationRules: {
                  allowList: copilotConfig.cloud.guardrails.input.restrictToTopic.validTopics,
                  denyList: copilotConfig.cloud.guardrails.input.restrictToTopic.invalidTopics
                }
              }
            } : {})
          } : {}), {
            metadata: {
              requestType: import_runtime_client_gql3.CopilotRequestType.Chat
            }
          }), agentSessionRef.current ? {
            agentSession: agentSessionRef.current
          } : {}), {
            agentStates: Object.values(coagentStatesRef.current).map((state) => {
              const stateObject = {
                agentName: state.name,
                state: JSON.stringify(state.state)
              };
              if (state.config !== void 0) {
                stateObject.config = JSON.stringify(state.config);
              }
              return stateObject;
            }),
            forwardedParameters: options.forwardedParameters || {}
          }),
          properties: finalProperties,
          signal: (_e = chatAbortControllerRef.current) == null ? void 0 : _e.signal
        })
      );
      const guardrailsEnabled = ((_h = (_g = (_f = copilotConfig.cloud) == null ? void 0 : _f.guardrails) == null ? void 0 : _g.input) == null ? void 0 : _h.restrictToTopic.enabled) || false;
      const reader = stream.getReader();
      let executedCoAgentStateRenders = [];
      let followUp = void 0;
      let messages2 = [];
      let syncedMessages = [];
      let interruptMessages = [];
      try {
        while (true) {
          let done, value;
          try {
            const readResult = yield reader.read();
            done = readResult.done;
            value = readResult.value;
          } catch (readError) {
            break;
          }
          if (done) {
            if (chatAbortControllerRef.current.signal.aborted) {
              return [];
            }
            break;
          }
          if (!(value == null ? void 0 : value.generateCopilotResponse)) {
            continue;
          }
          runIdRef.current = value.generateCopilotResponse.runId || null;
          extensionsRef.current = import_runtime_client_gql3.CopilotRuntimeClient.removeGraphQLTypename(
            value.generateCopilotResponse.extensions || {}
          );
          setRunId(runIdRef.current);
          setExtensions(extensionsRef.current);
          let rawMessagesResponse = value.generateCopilotResponse.messages;
          const metaEvents = (_j = (_i = value.generateCopilotResponse) == null ? void 0 : _i.metaEvents) != null ? _j : [];
          (metaEvents != null ? metaEvents : []).forEach((ev) => {
            if (ev.name === import_runtime_client_gql3.MetaEventName.LangGraphInterruptEvent) {
              let eventValue = (0, import_runtime_client_gql3.langGraphInterruptEvent)(ev).value;
              eventValue = (0, import_shared2.parseJson)(eventValue, eventValue);
              setLangGraphInterruptAction({
                event: __spreadProps(__spreadValues({}, (0, import_runtime_client_gql3.langGraphInterruptEvent)(ev)), {
                  value: eventValue
                })
              });
            }
            if (ev.name === import_runtime_client_gql3.MetaEventName.CopilotKitLangGraphInterruptEvent) {
              const data = ev.data;
              rawMessagesResponse = [...rawMessagesResponse, ...data.messages];
              interruptMessages = (0, import_runtime_client_gql3.convertGqlOutputToMessages)(
                // @ts-ignore
                (0, import_runtime_client_gql3.filterAdjacentAgentStateMessages)(data.messages)
              );
            }
          });
          messages2 = (0, import_runtime_client_gql3.convertGqlOutputToMessages)(
            (0, import_runtime_client_gql3.filterAdjacentAgentStateMessages)(rawMessagesResponse)
          );
          if (messages2.length === 0) {
            continue;
          }
          newMessages = [];
          if (((_k = value.generateCopilotResponse.status) == null ? void 0 : _k.__typename) === "FailedResponseStatus" && value.generateCopilotResponse.status.reason === "GUARDRAILS_VALIDATION_FAILED") {
            newMessages = [
              new import_runtime_client_gql3.TextMessage({
                role: import_runtime_client_gql3.MessageRole.Assistant,
                content: ((_l = value.generateCopilotResponse.status.details) == null ? void 0 : _l.guardrailsReason) || ""
              })
            ];
            setMessages([...previousMessages, ...newMessages]);
            break;
          } else {
            newMessages = [...messages2];
            for (const message of messages2) {
              if (message.isAgentStateMessage() && !message.active && !executedCoAgentStateRenders.includes(message.id) && onCoAgentStateRender) {
                if (guardrailsEnabled && value.generateCopilotResponse.status === void 0) {
                  break;
                }
                yield onCoAgentStateRender({
                  name: message.agentName,
                  nodeName: message.nodeName,
                  state: message.state
                });
                executedCoAgentStateRenders.push(message.id);
              }
            }
            const lastAgentStateMessage = [...messages2].reverse().find((message) => message.isAgentStateMessage());
            if (lastAgentStateMessage) {
              if (lastAgentStateMessage.state.messages && lastAgentStateMessage.state.messages.length > 0) {
                syncedMessages = (0, import_runtime_client_gql3.loadMessagesFromJsonRepresentation)(
                  lastAgentStateMessage.state.messages
                );
              }
              setCoagentStatesWithRef((prevAgentStates) => __spreadProps(__spreadValues({}, prevAgentStates), {
                [lastAgentStateMessage.agentName]: {
                  name: lastAgentStateMessage.agentName,
                  state: lastAgentStateMessage.state,
                  running: lastAgentStateMessage.running,
                  active: lastAgentStateMessage.active,
                  threadId: lastAgentStateMessage.threadId,
                  nodeName: lastAgentStateMessage.nodeName,
                  runId: lastAgentStateMessage.runId
                }
              }));
              if (lastAgentStateMessage.running) {
                setAgentSession({
                  threadId: lastAgentStateMessage.threadId,
                  agentName: lastAgentStateMessage.agentName,
                  nodeName: lastAgentStateMessage.nodeName
                });
              } else {
                if (agentLock) {
                  setAgentSession({
                    threadId: (0, import_shared2.randomId)(),
                    agentName: agentLock,
                    nodeName: void 0
                  });
                } else {
                  setAgentSession(null);
                }
              }
            }
          }
          if (newMessages.length > 0) {
            setMessages([...previousMessages, ...newMessages]);
          }
        }
        let finalMessages = constructFinalMessages(
          [...syncedMessages, ...interruptMessages],
          previousMessages,
          newMessages
        );
        let didExecuteAction = false;
        if (onFunctionCall) {
          const lastMessages = [];
          for (let i = finalMessages.length - 1; i >= 0; i--) {
            const message = finalMessages[i];
            if ((message.isActionExecutionMessage() || message.isResultMessage()) && message.status.code !== import_runtime_client_gql3.MessageStatusCode.Pending) {
              lastMessages.unshift(message);
            } else if (!message.isAgentStateMessage()) {
              break;
            }
          }
          for (const message of lastMessages) {
            setMessages(finalMessages);
            const action = actions.find(
              (action2) => action2.name === message.name
            );
            const currentResultMessagePairedFeAction = message.isResultMessage() ? getPairedFeAction(actions, message) : null;
            const executeActionFromMessage = (action2, message2) => __async(this, null, function* () {
              var _a2;
              const isInterruptAction = interruptMessages.find((m) => m.id === message2.id);
              followUp = (_a2 = action2 == null ? void 0 : action2.followUp) != null ? _a2 : !isInterruptAction;
              const resultMessage = yield executeAction({
                onFunctionCall,
                previousMessages,
                message: message2,
                chatAbortControllerRef,
                onError: (error) => {
                  addErrorToast([error]);
                  console.error(`Failed to execute action ${message2.name}: ${error}`);
                }
              });
              didExecuteAction = true;
              const messageIndex = finalMessages.findIndex((msg) => msg.id === message2.id);
              finalMessages.splice(messageIndex + 1, 0, resultMessage);
              return resultMessage;
            });
            if (action && message.isActionExecutionMessage()) {
              const resultMessage = yield executeActionFromMessage(action, message);
              const pairedFeAction = getPairedFeAction(actions, resultMessage);
              if (pairedFeAction) {
                const newExecutionMessage = new import_runtime_client_gql3.ActionExecutionMessage({
                  name: pairedFeAction.name,
                  arguments: (0, import_shared2.parseJson)(resultMessage.result, resultMessage.result),
                  status: message.status,
                  createdAt: message.createdAt,
                  parentMessageId: message.parentMessageId
                });
                yield executeActionFromMessage(pairedFeAction, newExecutionMessage);
              }
            } else if (message.isResultMessage() && currentResultMessagePairedFeAction) {
              const newExecutionMessage = new import_runtime_client_gql3.ActionExecutionMessage({
                name: currentResultMessagePairedFeAction.name,
                arguments: (0, import_shared2.parseJson)(message.result, message.result),
                status: message.status,
                createdAt: message.createdAt
              });
              finalMessages.push(newExecutionMessage);
              yield executeActionFromMessage(
                currentResultMessagePairedFeAction,
                newExecutionMessage
              );
            }
          }
          setMessages(finalMessages);
        }
        if (
          // if followUp is not explicitly false
          followUp !== false && // and we executed an action
          (didExecuteAction || // the last message is a server side result
          !isAgentRun && finalMessages.length && finalMessages[finalMessages.length - 1].isResultMessage()) && // the user did not stop generation
          !((_m = chatAbortControllerRef.current) == null ? void 0 : _m.signal.aborted)
        ) {
          yield new Promise((resolve) => setTimeout(resolve, 10));
          return yield runChatCompletionRef.current(finalMessages);
        } else if ((_n = chatAbortControllerRef.current) == null ? void 0 : _n.signal.aborted) {
          const repairedMessages = finalMessages.filter((message, actionExecutionIndex) => {
            if (message.isActionExecutionMessage()) {
              return finalMessages.find(
                (msg, resultIndex) => msg.isResultMessage() && msg.actionExecutionId === message.id && resultIndex === actionExecutionIndex + 1
              );
            }
            return true;
          });
          const repairedMessageIds = repairedMessages.map((message) => message.id);
          setMessages(repairedMessages);
          if ((_o = agentSessionRef.current) == null ? void 0 : _o.nodeName) {
            setAgentSession({
              threadId: agentSessionRef.current.threadId,
              agentName: agentSessionRef.current.agentName,
              nodeName: "__end__"
            });
          }
          return newMessages.filter((message) => repairedMessageIds.includes(message.id));
        } else {
          return newMessages.slice();
        }
      } finally {
        setIsLoading(false);
      }
    }),
    [
      messages,
      setMessages,
      makeSystemMessageCallback,
      copilotConfig,
      setIsLoading,
      initialMessages,
      isLoading,
      actions,
      onFunctionCall,
      onCoAgentStateRender,
      setCoagentStatesWithRef,
      coagentStatesRef,
      agentSession,
      setAgentSession
    ]
  );
  runChatCompletionRef.current = runChatCompletion;
  const runChatCompletionAndHandleFunctionCall = useAsyncCallback(
    (messages2) => __async(this, null, function* () {
      yield runChatCompletionRef.current(messages2);
    }),
    [messages]
  );
  const composeAndFlushMetaEventsInput = (0, import_react6.useCallback)(
    (metaEvents) => {
      return metaEvents.reduce((acc, event) => {
        if (!event)
          return acc;
        switch (event.name) {
          case import_runtime_client_gql3.MetaEventName.LangGraphInterruptEvent:
            if (event.response) {
              setLangGraphInterruptAction(null);
              const value = event.value;
              return [
                ...acc,
                {
                  name: event.name,
                  value: typeof value === "string" ? value : JSON.stringify(value),
                  response: typeof event.response === "string" ? event.response : JSON.stringify(event.response)
                }
              ];
            }
            return acc;
          default:
            return acc;
        }
      }, []);
    },
    [setLangGraphInterruptAction]
  );
  const append = useAsyncCallback(
    (message, options2) => __async(this, null, function* () {
      var _a;
      if (isLoading) {
        return;
      }
      const newMessages = [...messages, message];
      setMessages(newMessages);
      const followUp = (_a = options2 == null ? void 0 : options2.followUp) != null ? _a : true;
      if (followUp) {
        return runChatCompletionAndHandleFunctionCall(newMessages);
      }
    }),
    [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]
  );
  const reload = useAsyncCallback(
    (messageId) => __async(this, null, function* () {
      if (isLoading || messages.length === 0) {
        return;
      }
      const index = messages.findIndex((msg) => msg.id === messageId);
      if (index === -1) {
        console.warn(`Message with id ${messageId} not found`);
        return;
      }
      let newMessages = messages.slice(0, index);
      if (newMessages.length > 0 && newMessages[newMessages.length - 1].isAgentStateMessage()) {
        newMessages = newMessages.slice(0, newMessages.length - 1);
      }
      setMessages(newMessages);
      return runChatCompletionAndHandleFunctionCall(newMessages);
    }),
    [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]
  );
  const stop = () => {
    var _a;
    (_a = chatAbortControllerRef.current) == null ? void 0 : _a.abort("Stop was called");
  };
  return {
    append,
    reload,
    stop,
    runChatCompletion: () => runChatCompletionRef.current(messages)
  };
}
function constructFinalMessages(syncedMessages, previousMessages, newMessages) {
  const finalMessages = syncedMessages.length > 0 ? [...syncedMessages] : [...previousMessages, ...newMessages];
  if (syncedMessages.length > 0) {
    const messagesWithAgentState = [...previousMessages, ...newMessages];
    let previousMessageId = void 0;
    for (const message of messagesWithAgentState) {
      if (message.isAgentStateMessage()) {
        const index = finalMessages.findIndex((msg) => msg.id === previousMessageId);
        if (index !== -1) {
          finalMessages.splice(index + 1, 0, message);
        }
      }
      previousMessageId = message.id;
    }
  }
  return finalMessages;
}
function executeAction(_0) {
  return __async(this, arguments, function* ({
    onFunctionCall,
    previousMessages,
    message,
    chatAbortControllerRef,
    onError
  }) {
    let result;
    let error = null;
    try {
      result = yield Promise.race([
        onFunctionCall({
          messages: previousMessages,
          name: message.name,
          args: message.arguments
        }),
        new Promise(
          (resolve) => {
            var _a;
            return (_a = chatAbortControllerRef.current) == null ? void 0 : _a.signal.addEventListener(
              "abort",
              () => resolve("Operation was aborted by the user")
            );
          }
        ),
        // if the user stopped generation, we also abort consecutive actions
        new Promise((resolve) => {
          var _a;
          if ((_a = chatAbortControllerRef.current) == null ? void 0 : _a.signal.aborted) {
            resolve("Operation was aborted by the user");
          }
        })
      ]);
    } catch (e) {
      onError(e);
    }
    return new import_runtime_client_gql3.ResultMessage({
      id: "result-" + message.id,
      result: import_runtime_client_gql3.ResultMessage.encodeResult(
        error ? {
          content: result,
          error: JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error)))
        } : result
      ),
      actionExecutionId: message.id,
      actionName: message.name
    });
  });
}
function getPairedFeAction(actions, message) {
  let actionName = null;
  if (message.isActionExecutionMessage()) {
    actionName = message.name;
  } else if (message.isResultMessage()) {
    actionName = message.actionName;
  }
  return actions.find(
    (action) => action.name === actionName && action.available === "frontend" || action.pairedAction === actionName
  );
}

// src/components/copilot-provider/copilotkit.tsx
var import_react7 = require("react");
var import_react_dom = require("react-dom");
var import_shared3 = require("@copilotkit/shared");
var import_jsx_runtime4 = require("react/jsx-runtime");
var defaultCopilotContextCategories = ["global"];

// src/hooks/use-copilot-chat.ts
function useCopilotChat(_a = {}) {
  var _b = _a, {
    makeSystemMessage
  } = _b, options = __objRest(_b, [
    "makeSystemMessage"
  ]);
  const {
    getContextString,
    getFunctionCallHandler,
    copilotApiConfig,
    isLoading,
    setIsLoading,
    chatInstructions,
    actions,
    coagentStatesRef,
    setCoagentStatesWithRef,
    coAgentStateRenders,
    agentSession,
    setAgentSession,
    forwardedParameters,
    agentLock,
    threadId,
    setThreadId,
    runId,
    setRunId,
    chatAbortControllerRef,
    extensions,
    setExtensions,
    langGraphInterruptAction,
    setLangGraphInterruptAction
  } = useCopilotContext();
  const { messages, setMessages } = useCopilotMessagesContext();
  const [mcpServers, setLocalMcpServers] = (0, import_react8.useState)([]);
  (0, import_react8.useEffect)(() => {
    if (mcpServers.length > 0) {
      const serversCopy = [...mcpServers];
      copilotApiConfig.mcpServers = serversCopy;
      if (!copilotApiConfig.properties) {
        copilotApiConfig.properties = {};
      }
      copilotApiConfig.properties.mcpServers = serversCopy;
    }
  }, [mcpServers, copilotApiConfig]);
  const setMcpServers = (0, import_react8.useCallback)((servers) => {
    setLocalMcpServers(servers);
  }, []);
  const onCoAgentStateRender = useAsyncCallback(
    (args) => __async(this, null, function* () {
      var _a2;
      const { name, nodeName, state } = args;
      let action = Object.values(coAgentStateRenders).find(
        (action2) => action2.name === name && action2.nodeName === nodeName
      );
      if (!action) {
        action = Object.values(coAgentStateRenders).find(
          (action2) => action2.name === name && !action2.nodeName
        );
      }
      if (action) {
        yield (_a2 = action.handler) == null ? void 0 : _a2.call(action, { state, nodeName });
      }
    }),
    [coAgentStateRenders]
  );
  const makeSystemMessageCallback = (0, import_react8.useCallback)(() => {
    const systemMessageMaker = makeSystemMessage || defaultSystemMessage;
    const contextString = getContextString([], defaultCopilotContextCategories);
    return new import_runtime_client_gql4.TextMessage({
      content: systemMessageMaker(contextString, chatInstructions),
      role: import_runtime_client_gql4.Role.System
    });
  }, [getContextString, makeSystemMessage, chatInstructions]);
  const deleteMessage = (0, import_react8.useCallback)(
    (messageId) => {
      setMessages((prev) => prev.filter((message) => message.id !== messageId));
    },
    [setMessages]
  );
  const { append, reload, stop, runChatCompletion } = useChat(__spreadProps(__spreadValues({}, options), {
    actions: Object.values(actions),
    copilotConfig: copilotApiConfig,
    initialMessages: options.initialMessages || [],
    onFunctionCall: getFunctionCallHandler(),
    onCoAgentStateRender,
    messages,
    setMessages,
    makeSystemMessageCallback,
    isLoading,
    setIsLoading,
    coagentStatesRef,
    setCoagentStatesWithRef,
    agentSession,
    setAgentSession,
    forwardedParameters,
    threadId,
    setThreadId,
    runId,
    setRunId,
    chatAbortControllerRef,
    agentLock,
    extensions,
    setExtensions,
    langGraphInterruptAction,
    setLangGraphInterruptAction
  }));
  const latestAppend = useUpdatedRef(append);
  const latestAppendFunc = useAsyncCallback(
    (message, options2) => __async(this, null, function* () {
      return yield latestAppend.current(message, options2);
    }),
    [latestAppend]
  );
  const latestReload = useUpdatedRef(reload);
  const latestReloadFunc = useAsyncCallback(
    (messageId) => __async(this, null, function* () {
      return yield latestReload.current(messageId);
    }),
    [latestReload]
  );
  const latestStop = useUpdatedRef(stop);
  const latestStopFunc = (0, import_react8.useCallback)(() => {
    return latestStop.current();
  }, [latestStop]);
  const latestDelete = useUpdatedRef(deleteMessage);
  const latestDeleteFunc = (0, import_react8.useCallback)(
    (messageId) => {
      return latestDelete.current(messageId);
    },
    [latestDelete]
  );
  const latestSetMessages = useUpdatedRef(setMessages);
  const latestSetMessagesFunc = (0, import_react8.useCallback)(
    (messages2) => {
      return latestSetMessages.current(messages2);
    },
    [latestSetMessages]
  );
  const latestRunChatCompletion = useUpdatedRef(runChatCompletion);
  const latestRunChatCompletionFunc = useAsyncCallback(() => __async(this, null, function* () {
    return yield latestRunChatCompletion.current();
  }), [latestRunChatCompletion]);
  const reset = (0, import_react8.useCallback)(() => {
    latestStopFunc();
    setMessages([]);
    setRunId(null);
    setCoagentStatesWithRef({});
    let initialAgentSession = null;
    if (agentLock) {
      initialAgentSession = {
        agentName: agentLock
      };
    }
    setAgentSession(initialAgentSession);
  }, [
    latestStopFunc,
    setMessages,
    setThreadId,
    setCoagentStatesWithRef,
    setAgentSession,
    agentLock
  ]);
  const latestReset = useUpdatedRef(reset);
  const latestResetFunc = (0, import_react8.useCallback)(() => {
    return latestReset.current();
  }, [latestReset]);
  return {
    visibleMessages: messages,
    appendMessage: latestAppendFunc,
    setMessages: latestSetMessagesFunc,
    reloadMessages: latestReloadFunc,
    stopGeneration: latestStopFunc,
    reset: latestResetFunc,
    deleteMessage: latestDeleteFunc,
    runChatCompletion: latestRunChatCompletionFunc,
    isLoading,
    mcpServers,
    setMcpServers
  };
}
function useUpdatedRef(value) {
  const ref = (0, import_react8.useRef)(value);
  (0, import_react8.useEffect)(() => {
    ref.current = value;
  }, [value]);
  return ref;
}
function defaultSystemMessage(contextString, additionalInstructions) {
  return `
Please act as an efficient, competent, conscientious, and industrious professional assistant.

Help the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.
Always be polite and respectful, and prefer brevity over verbosity.

The user has provided you with the following context:
\`\`\`
${contextString}
\`\`\`

They have also provided you with functions you can call to initiate actions on their behalf, or functions you can call to receive more information.

Please assist them as best you can.

You can ask them for clarifying questions if needed, but don't be annoying about it. If you can reasonably 'fill in the blanks' yourself, do so.

If you would like to call a function, call it without saying anything else.
In case of a function error:
- If this error stems from incorrect function parameters or syntax, you may retry with corrected arguments.
- If the error's source is unclear or seems unrelated to your input, do not attempt further retries.
` + (additionalInstructions ? `

${additionalInstructions}` : "");
}

// src/hooks/use-coagent.ts
var import_shared4 = require("@copilotkit/shared");
function useCoAgent(options) {
  const generalContext = useCopilotContext();
  const { availableAgents } = generalContext;
  const { addToast } = useToast();
  const lastLoadedThreadId = (0, import_react9.useRef)();
  const lastLoadedState = (0, import_react9.useRef)();
  const { name } = options;
  (0, import_react9.useEffect)(() => {
    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === name)) {
      const message = `(useCoAgent): Agent "${name}" not found. Make sure the agent exists and is properly configured.`;
      console.warn(message);
      addToast({ type: "warning", message });
    }
  }, [availableAgents]);
  const messagesContext = useCopilotMessagesContext();
  const context = __spreadValues(__spreadValues({}, generalContext), messagesContext);
  const { coagentStates, coagentStatesRef, setCoagentStatesWithRef, threadId, copilotApiConfig } = context;
  const { appendMessage, runChatCompletion } = useCopilotChat();
  const runtimeClient = useCopilotRuntimeClient({
    url: copilotApiConfig.chatApiEndpoint,
    publicApiKey: copilotApiConfig.publicApiKey,
    credentials: copilotApiConfig.credentials
  });
  const setState = (0, import_react9.useCallback)(
    (newState) => {
      let coagentState = getCoagentState({ coagentStates, name, options });
      const updatedState = typeof newState === "function" ? newState(coagentState.state) : newState;
      setCoagentStatesWithRef(__spreadProps(__spreadValues({}, coagentStatesRef.current), {
        [name]: __spreadProps(__spreadValues({}, coagentState), {
          state: updatedState
        })
      }));
    },
    [coagentStates, name]
  );
  (0, import_react9.useEffect)(() => {
    const fetchAgentState = () => __async(this, null, function* () {
      var _a, _b, _c, _d;
      if (!threadId || threadId === lastLoadedThreadId.current)
        return;
      const result = yield runtimeClient.loadAgentState({
        threadId,
        agentName: name
      });
      const newState = (_b = (_a = result.data) == null ? void 0 : _a.loadAgentState) == null ? void 0 : _b.state;
      if (newState === lastLoadedState.current)
        return;
      if (((_d = (_c = result.data) == null ? void 0 : _c.loadAgentState) == null ? void 0 : _d.threadExists) && newState && newState != "{}") {
        lastLoadedState.current = newState;
        lastLoadedThreadId.current = threadId;
        const fetchedState = (0, import_shared4.parseJson)(newState, {});
        isExternalStateManagement(options) ? options.setState(fetchedState) : setState(fetchedState);
      }
    });
    void fetchAgentState();
  }, [threadId]);
  (0, import_react9.useEffect)(() => {
    if (isExternalStateManagement(options)) {
      setState(options.state);
    } else if (coagentStates[name] === void 0) {
      setState(options.initialState === void 0 ? {} : options.initialState);
    }
  }, [
    isExternalStateManagement(options) ? JSON.stringify(options.state) : void 0,
    // reset initialstate on reset
    coagentStates[name] === void 0
  ]);
  const runAgentCallback = useAsyncCallback(
    (hint) => __async(this, null, function* () {
      yield runAgent(name, context, appendMessage, runChatCompletion, hint);
    }),
    [name, context, appendMessage, runChatCompletion]
  );
  return (0, import_react9.useMemo)(() => {
    const coagentState = getCoagentState({ coagentStates, name, options });
    return {
      name,
      nodeName: coagentState.nodeName,
      threadId: coagentState.threadId,
      running: coagentState.running,
      state: coagentState.state,
      setState: isExternalStateManagement(options) ? options.setState : setState,
      start: () => startAgent(name, context),
      stop: () => stopAgent(name, context),
      run: runAgentCallback
    };
  }, [name, coagentStates, options, setState, runAgentCallback]);
}
function startAgent(name, context) {
  const { setAgentSession } = context;
  setAgentSession({
    agentName: name
  });
}
function stopAgent(name, context) {
  const { agentSession, setAgentSession } = context;
  if (agentSession && agentSession.agentName === name) {
    setAgentSession(null);
    context.setCoagentStates((prevAgentStates) => {
      return __spreadProps(__spreadValues({}, prevAgentStates), {
        [name]: __spreadProps(__spreadValues({}, prevAgentStates[name]), {
          running: false,
          active: false,
          threadId: void 0,
          nodeName: void 0,
          runId: void 0
        })
      });
    });
  } else {
    console.warn(`No agent session found for ${name}`);
  }
}
function runAgent(name, context, appendMessage, runChatCompletion, hint) {
  return __async(this, null, function* () {
    var _a, _b;
    const { agentSession, setAgentSession } = context;
    if (!agentSession || agentSession.agentName !== name) {
      setAgentSession({
        agentName: name
      });
    }
    let previousState = null;
    for (let i = context.messages.length - 1; i >= 0; i--) {
      const message = context.messages[i];
      if (message.isAgentStateMessage() && message.agentName === name) {
        previousState = message.state;
      }
    }
    let state = ((_b = (_a = context.coagentStatesRef.current) == null ? void 0 : _a[name]) == null ? void 0 : _b.state) || {};
    if (hint) {
      const hintMessage = hint({ previousState, currentState: state });
      if (hintMessage) {
        yield appendMessage(hintMessage);
      } else {
        yield runChatCompletion();
      }
    } else {
      yield runChatCompletion();
    }
  });
}
var isExternalStateManagement = (options) => {
  return "state" in options && "setState" in options;
};
var isInternalStateManagementWithInitial = (options) => {
  return "initialState" in options;
};
var getCoagentState = ({
  coagentStates,
  name,
  options
}) => {
  if (coagentStates[name]) {
    return coagentStates[name];
  } else {
    return {
      name,
      state: isInternalStateManagementWithInitial(options) ? options.initialState : {},
      config: options.config ? options.config : options.configurable ? { configurable: options.configurable } : {},
      running: false,
      active: false,
      threadId: void 0,
      nodeName: void 0,
      runId: void 0
    };
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  runAgent,
  startAgent,
  stopAgent,
  useCoAgent
});
//# sourceMappingURL=use-coagent.js.map