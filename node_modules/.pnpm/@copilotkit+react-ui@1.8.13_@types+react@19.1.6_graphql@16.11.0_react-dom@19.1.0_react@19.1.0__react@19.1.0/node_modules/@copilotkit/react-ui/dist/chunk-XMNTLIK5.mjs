import {
  Markdown
} from "./chunk-YTXEWDNC.mjs";
import {
  useChatContext
} from "./chunk-IEMQ2SQW.mjs";

// src/components/chat/messages/AssistantMessage.tsx
import { useState } from "react";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var AssistantMessage = (props) => {
  const { icons, labels } = useChatContext();
  const {
    message,
    isLoading,
    subComponent,
    onRegenerate,
    onCopy,
    onThumbsUp,
    onThumbsDown,
    isCurrentMessage,
    markdownTagRenderers
  } = props;
  const [copied, setCopied] = useState(false);
  const handleCopy = () => {
    if (message && onCopy) {
      navigator.clipboard.writeText(message);
      setCopied(true);
      onCopy(message);
      setTimeout(() => setCopied(false), 2e3);
    } else if (message) {
      navigator.clipboard.writeText(message);
      setCopied(true);
      setTimeout(() => setCopied(false), 2e3);
    }
  };
  const handleRegenerate = () => {
    if (onRegenerate) {
      onRegenerate();
    }
  };
  const handleThumbsUp = () => {
    if (onThumbsUp && message) {
      onThumbsUp(message);
    }
  };
  const handleThumbsDown = () => {
    if (onThumbsDown && message) {
      onThumbsDown(message);
    }
  };
  const LoadingIcon = () => /* @__PURE__ */ jsx("span", { children: icons.activityIcon });
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    (message || isLoading) && /* @__PURE__ */ jsxs("div", { className: "copilotKitMessage copilotKitAssistantMessage", children: [
      message && /* @__PURE__ */ jsx(Markdown, { content: message || "", components: markdownTagRenderers }),
      isLoading && /* @__PURE__ */ jsx(LoadingIcon, {}),
      message && !isLoading && /* @__PURE__ */ jsxs(
        "div",
        {
          className: `copilotKitMessageControls ${isCurrentMessage ? "currentMessage" : ""}`,
          children: [
            /* @__PURE__ */ jsx(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleRegenerate,
                "aria-label": labels.regenerateResponse,
                title: labels.regenerateResponse,
                children: icons.regenerateIcon
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleCopy,
                "aria-label": labels.copyToClipboard,
                title: labels.copyToClipboard,
                children: copied ? /* @__PURE__ */ jsx("span", { style: { fontSize: "10px", fontWeight: "bold" }, children: "\u2713" }) : icons.copyIcon
              }
            ),
            onThumbsUp && /* @__PURE__ */ jsx(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleThumbsUp,
                "aria-label": labels.thumbsUp,
                title: labels.thumbsUp,
                children: icons.thumbsUpIcon
              }
            ),
            onThumbsDown && /* @__PURE__ */ jsx(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleThumbsDown,
                "aria-label": labels.thumbsDown,
                title: labels.thumbsDown,
                children: icons.thumbsDownIcon
              }
            )
          ]
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { style: { marginBottom: "0.5rem" }, children: subComponent })
  ] });
};

export {
  AssistantMessage
};
//# sourceMappingURL=chunk-XMNTLIK5.mjs.map