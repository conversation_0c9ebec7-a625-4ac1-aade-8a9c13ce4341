export { AssistantMessageProps, ButtonProps, ComponentsMap, HeaderProps, InputProps, MessagesProps, RenderMessageProps, RenderSuggestionsListProps, Renderer, SuggestionsProps, UserMessageProps, WindowProps } from './components/chat/props.js';
export { CopilotPopup } from './components/chat/Popup.js';
export { CopilotSidebar } from './components/chat/Sidebar.js';
export { CopilotChat } from './components/chat/Chat.js';
export { Markdown } from './components/chat/Markdown.js';
export { AssistantMessage } from './components/chat/messages/AssistantMessage.js';
export { UserMessage } from './components/chat/messages/UserMessage.js';
export { useChatContext } from './components/chat/ChatContext.js';
export { RenderImageMessage } from './components/chat/messages/RenderImageMessage.js';
export { Suggestions as RenderSuggestionsList } from './components/chat/Suggestions.js';
export { Suggestion as RenderSuggestion } from './components/chat/Suggestion.js';
export { shouldShowDevConsole } from './components/dev-console/utils.js';
export { CopilotDevConsole } from './components/dev-console/console.js';
export { useCopilotChatSuggestions } from './hooks/use-copilot-chat-suggestions.js';
export { CopilotKitCSSProperties } from './types/css.js';
export { CopilotChatSuggestion } from './types/suggestions.js';
import '@copilotkit/runtime-client-gql';
import 'react';
import 'react/jsx-runtime';
import './components/chat/Modal.js';
import '@copilotkit/react-core';
import 'react-markdown';
import './components/dev-console/types.js';
