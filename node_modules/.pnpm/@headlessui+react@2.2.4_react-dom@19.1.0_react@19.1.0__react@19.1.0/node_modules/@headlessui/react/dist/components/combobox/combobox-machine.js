var S=Object.defineProperty;var I=(t,i,e)=>i in t?S(t,i,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[i]=e;var c=(t,i,e)=>(I(t,typeof i!="symbol"?i+"":i,e),e);import{Machine as h}from'../../machine.js';import{ActionTypes as R,stackMachines as A}from'../../machines/stack-machine.js';import{Focus as f,calculateActiveIndex as x}from'../../utils/calculate-active-index.js';import{sortByDomNode as E}from'../../utils/focus-management.js';import{match as g}from'../../utils/match.js';var C=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(C||{}),M=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(M||{}),F=(n=>(n[n.Pointer=0]="Pointer",n[n.Focus=1]="Focus",n[n.Other=2]="Other",n))(F||{}),_=(l=>(l[l.OpenCombobox=0]="OpenCombobox",l[l.CloseCombobox=1]="CloseCombobox",l[l.GoToOption=2]="GoToOption",l[l.SetTyping=3]="SetTyping",l[l.RegisterOption=4]="RegisterOption",l[l.UnregisterOption=5]="UnregisterOption",l[l.DefaultToFirstOption=6]="DefaultToFirstOption",l[l.SetActivationTrigger=7]="SetActivationTrigger",l[l.UpdateVirtualConfiguration=8]="UpdateVirtualConfiguration",l[l.SetInputElement=9]="SetInputElement",l[l.SetButtonElement=10]="SetButtonElement",l[l.SetOptionsElement=11]="SetOptionsElement",l))(_||{});function T(t,i=e=>e){let e=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,n=i(t.options.slice()),o=n.length>0&&n[0].dataRef.current.order!==null?n.sort((u,a)=>u.dataRef.current.order-a.dataRef.current.order):E(n,u=>u.dataRef.current.domRef.current),r=e?o.indexOf(e):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}let D={[1](t){var i;return(i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](t){var i,e;if((i=t.dataRef.current)!=null&&i.disabled||t.comboboxState===0)return t;if((e=t.dataRef.current)!=null&&e.value){let n=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(n!==-1)return{...t,activeOptionIndex:n,comboboxState:0,__demoMode:!1}}return{...t,comboboxState:0,__demoMode:!1}},[3](t,i){return t.isTyping===i.isTyping?t:{...t,isTyping:i.isTyping}},[2](t,i){var r,u,a,d;if((r=t.dataRef.current)!=null&&r.disabled||t.optionsElement&&!((u=t.dataRef.current)!=null&&u.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let{options:p,disabled:s}=t.virtual,b=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>p,resolveActiveIndex:()=>{var v,m;return(m=(v=t.activeOptionIndex)!=null?v:p.findIndex(y=>!s(y)))!=null?m:null},resolveDisabled:s,resolveId(){throw new Error("Function not implemented.")}}),l=(a=i.trigger)!=null?a:2;return t.activeOptionIndex===b&&t.activationTrigger===l?t:{...t,activeOptionIndex:b,activationTrigger:l,isTyping:!1,__demoMode:!1}}let e=T(t);if(e.activeOptionIndex===null){let p=e.options.findIndex(s=>!s.dataRef.current.disabled);p!==-1&&(e.activeOptionIndex=p)}let n=i.focus===f.Specific?i.idx:x(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled}),o=(d=i.trigger)!=null?d:2;return t.activeOptionIndex===n&&t.activationTrigger===o?t:{...t,...e,isTyping:!1,activeOptionIndex:n,activationTrigger:o,__demoMode:!1}},[4]:(t,i)=>{var r,u,a,d;if((r=t.dataRef.current)!=null&&r.virtual)return{...t,options:[...t.options,i.payload]};let e=i.payload,n=T(t,p=>(p.push(e),p));t.activeOptionIndex===null&&(a=(u=t.dataRef.current).isSelected)!=null&&a.call(u,i.payload.dataRef.current.value)&&(n.activeOptionIndex=n.options.indexOf(e));let o={...t,...n,activationTrigger:2};return(d=t.dataRef.current)!=null&&d.__demoMode&&t.dataRef.current.value===void 0&&(o.activeOptionIndex=0),o},[5]:(t,i)=>{var n;if((n=t.dataRef.current)!=null&&n.virtual)return{...t,options:t.options.filter(o=>o.id!==i.id)};let e=T(t,o=>{let r=o.findIndex(u=>u.id===i.id);return r!==-1&&o.splice(r,1),o});return{...t,...e,activationTrigger:2}},[6]:(t,i)=>t.defaultToFirstOption===i.value?t:{...t,defaultToFirstOption:i.value},[7]:(t,i)=>t.activationTrigger===i.trigger?t:{...t,activationTrigger:i.trigger},[8]:(t,i)=>{var n,o;if(t.virtual===null)return{...t,virtual:{options:i.options,disabled:(n=i.disabled)!=null?n:()=>!1}};if(t.virtual.options===i.options&&t.virtual.disabled===i.disabled)return t;let e=t.activeOptionIndex;if(t.activeOptionIndex!==null){let r=i.options.indexOf(t.virtual.options[t.activeOptionIndex]);r!==-1?e=r:e=null}return{...t,activeOptionIndex:e,virtual:{options:i.options,disabled:(o=i.disabled)!=null?o:()=>!1}}},[9]:(t,i)=>t.inputElement===i.element?t:{...t,inputElement:i.element},[10]:(t,i)=>t.buttonElement===i.element?t:{...t,buttonElement:i.element},[11]:(t,i)=>t.optionsElement===i.element?t:{...t,optionsElement:i.element}};class O extends h{constructor(e){super(e);c(this,"actions",{onChange:e=>{let{onChange:n,compare:o,mode:r,value:u}=this.state.dataRef.current;return g(r,{[0]:()=>n==null?void 0:n(e),[1]:()=>{let a=u.slice(),d=a.findIndex(p=>o(p,e));return d===-1?a.push(e):a.splice(d,1),n==null?void 0:n(a)}})},registerOption:(e,n)=>(this.send({type:4,payload:{id:e,dataRef:n}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(n.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:e})}),goToOption:(e,n)=>(this.send({type:6,value:!1}),this.send({type:2,...e,trigger:n})),setIsTyping:e=>{this.send({type:3,isTyping:e})},closeCombobox:()=>{var e,n;this.send({type:1}),this.send({type:6,value:!1}),(n=(e=this.state.dataRef.current).onClose)==null||n.call(e)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:e=>{this.send({type:7,trigger:e})},selectActiveOption:()=>{let e=this.selectors.activeOptionIndex(this.state);if(e!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[e]);else{let{dataRef:n}=this.state.options[e];this.actions.onChange(n.current.value)}this.actions.goToOption({focus:f.Specific,idx:e})}},setInputElement:e=>{this.send({type:9,element:e})},setButtonElement:e=>{this.send({type:10,element:e})},setOptionsElement:e=>{this.send({type:11,element:e})}});c(this,"selectors",{activeDescendantId:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);if(n!==null)return e.virtual?(r=e.options.find(u=>!u.dataRef.current.disabled&&e.dataRef.current.compare(u.dataRef.current.value,e.virtual.options[n])))==null?void 0:r.id:(o=e.options[n])==null?void 0:o.id},activeOptionIndex:e=>{if(e.defaultToFirstOption&&e.activeOptionIndex===null&&(e.virtual?e.virtual.options.length>0:e.options.length>0)){if(e.virtual){let{options:o,disabled:r}=e.virtual,u=o.findIndex(a=>{var d;return!((d=r==null?void 0:r(a))!=null&&d)});if(u!==-1)return u}let n=e.options.findIndex(o=>!o.dataRef.current.disabled);if(n!==-1)return n}return e.activeOptionIndex},activeOption:e=>{var o,r;let n=this.selectors.activeOptionIndex(e);return n===null?null:e.virtual?e.virtual.options[n!=null?n:0]:(r=(o=e.options[n])==null?void 0:o.dataRef.current.value)!=null?r:null},isActive:(e,n,o)=>{var u;let r=this.selectors.activeOptionIndex(e);return r===null?!1:e.virtual?r===e.dataRef.current.calculateIndex(n):((u=e.options[r])==null?void 0:u.id)===o},shouldScrollIntoView:(e,n,o)=>!(e.virtual||e.__demoMode||e.comboboxState!==0||e.activationTrigger===0||!this.selectors.isActive(e,n,o))});{let n=this.state.id,o=A.get(null);this.disposables.add(o.on(R.Push,r=>{!o.selectors.isTop(r,n)&&this.state.comboboxState===0&&this.actions.closeCombobox()})),this.on(0,()=>o.actions.push(n)),this.on(1,()=>o.actions.pop(n))}}static new({id:e,virtual:n=null,__demoMode:o=!1}){var r;return new O({id:e,dataRef:{current:{}},comboboxState:o?0:1,isTyping:!1,options:[],virtual:n?{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:o})}reduce(e,n){return g(n.type,D,e,n)}}export{_ as ActionTypes,F as ActivationTrigger,O as ComboboxMachine,C as ComboboxState,M as ValueMode};
