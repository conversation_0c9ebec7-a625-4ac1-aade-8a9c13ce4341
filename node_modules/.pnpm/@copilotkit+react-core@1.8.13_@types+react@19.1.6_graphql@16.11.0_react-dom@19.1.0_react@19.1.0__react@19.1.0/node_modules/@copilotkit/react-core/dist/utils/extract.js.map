{"version": 3, "sources": ["../../src/utils/extract.ts", "../../src/components/copilot-provider/copilotkit.tsx"], "sourcesContent": ["import {\n  Action,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  MappedParameterTypes,\n  Parameter,\n  actionParametersToJsonSchema,\n} from \"@copilotkit/shared\";\nimport {\n  ActionExecutionMessage,\n  Message,\n  Role,\n  TextMessage,\n  convertGqlOutputToMessages,\n  CopilotRequestType,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { CopilotContextParams, CopilotMessagesContextParams } from \"../context\";\nimport { defaultCopilotContextCategories } from \"../components\";\nimport { CopilotRuntimeClient } from \"@copilotkit/runtime-client-gql\";\nimport {\n  convertMessagesToGqlInput,\n  filterAgentStateMessages,\n} from \"@copilotkit/runtime-client-gql\";\n\ninterface InitialState<T extends Parameter[] | [] = []> {\n  status: \"initial\";\n  args: Partial<MappedParameterTypes<T>>;\n}\n\ninterface InProgressState<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n}\n\ninterface CompleteState<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n}\n\ntype StreamHandlerArgs<T extends Parameter[] | [] = []> =\n  | InitialState<T>\n  | InProgressState<T>\n  | CompleteState<T>;\n\ninterface ExtractOptions<T extends Parameter[]> {\n  context: CopilotContextParams & CopilotMessagesContextParams;\n  instructions: string;\n  parameters: T;\n  include?: IncludeOptions;\n  data?: any;\n  abortSignal?: AbortSignal;\n  stream?: (args: StreamHandlerArgs<T>) => void;\n  requestType?: CopilotRequestType;\n  forwardedParameters?: ForwardedParametersInput;\n}\n\ninterface IncludeOptions {\n  readable?: boolean;\n  messages?: boolean;\n}\n\nexport async function extract<const T extends Parameter[]>({\n  context,\n  instructions,\n  parameters,\n  include,\n  data,\n  abortSignal,\n  stream,\n  requestType = CopilotRequestType.Task,\n  forwardedParameters,\n}: ExtractOptions<T>): Promise<MappedParameterTypes<T>> {\n  const { messages } = context;\n\n  const action: Action<any> = {\n    name: \"extract\",\n    description: instructions,\n    parameters,\n    handler: (args: any) => {},\n  };\n\n  const includeReadable = include?.readable ?? false;\n  const includeMessages = include?.messages ?? false;\n\n  let contextString = \"\";\n\n  if (data) {\n    contextString = (typeof data === \"string\" ? data : JSON.stringify(data)) + \"\\n\\n\";\n  }\n\n  if (includeReadable) {\n    contextString += context.getContextString([], defaultCopilotContextCategories);\n  }\n\n  const systemMessage: Message = new TextMessage({\n    content: makeSystemMessage(contextString, instructions),\n    role: Role.System,\n  });\n\n  const instructionsMessage: Message = new TextMessage({\n    content: makeInstructionsMessage(instructions),\n    role: Role.User,\n  });\n\n  const response = context.runtimeClient.asStream(\n    context.runtimeClient.generateCopilotResponse({\n      data: {\n        frontend: {\n          actions: [\n            {\n              name: action.name,\n              description: action.description || \"\",\n              jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters || [])),\n            },\n          ],\n          url: window.location.href,\n        },\n\n        messages: convertMessagesToGqlInput(\n          includeMessages\n            ? [systemMessage, instructionsMessage, ...filterAgentStateMessages(messages)]\n            : [systemMessage, instructionsMessage],\n        ),\n        metadata: {\n          requestType: requestType,\n        },\n        forwardedParameters: {\n          ...(forwardedParameters ?? {}),\n          toolChoice: \"function\",\n          toolChoiceFunctionName: action.name,\n        },\n      },\n      properties: context.copilotApiConfig.properties,\n      signal: abortSignal,\n    }),\n  );\n\n  const reader = response.getReader();\n\n  let isInitial = true;\n\n  let actionExecutionMessage: ActionExecutionMessage | undefined = undefined;\n\n  while (true) {\n    const { done, value } = await reader.read();\n\n    if (done) {\n      break;\n    }\n\n    if (abortSignal?.aborted) {\n      throw new Error(\"Aborted\");\n    }\n\n    actionExecutionMessage = convertGqlOutputToMessages(\n      value.generateCopilotResponse.messages,\n    ).find((msg) => msg.isActionExecutionMessage()) as ActionExecutionMessage | undefined;\n\n    if (!actionExecutionMessage) {\n      continue;\n    }\n\n    stream?.({\n      status: isInitial ? \"initial\" : \"inProgress\",\n      args: actionExecutionMessage.arguments as Partial<MappedParameterTypes<T>>,\n    });\n\n    isInitial = false;\n  }\n\n  if (!actionExecutionMessage) {\n    throw new Error(\"extract() failed: No function call occurred\");\n  }\n\n  stream?.({\n    status: \"complete\",\n    args: actionExecutionMessage.arguments as MappedParameterTypes<T>,\n  });\n\n  return actionExecutionMessage.arguments as MappedParameterTypes<T>;\n}\n\n// We need to put this in a user message since some LLMs need\n// at least one user message to function\nfunction makeInstructionsMessage(instructions: string): string {\n  return `\nThe user has given you the following task to complete:\n\n\\`\\`\\`\n${instructions}\n\\`\\`\\`\n\nAny additional messages provided are for providing context only and should not be used to ask questions or engage in conversation.\n`;\n}\n\nfunction makeSystemMessage(contextString: string, instructions: string): string {\n  return `\nPlease act as an efficient, competent, conscientious, and industrious professional assistant.\n\nHelp the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.\nAlways be polite and respectful, and prefer brevity over verbosity.\n\nThe user has provided you with the following context:\n\\`\\`\\`\n${contextString}\n\\`\\`\\`\n\nThey have also provided you with a function called extract you MUST call to initiate actions on their behalf.\n\nPlease assist them as best you can.\n\nThis is not a conversation, so please do not ask questions. Just call the function without saying anything else.\n`;\n}\n", "/**\n * This component will typically wrap your entire application (or a sub-tree of your application where you want to have a copilot). It provides the copilot context to all other components and hooks.\n *\n * ## Example\n *\n * You can find more information about self-hosting CopilotKit [here](/guides/self-hosting).\n *\n * ```tsx\n * import { CopilotKit } from \"@copilotkit/react-core\";\n *\n * <CopilotKit runtimeUrl=\"<your-runtime-url>\">\n *   // ... your app ...\n * </CopilotKit>\n * ```\n */\n\nimport { useCallback, useEffect, useMemo, useRef, useState, SetStateAction } from \"react\";\nimport {\n  CopilotContext,\n  CopilotApiConfig,\n  ChatComponentsCache,\n  AgentSession,\n  AuthState,\n} from \"../../context/copilot-context\";\nimport useTree from \"../../hooks/use-tree\";\nimport { CopilotChatSuggestionConfiguration, DocumentPointer } from \"../../types\";\nimport { flushSync } from \"react-dom\";\nimport {\n  COPILOT_CLOUD_CHAT_URL,\n  CopilotCloudConfig,\n  FunctionCallHandler,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  randomUUID,\n  ConfigurationError,\n  MissingPublicApiKeyError,\n} from \"@copilotkit/shared\";\nimport { FrontendAction } from \"../../types/frontend-action\";\nimport useFlatCategoryStore from \"../../hooks/use-flat-category-store\";\nimport { CopilotKitProps } from \"./copilotkit-props\";\nimport { CoAgentStateRender } from \"../../types/coagent-action\";\nimport { CoagentState } from \"../../types/coagent-state\";\nimport { CopilotMessages } from \"./copilot-messages\";\nimport { ToastProvider } from \"../toast/toast-provider\";\nimport { useCopilotRuntimeClient } from \"../../hooks/use-copilot-runtime-client\";\nimport { shouldShowDevConsole } from \"../../utils\";\nimport { CopilotErrorBoundary } from \"../error-boundary/error-boundary\";\nimport { Agent, ExtensionsInput } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetterArgs,\n} from \"../../types/interrupt-action\";\n\nexport function CopilotKit({ children, ...props }: CopilotKitProps) {\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n  const enabled = shouldShowDevConsole(showDevConsole);\n\n  return (\n    <ToastProvider enabled={enabled}>\n      <CopilotErrorBoundary publicApiKey={props.publicApiKey} showUsageBanner={enabled}>\n        <CopilotKitInternal {...props}>{children}</CopilotKitInternal>\n      </CopilotErrorBoundary>\n    </ToastProvider>\n  );\n}\n\nexport function CopilotKitInternal(cpkProps: CopilotKitProps) {\n  const { children, ...props } = cpkProps;\n\n  /**\n   * This will throw an error if the props are invalid.\n   */\n  validateProps(cpkProps);\n\n  const chatApiEndpoint = props.runtimeUrl || COPILOT_CLOUD_CHAT_URL;\n\n  const [actions, setActions] = useState<Record<string, FrontendAction<any>>>({});\n  const [coAgentStateRenders, setCoAgentStateRenders] = useState<\n    Record<string, CoAgentStateRender<any>>\n  >({});\n\n  const chatComponentsCache = useRef<ChatComponentsCache>({\n    actions: {},\n    coAgentStateRenders: {},\n  });\n\n  const { addElement, removeElement, printTree } = useTree();\n  const [isLoading, setIsLoading] = useState(false);\n  const [chatInstructions, setChatInstructions] = useState(\"\");\n  const [authStates, setAuthStates] = useState<Record<string, AuthState>>({});\n  const [extensions, setExtensions] = useState<ExtensionsInput>({});\n  const [additionalInstructions, setAdditionalInstructions] = useState<string[]>([]);\n\n  const {\n    addElement: addDocument,\n    removeElement: removeDocument,\n    allElements: allDocuments,\n  } = useFlatCategoryStore<DocumentPointer>();\n\n  // Compute all the functions and properties that we need to pass\n\n  const setAction = useCallback((id: string, action: FrontendAction<any>) => {\n    setActions((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: action,\n      };\n    });\n  }, []);\n\n  const removeAction = useCallback((id: string) => {\n    setActions((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const setCoAgentStateRender = useCallback((id: string, stateRender: CoAgentStateRender<any>) => {\n    setCoAgentStateRenders((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: stateRender,\n      };\n    });\n  }, []);\n\n  const removeCoAgentStateRender = useCallback((id: string) => {\n    setCoAgentStateRenders((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const getContextString = useCallback(\n    (documents: DocumentPointer[], categories: string[]) => {\n      const documentsString = documents\n        .map((document) => {\n          return `${document.name} (${document.sourceApplication}):\\n${document.getContents()}`;\n        })\n        .join(\"\\n\\n\");\n\n      const nonDocumentStrings = printTree(categories);\n\n      return `${documentsString}\\n\\n${nonDocumentStrings}`;\n    },\n    [printTree],\n  );\n\n  const addContext = useCallback(\n    (\n      context: string,\n      parentId?: string,\n      categories: string[] = defaultCopilotContextCategories,\n    ) => {\n      return addElement(context, categories, parentId);\n    },\n    [addElement],\n  );\n\n  const removeContext = useCallback(\n    (id: string) => {\n      removeElement(id);\n    },\n    [removeElement],\n  );\n\n  const getFunctionCallHandler = useCallback(\n    (customEntryPoints?: Record<string, FrontendAction<any>>) => {\n      return entryPointsToFunctionCallHandler(Object.values(customEntryPoints || actions));\n    },\n    [actions],\n  );\n\n  const getDocumentsContext = useCallback(\n    (categories: string[]) => {\n      return allDocuments(categories);\n    },\n    [allDocuments],\n  );\n\n  const addDocumentContext = useCallback(\n    (documentPointer: DocumentPointer, categories: string[] = defaultCopilotContextCategories) => {\n      return addDocument(documentPointer, categories);\n    },\n    [addDocument],\n  );\n\n  const removeDocumentContext = useCallback(\n    (documentId: string) => {\n      removeDocument(documentId);\n    },\n    [removeDocument],\n  );\n\n  // get the appropriate CopilotApiConfig from the props\n  const copilotApiConfig: CopilotApiConfig = useMemo(() => {\n    let cloud: CopilotCloudConfig | undefined = undefined;\n    if (props.publicApiKey) {\n      cloud = {\n        guardrails: {\n          input: {\n            restrictToTopic: {\n              enabled: Boolean(props.guardrails_c),\n              validTopics: props.guardrails_c?.validTopics || [],\n              invalidTopics: props.guardrails_c?.invalidTopics || [],\n            },\n          },\n        },\n      };\n    }\n\n    return {\n      publicApiKey: props.publicApiKey,\n      ...(cloud ? { cloud } : {}),\n      chatApiEndpoint: chatApiEndpoint,\n      headers: props.headers || {},\n      properties: props.properties || {},\n      transcribeAudioUrl: props.transcribeAudioUrl,\n      textToSpeechUrl: props.textToSpeechUrl,\n      credentials: props.credentials,\n    };\n  }, [\n    props.publicApiKey,\n    props.headers,\n    props.properties,\n    props.transcribeAudioUrl,\n    props.textToSpeechUrl,\n    props.credentials,\n    props.cloudRestrictToTopic,\n    props.guardrails_c,\n  ]);\n\n  const headers = useMemo(() => {\n    const authHeaders = Object.values(authStates || {}).reduce((acc, state) => {\n      if (state.status === \"authenticated\" && state.authHeaders) {\n        return {\n          ...acc,\n          ...Object.entries(state.authHeaders).reduce(\n            (headers, [key, value]) => ({\n              ...headers,\n              [key.startsWith(\"X-Custom-\") ? key : `X-Custom-${key}`]: value,\n            }),\n            {},\n          ),\n        };\n      }\n      return acc;\n    }, {});\n\n    return {\n      ...(copilotApiConfig.headers || {}),\n      ...(copilotApiConfig.publicApiKey\n        ? { [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: copilotApiConfig.publicApiKey }\n        : {}),\n      ...authHeaders,\n    };\n  }, [copilotApiConfig.headers, copilotApiConfig.publicApiKey, authStates]);\n\n  const runtimeClient = useCopilotRuntimeClient({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    headers,\n    credentials: copilotApiConfig.credentials,\n  });\n\n  const [chatSuggestionConfiguration, setChatSuggestionConfiguration] = useState<{\n    [key: string]: CopilotChatSuggestionConfiguration;\n  }>({});\n\n  const addChatSuggestionConfiguration = (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => {\n    setChatSuggestionConfiguration((prev) => ({ ...prev, [id]: suggestion }));\n  };\n\n  const removeChatSuggestionConfiguration = (id: string) => {\n    setChatSuggestionConfiguration((prev) => {\n      const { [id]: _, ...rest } = prev;\n      return rest;\n    });\n  };\n\n  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);\n  const [coagentStates, setCoagentStates] = useState<Record<string, CoagentState>>({});\n  const coagentStatesRef = useRef<Record<string, CoagentState>>({});\n  const setCoagentStatesWithRef = useCallback(\n    (\n      value:\n        | Record<string, CoagentState>\n        | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n    ) => {\n      const newValue = typeof value === \"function\" ? value(coagentStatesRef.current) : value;\n      coagentStatesRef.current = newValue;\n      setCoagentStates((prev) => {\n        return newValue;\n      });\n    },\n    [],\n  );\n  const hasLoadedAgents = useRef(false);\n\n  useEffect(() => {\n    if (hasLoadedAgents.current) return;\n\n    const fetchData = async () => {\n      const result = await runtimeClient.availableAgents();\n      if (result.data?.availableAgents) {\n        setAvailableAgents(result.data.availableAgents.agents);\n      }\n      hasLoadedAgents.current = true;\n    };\n    void fetchData();\n  }, []);\n\n  let initialAgentSession: AgentSession | null = null;\n  if (props.agent) {\n    initialAgentSession = {\n      agentName: props.agent,\n    };\n  }\n\n  const [agentSession, setAgentSession] = useState<AgentSession | null>(initialAgentSession);\n\n  // Update agentSession when props.agent changes\n  useEffect(() => {\n    if (props.agent) {\n      setAgentSession({\n        agentName: props.agent,\n      });\n    } else {\n      setAgentSession(null);\n    }\n  }, [props.agent]);\n\n  const [internalThreadId, setInternalThreadId] = useState<string>(props.threadId || randomUUID());\n  const setThreadId = useCallback(\n    (value: SetStateAction<string>) => {\n      if (props.threadId) {\n        throw new Error(\"Cannot call setThreadId() when threadId is provided via props.\");\n      }\n      setInternalThreadId(value);\n    },\n    [props.threadId],\n  );\n\n  // update the internal threadId if the props.threadId changes\n  useEffect(() => {\n    if (props.threadId !== undefined) {\n      setInternalThreadId(props.threadId);\n    }\n  }, [props.threadId]);\n\n  const [runId, setRunId] = useState<string | null>(null);\n\n  const chatAbortControllerRef = useRef<AbortController | null>(null);\n\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n\n  const [langGraphInterruptAction, _setLangGraphInterruptAction] =\n    useState<LangGraphInterruptAction | null>(null);\n  const setLangGraphInterruptAction = useCallback((action: LangGraphInterruptActionSetterArgs) => {\n    _setLangGraphInterruptAction((prev) => {\n      if (prev == null) return action as LangGraphInterruptAction;\n      if (action == null) return null;\n      let event = prev.event;\n      if (action.event) {\n        // @ts-ignore\n        event = { ...prev.event, ...action.event };\n      }\n      return { ...prev, ...action, event };\n    });\n  }, []);\n  const removeLangGraphInterruptAction = useCallback((): void => {\n    setLangGraphInterruptAction(null);\n  }, []);\n\n  return (\n    <CopilotContext.Provider\n      value={{\n        actions,\n        chatComponentsCache,\n        getFunctionCallHandler,\n        setAction,\n        removeAction,\n        coAgentStateRenders,\n        setCoAgentStateRender,\n        removeCoAgentStateRender,\n        getContextString,\n        addContext,\n        removeContext,\n        getDocumentsContext,\n        addDocumentContext,\n        removeDocumentContext,\n        copilotApiConfig: copilotApiConfig,\n        isLoading,\n        setIsLoading,\n        chatSuggestionConfiguration,\n        addChatSuggestionConfiguration,\n        removeChatSuggestionConfiguration,\n        chatInstructions,\n        setChatInstructions,\n        additionalInstructions,\n        setAdditionalInstructions,\n        showDevConsole,\n        coagentStates,\n        setCoagentStates,\n        coagentStatesRef,\n        setCoagentStatesWithRef,\n        agentSession,\n        setAgentSession,\n        runtimeClient,\n        forwardedParameters: props.forwardedParameters || {},\n        agentLock: props.agent || null,\n        threadId: internalThreadId,\n        setThreadId,\n        runId,\n        setRunId,\n        chatAbortControllerRef,\n        availableAgents,\n        authConfig_c: props.authConfig_c,\n        authStates_c: authStates,\n        setAuthStates_c: setAuthStates,\n        extensions,\n        setExtensions,\n        langGraphInterruptAction,\n        setLangGraphInterruptAction,\n        removeLangGraphInterruptAction,\n      }}\n    >\n      <CopilotMessages>{children}</CopilotMessages>\n    </CopilotContext.Provider>\n  );\n}\n\nexport const defaultCopilotContextCategories = [\"global\"];\n\nfunction entryPointsToFunctionCallHandler(actions: FrontendAction<any>[]): FunctionCallHandler {\n  return async ({ name, args }) => {\n    let actionsByFunctionName: Record<string, FrontendAction<any>> = {};\n    for (let action of actions) {\n      actionsByFunctionName[action.name] = action;\n    }\n\n    const action = actionsByFunctionName[name];\n    let result: any = undefined;\n    if (action) {\n      await new Promise<void>((resolve, reject) => {\n        flushSync(async () => {\n          try {\n            result = await action.handler?.(args);\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n      });\n      await new Promise((resolve) => setTimeout(resolve, 20));\n    }\n    return result;\n  };\n}\n\nfunction formatFeatureName(featureName: string): string {\n  return featureName\n    .replace(/_c$/, \"\")\n    .split(\"_\")\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n    .join(\" \");\n}\n\nfunction validateProps(props: CopilotKitProps): never | void {\n  const cloudFeatures = Object.keys(props).filter((key) => key.endsWith(\"_c\"));\n\n  if (!props.runtimeUrl && !props.publicApiKey) {\n    throw new ConfigurationError(\"Missing required prop: 'runtimeUrl' or 'publicApiKey'\");\n  }\n\n  if (cloudFeatures.length > 0 && !props.publicApiKey) {\n    throw new MissingPublicApiKeyError(\n      `Missing required prop: 'publicApiKey' to use cloud features: ${cloudFeatures\n        .map(formatFeatureName)\n        .join(\", \")}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,iBAMO;AACP,gCAQO;;;ACCP,mBAAkF;AAUlF,uBAA0B;AAC1B,oBAQO;AAwBC;AAyXD,IAAM,kCAAkC,CAAC,QAAQ;;;ADjaxD,IAAAC,6BAGO;AAuCP,SAAsB,QAAqC,IAUH;AAAA,6CAVG;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,6CAAmB;AAAA,IACjC;AAAA,EACF,GAAwD;AAvExD;AAwEE,UAAM,EAAE,SAAS,IAAI;AAErB,UAAM,SAAsB;AAAA,MAC1B,MAAM;AAAA,MACN,aAAa;AAAA,MACb;AAAA,MACA,SAAS,CAAC,SAAc;AAAA,MAAC;AAAA,IAC3B;AAEA,UAAM,mBAAkB,wCAAS,aAAT,YAAqB;AAC7C,UAAM,mBAAkB,wCAAS,aAAT,YAAqB;AAE7C,QAAI,gBAAgB;AAEpB,QAAI,MAAM;AACR,uBAAiB,OAAO,SAAS,WAAW,OAAO,KAAK,UAAU,IAAI,KAAK;AAAA,IAC7E;AAEA,QAAI,iBAAiB;AACnB,uBAAiB,QAAQ,iBAAiB,CAAC,GAAG,+BAA+B;AAAA,IAC/E;AAEA,UAAM,gBAAyB,IAAI,sCAAY;AAAA,MAC7C,SAAS,kBAAkB,eAAe,YAAY;AAAA,MACtD,MAAM,+BAAK;AAAA,IACb,CAAC;AAED,UAAM,sBAA+B,IAAI,sCAAY;AAAA,MACnD,SAAS,wBAAwB,YAAY;AAAA,MAC7C,MAAM,+BAAK;AAAA,IACb,CAAC;AAED,UAAM,WAAW,QAAQ,cAAc;AAAA,MACrC,QAAQ,cAAc,wBAAwB;AAAA,QAC5C,MAAM;AAAA,UACJ,UAAU;AAAA,YACR,SAAS;AAAA,cACP;AAAA,gBACE,MAAM,OAAO;AAAA,gBACb,aAAa,OAAO,eAAe;AAAA,gBACnC,YAAY,KAAK,cAAU,6CAA6B,OAAO,cAAc,CAAC,CAAC,CAAC;AAAA,cAClF;AAAA,YACF;AAAA,YACA,KAAK,OAAO,SAAS;AAAA,UACvB;AAAA,UAEA,cAAU;AAAA,YACR,kBACI,CAAC,eAAe,qBAAqB,OAAG,qDAAyB,QAAQ,CAAC,IAC1E,CAAC,eAAe,mBAAmB;AAAA,UACzC;AAAA,UACA,UAAU;AAAA,YACR;AAAA,UACF;AAAA,UACA,qBAAqB,iCACf,oDAAuB,CAAC,IADT;AAAA,YAEnB,YAAY;AAAA,YACZ,wBAAwB,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,QACA,YAAY,QAAQ,iBAAiB;AAAA,QACrC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,UAAM,SAAS,SAAS,UAAU;AAElC,QAAI,YAAY;AAEhB,QAAI,yBAA6D;AAEjE,WAAO,MAAM;AACX,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAE1C,UAAI,MAAM;AACR;AAAA,MACF;AAEA,UAAI,2CAAa,SAAS;AACxB,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAEA,mCAAyB;AAAA,QACvB,MAAM,wBAAwB;AAAA,MAChC,EAAE,KAAK,CAAC,QAAQ,IAAI,yBAAyB,CAAC;AAE9C,UAAI,CAAC,wBAAwB;AAC3B;AAAA,MACF;AAEA,uCAAS;AAAA,QACP,QAAQ,YAAY,YAAY;AAAA,QAChC,MAAM,uBAAuB;AAAA,MAC/B;AAEA,kBAAY;AAAA,IACd;AAEA,QAAI,CAAC,wBAAwB;AAC3B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AAEA,qCAAS;AAAA,MACP,QAAQ;AAAA,MACR,MAAM,uBAAuB;AAAA,IAC/B;AAEA,WAAO,uBAAuB;AAAA,EAChC;AAAA;AAIA,SAAS,wBAAwB,cAA8B;AAC7D,SAAO;AAAA;AAAA;AAAA;AAAA,EAIP;AAAA;AAAA;AAAA;AAAA;AAKF;AAEA,SAAS,kBAAkB,eAAuB,cAA8B;AAC9E,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASF;", "names": ["import_shared", "import_runtime_client_gql"]}