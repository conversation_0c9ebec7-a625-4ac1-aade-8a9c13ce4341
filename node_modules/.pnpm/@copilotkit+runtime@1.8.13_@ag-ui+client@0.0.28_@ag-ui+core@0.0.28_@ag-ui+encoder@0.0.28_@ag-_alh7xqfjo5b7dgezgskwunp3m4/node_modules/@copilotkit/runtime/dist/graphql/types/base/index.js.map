{"version": 3, "sources": ["../../../../src/graphql/types/base/index.ts"], "sourcesContent": ["import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class BaseMessageInput {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => Date)\n  createdAt: Date;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0BAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMA,mBAAN,MAAMA;EAEXC;EAGAC;AACF;AANaF;;MACVG,2BAAM,MAAMC,MAAAA;;GADFJ,iBAAAA,WAAAA,MAAAA,MAAAA;;MAIVG,2BAAM,MAAME,IAAAA;qCACF,SAAA,cAAA,SAAA,IAAA;GALAL,iBAAAA,WAAAA,aAAAA,MAAAA;AAAAA,mBAAAA,aAAAA;MADZM,+BAAAA;GACYN,gBAAAA;", "names": ["BaseMessageInput", "id", "createdAt", "Field", "String", "Date", "InputType"]}