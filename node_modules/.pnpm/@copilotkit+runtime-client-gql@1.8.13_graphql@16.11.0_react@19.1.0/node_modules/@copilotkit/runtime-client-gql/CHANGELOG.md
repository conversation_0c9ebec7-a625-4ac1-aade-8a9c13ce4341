# @copilotkit/runtime-client-gql

## 1.8.13

### Patch Changes

- @copilotkit/shared@1.8.13

## 1.8.13-next.3

### Patch Changes

- @copilotkit/shared@1.8.13-next.3

## 1.8.13-next.2

### Patch Changes

- @copilotkit/shared@1.8.13-next.2

## 1.8.13-next.1

### Patch Changes

- @copilotkit/shared@1.8.13-next.1

## 1.8.13-next.0

### Patch Changes

- @copilotkit/shared@1.8.13-next.0

## 1.8.12

### Patch Changes

- @copilotkit/shared@1.8.12

## 1.8.12-next.6

### Patch Changes

- @copilotkit/shared@1.8.12-next.6

## 1.8.12-next.5

### Patch Changes

- @copilotkit/shared@1.8.12-next.5

## 1.8.12-next.4

### Patch Changes

- @copilotkit/shared@1.8.12-next.4

## 1.8.12-next.3

### Patch Changes

- @copilotkit/shared@1.8.12-next.3

## 1.8.12-next.2

### Patch Changes

- @copilotkit/shared@1.8.12-next.2

## 1.8.12-next.1

### Patch Changes

- @copilotkit/shared@1.8.12-next.1

## 1.8.12-next.0

### Patch Changes

- @copilotkit/shared@1.8.12-next.0

## 1.8.11

### Patch Changes

- @copilotkit/shared@1.8.11

## 1.8.11-next.1

### Patch Changes

- @copilotkit/shared@1.8.11-next.1

## 1.8.11-next.0

### Patch Changes

- @copilotkit/shared@1.8.11-next.0

## 1.8.10

### Patch Changes

- @copilotkit/shared@1.8.10

## 1.8.10-next.3

### Patch Changes

- @copilotkit/shared@1.8.10-next.3

## 1.8.10-next.2

### Patch Changes

- @copilotkit/shared@1.8.10-next.2

## 1.8.10-next.1

### Patch Changes

- @copilotkit/shared@1.8.10-next.1

## 1.8.10-next.0

### Patch Changes

- @copilotkit/shared@1.8.10-next.0

## 1.8.9

### Patch Changes

- @copilotkit/shared@1.8.9

## 1.8.9-next.0

### Patch Changes

- @copilotkit/shared@1.8.9-next.0

## 1.8.8

### Patch Changes

- @copilotkit/shared@1.8.8

## 1.8.8-next.1

### Patch Changes

- @copilotkit/shared@1.8.8-next.1

## 1.8.8-next.0

### Patch Changes

- @copilotkit/shared@1.8.8-next.0

## 1.8.7

### Patch Changes

- 8b8474f: - feat: add image input support with multi-model compatibility, pasting, and UX improvements
  - @copilotkit/shared@1.8.7

## 1.8.7-next.0

### Patch Changes

- 8b8474f: - feat: add image input support with multi-model compatibility, pasting, and UX improvements
  - @copilotkit/shared@1.8.7-next.0

## 1.8.6

### Patch Changes

- @copilotkit/shared@1.8.6

## 1.8.6-next.0

### Patch Changes

- @copilotkit/shared@1.8.6-next.0

## 1.8.5

### Patch Changes

- @copilotkit/shared@1.8.5

## 1.8.5-next.5

### Patch Changes

- @copilotkit/shared@1.8.5-next.5

## 1.8.5-next.4

### Patch Changes

- @copilotkit/shared@1.8.5-next.4

## 1.8.5-next.3

### Patch Changes

- @copilotkit/shared@1.8.5-next.3

## 1.8.5-next.2

### Patch Changes

- @copilotkit/shared@1.8.5-next.2

## 1.8.5-next.1

### Patch Changes

- @copilotkit/shared@1.8.5-next.1

## 1.8.5-next.0

### Patch Changes

- @copilotkit/shared@1.8.5-next.0

## 1.8.4

### Patch Changes

- Updated dependencies [f363760]
  - @copilotkit/shared@1.8.4

## 1.8.4-next.4

### Patch Changes

- @copilotkit/shared@1.8.4-next.4

## 1.8.4-next.3

### Patch Changes

- @copilotkit/shared@1.8.4-next.3

## 1.8.4-next.2

### Patch Changes

- @copilotkit/shared@1.8.4-next.2

## 1.8.4-next.1

### Patch Changes

- Updated dependencies [f363760]
  - @copilotkit/shared@1.8.4-next.1

## 1.8.4-next.0

### Patch Changes

- @copilotkit/shared@1.8.4-next.0

## 1.8.3

### Patch Changes

- @copilotkit/shared@1.8.3

## 1.8.3-next.0

### Patch Changes

- @copilotkit/shared@1.8.3-next.0

## 1.8.2-next.3

### Patch Changes

- @copilotkit/shared@1.8.2-next.3

## 1.8.2-next.2

### Patch Changes

- @copilotkit/shared@1.8.2-next.2

## 1.8.2-next.1

### Patch Changes

- @copilotkit/shared@1.8.2-next.1

## 1.8.2-next.0

### Patch Changes

- @copilotkit/shared@1.8.2-next.0

## 1.8.1

### Patch Changes

- @copilotkit/shared@1.8.1

## 1.8.1-next.1

### Patch Changes

- @copilotkit/shared@1.8.1-next.1

## 1.8.1-next.0

### Patch Changes

- @copilotkit/shared@1.8.1-next.0

## 1.8.0

### Patch Changes

- @copilotkit/shared@1.8.0

## 1.8.0-next.8

### Patch Changes

- @copilotkit/shared@1.8.0-next.8

## 1.8.0-next.7

### Patch Changes

- @copilotkit/shared@1.8.0-next.7

## 1.8.0-next.6

### Patch Changes

- @copilotkit/shared@1.8.0-next.6

## 1.8.0-next.5

### Patch Changes

- @copilotkit/shared@1.8.0-next.5

## 1.8.0-next.4

### Patch Changes

- @copilotkit/shared@1.8.0-next.4

## 1.8.0-next.3

### Patch Changes

- @copilotkit/shared@1.8.0-next.3

## 1.7.2-next.2

### Patch Changes

- @copilotkit/shared@1.7.2-next.2

## 1.7.2-next.1

### Patch Changes

- @copilotkit/shared@1.7.2-next.1

## 1.7.2-next.0

### Patch Changes

- @copilotkit/shared@1.7.2-next.0

## 1.7.1

### Patch Changes

- @copilotkit/shared@1.7.1

## 1.7.1-next.0

### Patch Changes

- @copilotkit/shared@1.7.1-next.0

## 1.7.0

### Patch Changes

- @copilotkit/shared@1.7.0

## 1.7.0-next.1

### Patch Changes

- @copilotkit/shared@1.7.0-next.1

## 1.7.0-next.0

### Patch Changes

- @copilotkit/shared@1.7.0-next.0

## 1.6.0

### Patch Changes

- d833f4c: - fix: provide the ability to type interrupt event value
- Updated dependencies [090203d]
  - @copilotkit/shared@1.6.0

## 1.6.0-next.12

### Patch Changes

- @copilotkit/shared@1.6.0-next.12

## 1.6.0-next.11

### Patch Changes

- @copilotkit/shared@1.6.0-next.11

## 1.6.0-next.10

### Patch Changes

- @copilotkit/shared@1.6.0-next.10

## 1.6.0-next.9

### Patch Changes

- @copilotkit/shared@1.6.0-next.9

## 1.6.0-next.8

### Patch Changes

- @copilotkit/shared@1.6.0-next.8

## 1.6.0-next.7

### Patch Changes

- @copilotkit/shared@1.6.0-next.7

## 1.6.0-next.6

### Patch Changes

- @copilotkit/shared@1.6.0-next.6

## 1.6.0-next.5

### Patch Changes

- Updated dependencies [090203d]
  - @copilotkit/shared@1.6.0-next.5

## 1.6.0-next.4

### Patch Changes

- @copilotkit/shared@1.6.0-next.4

## 1.6.0-next.3

### Patch Changes

- @copilotkit/shared@1.6.0-next.3

## 1.6.0-next.2

### Patch Changes

- @copilotkit/shared@1.6.0-next.2

## 1.6.0-next.1

### Patch Changes

- d833f4c: - fix: provide the ability to type interrupt event value
  - @copilotkit/shared@1.6.0-next.1

## 1.6.0-next.0

### Patch Changes

- @copilotkit/shared@1.6.0-next.0

## 1.5.20

### Patch Changes

- Updated dependencies [51f0d66]
  - @copilotkit/shared@1.5.20

## 1.5.20-next.0

### Patch Changes

- Updated dependencies [51f0d66]
  - @copilotkit/shared@1.5.20-next.0

## 1.5.19

### Patch Changes

- Updated dependencies [0dd1ab9]
  - @copilotkit/shared@1.5.19

## 1.5.19-next.1

### Patch Changes

- Updated dependencies [0dd1ab9]
  - @copilotkit/shared@1.5.19-next.1

## 1.5.19-next.0

### Patch Changes

- @copilotkit/shared@1.5.19-next.0

## 1.5.18

### Patch Changes

- d47cd26: - fix: detect and alert on version mismatch
- f77a7b9: - fix: use warning when version mismatch is not expected to error out
- Updated dependencies [d47cd26]
- Updated dependencies [f77a7b9]
- Updated dependencies [38d3ac2]
  - @copilotkit/shared@1.5.18

## 1.5.18-next.3

### Patch Changes

- f77a7b9: - fix: use warning when version mismatch is not expected to error out
- Updated dependencies [f77a7b9]
  - @copilotkit/shared@1.5.18-next.3

## 1.5.18-next.2

### Patch Changes

- Updated dependencies [38d3ac2]
  - @copilotkit/shared@1.5.18-next.2

## 1.5.18-next.1

### Patch Changes

- @copilotkit/shared@1.5.18-next.1

## 1.5.18-next.0

### Patch Changes

- d47cd26: - fix: detect and alert on version mismatch
- Updated dependencies [d47cd26]
  - @copilotkit/shared@1.5.18-next.0

## 1.5.17

### Patch Changes

- 1fc3902: - Revert "fix: detect and alert on version mismatch (#1333)"

  This reverts commit 48b7c7b1bd48ced82ffb9a00d6eddc1f7581e0c1.

- Updated dependencies [1fc3902]
  - @copilotkit/shared@1.5.17

## 1.5.17-next.0

### Patch Changes

- 1fc3902: - Revert "fix: detect and alert on version mismatch (#1333)"

  This reverts commit 48b7c7b1bd48ced82ffb9a00d6eddc1f7581e0c1.

- Updated dependencies [1fc3902]
  - @copilotkit/shared@1.5.17-next.0

## 1.5.16

### Patch Changes

- 48b7c7b: - fix: detect and alert on version mismatch
- Updated dependencies [48b7c7b]
  - @copilotkit/shared@1.5.16

## 1.5.16-next.2

### Patch Changes

- @copilotkit/shared@1.5.16-next.2

## 1.5.16-next.1

### Patch Changes

- 48b7c7b: - fix: detect and alert on version mismatch
- Updated dependencies [48b7c7b]
  - @copilotkit/shared@1.5.16-next.1

## 1.5.16-next.0

### Patch Changes

- @copilotkit/shared@1.5.16-next.0

## 1.5.15

### Patch Changes

- 0dc0f43: - fix(runtime-client-gql): call controller.close() after suppressing abort errors

  Signed-off-by: Tyler Slaton <<EMAIL>>

- 06f9f35: - feat(interrupt): add copilotkit interrupt as messages with copilotkit interrupt convenience fn
  - chore(deps): update dependencies for demos
  - chore(interrupt-as-message): add e2e test for interrupt as message
- 7b3141d: - feat(interrupt): support LG interrupt with useLangGraphInterrupt hook
  - chore(interrupt): add e2e test to interrupt functionality
  - feat(interrupt): add support for multiple interrupts and conditions
- 0bbb4ab: - fix: allow abort errors to pass further down to handler
- Updated dependencies [7b3141d]
  - @copilotkit/shared@1.5.15

## 1.5.15-next.8

### Patch Changes

- 06f9f35: - feat(interrupt): add copilotkit interrupt as messages with copilotkit interrupt convenience fn
  - chore(deps): update dependencies for demos
  - chore(interrupt-as-message): add e2e test for interrupt as message
  - @copilotkit/shared@1.5.15-next.8

## 1.5.15-next.7

### Patch Changes

- @copilotkit/shared@1.5.15-next.7

## 1.5.15-next.6

### Patch Changes

- @copilotkit/shared@1.5.15-next.6

## 1.5.15-next.5

### Patch Changes

- 0dc0f43: - fix(runtime-client-gql): call controller.close() after suppressing abort errors

  Signed-off-by: Tyler Slaton <<EMAIL>>

  - @copilotkit/shared@1.5.15-next.5

## 1.5.15-next.4

### Patch Changes

- 7b3141d: - feat(interrupt): support LG interrupt with useLangGraphInterrupt hook
  - chore(interrupt): add e2e test to interrupt functionality
  - feat(interrupt): add support for multiple interrupts and conditions
- Updated dependencies [7b3141d]
  - @copilotkit/shared@1.5.15-next.4

## 1.5.15-next.3

### Patch Changes

- @copilotkit/shared@1.5.15-next.3

## 1.5.15-next.2

### Patch Changes

- @copilotkit/shared@1.5.15-next.2

## 1.5.15-next.1

### Patch Changes

- 0bbb4ab: - fix: allow abort errors to pass further down to handler
  - @copilotkit/shared@1.5.15-next.1

## 1.5.15-next.0

### Patch Changes

- @copilotkit/shared@1.5.15-next.0

## 1.5.14

### Patch Changes

- Updated dependencies [0061f65]
  - @copilotkit/shared@1.5.14

## 1.5.14-next.0

### Patch Changes

- Updated dependencies [0061f65]
  - @copilotkit/shared@1.5.14-next.0

## 1.5.13

### Patch Changes

- @copilotkit/shared@1.5.13

## 1.5.13-next.0

### Patch Changes

- @copilotkit/shared@1.5.13-next.0

## 1.5.12

### Patch Changes

- fb87bcf: - fix: silence abort controller cancellation throwing an error
- 926499b: - Load the previous state of an agent if `threadId` is provided to CopilotKit, including all messages
- 6136a57: - fix(errors): add custom error classes to better describe library errors
  - fix(errors): use new errors in error handling
  - chore: add documentation and links to respective errors
- Updated dependencies [6136a57]
  - @copilotkit/shared@1.5.12

## 1.5.12-next.7

### Patch Changes

- 926499b: - Load the previous state of an agent if `threadId` is provided to CopilotKit, including all messages
  - @copilotkit/shared@1.5.12-next.7

## 1.5.12-next.6

### Patch Changes

- 6136a57: - fix(errors): add custom error classes to better describe library errors
  - fix(errors): use new errors in error handling
  - chore: add documentation and links to respective errors
- Updated dependencies [6136a57]
  - @copilotkit/shared@1.5.12-next.6

## 1.5.12-next.5

### Patch Changes

- @copilotkit/shared@1.5.12-next.5

## 1.5.12-next.4

### Patch Changes

- @copilotkit/shared@1.5.12-next.4

## 1.5.12-next.3

### Patch Changes

- @copilotkit/shared@1.5.12-next.3

## 1.5.12-next.2

### Patch Changes

- fb87bcf: - fix: silence abort controller cancellation throwing an error
  - @copilotkit/shared@1.5.12-next.2

## 1.5.12-next.1

### Patch Changes

- @copilotkit/shared@1.5.12-next.1

## 1.5.12-next.0

### Patch Changes

- @copilotkit/shared@1.5.12-next.0

## 1.5.11

### Patch Changes

- 72f9e58: test release notes
- aecb6f4: Fix build script
- 0a2e07e: Test changelog
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.11

## 1.5.11-next.0

### Patch Changes

- 72f9e58: test release notes
- aecb6f4: Fix build script
- 0a2e07e: Test changelog
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.11-next.0

## 1.5.10

### Patch Changes

- 72f9e58: test release notes
- aecb6f4: Fix build script
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.10

## 1.5.10-next.0

### Patch Changes

- 72f9e58: test release notes
- aecb6f4: Fix build script
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.10-next.0

## 1.5.9

### Patch Changes

- 72f9e58: test release notes
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.9

## 1.5.8

### Patch Changes

- 72f9e58: test release notes
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.8

## 1.5.6-next.0

### Patch Changes

- @copilotkit/shared@1.5.6-next.0

## 1.5.5-next.5

### Patch Changes

- @copilotkit/shared@1.5.5-next.5

## 1.5.5-next.3

### Patch Changes

- @copilotkit/shared@1.5.5-next.3

## 1.5.5-next.2

### Patch Changes

- 72f9e58: test release notes
- 9b3bdc2: test release notes
  - @copilotkit/shared@1.5.5-next.2

## 1.5.4

### Patch Changes

- @copilotkit/shared@1.5.4

## 1.5.3

### Patch Changes

- @copilotkit/shared@1.5.3

## 1.5.2

### Patch Changes

- b0192c1: This is a test release
  - @copilotkit/shared@1.5.2

## 1.5.1

### Patch Changes

- 5c01e9e: test prerelease #4
- da280ed: Test prerelease script
- 27e42d7: testing a prerelease
- 05240a9: test pre #2
- 33218fe: test prerelease #3
- 03f3d6f: Test next prerelease
- 649ebcc: - fix: add warning when using agents that are not available on agent related hooks
- Updated dependencies [5c01e9e]
- Updated dependencies [da280ed]
- Updated dependencies [27e42d7]
- Updated dependencies [05240a9]
- Updated dependencies [33218fe]
- Updated dependencies [03f3d6f]
  - @copilotkit/shared@1.5.1

## 1.5.1-next.3

### Patch Changes

- 33218fe: test prerelease #3
- Updated dependencies [33218fe]
  - @copilotkit/shared@1.5.1-next.3

## 1.5.1-next.2

### Patch Changes

- da280ed: Test prerelease script
- 649ebcc: - fix: add warning when using agents that are not available on agent related hooks
- Updated dependencies [da280ed]
  - @copilotkit/shared@1.5.1-next.2

## 1.5.1-next.1

### Patch Changes

- 03f3d6f: Test next prerelease
- Updated dependencies [03f3d6f]
  - @copilotkit/shared@1.5.1-next.1

## 1.5.1-next.0

### Patch Changes

- 27e42d7: testing a prerelease
- Updated dependencies [27e42d7]
  - @copilotkit/shared@1.5.1-next.0

## 1.5.0

### Minor Changes

- 1b47092: Synchronize LangGraph messages with CopilotKit

### Patch Changes

- 1b47092: CoAgents v0.3 prerelease
- Updated dependencies [1b47092]
- Updated dependencies [1b47092]
  - @copilotkit/shared@1.5.0

## 1.5.0-coagents-v0-3.0

### Minor Changes

- Synchronize LangGraph messages with CopilotKit

### Patch Changes

- e66bce4: CoAgents v0.3 prerelease
- Updated dependencies
- Updated dependencies [e66bce4]
  - @copilotkit/shared@1.5.0-coagents-v0-3.0

## 1.4.8

### Patch Changes

- - Better error handling
  - Introduce new "EmptyLLMAdapter" for when using CoAgents
  - Improve dev console help options
  - Allow CopilotKit remote endpoint without agents
- Updated dependencies
  - @copilotkit/shared@1.4.8

## 1.4.8-next.0

### Patch Changes

- @copilotkit/shared@1.4.8-next.0

## 1.4.7

### Patch Changes

- Fix broken build script before release
- Updated dependencies
  - @copilotkit/shared@1.4.7

## 1.4.6

### Patch Changes

- .

## 1.4.5

### Patch Changes

- testing release workflow
- Updated dependencies
  - @copilotkit/shared@1.4.5

## 1.4.5-next.0

### Patch Changes

- testing release workflow
- Updated dependencies
  - @copilotkit/shared@1.4.5-next.0

## 1.4.4

### Patch Changes

- @copilotkit/shared@1.4.4

## 1.4.4-next.4

### Patch Changes

- @copilotkit/shared@1.4.4-next.4

## 1.4.4-next.3

### Patch Changes

- @copilotkit/shared@1.4.4-next.3

## 1.4.4-next.2

### Patch Changes

- @copilotkit/shared@1.4.4-next.2

## 1.4.4-next.1

### Patch Changes

- @copilotkit/shared@1.4.4-next.1

## 1.4.4-next.0

### Patch Changes

- @copilotkit/shared@1.4.4-next.0

## 1.4.3

### Patch Changes

- c296282: - Better error surfacing when using LangGraph Platform streaming
  - Ensure state is immediately set without using flushSync
- - Better error surfacing when using LangGraph Platform streaming
  - Ensure state is immediately set without using flushSync
- Updated dependencies [c296282]
- Updated dependencies
  - @copilotkit/shared@1.4.3

## 1.4.3-pre.0

### Patch Changes

- - Better error surfacing when using LangGraph Platform streaming
  - Ensure state is immediately set without using flushSync
- Updated dependencies
  - @copilotkit/shared@1.4.3-pre.0

## 1.4.2

### Patch Changes

- - Make sure agent state is set immediately (#1077)
  - Support running an agent without messages (#1075)
- Updated dependencies
  - @copilotkit/shared@1.4.2

## 1.4.1

### Patch Changes

- 1721cbd: lower case copilotkit property
- 1721cbd: add zod conversion
- 8d0144f: bump
- 8d0144f: bump
- 8d0144f: bump
- e16d95e: New prerelease
- 1721cbd: Add convertActionsToDynamicStructuredTools to sdk-js
- CopilotKit Core:

  - Improved error messages and overall logs
  - `useCopilotAction.renderAndAwait` renamed to `.renderAndAwaitForResponse` (backwards compatible, will be deprecated in the future)
  - Improved scrolling behavior. It is now possible to scroll up during LLM response generation
  - Added Azure OpenAI integration
  - Updated interfaces for better developer ergonomics

  CoAgents:

  - Renamed `remoteActions` to `remoteEndpoints` (backwards compatible, will be deprecated in the future)
  - Support for LangGraph Platform in Remote Endpoints
  - LangGraph JS Support for CoAgents (locally via `langgraph dev`, `langgraph up` or deployed to LangGraph Platform)
  - Improved LangSmith integration - requests made through CoAgents will now surface in LangSmith
  - Enhanced state management and message handling

  CopilotKid Back-end SDK:

  - Released a whole-new `@copilotkit/sdk-js` for building agents with LangGraph JS Support

- 8d0144f: bump
- 8d0144f: bump
- fef1b74: fix assistant message CSS and propagate actions to LG JS
- Updated dependencies [1721cbd]
- Updated dependencies [1721cbd]
- Updated dependencies [8d0144f]
- Updated dependencies [8d0144f]
- Updated dependencies [8d0144f]
- Updated dependencies [e16d95e]
- Updated dependencies [1721cbd]
- Updated dependencies
- Updated dependencies [8d0144f]
- Updated dependencies [8d0144f]
- Updated dependencies [fef1b74]
  - @copilotkit/shared@1.4.1

## 1.4.1-pre.6

### Patch Changes

- 1721cbd: lower case copilotkit property
- 1721cbd: add zod conversion
- 1721cbd: Add convertActionsToDynamicStructuredTools to sdk-js
- fix assistant message CSS and propagate actions to LG JS
- Updated dependencies [1721cbd]
- Updated dependencies [1721cbd]
- Updated dependencies [1721cbd]
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.6

## 1.4.1-pre.5

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.5

## 1.4.1-pre.4

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.4

## 1.4.1-pre.3

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.3

## 1.4.1-pre.2

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.2

## 1.4.1-pre.1

### Patch Changes

- bump
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.1

## 1.4.1-pre.0

### Patch Changes

- New prerelease
- Updated dependencies
  - @copilotkit/shared@1.4.1-pre.0

## 1.4.0

### Minor Changes

CopilotKit Core:

- Improved error messages and overall logs
- `useCopilotAction.renderAndAwait` renamed to `.renderAndAwaitForResponse` (backwards compatible, will be deprecated in the future)
- Improved scrolling behavior. It is now possible to scroll up during LLM response generation
- Added Azure OpenAI integration
- Updated interfaces for better developer ergonomics

CoAgents:

- Renamed `remoteActions` to `remoteEndpoints` (backwards compatible, will be deprecated in the future)
- Support for LangGraph Platform in Remote Endpoints
- LangGraph JS Support for CoAgents (locally via `langgraph dev`, `langgraph up` or deployed to LangGraph Platform)
- Improved LangSmith integration - requests made through CoAgents will now surface in LangSmith
- Enhanced state management and message handling

CopilotKid Back-end SDK:

- Released a whole-new `@copilotkit/sdk-js` for building agents with LangGraph JS Support

### Patch Changes

- f6fab28: update tsup config
- f6fab28: update entry
- f6fab28: export langchain module
- 8a77944: Improve LangSmith support
- f6fab28: Ensure intermediate state config is sent as snake case
- f6fab28: update entry in tsup config
- 8a77944: Ensure the last message is sent to LangSmith
- a5efccd: Revert rxjs changes
- f6fab28: update entry
- f6fab28: Update exports
- f6fab28: Update exports
- 332d744: Add support for Azure OpenAI
- f6fab28: Export LangGraph functions
- f6fab28: Update lockfile
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies [a5efccd]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [332d744]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
  - @copilotkit/runtime@1.4.0
  - @copilotkit/shared@1.4.0

## 1.3.16-mme-revert-rxjs-changes.10

### Patch Changes

- f6fab28: update tsup config
- f6fab28: update entry
- f6fab28: export langchain module
- 8a77944: Improve LangSmith support
- f6fab28: Ensure intermediate state config is sent as snake case
- f6fab28: update entry in tsup config
- 8a77944: Ensure the last message is sent to LangSmith
- Revert rxjs changes
- f6fab28: update entry
- f6fab28: Update exports
- f6fab28: Update exports
- 332d744: Add support for Azure OpenAI
- f6fab28: Export LangGraph functions
- f6fab28: Update lockfile
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [8a77944]
- Updated dependencies
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
- Updated dependencies [332d744]
- Updated dependencies [f6fab28]
- Updated dependencies [f6fab28]
  - @copilotkit/runtime@1.3.16-mme-revert-rxjs-changes.10
  - @copilotkit/shared@1.3.16-mme-revert-rxjs-changes.10

## 1.3.15

### Patch Changes

- pass description for array and object action parameters in langchain adapter
- Updated dependencies
  - @copilotkit/runtime@1.3.15
  - @copilotkit/shared@1.3.15

## 1.3.14

### Patch Changes

- Add data-test-id to some elements for testing
- Updated dependencies
  - @copilotkit/runtime@1.3.14
  - @copilotkit/shared@1.3.14

## 1.3.13

### Patch Changes

- fix usage of one-at-a-time tool when called multiple times
- Updated dependencies
  - @copilotkit/runtime@1.3.13
  - @copilotkit/shared@1.3.13

## 1.3.12

### Patch Changes

- - enable dynamic parameters in langchain adapter tool call
  - fix unparsable action arguments causing tool call crashes
- Updated dependencies
  - @copilotkit/runtime@1.3.12
  - @copilotkit/shared@1.3.12

## 1.3.11

### Patch Changes

- 08e8956: Fix duplicate messages
- Fix duplicate messages
- Updated dependencies [08e8956]
- Updated dependencies
  - @copilotkit/runtime@1.3.11
  - @copilotkit/shared@1.3.11

## 1.3.11-mme-fix-duplicate-messages.0

### Patch Changes

- Fix duplicate messages
- Updated dependencies
  - @copilotkit/runtime@1.3.11-mme-fix-duplicate-messages.0
  - @copilotkit/shared@1.3.11-mme-fix-duplicate-messages.0

## 1.3.10

### Patch Changes

- change how message chunk type is resolved (fixed langchain adapters)
- Updated dependencies
  - @copilotkit/runtime@1.3.10
  - @copilotkit/shared@1.3.10

## 1.3.9

### Patch Changes

- Fix message id issues
- Updated dependencies
  - @copilotkit/runtime@1.3.9
  - @copilotkit/shared@1.3.9

## 1.3.8

### Patch Changes

- fix textarea on multiple llm providers and memoize react ui context
- Updated dependencies
  - @copilotkit/runtime@1.3.8
  - @copilotkit/shared@1.3.8

## 1.3.7

### Patch Changes

- Fix libraries for React 19 and Next.js 15 support
- Updated dependencies
  - @copilotkit/runtime@1.3.7
  - @copilotkit/shared@1.3.7

## 1.3.6

### Patch Changes

- 1. Removes the usage of the `crypto` Node pacakge, instaed uses `uuid`. This ensures that non-Next.js React apps can use CopilotKit.
  2. Fixes Nest.js runtime docs

- Updated dependencies
  - @copilotkit/runtime@1.3.6
  - @copilotkit/shared@1.3.6

## 1.3.5

### Patch Changes

- Improve CoAgent state render
- Updated dependencies
  - @copilotkit/runtime@1.3.5
  - @copilotkit/shared@1.3.5

## 1.3.4

### Patch Changes

- Add followUp property to useCopilotAction
- Updated dependencies
  - @copilotkit/runtime@1.3.4
  - @copilotkit/shared@1.3.4

## 1.3.3

### Patch Changes

- Impvovements to error handling and CoAgent protocol
- Updated dependencies
  - @copilotkit/runtime@1.3.3
  - @copilotkit/shared@1.3.3

## 1.3.2

### Patch Changes

- Features and bug fixes
- 30232c0: Ensure actions can be discovered on state change
- Updated dependencies
- Updated dependencies [30232c0]
  - @copilotkit/runtime@1.3.2
  - @copilotkit/shared@1.3.2

## 1.3.2-mme-discover-actions.0

### Patch Changes

- Ensure actions can be discovered on state change
- Updated dependencies
  - @copilotkit/runtime@1.3.2-mme-discover-actions.0
  - @copilotkit/shared@1.3.2-mme-discover-actions.0

## 1.3.1

### Patch Changes

- Revert CSS injection
- Updated dependencies
  - @copilotkit/runtime@1.3.1
  - @copilotkit/shared@1.3.1

## 1.3.0

### Minor Changes

- CoAgents and remote actions

### Patch Changes

- 5b63f55: stream intermediate state
- b6fd3d8: Better message grouping
- 89420c6: Rename hooks and bugfixes
- b6e8824: useCoAgent/useCoAgentAction
- 91c35b9: useAgentState
- 00be203: Remote actions preview
- fb15f72: Reduce request size by skipping intermediate state
- 8ecc3e4: Fix useCoAgent start/stop bug
- Updated dependencies
- Updated dependencies [5b63f55]
- Updated dependencies [b6fd3d8]
- Updated dependencies [89420c6]
- Updated dependencies [b6e8824]
- Updated dependencies [91c35b9]
- Updated dependencies [00be203]
- Updated dependencies [fb15f72]
- Updated dependencies [8ecc3e4]
  - @copilotkit/runtime@1.3.0
  - @copilotkit/shared@1.3.0

## 1.2.1

### Patch Changes

- inject minified css in bundle

  - removes the need to import `styles.css` manually
  - empty `styles.css` included in the build for backwards compatibility
  - uses tsup's `injectStyles` with `postcss` to bundle and minify the CSS, then inject it as a style tag
  - currently uses my fork of `tsup` where I added support for async function in `injectStyles` (must-have for postcss), a PR from my fork to the main library will follow shortly
  - remove material-ui, and use `react-icons` for icons (same icons as before)
  - remove unused `IncludedFilesPreview` component
  - updated docs

- Updated dependencies
  - @copilotkit/runtime@1.2.1
  - @copilotkit/shared@1.2.1

## 1.2.0

### Minor Changes

- Fix errors related to crypto not being found, and other bug fixes

### Patch Changes

- 638d51d: appendMessage fix 1
- faccbe1: state-abuse resistance for useCopilotChat
- b0cf700: remove unnecessary logging
- Updated dependencies
- Updated dependencies [638d51d]
- Updated dependencies [faccbe1]
- Updated dependencies [b0cf700]
  - @copilotkit/runtime@1.2.0
  - @copilotkit/shared@1.2.0

## 1.1.2

### Patch Changes

- Pin headless-ui/react version to v2.1.1
- Updated dependencies
  - @copilotkit/runtime@1.1.2
  - @copilotkit/shared@1.1.2

## 1.1.1

### Patch Changes

- - improved documentation
  - center textarea popup
  - show/hide dev console
  - forward maxTokens, stop and force function calling
- Updated dependencies
  - @copilotkit/runtime@1.1.1
  - @copilotkit/shared@1.1.1

## 1.1.0

### Minor Changes

- Official support for Groq (`GroqAdapter`)

### Patch Changes

- Updated dependencies
  - @copilotkit/runtime@1.1.0
  - @copilotkit/shared@1.1.0

## 1.0.9

### Patch Changes

- Dev console, bugfixes
- Updated dependencies
  - @copilotkit/runtime@1.0.9
  - @copilotkit/shared@1.0.9

## 1.0.8

### Patch Changes

- Remove redundant console logs
- Updated dependencies
  - @copilotkit/runtime@1.0.8
  - @copilotkit/shared@1.0.8

## 1.0.7

### Patch Changes

- Add \_copilotkit internal properties to runtime
- Updated dependencies
  - @copilotkit/runtime@1.0.7
  - @copilotkit/shared@1.0.7

## 1.0.6

### Patch Changes

- - Proactively prevent race conditions
  - Improve token counting performance
- Updated dependencies
  - @copilotkit/runtime@1.0.6
  - @copilotkit/shared@1.0.6

## 1.0.5

### Patch Changes

- Include @copilotkit/runtime-client-gql NPM package version in request to Runtime
- Updated dependencies
  - @copilotkit/runtime@1.0.5
  - @copilotkit/shared@1.0.5

## 1.0.4

### Patch Changes

- Remove nanoid
- Updated dependencies
  - @copilotkit/runtime@1.0.4
  - @copilotkit/shared@1.0.4

## 1.0.3

### Patch Changes

- Add README.md to published packages and add keywords to package.json
- Updated dependencies
  - @copilotkit/runtime@1.0.3
  - @copilotkit/shared@1.0.3

## 1.0.2

### Patch Changes

- Add README.md and homepage/url to published packages
- Updated dependencies
  - @copilotkit/runtime@1.0.2
  - @copilotkit/shared@1.0.2

## 1.0.1

### Patch Changes

- Remove PostHog, use Segment Anonymous Telemetry instead
- Updated dependencies
  - @copilotkit/runtime@1.0.1
  - @copilotkit/shared@1.0.1

## 1.0.0

### Major Changes

- b6a4b6eb: V1.0 Release Candidate

  - A robust new protocol between the frontend and the Copilot Runtime
  - Support for Copilot Cloud
  - Generative UI
  - Support for LangChain universal tool calling
  - OpenAI assistant API streaming

- V1.0 Release

  - A robust new protocol between the frontend and the Copilot Runtime
  - Support for Copilot Cloud
  - Generative UI
  - Support for LangChain universal tool calling
  - OpenAI assistant API streaming

### Patch Changes

- b6a4b6eb: Introduce anonymous telemetry
- b6a4b6eb: Set default Copilot Cloud runtime URL to versioned URL (v1)
- Updated dependencies [b6a4b6eb]
- Updated dependencies [b6a4b6eb]
- Updated dependencies [b6a4b6eb]
- Updated dependencies
  - @copilotkit/runtime@1.0.0
  - @copilotkit/shared@1.0.0

## 1.0.0-beta.2

### Patch Changes

- Set default Copilot Cloud runtime URL to versioned URL (v1)
- Updated dependencies
  - @copilotkit/runtime@1.0.0-beta.2
  - @copilotkit/shared@1.0.0-beta.2

## 1.0.0-beta.1

### Patch Changes

- Introduce anonymous telemetry
- Updated dependencies
  - @copilotkit/runtime@1.0.0-beta.1
  - @copilotkit/shared@1.0.0-beta.1

## 1.0.0-beta.0

### Major Changes

- V1.0 Release Candidate

  - A robust new protocol between the frontend and the Copilot Runtime
  - Support for Copilot Cloud
  - Generative UI
  - Support for LangChain universal tool calling
  - OpenAI assistant API streaming

### Patch Changes

- Updated dependencies
  - @copilotkit/runtime@1.0.0-beta.0
  - @copilotkit/shared@1.0.0-beta.0
