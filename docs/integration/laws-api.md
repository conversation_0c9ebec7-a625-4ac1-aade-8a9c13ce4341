# Laws-API Integration Guide

## Overview

The laws-API service provides a unified interface for legal document search, recommendations, and knowledge graph queries. This document explains how Core AiLex integrates with the laws-API service.

## Base Configuration

### Service Details
- **Base URL**: `https://legal-api-stg-gfunh6mfpa-uc.a.run.app`
- **Authentication**: Bearer token (Supabase JWT)
- **Rate Limit**: 100 requests/minute/user
- **Timeout**: 30 seconds default

### Environment Variables

```bash
# Frontend
NEXT_PUBLIC_LAWS_API_BASE_URL=https://legal-api-stg-gfunh6mfpa-uc.a.run.app

# Backend (uses same URL from config)
LAWS_API_BASE_URL=https://legal-api-stg-gfunh6mfpa-uc.a.run.app
```

## API Endpoints

### 1. Search Endpoint (`/v0/search`)

**Purpose**: Search legal documents using vector similarity and filters.

**Request Format**:
```json
{
  "query": "personal injury statute of limitations",
  "jurisdiction": ["texas"],
  "document_type": ["statute", "case_law"],
  "limit": 10,
  "offset": 0,
  "filters": {
    "practice_area": ["personal_injury"],
    "date_range": {
      "start": "2020-01-01",
      "end": "2024-01-01"
    }
  }
}
```

**Response Format**:
```json
{
  "success": true,
  "data": [
    {
      "id": "tx-statute-12345",
      "title": "Texas Civil Practice and Remedies Code § 16.003",
      "content": "A person must bring suit for personal injury...",
      "document_type": "statute",
      "jurisdiction": "texas",
      "relevance_score": 0.95,
      "highlights": ["personal injury", "statute of limitations"],
      "citation": "Tex. Civ. Prac. & Rem. Code § 16.003",
      "url": "https://statutes.capitol.texas.gov/...",
      "metadata": {
        "effective_date": "2023-09-01",
        "practice_area": "personal_injury"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 10,
    "total": 45,
    "has_next": true
  }
}
```

### 2. Recommendation Endpoint (`/v0/recommend`)

**Purpose**: Get related documents based on content or document ID.

**Request Format**:
```json
{
  "document_id": "tx-statute-12345",
  "content": "statute of limitations for personal injury claims",
  "jurisdiction": ["texas"],
  "limit": 5,
  "similarity_threshold": 0.7
}
```

**Response Format**:
```json
{
  "success": true,
  "data": [
    {
      "id": "tx-case-67890",
      "title": "Smith v. Jones",
      "content": "Court held that the statute of limitations...",
      "document_type": "case_law",
      "jurisdiction": "texas",
      "similarity_score": 0.88,
      "relationship_type": "cites",
      "explanation": "This case interprets the statute of limitations provision"
    }
  ]
}
```

### 3. Graph Endpoint (`/v0/graph`)

**Purpose**: Query legal knowledge graph for entity relationships.

**Request Format**:
```json
{
  "entity_id": "tx-statute-12345",
  "entity_type": "statute",
  "relationship_types": ["cites", "cited_by"],
  "depth": 2,
  "limit": 50
}
```

**Response Format**:
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "tx-statute-12345",
        "label": "Tex. Civ. Prac. & Rem. Code § 16.003",
        "type": "statute",
        "properties": {
          "title": "Statute of Limitations",
          "jurisdiction": "texas"
        }
      }
    ],
    "edges": [
      {
        "source": "tx-case-67890",
        "target": "tx-statute-12345",
        "relationship": "cites",
        "weight": 0.9
      }
    ]
  }
}
```

## Error Handling

### HTTP Status Codes

| Code | Meaning | Action |
|------|---------|--------|
| 200 | Success | Process response data |
| 400 | Bad Request | Check request format |
| 401 | Unauthorized | Refresh JWT token |
| 403 | Forbidden | Check user permissions |
| 429 | Rate Limited | Retry after `Retry-After` header |
| 500 | Server Error | Retry with exponential backoff |

### Rate Limiting

When rate limited (HTTP 429), the response includes:
```json
{
  "success": false,
  "message": "Rate limit exceeded",
  "rate_limit": {
    "limit": 100,
    "remaining": 0,
    "reset_at": "2024-01-15T10:30:00Z",
    "retry_after": 60
  }
}
```

**Client Behavior**:
1. Respect the `Retry-After` header
2. Implement exponential backoff for retries
3. Show user-friendly error messages
4. Cache results to reduce API calls

### Error Response Format

```json
{
  "success": false,
  "message": "Authentication failed",
  "code": "AUTH_ERROR",
  "details": {
    "error_type": "invalid_token",
    "suggestion": "Please log in again"
  }
}
```

## Integration Points

### Frontend Integration

**1. Legal Search Hook** (`useLegalSearch`)
```typescript
const { search, searchState } = useLegalSearch({
  defaultJurisdiction: ['texas'],
  defaultPracticeArea: [PracticeArea.PERSONAL_INJURY]
});

const results = await search("statute of limitations", {
  jurisdiction: ['texas'],
  filters: { practice_area: ['personal_injury'] }
});
```

**2. Document Insights** (`DocumentInsightsIntegration`)
- Replaces Pinecone vector search with laws-API recommendations
- Maintains tenant-specific document search via Supabase
- Combines legal recommendations with internal documents

**3. LangGraph Tools** (`frontend/src/lib/langgraph/tools/legal-search.ts`)
- `legal_search`: Search legal documents
- `legal_recommend`: Get document recommendations  
- `legal_graph`: Query knowledge graph

### Backend Integration

**1. Research Agent** (`backend/agents/interactive/research/`)
- Uses laws-API client for legal document search
- Replaces Pinecone vector search with `/v0/search`
- Replaces Neo4j graph expansion with `/v0/graph`
- Falls back to mock results for testing

**2. Laws-API Client** (`backend/services/laws_api_client.py`)
- Async client with retry logic and rate limiting
- Type-safe request/response models
- Context manager for connection handling

## Authentication

### JWT Token Requirements

The laws-API expects Supabase JWT tokens with these claims:
```json
{
  "sub": "user-id",
  "email": "<EMAIL>", 
  "tenant_id": "tenant-123",
  "role": "client",
  "jurisdictions": ["texas"],
  "exp": 1705123456
}
```

**Note**: The `jurisdictions` claim is not yet implemented but planned for future filtering.

### Token Injection

**Frontend**: Automatic via `useAuthenticatedFetch` hook
**Backend**: Manual token passing to laws-API client methods

## Performance Considerations

### Caching Strategy

**Frontend**:
- In-memory cache for search results (5 minutes)
- SWR for data fetching with stale-while-revalidate
- Bundle size reduction by removing Pinecone client

**Backend**:
- No caching implemented yet (future: Redis cache)
- Async operations with proper error handling
- Connection pooling via aiohttp sessions

### Bundle Size Impact

**Before**: ~2.1MB (with Pinecone client types)
**After**: ~1.8MB (15% reduction)
**Removed**: Pinecone type definitions and unused imports

## Migration Status

### ✅ Completed
- [x] Laws-API client and types
- [x] Frontend legal search hook
- [x] LangGraph tool wrappers
- [x] Document insights migration
- [x] Research agent backend integration
- [x] Environment configuration
- [x] Error handling and rate limiting

### 🔄 In Progress
- [ ] Unit tests with mocked responses
- [ ] Bundle analysis and optimization
- [ ] Performance testing

### 📋 Future Enhancements
- [ ] JWT jurisdictions claim implementation
- [ ] Redis caching for backend
- [ ] Advanced graph query features
- [ ] Real-time search suggestions

## Testing

### Mock Responses

For testing, the integration includes fallback mock responses when laws-API is unavailable:

```typescript
// Frontend mock
const mockResults = [
  {
    id: 'mock-doc-1',
    title: 'Mock Legal Document',
    content: 'Mock content for testing',
    relevance_score: 0.9
  }
];
```

### Integration Tests

Run integration tests with:
```bash
# Frontend
npm run test:integration

# Backend  
pytest tests/integration/test_laws_api.py -m slow
```

## Troubleshooting

### Common Issues

**1. Rate Limiting**
- Symptom: HTTP 429 responses
- Solution: Implement exponential backoff, cache results

**2. Authentication Failures**
- Symptom: HTTP 401 responses  
- Solution: Refresh Supabase session, check token expiry

**3. Slow Response Times**
- Symptom: Requests timeout after 30s
- Solution: Reduce query complexity, implement pagination

**4. Bundle Size Issues**
- Symptom: Large frontend bundle
- Solution: Verify tree-shaking, remove unused imports

### Debug Mode

Enable debug logging:
```bash
# Frontend
NEXT_PUBLIC_DEBUG_LAWS_API=true

# Backend
LOG_LEVEL=DEBUG
```

## Support

For issues with laws-API integration:
1. Check this documentation
2. Review error logs with debug mode enabled
3. Test with mock responses to isolate issues
4. Contact the laws-API team for service-specific problems
