{"version": 3, "sources": ["../src/hooks/use-langgraph-interrupt-render.ts"], "sourcesContent": ["import { useCopilotContext } from \"../context\";\nimport React, { useCallback } from \"react\";\nimport { executeConditions } from \"@copilotkit/shared\";\n\ntype InterruptProps = {\n  event: any;\n  result: any;\n  render: (props: {\n    event: any;\n    result: any;\n    resolve: (response: string) => void;\n  }) => string | React.ReactElement;\n  resolve: (response: string) => void;\n};\n\nconst InterruptRenderer: React.FC<InterruptProps> = ({ event, result, render, resolve }) => {\n  return render({ event, result, resolve });\n};\n\nexport function useLangGraphInterruptRender(): string | React.ReactElement | null {\n  const { langGraphInterruptAction, setLangGraphInterruptAction, agentSession } =\n    useCopilotContext();\n\n  const responseRef = React.useRef<string>();\n  const resolveInterrupt = useCallback(\n    (response: string) => {\n      responseRef.current = response;\n      // Use setTimeout to defer the state update to next tick\n      setTimeout(() => {\n        setLangGraphInterruptAction({ event: { response } });\n      }, 0);\n    },\n    [setLangGraphInterruptAction],\n  );\n\n  if (\n    !langGraphInterruptAction ||\n    !langGraphInterruptAction.event ||\n    !langGraphInterruptAction.render\n  )\n    return null;\n\n  const { render, handler, event, enabled } = langGraphInterruptAction;\n\n  const conditionsMet =\n    !agentSession || !enabled\n      ? true\n      : enabled({ eventValue: event.value, agentMetadata: agentSession });\n  if (!conditionsMet) {\n    return null;\n  }\n\n  let result = null;\n  if (handler) {\n    result = handler({\n      event,\n      resolve: resolveInterrupt,\n    });\n  }\n\n  return React.createElement(InterruptRenderer, {\n    event,\n    result,\n    render,\n    resolve: resolveInterrupt,\n  });\n}\n"], "mappings": ";;;;;AACA,OAAO,SAAS,mBAAmB;AAcnC,IAAM,oBAA8C,CAAC,EAAE,OAAO,QAAQ,QAAQ,QAAQ,MAAM;AAC1F,SAAO,OAAO,EAAE,OAAO,QAAQ,QAAQ,CAAC;AAC1C;AAEO,SAAS,8BAAkE;AAChF,QAAM,EAAE,0BAA0B,6BAA6B,aAAa,IAC1E,kBAAkB;AAEpB,QAAM,cAAc,MAAM,OAAe;AACzC,QAAM,mBAAmB;AAAA,IACvB,CAAC,aAAqB;AACpB,kBAAY,UAAU;AAEtB,iBAAW,MAAM;AACf,oCAA4B,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AAAA,MACrD,GAAG,CAAC;AAAA,IACN;AAAA,IACA,CAAC,2BAA2B;AAAA,EAC9B;AAEA,MACE,CAAC,4BACD,CAAC,yBAAyB,SAC1B,CAAC,yBAAyB;AAE1B,WAAO;AAET,QAAM,EAAE,QAAQ,SAAS,OAAO,QAAQ,IAAI;AAE5C,QAAM,gBACJ,CAAC,gBAAgB,CAAC,UACd,OACA,QAAQ,EAAE,YAAY,MAAM,OAAO,eAAe,aAAa,CAAC;AACtE,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACb,MAAI,SAAS;AACX,aAAS,QAAQ;AAAA,MACf;AAAA,MACA,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,cAAc,mBAAmB;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACH;", "names": []}