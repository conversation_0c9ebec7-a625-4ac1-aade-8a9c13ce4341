{"version": 3, "sources": ["../../../src/components/chat/CodeBlock.tsx", "../../../src/hooks/use-copy-to-clipboard.tsx", "../../../src/components/chat/Icons.tsx"], "sourcesContent": ["import { FC, memo } from \"react\";\nimport { Prism as Synta<PERSON><PERSON><PERSON><PERSON>er } from \"react-syntax-highlighter\";\nimport { useCopyToClipboard } from \"../../hooks/use-copy-to-clipboard\";\nimport { CheckIcon, CopyIcon, DownloadIcon } from \"./Icons\";\n\ninterface CodeActionButtonProps {\n  onClick: () => void;\n  children: React.ReactNode;\n}\n\ninterface Props {\n  language: string;\n  value: string;\n}\n\ninterface languageMap {\n  [key: string]: string | undefined;\n}\n\nexport const programmingLanguages: languageMap = {\n  javascript: \".js\",\n  python: \".py\",\n  java: \".java\",\n  c: \".c\",\n  cpp: \".cpp\",\n  \"c++\": \".cpp\",\n  \"c#\": \".cs\",\n  ruby: \".rb\",\n  php: \".php\",\n  swift: \".swift\",\n  \"objective-c\": \".m\",\n  kotlin: \".kt\",\n  typescript: \".ts\",\n  go: \".go\",\n  perl: \".pl\",\n  rust: \".rs\",\n  scala: \".scala\",\n  haskell: \".hs\",\n  lua: \".lua\",\n  shell: \".sh\",\n  sql: \".sql\",\n  html: \".html\",\n  css: \".css\",\n  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component\n};\n\nexport const generateRandomString = (length: number, lowercase = false) => {\n  const chars = \"ABCDEFGHJKLMNPQRSTUVWXY3456789\"; // excluding similar looking characters like Z, 2, I, 1, O, 0\n  let result = \"\";\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return lowercase ? result.toLowerCase() : result;\n};\n\nconst CodeBlock: FC<Props> = memo(({ language, value }) => {\n  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 2000 });\n\n  const downloadAsFile = () => {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n    const fileExtension = programmingLanguages[language] || \".file\";\n    const suggestedFileName = `file-${generateRandomString(3, true)}${fileExtension}`;\n    const fileName = window.prompt(\"Enter file name\" || \"\", suggestedFileName);\n\n    if (!fileName) {\n      // User pressed cancel on prompt.\n      return;\n    }\n\n    const blob = new Blob([value], { type: \"text/plain\" });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.download = fileName;\n    link.href = url;\n    link.style.display = \"none\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const onCopy = () => {\n    if (isCopied) return;\n    copyToClipboard(value);\n  };\n\n  return (\n    <div className=\"copilotKitCodeBlock\">\n      <div className=\"copilotKitCodeBlockToolbar\">\n        <span className=\"copilotKitCodeBlockToolbarLanguage\">{language}</span>\n        <div className=\"copilotKitCodeBlockToolbarButtons\">\n          <button className=\"copilotKitCodeBlockToolbarButton\" onClick={downloadAsFile}>\n            {DownloadIcon}\n          </button>\n          <button className=\"copilotKitCodeBlockToolbarButton\" onClick={onCopy}>\n            {isCopied ? CheckIcon : CopyIcon}\n          </button>\n        </div>\n      </div>\n      <SyntaxHighlighter\n        language={language}\n        style={highlightStyle}\n        PreTag=\"div\"\n        customStyle={{\n          margin: 0,\n          borderBottomLeftRadius: \"0.375rem\",\n          borderBottomRightRadius: \"0.375rem\",\n        }}\n      >\n        {value}\n      </SyntaxHighlighter>\n    </div>\n  );\n});\nCodeBlock.displayName = \"CodeBlock\";\n\nexport { CodeBlock };\n\n// import { vscDarkPlus as highlightStyle } from \"react-syntax-highlighter/dist/esm/styles/prism\";\n// As a workaround, we inline the vscDarkPlus from react-syntax-highlighter.\n// Importing it as recommended in the documentation leads to build errors in the non app router\n// (Next.js classic) setup.\nconst highlightStyle: any = {\n  'pre[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n    padding: \"1em\",\n    margin: \".5em 0\",\n    overflow: \"auto\",\n    background: \"#1e1e1e\",\n  },\n  'code[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n  },\n  'pre[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'code[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'pre[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'code[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    padding: \".1em .3em\",\n    borderRadius: \".3em\",\n    color: \"#db4c69\",\n    background: \"#1e1e1e\",\n  },\n  \".namespace\": {\n    Opacity: \".7\",\n  },\n  \"doctype.doctype-tag\": {\n    color: \"#569CD6\",\n  },\n  \"doctype.name\": {\n    color: \"#9cdcfe\",\n  },\n  comment: {\n    color: \"#6a9955\",\n  },\n  prolog: {\n    color: \"#6a9955\",\n  },\n  punctuation: {\n    color: \"#d4d4d4\",\n  },\n  \".language-html .language-css .token.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  \".language-html .language-javascript .token.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  property: {\n    color: \"#9cdcfe\",\n  },\n  tag: {\n    color: \"#569cd6\",\n  },\n  boolean: {\n    color: \"#569cd6\",\n  },\n  number: {\n    color: \"#b5cea8\",\n  },\n  constant: {\n    color: \"#9cdcfe\",\n  },\n  symbol: {\n    color: \"#b5cea8\",\n  },\n  inserted: {\n    color: \"#b5cea8\",\n  },\n  unit: {\n    color: \"#b5cea8\",\n  },\n  selector: {\n    color: \"#d7ba7d\",\n  },\n  \"attr-name\": {\n    color: \"#9cdcfe\",\n  },\n  string: {\n    color: \"#ce9178\",\n  },\n  char: {\n    color: \"#ce9178\",\n  },\n  builtin: {\n    color: \"#ce9178\",\n  },\n  deleted: {\n    color: \"#ce9178\",\n  },\n  \".language-css .token.string.url\": {\n    textDecoration: \"underline\",\n  },\n  operator: {\n    color: \"#d4d4d4\",\n  },\n  entity: {\n    color: \"#569cd6\",\n  },\n  \"operator.arrow\": {\n    color: \"#569CD6\",\n  },\n  atrule: {\n    color: \"#ce9178\",\n  },\n  \"atrule.rule\": {\n    color: \"#c586c0\",\n  },\n  \"atrule.url\": {\n    color: \"#9cdcfe\",\n  },\n  \"atrule.url.function\": {\n    color: \"#dcdcaa\",\n  },\n  \"atrule.url.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  keyword: {\n    color: \"#569CD6\",\n  },\n  \"keyword.module\": {\n    color: \"#c586c0\",\n  },\n  \"keyword.control-flow\": {\n    color: \"#c586c0\",\n  },\n  function: {\n    color: \"#dcdcaa\",\n  },\n  \"function.maybe-class-name\": {\n    color: \"#dcdcaa\",\n  },\n  regex: {\n    color: \"#d16969\",\n  },\n  important: {\n    color: \"#569cd6\",\n  },\n  italic: {\n    fontStyle: \"italic\",\n  },\n  \"class-name\": {\n    color: \"#4ec9b0\",\n  },\n  \"maybe-class-name\": {\n    color: \"#4ec9b0\",\n  },\n  console: {\n    color: \"#9cdcfe\",\n  },\n  parameter: {\n    color: \"#9cdcfe\",\n  },\n  interpolation: {\n    color: \"#9cdcfe\",\n  },\n  \"punctuation.interpolation-punctuation\": {\n    color: \"#569cd6\",\n  },\n  variable: {\n    color: \"#9cdcfe\",\n  },\n  \"imports.maybe-class-name\": {\n    color: \"#9cdcfe\",\n  },\n  \"exports.maybe-class-name\": {\n    color: \"#9cdcfe\",\n  },\n  escape: {\n    color: \"#d7ba7d\",\n  },\n  \"tag.punctuation\": {\n    color: \"#808080\",\n  },\n  cdata: {\n    color: \"#808080\",\n  },\n  \"attr-value\": {\n    color: \"#ce9178\",\n  },\n  \"attr-value.punctuation\": {\n    color: \"#ce9178\",\n  },\n  \"attr-value.punctuation.attr-equals\": {\n    color: \"#d4d4d4\",\n  },\n  namespace: {\n    color: \"#4ec9b0\",\n  },\n  'pre[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-css\"]': {\n    color: \"#ce9178\",\n  },\n  'code[class*=\"language-css\"]': {\n    color: \"#ce9178\",\n  },\n  'pre[class*=\"language-html\"]': {\n    color: \"#d4d4d4\",\n  },\n  'code[class*=\"language-html\"]': {\n    color: \"#d4d4d4\",\n  },\n  \".language-regex .token.anchor\": {\n    color: \"#dcdcaa\",\n  },\n  \".language-html .token.punctuation\": {\n    color: \"#808080\",\n  },\n  'pre[class*=\"language-\"] > code[class*=\"language-\"]': {\n    position: \"relative\",\n    zIndex: \"1\",\n  },\n  \".line-highlight.line-highlight\": {\n    background: \"#f7ebc6\",\n    boxShadow: \"inset 5px 0 0 #f7d87c\",\n    zIndex: \"0\",\n  },\n};\n", "import * as React from \"react\";\n\nexport interface useCopyToClipboardProps {\n  timeout?: number;\n}\n\nexport function useCopyToClipboard({ timeout = 2000 }: useCopyToClipboardProps) {\n  const [isCopied, setIsCopied] = React.useState<Boolean>(false);\n\n  const copyToClipboard = (value: string) => {\n    if (typeof window === \"undefined\" || !navigator.clipboard?.writeText) {\n      return;\n    }\n\n    if (!value) {\n      return;\n    }\n\n    navigator.clipboard.writeText(value).then(() => {\n      setIsCopied(true);\n\n      setTimeout(() => {\n        setIsCopied(false);\n      }, timeout);\n    });\n  };\n\n  return { isCopied, copyToClipboard };\n}\n", "import React from \"react\";\n\nexport const OpenIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <g transform=\"translate(24, 0) scale(-1, 1)\">\n      <path\n        fillRule=\"evenodd\"\n        d=\"M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z\"\n        clipRule=\"evenodd\"\n      />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\n  </svg>\n);\n\nexport const HeaderCloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const SendIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 19V5m0 0l-7 7m7-7l7 7\" />\n  </svg>\n);\n\nexport const MicrophoneIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z\"\n    />\n  </svg>\n);\n\nexport const StopIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z\"\n    />\n  </svg>\n);\n\nexport const RegenerateIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n    />\n  </svg>\n);\n\nexport const CopyIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\n    />\n  </svg>\n);\n\nexport const SmallSpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"13px\", height: \"13px\" }}></span>\n);\n\nexport const SpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"24px\", height: \"24px\" }}></span>\n);\n\nexport const ActivityIcon = (\n  <div style={{ display: \"flex\", alignItems: \"center\", gap: \"4px\" }}>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.2s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.4s\" }}></span>\n  </div>\n);\n\nexport const ThumbsUpIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z\"\n    />\n  </svg>\n);\n\nexport const ThumbsDownIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384\"\n    />\n  </svg>\n);\n\nexport const DownloadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\n    />\n  </svg>\n);\n\nexport const UploadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n  </svg>\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAyB;AACzB,sCAA2C;;;ACD3C,YAAuB;AAMhB,SAAS,mBAAmB,EAAE,UAAU,IAAK,GAA4B;AAC9E,QAAM,CAAC,UAAU,WAAW,IAAU,eAAkB,KAAK;AAE7D,QAAM,kBAAkB,CAAC,UAAkB;AAT7C;AAUI,QAAI,OAAO,WAAW,eAAe,GAAC,eAAU,cAAV,mBAAqB,YAAW;AACpE;AAAA,IACF;AAEA,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAEA,cAAU,UAAU,UAAU,KAAK,EAAE,KAAK,MAAM;AAC9C,kBAAY,IAAI;AAEhB,iBAAW,MAAM;AACf,oBAAY,KAAK;AAAA,MACnB,GAAG,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAO,EAAE,UAAU,gBAAgB;AACrC;;;ACjBM;AA0GC,IAAM,WACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAyDK,IAAM,eACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAiBK,IAAM,YACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,yBAAwB;AAAA;AAC/E;;;AFhJM,IAAAA,sBAAA;AAxED,IAAM,uBAAoC;AAAA,EAC/C,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,GAAG;AAAA,EACH,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA;AAEP;AAEO,IAAM,uBAAuB,CAAC,QAAgB,YAAY,UAAU;AACzE,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO,YAAY,OAAO,YAAY,IAAI;AAC5C;AAEA,IAAM,gBAAuB,mBAAK,CAAC,EAAE,UAAU,MAAM,MAAM;AACzD,QAAM,EAAE,UAAU,gBAAgB,IAAI,mBAAmB,EAAE,SAAS,IAAK,CAAC;AAE1E,QAAM,iBAAiB,MAAM;AAC3B,QAAI,OAAO,WAAW,aAAa;AACjC;AAAA,IACF;AACA,UAAM,gBAAgB,qBAAqB,QAAQ,KAAK;AACxD,UAAM,oBAAoB,QAAQ,qBAAqB,GAAG,IAAI,IAAI;AAClE,UAAM,WAAW,OAAO,OAAO,mBAAyB,iBAAiB;AAEzE,QAAI,CAAC,UAAU;AAEb;AAAA,IACF;AAEA,UAAM,OAAO,IAAI,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,aAAa,CAAC;AACrD,UAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,UAAM,OAAO,SAAS,cAAc,GAAG;AACvC,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,MAAM,UAAU;AACrB,aAAS,KAAK,YAAY,IAAI;AAC9B,SAAK,MAAM;AACX,aAAS,KAAK,YAAY,IAAI;AAC9B,QAAI,gBAAgB,GAAG;AAAA,EACzB;AAEA,QAAM,SAAS,MAAM;AACnB,QAAI;AAAU;AACd,oBAAgB,KAAK;AAAA,EACvB;AAEA,SACE,8CAAC,SAAI,WAAU,uBACb;AAAA,kDAAC,SAAI,WAAU,8BACb;AAAA,mDAAC,UAAK,WAAU,sCAAsC,oBAAS;AAAA,MAC/D,8CAAC,SAAI,WAAU,qCACb;AAAA,qDAAC,YAAO,WAAU,oCAAmC,SAAS,gBAC3D,wBACH;AAAA,QACA,6CAAC,YAAO,WAAU,oCAAmC,SAAS,QAC3D,qBAAW,YAAY,UAC1B;AAAA,SACF;AAAA,OACF;AAAA,IACA;AAAA,MAAC,gCAAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,OAAO;AAAA,QACP,QAAO;AAAA,QACP,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,wBAAwB;AAAA,UACxB,yBAAyB;AAAA,QAC3B;AAAA,QAEC;AAAA;AAAA,IACH;AAAA,KACF;AAEJ,CAAC;AACD,UAAU,cAAc;AAQxB,IAAM,iBAAsB;AAAA,EAC1B,2BAA2B;AAAA,IACzB,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,sCAAsC;AAAA,IACpC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACrC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,wCAAwC;AAAA,IACtC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,yCAAyC;AAAA,IACvC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,wCAAwC;AAAA,IACtC,SAAS;AAAA,IACT,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,mDAAmD;AAAA,IACjD,OAAO;AAAA,EACT;AAAA,EACA,0DAA0D;AAAA,IACxD,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,mCAAmC;AAAA,IACjC,gBAAgB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,IACrB,OAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AAAA,IACtB,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,6BAA6B;AAAA,IAC3B,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,yCAAyC;AAAA,IACvC,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,gCAAgC;AAAA,IAC9B,OAAO;AAAA,EACT;AAAA,EACA,iCAAiC;AAAA,IAC/B,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sDAAsD;AAAA,IACpD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,kCAAkC;AAAA,IAChC,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,EACV;AACF;", "names": ["import_jsx_runtime", "Syntax<PERSON><PERSON><PERSON><PERSON>"]}