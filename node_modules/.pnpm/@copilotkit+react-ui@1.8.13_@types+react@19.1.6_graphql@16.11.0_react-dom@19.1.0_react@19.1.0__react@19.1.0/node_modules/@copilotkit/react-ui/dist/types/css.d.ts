import { CSSProperties } from 'react';

interface CopilotKitCSSProperties extends CSSProperties {
    "--copilot-kit-primary-color"?: string;
    "--copilot-kit-contrast-color"?: string;
    "--copilot-kit-background-color"?: string;
    "--copilot-kit-input-background-color"?: string;
    "--copilot-kit-secondary-color"?: string;
    "--copilot-kit-secondary-contrast-color"?: string;
    "--copilot-kit-separator-color"?: string;
    "--copilot-kit-muted-color"?: string;
    "--copilot-kit-shadow-sm"?: string;
    "--copilot-kit-shadow-md"?: string;
    "--copilot-kit-shadow-lg"?: string;
    "--copilot-kit-dev-console-bg"?: string;
    "--copilot-kit-dev-console-text"?: string;
}

export { CopilotKitCSSProperties };
