import * as react_jsx_runtime from 'react/jsx-runtime';

declare function CopilotDevConsole(): react_jsx_runtime.JSX.Element | null;
declare function DebugMenuButton({ setShowDevConsole, checkForUpdates, mode, }: {
    setShowDevConsole: (show: boolean) => void;
    checkForUpdates: (force: boolean) => void;
    mode: "full" | "compact";
}): react_jsx_runtime.JSX.Element;

export { CopilotDevConsole, DebugMenuButton as default };
