{"version": 3, "sources": ["../src/hooks/use-langgraph-interrupt.ts"], "sourcesContent": ["import { useContext, useEffect, useMemo } from \"react\";\nimport { CopilotContext } from \"../context/copilot-context\";\nimport { LangGraphInterruptRender } from \"../types/interrupt-action\";\nimport { useCopilotChat } from \"./use-copilot-chat\";\nimport { useToast } from \"../components/toast/toast-provider\";\nimport { dataToUUID } from \"@copilotkit/shared\";\n\nexport function useLangGraphInterrupt<TEventValue = any>(\n  action: Omit<LangGraphInterruptRender<TEventValue>, \"id\">,\n  dependencies?: any[],\n) {\n  const { setLangGraphInterruptAction, removeLangGraphInterruptAction, langGraphInterruptAction } =\n    useContext(CopilotContext);\n  const { runChatCompletion } = useCopilotChat();\n  const { addToast } = useToast();\n\n  const actionId = dataToUUID(JSON.stringify(action), \"lgAction\");\n  // We only consider action to be defined once the ID is there\n  const hasAction = useMemo(\n    () => Boolean(langGraphInterruptAction?.id),\n    [langGraphInterruptAction],\n  );\n\n  const isCurrentAction = useMemo(\n    () => langGraphInterruptAction?.id && langGraphInterruptAction?.id === actionId,\n    [langGraphInterruptAction],\n  );\n\n  // Run chat completion to submit a response event. Only if it's the current action\n  useEffect(() => {\n    if (hasAction && isCurrentAction && langGraphInterruptAction?.event?.response) {\n      runChatCompletion();\n    }\n  }, [langGraphInterruptAction?.event?.response, runChatCompletion, hasAction, isCurrentAction]);\n\n  useEffect(() => {\n    if (!action) return;\n    // An action was already set, with no conditions and it's not the action we're using right now.\n    // Show a warning, as this action will not be executed\n    if (hasAction && !isCurrentAction && !action.enabled) {\n      addToast({\n        type: \"warning\",\n        message: \"An action is already registered for the interrupt event\",\n      });\n      return;\n    }\n\n    if (hasAction && isCurrentAction) {\n      return;\n    }\n\n    setLangGraphInterruptAction({ ...action, id: actionId });\n  }, [\n    action,\n    hasAction,\n    isCurrentAction,\n    setLangGraphInterruptAction,\n    removeLangGraphInterruptAction,\n    ...(dependencies || []),\n  ]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,YAAY,WAAW,eAAe;AAK/C,SAAS,kBAAkB;AAEpB,SAAS,sBACd,QACA,cACA;AAVF;AAWE,QAAM,EAAE,6BAA6B,gCAAgC,yBAAyB,IAC5F,WAAW,cAAc;AAC3B,QAAM,EAAE,kBAAkB,IAAI,eAAe;AAC7C,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,QAAM,WAAW,WAAW,KAAK,UAAU,MAAM,GAAG,UAAU;AAE9D,QAAM,YAAY;AAAA,IAChB,MAAM,QAAQ,qEAA0B,EAAE;AAAA,IAC1C,CAAC,wBAAwB;AAAA,EAC3B;AAEA,QAAM,kBAAkB;AAAA,IACtB,OAAM,qEAA0B,QAAM,qEAA0B,QAAO;AAAA,IACvE,CAAC,wBAAwB;AAAA,EAC3B;AAGA,YAAU,MAAM;AA7BlB,QAAAA;AA8BI,QAAI,aAAa,qBAAmBA,MAAA,qEAA0B,UAA1B,gBAAAA,IAAiC,WAAU;AAC7E,wBAAkB;AAAA,IACpB;AAAA,EACF,GAAG,EAAC,0EAA0B,UAA1B,mBAAiC,UAAU,mBAAmB,WAAW,eAAe,CAAC;AAE7F,YAAU,MAAM;AACd,QAAI,CAAC;AAAQ;AAGb,QAAI,aAAa,CAAC,mBAAmB,CAAC,OAAO,SAAS;AACpD,eAAS;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,MACX,CAAC;AACD;AAAA,IACF;AAEA,QAAI,aAAa,iBAAiB;AAChC;AAAA,IACF;AAEA,gCAA4B,iCAAK,SAAL,EAAa,IAAI,SAAS,EAAC;AAAA,EACzD,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAI,gBAAgB,CAAC;AAAA,EACvB,CAAC;AACH;", "names": ["_a"]}