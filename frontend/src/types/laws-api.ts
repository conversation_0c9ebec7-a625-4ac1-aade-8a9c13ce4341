/**
 * Types for Laws-API Service Integration
 * Base URL: https://legal-api-stg-gfunh6mfpa-uc.a.run.app
 * Endpoints: /v0/search, /v0/recommend, /v0/graph
 */

// Base API Response Structure
export interface LawsApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  pagination?: PaginationInfo;
  rate_limit?: RateLimitInfo;
}

export interface PaginationInfo {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset_at: string;
  retry_after?: number;
}

// Search API Types (/v1/search)
export interface SearchRequest {
  query: string;
  jurisdiction?: string[];
  document_type?: DocumentType[];
  limit?: number;
  offset?: number;
  filters?: SearchFilters;
  // New v1 parameters
  practice_areas?: PracticeArea[];
  date_start?: string;
  date_end?: string;
  sort_by?: SortBy;
  authority_min?: number;
}

export interface SearchFilters {
  date_range?: {
    start?: string;
    end?: string;
  };
  court_level?: CourtLevel[];
  practice_area?: PracticeArea[];
  relevance_threshold?: number;
}

// New enum for sorting options
export enum SortBy {
  RELEVANCE = 'relevance',
  DATE = 'date',
  AUTHORITY = 'authority',
  CITATION_COUNT = 'citation_count'
}

export interface SearchResult {
  id: string;
  title: string;
  content: string;
  document_type: DocumentType;
  jurisdiction: string;
  court_level?: CourtLevel;
  practice_area?: PracticeArea[];
  date_published?: string;
  citation?: string;
  url?: string;
  relevance_score: number;
  highlights?: string[];
  metadata?: Record<string, any>;
}

// Recommendation API Types (/v0/recommend)
export interface RecommendRequest {
  document_id?: string;
  content?: string;
  context?: string;
  jurisdiction?: string[];
  limit?: number;
  similarity_threshold?: number;
}

export interface RecommendationResult {
  id: string;
  title: string;
  content: string;
  document_type: DocumentType;
  jurisdiction: string;
  similarity_score: number;
  relationship_type: RelationshipType;
  explanation?: string;
  metadata?: Record<string, any>;
}

// Graph API Types (/v0/graph)
export interface GraphRequest {
  entity_id?: string;
  entity_type?: EntityType;
  relationship_types?: RelationshipType[];
  depth?: number;
  limit?: number;
}

export interface GraphNode {
  id: string;
  label: string;
  type: EntityType;
  properties: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface GraphEdge {
  source: string;
  target: string;
  relationship: RelationshipType;
  weight?: number;
  properties?: Record<string, any>;
}

export interface GraphResult {
  nodes: GraphNode[];
  edges: GraphEdge[];
  metadata?: {
    query_time_ms: number;
    total_nodes: number;
    total_edges: number;
  };
}

// Enums and Constants
export enum DocumentType {
  STATUTE = 'statute',
  CASE_LAW = 'case_law',
  REGULATION = 'regulation',
  CONSTITUTIONAL = 'constitutional',
  ADMINISTRATIVE = 'administrative',
  MUNICIPAL = 'municipal'
}

export enum CourtLevel {
  SUPREME_COURT = 'supreme_court',
  APPELLATE = 'appellate',
  DISTRICT = 'district',
  COUNTY = 'county',
  MUNICIPAL = 'municipal',
  FEDERAL_DISTRICT = 'federal_district',
  FEDERAL_APPELLATE = 'federal_appellate',
  FEDERAL_SUPREME = 'federal_supreme'
}

export enum PracticeArea {
  PERSONAL_INJURY = 'personal_injury',
  CONTRACT = 'contract',
  CRIMINAL = 'criminal',
  FAMILY = 'family',
  EMPLOYMENT = 'employment',
  REAL_ESTATE = 'real_estate',
  INTELLECTUAL_PROPERTY = 'intellectual_property',
  CORPORATE = 'corporate',
  TAX = 'tax',
  IMMIGRATION = 'immigration'
}

export enum EntityType {
  STATUTE = 'statute',
  CASE = 'case',
  COURT = 'court',
  JUDGE = 'judge',
  ATTORNEY = 'attorney',
  CONCEPT = 'concept',
  CITATION = 'citation'
}

export enum RelationshipType {
  CITES = 'cites',
  CITED_BY = 'cited_by',
  OVERRULES = 'overrules',
  OVERRULED_BY = 'overruled_by',
  DISTINGUISHES = 'distinguishes',
  FOLLOWS = 'follows',
  RELATED_TO = 'related_to',
  SUPERSEDES = 'supersedes',
  SUPERSEDED_BY = 'superseded_by'
}

// Error Types
export interface LawsApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  retry_after?: number;
}

export class LawsApiException extends Error {
  public readonly code: string;
  public readonly status: number;
  public readonly retryAfter?: number;

  constructor(
    message: string,
    code: string,
    status: number,
    retryAfter?: number
  ) {
    super(message);
    this.name = 'LawsApiException';
    this.code = code;
    this.status = status;
    this.retryAfter = retryAfter;
  }
}

// Client Configuration
export interface LawsApiConfig {
  baseUrl: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableRateLimit?: boolean;
}

// Default configuration
export const DEFAULT_LAWS_API_CONFIG: LawsApiConfig = {
  baseUrl: 'https://legal-api-stg-gfunh6mfpa-uc.a.run.app', // Will be overridden by config
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  enableRateLimit: true
};

// Utility Types
export type LawsApiEndpoint = '/v1/search' | '/v0/recommend' | '/v0/graph';

export interface RequestOptions {
  signal?: AbortSignal;
  timeout?: number;
  retryAttempts?: number;
}
