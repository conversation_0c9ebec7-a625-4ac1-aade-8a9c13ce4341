import Link from 'next/link'

// Force dynamic rendering for not-found page to prevent SSR issues
export const dynamic = 'force-dynamic'

export default function NotFound() {
  return (
    <html lang="en">
      <head>
        <title>Page Not Found - Pi Lawyer AI</title>
      </head>
      <body className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-xl text-gray-600 mb-6">Page Not Found</h2>
          <p className="text-gray-500 mb-8">
            The page you are looking for does not exist.
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Return Home
          </Link>
        </div>
      </body>
    </html>
  )
}
