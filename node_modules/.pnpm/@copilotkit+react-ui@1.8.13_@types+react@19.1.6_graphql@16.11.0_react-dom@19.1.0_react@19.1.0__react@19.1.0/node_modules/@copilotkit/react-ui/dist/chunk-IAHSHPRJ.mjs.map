{"version": 3, "sources": ["../src/components/chat/Chat.tsx"], "sourcesContent": ["/**\n * <br/>\n * <img src=\"/images/CopilotChat.gif\" width=\"500\" />\n *\n * A chatbot panel component for the CopilotKit framework. The component allows for a high degree\n * of customization through various props and custom CSS.\n *\n * ## Install Dependencies\n *\n * This component is part of the [@copilotkit/react-ui](https://npmjs.com/package/@copilotkit/react-ui) package.\n *\n * ```shell npm2yarn \\\"@copilotkit/react-ui\"\\\n * npm install @copilotkit/react-core @copilotkit/react-ui\n * ```\n *\n * ## Usage\n *\n * ```tsx\n * import { CopilotChat } from \"@copilotkit/react-ui\";\n * import \"@copilotkit/react-ui/styles.css\";\n *\n * <CopilotChat\n *   labels={{\n *     title: \"Your Assistant\",\n *     initial: \"Hi! 👋 How can I assist you today?\",\n *   }}\n * />\n * ```\n *\n * ### Look & Feel\n *\n * By default, CopilotKit components do not have any styles. You can import CopilotKit's stylesheet at the root of your project:\n * ```tsx title=\"YourRootComponent.tsx\"\n * ...\n * import \"@copilotkit/react-ui/styles.css\"; // [!code highlight]\n *\n * export function YourRootComponent() {\n *   return (\n *     <CopilotKit>\n *       ...\n *     </CopilotKit>\n *   );\n * }\n * ```\n * For more information about how to customize the styles, check out the [Customize Look & Feel](/guides/custom-look-and-feel/customize-built-in-ui-components) guide.\n */\n\nimport {\n  ChatContext,\n  ChatContextProvider,\n  CopilotChatIcons,\n  CopilotChatLabels,\n} from \"./ChatContext\";\nimport { Messages as DefaultMessages } from \"./Messages\";\nimport { Input as DefaultInput } from \"./Input\";\nimport { RenderTextMessage as DefaultRenderTextMessage } from \"./messages/RenderTextMessage\";\nimport { RenderActionExecutionMessage as DefaultRenderActionExecutionMessage } from \"./messages/RenderActionExecutionMessage\";\nimport { RenderResultMessage as DefaultRenderResultMessage } from \"./messages/RenderResultMessage\";\nimport { RenderAgentStateMessage as DefaultRenderAgentStateMessage } from \"./messages/RenderAgentStateMessage\";\nimport { RenderImageMessage as DefaultRenderImageMessage } from \"./messages/RenderImageMessage\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./messages/AssistantMessage\";\nimport { UserMessage as DefaultUserMessage } from \"./messages/UserMessage\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport {\n  SystemMessageFunction,\n  useCopilotChat,\n  useCopilotContext,\n  useCopilotMessagesContext,\n} from \"@copilotkit/react-core\";\nimport { reloadSuggestions } from \"./Suggestion\";\nimport { CopilotChatSuggestion } from \"../../types/suggestions\";\nimport { Message, Role, TextMessage, ImageMessage } from \"@copilotkit/runtime-client-gql\";\nimport { randomId } from \"@copilotkit/shared\";\nimport {\n  AssistantMessageProps,\n  ComponentsMap,\n  InputProps,\n  MessagesProps,\n  RenderMessageProps,\n  RenderSuggestionsListProps,\n  UserMessageProps,\n} from \"./props\";\n\nimport { HintFunction, runAgent, stopAgent } from \"@copilotkit/react-core\";\nimport { ImageUploadQueue } from \"./ImageUploadQueue\";\nimport { Suggestions as DefaultRenderSuggestionsList } from \"./Suggestions\";\n\n/**\n * Props for CopilotChat component.\n */\nexport interface CopilotChatProps {\n  /**\n   * Custom instructions to be added to the system message. Use this property to\n   * provide additional context or guidance to the language model, influencing\n   * its responses. These instructions can include specific directions,\n   * preferences, or criteria that the model should consider when generating\n   * its output, thereby tailoring the conversation more precisely to the\n   * user's needs or the application's requirements.\n   */\n  instructions?: string;\n\n  /**\n   * A callback that gets called when the in progress state changes.\n   */\n  onInProgress?: (inProgress: boolean) => void;\n\n  /**\n   * A callback that gets called when a new message it submitted.\n   */\n  onSubmitMessage?: (message: string) => void | Promise<void>;\n\n  /**\n   * A custom stop generation function.\n   */\n  onStopGeneration?: OnStopGeneration;\n\n  /**\n   * A custom reload messages function.\n   */\n  onReloadMessages?: OnReloadMessages;\n\n  /**\n   * A callback function to regenerate the assistant's response\n   */\n  onRegenerate?: (messageId: string) => void;\n\n  /**\n   * A callback function when the message is copied\n   */\n  onCopy?: (message: string) => void;\n\n  /**\n   * A callback function for thumbs up feedback\n   */\n  onThumbsUp?: (message: string) => void;\n\n  /**\n   * A callback function for thumbs down feedback\n   */\n  onThumbsDown?: (message: string) => void;\n\n  /**\n   * A list of markdown components to render in assistant message.\n   * Useful when you want to render custom elements in the message (e.g a reference tag element)\n   */\n  markdownTagRenderers?: ComponentsMap;\n\n  /**\n   * Icons can be used to set custom icons for the chat window.\n   */\n  icons?: CopilotChatIcons;\n\n  /**\n   * Labels can be used to set custom labels for the chat window.\n   */\n  labels?: CopilotChatLabels;\n\n  /**\n   * Enable image upload button (image inputs only supported on some models)\n   */\n  imageUploadsEnabled?: boolean;\n\n  /**\n   * The 'accept' attribute for the file input used for image uploads.\n   * Defaults to \"image/*\".\n   */\n  inputFileAccept?: string;\n\n  /**\n   * A function that takes in context string and instructions and returns\n   * the system message to include in the chat request.\n   * Use this to completely override the system message, when providing\n   * instructions is not enough.\n   */\n  makeSystemMessage?: SystemMessageFunction;\n\n  /**\n   * A custom assistant message component to use instead of the default.\n   */\n  AssistantMessage?: React.ComponentType<AssistantMessageProps>;\n\n  /**\n   * A custom user message component to use instead of the default.\n   */\n  UserMessage?: React.ComponentType<UserMessageProps>;\n\n  /**\n   * A custom Messages component to use instead of the default.\n   */\n  Messages?: React.ComponentType<MessagesProps>;\n\n  /**\n   * A custom RenderTextMessage component to use instead of the default.\n   */\n  RenderTextMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderActionExecutionMessage component to use instead of the default.\n   */\n  RenderActionExecutionMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderAgentStateMessage component to use instead of the default.\n   */\n  RenderAgentStateMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderResultMessage component to use instead of the default.\n   */\n  RenderResultMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderImageMessage component to use instead of the default.\n   */\n  RenderImageMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom suggestions list component to use instead of the default.\n   */\n  RenderSuggestionsList?: React.ComponentType<RenderSuggestionsListProps>;\n\n  /**\n   * A custom Input component to use instead of the default.\n   */\n  Input?: React.ComponentType<InputProps>;\n\n  /**\n   * A class name to apply to the root element.\n   */\n  className?: string;\n\n  /**\n   * Children to render.\n   */\n  children?: React.ReactNode;\n}\n\ninterface OnStopGenerationArguments {\n  /**\n   * The name of the currently executing agent.\n   */\n  currentAgentName: string | undefined;\n\n  /**\n   * The messages in the chat.\n   */\n  messages: Message[];\n\n  /**\n   * Set the messages in the chat.\n   */\n  setMessages: (messages: Message[]) => void;\n\n  /**\n   * Stop chat generation.\n   */\n  stopGeneration: () => void;\n\n  /**\n   * Restart the currently executing agent.\n   */\n  restartCurrentAgent: () => void;\n\n  /**\n   * Stop the currently executing agent.\n   */\n  stopCurrentAgent: () => void;\n\n  /**\n   * Run the currently executing agent.\n   */\n  runCurrentAgent: (hint?: HintFunction) => Promise<void>;\n\n  /**\n   * Set the state of the currently executing agent.\n   */\n  setCurrentAgentState: (state: any) => void;\n}\n\nexport type OnReloadMessagesArguments = OnStopGenerationArguments & {\n  /**\n   * The message on which \"regenerate\" was pressed\n   */\n  messageId: string;\n};\n\nexport type OnStopGeneration = (args: OnStopGenerationArguments) => void;\n\nexport type OnReloadMessages = (args: OnReloadMessagesArguments) => void;\n\nexport type ImageUpload = {\n  contentType: string;\n  bytes: string;\n};\n\nexport function CopilotChat({\n  instructions,\n  onSubmitMessage,\n  makeSystemMessage,\n  onInProgress,\n  onStopGeneration,\n  onReloadMessages,\n  onRegenerate,\n  onCopy,\n  onThumbsUp,\n  onThumbsDown,\n  markdownTagRenderers,\n  Messages = DefaultMessages,\n  RenderTextMessage = DefaultRenderTextMessage,\n  RenderActionExecutionMessage = DefaultRenderActionExecutionMessage,\n  RenderAgentStateMessage = DefaultRenderAgentStateMessage,\n  RenderResultMessage = DefaultRenderResultMessage,\n  RenderImageMessage = DefaultRenderImageMessage,\n  RenderSuggestionsList = DefaultRenderSuggestionsList,\n  Input = DefaultInput,\n  className,\n  icons,\n  labels,\n  AssistantMessage = DefaultAssistantMessage,\n  UserMessage = DefaultUserMessage,\n  imageUploadsEnabled,\n  inputFileAccept = \"image/*\",\n}: CopilotChatProps) {\n  const { additionalInstructions, setChatInstructions } = useCopilotContext();\n  const [selectedImages, setSelectedImages] = useState<Array<ImageUpload>>([]);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Clipboard paste handler\n  useEffect(() => {\n    if (!imageUploadsEnabled) return;\n\n    const handlePaste = async (e: ClipboardEvent) => {\n      const target = e.target as HTMLElement;\n      if (!target.parentElement?.classList.contains(\"copilotKitInput\")) return;\n\n      const items = Array.from(e.clipboardData?.items || []);\n      const imageItems = items.filter((item) => item.type.startsWith(\"image/\"));\n\n      if (imageItems.length === 0) return;\n\n      e.preventDefault(); // Prevent default paste behavior for images\n\n      const imagePromises: Promise<ImageUpload | null>[] = imageItems.map((item) => {\n        const file = item.getAsFile();\n        if (!file) return Promise.resolve(null);\n\n        return new Promise<ImageUpload | null>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (e) => {\n            const base64String = (e.target?.result as string)?.split(\",\")[1];\n            if (base64String) {\n              resolve({\n                contentType: file.type,\n                bytes: base64String,\n              });\n            } else {\n              resolve(null);\n            }\n          };\n          reader.onerror = reject;\n          reader.readAsDataURL(file);\n        });\n      });\n\n      try {\n        const loadedImages = (await Promise.all(imagePromises)).filter((img) => img !== null);\n        setSelectedImages((prev) => [...prev, ...loadedImages]);\n      } catch (error) {\n        // TODO: Show an error message to the user\n        console.error(\"Error processing pasted images:\", error);\n      }\n    };\n\n    document.addEventListener(\"paste\", handlePaste);\n    return () => document.removeEventListener(\"paste\", handlePaste);\n  }, [imageUploadsEnabled]);\n\n  useEffect(() => {\n    if (!additionalInstructions?.length) {\n      setChatInstructions(instructions || \"\");\n      return;\n    }\n\n    /*\n      Will result in a prompt like:\n\n      You are a helpful assistant. \n      Additionally, follow these instructions:\n      - Do not answer questions about the weather.\n      - Do not answer questions about the stock market.\"\n    */\n    const combinedAdditionalInstructions = [\n      instructions,\n      \"Additionally, follow these instructions:\",\n      ...additionalInstructions.map((instruction) => `- ${instruction}`),\n    ];\n\n    console.log(\"combinedAdditionalInstructions\", combinedAdditionalInstructions);\n\n    setChatInstructions(combinedAdditionalInstructions.join(\"\\n\") || \"\");\n  }, [instructions, additionalInstructions]);\n\n  const {\n    visibleMessages,\n    isLoading,\n    currentSuggestions,\n    sendMessage,\n    stopGeneration,\n    reloadMessages,\n  } = useCopilotChatLogic(\n    makeSystemMessage,\n    onInProgress,\n    onSubmitMessage,\n    onStopGeneration,\n    onReloadMessages,\n  );\n\n  // Wrapper for sendMessage to clear selected images\n  const handleSendMessage = (text: string) => {\n    const images = selectedImages;\n    setSelectedImages([]);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = \"\";\n    }\n\n    return sendMessage(text, images);\n  };\n\n  const chatContext = React.useContext(ChatContext);\n  const isVisible = chatContext ? chatContext.open : true;\n\n  const handleRegenerate = (messageId: string) => {\n    if (onRegenerate) {\n      onRegenerate(messageId);\n    }\n\n    reloadMessages(messageId);\n  };\n\n  const handleCopy = (message: string) => {\n    if (onCopy) {\n      onCopy(message);\n    }\n  };\n\n  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    if (!event.target.files || event.target.files.length === 0) {\n      return;\n    }\n\n    const files = Array.from(event.target.files).filter((file) => file.type.startsWith(\"image/\"));\n    if (files.length === 0) return;\n\n    const fileReadPromises = files.map((file) => {\n      return new Promise<{ contentType: string; bytes: string }>((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const base64String = (e.target?.result as string)?.split(\",\")[1] || \"\";\n          if (base64String) {\n            resolve({\n              contentType: file.type,\n              bytes: base64String,\n            });\n          }\n        };\n        reader.onerror = reject;\n        reader.readAsDataURL(file);\n      });\n    });\n\n    try {\n      const loadedImages = await Promise.all(fileReadPromises);\n      setSelectedImages((prev) => [...prev, ...loadedImages]);\n    } catch (error) {\n      // TODO: Show an error message to the user\n      console.error(\"Error reading files:\", error);\n    }\n  };\n\n  const removeSelectedImage = (index: number) => {\n    setSelectedImages((prev) => prev.filter((_, i) => i !== index));\n  };\n\n  return (\n    <WrappedCopilotChat icons={icons} labels={labels} className={className}>\n      <Messages\n        AssistantMessage={AssistantMessage}\n        UserMessage={UserMessage}\n        RenderTextMessage={RenderTextMessage}\n        RenderActionExecutionMessage={RenderActionExecutionMessage}\n        RenderAgentStateMessage={RenderAgentStateMessage}\n        RenderResultMessage={RenderResultMessage}\n        RenderImageMessage={RenderImageMessage}\n        messages={visibleMessages}\n        inProgress={isLoading}\n        onRegenerate={handleRegenerate}\n        onCopy={handleCopy}\n        onThumbsUp={onThumbsUp}\n        onThumbsDown={onThumbsDown}\n        markdownTagRenderers={markdownTagRenderers}\n      >\n        {currentSuggestions.length > 0 && (\n          <RenderSuggestionsList\n            onSuggestionClick={handleSendMessage}\n            suggestions={currentSuggestions}\n          />\n        )}\n      </Messages>\n\n      {imageUploadsEnabled && (\n        <>\n          <ImageUploadQueue images={selectedImages} onRemoveImage={removeSelectedImage} />\n          <input\n            type=\"file\"\n            multiple\n            ref={fileInputRef}\n            onChange={handleImageUpload}\n            accept={inputFileAccept}\n            style={{ display: \"none\" }}\n          />\n        </>\n      )}\n\n      <Input\n        inProgress={isLoading}\n        onSend={handleSendMessage}\n        isVisible={isVisible}\n        onStop={stopGeneration}\n        onUpload={imageUploadsEnabled ? () => fileInputRef.current?.click() : undefined}\n      />\n    </WrappedCopilotChat>\n  );\n}\n\nexport function WrappedCopilotChat({\n  children,\n  icons,\n  labels,\n  className,\n}: {\n  children: React.ReactNode;\n  icons?: CopilotChatIcons;\n  labels?: CopilotChatLabels;\n  className?: string;\n}) {\n  const chatContext = React.useContext(ChatContext);\n  if (!chatContext) {\n    return (\n      <ChatContextProvider icons={icons} labels={labels} open={true} setOpen={() => {}}>\n        <div className={`copilotKitChat ${className ?? \"\"}`}>{children}</div>\n      </ChatContextProvider>\n    );\n  }\n  return <>{children}</>;\n}\n\nconst SUGGESTIONS_DEBOUNCE_TIMEOUT = 1000;\n\nexport const useCopilotChatLogic = (\n  makeSystemMessage?: SystemMessageFunction,\n  onInProgress?: (isLoading: boolean) => void,\n  onSubmitMessage?: (messageContent: string) => Promise<void> | void,\n  onStopGeneration?: OnStopGeneration,\n  onReloadMessages?: OnReloadMessages,\n) => {\n  const {\n    visibleMessages,\n    appendMessage,\n    reloadMessages: defaultReloadMessages,\n    stopGeneration: defaultStopGeneration,\n    runChatCompletion,\n    isLoading,\n  } = useCopilotChat({\n    id: randomId(),\n    makeSystemMessage,\n  });\n\n  const [currentSuggestions, setCurrentSuggestions] = useState<CopilotChatSuggestion[]>([]);\n  const suggestionsAbortControllerRef = useRef<AbortController | null>(null);\n  const debounceTimerRef = useRef<any>();\n\n  const abortSuggestions = () => {\n    suggestionsAbortControllerRef.current?.abort();\n    suggestionsAbortControllerRef.current = null;\n  };\n\n  const generalContext = useCopilotContext();\n  const messagesContext = useCopilotMessagesContext();\n  const context = { ...generalContext, ...messagesContext };\n\n  useEffect(() => {\n    onInProgress?.(isLoading);\n\n    abortSuggestions();\n\n    debounceTimerRef.current = setTimeout(\n      () => {\n        if (!isLoading && Object.keys(context.chatSuggestionConfiguration).length !== 0) {\n          suggestionsAbortControllerRef.current = new AbortController();\n          reloadSuggestions(\n            context,\n            context.chatSuggestionConfiguration,\n            setCurrentSuggestions,\n            suggestionsAbortControllerRef,\n          );\n        }\n      },\n      currentSuggestions.length == 0 ? 0 : SUGGESTIONS_DEBOUNCE_TIMEOUT,\n    );\n\n    return () => {\n      clearTimeout(debounceTimerRef.current);\n    };\n  }, [\n    isLoading,\n    context.chatSuggestionConfiguration,\n    // hackish way to trigger suggestions reload on reset, but better than moving suggestions to the\n    // global context\n    visibleMessages.length == 0,\n  ]);\n\n  const sendMessage = async (\n    messageContent: string,\n    imagesToUse?: Array<{ contentType: string; bytes: string }>,\n  ) => {\n    // Use images passed in the call OR the ones from the state (passed via props)\n    const images = imagesToUse || [];\n\n    abortSuggestions();\n    setCurrentSuggestions([]);\n\n    let firstMessage: Message | null = null;\n\n    // If there's text content, send a text message first\n    if (messageContent.trim().length > 0) {\n      const textMessage = new TextMessage({\n        content: messageContent,\n        role: Role.User,\n      });\n\n      if (onSubmitMessage) {\n        try {\n          // Call onSubmitMessage only with text, as image handling is internal right now\n          await onSubmitMessage(messageContent);\n        } catch (error) {\n          console.error(\"Error in onSubmitMessage:\", error);\n        }\n      }\n\n      await appendMessage(textMessage, { followUp: images.length === 0 });\n\n      if (!firstMessage) {\n        firstMessage = textMessage;\n      }\n    }\n\n    // Send image messages\n    if (images.length > 0) {\n      for (let i = 0; i < images.length; i++) {\n        const imageMessage = new ImageMessage({\n          format: images[i].contentType.replace(\"image/\", \"\"),\n          bytes: images[i].bytes,\n          role: Role.User,\n        });\n        await appendMessage(imageMessage, { followUp: i === images.length - 1 });\n        if (!firstMessage) {\n          firstMessage = imageMessage;\n        }\n      }\n    }\n\n    if (!firstMessage) {\n      // Should not happen if send button is properly disabled, but handle just in case\n      return new TextMessage({ content: \"\", role: Role.User }); // Return a dummy message\n    }\n\n    // The hook implicitly triggers API call on appendMessage.\n    // We return the first message sent (either text or first image)\n    return firstMessage;\n  };\n\n  const messages = visibleMessages;\n  const { setMessages } = messagesContext;\n  const currentAgentName = generalContext.agentSession?.agentName;\n  const restartCurrentAgent = async (hint?: HintFunction) => {\n    if (generalContext.agentSession) {\n      generalContext.setAgentSession({\n        ...generalContext.agentSession,\n        nodeName: undefined,\n        threadId: undefined,\n      });\n      generalContext.setCoagentStates((prevAgentStates) => {\n        return {\n          ...prevAgentStates,\n          [generalContext.agentSession!.agentName]: {\n            ...prevAgentStates[generalContext.agentSession!.agentName],\n            threadId: undefined,\n            nodeName: undefined,\n            runId: undefined,\n          },\n        };\n      });\n    }\n  };\n  const runCurrentAgent = async (hint?: HintFunction) => {\n    if (generalContext.agentSession) {\n      await runAgent(\n        generalContext.agentSession.agentName,\n        context,\n        appendMessage,\n        runChatCompletion,\n        hint,\n      );\n    }\n  };\n  const stopCurrentAgent = () => {\n    if (generalContext.agentSession) {\n      stopAgent(generalContext.agentSession.agentName, context);\n    }\n  };\n  const setCurrentAgentState = (state: any) => {\n    if (generalContext.agentSession) {\n      generalContext.setCoagentStates((prevAgentStates) => {\n        return {\n          ...prevAgentStates,\n          [generalContext.agentSession!.agentName]: {\n            state,\n          },\n        } as any;\n      });\n    }\n  };\n\n  function stopGeneration() {\n    if (onStopGeneration) {\n      onStopGeneration({\n        messages,\n        setMessages,\n        stopGeneration: defaultStopGeneration,\n        currentAgentName,\n        restartCurrentAgent,\n        stopCurrentAgent,\n        runCurrentAgent,\n        setCurrentAgentState,\n      });\n    } else {\n      defaultStopGeneration();\n    }\n  }\n  function reloadMessages(messageId: string) {\n    if (onReloadMessages) {\n      onReloadMessages({\n        messages,\n        setMessages,\n        stopGeneration: defaultStopGeneration,\n        currentAgentName,\n        restartCurrentAgent,\n        stopCurrentAgent,\n        runCurrentAgent,\n        setCurrentAgentState,\n        messageId,\n      });\n    } else {\n      defaultReloadMessages(messageId);\n    }\n  }\n\n  return {\n    visibleMessages,\n    isLoading,\n    currentSuggestions,\n    sendMessage,\n    stopGeneration,\n    reloadMessages,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,OAAO,SAAS,WAAW,QAAQ,gBAAgB;AACnD;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAGP,SAAkB,MAAM,aAAa,oBAAoB;AACzD,SAAS,gBAAgB;AAWzB,SAAuB,UAAU,iBAAiB;AAmaxC,SAQF,UARE,KAQF,YARE;AA/MH,SAAS,YAAY;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAAA,YAAW;AAAA,EACX,mBAAAC,qBAAoB;AAAA,EACpB,8BAAAC,gCAA+B;AAAA,EAC/B,yBAAAC,2BAA0B;AAAA,EAC1B,qBAAAC,uBAAsB;AAAA,EACtB,oBAAAC,sBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,OAAAC,SAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAAC,oBAAmB;AAAA,EACnB,aAAAC,eAAc;AAAA,EACd;AAAA,EACA,kBAAkB;AACpB,GAAqB;AACnB,QAAM,EAAE,wBAAwB,oBAAoB,IAAI,kBAAkB;AAC1E,QAAM,CAAC,gBAAgB,iBAAiB,IAAI,SAA6B,CAAC,CAAC;AAC3E,QAAM,eAAe,OAAyB,IAAI;AAGlD,YAAU,MAAM;AACd,QAAI,CAAC;AAAqB;AAE1B,UAAM,cAAc,CAAO,MAAsB;AA3UrD;AA4UM,YAAM,SAAS,EAAE;AACjB,UAAI,GAAC,YAAO,kBAAP,mBAAsB,UAAU,SAAS;AAAoB;AAElE,YAAM,QAAQ,MAAM,OAAK,OAAE,kBAAF,mBAAiB,UAAS,CAAC,CAAC;AACrD,YAAM,aAAa,MAAM,OAAO,CAAC,SAAS,KAAK,KAAK,WAAW,QAAQ,CAAC;AAExE,UAAI,WAAW,WAAW;AAAG;AAE7B,QAAE,eAAe;AAEjB,YAAM,gBAA+C,WAAW,IAAI,CAAC,SAAS;AAC5E,cAAM,OAAO,KAAK,UAAU;AAC5B,YAAI,CAAC;AAAM,iBAAO,QAAQ,QAAQ,IAAI;AAEtC,eAAO,IAAI,QAA4B,CAAC,SAAS,WAAW;AAC1D,gBAAM,SAAS,IAAI,WAAW;AAC9B,iBAAO,SAAS,CAACC,OAAM;AA5VjC,gBAAAC,KAAAC;AA6VY,kBAAM,gBAAgBA,OAAAD,MAAAD,GAAE,WAAF,gBAAAC,IAAU,WAAV,gBAAAC,IAA6B,MAAM,KAAK;AAC9D,gBAAI,cAAc;AAChB,sBAAQ;AAAA,gBACN,aAAa,KAAK;AAAA,gBAClB,OAAO;AAAA,cACT,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ,IAAI;AAAA,YACd;AAAA,UACF;AACA,iBAAO,UAAU;AACjB,iBAAO,cAAc,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAED,UAAI;AACF,cAAM,gBAAgB,MAAM,QAAQ,IAAI,aAAa,GAAG,OAAO,CAAC,QAAQ,QAAQ,IAAI;AACpF,0BAAkB,CAAC,SAAS,CAAC,GAAG,MAAM,GAAG,YAAY,CAAC;AAAA,MACxD,SAAS,OAAP;AAEA,gBAAQ,MAAM,mCAAmC,KAAK;AAAA,MACxD;AAAA,IACF;AAEA,aAAS,iBAAiB,SAAS,WAAW;AAC9C,WAAO,MAAM,SAAS,oBAAoB,SAAS,WAAW;AAAA,EAChE,GAAG,CAAC,mBAAmB,CAAC;AAExB,YAAU,MAAM;AACd,QAAI,EAAC,iEAAwB,SAAQ;AACnC,0BAAoB,gBAAgB,EAAE;AACtC;AAAA,IACF;AAUA,UAAM,iCAAiC;AAAA,MACrC;AAAA,MACA;AAAA,MACA,GAAG,uBAAuB,IAAI,CAAC,gBAAgB,KAAK,aAAa;AAAA,IACnE;AAEA,YAAQ,IAAI,kCAAkC,8BAA8B;AAE5E,wBAAoB,+BAA+B,KAAK,IAAI,KAAK,EAAE;AAAA,EACrE,GAAG,CAAC,cAAc,sBAAsB,CAAC;AAEzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAGA,QAAM,oBAAoB,CAAC,SAAiB;AAC1C,UAAM,SAAS;AACf,sBAAkB,CAAC,CAAC;AACpB,QAAI,aAAa,SAAS;AACxB,mBAAa,QAAQ,QAAQ;AAAA,IAC/B;AAEA,WAAO,YAAY,MAAM,MAAM;AAAA,EACjC;AAEA,QAAM,cAAc,MAAM,WAAW,WAAW;AAChD,QAAM,YAAY,cAAc,YAAY,OAAO;AAEnD,QAAM,mBAAmB,CAAC,cAAsB;AAC9C,QAAI,cAAc;AAChB,mBAAa,SAAS;AAAA,IACxB;AAEA,mBAAe,SAAS;AAAA,EAC1B;AAEA,QAAM,aAAa,CAAC,YAAoB;AACtC,QAAI,QAAQ;AACV,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,QAAM,oBAAoB,CAAO,UAA+C;AAC9E,QAAI,CAAC,MAAM,OAAO,SAAS,MAAM,OAAO,MAAM,WAAW,GAAG;AAC1D;AAAA,IACF;AAEA,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO,KAAK,EAAE,OAAO,CAAC,SAAS,KAAK,KAAK,WAAW,QAAQ,CAAC;AAC5F,QAAI,MAAM,WAAW;AAAG;AAExB,UAAM,mBAAmB,MAAM,IAAI,CAAC,SAAS;AAC3C,aAAO,IAAI,QAAgD,CAAC,SAAS,WAAW;AAC9E,cAAM,SAAS,IAAI,WAAW;AAC9B,eAAO,SAAS,CAAC,MAAM;AAxc/B;AAycU,gBAAM,iBAAgB,aAAE,WAAF,mBAAU,WAAV,mBAA6B,MAAM,KAAK,OAAM;AACpE,cAAI,cAAc;AAChB,oBAAQ;AAAA,cACN,aAAa,KAAK;AAAA,cAClB,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,UAAU;AACjB,eAAO,cAAc,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAED,QAAI;AACF,YAAM,eAAe,MAAM,QAAQ,IAAI,gBAAgB;AACvD,wBAAkB,CAAC,SAAS,CAAC,GAAG,MAAM,GAAG,YAAY,CAAC;AAAA,IACxD,SAAS,OAAP;AAEA,cAAQ,MAAM,wBAAwB,KAAK;AAAA,IAC7C;AAAA,EACF;AAEA,QAAM,sBAAsB,CAAC,UAAkB;AAC7C,sBAAkB,CAAC,SAAS,KAAK,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EAChE;AAEA,SACE,qBAAC,sBAAmB,OAAc,QAAgB,WAChD;AAAA;AAAA,MAACX;AAAA,MAAA;AAAA,QACC,kBAAkBO;AAAA,QAClB,aAAaC;AAAA,QACb,mBAAmBP;AAAA,QACnB,8BAA8BC;AAAA,QAC9B,yBAAyBC;AAAA,QACzB,qBAAqBC;AAAA,QACrB,oBAAoBC;AAAA,QACpB,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QAEC,6BAAmB,SAAS,KAC3B;AAAA,UAAC;AAAA;AAAA,YACC,mBAAmB;AAAA,YACnB,aAAa;AAAA;AAAA,QACf;AAAA;AAAA,IAEJ;AAAA,IAEC,uBACC,iCACE;AAAA,0BAAC,oBAAiB,QAAQ,gBAAgB,eAAe,qBAAqB;AAAA,MAC9E;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,UAAQ;AAAA,UACR,KAAK;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO,EAAE,SAAS,OAAO;AAAA;AAAA,MAC3B;AAAA,OACF;AAAA,IAGF;AAAA,MAACC;AAAA,MAAA;AAAA,QACC,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,QACR,UAAU,sBAAsB,MAAG;AAhhB3C;AAghB8C,oCAAa,YAAb,mBAAsB;AAAA,YAAU;AAAA;AAAA,IACxE;AAAA,KACF;AAEJ;AAEO,SAAS,mBAAmB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,QAAM,cAAc,MAAM,WAAW,WAAW;AAChD,MAAI,CAAC,aAAa;AAChB,WACE,oBAAC,uBAAoB,OAAc,QAAgB,MAAM,MAAM,SAAS,MAAM;AAAA,IAAC,GAC7E,8BAAC,SAAI,WAAW,kBAAkB,gCAAa,MAAO,UAAS,GACjE;AAAA,EAEJ;AACA,SAAO,gCAAG,UAAS;AACrB;AAEA,IAAM,+BAA+B;AAE9B,IAAM,sBAAsB,CACjC,mBACA,cACA,iBACA,kBACA,qBACG;AApjBL;AAqjBE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AAAA,IACjB,IAAI,SAAS;AAAA,IACb;AAAA,EACF,CAAC;AAED,QAAM,CAAC,oBAAoB,qBAAqB,IAAI,SAAkC,CAAC,CAAC;AACxF,QAAM,gCAAgC,OAA+B,IAAI;AACzE,QAAM,mBAAmB,OAAY;AAErC,QAAM,mBAAmB,MAAM;AArkBjC,QAAAI;AAskBI,KAAAA,MAAA,8BAA8B,YAA9B,gBAAAA,IAAuC;AACvC,kCAA8B,UAAU;AAAA,EAC1C;AAEA,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,kBAAkB,0BAA0B;AAClD,QAAM,UAAU,kCAAK,iBAAmB;AAExC,YAAU,MAAM;AACd,iDAAe;AAEf,qBAAiB;AAEjB,qBAAiB,UAAU;AAAA,MACzB,MAAM;AACJ,YAAI,CAAC,aAAa,OAAO,KAAK,QAAQ,2BAA2B,EAAE,WAAW,GAAG;AAC/E,wCAA8B,UAAU,IAAI,gBAAgB;AAC5D;AAAA,YACE;AAAA,YACA,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,mBAAmB,UAAU,IAAI,IAAI;AAAA,IACvC;AAEA,WAAO,MAAM;AACX,mBAAa,iBAAiB,OAAO;AAAA,IACvC;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA,QAAQ;AAAA;AAAA;AAAA,IAGR,gBAAgB,UAAU;AAAA,EAC5B,CAAC;AAED,QAAM,cAAc,CAClB,gBACA,gBACG;AAEH,UAAM,SAAS,eAAe,CAAC;AAE/B,qBAAiB;AACjB,0BAAsB,CAAC,CAAC;AAExB,QAAI,eAA+B;AAGnC,QAAI,eAAe,KAAK,EAAE,SAAS,GAAG;AACpC,YAAM,cAAc,IAAI,YAAY;AAAA,QAClC,SAAS;AAAA,QACT,MAAM,KAAK;AAAA,MACb,CAAC;AAED,UAAI,iBAAiB;AACnB,YAAI;AAEF,gBAAM,gBAAgB,cAAc;AAAA,QACtC,SAAS,OAAP;AACA,kBAAQ,MAAM,6BAA6B,KAAK;AAAA,QAClD;AAAA,MACF;AAEA,YAAM,cAAc,aAAa,EAAE,UAAU,OAAO,WAAW,EAAE,CAAC;AAElE,UAAI,CAAC,cAAc;AACjB,uBAAe;AAAA,MACjB;AAAA,IACF;AAGA,QAAI,OAAO,SAAS,GAAG;AACrB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,eAAe,IAAI,aAAa;AAAA,UACpC,QAAQ,OAAO,CAAC,EAAE,YAAY,QAAQ,UAAU,EAAE;AAAA,UAClD,OAAO,OAAO,CAAC,EAAE;AAAA,UACjB,MAAM,KAAK;AAAA,QACb,CAAC;AACD,cAAM,cAAc,cAAc,EAAE,UAAU,MAAM,OAAO,SAAS,EAAE,CAAC;AACvE,YAAI,CAAC,cAAc;AACjB,yBAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,cAAc;AAEjB,aAAO,IAAI,YAAY,EAAE,SAAS,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,IACzD;AAIA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW;AACjB,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,oBAAmB,oBAAe,iBAAf,mBAA6B;AACtD,QAAM,sBAAsB,CAAO,SAAwB;AACzD,QAAI,eAAe,cAAc;AAC/B,qBAAe,gBAAgB,iCAC1B,eAAe,eADW;AAAA,QAE7B,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,EAAC;AACD,qBAAe,iBAAiB,CAAC,oBAAoB;AACnD,eAAO,iCACF,kBADE;AAAA,UAEL,CAAC,eAAe,aAAc,SAAS,GAAG,iCACrC,gBAAgB,eAAe,aAAc,SAAS,IADjB;AAAA,YAExC,UAAU;AAAA,YACV,UAAU;AAAA,YACV,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,kBAAkB,CAAO,SAAwB;AACrD,QAAI,eAAe,cAAc;AAC/B,YAAM;AAAA,QACJ,eAAe,aAAa;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,eAAe,cAAc;AAC/B,gBAAU,eAAe,aAAa,WAAW,OAAO;AAAA,IAC1D;AAAA,EACF;AACA,QAAM,uBAAuB,CAAC,UAAe;AAC3C,QAAI,eAAe,cAAc;AAC/B,qBAAe,iBAAiB,CAAC,oBAAoB;AACnD,eAAO,iCACF,kBADE;AAAA,UAEL,CAAC,eAAe,aAAc,SAAS,GAAG;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS,iBAAiB;AACxB,QAAI,kBAAkB;AACpB,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,4BAAsB;AAAA,IACxB;AAAA,EACF;AACA,WAAS,eAAe,WAAmB;AACzC,QAAI,kBAAkB;AACpB,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,4BAAsB,SAAS;AAAA,IACjC;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["Messages", "RenderTextMessage", "RenderActionExecutionMessage", "RenderAgentStateMessage", "RenderResultMessage", "RenderImageMessage", "Input", "AssistantM<PERSON><PERSON>", "UserMessage", "e", "_a", "_b"]}