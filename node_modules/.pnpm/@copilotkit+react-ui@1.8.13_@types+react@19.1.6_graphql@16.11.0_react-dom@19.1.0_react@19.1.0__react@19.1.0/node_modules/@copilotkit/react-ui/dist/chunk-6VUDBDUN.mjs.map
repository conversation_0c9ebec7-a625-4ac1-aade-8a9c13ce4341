{"version": 3, "sources": ["../src/components/chat/messages/RenderResultMessage.tsx"], "sourcesContent": ["import { RenderMessageProps } from \"../props\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderResultMessage({\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const { message, inProgress, index, isCurrentMessage } = props;\n\n  if (message.isResultMessage() && inProgress && isCurrentMessage) {\n    return (\n      <AssistantMessage\n        key={index}\n        data-message-role=\"assistant\"\n        rawData={message}\n        isLoading={true}\n        isGenerating={true}\n      />\n    );\n  }\n\n  // Avoid 'Nothing was returned from render' React error\n  else {\n    return null;\n  }\n}\n"], "mappings": ";;;;;;;;AAWM;AARC,SAAS,oBAAoB,IAGb;AAHa,eAClC;AAAA,sBAAAA,oBAAmB;AAAA,EAJrB,IAGoC,IAE/B,kBAF+B,IAE/B;AAAA,IADH;AAAA;AAGA,QAAM,EAAE,SAAS,YAAY,OAAO,iBAAiB,IAAI;AAEzD,MAAI,QAAQ,gBAAgB,KAAK,cAAc,kBAAkB;AAC/D,WACE;AAAA,MAACA;AAAA,MAAA;AAAA,QAEC,qBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,cAAc;AAAA;AAAA,MAJT;AAAA,IAKP;AAAA,EAEJ,OAGK;AACH,WAAO;AAAA,EACT;AACF;", "names": ["AssistantM<PERSON><PERSON>"]}