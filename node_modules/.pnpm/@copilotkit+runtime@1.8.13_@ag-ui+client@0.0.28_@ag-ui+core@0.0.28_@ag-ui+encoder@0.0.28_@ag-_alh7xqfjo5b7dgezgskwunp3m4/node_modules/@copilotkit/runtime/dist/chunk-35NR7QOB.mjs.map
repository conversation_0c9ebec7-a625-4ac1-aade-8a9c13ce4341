{"version": 3, "sources": ["../src/lib/integrations/nest/index.ts"], "sourcesContent": ["import { CreateCopilotRuntimeServerOptions } from \"../shared\";\nimport { copilotRuntimeNodeHttpEndpoint } from \"../node-http\";\nimport telemetry, { getRuntimeInstanceTelemetryInfo } from \"../../telemetry-client\";\n\nexport function copilotRuntimeNestEndpoint(options: CreateCopilotRuntimeServerOptions) {\n  telemetry.setGlobalProperties({\n    runtime: {\n      framework: \"nest\",\n    },\n  });\n\n  telemetry.capture(\n    \"oss.runtime.instance_created\",\n    getRuntimeInstanceTelemetryInfo(options.runtime),\n  );\n  return copilotRuntimeNodeHttpEndpoint(options);\n}\n"], "mappings": ";;;;;;;;;;AAIO,SAASA,2BAA2BC,SAA0C;AACnFC,2BAAUC,oBAAoB;IAC5BC,SAAS;MACPC,WAAW;IACb;EACF,CAAA;AAEAH,2BAAUI,QACR,gCACAC,gCAAgCN,QAAQG,OAAO,CAAA;AAEjD,SAAOI,+BAA+BP,OAAAA;AACxC;AAZgBD;", "names": ["copilotRuntimeNestEndpoint", "options", "telemetry", "setGlobalProperties", "runtime", "framework", "capture", "getRuntimeInstanceTelemetryInfo", "copilotRuntimeNodeHttpEndpoint"]}