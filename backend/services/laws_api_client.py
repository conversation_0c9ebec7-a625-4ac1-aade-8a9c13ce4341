"""
Laws-API Client for Backend Services

This module provides a Python client for the laws-API service,
designed for use in backend agents and services.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

import aiohttp
from aiohttp import ClientTimeout, ClientSession

logger = logging.getLogger(__name__)


class DocumentType(str, Enum):
    STATUTE = 'statute'
    CASE_LAW = 'case_law'
    REGULATION = 'regulation'
    CONSTITUTIONAL = 'constitutional'
    ADMINISTRATIVE = 'administrative'
    MUNICIPAL = 'municipal'


class PracticeArea(str, Enum):
    PERSONAL_INJURY = 'personal_injury'
    CONTRACT = 'contract'
    CRIMINAL = 'criminal'
    FAMILY = 'family'
    EMPLOYMENT = 'employment'
    REAL_ESTATE = 'real_estate'
    INTELLECTUAL_PROPERTY = 'intellectual_property'
    CORPORATE = 'corporate'
    TAX = 'tax'
    IMMIGRATION = 'immigration'


class EntityType(str, Enum):
    STATUTE = 'statute'
    CASE = 'case'
    COURT = 'court'
    JUDGE = 'judge'
    ATTORNEY = 'attorney'
    CONCEPT = 'concept'
    CITATION = 'citation'


class RelationshipType(str, Enum):
    CITES = 'cites'
    CITED_BY = 'cited_by'
    OVERRULES = 'overrules'
    OVERRULED_BY = 'overruled_by'
    DISTINGUISHES = 'distinguishes'
    FOLLOWS = 'follows'
    RELATED_TO = 'related_to'
    SUPERSEDES = 'supersedes'
    SUPERSEDED_BY = 'superseded_by'


@dataclass
class SearchRequest:
    query: str
    jurisdiction: Optional[List[str]] = None
    document_type: Optional[List[DocumentType]] = None
    limit: Optional[int] = None
    offset: Optional[int] = None
    filters: Optional[Dict[str, Any]] = None


@dataclass
class SearchResult:
    id: str
    title: str
    content: str
    document_type: DocumentType
    jurisdiction: str
    relevance_score: float
    highlights: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    citation: Optional[str] = None
    url: Optional[str] = None


@dataclass
class RecommendRequest:
    document_id: Optional[str] = None
    content: Optional[str] = None
    context: Optional[str] = None
    jurisdiction: Optional[List[str]] = None
    limit: Optional[int] = None
    similarity_threshold: Optional[float] = None


@dataclass
class RecommendationResult:
    id: str
    title: str
    content: str
    document_type: DocumentType
    jurisdiction: str
    similarity_score: float
    relationship_type: RelationshipType
    explanation: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class GraphRequest:
    entity_id: Optional[str] = None
    entity_type: Optional[EntityType] = None
    relationship_types: Optional[List[RelationshipType]] = None
    depth: Optional[int] = None
    limit: Optional[int] = None


@dataclass
class GraphNode:
    id: str
    label: str
    type: EntityType
    properties: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class GraphEdge:
    source: str
    target: str
    relationship: RelationshipType
    weight: Optional[float] = None
    properties: Optional[Dict[str, Any]] = None


@dataclass
class GraphResult:
    nodes: List[GraphNode]
    edges: List[GraphEdge]
    metadata: Optional[Dict[str, Any]] = None


class LawsApiException(Exception):
    """Exception raised by the Laws-API client."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, retry_after: Optional[int] = None):
        super().__init__(message)
        self.status_code = status_code
        self.retry_after = retry_after


class LawsApiClient:
    """
    Async client for the laws-API service.
    """
    
    def __init__(
        self,
        base_url: str = "https://legal-api-stg-gfunh6mfpa-uc.a.run.app",
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        self.base_url = base_url.rstrip('/')
        self.timeout = ClientTimeout(total=timeout)
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._session: Optional[ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._session = ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()
    
    async def _get_session(self) -> ClientSession:
        """Get or create an aiohttp session."""
        if not self._session:
            self._session = ClientSession(timeout=self.timeout)
        return self._session
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        auth_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make an HTTP request to the laws-API."""
        url = f"{self.base_url}{endpoint}"
        
        # Prepare headers
        request_headers = {
            'Content-Type': 'application/json',
            **(headers or {})
        }
        
        if auth_token:
            request_headers['Authorization'] = f'Bearer {auth_token}'
        
        session = await self._get_session()
        
        for attempt in range(self.max_retries + 1):
            try:
                async with session.request(
                    method,
                    url,
                    json=data,
                    headers=request_headers
                ) as response:
                    
                    # Handle rate limiting
                    if response.status == 429:
                        retry_after = int(response.headers.get('Retry-After', self.retry_delay))
                        if attempt < self.max_retries:
                            logger.warning(f"Rate limited, retrying after {retry_after}s")
                            await asyncio.sleep(retry_after)
                            continue
                        else:
                            raise LawsApiException(
                                "Rate limit exceeded",
                                status_code=429,
                                retry_after=retry_after
                            )
                    
                    # Handle other HTTP errors
                    if not response.ok:
                        error_text = await response.text()
                        raise LawsApiException(
                            f"HTTP {response.status}: {error_text}",
                            status_code=response.status
                        )
                    
                    return await response.json()
                    
            except aiohttp.ClientError as e:
                if attempt < self.max_retries:
                    logger.warning(f"Request failed, retrying: {e}")
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    raise LawsApiException(f"Request failed after {self.max_retries} retries: {e}")
        
        raise LawsApiException("Unexpected error in request loop")
    
    async def search(
        self,
        request: SearchRequest,
        auth_token: Optional[str] = None
    ) -> List[SearchResult]:
        """Search for legal documents."""
        data = {
            'query': request.query,
            'jurisdiction': request.jurisdiction or ['texas'],
            'limit': request.limit or 10,
            'offset': request.offset or 0
        }
        
        if request.document_type:
            data['document_type'] = [dt.value for dt in request.document_type]
        
        if request.filters:
            data['filters'] = request.filters
        
        response = await self._make_request(
            'POST',
            '/v0/search',
            data=data,
            auth_token=auth_token
        )
        
        if not response.get('success'):
            raise LawsApiException(response.get('message', 'Search failed'))
        
        results = []
        for item in response.get('data', []):
            results.append(SearchResult(
                id=item['id'],
                title=item['title'],
                content=item['content'],
                document_type=DocumentType(item['document_type']),
                jurisdiction=item['jurisdiction'],
                relevance_score=item['relevance_score'],
                highlights=item.get('highlights'),
                metadata=item.get('metadata'),
                citation=item.get('citation'),
                url=item.get('url')
            ))
        
        return results
    
    async def recommend(
        self,
        request: RecommendRequest,
        auth_token: Optional[str] = None
    ) -> List[RecommendationResult]:
        """Get document recommendations."""
        data = {
            'jurisdiction': request.jurisdiction or ['texas'],
            'limit': request.limit or 5,
            'similarity_threshold': request.similarity_threshold or 0.7
        }
        
        if request.document_id:
            data['document_id'] = request.document_id
        if request.content:
            data['content'] = request.content
        if request.context:
            data['context'] = request.context
        
        response = await self._make_request(
            'POST',
            '/v0/recommend',
            data=data,
            auth_token=auth_token
        )
        
        if not response.get('success'):
            raise LawsApiException(response.get('message', 'Recommendation failed'))
        
        results = []
        for item in response.get('data', []):
            results.append(RecommendationResult(
                id=item['id'],
                title=item['title'],
                content=item['content'],
                document_type=DocumentType(item['document_type']),
                jurisdiction=item['jurisdiction'],
                similarity_score=item['similarity_score'],
                relationship_type=RelationshipType(item['relationship_type']),
                explanation=item.get('explanation'),
                metadata=item.get('metadata')
            ))
        
        return results
    
    async def graph_query(
        self,
        request: GraphRequest,
        auth_token: Optional[str] = None
    ) -> GraphResult:
        """Query the legal knowledge graph."""
        data = {
            'depth': request.depth or 2,
            'limit': request.limit or 50
        }
        
        if request.entity_id:
            data['entity_id'] = request.entity_id
        if request.entity_type:
            data['entity_type'] = request.entity_type.value
        if request.relationship_types:
            data['relationship_types'] = [rt.value for rt in request.relationship_types]
        
        response = await self._make_request(
            'POST',
            '/v0/graph',
            data=data,
            auth_token=auth_token
        )
        
        if not response.get('success'):
            raise LawsApiException(response.get('message', 'Graph query failed'))
        
        graph_data = response.get('data', {})
        
        nodes = []
        for node_data in graph_data.get('nodes', []):
            nodes.append(GraphNode(
                id=node_data['id'],
                label=node_data['label'],
                type=EntityType(node_data['type']),
                properties=node_data['properties'],
                metadata=node_data.get('metadata')
            ))
        
        edges = []
        for edge_data in graph_data.get('edges', []):
            edges.append(GraphEdge(
                source=edge_data['source'],
                target=edge_data['target'],
                relationship=RelationshipType(edge_data['relationship']),
                weight=edge_data.get('weight'),
                properties=edge_data.get('properties')
            ))
        
        return GraphResult(
            nodes=nodes,
            edges=edges,
            metadata=graph_data.get('metadata')
        )
