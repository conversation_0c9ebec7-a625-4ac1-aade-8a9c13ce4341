{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["./frontend/src/*"], "@/components/*": ["./frontend/src/components/*"], "@/lib/*": ["./frontend/src/lib/*"], "@/hooks/*": ["./frontend/src/hooks/*"], "@/types/*": ["./frontend/src/types/*"], "@/app/*": ["./frontend/src/app/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "frontend/**/*.ts", "frontend/**/*.tsx", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "frontend/node_modules", ".next", "frontend/.next"]}