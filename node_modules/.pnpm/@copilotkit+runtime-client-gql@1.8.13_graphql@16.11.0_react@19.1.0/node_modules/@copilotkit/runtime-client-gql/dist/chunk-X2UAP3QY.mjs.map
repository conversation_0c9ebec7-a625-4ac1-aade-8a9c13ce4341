{"version": 3, "sources": ["../src/graphql/definitions/queries.ts"], "sourcesContent": ["import { graphql } from \"../@generated/gql\";\n\nexport const getAvailableAgentsQuery = graphql(/** GraphQL **/ `\n  query availableAgents {\n    availableAgents {\n      agents {\n        name\n        id\n        description\n      }\n    }\n  }\n`);\n\nexport const loadAgentStateQuery = graphql(/** GraphQL **/ `\n  query loadAgentState($data: LoadAgentStateInput!) {\n    loadAgentState(data: $data) {\n      threadId\n      threadExists\n      state\n      messages\n    }\n  }\n`);\n"], "mappings": ";;;;;AAEO,IAAM,0BAA0B;AAAA;AAAA,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU9D;AAEM,IAAM,sBAAsB;AAAA;AAAA,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAS1D;", "names": []}