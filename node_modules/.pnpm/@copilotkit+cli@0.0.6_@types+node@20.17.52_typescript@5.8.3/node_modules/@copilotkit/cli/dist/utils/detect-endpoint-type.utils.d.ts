declare enum RemoteEndpointType {
    LangGraphPlatform = "LangGraphPlatform",
    CopilotKit = "CopilotKit",
    Invalid = "Invalid"
}
declare const getHumanReadableEndpointType: (type: RemoteEndpointType) => "CopilotKit" | "Invalid" | "LangGraph Platform";
declare function detectRemoteEndpointType(url: string): Promise<{
    url: string;
    type: RemoteEndpointType;
    humanReadableType: string;
}>;

export { RemoteEndpointType, detectRemoteEndpointType, getHumanReadableEndpointType };
