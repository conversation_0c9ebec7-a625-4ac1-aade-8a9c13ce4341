# Authentication API Reference

## 🎣 **Client-Side Hooks**

### **useAuth()**

Main authentication hook providing access to user state and authentication methods.

```typescript
const {
  user,           // Supabase User object
  session,        // Supabase Session object
  profile,        // User profile from database
  isLoading,      // Loading state
  isAuthenticated, // Authentication status
  signIn,         // Sign in function
  signOut,        // Sign out function
  signUp,         // Sign up function
  resetPassword,  // Password reset function
  updatePassword, // Password update function
  refreshSession  // Manual session refresh
} = useAuth();
```

**Returns**:
- `user: User | null` - Supabase user object
- `session: Session | null` - Current session
- `profile: UserProfile | null` - User profile data
- `isLoading: boolean` - Loading state
- `isAuthenticated: boolean` - Authentication status
- `signIn: (email: string, password: string) => Promise<{ error?: Error }>` - Sign in method
- `signOut: () => Promise<void>` - Sign out method
- `signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ error?: Error }>` - Sign up method
- `resetPassword: (email: string) => Promise<{ error?: Error }>` - Password reset
- `updatePassword: (password: string) => Promise<{ error?: Error }>` - Password update
- `refreshSession: () => Promise<void>` - Session refresh

**Example**:
```typescript
function LoginForm() {
  const { signIn, isLoading } = useAuth();
  
  const handleSubmit = async (email: string, password: string) => {
    const { error } = await signIn(email, password);
    if (error) {
      console.error('Login failed:', error);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
    </form>
  );
}
```

---

### **useRequireAuth(redirectTo?)**

Hook that automatically redirects unauthenticated users.

**Parameters**:
- `redirectTo?: string` - Redirect path (default: '/auth/login')

**Returns**:
- `isLoading: boolean` - Loading state
- `isAuthenticated: boolean` - Authentication status

**Example**:
```typescript
function ProtectedPage() {
  const { isLoading } = useRequireAuth('/custom-login');
  
  if (isLoading) return <div>Loading...</div>;
  
  return <div>Protected content</div>;
}
```

---

### **useRequireRole(allowedRoles)**

Hook that enforces role-based access control.

**Parameters**:
- `allowedRoles: string[]` - Array of allowed role names

**Returns**:
- `isLoading: boolean` - Loading state
- `isAuthorized: boolean` - Authorization status

**Example**:
```typescript
function AdminPanel() {
  const { isLoading, isAuthorized } = useRequireRole(['admin', 'partner']);
  
  if (isLoading) return <div>Loading...</div>;
  if (!isAuthorized) return <div>Access denied</div>;
  
  return <div>Admin content</div>;
}
```

---

## 🖥️ **Server-Side Functions**

### **requireAuth(request)**

Enhanced authentication function for API routes.

**Parameters**:
- `request: NextRequest` - Next.js request object

**Returns**:
```typescript
Promise<{
  success: true;
  user: AuthUser;
  supabase: SupabaseClient;
} | {
  success: false;
  error: string;
  status: number;
}>
```

**Example**:
```typescript
export async function GET(request: NextRequest) {
  const authResult = await requireAuth(request);
  
  if (!authResult.success) {
    return NextResponse.json(
      { error: authResult.error }, 
      { status: authResult.status }
    );
  }
  
  const { user, supabase } = authResult;
  // Use authenticated user and pre-configured supabase client
}
```

---

### **createClient()**

Create a Supabase client for server-side operations.

**Returns**: `SupabaseClient` - Configured Supabase client

**Example**:
```typescript
import { createClient } from '@/lib/auth/server';

export async function GET() {
  const supabase = createClient();
  const { data } = await supabase
    .schema('tenants')
    .from('cases')
    .select('*');
    
  return NextResponse.json(data);
}
```

---

### **hasRole(user, roles)**

Check if user has any of the specified roles.

**Parameters**:
- `user: AuthUser | null` - User object
- `roles: UserRole[]` - Array of roles to check

**Returns**: `boolean` - True if user has any of the roles

**Example**:
```typescript
import { hasRole } from '@/lib/auth/server';

export async function DELETE(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  if (!hasRole(authResult.user, ['admin', 'partner'])) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  // Proceed with deletion
}
```

---

## 🔐 **JWT Utilities**

### **extractJwtFromRequest(request)**

Extract JWT token from Authorization header.

**Parameters**:
- `request: NextRequest` - Next.js request object

**Returns**: `string | undefined` - JWT token or undefined

**Example**:
```typescript
import { extractJwtFromRequest } from '@/lib/auth/jwt-utils';

export async function GET(request: NextRequest) {
  const token = extractJwtFromRequest(request);
  if (!token) {
    return NextResponse.json({ error: 'No token provided' }, { status: 401 });
  }
  
  // Use token for validation
}
```

---

### **validateJwt(token)**

Validate JWT token and extract payload.

**Parameters**:
- `token: string` - JWT token to validate

**Returns**: `Promise<JWTPayload>` - Decoded JWT payload

**Throws**: `JWTValidationError` - If token is invalid

**Example**:
```typescript
import { validateJwt, JWTValidationError } from '@/lib/auth/jwt-utils';

try {
  const payload = await validateJwt(token);
  console.log('User ID:', payload.sub);
  console.log('Tenant ID:', payload.tenant_id);
} catch (error) {
  if (error instanceof JWTValidationError) {
    console.error('Invalid token:', error.message);
  }
}
```

---

### **extractOrganizationId(payload)**

Extract organization/tenant ID from JWT payload.

**Parameters**:
- `payload: JWTPayload` - JWT payload object

**Returns**: `string | undefined` - Organization ID or undefined

**Example**:
```typescript
const payload = await validateJwt(token);
const orgId = extractOrganizationId(payload);
if (orgId) {
  // Use organization ID for tenant isolation
}
```

---

### **extractUserId(payload)**

Extract user ID from JWT payload.

**Parameters**:
- `payload: JWTPayload` - JWT payload object

**Returns**: `string` - User ID (subject)

**Example**:
```typescript
const payload = await validateJwt(token);
const userId = extractUserId(payload);
console.log('User ID:', userId);
```

---

## 🛡️ **Permission Functions**

### **checkPermission(user, permission, resourceType?, resourceId?)**

Check if user has a specific permission.

**Parameters**:
- `user: AuthUser | null` - User object
- `permission: string` - Permission to check (e.g., 'read:cases')
- `resourceType?: string` - Resource type for resource-specific permissions
- `resourceId?: string` - Resource ID for resource-specific permissions

**Returns**: `boolean` - True if user has permission

**Example**:
```typescript
import { checkPermission } from '@/lib/auth/permissions';

if (checkPermission(user, 'write:cases')) {
  // User can create/edit cases
}

if (checkPermission(user, 'read:own_case', 'case', caseId)) {
  // Client can read their own case
}
```

---

### **getUserPermissions(user)**

Get all permissions for a user.

**Parameters**:
- `user: AuthUser | null` - User object

**Returns**: `string[]` - Array of permission strings

**Example**:
```typescript
const permissions = getUserPermissions(user);
console.log('User permissions:', permissions);
// Output: ['read:cases', 'write:cases', 'read:clients', ...]
```

---

### **canAccessTenant(user, tenantId)**

Check if user can access a specific tenant's data.

**Parameters**:
- `user: AuthUser | null` - User object
- `tenantId: string` - Tenant ID to check

**Returns**: `boolean` - True if user can access tenant

**Example**:
```typescript
if (!canAccessTenant(user, requestedTenantId)) {
  return NextResponse.json({ error: 'Access denied' }, { status: 403 });
}
```

---

### **isSuperAdmin(user)**

Check if user is a super administrator.

**Parameters**:
- `user: AuthUser | null` - User object

**Returns**: `boolean` - True if user is super admin

**Example**:
```typescript
if (isSuperAdmin(user)) {
  // User has full system access
}
```

---

## 🏷️ **TypeScript Types**

### **AuthUser**

```typescript
interface AuthUser {
  id: string;
  email?: string;
  role: UserRole;
  tenantId?: string;
  metadata: Record<string, any>;
}
```

### **UserProfile**

```typescript
interface UserProfile {
  id: string;
  auth_id: string;
  email: string;
  role: string;
  tenant_id: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at: string;
}
```

### **UserRole**

```typescript
enum UserRole {
  Superadmin = 'superadmin',
  Partner = 'partner',
  Attorney = 'attorney',
  Paralegal = 'paralegal',
  Staff = 'staff',
  Client = 'client'
}
```

### **JWTPayload**

```typescript
interface JWTPayload {
  sub: string;           // User ID
  iat: number;           // Issued at
  exp: number;           // Expiration time
  tenant_id?: string;    // Tenant ID
  org_id?: string;       // Alternative org ID
  user_metadata?: {
    organization_id?: string;
    role?: string;
  };
  [key: string]: any;    // Additional claims
}
```

### **AuthContextType**

```typescript
interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: Error }>;
  signOut: () => Promise<void>;
  signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ error?: Error }>;
  resetPassword: (email: string) => Promise<{ error?: Error }>;
  updatePassword: (password: string) => Promise<{ error?: Error }>;
  refreshSession: () => Promise<void>;
}
```

---

## ⚠️ **Error Types**

### **JWTValidationError**

```typescript
class JWTValidationError extends Error {
  status: number;
  
  constructor(message: string, status = 401) {
    super(message);
    this.name = 'JWTValidationError';
    this.status = status;
  }
}
```

**Usage**:
```typescript
try {
  await validateJwt(token);
} catch (error) {
  if (error instanceof JWTValidationError) {
    return NextResponse.json(
      { error: error.message }, 
      { status: error.status }
    );
  }
}
```
