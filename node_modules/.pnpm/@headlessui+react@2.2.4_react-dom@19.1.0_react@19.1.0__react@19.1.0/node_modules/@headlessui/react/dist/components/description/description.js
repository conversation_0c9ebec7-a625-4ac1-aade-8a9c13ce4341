"use client";import m,{createContext as T,useContext as u,useMemo as c,useState as P}from"react";import{useEvent as g}from'../../hooks/use-event.js';import{useId as x}from'../../hooks/use-id.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as E}from'../../hooks/use-sync-refs.js';import{useDisabled as v}from'../../internal/disabled.js';import{forwardRefWithAs as R,useRender as I}from'../../utils/render.js';let a=T(null);a.displayName="DescriptionContext";function f(){let r=u(a);if(r===null){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,f),e}return r}function U(){var r,e;return(e=(r=u(a))==null?void 0:r.value)!=null?e:void 0}function w(){let[r,e]=P([]);return[r.length>0?r.join(" "):void 0,c(()=>function(t){let i=g(n=>(e(s=>[...s,n]),()=>e(s=>{let o=s.slice(),p=o.indexOf(n);return p!==-1&&o.splice(p,1),o}))),l=c(()=>({register:i,slot:t.slot,name:t.name,props:t.props,value:t.value}),[i,t.slot,t.name,t.props,t.value]);return m.createElement(a.Provider,{value:l},t.children)},[e])]}let S="p";function C(r,e){let d=x(),t=v(),{id:i=`headlessui-description-${d}`,...l}=r,n=f(),s=E(e);y(()=>n.register(i),[i,n.register]);let o=t||!1,p=c(()=>({...n.slot,disabled:o}),[n.slot,o]),D={ref:s,...n.props,id:i};return I()({ourProps:D,theirProps:l,slot:p,defaultTag:S,name:n.name||"Description"})}let _=R(C),H=Object.assign(_,{});export{H as Description,U as useDescribedBy,w as useDescriptions};
