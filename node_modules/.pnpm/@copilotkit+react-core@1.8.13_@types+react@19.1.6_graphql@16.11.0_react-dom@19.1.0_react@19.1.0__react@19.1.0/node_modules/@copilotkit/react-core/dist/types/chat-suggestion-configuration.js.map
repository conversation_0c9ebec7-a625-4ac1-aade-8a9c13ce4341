{"version": 3, "sources": ["../../src/types/chat-suggestion-configuration.ts"], "sourcesContent": ["export interface CopilotChatSuggestionConfiguration {\n  /**\n   * A prompt or instructions for the GPT to generate suggestions.\n   */\n  instructions: string;\n\n  /**\n   * The minimum number of suggestions to generate. Defaults to `1`.\n   * @default 1\n   */\n  minSuggestions?: number;\n\n  /**\n   * The maximum number of suggestions to generate. Defaults to `3`.\n   * @default 1\n   */\n  maxSuggestions?: number;\n\n  /**\n   * An optional class name to apply to the suggestions.\n   */\n  className?: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}