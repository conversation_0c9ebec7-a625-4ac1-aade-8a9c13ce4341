# @copilotkit/cli

CopilotKit CLI

[![Version](https://img.shields.io/npm/v/@copilotkit/cli.svg)](https://npmjs.org/package/@copilotkit/cli)
[![Downloads/week](https://img.shields.io/npm/dw/@copilotkit/cli.svg)](https://npmjs.org/package/@copilotkit/cli)

<!-- toc -->
* [@copilotkit/cli](#copilotkitcli)
* [Usage](#usage)
<!-- tocstop -->

# Usage

<!-- usage -->
```sh-session
$ npm install -g @copilotkit/cli
$ copilotkit COMMAND
running command...
$ copilotkit (--version)
@copilotkit/cli/0.0.3 darwin-arm64 node-v20.18.0
$ copilotkit --help [COMMAND]
USAGE
  $ copilotkit COMMAND
...
```
<!-- usagestop -->

The CopilotKit CLI can be used in two ways:

1. Run directly with npx:

```sh
npx @copilotkit/cli tunnel <port>
```

2. Install the CLI globally:

```sh
npm install -g @copilotkit/cli
```
