import { z } from "zod";
import { G<PERSON>BaseTool, GmailBaseToolParams } from "./base.js";
export declare class GmailCreateDraft extends GmailBaseTool {
    name: string;
    schema: z.ZodObject<{
        message: z.ZodString;
        to: z.<PERSON><PERSON><z.ZodString, "many">;
        subject: z.ZodString;
        cc: z.Zod<PERSON>ptional<z.Zod<PERSON>y<z.ZodString, "many">>;
        bcc: z.ZodOptional<z.Zod<PERSON>rray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        message: string;
        subject: string;
        to: string[];
        cc?: string[] | undefined;
        bcc?: string[] | undefined;
    }, {
        message: string;
        subject: string;
        to: string[];
        cc?: string[] | undefined;
        bcc?: string[] | undefined;
    }>;
    description: string;
    constructor(fields?: GmailBaseToolParams);
    private prepareDraftMessage;
    _call(arg: z.output<typeof this.schema>): Promise<string>;
}
export type CreateDraftSchema = {
    message: string;
    to: string[];
    subject: string;
    cc?: string[];
    bcc?: string[];
};
