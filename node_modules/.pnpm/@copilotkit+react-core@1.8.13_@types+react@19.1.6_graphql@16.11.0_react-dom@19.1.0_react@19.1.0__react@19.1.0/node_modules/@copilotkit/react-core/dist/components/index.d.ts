export { CopilotKit, defaultCopilotContextCategories } from './copilot-provider/copilotkit.js';
export { CopilotKitProps } from './copilot-provider/copilotkit-props.js';
import 'react/jsx-runtime';
import '@copilotkit/runtime-client-gql';
import 'react';
import '../copilot-context-8fb74a85.js';
import '@copilotkit/shared';
import '../types/frontend-action.js';
import '../hooks/use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';
