var T=Object.defineProperty;var m=(e,o,t)=>o in e?T(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t;var v=(e,o,t)=>(m(e,typeof o!="symbol"?o+"":o,t),t);import{Machine as y,batch as g}from'../../machine.js';import{ActionTypes as I,stackMachines as R}from'../../machines/stack-machine.js';import{Focus as p,calculateActiveIndex as x}from'../../utils/calculate-active-index.js';import{sortByDomNode as A}from'../../utils/focus-management.js';import{match as S}from'../../utils/match.js';var E=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(E||{}),L=(t=>(t[t.Single=0]="Single",t[t.Multi=1]="Multi",t))(L||{}),F=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(F||{}),M=(r=>(r[r.OpenListbox=0]="OpenListbox",r[r.CloseListbox=1]="CloseListbox",r[r.GoToOption=2]="GoToOption",r[r.Search=3]="Search",r[r.ClearSearch=4]="ClearSearch",r[r.RegisterOptions=5]="RegisterOptions",r[r.UnregisterOptions=6]="UnregisterOptions",r[r.SetButtonElement=7]="SetButtonElement",r[r.SetOptionsElement=8]="SetOptionsElement",r[r.SortOptions=9]="SortOptions",r))(M||{});function b(e,o=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,n=A(o(e.options.slice()),s=>s.dataRef.current.domRef.current),i=t?n.indexOf(t):null;return i===-1&&(i=null),{options:n,activeOptionIndex:i}}let C={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,pendingFocus:{focus:p.Nothing},listboxState:1,__demoMode:!1}},[0](e,o){if(e.dataRef.current.disabled||e.listboxState===0)return e;let t=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,i=e.options.findIndex(s=>n(s.dataRef.current.value));return i!==-1&&(t=i),{...e,pendingFocus:o.focus,listboxState:0,activeOptionIndex:t,__demoMode:!1}},[2](e,o){var s,l,u,d,a;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:"",activationTrigger:(s=o.trigger)!=null?s:1,__demoMode:!1};if(o.focus===p.Nothing)return{...t,activeOptionIndex:null};if(o.focus===p.Specific)return{...t,activeOptionIndex:e.options.findIndex(r=>r.id===o.id)};if(o.focus===p.Previous){let r=e.activeOptionIndex;if(r!==null){let O=e.options[r].dataRef.current.domRef,f=x(o,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});if(f!==null){let c=e.options[f].dataRef.current.domRef;if(((l=O.current)==null?void 0:l.previousElementSibling)===c.current||((u=c.current)==null?void 0:u.previousElementSibling)===null)return{...t,activeOptionIndex:f}}}}else if(o.focus===p.Next){let r=e.activeOptionIndex;if(r!==null){let O=e.options[r].dataRef.current.domRef,f=x(o,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});if(f!==null){let c=e.options[f].dataRef.current.domRef;if(((d=O.current)==null?void 0:d.nextElementSibling)===c.current||((a=c.current)==null?void 0:a.nextElementSibling)===null)return{...t,activeOptionIndex:f}}}}let n=b(e),i=x(o,{resolveItems:()=>n.options,resolveActiveIndex:()=>n.activeOptionIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled});return{...t,...n,activeOptionIndex:i}},[3]:(e,o)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let n=e.searchQuery!==""?0:1,i=e.searchQuery+o.value.toLowerCase(),l=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find(d=>{var a;return!d.dataRef.current.disabled&&((a=d.dataRef.current.textValue)==null?void 0:a.startsWith(i))}),u=l?e.options.indexOf(l):-1;return u===-1||u===e.activeOptionIndex?{...e,searchQuery:i}:{...e,searchQuery:i,activeOptionIndex:u,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,o)=>{let t=e.options.concat(o.options),n=e.activeOptionIndex;if(e.pendingFocus.focus!==p.Nothing&&(n=x(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:i=>i.id,resolveDisabled:i=>i.dataRef.current.disabled})),e.activeOptionIndex===null){let{isSelected:i}=e.dataRef.current;if(i){let s=t.findIndex(l=>i==null?void 0:i(l.dataRef.current.value));s!==-1&&(n=s)}}return{...e,options:t,activeOptionIndex:n,pendingFocus:{focus:p.Nothing},pendingShouldSort:!0}},[6]:(e,o)=>{let t=e.options,n=[],i=new Set(o.options);for(let[s,l]of t.entries())if(i.has(l.id)&&(n.push(s),i.delete(l.id),i.size===0))break;if(n.length>0){t=t.slice();for(let s of n.reverse())t.splice(s,1)}return{...e,options:t,activationTrigger:1}},[7]:(e,o)=>e.buttonElement===o.element?e:{...e,buttonElement:o.element},[8]:(e,o)=>e.optionsElement===o.element?e:{...e,optionsElement:o.element},[9]:e=>e.pendingShouldSort?{...e,...b(e),pendingShouldSort:!1}:e};class h extends y{constructor(t){super(t);v(this,"actions",{onChange:t=>{let{onChange:n,compare:i,mode:s,value:l}=this.state.dataRef.current;return S(s,{[0]:()=>n==null?void 0:n(t),[1]:()=>{let u=l.slice(),d=u.findIndex(a=>i(a,t));return d===-1?u.push(t):u.splice(d,1),n==null?void 0:n(u)}})},registerOption:g(()=>{let t=[],n=new Set;return[(i,s)=>{n.has(s)||(n.add(s),t.push({id:i,dataRef:s}))},()=>(n.clear(),this.send({type:5,options:t.splice(0)}))]}),unregisterOption:g(()=>{let t=[];return[n=>t.push(n),()=>{this.send({type:6,options:t.splice(0)})}]}),goToOption:g(()=>{let t=null;return[(n,i)=>{t={type:2,...n,trigger:i}},()=>t&&this.send(t)]}),closeListbox:()=>{this.send({type:1})},openListbox:t=>{this.send({type:0,focus:t})},selectActiveOption:()=>{if(this.state.activeOptionIndex!==null){let{dataRef:t,id:n}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(t.current.value),this.send({type:2,focus:p.Specific,id:n})}},selectOption:t=>{let n=this.state.options.find(i=>i.id===t);n&&this.actions.onChange(n.dataRef.current.value)},search:t=>{this.send({type:3,value:t})},clearSearch:()=>{this.send({type:4})},setButtonElement:t=>{this.send({type:7,element:t})},setOptionsElement:t=>{this.send({type:8,element:t})}});v(this,"selectors",{activeDescendantId(t){var s;let n=t.activeOptionIndex,i=t.options;return n===null||(s=i[n])==null?void 0:s.id},isActive(t,n){var l;let i=t.activeOptionIndex,s=t.options;return i!==null?((l=s[i])==null?void 0:l.id)===n:!1},shouldScrollIntoView(t,n){return t.__demoMode||t.listboxState!==0||t.activationTrigger===0?!1:this.isActive(t,n)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})});{let n=this.state.id,i=R.get(null);this.disposables.add(i.on(I.Push,s=>{!i.selectors.isTop(s,n)&&this.state.listboxState===0&&this.actions.closeListbox()})),this.on(0,()=>i.actions.push(n)),this.on(1,()=>i.actions.pop(n))}}static new({id:t,__demoMode:n=!1}){return new h({id:t,dataRef:{current:{}},listboxState:n?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,pendingShouldSort:!1,pendingFocus:{focus:p.Nothing},__demoMode:n})}reduce(t,n){return S(n.type,C,t,n)}}export{M as ActionTypes,F as ActivationTrigger,h as ListboxMachine,E as ListboxStates,L as ValueMode};
