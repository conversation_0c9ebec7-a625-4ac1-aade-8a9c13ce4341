"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.promiseForObject = promiseForObject;
const promise_helpers_1 = require("@whatwg-node/promise-helpers");
/**
 * This function transforms a JS object `Record<string, Promise<T>>` into
 * a `Promise<Record<string, T>>`
 *
 * This is akin to bluebird's `Promise.props`, but implemented only using
 * `Promise.all` so it will work with any implementation of ES6 promises.
 */
function promiseForObject(object, signal, signalPromise) {
    signal?.throwIfAborted();
    const resolvedObject = Object.create(null);
    const promises = [];
    for (const key in object) {
        const valueSet$ = (0, promise_helpers_1.handleMaybePromise)(() => object[key], resolvedValue => {
            resolvedObject[key] = resolvedValue;
        });
        if ((0, promise_helpers_1.isPromise)(valueSet$)) {
            promises.push(valueSet$);
        }
    }
    if (!promises.length) {
        return resolvedObject;
    }
    const promiseAll = promises.length === 1 ? promises[0] : Promise.all(promises);
    if (signalPromise) {
        return Promise.race([signalPromise, promiseAll]).then(() => resolvedObject);
    }
    return promiseAll.then(() => resolvedObject);
}
