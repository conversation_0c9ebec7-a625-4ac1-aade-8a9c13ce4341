import { RemoteEndpointType } from '../utils/detect-endpoint-type.utils.js';

type AnalyticsEvents = {
    "cli.login.initiated": {};
    "cli.login.success": {
        organizationId: string;
        userId: string;
        email: string;
    };
    "cli.logout": {
        organizationId: string;
        userId: string;
        email: string;
    };
    "cli.dev.initiatied": {
        port: string;
        projectId: string;
        endpointType: RemoteEndpointType.LangGraphPlatform | RemoteEndpointType.CopilotKit;
    };
    "cli.dev.tunnel.created": {
        tunnelId: string;
        port: string;
        projectId: string;
        endpointType: RemoteEndpointType.LangGraphPlatform | RemoteEndpointType.CopilotKit;
    };
    "cli.dev.tunnel.closed": {
        tunnelId: string;
    };
};

export type { AnalyticsEvents };
