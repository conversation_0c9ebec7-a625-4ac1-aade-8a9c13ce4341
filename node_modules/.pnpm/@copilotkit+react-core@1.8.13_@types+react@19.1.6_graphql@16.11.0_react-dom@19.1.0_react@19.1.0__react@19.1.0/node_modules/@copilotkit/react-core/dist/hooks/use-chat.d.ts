import React from 'react';
import { Fun<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CoAgentStateRenderHandler } from '@copilotkit/shared';
import { Message, TextMessage, ForwardedParametersInput, ExtensionsInput } from '@copilotkit/runtime-client-gql';
import { c as CopilotApiConfig, A as AgentSession, L as LangGraphInterruptAction, d as LangGraphInterruptActionSetter } from '../copilot-context-8fb74a85.js';
import { FrontendAction } from '../types/frontend-action.js';
import { CoagentState } from '../types/coagent-state.js';
import './use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';

type UseChatOptions = {
    /**
     * System messages of the chat. Defaults to an empty array.
     */
    initialMessages?: Message[];
    /**
     * Callback function to be called when a function call is received.
     * If the function returns a `ChatRequest` object, the request will be sent
     * automatically to the API and will be used to update the chat.
     */
    onFunctionCall?: FunctionCallHandler;
    /**
     * Callback function to be called when a coagent action is received.
     */
    onCoAgentStateRender?: CoAgentStateRenderHandler;
    /**
     * Function definitions to be sent to the API.
     */
    actions: FrontendAction<any>[];
    /**
     * The CopilotKit API configuration.
     */
    copilotConfig: CopilotApiConfig;
    /**
     * The current list of messages in the chat.
     */
    messages: Message[];
    /**
     * The setState-powered method to update the chat messages.
     */
    setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
    /**
     * A callback to get the latest system message.
     */
    makeSystemMessageCallback: () => TextMessage;
    /**
     * Whether the API request is in progress
     */
    isLoading: boolean;
    /**
     * setState-powered method to update the isChatLoading value
     */
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
    /**
     * The current list of coagent states.
     */
    coagentStatesRef: React.RefObject<Record<string, CoagentState>>;
    /**
     * setState-powered method to update the agent states
     */
    setCoagentStatesWithRef: React.Dispatch<React.SetStateAction<Record<string, CoagentState>>>;
    /**
     * The current agent session.
     */
    agentSession: AgentSession | null;
    /**
     * setState-powered method to update the agent session
     */
    setAgentSession: React.Dispatch<React.SetStateAction<AgentSession | null>>;
    /**
     * The forwarded parameters.
     */
    forwardedParameters?: Pick<ForwardedParametersInput, "temperature">;
    /**
     * The current thread ID.
     */
    threadId: string;
    /**
     * set the current thread ID
     */
    setThreadId: (threadId: string) => void;
    /**
     * The current run ID.
     */
    runId: string | null;
    /**
     * set the current run ID
     */
    setRunId: (runId: string | null) => void;
    /**
     * The global chat abort controller.
     */
    chatAbortControllerRef: React.MutableRefObject<AbortController | null>;
    /**
     * The agent lock.
     */
    agentLock: string | null;
    /**
     * The extensions.
     */
    extensions: ExtensionsInput;
    /**
     * The setState-powered method to update the extensions.
     */
    setExtensions: React.Dispatch<React.SetStateAction<ExtensionsInput>>;
    langGraphInterruptAction: LangGraphInterruptAction | null;
    setLangGraphInterruptAction: LangGraphInterruptActionSetter;
};
type UseChatHelpers = {
    /**
     * Append a user message to the chat list. This triggers the API call to fetch
     * the assistant's response.
     * @param message The message to append
     */
    append: (message: Message, options?: AppendMessageOptions) => Promise<void>;
    /**
     * Reload the last AI chat response for the given chat history. If the last
     * message isn't from the assistant, it will request the API to generate a
     * new response.
     */
    reload: (messageId: string) => Promise<void>;
    /**
     * Abort the current request immediately, keep the generated tokens if any.
     */
    stop: () => void;
    /**
     * Run the chat completion.
     */
    runChatCompletion: () => Promise<Message[]>;
};
interface AppendMessageOptions {
    /**
     * Whether to run the chat completion after appending the message. Defaults to `true`.
     */
    followUp?: boolean;
}
declare function useChat(options: UseChatOptions): UseChatHelpers;

export { AppendMessageOptions, UseChatHelpers, UseChatOptions, useChat };
