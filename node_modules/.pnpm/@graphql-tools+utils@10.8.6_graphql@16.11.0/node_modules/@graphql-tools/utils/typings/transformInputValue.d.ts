import { GraphQLInputType } from 'graphql';
import { InputLeafValueTransformer, InputObjectValueTransformer, Maybe } from './types.js';
export declare function transformInputValue(type: GraphQLInputType, value: any, inputLeafValueTransformer?: Maybe<InputLeafValueTransformer>, inputObjectValueTransformer?: Maybe<InputObjectValueTransformer>): any;
export declare function serializeInputValue(type: GraphQLInputType, value: any): any;
export declare function parseInputValue(type: GraphQLInputType, value: any): any;
export declare function parseInputValueLiteral(type: GraphQLInputType, value: any): any;
