import * as react_jsx_runtime from 'react/jsx-runtime';
import { CopilotKitError, Severity } from '@copilotkit/shared';

interface UsageBannerProps {
    severity?: Severity;
    message?: string;
    icon?: React.ReactNode;
    actions?: {
        primary?: {
            label: string;
            onClick: () => void;
        };
        secondary?: {
            label: string;
            onClick: () => void;
        };
    };
}
declare function UsageBanner({ severity, message, icon, actions, }: UsageBannerProps): react_jsx_runtime.JSX.Element | null;
declare function renderCopilotKitUsage(error: CopilotKitError): react_jsx_runtime.JSX.Element | undefined;

export { UsageBanner, renderCopilotKitUsage };
