"""
Pydantic schemas for intake webhook API.

This module defines the request and response schemas for the avricons intake webhook endpoint.
"""

from typing import Any, Dict
from uuid import UUID

from pydantic import BaseModel, Field


class IntakeEventRequest(BaseModel):
    """
    Schema for incoming intake webhook events from the Voice service.
    
    This represents the JSON payload that the Voice service sends to our webhook endpoint.
    """
    
    tenant_id: UUID = Field(
        ...,
        description="The tenant ID associated with this intake event"
    )
    
    call_id: str = Field(
        ...,
        description="Unique identifier for the voice call"
    )
    
    call_status: str = Field(
        ...,
        description="Status of the call (completed, failed, etc.)"
    )
    
    call_duration: int = Field(
        ...,
        description="Duration of the call in seconds"
    )
    
    transcript: str = Field(
        ...,
        description="Full transcript of the call"
    )
    
    summary: str = Field(
        ...,
        description="AI-generated summary of the call"
    )
    
    client_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="Extracted client information from the call"
    )
    
    case_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="Extracted case information from the call"
    )
    
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata about the call"
    )
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            UUID: str
        }


class IntakeEventResponse(BaseModel):
    """
    Schema for the webhook response.
    
    This is what we return to the Voice service to acknowledge receipt.
    """
    
    status: str = Field(
        default="queued",
        description="Status of the webhook processing"
    )
    
    event_id: UUID = Field(
        ...,
        description="Unique identifier for the stored event"
    )
    
    message: str = Field(
        default="Event queued for processing",
        description="Human-readable status message"
    )
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            UUID: str
        }
