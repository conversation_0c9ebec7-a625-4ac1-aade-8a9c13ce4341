{"version": 3, "sources": ["../../src/services/tunnel.service.ts"], "sourcesContent": ["import type {Tunnel} from 'localtunnel'\n\nimport axios from 'axios'\nimport localtunnel from 'localtunnel'\n\nexport interface TunnelOptions {\n  port: number\n  subdomain?: string\n}\n\nexport class TunnelService {\n  private readonly META_DATA_URL = 'https://metadata-cdn.copilotkit.ai/cloud.config.json'\n\n  async create(options: TunnelOptions): Promise<Tunnel> {\n    return localtunnel(options)\n  }\n\n  async getMetaData() {\n    const response = await axios.get<{\n      tunnelHost: string\n    }>(this.META_DATA_URL)\n    return response.data\n  }\n}\n"], "mappings": ";AAEA,OAAO,WAAW;AAClB,OAAO,iBAAiB;AAOjB,IAAM,gBAAN,MAAoB;AAAA,EACR,gBAAgB;AAAA,EAEjC,MAAM,OAAO,SAAyC;AACpD,WAAO,YAAY,OAAO;AAAA,EAC5B;AAAA,EAEA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,MAAM,IAE1B,KAAK,aAAa;AACrB,WAAO,SAAS;AAAA,EAClB;AACF;", "names": []}