import {
  useCopilotChat
} from "./chunk-KQPKROYS.mjs";
import {
  CopilotContext
} from "./chunk-7WG4MNRX.mjs";
import {
  useToast
} from "./chunk-22ENANUU.mjs";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-SKC7AJIV.mjs";

// src/hooks/use-langgraph-interrupt.ts
import { useContext, useEffect, useMemo } from "react";
import { dataToUUID } from "@copilotkit/shared";
function useLangGraphInterrupt(action, dependencies) {
  var _a;
  const { setLangGraphInterruptAction, removeLangGraphInterruptAction, langGraphInterruptAction } = useContext(CopilotContext);
  const { runChatCompletion } = useCopilotChat();
  const { addToast } = useToast();
  const actionId = dataToUUID(JSON.stringify(action), "lgAction");
  const hasAction = useMemo(
    () => Boolean(langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.id),
    [langGraphInterruptAction]
  );
  const isCurrentAction = useMemo(
    () => (langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.id) && (langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.id) === actionId,
    [langGraphInterruptAction]
  );
  useEffect(() => {
    var _a2;
    if (hasAction && isCurrentAction && ((_a2 = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event) == null ? void 0 : _a2.response)) {
      runChatCompletion();
    }
  }, [(_a = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event) == null ? void 0 : _a.response, runChatCompletion, hasAction, isCurrentAction]);
  useEffect(() => {
    if (!action)
      return;
    if (hasAction && !isCurrentAction && !action.enabled) {
      addToast({
        type: "warning",
        message: "An action is already registered for the interrupt event"
      });
      return;
    }
    if (hasAction && isCurrentAction) {
      return;
    }
    setLangGraphInterruptAction(__spreadProps(__spreadValues({}, action), { id: actionId }));
  }, [
    action,
    hasAction,
    isCurrentAction,
    setLangGraphInterruptAction,
    removeLangGraphInterruptAction,
    ...dependencies || []
  ]);
}

export {
  useLangGraphInterrupt
};
//# sourceMappingURL=chunk-YXPFUKGZ.mjs.map