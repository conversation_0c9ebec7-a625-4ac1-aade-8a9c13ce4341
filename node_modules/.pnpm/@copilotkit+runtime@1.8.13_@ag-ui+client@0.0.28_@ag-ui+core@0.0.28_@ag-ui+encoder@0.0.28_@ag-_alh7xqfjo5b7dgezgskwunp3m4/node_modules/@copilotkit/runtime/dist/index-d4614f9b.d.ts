import { BaseMessageInput } from './graphql/types/base/index.js';

declare enum MessageRole {
    user = "user",
    assistant = "assistant",
    system = "system",
    tool = "tool",
    developer = "developer"
}
declare enum ActionInputAvailability {
    disabled = "disabled",
    enabled = "enabled",
    remote = "remote"
}

declare class MessageInput extends BaseMessageInput {
    textMessage?: TextMessageInput;
    actionExecutionMessage?: ActionExecutionMessageInput;
    resultMessage?: ResultMessageInput;
    agentStateMessage?: AgentStateMessageInput;
    imageMessage?: ImageMessageInput;
}
declare class TextMessageInput {
    content: string;
    parentMessageId?: string;
    role: MessageRole;
}
declare class ActionExecutionMessageInput {
    name: string;
    arguments: string;
    parentMessageId?: string;
    scope?: String;
}
declare class ResultMessageInput {
    actionExecutionId: string;
    actionName: string;
    parentMessageId?: string;
    result: string;
}
declare class AgentStateMessageInput {
    threadId: string;
    agentName: string;
    role: MessageRole;
    state: string;
    running: boolean;
    nodeName: string;
    runId: string;
    active: boolean;
}
declare class ImageMessageInput {
    format: string;
    bytes: string;
    parentMessageId?: string;
    role: MessageRole;
}

type MessageType = "TextMessage" | "ActionExecutionMessage" | "ResultMessage" | "AgentStateMessage" | "ImageMessage";
declare class Message extends BaseMessageInput {
    type: MessageType;
    isTextMessage(): this is TextMessage;
    isActionExecutionMessage(): this is ActionExecutionMessage;
    isResultMessage(): this is ResultMessage;
    isAgentStateMessage(): this is AgentStateMessage;
    isImageMessage(): this is ImageMessage;
}
declare class TextMessage extends Message implements TextMessageInput {
    type: MessageType;
    content: string;
    role: MessageRole;
    parentMessageId?: string;
}
declare class ActionExecutionMessage extends Message implements Omit<ActionExecutionMessageInput, "arguments" | "scope"> {
    type: MessageType;
    name: string;
    arguments: Record<string, any>;
    parentMessageId?: string;
}
declare class ResultMessage extends Message implements ResultMessageInput {
    type: MessageType;
    actionExecutionId: string;
    actionName: string;
    result: string;
    static encodeResult(result: any, error?: {
        code: string;
        message: string;
    } | string | Error): string;
    static decodeResult(result: string): {
        error?: {
            code: string;
            message: string;
        };
        result: string;
    };
    hasError(): boolean;
    getError(): {
        code: string;
        message: string;
    } | undefined;
}
declare class AgentStateMessage extends Message implements Omit<AgentStateMessageInput, "state"> {
    type: MessageType;
    threadId: string;
    agentName: string;
    nodeName: string;
    runId: string;
    active: boolean;
    role: MessageRole;
    state: any;
    running: boolean;
}
declare class ImageMessage extends Message implements ImageMessageInput {
    type: MessageType;
    format: string;
    bytes: string;
    role: MessageRole;
    parentMessageId?: string;
}

export { ActionInputAvailability as A, ImageMessage as I, MessageInput as M, ResultMessage as R, TextMessage as T, Message as a, ActionExecutionMessage as b, MessageType as c, AgentStateMessage as d };
