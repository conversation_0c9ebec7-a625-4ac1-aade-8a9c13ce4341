{"name": "@browserbasehq/stagehand", "version": "1.14.0", "description": "An AI web browsing framework focused on simplicity and extensibility.", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"2048": "npm run build && tsx examples/2048.ts", "popup": "npm run build && tsx examples/popup.ts", "example": "npm run build && tsx examples/example.ts", "langchain": "npm run build && tsx examples/langchain.ts", "debug-url": "npm run build && tsx examples/debugUrl.ts", "external-client": "npm run build && tsx examples/external_client.ts", "instructions": "npm run build && tsx examples/instructions.ts", "ai-sdk-client": "npm run build && tsx examples/ai_sdk_example.ts", "actionable_observe_example": "npm run build && tsx examples/actionable_observe_example.ts", "format": "prettier --write .", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "eslint": "eslint .", "cache:clear": "rm -rf .cache", "evals": "npm run build && tsx evals/index.eval.ts", "e2e": "npm run build && cd evals/deterministic && npx playwright test --config=e2e.playwright.config.ts", "e2e:bb": "npm run build && cd evals/deterministic && npx playwright test --config=bb.playwright.config.ts", "e2e:local": "npm run build && cd evals/deterministic && npx playwright test --config=local.playwright.config.ts", "build-dom-scripts": "tsx lib/dom/genDomScripts.ts", "build-types": "tsc --emitDeclarationOnly --outDir dist", "build-js": "tsup lib/index.ts --dts", "build": "npm run lint && npm run build-dom-scripts && npm run build-js && npm run build-types", "lint": "npm run prettier && npm run eslint", "release": "npm run build && changeset publish", "release-canary": "npm run build && changeset version --snapshot && changeset publish --tag alpha"}, "files": ["dist/**", "lib/**"], "keywords": [], "author": "Browserbase", "license": "MIT", "devDependencies": {"@ai-sdk/google": "^1.0.13", "@ai-sdk/openai": "^1.0.14", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.9", "@eslint/js": "^9.16.0", "@langchain/core": "^0.3.40", "@langchain/openai": "^0.4.4", "@types/adm-zip": "^0.5.7", "@types/cheerio": "^0.22.35", "@types/express": "^4.17.21", "@types/node": "^20.11.30", "@types/ws": "^8.5.13", "adm-zip": "^0.5.16", "ai": "^4.0.26", "autoevals": "^0.0.64", "braintrust": "^0.0.171", "chalk": "^5.4.1", "cheerio": "^1.0.0", "chromium-bidi": "^0.10.0", "esbuild": "^0.21.4", "eslint": "^9.16.0", "express": "^4.21.0", "globals": "^15.13.0", "multer": "^1.4.5-lts.1", "prettier": "^3.2.5", "string-comparison": "^1.3.0", "tsup": "^8.2.1", "tsx": "^4.10.5", "typescript": "^5.2.2", "typescript-eslint": "^8.17.0"}, "peerDependencies": {"@playwright/test": "^1.42.1", "deepmerge": "^4.3.1", "dotenv": "^16.4.5", "openai": "^4.62.1", "zod": "^3.23.8"}, "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "@browserbasehq/sdk": "^2.0.0", "ws": "^8.18.0", "zod-to-json-schema": "^3.23.5"}, "directories": {"doc": "docs", "example": "examples", "lib": "lib"}, "repository": {"type": "git", "url": "git+https://github.com/browserbase/stagehand.git"}, "bugs": {"url": "https://github.com/browserbase/stagehand/issues"}, "homepage": "https://github.com/browserbase/stagehand#readme"}