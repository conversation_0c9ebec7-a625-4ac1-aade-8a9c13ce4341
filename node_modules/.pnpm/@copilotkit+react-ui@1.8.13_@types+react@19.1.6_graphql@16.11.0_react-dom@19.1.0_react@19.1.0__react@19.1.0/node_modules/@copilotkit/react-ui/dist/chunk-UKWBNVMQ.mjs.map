{"version": 3, "sources": ["../src/components/chat/messages/RenderImageMessage.tsx"], "sourcesContent": ["import { RenderMessageProps } from \"../props\";\nimport { UserMessage as DefaultUserMessage } from \"./UserMessage\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderImageMessage({\n  UserMessage = DefaultUserMessage,\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const {\n    message,\n    inProgress,\n    index,\n    isCurrentMessage,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n  } = props;\n\n  if (message.isImageMessage()) {\n    const imageData = `data:${message.format};base64,${message.bytes}`;\n    const imageComponent = (\n      <div className=\"copilotKitImage\">\n        <img\n          src={imageData}\n          alt=\"User uploaded image\"\n          style={{ maxWidth: \"100%\", maxHeight: \"300px\", borderRadius: \"8px\" }}\n        />\n      </div>\n    );\n\n    if (message.role === \"user\") {\n      return (\n        <UserMessage\n          key={index}\n          data-message-role=\"user\"\n          message=\"\"\n          rawData={message}\n          subComponent={imageComponent}\n        />\n      );\n    } else if (message.role === \"assistant\") {\n      return (\n        <AssistantMessage\n          key={index}\n          data-message-role=\"assistant\"\n          message=\"\"\n          rawData={message}\n          subComponent={imageComponent}\n          isLoading={inProgress && isCurrentMessage && !message.bytes}\n          isGenerating={inProgress && isCurrentMessage && !!message.bytes}\n          isCurrentMessage={isCurrentMessage}\n          onRegenerate={() => onRegenerate?.(message.id)}\n          onCopy={onCopy}\n          onThumbsUp={onThumbsUp}\n          onThumbsDown={onThumbsDown}\n        />\n      );\n    }\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;AAwBQ;AApBD,SAAS,mBAAmB,IAIZ;AAJY,eACjC;AAAA,iBAAAA,eAAc;AAAA,IACd,kBAAAC,oBAAmB;AAAA,EANrB,IAImC,IAG9B,kBAH8B,IAG9B;AAAA,IAFH;AAAA,IACA;AAAA;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,QAAQ,eAAe,GAAG;AAC5B,UAAM,YAAY,QAAQ,QAAQ,iBAAiB,QAAQ;AAC3D,UAAM,iBACJ,oBAAC,SAAI,WAAU,mBACb;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,KAAI;AAAA,QACJ,OAAO,EAAE,UAAU,QAAQ,WAAW,SAAS,cAAc,MAAM;AAAA;AAAA,IACrE,GACF;AAGF,QAAI,QAAQ,SAAS,QAAQ;AAC3B,aACE;AAAA,QAACD;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAQ;AAAA,UACR,SAAS;AAAA,UACT,cAAc;AAAA;AAAA,QAJT;AAAA,MAKP;AAAA,IAEJ,WAAW,QAAQ,SAAS,aAAa;AACvC,aACE;AAAA,QAACC;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAQ;AAAA,UACR,SAAS;AAAA,UACT,cAAc;AAAA,UACd,WAAW,cAAc,oBAAoB,CAAC,QAAQ;AAAA,UACtD,cAAc,cAAc,oBAAoB,CAAC,CAAC,QAAQ;AAAA,UAC1D;AAAA,UACA,cAAc,MAAM,6CAAe,QAAQ;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA;AAAA,QAXK;AAAA,MAYP;AAAA,IAEJ;AAAA,EACF;AAEA,SAAO;AACT;", "names": ["UserMessage", "AssistantM<PERSON><PERSON>"]}