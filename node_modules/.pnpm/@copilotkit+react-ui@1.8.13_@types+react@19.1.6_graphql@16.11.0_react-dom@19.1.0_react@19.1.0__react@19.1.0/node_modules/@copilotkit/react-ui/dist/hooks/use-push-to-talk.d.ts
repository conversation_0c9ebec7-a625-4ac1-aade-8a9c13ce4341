import * as React from 'react';
import { Message } from '@copilotkit/runtime-client-gql';

declare const checkMicrophonePermission: () => Promise<boolean | undefined>;
declare const requestMicAndPlaybackPermission: () => Promise<{
    stream: MediaStream;
    audioContext: AudioContext;
} | null>;
type PushToTalkState = "idle" | "recording" | "transcribing";
type SendFunction = (text: string) => Promise<Message>;
declare const usePushToTalk: ({ sendFunction, inProgress, }: {
    sendFunction: SendFunction;
    inProgress: boolean;
}) => {
    pushToTalkState: PushToTalkState;
    setPushToTalkState: React.Dispatch<React.SetStateAction<PushToTalkState>>;
};

export { PushToTalkState, SendFunction, checkMicrophonePermission, requestMicAndPlaybackPermission, usePushToTalk };
