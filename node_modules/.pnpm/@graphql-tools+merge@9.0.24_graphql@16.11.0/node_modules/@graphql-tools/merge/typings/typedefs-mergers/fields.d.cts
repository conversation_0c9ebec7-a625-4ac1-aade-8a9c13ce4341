import { DirectiveDefinitionNode, FieldDefinitionNode, InputValueDefinitionNode, NameNode } from 'graphql';
import { Config } from './merge-typedefs.cjs';
type FieldDefNode = FieldDefinitionNode | InputValueDefinitionNode;
type NamedDefNode = {
    name: NameNode;
};
export type OnFieldTypeConflict = (existingField: FieldDefNode, otherField: FieldDefNode, type: NamedDefNode, ignoreNullability: boolean | undefined) => FieldDefNode;
export declare function mergeFields<T extends FieldDefNode>(type: {
    name: NameNode;
}, f1: ReadonlyArray<T> | undefined, f2: ReadonlyArray<T> | undefined, config?: Config, directives?: Record<string, DirectiveDefinitionNode>): T[];
export {};
