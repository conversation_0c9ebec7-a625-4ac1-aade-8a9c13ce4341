// Original file: proto/channelz.proto

import type { Long } from '@grpc/proto-loader';

export interface GetSubchannelRequest {
  /**
   * subchannel_id is the identifier of the specific subchannel to get.
   */
  'subchannel_id'?: (number | string | Long);
}

export interface GetSubchannelRequest__Output {
  /**
   * subchannel_id is the identifier of the specific subchannel to get.
   */
  'subchannel_id': (string);
}
