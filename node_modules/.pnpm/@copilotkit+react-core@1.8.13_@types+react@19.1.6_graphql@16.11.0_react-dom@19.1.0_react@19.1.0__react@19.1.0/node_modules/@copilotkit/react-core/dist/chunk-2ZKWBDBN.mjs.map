{"version": 3, "sources": ["../src/hooks/use-copilot-readable.ts"], "sourcesContent": ["/**\n * `useCopilotReadable` is a React hook that provides app-state and other information\n * to the Copilot. Optionally, the hook can also handle hierarchical state within your\n * application, passing these parent-child relationships to the Copilot.\n *\n * ## Usage\n *\n * ### Simple Usage\n *\n * In its most basic usage, useCopilotReadable accepts a single string argument\n * representing any piece of app state, making it available for the Copilot to use\n * as context when responding to user input.\n *\n * ```tsx\n * import { useCopilotReadable } from \"@copilotkit/react-core\";\n *\n * export function MyComponent() {\n *   const [employees, setEmployees] = useState([]);\n *\n *   useCopilotReadable({\n *     description: \"The list of employees\",\n *     value: employees,\n *   });\n * }\n * ```\n *\n * ### Nested Components\n *\n * Optionally, you can maintain the hierarchical structure of information by passing\n * `parentId`. This allows you to use `useCopilotReadable` in nested components:\n *\n * ```tsx /employeeContextId/1 {17,23}\n * import { useCopilotReadable } from \"@copilotkit/react-core\";\n *\n * function Employee(props: EmployeeProps) {\n *   const { employeeName, workProfile, metadata } = props;\n *\n *   // propagate any information to copilot\n *   const employeeContextId = useCopilotReadable({\n *     description: \"Employee name\",\n *     value: employeeName\n *   });\n *\n *   // Pass a parentID to maintain a hierarchical structure.\n *   // Especially useful with child React components, list elements, etc.\n *   useCopilotReadable({\n *     description: \"Work profile\",\n *     value: workProfile.description(),\n *     parentId: employeeContextId\n *   });\n *\n *   useCopilotReadable({\n *     description: \"Employee metadata\",\n *     value: metadata.description(),\n *     parentId: employeeContextId\n *   });\n *\n *   return (\n *     // Render as usual...\n *   );\n * }\n * ```\n */\nimport { useEffect, useRef } from \"react\";\nimport { useCopilotContext } from \"../context/copilot-context\";\n\n/**\n * Options for the useCopilotReadable hook.\n */\nexport interface UseCopilotReadableOptions {\n  /**\n   * The description of the information to be added to the Copilot context.\n   */\n  description: string;\n  /**\n   * The value to be added to the Copilot context. Object values are automatically stringified.\n   */\n  value: any;\n  /**\n   * The ID of the parent context, if any.\n   */\n  parentId?: string;\n  /**\n   * An array of categories to control which context are visible where. Particularly useful\n   * with CopilotTextarea (see `useMakeAutosuggestionFunction`)\n   */\n  categories?: string[];\n\n  /**\n   * Whether the context is available to the Copilot.\n   */\n  available?: \"enabled\" | \"disabled\";\n\n  /**\n   * A custom conversion function to use to serialize the value to a string. If not provided, the value\n   * will be serialized using `JSON.stringify`.\n   */\n  convert?: (description: string, value: any) => string;\n}\n\nfunction convertToJSON(description: string, value: any): string {\n  return `${description}: ${typeof value === \"string\" ? value : JSON.stringify(value)}`;\n}\n\n/**\n * Adds the given information to the Copilot context to make it readable by Copilot.\n */\nexport function useCopilotReadable(\n  {\n    description,\n    value,\n    parentId,\n    categories,\n    convert,\n    available = \"enabled\",\n  }: UseCopilotReadableOptions,\n  dependencies?: any[],\n): string | undefined {\n  const { addContext, removeContext } = useCopilotContext();\n  const idRef = useRef<string>();\n  convert = convert || convertToJSON;\n\n  const information = convert(description, value);\n\n  useEffect(() => {\n    if (available === \"disabled\") return;\n\n    const id = addContext(information, parentId, categories);\n    idRef.current = id;\n\n    return () => {\n      removeContext(id);\n    };\n  }, [available, information, parentId, addContext, removeContext, ...(dependencies || [])]);\n\n  return idRef.current;\n}\n"], "mappings": ";;;;;AA+DA,SAAS,WAAW,cAAc;AAqClC,SAAS,cAAc,aAAqB,OAAoB;AAC9D,SAAO,GAAG,gBAAgB,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,KAAK;AACpF;AAKO,SAAS,mBACd;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AACd,GACA,cACoB;AACpB,QAAM,EAAE,YAAY,cAAc,IAAI,kBAAkB;AACxD,QAAM,QAAQ,OAAe;AAC7B,YAAU,WAAW;AAErB,QAAM,cAAc,QAAQ,aAAa,KAAK;AAE9C,YAAU,MAAM;AACd,QAAI,cAAc;AAAY;AAE9B,UAAM,KAAK,WAAW,aAAa,UAAU,UAAU;AACvD,UAAM,UAAU;AAEhB,WAAO,MAAM;AACX,oBAAc,EAAE;AAAA,IAClB;AAAA,EACF,GAAG,CAAC,WAAW,aAAa,UAAU,YAAY,eAAe,GAAI,gBAAgB,CAAC,CAAE,CAAC;AAEzF,SAAO,MAAM;AACf;", "names": []}