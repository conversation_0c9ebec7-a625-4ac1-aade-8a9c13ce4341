-- Functions for handling prospect to user conversion
-- Ensures smooth transition from prospect to authenticated user

-- Function to convert prospect to authenticated user
CREATE OR REPLACE FUNCTION public.convert_prospect_to_user(
    prospect_email TEXT,
    auth_user_id UUID,
    tenant_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, tenants, auth
AS $$
DECLARE
    prospect_record public.prospects%ROWTYPE;
    conversion_successful BOOLEAN := false;
BEGIN
    -- Find the prospect by email
    SELECT * INTO prospect_record
    FROM public.prospects
    WHERE email = prospect_email
    AND status = 'active'
    AND deleted_at IS NULL;
    
    -- If prospect not found, return false
    IF NOT FOUND THEN
        RAISE NOTICE 'Prospect not found for email: %', prospect_email;
        RETURN false;
    END IF;
    
    -- Update prospect record with conversion data
    UPDATE public.prospects
    SET 
        status = 'converted',
        converted_to_user_id = auth_user_id,
        converted_to_tenant_id = tenant_id,
        converted_at = NOW(),
        updated_at = NOW()
    WHERE id = prospect_record.id;
    
    -- If tenant_id is provided, create entry in tenants.users
    IF tenant_id IS NOT NULL THEN
        -- Check if user already exists in tenants.users
        IF NOT EXISTS (
            SELECT 1 FROM tenants.users 
            WHERE id = auth_user_id
        ) THEN
            -- Insert into tenants.users with prospect data
            INSERT INTO tenants.users (
                id,
                email,
                role,
                tenant_id,
                created_at,
                last_login,
                settings
            ) VALUES (
                auth_user_id,
                prospect_record.email,
                'staff', -- Default role, can be updated later
                tenant_id,
                NOW(),
                NOW(),
                jsonb_build_object(
                    'prospect_data', jsonb_build_object(
                        'signup_source', prospect_record.signup_source,
                        'practice_area_interest', prospect_record.practice_area_interest,
                        'case_urgency', prospect_record.case_urgency,
                        'estimated_case_value', prospect_record.estimated_case_value,
                        'communication_preferences', prospect_record.communication_preferences,
                        'original_signup_date', prospect_record.created_at
                    )
                )
            );
        END IF;
    END IF;
    
    -- Log the conversion in prospect_interactions
    INSERT INTO public.prospect_interactions (
        prospect_id,
        interaction_type,
        subject,
        metadata,
        created_at
    ) VALUES (
        prospect_record.id,
        'conversion',
        'Converted to authenticated user',
        jsonb_build_object(
            'auth_user_id', auth_user_id,
            'tenant_id', tenant_id,
            'conversion_timestamp', NOW()
        ),
        NOW()
    );
    
    conversion_successful := true;
    
    RETURN conversion_successful;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error converting prospect: %', SQLERRM;
        RETURN false;
END;
$$;

-- Function to handle automatic prospect conversion on user registration
CREATE OR REPLACE FUNCTION public.handle_prospect_conversion()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, tenants, auth
AS $$
DECLARE
    user_email TEXT;
    tenant_id_from_metadata UUID;
    conversion_result BOOLEAN;
BEGIN
    -- Get user email from the new auth.users record
    user_email := NEW.email;
    
    -- Extract tenant_id from raw_user_meta_data if available
    tenant_id_from_metadata := (NEW.raw_user_meta_data->>'tenant_id')::UUID;
    
    -- Attempt to convert prospect
    SELECT public.convert_prospect_to_user(
        user_email,
        NEW.id,
        tenant_id_from_metadata
    ) INTO conversion_result;
    
    -- Log the conversion attempt
    IF conversion_result THEN
        RAISE NOTICE 'Successfully converted prospect % to user %', user_email, NEW.id;
    ELSE
        RAISE NOTICE 'No prospect found for email % during user registration', user_email;
    END IF;
    
    RETURN NEW;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Don't fail user registration if prospect conversion fails
        RAISE NOTICE 'Error in prospect conversion trigger: %', SQLERRM;
        RETURN NEW;
END;
$$;

-- Create trigger to automatically convert prospects on user registration
DROP TRIGGER IF EXISTS trigger_prospect_conversion ON auth.users;
CREATE TRIGGER trigger_prospect_conversion
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_prospect_conversion();

-- Function to generate email verification token
CREATE OR REPLACE FUNCTION public.generate_email_verification_token(
    prospect_email TEXT
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    verification_token TEXT;
    token_exists BOOLEAN;
BEGIN
    -- Generate a unique verification token
    LOOP
        verification_token := encode(gen_random_bytes(32), 'base64');
        
        -- Check if token already exists
        SELECT EXISTS(
            SELECT 1 FROM public.prospects 
            WHERE email_verification_token = verification_token
        ) INTO token_exists;
        
        -- Exit loop if token is unique
        EXIT WHEN NOT token_exists;
    END LOOP;
    
    -- Update prospect with verification token
    UPDATE public.prospects
    SET 
        email_verification_token = verification_token,
        email_verification_sent_at = NOW(),
        updated_at = NOW()
    WHERE email = prospect_email
    AND status = 'active'
    AND deleted_at IS NULL;
    
    -- Return the token if update was successful
    IF FOUND THEN
        RETURN verification_token;
    ELSE
        RETURN NULL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error generating verification token: %', SQLERRM;
        RETURN NULL;
END;
$$;

-- Function to verify email with token
CREATE OR REPLACE FUNCTION public.verify_prospect_email(
    verification_token TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    prospect_found BOOLEAN := false;
BEGIN
    -- Update prospect if token is valid and not expired (24 hours)
    UPDATE public.prospects
    SET 
        email_verified = true,
        email_verified_at = NOW(),
        email_verification_token = NULL, -- Clear token after use
        updated_at = NOW()
    WHERE email_verification_token = verification_token
    AND email_verification_sent_at > NOW() - INTERVAL '24 hours'
    AND email_verified = false
    AND status = 'active'
    AND deleted_at IS NULL;
    
    -- Check if update was successful
    GET DIAGNOSTICS prospect_found = FOUND;
    
    -- Log the verification attempt
    IF prospect_found THEN
        INSERT INTO public.prospect_interactions (
            prospect_id,
            interaction_type,
            subject,
            metadata,
            created_at
        )
        SELECT 
            id,
            'email_verified',
            'Email address verified',
            jsonb_build_object(
                'verification_token', verification_token,
                'verified_at', NOW()
            ),
            NOW()
        FROM public.prospects
        WHERE email_verified = true
        AND email_verified_at = NOW()
        LIMIT 1;
    END IF;
    
    RETURN prospect_found;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error verifying email: %', SQLERRM;
        RETURN false;
END;
$$;

-- Function for GDPR data cleanup (auto-delete old unverified prospects)
CREATE OR REPLACE FUNCTION public.cleanup_expired_prospects()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Soft delete prospects that have passed their retention date
    UPDATE public.prospects
    SET 
        deleted_at = NOW(),
        status = 'archived',
        updated_at = NOW()
    WHERE data_retention_until < CURRENT_DATE
    AND status NOT IN ('converted', 'archived')
    AND deleted_at IS NULL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log cleanup activity
    IF deleted_count > 0 THEN
        RAISE NOTICE 'Cleaned up % expired prospects', deleted_count;
    END IF;
    
    RETURN deleted_count;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error in prospect cleanup: %', SQLERRM;
        RETURN 0;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.convert_prospect_to_user(TEXT, UUID, UUID) TO service_role;
GRANT EXECUTE ON FUNCTION public.generate_email_verification_token(TEXT) TO service_role, authenticated;
GRANT EXECUTE ON FUNCTION public.verify_prospect_email(TEXT) TO service_role, authenticated, anon;
GRANT EXECUTE ON FUNCTION public.cleanup_expired_prospects() TO service_role;
