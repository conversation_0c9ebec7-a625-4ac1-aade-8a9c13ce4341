{"version": 3, "sources": ["../../src/types/css.ts"], "sourcesContent": ["// autogenerated (see postcss.config.js) - do not edit\nimport { CSSProperties } from \"react\";\n\nexport interface CopilotKitCSSProperties extends CSSProperties {\n  \"--copilot-kit-primary-color\"?: string;\n  \"--copilot-kit-contrast-color\"?: string;\n  \"--copilot-kit-background-color\"?: string;\n  \"--copilot-kit-input-background-color\"?: string;\n  \"--copilot-kit-secondary-color\"?: string;\n  \"--copilot-kit-secondary-contrast-color\"?: string;\n  \"--copilot-kit-separator-color\"?: string;\n  \"--copilot-kit-muted-color\"?: string;\n  \"--copilot-kit-shadow-sm\"?: string;\n  \"--copilot-kit-shadow-md\"?: string;\n  \"--copilot-kit-shadow-lg\"?: string;\n  \"--copilot-kit-dev-console-bg\"?: string;\n  \"--copilot-kit-dev-console-text\"?: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}