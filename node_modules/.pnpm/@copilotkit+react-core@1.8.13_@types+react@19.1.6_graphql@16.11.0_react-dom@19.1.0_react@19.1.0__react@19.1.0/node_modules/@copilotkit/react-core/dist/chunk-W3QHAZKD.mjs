import {
  useCopilotAction
} from "./chunk-INRN7B2F.mjs";
import {
  useCopilotContext
} from "./chunk-7WG4MNRX.mjs";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-SKC7AJIV.mjs";

// src/hooks/use-copilot-authenticated-action.ts
import { Fragment, useCallback, useRef } from "react";
import React from "react";
function useCopilotAuthenticatedAction_c(action, dependencies) {
  const { authConfig_c, authStates_c, setAuthStates_c } = useCopilotContext();
  const pendingActionRef = useRef(null);
  const executeAction = useCallback(
    (props) => {
      if (typeof action.render === "function") {
        return action.render(props);
      }
      return action.render || React.createElement(Fragment);
    },
    [action]
  );
  const wrappedRender = useCallback(
    (props) => {
      const isAuthenticated = Object.values(authStates_c || {}).some(
        (state) => state.status === "authenticated"
      );
      if (!isAuthenticated) {
        pendingActionRef.current = props;
        return (authConfig_c == null ? void 0 : authConfig_c.SignInComponent) ? React.createElement(authConfig_c.SignInComponent, {
          onSignInComplete: (authState) => {
            setAuthStates_c == null ? void 0 : setAuthStates_c((prev) => __spreadProps(__spreadValues({}, prev), { [action.name]: authState }));
            if (pendingActionRef.current) {
              executeAction(pendingActionRef.current);
              pendingActionRef.current = null;
            }
          }
        }) : React.createElement(Fragment);
      }
      return executeAction(props);
    },
    [action, authStates_c, setAuthStates_c]
  );
  useCopilotAction(
    __spreadProps(__spreadValues({}, action), {
      render: wrappedRender
    }),
    dependencies
  );
}

export {
  useCopilotAuthenticatedAction_c
};
//# sourceMappingURL=chunk-W3QHAZKD.mjs.map