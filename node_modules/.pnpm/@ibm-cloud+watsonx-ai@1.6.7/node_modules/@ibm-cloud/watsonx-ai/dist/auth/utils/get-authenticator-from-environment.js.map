{"version": 3, "file": "get-authenticator-from-environment.js", "sourceRoot": "", "sources": ["../../../auth/utils/get-authenticator-from-environment.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,iDAAiD;AAEjD,2DAQ4B;AAC5B,qDAAqF;AAErF;;;;;;;;;;;GAWG;AACH,SAAgB,+BAA+B,CAC7C,WAAmB,EACnB,YAAkD;IAElD,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,wDAAwD;IACxD,MAAM,WAAW,GAAG,IAAA,wCAAmB,EAAC,WAAW,CAAC,CAAC;IAErD,IAAI,WAAW,KAAK,IAAI,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;KAC5E;IAED,iCAAiC;IACjC,OAAO,WAAW,CAAC,UAAU,CAAC;IAE9B,iDAAiD;IACjD,IAAI,WAAW,CAAC,OAAO,EAAE;QACvB,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC;QACtC,OAAO,WAAW,CAAC,OAAO,CAAC;KAC5B;IAED,IAAI,WAAW,CAAC,cAAc,EAAE;QAC9B,WAAW,CAAC,sBAAsB,GAAG,WAAW,CAAC,cAAc,CAAC;QAChE,OAAO,WAAW,CAAC,cAAc,CAAC;KACnC;IAED,wDAAwD;IACxD,2CAA2C;IAC3C,oCAAoC;IACpC,IAAI,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC;IAE/B,IAAI,CAAC,QAAQ,EAAE;QACb,oDAAoD;QACpD,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;KACjC;IACD,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAC7C,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,kCAAa,CAAC,YAAY,CAAC,CAAC,CAAC,kCAAa,CAAC,kBAAkB,CAAC;KAC/F;IAED,mDAAmD;IACnD,IAAI,aAAa,CAAC;IAElB,mEAAmE;IACnE,8DAA8D;IAC9D,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;IAClC,IAAI,YAAY,IAAI,QAAQ,KAAK,4CAA2B,CAAC,YAAY,EAAE;QACzE,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;KAC/E;IACD,QAAQ,QAAQ,EAAE;QAChB,KAAK,kCAAa,CAAC,eAAe;YAChC,aAAa,GAAG,IAAI,wCAAmB,EAAE,CAAC;YAC1C,MAAM;QACR,KAAK,kCAAa,CAAC,cAAc;YAC/B,aAAa,GAAG,IAAI,uCAAkB,CAAC,WAAW,CAAC,CAAC;YACpD,MAAM;QACR,KAAK,kCAAa,CAAC,oBAAoB;YACrC,aAAa,GAAG,IAAI,6CAAwB,CAAC,WAAW,CAAC,CAAC;YAC1D,MAAM;QACR,KAAK,kCAAa,CAAC,aAAa;YAC9B,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;YACpE,aAAa,GAAG,IAAI,iDAA4B,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM;QACR,KAAK,kCAAa,CAAC,YAAY;YAC7B,aAAa,GAAG,IAAI,qCAAgB,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM;QACR,KAAK,4CAA2B,CAAC,YAAY;YAC3C,IAAI,YAAY,EAAE;gBAChB,aAAa,GAAG,IAAI,4CAA2B,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;aAC5E;;gBACC,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;YACJ,MAAM;QACR;YACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,QAAQ,EAAE,CAAC,CAAC;KAC/D;IAED,OAAO,aAAa,CAAC;AACvB,CAAC;AAjFD,0EAiFC"}