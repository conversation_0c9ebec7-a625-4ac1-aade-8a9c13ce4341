import { Command } from '@oclif/core';

interface LoginResponse {
    cliToken: string;
    user: {
        email: string;
        id: string;
    };
    organization: {
        id: string;
    };
}
declare class AuthService {
    private readonly config;
    private readonly COPILOT_CLOUD_BASE_URL;
    getToken(): string | undefined;
    getCLIToken(): string | undefined;
    logout(): Promise<void>;
    requireLogin(cmd: Command): Promise<LoginResponse>;
    login(): Promise<LoginResponse>;
}

export { AuthService };
