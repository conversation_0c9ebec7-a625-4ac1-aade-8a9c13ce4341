import { Config } from '@oclif/core';
import { AuthService } from '../services/auth.service.js';
import { BaseCommand } from './base-command.js';

declare class CloudLogin extends BaseCommand {
    private authService;
    static description: string;
    static examples: string[];
    constructor(argv: string[], config: Config, authService?: AuthService);
    run(): Promise<void>;
}

export { CloudLogin as default };
