{"version": 3, "sources": ["../../src/services/analytics.service.ts"], "sourcesContent": ["import {Analytics} from '@segment/analytics-node'\nimport {AnalyticsEvents} from './events.js'\nimport Conf from 'conf'\n\nexport class AnalyticsService {\n  private segment: Analytics | undefined\n  private globalProperties: Record<string, any> = {}\n  private userId: string | undefined;\n  private email: string | undefined;\n  private organizationId: string | undefined;\n  private config = new Conf({projectName: 'CopilotKitCLI'})\n\n  constructor(private readonly authData?: {\n    userId: string,\n    email: string,\n    organizationId: string,\n  }) {\n    if (process.env.SEGMENT_DISABLED === 'true') {\n      return;\n    }\n\n    const segmentWriteKey = process.env.SEGMENT_WRITE_KEY || \"9Pv6QyExYef2P4hPz4gks6QAvNMi2AOf\"\n\n    this.globalProperties = {\n      service: 'cli',\n    }\n\n\n    if (this.authData?.userId) {\n      this.userId = this.authData.userId\n    }\n\n    if (this.authData?.email) {\n      this.email = this.authData.email\n      this.globalProperties.email = this.authData.email\n    }\n\n    if (this.authData?.organizationId) {\n      this.organizationId = this.authData.organizationId\n    }\n\n    this.segment = new Analytics({\n      writeKey: segmentWriteKey,\n      disable: process.env.SEGMENT_DISABLE === 'true',\n    })\n\n    const config = new Conf({projectName: 'CopilotKitCLI'})\n    if (!config.get('anonymousId')) {\n      config.set('anonymousId', crypto.randomUUID())\n    }\n  }\n\n  private getAnonymousId(): string {\n    const anonymousId = this.config.get('anonymousId')\n    if (!anonymousId) {\n      const anonymousId = crypto.randomUUID()\n      this.config.set('anonymousId', anonymousId)\n      return anonymousId\n    }\n\n    return anonymousId as string;\n  }\n\n  public track<K extends keyof AnalyticsEvents>(\n    event: Omit<Parameters<Analytics['track']>[0], 'userId'> & {\n      event: K\n      properties: AnalyticsEvents[K]\n    },\n  ): void {\n    if (!this.segment) {\n      return;\n    }\n\n    const payload = {\n      userId: this.userId ? this.userId : undefined,\n      email: this.email ? this.email : undefined,\n      anonymousId: this.getAnonymousId(),\n      event: event.event,\n      properties: {\n        ...this.globalProperties,\n        ...event.properties,\n        $groups: this.organizationId ? {\n          segment_group: this.organizationId,\n        } : undefined,\n        eventProperties: {\n          ...event.properties,\n          ...this.globalProperties,\n        },\n      },\n    }\n\n    this.segment.track(payload)\n  }\n}\n"], "mappings": ";AAAA,SAAQ,iBAAgB;AAExB,OAAO,UAAU;AAEV,IAAM,mBAAN,MAAuB;AAAA,EAQ5B,YAA6B,UAI1B;AAJ0B;AAK3B,QAAI,QAAQ,IAAI,qBAAqB,QAAQ;AAC3C;AAAA,IACF;AAEA,UAAM,kBAAkB,QAAQ,IAAI,qBAAqB;AAEzD,SAAK,mBAAmB;AAAA,MACtB,SAAS;AAAA,IACX;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,SAAS,KAAK,SAAS;AAAA,IAC9B;AAEA,QAAI,KAAK,UAAU,OAAO;AACxB,WAAK,QAAQ,KAAK,SAAS;AAC3B,WAAK,iBAAiB,QAAQ,KAAK,SAAS;AAAA,IAC9C;AAEA,QAAI,KAAK,UAAU,gBAAgB;AACjC,WAAK,iBAAiB,KAAK,SAAS;AAAA,IACtC;AAEA,SAAK,UAAU,IAAI,UAAU;AAAA,MAC3B,UAAU;AAAA,MACV,SAAS,QAAQ,IAAI,oBAAoB;AAAA,IAC3C,CAAC;AAED,UAAM,SAAS,IAAI,KAAK,EAAC,aAAa,gBAAe,CAAC;AACtD,QAAI,CAAC,OAAO,IAAI,aAAa,GAAG;AAC9B,aAAO,IAAI,eAAe,OAAO,WAAW,CAAC;AAAA,IAC/C;AAAA,EACF;AAAA,EA7CQ;AAAA,EACA,mBAAwC,CAAC;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,IAAI,KAAK,EAAC,aAAa,gBAAe,CAAC;AAAA,EA0ChD,iBAAyB;AAC/B,UAAM,cAAc,KAAK,OAAO,IAAI,aAAa;AACjD,QAAI,CAAC,aAAa;AAChB,YAAMA,eAAc,OAAO,WAAW;AACtC,WAAK,OAAO,IAAI,eAAeA,YAAW;AAC1C,aAAOA;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,MACL,OAIM;AACN,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AAEA,UAAM,UAAU;AAAA,MACd,QAAQ,KAAK,SAAS,KAAK,SAAS;AAAA,MACpC,OAAO,KAAK,QAAQ,KAAK,QAAQ;AAAA,MACjC,aAAa,KAAK,eAAe;AAAA,MACjC,OAAO,MAAM;AAAA,MACb,YAAY;AAAA,QACV,GAAG,KAAK;AAAA,QACR,GAAG,MAAM;AAAA,QACT,SAAS,KAAK,iBAAiB;AAAA,UAC7B,eAAe,KAAK;AAAA,QACtB,IAAI;AAAA,QACJ,iBAAiB;AAAA,UACf,GAAG,MAAM;AAAA,UACT,GAAG,KAAK;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,OAAO;AAAA,EAC5B;AACF;", "names": ["anonymousId"]}