{"version": 3, "sources": ["../src/components/chat/Popup.tsx"], "sourcesContent": ["/**\n * <br/>\n * <img src=\"/images/CopilotPopup.gif\" width=\"500\" />\n *\n * A chatbot popup component for the CopilotKit framework. The component allows for a high degree\n * of customization through various props and custom CSS.\n *\n * See [CopilotSidebar](/reference/components/chat/CopilotSidebar) for a sidebar version of this component.\n *\n * ## Install Dependencies\n *\n * This component is part of the [@copilotkit/react-ui](https://npmjs.com/package/@copilotkit/react-ui) package.\n *\n * ```shell npm2yarn \\\"@copilotkit/react-ui\"\\\n * npm install @copilotkit/react-core @copilotkit/react-ui\n * ```\n * ## Usage\n *\n * ```tsx\n * import { CopilotPopup } from \"@copilotkit/react-ui\";\n * import \"@copilotkit/react-ui/styles.css\";\n *\n * <CopilotPopup\n *   labels={{\n *     title: \"Your Assistant\",\n *     initial: \"Hi! 👋 How can I assist you today?\",\n *   }}\n * />\n * ```\n *\n * ### Look & Feel\n *\n * By default, CopilotKit components do not have any styles. You can import CopilotKit's stylesheet at the root of your project:\n * ```tsx title=\"YourRootComponent.tsx\"\n * ...\n * import \"@copilotkit/react-ui/styles.css\"; // [!code highlight]\n *\n * export function YourRootComponent() {\n *   return (\n *     <CopilotKit>\n *       ...\n *     </CopilotKit>\n *   );\n * }\n * ```\n * For more information about how to customize the styles, check out the [Customize Look & Feel](/guides/custom-look-and-feel/customize-built-in-ui-components) guide.\n */\n\nimport { CopilotModal, CopilotModalProps } from \"./Modal\";\n\nexport function CopilotPopup(props: CopilotModalProps) {\n  props = {\n    ...props,\n    className: props.className ? props.className + \" copilotKitPopup\" : \"copilotKitPopup\",\n  };\n  return <CopilotModal {...props}>{props.children}</CopilotModal>;\n}\n"], "mappings": ";;;;;;;;;AAuDS;AALF,SAAS,aAAa,OAA0B;AACrD,UAAQ,iCACH,QADG;AAAA,IAEN,WAAW,MAAM,YAAY,MAAM,YAAY,qBAAqB;AAAA,EACtE;AACA,SAAO,oBAAC,+CAAiB,QAAjB,EAAyB,gBAAM,WAAS;AAClD;", "names": []}