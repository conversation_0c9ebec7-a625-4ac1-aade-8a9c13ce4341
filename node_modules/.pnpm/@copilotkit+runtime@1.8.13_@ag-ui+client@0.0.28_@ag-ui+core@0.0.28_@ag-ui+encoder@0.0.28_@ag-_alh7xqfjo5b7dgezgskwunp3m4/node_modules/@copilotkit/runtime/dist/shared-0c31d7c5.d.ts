import * as graphql from 'graphql';
import * as pino from 'pino';
import { YogaInitialContext, createYoga } from 'graphql-yoga';
import { Parameter, Action } from '@copilotkit/shared';
import { b as CopilotServiceAdapter, A as ActionInput, d as AgentSessionInput, e as AgentStateInput, F as ForwardedParametersInput, E as ExtensionsInput, R as RemoteChainParameters, f as RuntimeEventSource, g as ExtensionsResponse } from './langserve-4a5c9217.js';
import { M as MessageInput, a as Message } from './index-d4614f9b.js';
import { CopilotCloudOptions } from './lib/cloud/index.js';
import { AbstractAgent } from '@ag-ui/client';

declare enum MetaEventName {
    LangGraphInterruptEvent = "LangGraphInterruptEvent",
    CopilotKitLangGraphInterruptEvent = "CopilotKitLangGraphInterruptEvent"
}

declare class MetaEventInput {
    name: MetaEventName;
    value?: string;
    response?: string;
    messages?: MessageInput[];
}

type EndpointDefinition = CopilotKitEndpoint | LangGraphPlatformEndpoint;
declare enum EndpointType {
    CopilotKit = "copilotKit",
    LangGraphPlatform = "langgraph-platform"
}
interface BaseEndpointDefinition<TActionType extends EndpointType> {
    type?: TActionType;
}
interface CopilotKitEndpoint extends BaseEndpointDefinition<EndpointType.CopilotKit> {
    url: string;
    onBeforeRequest?: ({ ctx }: {
        ctx: GraphQLContext;
    }) => {
        headers?: Record<string, string> | undefined;
    };
}
interface LangGraphPlatformAgent {
    name: string;
    description: string;
    assistantId?: string;
}
interface LangGraphPlatformEndpoint extends BaseEndpointDefinition<EndpointType.LangGraphPlatform> {
    deploymentUrl: string;
    langsmithApiKey?: string | null;
    agents: LangGraphPlatformAgent[];
}

declare class Agent {
    id: string;
    name: string;
    description?: string;
}

declare class LoadAgentStateResponse {
    threadId: string;
    threadExists: boolean;
    state: string;
    messages: string;
}

interface LLMRequestData {
    threadId?: string;
    runId?: string;
    model?: string;
    messages: any[];
    actions?: any[];
    forwardedParameters?: any;
    timestamp: number;
    provider?: string;
    [key: string]: any;
}
interface LLMResponseData {
    threadId: string;
    runId?: string;
    model?: string;
    output: any;
    latency: number;
    timestamp: number;
    provider?: string;
    isProgressiveChunk?: boolean;
    isFinalResponse?: boolean;
    [key: string]: any;
}
interface LLMErrorData {
    threadId?: string;
    runId?: string;
    model?: string;
    error: Error | string;
    timestamp: number;
    provider?: string;
    [key: string]: any;
}
interface CopilotObservabilityHooks {
    handleRequest: (data: LLMRequestData) => void | Promise<void>;
    handleResponse: (data: LLMResponseData) => void | Promise<void>;
    handleError: (data: LLMErrorData) => void | Promise<void>;
}
/**
 * Configuration for CopilotKit logging functionality.
 *
 * @remarks
 * Custom logging handlers require a valid CopilotKit public API key.
 * Sign up at https://docs.copilotkit.ai/quickstart#get-a-copilot-cloud-public-api-key to get your key.
 */
interface CopilotObservabilityConfig {
    /**
     * Enable or disable logging functionality.
     *
     * @default false
     */
    enabled: boolean;
    /**
     * Controls whether logs are streamed progressively or buffered.
     * - When true: Each token and update is logged as it's generated (real-time)
     * - When false: Complete responses are logged after completion (batched)
     *
     * @default true
     */
    progressive: boolean;
    /**
     * Custom observability hooks for request, response, and error events.
     *
     * @remarks
     * Using custom observability hooks requires a valid CopilotKit public API key.
     */
    hooks: CopilotObservabilityHooks;
}

/**
 * Represents a tool provided by an MCP server.
 */
interface MCPTool {
    description?: string;
    /** Schema defining parameters, mirroring the MCP structure. */
    schema?: {
        parameters?: {
            properties?: Record<string, any>;
            required?: string[];
            jsonSchema?: Record<string, any>;
        };
    };
    /** The function to call to execute the tool on the MCP server. */
    execute(options: {
        params: any;
    }): Promise<any>;
}
/**
 * Defines the contract for *any* MCP client implementation the user might provide.
 */
interface MCPClient {
    /** A method that returns a map of tool names to MCPTool objects available from the connected MCP server. */
    tools(): Promise<Record<string, MCPTool>>;
    /** An optional method for cleanup if the underlying client requires explicit disconnection. */
    close?(): Promise<void>;
}
/**
 * Configuration for connecting to an MCP endpoint.
 */
interface MCPEndpointConfig {
    endpoint: string;
    apiKey?: string;
}
/**
 * Extracts CopilotKit-compatible parameters from an MCP tool schema.
 * @param toolOrSchema The schema object from an MCPTool or the full MCPTool object.
 * @returns An array of Parameter objects.
 */
declare function extractParametersFromSchema(toolOrSchema?: MCPTool | MCPTool["schema"]): Parameter[];
/**
 * Converts a map of MCPTools into an array of CopilotKit Actions.
 * @param mcpTools A record mapping tool names to MCPTool objects.
 * @param mcpEndpoint The endpoint URL from which these tools were fetched.
 * @returns An array of Action<any> objects.
 */
declare function convertMCPToolsToActions(mcpTools: Record<string, MCPTool>, mcpEndpoint: string): Action<any>[];
/**
 * Generate better instructions for using MCP tools
 * This is used to enhance the system prompt with tool documentation
 */
declare function generateMcpToolInstructions(toolsMap: Record<string, MCPTool>): string;

/**
 * <Callout type="info">
 *   This is the reference for the `CopilotRuntime` class. For more information and example code snippets, please see [Concept: Copilot Runtime](/concepts/copilot-runtime).
 * </Callout>
 *
 * ## Usage
 *
 * ```tsx
 * import { CopilotRuntime } from "@copilotkit/runtime";
 *
 * const copilotKit = new CopilotRuntime();
 * ```
 */

type CreateMCPClientFunction = (config: MCPEndpointConfig) => Promise<MCPClient>;
interface CopilotRuntimeRequest {
    serviceAdapter: CopilotServiceAdapter;
    messages: MessageInput[];
    actions: ActionInput[];
    agentSession?: AgentSessionInput;
    agentStates?: AgentStateInput[];
    outputMessagesPromise: Promise<Message[]>;
    threadId?: string;
    runId?: string;
    publicApiKey?: string;
    graphqlContext: GraphQLContext;
    forwardedParameters?: ForwardedParametersInput;
    url?: string;
    extensions?: ExtensionsInput;
    metaEvents?: MetaEventInput[];
}
interface CopilotRuntimeResponse {
    threadId: string;
    runId?: string;
    eventSource: RuntimeEventSource;
    serverSideActions: Action<any>[];
    actionInputsWithoutAgents: ActionInput[];
    extensions?: ExtensionsResponse;
}
type ActionsConfiguration<T extends Parameter[] | [] = []> = Action<T>[] | ((ctx: {
    properties: any;
    url?: string;
}) => Action<T>[]);
interface OnBeforeRequestOptions {
    threadId?: string;
    runId?: string;
    inputMessages: Message[];
    properties: any;
    url?: string;
}
type OnBeforeRequestHandler = (options: OnBeforeRequestOptions) => void | Promise<void>;
interface OnAfterRequestOptions {
    threadId: string;
    runId?: string;
    inputMessages: Message[];
    outputMessages: Message[];
    properties: any;
    url?: string;
}
type OnAfterRequestHandler = (options: OnAfterRequestOptions) => void | Promise<void>;
interface Middleware {
    /**
     * A function that is called before the request is processed.
     */
    onBeforeRequest?: OnBeforeRequestHandler;
    /**
     * A function that is called after the request is processed.
     */
    onAfterRequest?: OnAfterRequestHandler;
}
type AgentWithEndpoint = Agent & {
    endpoint: EndpointDefinition;
};
interface CopilotRuntimeConstructorParams<T extends Parameter[] | [] = []> {
    /**
     * Middleware to be used by the runtime.
     *
     * ```ts
     * onBeforeRequest: (options: {
     *   threadId?: string;
     *   runId?: string;
     *   inputMessages: Message[];
     *   properties: any;
     * }) => void | Promise<void>;
     * ```
     *
     * ```ts
     * onAfterRequest: (options: {
     *   threadId?: string;
     *   runId?: string;
     *   inputMessages: Message[];
     *   outputMessages: Message[];
     *   properties: any;
     * }) => void | Promise<void>;
     * ```
     */
    middleware?: Middleware;
    actions?: ActionsConfiguration<T>;
    remoteActions?: CopilotKitEndpoint[];
    remoteEndpoints?: EndpointDefinition[];
    langserve?: RemoteChainParameters[];
    agents?: Record<string, AbstractAgent>;
    delegateAgentProcessingToServiceAdapter?: boolean;
    /**
     * Configuration for LLM request/response logging.
     * Requires publicApiKey from CopilotKit component to be set:
     *
     * ```tsx
     * <CopilotKit publicApiKey="ck_pub_..." />
     * ```
     *
     * Example logging config:
     * ```ts
     * logging: {
     *   enabled: true, // Enable or disable logging
     *   progressive: true, // Set to false for buffered logging
     *   logger: {
     *     logRequest: (data) => langfuse.trace({ name: "LLM Request", input: data }),
     *     logResponse: (data) => langfuse.trace({ name: "LLM Response", output: data }),
     *     logError: (errorData) => langfuse.trace({ name: "LLM Error", metadata: errorData }),
     *   },
     * }
     * ```
     */
    observability_c?: CopilotObservabilityConfig;
    /**
     * Configuration for connecting to Model Context Protocol (MCP) servers.
     * Allows fetching and using tools defined on external MCP-compliant servers.
     * Requires providing the `createMCPClient` function during instantiation.
     * @experimental
     */
    mcpServers?: MCPEndpointConfig[];
    /**
     * A function that creates an MCP client instance for a given endpoint configuration.
     * This function is responsible for using the appropriate MCP client library
     * (e.g., `@copilotkit/runtime`, `ai`) to establish a connection.
     * Required if `mcpServers` is provided.
     *
     * ```typescript
     * import { experimental_createMCPClient } from "ai"; // Import from vercel ai library
     * // ...
     * const runtime = new CopilotRuntime({
     *   mcpServers: [{ endpoint: "..." }],
     *   async createMCPClient(config) {
     *     return await experimental_createMCPClient({
     *       transport: {
     *         type: "sse",
     *         url: config.endpoint,
     *         headers: config.apiKey
     *           ? { Authorization: `Bearer ${config.apiKey}` }
     *           : undefined,
     *       },
     *     });
     *   }
     * });
     * ```
     */
    createMCPClient?: CreateMCPClientFunction;
}
declare class CopilotRuntime<const T extends Parameter[] | [] = []> {
    actions: ActionsConfiguration<T>;
    agents: Record<string, AbstractAgent>;
    remoteEndpointDefinitions: EndpointDefinition[];
    private langserve;
    private onBeforeRequest?;
    private onAfterRequest?;
    private delegateAgentProcessingToServiceAdapter;
    private observability?;
    private availableAgents;
    private readonly mcpServersConfig?;
    private mcpActionCache;
    private readonly createMCPClientImpl?;
    constructor(params?: CopilotRuntimeConstructorParams<T>);
    private injectMCPToolInstructions;
    processRuntimeRequest(request: CopilotRuntimeRequest): Promise<CopilotRuntimeResponse>;
    discoverAgentsFromEndpoints(graphqlContext: GraphQLContext): Promise<AgentWithEndpoint[]>;
    loadAgentState(graphqlContext: GraphQLContext, threadId: string, agentName: string): Promise<LoadAgentStateResponse>;
    private processAgentRequest;
    private getServerSideActions;
    private detectProvider;
}
declare function flattenToolCallsNoDuplicates(toolsByPriority: ActionInput[]): ActionInput[];
declare function copilotKitEndpoint(config: Omit<CopilotKitEndpoint, "type">): CopilotKitEndpoint;
declare function langGraphPlatformEndpoint(config: Omit<LangGraphPlatformEndpoint, "type">): LangGraphPlatformEndpoint;
declare function resolveEndpointType(endpoint: EndpointDefinition): EndpointType;

type LogLevel = "debug" | "info" | "warn" | "error";
type CopilotRuntimeLogger = ReturnType<typeof createLogger>;
declare function createLogger(options?: {
    level?: LogLevel;
    component?: string;
}): pino.Logger<never>;

declare const logger: pino.Logger<never>;
declare const addCustomHeaderPlugin: {
    onResponse({ response }: {
        response: any;
    }): void;
};
type AnyPrimitive = string | boolean | number | null;
type CopilotRequestContextProperties = Record<string, AnyPrimitive | Record<string, AnyPrimitive>>;
type GraphQLContext = YogaInitialContext & {
    _copilotkit: CreateCopilotRuntimeServerOptions;
    properties: CopilotRequestContextProperties;
    logger: typeof logger;
};
interface CreateCopilotRuntimeServerOptions {
    runtime: CopilotRuntime<any>;
    serviceAdapter: CopilotServiceAdapter;
    endpoint: string;
    baseUrl?: string;
    cloud?: CopilotCloudOptions;
    properties?: CopilotRequestContextProperties;
    logLevel?: LogLevel;
}
declare function createContext(initialContext: YogaInitialContext, copilotKitContext: CreateCopilotRuntimeServerOptions, contextLogger: typeof logger, properties?: CopilotRequestContextProperties): Promise<Partial<GraphQLContext>>;
declare function buildSchema(options?: {
    emitSchemaFile?: string;
}): graphql.GraphQLSchema;
type CommonConfig = {
    logging: typeof logger;
    schema: ReturnType<typeof buildSchema>;
    plugins: Parameters<typeof createYoga>[0]["plugins"];
    context: (ctx: YogaInitialContext) => Promise<Partial<GraphQLContext>>;
};
declare function getCommonConfig(options: CreateCopilotRuntimeServerOptions): CommonConfig;

export { CopilotRequestContextProperties as C, GraphQLContext as G, LogLevel as L, MCPTool as M, addCustomHeaderPlugin as a, CreateCopilotRuntimeServerOptions as b, createContext as c, buildSchema as d, CommonConfig as e, CopilotRuntimeLogger as f, getCommonConfig as g, createLogger as h, CopilotRuntimeRequest as i, CopilotRuntimeConstructorParams as j, CopilotRuntime as k, flattenToolCallsNoDuplicates as l, copilotKitEndpoint as m, langGraphPlatformEndpoint as n, MCPClient as o, MCPEndpointConfig as p, extractParametersFromSchema as q, resolveEndpointType as r, convertMCPToolsToActions as s, generateMcpToolInstructions as t };
