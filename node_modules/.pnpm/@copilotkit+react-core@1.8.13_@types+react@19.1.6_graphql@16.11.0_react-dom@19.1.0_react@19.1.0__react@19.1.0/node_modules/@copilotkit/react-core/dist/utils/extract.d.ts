import { Parameter, MappedParameterTypes } from '@copilotkit/shared';
import { CopilotRequestType, ForwardedParametersInput } from '@copilotkit/runtime-client-gql';
import { a as CopilotContextParams } from '../copilot-context-8fb74a85.js';
import { CopilotMessagesContextParams } from '../context/copilot-messages-context.js';
import '../types/frontend-action.js';
import 'react';
import '../hooks/use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';

interface InitialState<T extends Parameter[] | [] = []> {
    status: "initial";
    args: Partial<MappedParameterTypes<T>>;
}
interface InProgressState<T extends Parameter[] | [] = []> {
    status: "inProgress";
    args: Partial<MappedParameterTypes<T>>;
}
interface CompleteState<T extends Parameter[] | [] = []> {
    status: "complete";
    args: MappedParameterTypes<T>;
}
type StreamHandlerArgs<T extends Parameter[] | [] = []> = InitialState<T> | InProgressState<T> | CompleteState<T>;
interface ExtractOptions<T extends Parameter[]> {
    context: CopilotContextParams & CopilotMessagesContextParams;
    instructions: string;
    parameters: T;
    include?: IncludeOptions;
    data?: any;
    abortSignal?: AbortSignal;
    stream?: (args: StreamHandlerArgs<T>) => void;
    requestType?: CopilotRequestType;
    forwardedParameters?: ForwardedParametersInput;
}
interface IncludeOptions {
    readable?: boolean;
    messages?: boolean;
}
declare function extract<const T extends Parameter[]>({ context, instructions, parameters, include, data, abortSignal, stream, requestType, forwardedParameters, }: ExtractOptions<T>): Promise<MappedParameterTypes<T>>;

export { extract };
