import { A as ActionInputAvailability, T as TextMessage, b as ActionExecutionMessage, R as ResultMessage, a as Message } from './index-d4614f9b.js';
import * as rxjs from 'rxjs';
import { Subject, ReplaySubject } from 'rxjs';
import { Action, Parameter } from '@copilotkit/shared';

declare enum GuardrailsResultStatus {
    ALLOWED = "allowed",
    DENIED = "denied"
}
declare class GuardrailsResult {
    status: GuardrailsResultStatus;
    reason?: string;
}

declare class ActionInput {
    name: string;
    description: string;
    jsonSchema: string;
    available?: ActionInputAvailability;
}

declare enum RuntimeEventTypes {
    TextMessageStart = "TextMessageStart",
    TextMessageContent = "TextMessageContent",
    TextMessageEnd = "TextMessageEnd",
    ActionExecutionStart = "ActionExecutionStart",
    ActionExecutionArgs = "ActionExecutionArgs",
    ActionExecutionEnd = "ActionExecutionEnd",
    ActionExecutionResult = "ActionExecutionResult",
    AgentStateMessage = "AgentStateMessage",
    MetaEvent = "MetaEvent"
}
declare enum RuntimeMetaEventName {
    LangGraphInterruptEvent = "LangGraphInterruptEvent",
    LangGraphInterruptResumeEvent = "LangGraphInterruptResumeEvent",
    CopilotKitLangGraphInterruptEvent = "CopilotKitLangGraphInterruptEvent"
}
type RunTimeMetaEvent = {
    type: RuntimeEventTypes.MetaEvent;
    name: RuntimeMetaEventName.LangGraphInterruptEvent;
    value: string;
} | {
    type: RuntimeEventTypes.MetaEvent;
    name: RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent;
    data: {
        value: string;
        messages: (TextMessage | ActionExecutionMessage | ResultMessage)[];
    };
} | {
    type: RuntimeEventTypes.MetaEvent;
    name: RuntimeMetaEventName.LangGraphInterruptResumeEvent;
    data: string;
};
type RuntimeEvent = {
    type: RuntimeEventTypes.TextMessageStart;
    messageId: string;
    parentMessageId?: string;
} | {
    type: RuntimeEventTypes.TextMessageContent;
    messageId: string;
    content: string;
} | {
    type: RuntimeEventTypes.TextMessageEnd;
    messageId: string;
} | {
    type: RuntimeEventTypes.ActionExecutionStart;
    actionExecutionId: string;
    actionName: string;
    parentMessageId?: string;
} | {
    type: RuntimeEventTypes.ActionExecutionArgs;
    actionExecutionId: string;
    args: string;
} | {
    type: RuntimeEventTypes.ActionExecutionEnd;
    actionExecutionId: string;
} | {
    type: RuntimeEventTypes.ActionExecutionResult;
    actionName: string;
    actionExecutionId: string;
    result: string;
} | {
    type: RuntimeEventTypes.AgentStateMessage;
    threadId: string;
    agentName: string;
    nodeName: string;
    runId: string;
    active: boolean;
    role: string;
    state: string;
    running: boolean;
} | RunTimeMetaEvent;
type EventSourceCallback = (eventStream$: RuntimeEventSubject) => Promise<void>;
declare class RuntimeEventSubject extends ReplaySubject<RuntimeEvent> {
    constructor();
    sendTextMessageStart({ messageId, parentMessageId, }: {
        messageId: string;
        parentMessageId?: string;
    }): void;
    sendTextMessageContent({ messageId, content }: {
        messageId: string;
        content: string;
    }): void;
    sendTextMessageEnd({ messageId }: {
        messageId: string;
    }): void;
    sendTextMessage(messageId: string, content: string): void;
    sendActionExecutionStart({ actionExecutionId, actionName, parentMessageId, }: {
        actionExecutionId: string;
        actionName: string;
        parentMessageId?: string;
    }): void;
    sendActionExecutionArgs({ actionExecutionId, args, }: {
        actionExecutionId: string;
        args: string;
    }): void;
    sendActionExecutionEnd({ actionExecutionId }: {
        actionExecutionId: string;
    }): void;
    sendActionExecution({ actionExecutionId, actionName, args, parentMessageId, }: {
        actionExecutionId: string;
        actionName: string;
        args: string;
        parentMessageId?: string;
    }): void;
    sendActionExecutionResult({ actionExecutionId, actionName, result, error, }: {
        actionExecutionId: string;
        actionName: string;
        result?: string;
        error?: {
            code: string;
            message: string;
        };
    }): void;
    sendAgentStateMessage({ threadId, agentName, nodeName, runId, active, role, state, running, }: {
        threadId: string;
        agentName: string;
        nodeName: string;
        runId: string;
        active: boolean;
        role: string;
        state: string;
        running: boolean;
    }): void;
}
declare class RuntimeEventSource {
    private eventStream$;
    private callback;
    stream(callback: EventSourceCallback): Promise<void>;
    sendErrorMessageToChat(message?: string): void;
    processRuntimeEvents({ serverSideActions, guardrailsResult$, actionInputsWithoutAgents, threadId, }: {
        serverSideActions: Action<any>[];
        guardrailsResult$?: Subject<GuardrailsResult>;
        actionInputsWithoutAgents: ActionInput[];
        threadId: string;
    }): rxjs.Observable<RuntimeEvent>;
}

declare class ForwardedParametersInput {
    model?: string;
    maxTokens?: number;
    stop?: string[];
    toolChoice?: String;
    toolChoiceFunctionName?: string;
    temperature?: number;
}

/**
 * The extensions input is used to pass additional information to the copilot runtime, specific to a
 * service adapter or agent framework.
 */
declare class ExtensionsInput {
    openaiAssistantAPI?: OpenAIApiAssistantAPIInput;
}
declare class OpenAIApiAssistantAPIInput {
    runId?: string;
    threadId?: string;
}

/**
 * The extensions response is used to receive additional information from the copilot runtime, specific to a
 * service adapter or agent framework.
 *
 * Next time a request to the runtime is made, the extensions response will be included in the request as input.
 */
declare class ExtensionsResponse {
    openaiAssistantAPI?: OpenAIApiAssistantAPIResponse;
}
declare class OpenAIApiAssistantAPIResponse {
    runId?: string;
    threadId?: string;
}

declare class AgentSessionInput {
    agentName: string;
    threadId?: string;
    nodeName?: string;
}

declare class AgentStateInput {
    agentName: string;
    state: string;
    config?: string;
}

interface CopilotRuntimeChatCompletionRequest {
    eventSource: RuntimeEventSource;
    messages: Message[];
    actions: ActionInput[];
    model?: string;
    threadId?: string;
    runId?: string;
    forwardedParameters?: ForwardedParametersInput;
    extensions?: ExtensionsInput;
    agentSession?: AgentSessionInput;
    agentStates?: AgentStateInput[];
}
interface CopilotRuntimeChatCompletionResponse {
    threadId: string;
    runId?: string;
    extensions?: ExtensionsResponse;
}
interface CopilotServiceAdapter {
    process(request: CopilotRuntimeChatCompletionRequest): Promise<CopilotRuntimeChatCompletionResponse>;
}

interface RemoteChainParameters {
    name: string;
    description: string;
    chainUrl: string;
    parameters?: Parameter[];
    parameterType?: "single" | "multi";
}
declare class RemoteChain {
    name: string;
    description: string;
    chainUrl: string;
    parameters?: Parameter[];
    parameterType: "single" | "multi";
    constructor(options: RemoteChainParameters);
    toAction(): Promise<Action<any>>;
    inferLangServeParameters(): Promise<void>;
}

export { ActionInput as A, CopilotRuntimeChatCompletionRequest as C, ExtensionsInput as E, ForwardedParametersInput as F, RemoteChainParameters as R, CopilotRuntimeChatCompletionResponse as a, CopilotServiceAdapter as b, RemoteChain as c, AgentSessionInput as d, AgentStateInput as e, RuntimeEventSource as f, ExtensionsResponse as g };
