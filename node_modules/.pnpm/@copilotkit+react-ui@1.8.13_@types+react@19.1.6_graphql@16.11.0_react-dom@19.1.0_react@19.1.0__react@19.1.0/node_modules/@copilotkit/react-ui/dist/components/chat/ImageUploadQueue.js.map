{"version": 3, "sources": ["../../../src/components/chat/ImageUploadQueue.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface ImageUploadQueueProps {\n  images: Array<{ contentType: string; bytes: string }>;\n  onRemoveImage: (index: number) => void;\n  className?: string;\n}\n\nexport const ImageUploadQueue: React.FC<ImageUploadQueueProps> = ({\n  images,\n  onRemoveImage,\n  className = \"\",\n}) => {\n  if (images.length === 0) return null;\n\n  return (\n    <div\n      className={`copilotKitImageUploadQueue ${className}`}\n      style={{\n        display: \"flex\",\n        flexWrap: \"wrap\",\n        gap: \"8px\",\n        margin: \"8px\",\n        padding: \"8px\",\n      }}\n    >\n      {images.map((image, index) => (\n        <div\n          key={index}\n          className=\"copilotKitImageUploadQueueItem\"\n          style={{\n            position: \"relative\",\n            display: \"inline-block\",\n            width: \"60px\",\n            height: \"60px\",\n            borderRadius: \"4px\",\n            overflow: \"hidden\",\n          }}\n        >\n          {/* eslint-disable-next-line @next/next/no-img-element */}\n          <img\n            src={`data:${image.contentType};base64,${image.bytes}`}\n            alt={`Selected image ${index + 1}`}\n            style={{\n              width: \"100%\",\n              height: \"100%\",\n              objectFit: \"cover\",\n            }}\n          />\n          <button\n            onClick={() => onRemoveImage(index)}\n            className=\"copilotKitImageUploadQueueRemoveButton\"\n            style={{\n              position: \"absolute\",\n              top: \"2px\",\n              right: \"2px\",\n              background: \"rgba(0,0,0,0.6)\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"50%\",\n              width: \"18px\",\n              height: \"18px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              cursor: \"pointer\",\n              fontSize: \"10px\",\n              padding: 0,\n            }}\n          >\n            ✕\n          </button>\n        </div>\n      ))}\n    </div>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BQ;AAnBD,IAAM,mBAAoD,CAAC;AAAA,EAChE;AAAA,EACA;AAAA,EACA,YAAY;AACd,MAAM;AACJ,MAAI,OAAO,WAAW;AAAG,WAAO;AAEhC,SACE;AAAA,IAAC;AAAA;AAAA,MACC,WAAW,8BAA8B;AAAA,MACzC,OAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MAEC,iBAAO,IAAI,CAAC,OAAO,UAClB;AAAA,QAAC;AAAA;AAAA,UAEC,WAAU;AAAA,UACV,OAAO;AAAA,YACL,UAAU;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,UAAU;AAAA,UACZ;AAAA,UAGA;AAAA;AAAA,cAAC;AAAA;AAAA,gBACC,KAAK,QAAQ,MAAM,sBAAsB,MAAM;AAAA,gBAC/C,KAAK,kBAAkB,QAAQ;AAAA,gBAC/B,OAAO;AAAA,kBACL,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,WAAW;AAAA,gBACb;AAAA;AAAA,YACF;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,SAAS,MAAM,cAAc,KAAK;AAAA,gBAClC,WAAU;AAAA,gBACV,OAAO;AAAA,kBACL,UAAU;AAAA,kBACV,KAAK;AAAA,kBACL,OAAO;AAAA,kBACP,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,cAAc;AAAA,kBACd,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,gBAAgB;AAAA,kBAChB,QAAQ;AAAA,kBACR,UAAU;AAAA,kBACV,SAAS;AAAA,gBACX;AAAA,gBACD;AAAA;AAAA,YAED;AAAA;AAAA;AAAA,QA3CK;AAAA,MA4CP,CACD;AAAA;AAAA,EACH;AAEJ;", "names": []}