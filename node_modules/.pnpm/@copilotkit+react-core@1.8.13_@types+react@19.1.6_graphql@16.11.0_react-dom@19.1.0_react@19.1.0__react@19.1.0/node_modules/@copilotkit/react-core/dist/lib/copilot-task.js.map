{"version": 3, "sources": ["../../src/lib/copilot-task.ts", "../../src/types/frontend-action.ts", "../../src/components/copilot-provider/copilotkit.tsx"], "sourcesContent": ["/**\n * This class is used to execute one-off tasks, for example on button press. It can use the context available via [useCopilotReadable](/reference/hooks/useCopilotReadable) and the actions provided by [useCopilotAction](/reference/hooks/useCopilotAction), or you can provide your own context and actions.\n *\n * ## Example\n * In the simplest case, use CopilotTask in the context of your app by giving it instructions on what to do.\n *\n * ```tsx\n * import { CopilotTask, useCopilotContext } from \"@copilotkit/react-core\";\n *\n * export function MyComponent() {\n *   const context = useCopilotContext();\n *\n *   const task = new CopilotTask({\n *     instructions: \"Set a random message\",\n *     actions: [\n *       {\n *         name: \"setMessage\",\n *       description: \"Set the message.\",\n *       argumentAnnotations: [\n *         {\n *           name: \"message\",\n *           type: \"string\",\n *           description:\n *             \"A message to display.\",\n *           required: true,\n *         },\n *       ],\n *      }\n *     ]\n *   });\n *\n *   const executeTask = async () => {\n *     await task.run(context, action);\n *   }\n *\n *   return (\n *     <>\n *       <button onClick={executeTask}>\n *         Execute task\n *       </button>\n *     </>\n *   )\n * }\n * ```\n *\n * Have a look at the [Presentation Example App](https://github.com/CopilotKit/CopilotKit/blob/main/CopilotKit/examples/next-openai/src/app/presentation/page.tsx) for a more complete example.\n */\n\nimport {\n  ActionExecutionMessage,\n  CopilotRuntimeClient,\n  Message,\n  Role,\n  TextMessage,\n  convertGqlOutputToMessages,\n  convertMessagesToGqlInput,\n  filterAgentStateMessages,\n  CopilotRequestType,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { FrontendAction, processActionsForRuntimeRequest } from \"../types/frontend-action\";\nimport { CopilotContextParams } from \"../context\";\nimport { defaultCopilotContextCategories } from \"../components\";\n\nexport interface CopilotTaskConfig {\n  /**\n   * The instructions to be given to the assistant.\n   */\n  instructions: string;\n  /**\n   * An array of action definitions that can be called.\n   */\n  actions?: FrontendAction<any>[];\n  /**\n   * Whether to include the copilot readable context in the task.\n   */\n  includeCopilotReadable?: boolean;\n\n  /**\n   * Whether to include actions defined via useCopilotAction in the task.\n   */\n  includeCopilotActions?: boolean;\n\n  /**\n   * The forwarded parameters to use for the task.\n   */\n  forwardedParameters?: ForwardedParametersInput;\n}\n\nexport class CopilotTask<T = any> {\n  private instructions: string;\n  private actions: FrontendAction<any>[];\n  private includeCopilotReadable: boolean;\n  private includeCopilotActions: boolean;\n  private forwardedParameters?: ForwardedParametersInput;\n  constructor(config: CopilotTaskConfig) {\n    this.instructions = config.instructions;\n    this.actions = config.actions || [];\n    this.includeCopilotReadable = config.includeCopilotReadable !== false;\n    this.includeCopilotActions = config.includeCopilotActions !== false;\n    this.forwardedParameters = config.forwardedParameters;\n  }\n\n  /**\n   * Run the task.\n   * @param context The CopilotContext to use for the task. Use `useCopilotContext` to obtain the current context.\n   * @param data The data to use for the task.\n   */\n  async run(context: CopilotContextParams, data?: T): Promise<void> {\n    const actions = this.includeCopilotActions ? Object.assign({}, context.actions) : {};\n\n    // merge functions into entry points\n    for (const fn of this.actions) {\n      actions[fn.name] = fn;\n    }\n\n    let contextString = \"\";\n\n    if (data) {\n      contextString = (typeof data === \"string\" ? data : JSON.stringify(data)) + \"\\n\\n\";\n    }\n\n    if (this.includeCopilotReadable) {\n      contextString += context.getContextString([], defaultCopilotContextCategories);\n    }\n\n    const systemMessage = new TextMessage({\n      content: taskSystemMessage(contextString, this.instructions),\n      role: Role.System,\n    });\n\n    const messages: Message[] = [systemMessage];\n\n    const runtimeClient = new CopilotRuntimeClient({\n      url: context.copilotApiConfig.chatApiEndpoint,\n      publicApiKey: context.copilotApiConfig.publicApiKey,\n      headers: context.copilotApiConfig.headers,\n      credentials: context.copilotApiConfig.credentials,\n    });\n\n    const response = await runtimeClient\n      .generateCopilotResponse({\n        data: {\n          frontend: {\n            actions: processActionsForRuntimeRequest(Object.values(actions)),\n            url: window.location.href,\n          },\n          messages: convertMessagesToGqlInput(filterAgentStateMessages(messages)),\n          metadata: {\n            requestType: CopilotRequestType.Task,\n          },\n          forwardedParameters: {\n            // if forwardedParameters is provided, use it\n            ...(this.forwardedParameters ?? {}),\n            toolChoice: \"required\",\n          },\n        },\n        properties: context.copilotApiConfig.properties,\n      })\n      .toPromise();\n\n    const functionCallHandler = context.getFunctionCallHandler(actions);\n    const functionCalls = convertGqlOutputToMessages(\n      response.data?.generateCopilotResponse?.messages || [],\n    ).filter((m): m is ActionExecutionMessage => m.isActionExecutionMessage());\n\n    for (const functionCall of functionCalls) {\n      await functionCallHandler({\n        messages,\n        name: functionCall.name,\n        args: functionCall.arguments,\n      });\n    }\n  }\n}\n\nfunction taskSystemMessage(contextString: string, instructions: string): string {\n  return `\nPlease act as an efficient, competent, conscientious, and industrious professional assistant.\n\nHelp the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.\nAlways be polite and respectful, and prefer brevity over verbosity.\n\nThe user has provided you with the following context:\n\\`\\`\\`\n${contextString}\n\\`\\`\\`\n\nThey have also provided you with functions you can call to initiate actions on their behalf.\n\nPlease assist them as best you can.\n\nThis is not a conversation, so please do not ask questions. Just call a function without saying anything else.\n\nThe user has given you the following task to complete:\n\n\\`\\`\\`\n${instructions}\n\\`\\`\\`\n`;\n}\n", "import { ActionInputAvailability } from \"@copilotkit/runtime-client-gql\";\nimport {\n  Action,\n  Parameter,\n  MappedParameterTypes,\n  actionParametersToJsonSchema,\n} from \"@copilotkit/shared\";\nimport React from \"react\";\n\ninterface InProgressState<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  result: undefined;\n}\n\ninterface ExecutingState<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  result: undefined;\n}\n\ninterface CompleteState<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  result: any;\n}\n\ninterface InProgressStateNoArgs<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  result: undefined;\n}\n\ninterface ExecutingStateNoArgs<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  result: undefined;\n}\n\ninterface CompleteStateNoArgs<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  result: any;\n}\n\ninterface InProgressStateWait<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n  result: undefined;\n}\n\ninterface ExecutingStateWait<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: (result: any) => void;\n  respond: (result: any) => void;\n  result: undefined;\n}\n\ninterface CompleteStateWait<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n  result: any;\n}\n\ninterface InProgressStateNoArgsWait<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n  result: undefined;\n}\n\ninterface ExecutingStateNoArgsWait<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: (result: any) => void;\n  respond: (result: any) => void;\n  result: undefined;\n}\n\ninterface CompleteStateNoArgsWait<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n}\n\nexport type ActionRenderProps<T extends Parameter[] | [] = []> =\n  | CompleteState<T>\n  | ExecutingState<T>\n  | InProgressState<T>;\n\nexport type ActionRenderPropsNoArgs<T extends Parameter[] | [] = []> =\n  | CompleteStateNoArgs<T>\n  | ExecutingStateNoArgs<T>\n  | InProgressStateNoArgs<T>;\n\nexport type ActionRenderPropsWait<T extends Parameter[] | [] = []> =\n  | CompleteStateWait<T>\n  | ExecutingStateWait<T>\n  | InProgressStateWait<T>;\n\nexport type ActionRenderPropsNoArgsWait<T extends Parameter[] | [] = []> =\n  | CompleteStateNoArgsWait<T>\n  | ExecutingStateNoArgsWait<T>\n  | InProgressStateNoArgsWait<T>;\n\nexport type CatchAllActionRenderProps<T extends Parameter[] | [] = []> =\n  | (CompleteState<T> & {\n      name: string;\n    })\n  | (ExecutingState<T> & {\n      name: string;\n    })\n  | (InProgressState<T> & {\n      name: string;\n    });\n\nexport type FrontendActionAvailability = \"disabled\" | \"enabled\" | \"remote\" | \"frontend\";\n\nexport type FrontendAction<\n  T extends Parameter[] | [] = [],\n  N extends string = string,\n> = Action<T> & {\n  name: Exclude<N, \"*\">;\n  /**\n   * @deprecated Use `available` instead.\n   */\n  disabled?: boolean;\n  available?: FrontendActionAvailability;\n  pairedAction?: string;\n  followUp?: boolean;\n} & (\n    | {\n        render?:\n          | string\n          | (T extends []\n              ? (props: ActionRenderPropsNoArgs<T>) => string | React.ReactElement\n              : (props: ActionRenderProps<T>) => string | React.ReactElement);\n        /** @deprecated use renderAndWaitForResponse instead */\n        renderAndWait?: never;\n        renderAndWaitForResponse?: never;\n      }\n    | {\n        render?: never;\n        /** @deprecated use renderAndWaitForResponse instead */\n        renderAndWait?: T extends []\n          ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement\n          : (props: ActionRenderPropsWait<T>) => React.ReactElement;\n        renderAndWaitForResponse?: T extends []\n          ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement\n          : (props: ActionRenderPropsWait<T>) => React.ReactElement;\n        handler?: never;\n      }\n  );\n\nexport type CatchAllFrontendAction = {\n  name: \"*\";\n  render: (props: CatchAllActionRenderProps<any>) => React.ReactElement;\n};\n\nexport type RenderFunctionStatus = ActionRenderProps<any>[\"status\"];\n\nexport function processActionsForRuntimeRequest(actions: FrontendAction<any>[]) {\n  const filteredActions = actions\n    .filter(\n      (action) =>\n        action.available !== ActionInputAvailability.Disabled &&\n        action.disabled !== true &&\n        action.name !== \"*\" &&\n        action.available != \"frontend\" &&\n        !action.pairedAction,\n    )\n    .map((action) => {\n      let available: ActionInputAvailability | undefined = ActionInputAvailability.Enabled;\n      if (action.disabled) {\n        available = ActionInputAvailability.Disabled;\n      } else if (action.available === \"disabled\") {\n        available = ActionInputAvailability.Disabled;\n      } else if (action.available === \"remote\") {\n        available = ActionInputAvailability.Remote;\n      }\n      return {\n        name: action.name,\n        description: action.description || \"\",\n        jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters || [])),\n        available,\n      };\n    });\n  return filteredActions;\n}\n", "/**\n * This component will typically wrap your entire application (or a sub-tree of your application where you want to have a copilot). It provides the copilot context to all other components and hooks.\n *\n * ## Example\n *\n * You can find more information about self-hosting CopilotKit [here](/guides/self-hosting).\n *\n * ```tsx\n * import { CopilotKit } from \"@copilotkit/react-core\";\n *\n * <CopilotKit runtimeUrl=\"<your-runtime-url>\">\n *   // ... your app ...\n * </CopilotKit>\n * ```\n */\n\nimport { useCallback, useEffect, useMemo, useRef, useState, SetStateAction } from \"react\";\nimport {\n  CopilotContext,\n  CopilotApiConfig,\n  ChatComponentsCache,\n  AgentSession,\n  AuthState,\n} from \"../../context/copilot-context\";\nimport useTree from \"../../hooks/use-tree\";\nimport { CopilotChatSuggestionConfiguration, DocumentPointer } from \"../../types\";\nimport { flushSync } from \"react-dom\";\nimport {\n  COPILOT_CLOUD_CHAT_URL,\n  CopilotCloudConfig,\n  FunctionCallHandler,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  randomUUID,\n  ConfigurationError,\n  MissingPublicApiKeyError,\n} from \"@copilotkit/shared\";\nimport { FrontendAction } from \"../../types/frontend-action\";\nimport useFlatCategoryStore from \"../../hooks/use-flat-category-store\";\nimport { CopilotKitProps } from \"./copilotkit-props\";\nimport { CoAgentStateRender } from \"../../types/coagent-action\";\nimport { CoagentState } from \"../../types/coagent-state\";\nimport { CopilotMessages } from \"./copilot-messages\";\nimport { ToastProvider } from \"../toast/toast-provider\";\nimport { useCopilotRuntimeClient } from \"../../hooks/use-copilot-runtime-client\";\nimport { shouldShowDevConsole } from \"../../utils\";\nimport { CopilotErrorBoundary } from \"../error-boundary/error-boundary\";\nimport { Agent, ExtensionsInput } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetterArgs,\n} from \"../../types/interrupt-action\";\n\nexport function CopilotKit({ children, ...props }: CopilotKitProps) {\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n  const enabled = shouldShowDevConsole(showDevConsole);\n\n  return (\n    <ToastProvider enabled={enabled}>\n      <CopilotErrorBoundary publicApiKey={props.publicApiKey} showUsageBanner={enabled}>\n        <CopilotKitInternal {...props}>{children}</CopilotKitInternal>\n      </CopilotErrorBoundary>\n    </ToastProvider>\n  );\n}\n\nexport function CopilotKitInternal(cpkProps: CopilotKitProps) {\n  const { children, ...props } = cpkProps;\n\n  /**\n   * This will throw an error if the props are invalid.\n   */\n  validateProps(cpkProps);\n\n  const chatApiEndpoint = props.runtimeUrl || COPILOT_CLOUD_CHAT_URL;\n\n  const [actions, setActions] = useState<Record<string, FrontendAction<any>>>({});\n  const [coAgentStateRenders, setCoAgentStateRenders] = useState<\n    Record<string, CoAgentStateRender<any>>\n  >({});\n\n  const chatComponentsCache = useRef<ChatComponentsCache>({\n    actions: {},\n    coAgentStateRenders: {},\n  });\n\n  const { addElement, removeElement, printTree } = useTree();\n  const [isLoading, setIsLoading] = useState(false);\n  const [chatInstructions, setChatInstructions] = useState(\"\");\n  const [authStates, setAuthStates] = useState<Record<string, AuthState>>({});\n  const [extensions, setExtensions] = useState<ExtensionsInput>({});\n  const [additionalInstructions, setAdditionalInstructions] = useState<string[]>([]);\n\n  const {\n    addElement: addDocument,\n    removeElement: removeDocument,\n    allElements: allDocuments,\n  } = useFlatCategoryStore<DocumentPointer>();\n\n  // Compute all the functions and properties that we need to pass\n\n  const setAction = useCallback((id: string, action: FrontendAction<any>) => {\n    setActions((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: action,\n      };\n    });\n  }, []);\n\n  const removeAction = useCallback((id: string) => {\n    setActions((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const setCoAgentStateRender = useCallback((id: string, stateRender: CoAgentStateRender<any>) => {\n    setCoAgentStateRenders((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: stateRender,\n      };\n    });\n  }, []);\n\n  const removeCoAgentStateRender = useCallback((id: string) => {\n    setCoAgentStateRenders((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const getContextString = useCallback(\n    (documents: DocumentPointer[], categories: string[]) => {\n      const documentsString = documents\n        .map((document) => {\n          return `${document.name} (${document.sourceApplication}):\\n${document.getContents()}`;\n        })\n        .join(\"\\n\\n\");\n\n      const nonDocumentStrings = printTree(categories);\n\n      return `${documentsString}\\n\\n${nonDocumentStrings}`;\n    },\n    [printTree],\n  );\n\n  const addContext = useCallback(\n    (\n      context: string,\n      parentId?: string,\n      categories: string[] = defaultCopilotContextCategories,\n    ) => {\n      return addElement(context, categories, parentId);\n    },\n    [addElement],\n  );\n\n  const removeContext = useCallback(\n    (id: string) => {\n      removeElement(id);\n    },\n    [removeElement],\n  );\n\n  const getFunctionCallHandler = useCallback(\n    (customEntryPoints?: Record<string, FrontendAction<any>>) => {\n      return entryPointsToFunctionCallHandler(Object.values(customEntryPoints || actions));\n    },\n    [actions],\n  );\n\n  const getDocumentsContext = useCallback(\n    (categories: string[]) => {\n      return allDocuments(categories);\n    },\n    [allDocuments],\n  );\n\n  const addDocumentContext = useCallback(\n    (documentPointer: DocumentPointer, categories: string[] = defaultCopilotContextCategories) => {\n      return addDocument(documentPointer, categories);\n    },\n    [addDocument],\n  );\n\n  const removeDocumentContext = useCallback(\n    (documentId: string) => {\n      removeDocument(documentId);\n    },\n    [removeDocument],\n  );\n\n  // get the appropriate CopilotApiConfig from the props\n  const copilotApiConfig: CopilotApiConfig = useMemo(() => {\n    let cloud: CopilotCloudConfig | undefined = undefined;\n    if (props.publicApiKey) {\n      cloud = {\n        guardrails: {\n          input: {\n            restrictToTopic: {\n              enabled: Boolean(props.guardrails_c),\n              validTopics: props.guardrails_c?.validTopics || [],\n              invalidTopics: props.guardrails_c?.invalidTopics || [],\n            },\n          },\n        },\n      };\n    }\n\n    return {\n      publicApiKey: props.publicApiKey,\n      ...(cloud ? { cloud } : {}),\n      chatApiEndpoint: chatApiEndpoint,\n      headers: props.headers || {},\n      properties: props.properties || {},\n      transcribeAudioUrl: props.transcribeAudioUrl,\n      textToSpeechUrl: props.textToSpeechUrl,\n      credentials: props.credentials,\n    };\n  }, [\n    props.publicApiKey,\n    props.headers,\n    props.properties,\n    props.transcribeAudioUrl,\n    props.textToSpeechUrl,\n    props.credentials,\n    props.cloudRestrictToTopic,\n    props.guardrails_c,\n  ]);\n\n  const headers = useMemo(() => {\n    const authHeaders = Object.values(authStates || {}).reduce((acc, state) => {\n      if (state.status === \"authenticated\" && state.authHeaders) {\n        return {\n          ...acc,\n          ...Object.entries(state.authHeaders).reduce(\n            (headers, [key, value]) => ({\n              ...headers,\n              [key.startsWith(\"X-Custom-\") ? key : `X-Custom-${key}`]: value,\n            }),\n            {},\n          ),\n        };\n      }\n      return acc;\n    }, {});\n\n    return {\n      ...(copilotApiConfig.headers || {}),\n      ...(copilotApiConfig.publicApiKey\n        ? { [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: copilotApiConfig.publicApiKey }\n        : {}),\n      ...authHeaders,\n    };\n  }, [copilotApiConfig.headers, copilotApiConfig.publicApiKey, authStates]);\n\n  const runtimeClient = useCopilotRuntimeClient({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    headers,\n    credentials: copilotApiConfig.credentials,\n  });\n\n  const [chatSuggestionConfiguration, setChatSuggestionConfiguration] = useState<{\n    [key: string]: CopilotChatSuggestionConfiguration;\n  }>({});\n\n  const addChatSuggestionConfiguration = (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => {\n    setChatSuggestionConfiguration((prev) => ({ ...prev, [id]: suggestion }));\n  };\n\n  const removeChatSuggestionConfiguration = (id: string) => {\n    setChatSuggestionConfiguration((prev) => {\n      const { [id]: _, ...rest } = prev;\n      return rest;\n    });\n  };\n\n  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);\n  const [coagentStates, setCoagentStates] = useState<Record<string, CoagentState>>({});\n  const coagentStatesRef = useRef<Record<string, CoagentState>>({});\n  const setCoagentStatesWithRef = useCallback(\n    (\n      value:\n        | Record<string, CoagentState>\n        | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n    ) => {\n      const newValue = typeof value === \"function\" ? value(coagentStatesRef.current) : value;\n      coagentStatesRef.current = newValue;\n      setCoagentStates((prev) => {\n        return newValue;\n      });\n    },\n    [],\n  );\n  const hasLoadedAgents = useRef(false);\n\n  useEffect(() => {\n    if (hasLoadedAgents.current) return;\n\n    const fetchData = async () => {\n      const result = await runtimeClient.availableAgents();\n      if (result.data?.availableAgents) {\n        setAvailableAgents(result.data.availableAgents.agents);\n      }\n      hasLoadedAgents.current = true;\n    };\n    void fetchData();\n  }, []);\n\n  let initialAgentSession: AgentSession | null = null;\n  if (props.agent) {\n    initialAgentSession = {\n      agentName: props.agent,\n    };\n  }\n\n  const [agentSession, setAgentSession] = useState<AgentSession | null>(initialAgentSession);\n\n  // Update agentSession when props.agent changes\n  useEffect(() => {\n    if (props.agent) {\n      setAgentSession({\n        agentName: props.agent,\n      });\n    } else {\n      setAgentSession(null);\n    }\n  }, [props.agent]);\n\n  const [internalThreadId, setInternalThreadId] = useState<string>(props.threadId || randomUUID());\n  const setThreadId = useCallback(\n    (value: SetStateAction<string>) => {\n      if (props.threadId) {\n        throw new Error(\"Cannot call setThreadId() when threadId is provided via props.\");\n      }\n      setInternalThreadId(value);\n    },\n    [props.threadId],\n  );\n\n  // update the internal threadId if the props.threadId changes\n  useEffect(() => {\n    if (props.threadId !== undefined) {\n      setInternalThreadId(props.threadId);\n    }\n  }, [props.threadId]);\n\n  const [runId, setRunId] = useState<string | null>(null);\n\n  const chatAbortControllerRef = useRef<AbortController | null>(null);\n\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n\n  const [langGraphInterruptAction, _setLangGraphInterruptAction] =\n    useState<LangGraphInterruptAction | null>(null);\n  const setLangGraphInterruptAction = useCallback((action: LangGraphInterruptActionSetterArgs) => {\n    _setLangGraphInterruptAction((prev) => {\n      if (prev == null) return action as LangGraphInterruptAction;\n      if (action == null) return null;\n      let event = prev.event;\n      if (action.event) {\n        // @ts-ignore\n        event = { ...prev.event, ...action.event };\n      }\n      return { ...prev, ...action, event };\n    });\n  }, []);\n  const removeLangGraphInterruptAction = useCallback((): void => {\n    setLangGraphInterruptAction(null);\n  }, []);\n\n  return (\n    <CopilotContext.Provider\n      value={{\n        actions,\n        chatComponentsCache,\n        getFunctionCallHandler,\n        setAction,\n        removeAction,\n        coAgentStateRenders,\n        setCoAgentStateRender,\n        removeCoAgentStateRender,\n        getContextString,\n        addContext,\n        removeContext,\n        getDocumentsContext,\n        addDocumentContext,\n        removeDocumentContext,\n        copilotApiConfig: copilotApiConfig,\n        isLoading,\n        setIsLoading,\n        chatSuggestionConfiguration,\n        addChatSuggestionConfiguration,\n        removeChatSuggestionConfiguration,\n        chatInstructions,\n        setChatInstructions,\n        additionalInstructions,\n        setAdditionalInstructions,\n        showDevConsole,\n        coagentStates,\n        setCoagentStates,\n        coagentStatesRef,\n        setCoagentStatesWithRef,\n        agentSession,\n        setAgentSession,\n        runtimeClient,\n        forwardedParameters: props.forwardedParameters || {},\n        agentLock: props.agent || null,\n        threadId: internalThreadId,\n        setThreadId,\n        runId,\n        setRunId,\n        chatAbortControllerRef,\n        availableAgents,\n        authConfig_c: props.authConfig_c,\n        authStates_c: authStates,\n        setAuthStates_c: setAuthStates,\n        extensions,\n        setExtensions,\n        langGraphInterruptAction,\n        setLangGraphInterruptAction,\n        removeLangGraphInterruptAction,\n      }}\n    >\n      <CopilotMessages>{children}</CopilotMessages>\n    </CopilotContext.Provider>\n  );\n}\n\nexport const defaultCopilotContextCategories = [\"global\"];\n\nfunction entryPointsToFunctionCallHandler(actions: FrontendAction<any>[]): FunctionCallHandler {\n  return async ({ name, args }) => {\n    let actionsByFunctionName: Record<string, FrontendAction<any>> = {};\n    for (let action of actions) {\n      actionsByFunctionName[action.name] = action;\n    }\n\n    const action = actionsByFunctionName[name];\n    let result: any = undefined;\n    if (action) {\n      await new Promise<void>((resolve, reject) => {\n        flushSync(async () => {\n          try {\n            result = await action.handler?.(args);\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n      });\n      await new Promise((resolve) => setTimeout(resolve, 20));\n    }\n    return result;\n  };\n}\n\nfunction formatFeatureName(featureName: string): string {\n  return featureName\n    .replace(/_c$/, \"\")\n    .split(\"_\")\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n    .join(\" \");\n}\n\nfunction validateProps(props: CopilotKitProps): never | void {\n  const cloudFeatures = Object.keys(props).filter((key) => key.endsWith(\"_c\"));\n\n  if (!props.runtimeUrl && !props.publicApiKey) {\n    throw new ConfigurationError(\"Missing required prop: 'runtimeUrl' or 'publicApiKey'\");\n  }\n\n  if (cloudFeatures.length > 0 && !props.publicApiKey) {\n    throw new MissingPublicApiKeyError(\n      `Missing required prop: 'publicApiKey' to use cloud features: ${cloudFeatures\n        .map(formatFeatureName)\n        .join(\", \")}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAgDA,IAAAA,6BAWO;;;AC3DP,gCAAwC;AACxC,oBAKO;AAwKA,SAAS,gCAAgC,SAAgC;AAC9E,QAAM,kBAAkB,QACrB;AAAA,IACC,CAAC,WACC,OAAO,cAAc,kDAAwB,YAC7C,OAAO,aAAa,QACpB,OAAO,SAAS,OAChB,OAAO,aAAa,cACpB,CAAC,OAAO;AAAA,EACZ,EACC,IAAI,CAAC,WAAW;AACf,QAAI,YAAiD,kDAAwB;AAC7E,QAAI,OAAO,UAAU;AACnB,kBAAY,kDAAwB;AAAA,IACtC,WAAW,OAAO,cAAc,YAAY;AAC1C,kBAAY,kDAAwB;AAAA,IACtC,WAAW,OAAO,cAAc,UAAU;AACxC,kBAAY,kDAAwB;AAAA,IACtC;AACA,WAAO;AAAA,MACL,MAAM,OAAO;AAAA,MACb,aAAa,OAAO,eAAe;AAAA,MACnC,YAAY,KAAK,cAAU,4CAA6B,OAAO,cAAc,CAAC,CAAC,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACF,CAAC;AACH,SAAO;AACT;;;ACzLA,mBAAkF;AAUlF,uBAA0B;AAC1B,IAAAC,iBAQO;AAwBC;AAyXD,IAAM,kCAAkC,CAAC,QAAQ;;;AF3VjD,IAAM,cAAN,MAA2B;AAAA,EAMhC,YAAY,QAA2B;AACrC,SAAK,eAAe,OAAO;AAC3B,SAAK,UAAU,OAAO,WAAW,CAAC;AAClC,SAAK,yBAAyB,OAAO,2BAA2B;AAChE,SAAK,wBAAwB,OAAO,0BAA0B;AAC9D,SAAK,sBAAsB,OAAO;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,IAAI,SAA+B,MAAyB;AAAA;AA5GpE;AA6GI,YAAM,UAAU,KAAK,wBAAwB,OAAO,OAAO,CAAC,GAAG,QAAQ,OAAO,IAAI,CAAC;AAGnF,iBAAW,MAAM,KAAK,SAAS;AAC7B,gBAAQ,GAAG,IAAI,IAAI;AAAA,MACrB;AAEA,UAAI,gBAAgB;AAEpB,UAAI,MAAM;AACR,yBAAiB,OAAO,SAAS,WAAW,OAAO,KAAK,UAAU,IAAI,KAAK;AAAA,MAC7E;AAEA,UAAI,KAAK,wBAAwB;AAC/B,yBAAiB,QAAQ,iBAAiB,CAAC,GAAG,+BAA+B;AAAA,MAC/E;AAEA,YAAM,gBAAgB,IAAI,uCAAY;AAAA,QACpC,SAAS,kBAAkB,eAAe,KAAK,YAAY;AAAA,QAC3D,MAAM,gCAAK;AAAA,MACb,CAAC;AAED,YAAM,WAAsB,CAAC,aAAa;AAE1C,YAAM,gBAAgB,IAAI,gDAAqB;AAAA,QAC7C,KAAK,QAAQ,iBAAiB;AAAA,QAC9B,cAAc,QAAQ,iBAAiB;AAAA,QACvC,SAAS,QAAQ,iBAAiB;AAAA,QAClC,aAAa,QAAQ,iBAAiB;AAAA,MACxC,CAAC;AAED,YAAM,WAAW,MAAM,cACpB,wBAAwB;AAAA,QACvB,MAAM;AAAA,UACJ,UAAU;AAAA,YACR,SAAS,gCAAgC,OAAO,OAAO,OAAO,CAAC;AAAA,YAC/D,KAAK,OAAO,SAAS;AAAA,UACvB;AAAA,UACA,cAAU,0DAA0B,qDAAyB,QAAQ,CAAC;AAAA,UACtE,UAAU;AAAA,YACR,aAAa,8CAAmB;AAAA,UAClC;AAAA,UACA,qBAAqB,kCAEf,UAAK,wBAAL,YAA4B,CAAC,IAFd;AAAA,YAGnB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,YAAY,QAAQ,iBAAiB;AAAA,MACvC,CAAC,EACA,UAAU;AAEb,YAAM,sBAAsB,QAAQ,uBAAuB,OAAO;AAClE,YAAM,oBAAgB;AAAA,UACpB,oBAAS,SAAT,mBAAe,4BAAf,mBAAwC,aAAY,CAAC;AAAA,MACvD,EAAE,OAAO,CAAC,MAAmC,EAAE,yBAAyB,CAAC;AAEzE,iBAAW,gBAAgB,eAAe;AACxC,cAAM,oBAAoB;AAAA,UACxB;AAAA,UACA,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AACF;AAEA,SAAS,kBAAkB,eAAuB,cAA8B;AAC9E,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA;AAAA;AAAA;AAGF;", "names": ["import_runtime_client_gql", "import_shared"]}