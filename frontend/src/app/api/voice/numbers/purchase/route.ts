import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    
    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenant ID from JWT
    const tenantId = session.user.user_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID not found' }, { status: 400 });
    }

    const body = await request.json();
    const { phone_number, forwarding_number } = body;

    if (!phone_number) {
      return NextResponse.json({ error: 'Phone number is required' }, { status: 400 });
    }

    // TODO: Replace with actual Telnyx API call to purchase number
    // For now, simulate a successful purchase
    const purchasedNumber = {
      id: Date.now().toString(),
      tenant_id: tenantId,
      phone_number,
      status: 'pending',
      country_code: 'US',
      number_type: 'local',
      monthly_cost: 100, // in cents
      features: ['voice', 'sms'],
      forwarding_number: forwarding_number || null,
      created_at: new Date().toISOString(),
      purchased_at: new Date().toISOString(),
    };

    // TODO: Save to database
    console.log('Purchased number:', purchasedNumber);

    return NextResponse.json({ 
      success: true,
      number: purchasedNumber 
    });

  } catch (error) {
    console.error('Error purchasing phone number:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
