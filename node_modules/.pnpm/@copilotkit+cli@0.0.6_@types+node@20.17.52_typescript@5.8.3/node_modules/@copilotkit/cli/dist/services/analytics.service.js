// src/services/analytics.service.ts
import { Analytics } from "@segment/analytics-node";
import Conf from "conf";
var AnalyticsService = class {
  constructor(authData) {
    this.authData = authData;
    if (process.env.SEGMENT_DISABLED === "true") {
      return;
    }
    const segmentWriteKey = process.env.SEGMENT_WRITE_KEY || "9Pv6QyExYef2P4hPz4gks6QAvNMi2AOf";
    this.globalProperties = {
      service: "cli"
    };
    if (this.authData?.userId) {
      this.userId = this.authData.userId;
    }
    if (this.authData?.email) {
      this.email = this.authData.email;
      this.globalProperties.email = this.authData.email;
    }
    if (this.authData?.organizationId) {
      this.organizationId = this.authData.organizationId;
    }
    this.segment = new Analytics({
      writeKey: segmentWriteKey,
      disable: process.env.SEGMENT_DISABLE === "true"
    });
    const config = new Conf({ projectName: "CopilotKitCLI" });
    if (!config.get("anonymousId")) {
      config.set("anonymousId", crypto.randomUUID());
    }
  }
  segment;
  globalProperties = {};
  userId;
  email;
  organizationId;
  config = new Conf({ projectName: "CopilotKitCLI" });
  getAnonymousId() {
    const anonymousId = this.config.get("anonymousId");
    if (!anonymousId) {
      const anonymousId2 = crypto.randomUUID();
      this.config.set("anonymousId", anonymousId2);
      return anonymousId2;
    }
    return anonymousId;
  }
  track(event) {
    if (!this.segment) {
      return;
    }
    const payload = {
      userId: this.userId ? this.userId : void 0,
      email: this.email ? this.email : void 0,
      anonymousId: this.getAnonymousId(),
      event: event.event,
      properties: {
        ...this.globalProperties,
        ...event.properties,
        $groups: this.organizationId ? {
          segment_group: this.organizationId
        } : void 0,
        eventProperties: {
          ...event.properties,
          ...this.globalProperties
        }
      }
    };
    this.segment.track(payload);
  }
};
export {
  AnalyticsService
};
//# sourceMappingURL=analytics.service.js.map