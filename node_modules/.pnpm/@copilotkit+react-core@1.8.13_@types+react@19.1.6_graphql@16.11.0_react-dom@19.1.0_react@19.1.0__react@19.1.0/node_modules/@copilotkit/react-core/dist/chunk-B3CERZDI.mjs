import {
  CopilotContext
} from "./chunk-7WG4MNRX.mjs";
import {
  useToast
} from "./chunk-22ENANUU.mjs";

// src/hooks/use-coagent-state-render.ts
import { useRef, useContext, useEffect } from "react";
import { randomId } from "@copilotkit/shared";
function useCoAgentStateRender(action, dependencies) {
  const {
    setCoAgentStateRender,
    removeCoAgentStateRender,
    coAgentStateRenders,
    chatComponentsCache,
    availableAgents
  } = useContext(CopilotContext);
  const idRef = useRef(randomId());
  const { addToast } = useToast();
  useEffect(() => {
    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === action.name)) {
      const message = `(useCoAgentStateRender): Agent "${action.name}" not found. Make sure the agent exists and is properly configured.`;
      addToast({ type: "warning", message });
    }
  }, [availableAgents]);
  const key = `${action.name}-${action.nodeName || "global"}`;
  if (dependencies === void 0) {
    if (coAgentStateRenders[idRef.current]) {
      coAgentStateRenders[idRef.current].handler = action.handler;
      if (typeof action.render === "function") {
        if (chatComponentsCache.current !== null) {
          chatComponentsCache.current.coAgentStateRenders[key] = action.render;
        }
      }
    }
  }
  useEffect(() => {
    const currentId = idRef.current;
    const hasDuplicate = Object.entries(coAgentStateRenders).some(([id, otherAction]) => {
      if (id === currentId)
        return false;
      if (otherAction.name !== action.name)
        return false;
      const hasNodeName = !!action.nodeName;
      const hasOtherNodeName = !!otherAction.nodeName;
      if (!hasNodeName && !hasOtherNodeName)
        return true;
      if (hasNodeName !== hasOtherNodeName)
        return false;
      return action.nodeName === otherAction.nodeName;
    });
    if (hasDuplicate) {
      const message = action.nodeName ? `Found multiple state renders for agent ${action.name} and node ${action.nodeName}. State renders might get overridden` : `Found multiple state renders for agent ${action.name}. State renders might get overridden`;
      addToast({
        type: "warning",
        message,
        id: `dup-action-${action.name}`
      });
    }
  }, [coAgentStateRenders]);
  useEffect(() => {
    setCoAgentStateRender(idRef.current, action);
    if (chatComponentsCache.current !== null && action.render !== void 0) {
      chatComponentsCache.current.coAgentStateRenders[key] = action.render;
    }
    return () => {
      removeCoAgentStateRender(idRef.current);
    };
  }, [
    setCoAgentStateRender,
    removeCoAgentStateRender,
    action.name,
    // include render only if it's a string
    typeof action.render === "string" ? action.render : void 0,
    // dependencies set by the developer
    ...dependencies || []
  ]);
}

export {
  useCoAgentStateRender
};
//# sourceMappingURL=chunk-B3CERZDI.mjs.map