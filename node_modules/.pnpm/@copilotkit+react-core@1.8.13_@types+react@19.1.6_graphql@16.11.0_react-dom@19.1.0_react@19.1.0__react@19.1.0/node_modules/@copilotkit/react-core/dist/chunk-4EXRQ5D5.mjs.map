{"version": 3, "sources": ["../src/hooks/use-copilot-additional-instructions.ts"], "sourcesContent": ["/**\n * `useCopilotAdditionalInstructions` is a React hook that provides additional instructions\n * to the Copilot.\n *\n * ## Usage\n *\n * ### Simple Usage\n *\n * In its most basic usage, useCopilotAdditionalInstructions accepts a single string argument\n * representing the instructions to be added to the Copilot.\n *\n * ```tsx\n * import { useCopilotAdditionalInstructions } from \"@copilotkit/react-core\";\n *\n * export function MyComponent() {\n *   useCopilotAdditionalInstructions({\n *     instructions: \"Do not answer questions about the weather.\",\n *   });\n * }\n * ```\n *\n * ### Conditional Usage\n *\n * You can also conditionally add instructions based on the state of your app.\n *\n * ```tsx\n * import { useCopilotAdditionalInstructions } from \"@copilotkit/react-core\";\n *\n * export function MyComponent() {\n *   const [showInstructions, setShowInstructions] = useState(false);\n *\n *   useCopilotAdditionalInstructions({\n *     available: showInstructions ? \"enabled\" : \"disabled\",\n *     instructions: \"Do not answer questions about the weather.\",\n *   });\n * }\n * ```\n */\nimport { useEffect } from \"react\";\nimport { useCopilotContext } from \"../context/copilot-context\";\n\n/**\n * Options for the useCopilotAdditionalInstructions hook.\n */\nexport interface UseCopilotAdditionalInstructionsOptions {\n  /**\n   * The instructions to be added to the Copilot. Will be added to the instructions like so:\n   *\n   * ```txt\n   * You are a helpful assistant.\n   * Additionally, follow these instructions:\n   * - Do not answer questions about the weather.\n   * - Do not answer questions about the stock market.\n   * ```\n   */\n  instructions: string;\n\n  /**\n   * Whether the instructions are available to the Copilot.\n   */\n  available?: \"enabled\" | \"disabled\";\n}\n\n/**\n * Adds the given instructions to the Copilot context.\n */\nexport function useCopilotAdditionalInstructions(\n  { instructions, available = \"enabled\" }: UseCopilotAdditionalInstructionsOptions,\n  dependencies?: any[],\n) {\n  const { setAdditionalInstructions } = useCopilotContext();\n\n  useEffect(() => {\n    if (available === \"disabled\") return;\n\n    setAdditionalInstructions((prevInstructions) => [...(prevInstructions || []), instructions]);\n\n    return () => {\n      setAdditionalInstructions(\n        (prevInstructions) =>\n          prevInstructions?.filter((instruction) => instruction !== instructions) || [],\n      );\n    };\n  }, [available, instructions, setAdditionalInstructions, ...(dependencies || [])]);\n}\n"], "mappings": ";;;;;AAsCA,SAAS,iBAAiB;AA4BnB,SAAS,iCACd,EAAE,cAAc,YAAY,UAAU,GACtC,cACA;AACA,QAAM,EAAE,0BAA0B,IAAI,kBAAkB;AAExD,YAAU,MAAM;AACd,QAAI,cAAc;AAAY;AAE9B,8BAA0B,CAAC,qBAAqB,CAAC,GAAI,oBAAoB,CAAC,GAAI,YAAY,CAAC;AAE3F,WAAO,MAAM;AACX;AAAA,QACE,CAAC,sBACC,qDAAkB,OAAO,CAAC,gBAAgB,gBAAgB,kBAAiB,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,WAAW,cAAc,2BAA2B,GAAI,gBAAgB,CAAC,CAAE,CAAC;AAClF;", "names": []}