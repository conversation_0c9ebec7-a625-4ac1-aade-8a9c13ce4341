{"version": 3, "sources": ["../src/hooks/use-copilot-authenticated-action.ts"], "sourcesContent": ["import { Parameter } from \"@copilotkit/shared\";\nimport { Fragment, useCallback, useRef } from \"react\";\nimport { useCopilotContext } from \"../context/copilot-context\";\nimport { FrontendAction, ActionRenderProps } from \"../types/frontend-action\";\nimport { useCopilotAction } from \"./use-copilot-action\";\nimport React from \"react\";\n\n/**\n * Hook to create an authenticated action that requires user sign-in before execution.\n *\n * @remarks\n * This feature is only available when using CopilotKit's hosted cloud service.\n * To use this feature, sign up at https://cloud.copilotkit.ai to get your publicApiKey.\n *\n * @param action - The frontend action to be wrapped with authentication\n * @param dependencies - Optional array of dependencies that will trigger recreation of the action when changed\n */\nexport function useCopilotAuthenticatedAction_c<T extends Parameter[]>(\n  action: FrontendAction<T>,\n  dependencies?: any[],\n): void {\n  const { authConfig_c, authStates_c, setAuthStates_c } = useCopilotContext();\n  const pendingActionRef = useRef<ActionRenderProps<Parameter[]> | null>(null);\n\n  const executeAction = useCallback(\n    (props: ActionRenderProps<Parameter[]>) => {\n      if (typeof action.render === \"function\") {\n        return action.render(props);\n      }\n      return action.render || React.createElement(Fragment);\n    },\n    [action],\n  );\n\n  const wrappedRender = useCallback(\n    (props: ActionRenderProps<Parameter[]>): string | React.ReactElement => {\n      const isAuthenticated = Object.values(authStates_c || {}).some(\n        (state) => state.status === \"authenticated\",\n      );\n\n      if (!isAuthenticated) {\n        // Store action details for later execution\n        pendingActionRef.current = props;\n\n        return authConfig_c?.SignInComponent\n          ? React.createElement(authConfig_c.SignInComponent, {\n              onSignInComplete: (authState) => {\n                setAuthStates_c?.((prev) => ({ ...prev, [action.name]: authState }));\n                if (pendingActionRef.current) {\n                  executeAction(pendingActionRef.current);\n                  pendingActionRef.current = null;\n                }\n              },\n            })\n          : React.createElement(Fragment);\n      }\n\n      return executeAction(props);\n    },\n    [action, authStates_c, setAuthStates_c],\n  );\n\n  useCopilotAction(\n    {\n      ...action,\n      render: wrappedRender,\n    } as FrontendAction<T>,\n    dependencies,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;AACA,SAAS,UAAU,aAAa,cAAc;AAI9C,OAAO,WAAW;AAYX,SAAS,gCACd,QACA,cACM;AACN,QAAM,EAAE,cAAc,cAAc,gBAAgB,IAAI,kBAAkB;AAC1E,QAAM,mBAAmB,OAA8C,IAAI;AAE3E,QAAM,gBAAgB;AAAA,IACpB,CAAC,UAA0C;AACzC,UAAI,OAAO,OAAO,WAAW,YAAY;AACvC,eAAO,OAAO,OAAO,KAAK;AAAA,MAC5B;AACA,aAAO,OAAO,UAAU,MAAM,cAAc,QAAQ;AAAA,IACtD;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AAEA,QAAM,gBAAgB;AAAA,IACpB,CAAC,UAAuE;AACtE,YAAM,kBAAkB,OAAO,OAAO,gBAAgB,CAAC,CAAC,EAAE;AAAA,QACxD,CAAC,UAAU,MAAM,WAAW;AAAA,MAC9B;AAEA,UAAI,CAAC,iBAAiB;AAEpB,yBAAiB,UAAU;AAE3B,gBAAO,6CAAc,mBACjB,MAAM,cAAc,aAAa,iBAAiB;AAAA,UAChD,kBAAkB,CAAC,cAAc;AAC/B,+DAAkB,CAAC,SAAU,iCAAK,OAAL,EAAW,CAAC,OAAO,IAAI,GAAG,UAAU;AACjE,gBAAI,iBAAiB,SAAS;AAC5B,4BAAc,iBAAiB,OAAO;AACtC,+BAAiB,UAAU;AAAA,YAC7B;AAAA,UACF;AAAA,QACF,CAAC,IACD,MAAM,cAAc,QAAQ;AAAA,MAClC;AAEA,aAAO,cAAc,KAAK;AAAA,IAC5B;AAAA,IACA,CAAC,QAAQ,cAAc,eAAe;AAAA,EACxC;AAEA;AAAA,IACE,iCACK,SADL;AAAA,MAEE,QAAQ;AAAA,IACV;AAAA,IACA;AAAA,EACF;AACF;", "names": []}