export declare const VIEW_EVENTS_PROMPT = "\nDate format: YYYY-MM-DDThh:mm:ss+00:00\nBased on this event description: 'View my events on Thursday',\noutput a json of the following parameters: \nToday's datetime on UTC time 2023-05-02T10:00:00+00:00, it's Tuesday and timezone\nof the user is -5, take into account the timezone of the user and today's date.\nIf the user is searching for events with a specific title, person or location, put it into the search_query parameter.\n1. time_min \n2. time_max\n3. user_timezone\n4. max_results \n5. search_query \nevent_summary:\n{{\n    \"time_min\": \"2023-05-04T00:00:00-05:00\",\n    \"time_max\": \"2023-05-04T23:59:59-05:00\",\n    \"user_timezone\": \"America/New_York\",\n    \"max_results\": 10,\n    \"search_query\": \"\"\n}}\n\nDate format: YYYY-MM-DDThh:mm:ss+00:00\nBased on this event description: '{query}', output a json of the\nfollowing parameters: \nToday's datetime on UTC time {date}, today it's {dayName} and timezone of the user {u_timezone},\ntake into account the timezone of the user and today's date.\nIf the user is searching for events with a specific title, person or location, put it into the search_query parameter.\n1. time_min \n2. time_max\n3. user_timezone\n4. max_results \n5. search_query \nevent_summary:\n";
