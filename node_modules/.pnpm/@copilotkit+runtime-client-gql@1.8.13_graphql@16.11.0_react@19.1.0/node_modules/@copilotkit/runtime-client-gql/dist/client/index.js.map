{"version": 3, "sources": ["../../src/client/index.ts", "../../src/client/CopilotRuntimeClient.ts", "../../package.json", "../../src/graphql/@generated/graphql.ts", "../../src/graphql/@generated/gql.ts", "../../src/graphql/definitions/mutations.ts", "../../src/graphql/definitions/queries.ts", "../../src/client/types.ts", "../../src/client/conversion.ts"], "sourcesContent": ["export * from \"./CopilotRuntimeClient\";\nexport {\n  convertMessagesToGqlInput,\n  convertGqlOutputToMessages,\n  filterAdjacentAgentStateMessages,\n  filterAgentStateMessages,\n  loadMessagesFromJsonRepresentation,\n} from \"./conversion\";\nexport * from \"./types\";\nexport type { GraphQLError } from \"graphql\";\n", "import { Client, cacheExchange, fetchExchange } from \"@urql/core\";\nimport * as packageJson from \"../../package.json\";\nimport {\n  AvailableAgentsQuery,\n  GenerateCopilotResponseMutation,\n  GenerateCopilotResponseMutationVariables,\n  LoadAgentStateQuery,\n} from \"../graphql/@generated/graphql\";\nimport { generateCopilotResponseMutation } from \"../graphql/definitions/mutations\";\nimport { getAvailableAgentsQuery, loadAgentStateQuery } from \"../graphql/definitions/queries\";\nimport { OperationResultSource, OperationResult } from \"urql\";\nimport {\n  ResolvedCopilotKitError,\n  CopilotKitLowLevelError,\n  CopilotKitError,\n  CopilotKitVersionMismatchError,\n  getPossibleVersionMismatch,\n} from \"@copilotkit/shared\";\n\nconst createFetchFn =\n  (signal?: AbortSignal, handleGQLWarning?: (warning: string) => void) =>\n  async (...args: Parameters<typeof fetch>) => {\n    // @ts-expect-error -- since this is our own header, TS will not recognize\n    const publicApiKey = args[1]?.headers?.[\"x-copilotcloud-public-api-key\"];\n    try {\n      const result = await fetch(args[0], { ...(args[1] ?? {}), signal });\n\n      // No mismatch checking if cloud is being used\n      const mismatch = publicApiKey\n        ? null\n        : await getPossibleVersionMismatch({\n            runtimeVersion: result.headers.get(\"X-CopilotKit-Runtime-Version\")!,\n            runtimeClientGqlVersion: packageJson.version,\n          });\n      if (result.status !== 200) {\n        if (result.status >= 400 && result.status <= 500) {\n          if (mismatch) {\n            throw new CopilotKitVersionMismatchError(mismatch);\n          }\n\n          throw new ResolvedCopilotKitError({ status: result.status });\n        }\n      }\n\n      if (mismatch && handleGQLWarning) {\n        handleGQLWarning(mismatch.message);\n      }\n\n      return result;\n    } catch (error) {\n      // Let abort error pass through. It will be suppressed later\n      if (\n        (error as Error).message.includes(\"BodyStreamBuffer was aborted\") ||\n        (error as Error).message.includes(\"signal is aborted without reason\")\n      ) {\n        throw error;\n      }\n      if (error instanceof CopilotKitError) {\n        throw error;\n      }\n      throw new CopilotKitLowLevelError({ error: error as Error, url: args[0] as string });\n    }\n  };\n\nexport interface CopilotRuntimeClientOptions {\n  url: string;\n  publicApiKey?: string;\n  headers?: Record<string, string>;\n  credentials?: RequestCredentials;\n  handleGQLErrors?: (error: Error) => void;\n  handleGQLWarning?: (warning: string) => void;\n}\n\nexport class CopilotRuntimeClient {\n  client: Client;\n  public handleGQLErrors?: (error: Error) => void;\n  public handleGQLWarning?: (warning: string) => void;\n\n  constructor(options: CopilotRuntimeClientOptions) {\n    const headers: Record<string, string> = {};\n\n    this.handleGQLErrors = options.handleGQLErrors;\n    this.handleGQLWarning = options.handleGQLWarning;\n\n    if (options.headers) {\n      Object.assign(headers, options.headers);\n    }\n\n    if (options.publicApiKey) {\n      headers[\"x-copilotcloud-public-api-key\"] = options.publicApiKey;\n    }\n\n    this.client = new Client({\n      url: options.url,\n      exchanges: [cacheExchange, fetchExchange],\n      fetchOptions: {\n        headers: {\n          ...headers,\n          \"X-CopilotKit-Runtime-Client-GQL-Version\": packageJson.version,\n        },\n        ...(options.credentials ? { credentials: options.credentials } : {}),\n      },\n    });\n  }\n\n  generateCopilotResponse({\n    data,\n    properties,\n    signal,\n  }: {\n    data: GenerateCopilotResponseMutationVariables[\"data\"];\n    properties?: GenerateCopilotResponseMutationVariables[\"properties\"];\n    signal?: AbortSignal;\n  }) {\n    const fetchFn = createFetchFn(signal, this.handleGQLWarning);\n    const result = this.client.mutation<\n      GenerateCopilotResponseMutation,\n      GenerateCopilotResponseMutationVariables\n    >(generateCopilotResponseMutation, { data, properties }, { fetch: fetchFn });\n\n    return result;\n  }\n\n  public asStream<S, T>(source: OperationResultSource<OperationResult<S, { data: T }>>) {\n    const handleGQLErrors = this.handleGQLErrors;\n    return new ReadableStream<S>({\n      start(controller) {\n        source.subscribe(({ data, hasNext, error }) => {\n          if (error) {\n            if (\n              error.message.includes(\"BodyStreamBuffer was aborted\") ||\n              error.message.includes(\"signal is aborted without reason\")\n            ) {\n              // close the stream if there is no next item\n              if (!hasNext) controller.close();\n\n              //suppress this specific error\n              console.warn(\"Abort error suppressed\");\n              return;\n            }\n            controller.error(error);\n            if (handleGQLErrors) {\n              handleGQLErrors(error);\n            }\n          } else {\n            controller.enqueue(data);\n            if (!hasNext) {\n              controller.close();\n            }\n          }\n        });\n      },\n    });\n  }\n\n  availableAgents() {\n    const fetchFn = createFetchFn();\n    return this.client.query<AvailableAgentsQuery>(getAvailableAgentsQuery, {}, { fetch: fetchFn });\n  }\n\n  loadAgentState(data: { threadId: string; agentName: string }) {\n    const fetchFn = createFetchFn();\n    return this.client.query<LoadAgentStateQuery>(\n      loadAgentStateQuery,\n      { data },\n      { fetch: fetchFn },\n    );\n  }\n\n  static removeGraphQLTypename(data: any) {\n    if (Array.isArray(data)) {\n      data.forEach((item) => CopilotRuntimeClient.removeGraphQLTypename(item));\n    } else if (typeof data === \"object\" && data !== null) {\n      delete data.__typename;\n      Object.keys(data).forEach((key) => {\n        if (typeof data[key] === \"object\" && data[key] !== null) {\n          CopilotRuntimeClient.removeGraphQLTypename(data[key]);\n        }\n      });\n    }\n    return data;\n  }\n}\n", "{\n  \"name\": \"@copilotkit/runtime-client-gql\",\n  \"private\": false,\n  \"homepage\": \"https://github.com/CopilotKit/CopilotKit\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/CopilotKit/CopilotKit.git\"\n  },\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"version\": \"1.8.13\",\n  \"sideEffects\": false,\n  \"main\": \"./dist/index.js\",\n  \"module\": \"./dist/index.mjs\",\n  \"exports\": {\n    \".\": {\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\",\n      \"types\": \"./dist/index.d.ts\"\n    }\n  },\n  \"types\": \"./dist/index.d.ts\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"build\": \"pnpm run graphql-codegen && tsup --clean\",\n    \"dev\": \"concurrently \\\"pnpm run graphql-codegen:watch\\\" \\\"tsup --watch --no-splitting\\\"\",\n    \"test\": \"jest --passWithNoTests\",\n    \"check-types\": \"tsc --noEmit\",\n    \"clean\": \"rm -rf .turbo && rm -rf node_modules && rm -rf ./src/graphql/@generated && rm -rf dist && rm -rf .next\",\n    \"graphql-codegen\": \"graphql-codegen -c codegen.ts\",\n    \"graphql-codegen:watch\": \"graphql-codegen -c codegen.ts --watch\",\n    \"link:global\": \"pnpm link --global\",\n    \"unlink:global\": \"pnpm unlink --global\"\n  },\n  \"peerDependencies\": {\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\"\n  },\n  \"devDependencies\": {\n    \"@graphql-codegen/cli\": \"^5.0.2\",\n    \"@graphql-codegen/client-preset\": \"^4.2.6\",\n    \"@graphql-codegen/introspection\": \"^4.0.3\",\n    \"@graphql-codegen/typescript\": \"^4.0.7\",\n    \"@graphql-codegen/typescript-operations\": \"^4.2.1\",\n    \"@graphql-codegen/typescript-urql\": \"^4.0.0\",\n    \"@graphql-codegen/urql-introspection\": \"^3.0.0\",\n    \"@graphql-typed-document-node/core\": \"^3.2.0\",\n    \"@parcel/watcher\": \"^2.4.1\",\n    \"@types/node\": \"^20.12.12\",\n    \"concurrently\": \"^8.2.2\",\n    \"esbuild\": \"^0.23.0\",\n    \"jest\": \"^29.6.4\",\n    \"ts-jest\": \"^29.1.1\",\n    \"ts-node\": \"^10.9.2\",\n    \"tsup\": \"^6.7.0\",\n    \"typescript\": \"^5.4.5\",\n    \"@copilotkit/runtime\": \"workspace:*\",\n    \"graphql\": \"^16.8.1\"\n  },\n  \"dependencies\": {\n    \"@copilotkit/shared\": \"workspace:*\",\n    \"@urql/core\": \"^5.0.3\",\n    \"untruncate-json\": \"^0.0.1\",\n    \"urql\": \"^4.1.0\"\n  },\n  \"keywords\": [\n    \"copilotkit\",\n    \"copilot\",\n    \"react\",\n    \"nextjs\",\n    \"nodejs\",\n    \"ai\",\n    \"assistant\",\n    \"javascript\",\n    \"automation\",\n    \"textarea\"\n  ]\n}\n", "/* eslint-disable */\nimport type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';\nexport type Maybe<T> = T | null;\nexport type InputMaybe<T> = Maybe<T>;\nexport type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };\nexport type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };\nexport type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };\nexport type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };\nexport type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };\n/** All built-in and custom scalars, mapped to their actual values */\nexport type Scalars = {\n  ID: { input: string; output: string; }\n  String: { input: string; output: string; }\n  Boolean: { input: boolean; output: boolean; }\n  Int: { input: number; output: number; }\n  Float: { input: number; output: number; }\n  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format. */\n  DateTimeISO: { input: any; output: any; }\n  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */\n  JSON: { input: any; output: any; }\n  /** The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */\n  JSONObject: { input: any; output: any; }\n};\n\nexport type ActionExecutionMessageInput = {\n  arguments: Scalars['String']['input'];\n  name: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  /** @deprecated This field will be removed in a future version */\n  scope?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type ActionExecutionMessageOutput = BaseMessageOutput & {\n  __typename?: 'ActionExecutionMessageOutput';\n  arguments: Array<Scalars['String']['output']>;\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  name: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  /** @deprecated This field will be removed in a future version */\n  scope?: Maybe<Scalars['String']['output']>;\n  status: MessageStatus;\n};\n\nexport type ActionInput = {\n  available?: InputMaybe<ActionInputAvailability>;\n  description: Scalars['String']['input'];\n  jsonSchema: Scalars['String']['input'];\n  name: Scalars['String']['input'];\n};\n\n/** The availability of the frontend action */\nexport enum ActionInputAvailability {\n  Disabled = 'disabled',\n  Enabled = 'enabled',\n  Remote = 'remote'\n}\n\nexport type Agent = {\n  __typename?: 'Agent';\n  description: Scalars['String']['output'];\n  id: Scalars['String']['output'];\n  name: Scalars['String']['output'];\n};\n\nexport type AgentSessionInput = {\n  agentName: Scalars['String']['input'];\n  nodeName?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type AgentStateInput = {\n  agentName: Scalars['String']['input'];\n  config?: InputMaybe<Scalars['String']['input']>;\n  state: Scalars['String']['input'];\n};\n\nexport type AgentStateMessageInput = {\n  active: Scalars['Boolean']['input'];\n  agentName: Scalars['String']['input'];\n  nodeName: Scalars['String']['input'];\n  role: MessageRole;\n  runId: Scalars['String']['input'];\n  running: Scalars['Boolean']['input'];\n  state: Scalars['String']['input'];\n  threadId: Scalars['String']['input'];\n};\n\nexport type AgentStateMessageOutput = BaseMessageOutput & {\n  __typename?: 'AgentStateMessageOutput';\n  active: Scalars['Boolean']['output'];\n  agentName: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  nodeName: Scalars['String']['output'];\n  role: MessageRole;\n  runId: Scalars['String']['output'];\n  running: Scalars['Boolean']['output'];\n  state: Scalars['String']['output'];\n  status: MessageStatus;\n  threadId: Scalars['String']['output'];\n};\n\nexport type AgentsResponse = {\n  __typename?: 'AgentsResponse';\n  agents: Array<Agent>;\n};\n\nexport type BaseMessageOutput = {\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  status: MessageStatus;\n};\n\nexport type BaseMetaEvent = {\n  name: MetaEventName;\n  type: Scalars['String']['output'];\n};\n\nexport type BaseResponseStatus = {\n  code: ResponseStatusCode;\n};\n\nexport type CloudInput = {\n  guardrails?: InputMaybe<GuardrailsInput>;\n};\n\nexport type CopilotKitLangGraphInterruptEvent = BaseMetaEvent & {\n  __typename?: 'CopilotKitLangGraphInterruptEvent';\n  data: CopilotKitLangGraphInterruptEventData;\n  name: MetaEventName;\n  response?: Maybe<Scalars['String']['output']>;\n  type: Scalars['String']['output'];\n};\n\nexport type CopilotKitLangGraphInterruptEventData = {\n  __typename?: 'CopilotKitLangGraphInterruptEventData';\n  messages: Array<BaseMessageOutput>;\n  value: Scalars['String']['output'];\n};\n\n/** The type of Copilot request */\nexport enum CopilotRequestType {\n  Chat = 'Chat',\n  Suggestion = 'Suggestion',\n  Task = 'Task',\n  TextareaCompletion = 'TextareaCompletion',\n  TextareaPopover = 'TextareaPopover'\n}\n\nexport type CopilotResponse = {\n  __typename?: 'CopilotResponse';\n  extensions?: Maybe<ExtensionsResponse>;\n  messages: Array<BaseMessageOutput>;\n  metaEvents?: Maybe<Array<BaseMetaEvent>>;\n  runId?: Maybe<Scalars['String']['output']>;\n  status: ResponseStatus;\n  threadId: Scalars['String']['output'];\n};\n\nexport type ExtensionsInput = {\n  openaiAssistantAPI?: InputMaybe<OpenAiApiAssistantApiInput>;\n};\n\nexport type ExtensionsResponse = {\n  __typename?: 'ExtensionsResponse';\n  openaiAssistantAPI?: Maybe<OpenAiApiAssistantApiResponse>;\n};\n\nexport type FailedMessageStatus = {\n  __typename?: 'FailedMessageStatus';\n  code: MessageStatusCode;\n  reason: Scalars['String']['output'];\n};\n\nexport type FailedResponseStatus = BaseResponseStatus & {\n  __typename?: 'FailedResponseStatus';\n  code: ResponseStatusCode;\n  details?: Maybe<Scalars['JSON']['output']>;\n  reason: FailedResponseStatusReason;\n};\n\nexport enum FailedResponseStatusReason {\n  GuardrailsValidationFailed = 'GUARDRAILS_VALIDATION_FAILED',\n  MessageStreamInterrupted = 'MESSAGE_STREAM_INTERRUPTED',\n  UnknownError = 'UNKNOWN_ERROR'\n}\n\nexport type ForwardedParametersInput = {\n  maxTokens?: InputMaybe<Scalars['Float']['input']>;\n  model?: InputMaybe<Scalars['String']['input']>;\n  stop?: InputMaybe<Array<Scalars['String']['input']>>;\n  temperature?: InputMaybe<Scalars['Float']['input']>;\n  toolChoice?: InputMaybe<Scalars['String']['input']>;\n  toolChoiceFunctionName?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type FrontendInput = {\n  actions: Array<ActionInput>;\n  toDeprecate_fullContext?: InputMaybe<Scalars['String']['input']>;\n  url?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type GenerateCopilotResponseInput = {\n  agentSession?: InputMaybe<AgentSessionInput>;\n  agentState?: InputMaybe<AgentStateInput>;\n  agentStates?: InputMaybe<Array<AgentStateInput>>;\n  cloud?: InputMaybe<CloudInput>;\n  extensions?: InputMaybe<ExtensionsInput>;\n  forwardedParameters?: InputMaybe<ForwardedParametersInput>;\n  frontend: FrontendInput;\n  messages: Array<MessageInput>;\n  metaEvents?: InputMaybe<Array<MetaEventInput>>;\n  metadata: GenerateCopilotResponseMetadataInput;\n  runId?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type GenerateCopilotResponseMetadataInput = {\n  requestType?: InputMaybe<CopilotRequestType>;\n};\n\nexport type GuardrailsInput = {\n  inputValidationRules: GuardrailsRuleInput;\n};\n\nexport type GuardrailsRuleInput = {\n  allowList?: InputMaybe<Array<Scalars['String']['input']>>;\n  denyList?: InputMaybe<Array<Scalars['String']['input']>>;\n};\n\nexport type ImageMessageInput = {\n  bytes: Scalars['String']['input'];\n  format: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  role: MessageRole;\n};\n\nexport type ImageMessageOutput = BaseMessageOutput & {\n  __typename?: 'ImageMessageOutput';\n  bytes: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  format: Scalars['String']['output'];\n  id: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  role: MessageRole;\n  status: MessageStatus;\n};\n\nexport type LangGraphInterruptEvent = BaseMetaEvent & {\n  __typename?: 'LangGraphInterruptEvent';\n  name: MetaEventName;\n  response?: Maybe<Scalars['String']['output']>;\n  type: Scalars['String']['output'];\n  value: Scalars['String']['output'];\n};\n\nexport type LoadAgentStateInput = {\n  agentName: Scalars['String']['input'];\n  threadId: Scalars['String']['input'];\n};\n\nexport type LoadAgentStateResponse = {\n  __typename?: 'LoadAgentStateResponse';\n  messages: Scalars['String']['output'];\n  state: Scalars['String']['output'];\n  threadExists: Scalars['Boolean']['output'];\n  threadId: Scalars['String']['output'];\n};\n\nexport type MessageInput = {\n  actionExecutionMessage?: InputMaybe<ActionExecutionMessageInput>;\n  agentStateMessage?: InputMaybe<AgentStateMessageInput>;\n  createdAt: Scalars['DateTimeISO']['input'];\n  id: Scalars['String']['input'];\n  imageMessage?: InputMaybe<ImageMessageInput>;\n  resultMessage?: InputMaybe<ResultMessageInput>;\n  textMessage?: InputMaybe<TextMessageInput>;\n};\n\n/** The role of the message */\nexport enum MessageRole {\n  Assistant = 'assistant',\n  Developer = 'developer',\n  System = 'system',\n  Tool = 'tool',\n  User = 'user'\n}\n\nexport type MessageStatus = FailedMessageStatus | PendingMessageStatus | SuccessMessageStatus;\n\nexport enum MessageStatusCode {\n  Failed = 'Failed',\n  Pending = 'Pending',\n  Success = 'Success'\n}\n\nexport type MetaEventInput = {\n  messages?: InputMaybe<Array<MessageInput>>;\n  name: MetaEventName;\n  response?: InputMaybe<Scalars['String']['input']>;\n  value: Scalars['String']['input'];\n};\n\n/** Meta event types */\nexport enum MetaEventName {\n  CopilotKitLangGraphInterruptEvent = 'CopilotKitLangGraphInterruptEvent',\n  LangGraphInterruptEvent = 'LangGraphInterruptEvent'\n}\n\nexport type Mutation = {\n  __typename?: 'Mutation';\n  generateCopilotResponse: CopilotResponse;\n};\n\n\nexport type MutationGenerateCopilotResponseArgs = {\n  data: GenerateCopilotResponseInput;\n  properties?: InputMaybe<Scalars['JSONObject']['input']>;\n};\n\nexport type OpenAiApiAssistantApiInput = {\n  runId?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type OpenAiApiAssistantApiResponse = {\n  __typename?: 'OpenAIApiAssistantAPIResponse';\n  runId?: Maybe<Scalars['String']['output']>;\n  threadId?: Maybe<Scalars['String']['output']>;\n};\n\nexport type PendingMessageStatus = {\n  __typename?: 'PendingMessageStatus';\n  code: MessageStatusCode;\n};\n\nexport type PendingResponseStatus = BaseResponseStatus & {\n  __typename?: 'PendingResponseStatus';\n  code: ResponseStatusCode;\n};\n\nexport type Query = {\n  __typename?: 'Query';\n  availableAgents: AgentsResponse;\n  hello: Scalars['String']['output'];\n  loadAgentState: LoadAgentStateResponse;\n};\n\n\nexport type QueryLoadAgentStateArgs = {\n  data: LoadAgentStateInput;\n};\n\nexport type ResponseStatus = FailedResponseStatus | PendingResponseStatus | SuccessResponseStatus;\n\nexport enum ResponseStatusCode {\n  Failed = 'Failed',\n  Pending = 'Pending',\n  Success = 'Success'\n}\n\nexport type ResultMessageInput = {\n  actionExecutionId: Scalars['String']['input'];\n  actionName: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  result: Scalars['String']['input'];\n};\n\nexport type ResultMessageOutput = BaseMessageOutput & {\n  __typename?: 'ResultMessageOutput';\n  actionExecutionId: Scalars['String']['output'];\n  actionName: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  result: Scalars['String']['output'];\n  status: MessageStatus;\n};\n\nexport type SuccessMessageStatus = {\n  __typename?: 'SuccessMessageStatus';\n  code: MessageStatusCode;\n};\n\nexport type SuccessResponseStatus = BaseResponseStatus & {\n  __typename?: 'SuccessResponseStatus';\n  code: ResponseStatusCode;\n};\n\nexport type TextMessageInput = {\n  content: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  role: MessageRole;\n};\n\nexport type TextMessageOutput = BaseMessageOutput & {\n  __typename?: 'TextMessageOutput';\n  content: Array<Scalars['String']['output']>;\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  role: MessageRole;\n  status: MessageStatus;\n};\n\nexport type GenerateCopilotResponseMutationVariables = Exact<{\n  data: GenerateCopilotResponseInput;\n  properties?: InputMaybe<Scalars['JSONObject']['input']>;\n}>;\n\n\nexport type GenerateCopilotResponseMutation = { __typename?: 'Mutation', generateCopilotResponse: { __typename?: 'CopilotResponse', threadId: string, runId?: string | null, extensions?: { __typename?: 'ExtensionsResponse', openaiAssistantAPI?: { __typename?: 'OpenAIApiAssistantAPIResponse', runId?: string | null, threadId?: string | null } | null } | null, messages: Array<{ __typename: 'ActionExecutionMessageOutput', id: string, createdAt: any, name: string, arguments: Array<string>, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'AgentStateMessageOutput', id: string, createdAt: any, threadId: string, state: string, running: boolean, agentName: string, nodeName: string, runId: string, active: boolean, role: MessageRole, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ImageMessageOutput', id: string, createdAt: any, format: string, bytes: string, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ResultMessageOutput', id: string, createdAt: any, result: string, actionExecutionId: string, actionName: string, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'TextMessageOutput', id: string, createdAt: any, content: Array<string>, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } }>, metaEvents?: Array<{ __typename?: 'CopilotKitLangGraphInterruptEvent', type: string, name: MetaEventName, data: { __typename?: 'CopilotKitLangGraphInterruptEventData', value: string, messages: Array<{ __typename: 'ActionExecutionMessageOutput', id: string, createdAt: any, name: string, arguments: Array<string>, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'AgentStateMessageOutput', id: string, createdAt: any, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ImageMessageOutput', id: string, createdAt: any, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ResultMessageOutput', id: string, createdAt: any, result: string, actionExecutionId: string, actionName: string, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'TextMessageOutput', id: string, createdAt: any, content: Array<string>, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } }> } } | { __typename?: 'LangGraphInterruptEvent', type: string, name: MetaEventName, value: string }> | null } & ({ __typename?: 'CopilotResponse', status: { __typename?: 'FailedResponseStatus', code: ResponseStatusCode, reason: FailedResponseStatusReason, details?: any | null } | { __typename?: 'PendingResponseStatus', code: ResponseStatusCode } | { __typename?: 'SuccessResponseStatus', code: ResponseStatusCode } } | { __typename?: 'CopilotResponse', status?: never }) };\n\nexport type AvailableAgentsQueryVariables = Exact<{ [key: string]: never; }>;\n\n\nexport type AvailableAgentsQuery = { __typename?: 'Query', availableAgents: { __typename?: 'AgentsResponse', agents: Array<{ __typename?: 'Agent', name: string, id: string, description: string }> } };\n\nexport type LoadAgentStateQueryVariables = Exact<{\n  data: LoadAgentStateInput;\n}>;\n\n\nexport type LoadAgentStateQuery = { __typename?: 'Query', loadAgentState: { __typename?: 'LoadAgentStateResponse', threadId: string, threadExists: boolean, state: string, messages: string } };\n\n\nexport const GenerateCopilotResponseDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"mutation\",\"name\":{\"kind\":\"Name\",\"value\":\"generateCopilotResponse\"},\"variableDefinitions\":[{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}},\"type\":{\"kind\":\"NonNullType\",\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"GenerateCopilotResponseInput\"}}}},{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"}},\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"JSONObject\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"generateCopilotResponse\"},\"arguments\":[{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}}},{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"extensions\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"openaiAssistantAPI\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"CopilotResponse\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseResponseStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedResponseStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"details\"}}]}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"__typename\"}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"createdAt\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"SuccessMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"PendingMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"TextMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"content\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}]},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ImageMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"format\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"bytes\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ActionExecutionMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"arguments\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}]},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ResultMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"result\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionExecutionId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionName\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"AgentStateMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"state\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"running\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"agentName\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"nodeName\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"active\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"metaEvents\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"LangGraphInterruptEvent\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"type\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"value\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"CopilotKitLangGraphInterruptEvent\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"type\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"__typename\"}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"createdAt\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"SuccessMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"PendingMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"TextMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"content\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ActionExecutionMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"arguments\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ResultMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"result\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionExecutionId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionName\"}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"value\"}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<GenerateCopilotResponseMutation, GenerateCopilotResponseMutationVariables>;\nexport const AvailableAgentsDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"query\",\"name\":{\"kind\":\"Name\",\"value\":\"availableAgents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"availableAgents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"agents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"description\"}}]}}]}}]}}]} as unknown as DocumentNode<AvailableAgentsQuery, AvailableAgentsQueryVariables>;\nexport const LoadAgentStateDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"query\",\"name\":{\"kind\":\"Name\",\"value\":\"loadAgentState\"},\"variableDefinitions\":[{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}},\"type\":{\"kind\":\"NonNullType\",\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"LoadAgentStateInput\"}}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"loadAgentState\"},\"arguments\":[{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadExists\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"state\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"}}]}}]}}]} as unknown as DocumentNode<LoadAgentStateQuery, LoadAgentStateQueryVariables>;", "/* eslint-disable */\nimport * as types from './graphql';\nimport type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';\n\n/**\n * Map of all GraphQL operations in the project.\n *\n * This map has several performance disadvantages:\n * 1. It is not tree-shakeable, so it will include all operations in the project.\n * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.\n * 3. It does not support dead code elimination, so it will add unused operations.\n *\n * Therefore it is highly recommended to use the babel or swc plugin for production.\n */\nconst documents = {\n    \"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\": types.GenerateCopilotResponseDocument,\n    \"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\": types.AvailableAgentsDocument,\n    \"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\": types.LoadAgentStateDocument,\n};\n\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n *\n *\n * @example\n * ```ts\n * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);\n * ```\n *\n * The query argument is unknown!\n * Please regenerate the types.\n */\nexport function graphql(source: string): unknown;\n\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n */\nexport function graphql(source: \"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"): (typeof documents)[\"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"];\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n */\nexport function graphql(source: \"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\"): (typeof documents)[\"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\"];\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n */\nexport function graphql(source: \"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\"): (typeof documents)[\"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\"];\n\nexport function graphql(source: string) {\n  return (documents as any)[source] ?? {};\n}\n\nexport type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;", "import { graphql } from \"../@generated/gql\";\n\nexport const generateCopilotResponseMutation = graphql(/** GraphQL **/ `\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\n    generateCopilotResponse(data: $data, properties: $properties) {\n      threadId\n      runId\n      extensions {\n        openaiAssistantAPI {\n          runId\n          threadId\n        }\n      }\n      ... on CopilotResponse @defer {\n        status {\n          ... on BaseResponseStatus {\n            code\n          }\n          ... on FailedResponseStatus {\n            reason\n            details\n          }\n        }\n      }\n      messages @stream {\n        __typename\n        ... on BaseMessageOutput {\n          id\n          createdAt\n        }\n        ... on BaseMessageOutput @defer {\n          status {\n            ... on SuccessMessageStatus {\n              code\n            }\n            ... on FailedMessageStatus {\n              code\n              reason\n            }\n            ... on PendingMessageStatus {\n              code\n            }\n          }\n        }\n        ... on TextMessageOutput {\n          content @stream\n          role\n          parentMessageId\n        }\n        ... on ImageMessageOutput {\n          format\n          bytes\n          role\n          parentMessageId\n        }\n        ... on ActionExecutionMessageOutput {\n          name\n          arguments @stream\n          parentMessageId\n        }\n        ... on ResultMessageOutput {\n          result\n          actionExecutionId\n          actionName\n        }\n        ... on AgentStateMessageOutput {\n          threadId\n          state\n          running\n          agentName\n          nodeName\n          runId\n          active\n          role\n        }\n      }\n      metaEvents @stream {\n        ... on LangGraphInterruptEvent {\n          type\n          name\n          value\n        }\n\n        ... on CopilotKitLangGraphInterruptEvent {\n          type\n          name\n          data {\n            messages {\n              __typename\n              ... on BaseMessageOutput {\n                id\n                createdAt\n              }\n              ... on BaseMessageOutput @defer {\n                status {\n                  ... on SuccessMessageStatus {\n                    code\n                  }\n                  ... on FailedMessageStatus {\n                    code\n                    reason\n                  }\n                  ... on PendingMessageStatus {\n                    code\n                  }\n                }\n              }\n              ... on TextMessageOutput {\n                content\n                role\n                parentMessageId\n              }\n              ... on ActionExecutionMessageOutput {\n                name\n                arguments\n                parentMessageId\n              }\n              ... on ResultMessageOutput {\n                result\n                actionExecutionId\n                actionName\n              }\n            }\n            value\n          }\n        }\n      }\n    }\n  }\n`);\n", "import { graphql } from \"../@generated/gql\";\n\nexport const getAvailableAgentsQuery = graphql(/** GraphQL **/ `\n  query availableAgents {\n    availableAgents {\n      agents {\n        name\n        id\n        description\n      }\n    }\n  }\n`);\n\nexport const loadAgentStateQuery = graphql(/** GraphQL **/ `\n  query loadAgentState($data: LoadAgentStateInput!) {\n    loadAgentState(data: $data) {\n      threadId\n      threadExists\n      state\n      messages\n    }\n  }\n`);\n", "import { randomId } from \"@copilotkit/shared\";\nimport {\n  ActionExecutionMessageInput,\n  MessageRole,\n  MessageStatus,\n  ResultMessageInput,\n  TextMessageInput,\n  BaseMessageOutput,\n  AgentStateMessageInput,\n  MessageStatusCode,\n  LangGraphInterruptEvent as GqlLangGraphInterruptEvent,\n  MetaEventName,\n  CopilotKitLangGraphInterruptEvent as GqlCopilotKitLangGraphInterruptEvent,\n  ImageMessageInput,\n} from \"../graphql/@generated/graphql\";\nimport { parseJson } from \"@copilotkit/shared\";\n\ntype MessageType =\n  | \"TextMessage\"\n  | \"ActionExecutionMessage\"\n  | \"ResultMessage\"\n  | \"AgentStateMessage\"\n  | \"ImageMessage\";\n\nexport class Message {\n  type: MessageType;\n  id: BaseMessageOutput[\"id\"];\n  createdAt: BaseMessageOutput[\"createdAt\"];\n  status: MessageStatus;\n\n  constructor(props: any) {\n    props.id ??= randomId();\n    props.status ??= { code: MessageStatusCode.Success };\n    props.createdAt ??= new Date();\n    Object.assign(this, props);\n  }\n\n  isTextMessage(): this is TextMessage {\n    return this.type === \"TextMessage\";\n  }\n\n  isActionExecutionMessage(): this is ActionExecutionMessage {\n    return this.type === \"ActionExecutionMessage\";\n  }\n\n  isResultMessage(): this is ResultMessage {\n    return this.type === \"ResultMessage\";\n  }\n\n  isAgentStateMessage(): this is AgentStateMessage {\n    return this.type === \"AgentStateMessage\";\n  }\n\n  isImageMessage(): this is ImageMessage {\n    return this.type === \"ImageMessage\";\n  }\n}\n\n// alias Role to MessageRole\nexport const Role = MessageRole;\n\n// when constructing any message, the base fields are optional\ntype MessageConstructorOptions = Partial<Message>;\n\ntype TextMessageConstructorOptions = MessageConstructorOptions & TextMessageInput;\n\nexport class TextMessage extends Message implements TextMessageConstructorOptions {\n  role: TextMessageInput[\"role\"];\n  content: TextMessageInput[\"content\"];\n  parentMessageId: TextMessageInput[\"parentMessageId\"];\n\n  constructor(props: TextMessageConstructorOptions) {\n    super(props);\n    this.type = \"TextMessage\";\n  }\n}\n\ntype ActionExecutionMessageConstructorOptions = MessageConstructorOptions &\n  Omit<ActionExecutionMessageInput, \"arguments\"> & {\n    arguments: Record<string, any>;\n  };\n\nexport class ActionExecutionMessage\n  extends Message\n  implements Omit<ActionExecutionMessageInput, \"arguments\" | \"scope\">\n{\n  name: ActionExecutionMessageInput[\"name\"];\n  arguments: Record<string, any>;\n  parentMessageId: ActionExecutionMessageInput[\"parentMessageId\"];\n  constructor(props: ActionExecutionMessageConstructorOptions) {\n    super(props);\n    this.type = \"ActionExecutionMessage\";\n  }\n}\n\ntype ResultMessageConstructorOptions = MessageConstructorOptions & ResultMessageInput;\n\nexport class ResultMessage extends Message implements ResultMessageConstructorOptions {\n  actionExecutionId: ResultMessageInput[\"actionExecutionId\"];\n  actionName: ResultMessageInput[\"actionName\"];\n  result: ResultMessageInput[\"result\"];\n\n  constructor(props: ResultMessageConstructorOptions) {\n    super(props);\n    this.type = \"ResultMessage\";\n  }\n\n  static decodeResult(result: string): any {\n    return parseJson(result, result);\n  }\n\n  static encodeResult(result: any): string {\n    if (result === undefined) {\n      return \"\";\n    } else if (typeof result === \"string\") {\n      return result;\n    } else {\n      return JSON.stringify(result);\n    }\n  }\n}\n\nexport class AgentStateMessage extends Message implements Omit<AgentStateMessageInput, \"state\"> {\n  agentName: AgentStateMessageInput[\"agentName\"];\n  state: any;\n  running: AgentStateMessageInput[\"running\"];\n  threadId: AgentStateMessageInput[\"threadId\"];\n  role: AgentStateMessageInput[\"role\"];\n  nodeName: AgentStateMessageInput[\"nodeName\"];\n  runId: AgentStateMessageInput[\"runId\"];\n  active: AgentStateMessageInput[\"active\"];\n\n  constructor(props: any) {\n    super(props);\n    this.type = \"AgentStateMessage\";\n  }\n}\n\ntype ImageMessageConstructorOptions = MessageConstructorOptions & ImageMessageInput;\n\nexport class ImageMessage extends Message implements ImageMessageConstructorOptions {\n  format: ImageMessageInput[\"format\"];\n  bytes: ImageMessageInput[\"bytes\"];\n  role: ImageMessageInput[\"role\"];\n  parentMessageId: ImageMessageInput[\"parentMessageId\"];\n\n  constructor(props: ImageMessageConstructorOptions) {\n    super(props);\n    this.type = \"ImageMessage\";\n  }\n}\n\nexport function langGraphInterruptEvent(\n  eventProps: Omit<LangGraphInterruptEvent, \"name\" | \"type\" | \"__typename\">,\n): LangGraphInterruptEvent {\n  return { ...eventProps, name: MetaEventName.LangGraphInterruptEvent, type: \"MetaEvent\" };\n}\n\nexport type LangGraphInterruptEvent<TValue extends any = any> = GqlLangGraphInterruptEvent & {\n  value: TValue;\n};\n\ntype CopilotKitLangGraphInterruptEvent<TValue extends any = any> =\n  GqlCopilotKitLangGraphInterruptEvent & {\n    data: GqlCopilotKitLangGraphInterruptEvent[\"data\"] & { value: TValue };\n  };\n\nexport type MetaEvent = LangGraphInterruptEvent | CopilotKitLangGraphInterruptEvent;\n", "import {\n  GenerateCopilotResponseMutation,\n  MessageInput,\n  MessageStatusCode,\n} from \"../graphql/@generated/graphql\";\nimport {\n  ActionExecutionMessage,\n  AgentStateMessage,\n  Message,\n  ResultMessage,\n  TextMessage,\n  ImageMessage,\n} from \"./types\";\n\nimport untruncate<PERSON><PERSON> from \"untruncate-json\";\nimport { parseJson } from \"@copilotkit/shared\";\n\nexport function filterAgentStateMessages(messages: Message[]): Message[] {\n  return messages.filter((message) => !message.isAgentStateMessage());\n}\n\nexport function convertMessagesToGqlInput(messages: Message[]): MessageInput[] {\n  return messages.map((message) => {\n    if (message.isTextMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        textMessage: {\n          content: message.content,\n          role: message.role as any,\n          parentMessageId: message.parentMessageId,\n        },\n      };\n    } else if (message.isActionExecutionMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        actionExecutionMessage: {\n          name: message.name,\n          arguments: JSON.stringify(message.arguments),\n          parentMessageId: message.parentMessageId,\n        },\n      };\n    } else if (message.isResultMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        resultMessage: {\n          result: message.result,\n          actionExecutionId: message.actionExecutionId,\n          actionName: message.actionName,\n        },\n      };\n    } else if (message.isAgentStateMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        agentStateMessage: {\n          threadId: message.threadId,\n          role: message.role,\n          agentName: message.agentName,\n          nodeName: message.nodeName,\n          runId: message.runId,\n          active: message.active,\n          running: message.running,\n          state: JSON.stringify(message.state),\n        },\n      };\n    } else if (message.isImageMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        imageMessage: {\n          format: message.format,\n          bytes: message.bytes,\n          role: message.role as any,\n          parentMessageId: message.parentMessageId,\n        },\n      };\n    } else {\n      throw new Error(\"Unknown message type\");\n    }\n  });\n}\n\nexport function filterAdjacentAgentStateMessages(\n  messages: GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"],\n): GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"] {\n  const filteredMessages: GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"] =\n    [];\n\n  messages.forEach((message, i) => {\n    // keep all other message types\n    if (message.__typename !== \"AgentStateMessageOutput\") {\n      filteredMessages.push(message);\n    } else {\n      const prevAgentStateMessageIndex = filteredMessages.findIndex(\n        // TODO: also check runId\n        (m) => m.__typename === \"AgentStateMessageOutput\" && m.agentName === message.agentName,\n      );\n      if (prevAgentStateMessageIndex === -1) {\n        filteredMessages.push(message);\n      } else {\n        filteredMessages[prevAgentStateMessageIndex] = message;\n      }\n    }\n  });\n\n  return filteredMessages;\n}\n\nexport function convertGqlOutputToMessages(\n  messages: GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"],\n): Message[] {\n  return messages.map((message) => {\n    if (message.__typename === \"TextMessageOutput\") {\n      return new TextMessage({\n        id: message.id,\n        role: message.role,\n        content: message.content.join(\"\"),\n        parentMessageId: message.parentMessageId,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    } else if (message.__typename === \"ActionExecutionMessageOutput\") {\n      return new ActionExecutionMessage({\n        id: message.id,\n        name: message.name,\n        arguments: getPartialArguments(message.arguments),\n        parentMessageId: message.parentMessageId,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    } else if (message.__typename === \"ResultMessageOutput\") {\n      return new ResultMessage({\n        id: message.id,\n        result: message.result,\n        actionExecutionId: message.actionExecutionId,\n        actionName: message.actionName,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    } else if (message.__typename === \"AgentStateMessageOutput\") {\n      return new AgentStateMessage({\n        id: message.id,\n        threadId: message.threadId,\n        role: message.role,\n        agentName: message.agentName,\n        nodeName: message.nodeName,\n        runId: message.runId,\n        active: message.active,\n        running: message.running,\n        state: parseJson(message.state, {}),\n        createdAt: new Date(),\n      });\n    } else if (message.__typename === \"ImageMessageOutput\") {\n      return new ImageMessage({\n        id: message.id,\n        format: message.format,\n        bytes: message.bytes,\n        role: message.role,\n        parentMessageId: message.parentMessageId,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    }\n\n    throw new Error(\"Unknown message type\");\n  });\n}\n\nexport function loadMessagesFromJsonRepresentation(json: any[]): Message[] {\n  const result: Message[] = [];\n  for (const item of json) {\n    if (\"content\" in item) {\n      result.push(\n        new TextMessage({\n          id: item.id,\n          role: item.role,\n          content: item.content,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    } else if (\"arguments\" in item) {\n      result.push(\n        new ActionExecutionMessage({\n          id: item.id,\n          name: item.name,\n          arguments: item.arguments,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    } else if (\"result\" in item) {\n      result.push(\n        new ResultMessage({\n          id: item.id,\n          result: item.result,\n          actionExecutionId: item.actionExecutionId,\n          actionName: item.actionName,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    } else if (\"state\" in item) {\n      result.push(\n        new AgentStateMessage({\n          id: item.id,\n          threadId: item.threadId,\n          role: item.role,\n          agentName: item.agentName,\n          nodeName: item.nodeName,\n          runId: item.runId,\n          active: item.active,\n          running: item.running,\n          state: item.state,\n          createdAt: item.createdAt || new Date(),\n        }),\n      );\n    } else if (\"format\" in item && \"bytes\" in item) {\n      result.push(\n        new ImageMessage({\n          id: item.id,\n          format: item.format,\n          bytes: item.bytes,\n          role: item.role,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    }\n  }\n  return result;\n}\n\nfunction getPartialArguments(args: string[]) {\n  try {\n    if (!args.length) return {};\n\n    return JSON.parse(untruncateJson(args.join(\"\")));\n  } catch (e) {\n    return {};\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAqD;;;ACWnD,cAAW;;;AC8QN,IAAK,cAAL,kBAAKA,iBAAL;AACL,EAAAA,aAAA,eAAY;AACZ,EAAAA,aAAA,eAAY;AACZ,EAAAA,aAAA,YAAS;AACT,EAAAA,aAAA,UAAO;AACP,EAAAA,aAAA,UAAO;AALG,SAAAA;AAAA,GAAA;AAiJL,IAAM,kCAAkC,EAAC,QAAO,YAAW,eAAc,CAAC,EAAC,QAAO,uBAAsB,aAAY,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,GAAE,uBAAsB,CAAC,EAAC,QAAO,sBAAqB,YAAW,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,QAAO,EAAC,QAAO,eAAc,QAAO,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,+BAA8B,EAAC,EAAC,EAAC,GAAE,EAAC,QAAO,sBAAqB,YAAW,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,GAAE,QAAO,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,GAAE,aAAY,CAAC,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,GAAE,SAAQ,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,EAAC,GAAE,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,GAAE,SAAQ,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,qBAAoB,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,qBAAoB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,KAAI,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,qBAAoB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,+BAA8B,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oCAAmC,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,KAAI,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,+BAA8B,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC;AACloT,IAAM,0BAA0B,EAAC,QAAO,YAAW,eAAc,CAAC,EAAC,QAAO,uBAAsB,aAAY,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,KAAI,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,cAAa,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC;AAC7mB,IAAM,yBAAyB,EAAC,QAAO,YAAW,eAAc,CAAC,EAAC,QAAO,uBAAsB,aAAY,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,iBAAgB,GAAE,uBAAsB,CAAC,EAAC,QAAO,sBAAqB,YAAW,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,QAAO,EAAC,QAAO,eAAc,QAAO,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,iBAAgB,GAAE,aAAY,CAAC,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,GAAE,SAAQ,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,eAAc,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC;;;AC9Zz7B,IAAM,YAAY;AAAA,EACd,m/FAAy/F;AAAA,EACz/F,4IAAkJ;AAAA,EAClJ,6KAAmL;AACvL;AA6BO,SAAS,QAAQ,QAAgB;AACtC,SAAQ,UAAkB,MAAM,KAAK,CAAC;AACxC;;;AC/CO,IAAM,kCAAkC;AAAA;AAAA,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+HtE;;;AC/HM,IAAM,0BAA0B;AAAA;AAAA,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU9D;AAEM,IAAM,sBAAsB;AAAA;AAAA,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAS1D;;;ALZD,oBAMO;AAEP,IAAM,gBACJ,CAAC,QAAsB,qBACvB,UAAU,SAAmC;AArB/C;AAuBI,QAAM,gBAAe,gBAAK,CAAC,MAAN,mBAAS,YAAT,mBAAmB;AACxC,MAAI;AACF,UAAM,SAAS,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,GAAI,KAAK,CAAC,KAAK,CAAC,GAAI,OAAO,CAAC;AAGlE,UAAM,WAAW,eACb,OACA,UAAM,0CAA2B;AAAA,MAC/B,gBAAgB,OAAO,QAAQ,IAAI,8BAA8B;AAAA,MACjE,yBAAqC;AAAA,IACvC,CAAC;AACL,QAAI,OAAO,WAAW,KAAK;AACzB,UAAI,OAAO,UAAU,OAAO,OAAO,UAAU,KAAK;AAChD,YAAI,UAAU;AACZ,gBAAM,IAAI,6CAA+B,QAAQ;AAAA,QACnD;AAEA,cAAM,IAAI,sCAAwB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAAA,MAC7D;AAAA,IACF;AAEA,QAAI,YAAY,kBAAkB;AAChC,uBAAiB,SAAS,OAAO;AAAA,IACnC;AAEA,WAAO;AAAA,EACT,SAAS,OAAP;AAEA,QACG,MAAgB,QAAQ,SAAS,8BAA8B,KAC/D,MAAgB,QAAQ,SAAS,kCAAkC,GACpE;AACA,YAAM;AAAA,IACR;AACA,QAAI,iBAAiB,+BAAiB;AACpC,YAAM;AAAA,IACR;AACA,UAAM,IAAI,sCAAwB,EAAE,OAAuB,KAAK,KAAK,CAAC,EAAY,CAAC;AAAA,EACrF;AACF;AAWK,IAAM,uBAAN,MAA2B;AAAA,EAKhC,YAAY,SAAsC;AAChD,UAAM,UAAkC,CAAC;AAEzC,SAAK,kBAAkB,QAAQ;AAC/B,SAAK,mBAAmB,QAAQ;AAEhC,QAAI,QAAQ,SAAS;AACnB,aAAO,OAAO,SAAS,QAAQ,OAAO;AAAA,IACxC;AAEA,QAAI,QAAQ,cAAc;AACxB,cAAQ,+BAA+B,IAAI,QAAQ;AAAA,IACrD;AAEA,SAAK,SAAS,IAAI,mBAAO;AAAA,MACvB,KAAK,QAAQ;AAAA,MACb,WAAW,CAAC,2BAAe,yBAAa;AAAA,MACxC,cAAc;AAAA,QACZ,SAAS;AAAA,UACP,GAAG;AAAA,UACH,2CAAuD;AAAA,QACzD;AAAA,QACA,GAAI,QAAQ,cAAc,EAAE,aAAa,QAAQ,YAAY,IAAI,CAAC;AAAA,MACpE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,UAAU,cAAc,QAAQ,KAAK,gBAAgB;AAC3D,UAAM,SAAS,KAAK,OAAO,SAGzB,iCAAiC,EAAE,MAAM,WAAW,GAAG,EAAE,OAAO,QAAQ,CAAC;AAE3E,WAAO;AAAA,EACT;AAAA,EAEO,SAAe,QAAgE;AACpF,UAAM,kBAAkB,KAAK;AAC7B,WAAO,IAAI,eAAkB;AAAA,MAC3B,MAAM,YAAY;AAChB,eAAO,UAAU,CAAC,EAAE,MAAM,SAAS,MAAM,MAAM;AAC7C,cAAI,OAAO;AACT,gBACE,MAAM,QAAQ,SAAS,8BAA8B,KACrD,MAAM,QAAQ,SAAS,kCAAkC,GACzD;AAEA,kBAAI,CAAC;AAAS,2BAAW,MAAM;AAG/B,sBAAQ,KAAK,wBAAwB;AACrC;AAAA,YACF;AACA,uBAAW,MAAM,KAAK;AACtB,gBAAI,iBAAiB;AACnB,8BAAgB,KAAK;AAAA,YACvB;AAAA,UACF,OAAO;AACL,uBAAW,QAAQ,IAAI;AACvB,gBAAI,CAAC,SAAS;AACZ,yBAAW,MAAM;AAAA,YACnB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,cAAc;AAC9B,WAAO,KAAK,OAAO,MAA4B,yBAAyB,CAAC,GAAG,EAAE,OAAO,QAAQ,CAAC;AAAA,EAChG;AAAA,EAEA,eAAe,MAA+C;AAC5D,UAAM,UAAU,cAAc;AAC9B,WAAO,KAAK,OAAO;AAAA,MACjB;AAAA,MACA,EAAE,KAAK;AAAA,MACP,EAAE,OAAO,QAAQ;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,OAAO,sBAAsB,MAAW;AACtC,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAK,QAAQ,CAAC,SAAS,qBAAqB,sBAAsB,IAAI,CAAC;AAAA,IACzE,WAAW,OAAO,SAAS,YAAY,SAAS,MAAM;AACpD,aAAO,KAAK;AACZ,aAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AACjC,YAAI,OAAO,KAAK,GAAG,MAAM,YAAY,KAAK,GAAG,MAAM,MAAM;AACvD,+BAAqB,sBAAsB,KAAK,GAAG,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;;;AMtLA,IAAAC,iBAAyB;AAezB,IAAAC,iBAA0B;AASnB,IAAM,UAAN,MAAc;AAAA,EAMnB,YAAY,OAAY;AACtB,UAAM,OAAN,MAAM,SAAO,yBAAS;AACtB,UAAM,WAAN,MAAM,SAAW,EAAE,8BAAgC;AACnD,UAAM,cAAN,MAAM,YAAc,oBAAI,KAAK;AAC7B,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AAAA,EAEA,gBAAqC;AACnC,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,2BAA2D;AACzD,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,kBAAyC;AACvC,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,sBAAiD;AAC/C,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,iBAAuC;AACrC,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AAGO,IAAM,OAAO;AAOb,IAAM,cAAN,cAA0B,QAAiD;AAAA,EAKhF,YAAY,OAAsC;AAChD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAOO,IAAM,yBAAN,cACG,QAEV;AAAA,EAIE,YAAY,OAAiD;AAC3D,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAIO,IAAM,gBAAN,cAA4B,QAAmD;AAAA,EAKpF,YAAY,OAAwC;AAClD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,aAAa,QAAqB;AACvC,eAAO,0BAAU,QAAQ,MAAM;AAAA,EACjC;AAAA,EAEA,OAAO,aAAa,QAAqB;AACvC,QAAI,WAAW,QAAW;AACxB,aAAO;AAAA,IACT,WAAW,OAAO,WAAW,UAAU;AACrC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,UAAU,MAAM;AAAA,IAC9B;AAAA,EACF;AACF;AAEO,IAAM,oBAAN,cAAgC,QAAyD;AAAA,EAU9F,YAAY,OAAY;AACtB,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAIO,IAAM,eAAN,cAA2B,QAAkD;AAAA,EAMlF,YAAY,OAAuC;AACjD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAEO,SAAS,wBACd,YACyB;AACzB,SAAO,EAAE,GAAG,YAAY,+DAA6C,MAAM,YAAY;AACzF;;;AC9IA,6BAA2B;AAC3B,IAAAC,iBAA0B;AAEnB,SAAS,yBAAyB,UAAgC;AACvE,SAAO,SAAS,OAAO,CAAC,YAAY,CAAC,QAAQ,oBAAoB,CAAC;AACpE;AAEO,SAAS,0BAA0B,UAAqC;AAC7E,SAAO,SAAS,IAAI,CAAC,YAAY;AAC/B,QAAI,QAAQ,cAAc,GAAG;AAC3B,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,aAAa;AAAA,UACX,SAAS,QAAQ;AAAA,UACjB,MAAM,QAAQ;AAAA,UACd,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,wBAAwB;AAAA,UACtB,MAAM,QAAQ;AAAA,UACd,WAAW,KAAK,UAAU,QAAQ,SAAS;AAAA,UAC3C,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,gBAAgB,GAAG;AACpC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,eAAe;AAAA,UACb,QAAQ,QAAQ;AAAA,UAChB,mBAAmB,QAAQ;AAAA,UAC3B,YAAY,QAAQ;AAAA,QACtB;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,oBAAoB,GAAG;AACxC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,mBAAmB;AAAA,UACjB,UAAU,QAAQ;AAAA,UAClB,MAAM,QAAQ;AAAA,UACd,WAAW,QAAQ;AAAA,UACnB,UAAU,QAAQ;AAAA,UAClB,OAAO,QAAQ;AAAA,UACf,QAAQ,QAAQ;AAAA,UAChB,SAAS,QAAQ;AAAA,UACjB,OAAO,KAAK,UAAU,QAAQ,KAAK;AAAA,QACrC;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,eAAe,GAAG;AACnC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,cAAc;AAAA,UACZ,QAAQ,QAAQ;AAAA,UAChB,OAAO,QAAQ;AAAA,UACf,MAAM,QAAQ;AAAA,UACd,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AAEO,SAAS,iCACd,UACwE;AACxE,QAAM,mBACJ,CAAC;AAEH,WAAS,QAAQ,CAAC,SAAS,MAAM;AAE/B,QAAI,QAAQ,eAAe,2BAA2B;AACpD,uBAAiB,KAAK,OAAO;AAAA,IAC/B,OAAO;AACL,YAAM,6BAA6B,iBAAiB;AAAA;AAAA,QAElD,CAAC,MAAM,EAAE,eAAe,6BAA6B,EAAE,cAAc,QAAQ;AAAA,MAC/E;AACA,UAAI,+BAA+B,IAAI;AACrC,yBAAiB,KAAK,OAAO;AAAA,MAC/B,OAAO;AACL,yBAAiB,0BAA0B,IAAI;AAAA,MACjD;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEO,SAAS,2BACd,UACW;AACX,SAAO,SAAS,IAAI,CAAC,YAAY;AAC/B,QAAI,QAAQ,eAAe,qBAAqB;AAC9C,aAAO,IAAI,YAAY;AAAA,QACrB,IAAI,QAAQ;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ,QAAQ,KAAK,EAAE;AAAA,QAChC,iBAAiB,QAAQ;AAAA,QACzB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,gCAAgC;AAChE,aAAO,IAAI,uBAAuB;AAAA,QAChC,IAAI,QAAQ;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,WAAW,oBAAoB,QAAQ,SAAS;AAAA,QAChD,iBAAiB,QAAQ;AAAA,QACzB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,uBAAuB;AACvD,aAAO,IAAI,cAAc;AAAA,QACvB,IAAI,QAAQ;AAAA,QACZ,QAAQ,QAAQ;AAAA,QAChB,mBAAmB,QAAQ;AAAA,QAC3B,YAAY,QAAQ;AAAA,QACpB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,2BAA2B;AAC3D,aAAO,IAAI,kBAAkB;AAAA,QAC3B,IAAI,QAAQ;AAAA,QACZ,UAAU,QAAQ;AAAA,QAClB,MAAM,QAAQ;AAAA,QACd,WAAW,QAAQ;AAAA,QACnB,UAAU,QAAQ;AAAA,QAClB,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ;AAAA,QAChB,SAAS,QAAQ;AAAA,QACjB,WAAO,0BAAU,QAAQ,OAAO,CAAC,CAAC;AAAA,QAClC,WAAW,oBAAI,KAAK;AAAA,MACtB,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,sBAAsB;AACtD,aAAO,IAAI,aAAa;AAAA,QACtB,IAAI,QAAQ;AAAA,QACZ,QAAQ,QAAQ;AAAA,QAChB,OAAO,QAAQ;AAAA,QACf,MAAM,QAAQ;AAAA,QACd,iBAAiB,QAAQ;AAAA,QACzB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH;AAEA,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC,CAAC;AACH;AAEO,SAAS,mCAAmC,MAAwB;AACzE,QAAM,SAAoB,CAAC;AAC3B,aAAW,QAAQ,MAAM;AACvB,QAAI,aAAa,MAAM;AACrB,aAAO;AAAA,QACL,IAAI,YAAY;AAAA,UACd,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,UACd,iBAAiB,KAAK;AAAA,UACtB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF,WAAW,eAAe,MAAM;AAC9B,aAAO;AAAA,QACL,IAAI,uBAAuB;AAAA,UACzB,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,iBAAiB,KAAK;AAAA,UACtB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF,WAAW,YAAY,MAAM;AAC3B,aAAO;AAAA,QACL,IAAI,cAAc;AAAA,UAChB,IAAI,KAAK;AAAA,UACT,QAAQ,KAAK;AAAA,UACb,mBAAmB,KAAK;AAAA,UACxB,YAAY,KAAK;AAAA,UACjB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF,WAAW,WAAW,MAAM;AAC1B,aAAO;AAAA,QACL,IAAI,kBAAkB;AAAA,UACpB,IAAI,KAAK;AAAA,UACT,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK;AAAA,UACd,OAAO,KAAK;AAAA,UACZ,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,IACF,WAAW,YAAY,QAAQ,WAAW,MAAM;AAC9C,aAAO;AAAA,QACL,IAAI,aAAa;AAAA,UACf,IAAI,KAAK;AAAA,UACT,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,iBAAiB,KAAK;AAAA,UACtB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,MAAgB;AAC3C,MAAI;AACF,QAAI,CAAC,KAAK;AAAQ,aAAO,CAAC;AAE1B,WAAO,KAAK,UAAM,uBAAAC,SAAe,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EACjD,SAAS,GAAP;AACA,WAAO,CAAC;AAAA,EACV;AACF;", "names": ["MessageRole", "import_shared", "import_shared", "import_shared", "untruncate<PERSON><PERSON>"]}