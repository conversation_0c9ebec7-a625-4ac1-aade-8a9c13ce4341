import {
  copilotRuntimeNodeHttpEndpoint,
  getRuntimeInstanceTelemetryInfo,
  telemetry_client_default
} from "./chunk-XOH4CYV6.mjs";
import {
  __name
} from "./chunk-FHD4JECV.mjs";

// src/lib/integrations/node-express/index.ts
function copilotRuntimeNodeExpressEndpoint(options) {
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "node-express"
    }
  });
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  return copilotRuntimeNodeHttpEndpoint(options);
}
__name(copilotRuntimeNodeExpressEndpoint, "copilotRuntimeNodeExpressEndpoint");

export {
  copilotRuntimeNodeExpressEndpoint
};
//# sourceMappingURL=chunk-HEMZD7QI.mjs.map