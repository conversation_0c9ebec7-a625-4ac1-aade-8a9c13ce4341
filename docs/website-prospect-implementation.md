# Website Prospect Implementation Guide

## Overview

Prospect management is implemented in the **website repo (ailexlaw.com)**, not in this PI Lawyer AI repo. This document provides the implementation details for the website team.

## Architecture Decision

**Website Repo (ailexlaw.com):**
- Handles prospect signups
- Email verification
- Marketing attribution
- GDPR compliance
- Newsletter management

**PI Lawyer AI Repo (this repo):**
- User registration/authentication
- Automatic prospect conversion
- Tenant management
- Legal AI features

## Database Schema (Already Created)

The following tables exist in the Supabase database and are used by the website:

### `public.prospects`
```sql
CREATE TABLE public.prospects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    signup_source TEXT DEFAULT 'website',
    signup_page TEXT,
    utm_source TEXT,
    utm_medium TEXT,
    utm_campaign TEXT,
    practice_area_interest TEXT[],
    case_urgency TEXT,
    estimated_case_value TEXT,
    newsletter_subscribed BOOLEAN DEFAULT true,
    marketing_consent BOOLEAN DEFAULT false,
    communication_preferences JSONB,
    status TEXT DEFAULT 'active',
    email_verified BOOLEAN DEFAULT false,
    email_verification_token TEXT,
    gdpr_consent BOOLEAN NOT NULL,
    gdpr_consent_date TIMESTAMPTZ,
    gdpr_consent_ip TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- ... other fields
);
```

### `public.prospect_interactions`
```sql
CREATE TABLE public.prospect_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prospect_id UUID REFERENCES prospects(id),
    interaction_type TEXT,
    subject TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Conversion Process

When prospects register for PI Lawyer AI, the conversion happens automatically:

### 1. User Registration
User registers in PI Lawyer AI with same email used for prospect signup.

### 2. Automatic Conversion Trigger
```sql
-- This trigger exists in PI Lawyer AI database
CREATE TRIGGER trigger_prospect_conversion
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_prospect_conversion();
```

### 3. Data Preservation
Original prospect data is preserved in the user's settings:
```json
{
  "prospect_data": {
    "signup_source": "website",
    "practice_area_interest": ["personal_injury"],
    "case_urgency": "within_month",
    "original_signup_date": "2025-01-27T10:00:00Z"
  }
}
```

## Website Implementation Requirements

### Environment Variables
```bash
# Supabase (same database as PI Lawyer AI)
NEXT_PUBLIC_SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# Email service
RESEND_API_KEY=your_resend_key

# Bot protection
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_key
TURNSTILE_SECRET_KEY=your_turnstile_secret

# Website config
NEXT_PUBLIC_SITE_URL=https://www.ailexlaw.com
```

### Required API Endpoints (Website Repo)
1. `POST /api/prospects/signup` - Handle form submissions
2. `POST /api/prospects/verify-email` - Send verification emails
3. `PUT /api/prospects/verify-email` - Verify email tokens
4. `GET /api/prospects/manage` - Admin dashboard (optional)

### Required Pages (Website Repo)
1. `/verify-email` - Email verification page
2. Signup forms on various pages
3. Admin dashboard (optional)

### Data Validation Schema
```typescript
const prospectSignupSchema = z.object({
  email: z.string().email(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  signupSource: z.enum(['website', 'landing_page', 'referral', 'social_media', 'advertisement', 'other']).default('website'),
  practiceAreaInterest: z.array(z.enum(['personal_injury', 'criminal_defense', 'family_law'])).default([]),
  caseUrgency: z.enum(['immediate', 'within_month', 'within_quarter', 'planning_ahead']).optional(),
  gdprConsent: z.boolean(),
  // ... other fields
});
```

## Integration Points

### From Website → PI Lawyer AI
- Prospects stored in shared Supabase database
- Automatic conversion when user registers
- No API calls between systems needed

### From PI Lawyer AI → Website
- Access to prospect data via shared database
- Conversion tracking and analytics
- User settings contain original prospect context

## Benefits of This Architecture

1. **Independence** - Website works regardless of PI Lawyer AI status
2. **Performance** - No cross-domain API calls for forms
3. **Simplicity** - Each system owns its domain
4. **Reliability** - No single point of failure
5. **Maintenance** - Clear separation of concerns

## Monitoring & Analytics

### Conversion Tracking
```sql
-- Track conversion rates by source
SELECT 
  signup_source,
  COUNT(*) as total_prospects,
  COUNT(*) FILTER (WHERE status = 'converted') as conversions,
  ROUND(COUNT(*) FILTER (WHERE status = 'converted') * 100.0 / COUNT(*), 2) as conversion_rate
FROM public.prospects 
GROUP BY signup_source;
```

### Email Verification Rates
```sql
-- Track email verification rates
SELECT 
  COUNT(*) FILTER (WHERE email_verified = true) * 100.0 / COUNT(*) as verification_rate
FROM public.prospects 
WHERE created_at >= NOW() - INTERVAL '30 days';
```

## Support & Maintenance

### Website Team Responsibilities
- Prospect signup forms and UX
- Email verification workflow
- Marketing attribution tracking
- GDPR compliance features
- Newsletter management

### PI Lawyer AI Team Responsibilities
- User registration and authentication
- Prospect conversion logic
- User data management
- Platform features

## Future Considerations

This architecture allows for:
- Independent scaling of website and platform
- Different deployment schedules
- Team ownership boundaries
- Technology stack flexibility

The prospect management system in the website repo can evolve independently while maintaining seamless integration with the PI Lawyer AI platform through the shared database and conversion triggers.
