import {
  CopilotModal
} from "./chunk-FWKHST6I.mjs";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-MRXNTQOX.mjs";

// src/components/chat/Popup.tsx
import { jsx } from "react/jsx-runtime";
function CopilotPopup(props) {
  props = __spreadProps(__spreadValues({}, props), {
    className: props.className ? props.className + " copilotKitPopup" : "copilotKitPopup"
  });
  return /* @__PURE__ */ jsx(CopilotModal, __spreadProps(__spreadValues({}, props), { children: props.children }));
}

export {
  CopilotPopup
};
//# sourceMappingURL=chunk-QEZ3YYLO.mjs.map