{"version": 3, "sources": ["../src/graphql/definitions/mutations.ts"], "sourcesContent": ["import { graphql } from \"../@generated/gql\";\n\nexport const generateCopilotResponseMutation = graphql(/** GraphQL **/ `\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\n    generateCopilotResponse(data: $data, properties: $properties) {\n      threadId\n      runId\n      extensions {\n        openaiAssistantAPI {\n          runId\n          threadId\n        }\n      }\n      ... on CopilotResponse @defer {\n        status {\n          ... on BaseResponseStatus {\n            code\n          }\n          ... on FailedResponseStatus {\n            reason\n            details\n          }\n        }\n      }\n      messages @stream {\n        __typename\n        ... on BaseMessageOutput {\n          id\n          createdAt\n        }\n        ... on BaseMessageOutput @defer {\n          status {\n            ... on SuccessMessageStatus {\n              code\n            }\n            ... on FailedMessageStatus {\n              code\n              reason\n            }\n            ... on PendingMessageStatus {\n              code\n            }\n          }\n        }\n        ... on TextMessageOutput {\n          content @stream\n          role\n          parentMessageId\n        }\n        ... on ImageMessageOutput {\n          format\n          bytes\n          role\n          parentMessageId\n        }\n        ... on ActionExecutionMessageOutput {\n          name\n          arguments @stream\n          parentMessageId\n        }\n        ... on ResultMessageOutput {\n          result\n          actionExecutionId\n          actionName\n        }\n        ... on AgentStateMessageOutput {\n          threadId\n          state\n          running\n          agentName\n          nodeName\n          runId\n          active\n          role\n        }\n      }\n      metaEvents @stream {\n        ... on LangGraphInterruptEvent {\n          type\n          name\n          value\n        }\n\n        ... on CopilotKitLangGraphInterruptEvent {\n          type\n          name\n          data {\n            messages {\n              __typename\n              ... on BaseMessageOutput {\n                id\n                createdAt\n              }\n              ... on BaseMessageOutput @defer {\n                status {\n                  ... on SuccessMessageStatus {\n                    code\n                  }\n                  ... on FailedMessageStatus {\n                    code\n                    reason\n                  }\n                  ... on PendingMessageStatus {\n                    code\n                  }\n                }\n              }\n              ... on TextMessageOutput {\n                content\n                role\n                parentMessageId\n              }\n              ... on ActionExecutionMessageOutput {\n                name\n                arguments\n                parentMessageId\n              }\n              ... on ResultMessageOutput {\n                result\n                actionExecutionId\n                actionName\n              }\n            }\n            value\n          }\n        }\n      }\n    }\n  }\n`);\n"], "mappings": ";;;;;AAEO,IAAM,kCAAkC;AAAA;AAAA,EAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+HtE;", "names": []}