{"version": 3, "sources": ["../src/types/frontend-action.ts"], "sourcesContent": ["import { ActionInputAvailability } from \"@copilotkit/runtime-client-gql\";\nimport {\n  Action,\n  Parameter,\n  MappedParameterTypes,\n  actionParametersToJsonSchema,\n} from \"@copilotkit/shared\";\nimport React from \"react\";\n\ninterface InProgressState<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  result: undefined;\n}\n\ninterface ExecutingState<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  result: undefined;\n}\n\ninterface CompleteState<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  result: any;\n}\n\ninterface InProgressStateNoArgs<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  result: undefined;\n}\n\ninterface ExecutingStateNoArgs<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  result: undefined;\n}\n\ninterface CompleteStateNoArgs<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  result: any;\n}\n\ninterface InProgressStateWait<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n  result: undefined;\n}\n\ninterface ExecutingStateWait<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: (result: any) => void;\n  respond: (result: any) => void;\n  result: undefined;\n}\n\ninterface CompleteStateWait<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n  result: any;\n}\n\ninterface InProgressStateNoArgsWait<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n  result: undefined;\n}\n\ninterface ExecutingStateNoArgsWait<T extends Parameter[] | [] = []> {\n  status: \"executing\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: (result: any) => void;\n  respond: (result: any) => void;\n  result: undefined;\n}\n\ninterface CompleteStateNoArgsWait<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n  /** @deprecated use respond instead */\n  handler: undefined;\n  respond: undefined;\n}\n\nexport type ActionRenderProps<T extends Parameter[] | [] = []> =\n  | CompleteState<T>\n  | ExecutingState<T>\n  | InProgressState<T>;\n\nexport type ActionRenderPropsNoArgs<T extends Parameter[] | [] = []> =\n  | CompleteStateNoArgs<T>\n  | ExecutingStateNoArgs<T>\n  | InProgressStateNoArgs<T>;\n\nexport type ActionRenderPropsWait<T extends Parameter[] | [] = []> =\n  | CompleteStateWait<T>\n  | ExecutingStateWait<T>\n  | InProgressStateWait<T>;\n\nexport type ActionRenderPropsNoArgsWait<T extends Parameter[] | [] = []> =\n  | CompleteStateNoArgsWait<T>\n  | ExecutingStateNoArgsWait<T>\n  | InProgressStateNoArgsWait<T>;\n\nexport type CatchAllActionRenderProps<T extends Parameter[] | [] = []> =\n  | (CompleteState<T> & {\n      name: string;\n    })\n  | (ExecutingState<T> & {\n      name: string;\n    })\n  | (InProgressState<T> & {\n      name: string;\n    });\n\nexport type FrontendActionAvailability = \"disabled\" | \"enabled\" | \"remote\" | \"frontend\";\n\nexport type FrontendAction<\n  T extends Parameter[] | [] = [],\n  N extends string = string,\n> = Action<T> & {\n  name: Exclude<N, \"*\">;\n  /**\n   * @deprecated Use `available` instead.\n   */\n  disabled?: boolean;\n  available?: FrontendActionAvailability;\n  pairedAction?: string;\n  followUp?: boolean;\n} & (\n    | {\n        render?:\n          | string\n          | (T extends []\n              ? (props: ActionRenderPropsNoArgs<T>) => string | React.ReactElement\n              : (props: ActionRenderProps<T>) => string | React.ReactElement);\n        /** @deprecated use renderAndWaitForResponse instead */\n        renderAndWait?: never;\n        renderAndWaitForResponse?: never;\n      }\n    | {\n        render?: never;\n        /** @deprecated use renderAndWaitForResponse instead */\n        renderAndWait?: T extends []\n          ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement\n          : (props: ActionRenderPropsWait<T>) => React.ReactElement;\n        renderAndWaitForResponse?: T extends []\n          ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement\n          : (props: ActionRenderPropsWait<T>) => React.ReactElement;\n        handler?: never;\n      }\n  );\n\nexport type CatchAllFrontendAction = {\n  name: \"*\";\n  render: (props: CatchAllActionRenderProps<any>) => React.ReactElement;\n};\n\nexport type RenderFunctionStatus = ActionRenderProps<any>[\"status\"];\n\nexport function processActionsForRuntimeRequest(actions: FrontendAction<any>[]) {\n  const filteredActions = actions\n    .filter(\n      (action) =>\n        action.available !== ActionInputAvailability.Disabled &&\n        action.disabled !== true &&\n        action.name !== \"*\" &&\n        action.available != \"frontend\" &&\n        !action.pairedAction,\n    )\n    .map((action) => {\n      let available: ActionInputAvailability | undefined = ActionInputAvailability.Enabled;\n      if (action.disabled) {\n        available = ActionInputAvailability.Disabled;\n      } else if (action.available === \"disabled\") {\n        available = ActionInputAvailability.Disabled;\n      } else if (action.available === \"remote\") {\n        available = ActionInputAvailability.Remote;\n      }\n      return {\n        name: action.name,\n        description: action.description || \"\",\n        jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters || [])),\n        available,\n      };\n    });\n  return filteredActions;\n}\n"], "mappings": ";AAAA,SAAS,+BAA+B;AACxC;AAAA,EAIE;AAAA,OACK;AAwKA,SAAS,gCAAgC,SAAgC;AAC9E,QAAM,kBAAkB,QACrB;AAAA,IACC,CAAC,WACC,OAAO,cAAc,wBAAwB,YAC7C,OAAO,aAAa,QACpB,OAAO,SAAS,OAChB,OAAO,aAAa,cACpB,CAAC,OAAO;AAAA,EACZ,EACC,IAAI,CAAC,WAAW;AACf,QAAI,YAAiD,wBAAwB;AAC7E,QAAI,OAAO,UAAU;AACnB,kBAAY,wBAAwB;AAAA,IACtC,WAAW,OAAO,cAAc,YAAY;AAC1C,kBAAY,wBAAwB;AAAA,IACtC,WAAW,OAAO,cAAc,UAAU;AACxC,kBAAY,wBAAwB;AAAA,IACtC;AACA,WAAO;AAAA,MACL,MAAM,OAAO;AAAA,MACb,aAAa,OAAO,eAAe;AAAA,MACnC,YAAY,KAAK,UAAU,6BAA6B,OAAO,cAAc,CAAC,CAAC,CAAC;AAAA,MAChF;AAAA,IACF;AAAA,EACF,CAAC;AACH,SAAO;AACT;", "names": []}