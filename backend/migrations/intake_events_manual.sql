-- Manual SQL script to create intake_events table
-- This can be run directly on the Supabase database

-- Ensure tenants schema exists
CREATE SCHEMA IF NOT EXISTS tenants;

-- Create intake_events table
CREATE TABLE IF NOT EXISTS tenants.intake_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    payload JSONB NOT NULL,
    received_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    processed BOOLEAN NOT NULL DEFAULT false
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS ix_tenants_intake_events_tenant_id 
    ON tenants.intake_events(tenant_id);

CREATE INDEX IF NOT EXISTS ix_tenants_intake_events_received_at 
    ON tenants.intake_events(received_at);

CREATE INDEX IF NOT EXISTS ix_tenants_intake_events_processed 
    ON tenants.intake_events(processed);

-- Add comments for documentation
COMMENT ON TABLE tenants.intake_events IS 'Stores webhook events from the Voice service for intake processing';
COMMENT ON COLUMN tenants.intake_events.id IS 'Unique identifier for the intake event';
COMMENT ON COLUMN tenants.intake_events.tenant_id IS 'Tenant ID for multi-tenant isolation';
COMMENT ON COLUMN tenants.intake_events.payload IS 'Raw JSON payload from the Voice service webhook';
COMMENT ON COLUMN tenants.intake_events.received_at IS 'Timestamp when the webhook was received';
COMMENT ON COLUMN tenants.intake_events.processed IS 'Flag indicating if the event has been processed';

-- Verify table creation
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'tenants' AND tablename = 'intake_events';

-- Verify indexes
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'tenants' AND tablename = 'intake_events';
