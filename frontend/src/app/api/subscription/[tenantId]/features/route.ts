import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function GET(
  request: NextRequest,
  { params }: { params: { tenantId: string } }
) {
  try {
    const cookieStore = await cookies();

    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const tenantId = params.tenantId;
    
    // Verify that the user can access this tenant's features
    // This should match the tenant_id from the JWT token
    const userTenantId = session.user.user_metadata?.tenant_id;
    if (userTenantId !== tenantId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Call the backend API to get tenant features
    const backendResponse = await fetch(
      `${process.env.BACKEND_URL || 'http://localhost:8000'}/api/subscription/${tenantId}/features`,
      {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!backendResponse.ok) {
      if (backendResponse.status === 404) {
        // No subscription found, return empty features
        return NextResponse.json({ features: [] });
      }
      
      const errorText = await backendResponse.text();
      console.error('Backend API error:', errorText);
      return NextResponse.json(
        { error: 'Failed to fetch features' },
        { status: backendResponse.status }
      );
    }

    const data = await backendResponse.json();
    
    return NextResponse.json({
      features: data.features || [],
    });

  } catch (error) {
    console.error('Error fetching tenant features:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
