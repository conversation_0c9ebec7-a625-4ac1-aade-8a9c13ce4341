import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { vi } from 'vitest';
import VoiceMetricsPage from '../page';

// Mock the SuperAdminGuard component
vi.mock('@/components/auth/SuperAdminGuard', () => {
  return {
    SuperAdminGuard: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  };
});

// Mock the useRbac hook
vi.mock('@/hooks/useRbac', () => ({
  useRbac: () => ({
    isSuperAdmin: vi.fn(() => true),
    isLoading: vi.fn(() => false),
  }),
}));

// Mock the useToast hook
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock environment variables
const originalEnv = process.env;

describe('VoiceMetricsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    process.env = {
      ...originalEnv,
      NEXT_PUBLIC_GRAFANA_URL: 'https://grafana.test.com/d/voice-metrics',
      NEXT_PUBLIC_PROMETHEUS_PROXY_URL: '/api/prometheus',
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  const mockPrometheusResponse = (value: string) => ({
    status: 'success',
    data: {
      resultType: 'vector',
      result: [
        {
          metric: {},
          value: [Date.now() / 1000, value],
        },
      ],
    },
  });

  it('renders the voice metrics page with all stat cards', async () => {
    // Mock successful API responses
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('15'), // active calls
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('3'), // queue depth
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('1.5'), // latency
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('1'), // rejects
      });

    render(<VoiceMetricsPage />);

    // Check page title and description
    expect(screen.getByText('Voice Metrics')).toBeInTheDocument();
    expect(screen.getByText(/Real-time monitoring of voice service performance/)).toBeInTheDocument();

    // Wait for metrics to load and check stat cards
    await waitFor(() => {
      expect(screen.getByText('Active Calls')).toBeInTheDocument();
      expect(screen.getByText('Queue Depth')).toBeInTheDocument();
      expect(screen.getByText('P95 Latency')).toBeInTheDocument();
      expect(screen.getByText('Call Rejects')).toBeInTheDocument();
    });

    // Check that metrics values are displayed
    await waitFor(() => {
      expect(screen.getByText('15')).toBeInTheDocument(); // active calls
      expect(screen.getByText('3')).toBeInTheDocument(); // queue depth
      expect(screen.getByText('1500ms')).toBeInTheDocument(); // latency in ms
      expect(screen.getByText('1')).toBeInTheDocument(); // rejects
    });
  });

  it('displays Grafana iframe when URL is configured', async () => {
    // Mock fetch to prevent API calls
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockPrometheusResponse('5'),
    });

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    const iframe = screen.getByTitle('Voice Metrics Grafana Dashboard');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute(
      'src',
      'https://grafana.test.com/d/voice-metrics?orgId=1&refresh=10s&kiosk&theme=light'
    );
  });

  it('shows placeholder when Grafana URL is not configured', async () => {
    process.env.NEXT_PUBLIC_GRAFANA_URL = '';

    // Mock fetch to prevent API calls
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => mockPrometheusResponse('5'),
    });

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    expect(screen.getByText('Configure NEXT_PUBLIC_GRAFANA_URL to display the embedded dashboard')).toBeInTheDocument();
    expect(screen.getByText('Grafana dashboard will appear here when configured')).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    // Mock API failure
    mockFetch.mockRejectedValue(new Error('Network error'));

    render(<VoiceMetricsPage />);

    await waitFor(() => {
      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(screen.getByText('Configuration Error')).toBeInTheDocument();
    });
  });

  it('refreshes metrics when refresh button is clicked', async () => {
    // Initial load
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('10'),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('2'),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('1.0'),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('0'),
      });

    render(<VoiceMetricsPage />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('10')).toBeInTheDocument();
    });

    // Clear previous calls
    mockFetch.mockClear();

    // Mock refresh responses
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('20'),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('5'),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('2.0'),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('2'),
      });

    // Click refresh button
    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);

    // Wait for new values
    await waitFor(() => {
      expect(screen.getByText('20')).toBeInTheDocument();
    });

    expect(mockFetch).toHaveBeenCalledTimes(4);
  });

  it('displays correct status badges based on metric values', async () => {
    // Mock high values that should trigger warning/error badges
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('60'), // high active calls
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('15'), // high queue depth
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('6.0'), // high latency
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPrometheusResponse('8'), // high rejects
      });

    render(<VoiceMetricsPage />);

    await waitFor(() => {
      expect(screen.getAllByText('High')).toHaveLength(3); // For active calls, queue depth, and rejects
      expect(screen.getByText('Slow')).toBeInTheDocument(); // For latency
    });
  });

  it('formats last updated time correctly', async () => {
    mockFetch
      .mockResolvedValue({
        ok: true,
        json: async () => mockPrometheusResponse('1'),
      });

    render(<VoiceMetricsPage />);

    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
    });
  });
});
