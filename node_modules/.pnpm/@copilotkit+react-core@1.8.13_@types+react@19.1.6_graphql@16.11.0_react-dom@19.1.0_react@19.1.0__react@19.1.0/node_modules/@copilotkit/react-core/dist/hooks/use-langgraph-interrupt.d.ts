import { e as LangGraphInterruptRender } from '../copilot-context-8fb74a85.js';
import '@copilotkit/shared';
import '../types/frontend-action.js';
import '@copilotkit/runtime-client-gql';
import 'react';
import './use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';

declare function useLangGraphInterrupt<TEventValue = any>(action: Omit<LangGraphInterruptRender<TEventValue>, "id">, dependencies?: any[]): void;

export { useLangGraphInterrupt };
