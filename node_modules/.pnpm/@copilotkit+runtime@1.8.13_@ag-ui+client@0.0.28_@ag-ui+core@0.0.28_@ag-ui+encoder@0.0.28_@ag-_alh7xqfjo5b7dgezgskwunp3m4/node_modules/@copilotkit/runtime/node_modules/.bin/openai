#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules/openai/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules/openai/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules/openai/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules/openai/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules/openai/bin/cli" "$@"
else
  exec node  "$basedir/../../../../../../openai@4.103.0_ws@8.18.2_zod@3.25.32/node_modules/openai/bin/cli" "$@"
fi
