import * as react_jsx_runtime from 'react/jsx-runtime';
import { GraphQLError } from '@copilotkit/runtime-client-gql';
import React from 'react';
import { PartialBy } from '@copilotkit/shared';

interface ToastContextValue {
    toasts: Toast[];
    addToast: (toast: PartialBy<Toast, "id">) => void;
    addGraphQLErrorsToast: (errors: GraphQLError[]) => void;
    removeToast: (id: string) => void;
    enabled: boolean;
}
declare function useToast(): ToastContextValue;
declare function ToastProvider({ enabled, children, }: {
    enabled: boolean;
    children: React.ReactNode;
}): react_jsx_runtime.JSX.Element;
interface Toast {
    id: string;
    message: string | React.ReactNode;
    type: "info" | "success" | "warning" | "error";
    duration?: number;
}
declare function Toast({ message, type, onClose, }: {
    message: string | React.ReactNode;
    type: "info" | "success" | "warning" | "error";
    onClose: () => void;
}): react_jsx_runtime.JSX.Element;

export { ToastProvider, useToast };
