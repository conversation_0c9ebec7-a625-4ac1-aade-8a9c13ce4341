// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
export class Downloads extends APIResource {
    /**
     * Session Downloads
     */
    list(id, options) {
        return this._client.get(`/v1/sessions/${id}/downloads`, {
            ...options,
            headers: { Accept: 'application/zip', ...options?.headers },
            __binaryResponse: true,
        });
    }
}
//# sourceMappingURL=downloads.mjs.map