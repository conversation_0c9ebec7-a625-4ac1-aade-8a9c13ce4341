#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/localtunnel@2.0.2/node_modules/localtunnel/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/localtunnel@2.0.2/node_modules/localtunnel/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/localtunnel@2.0.2/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/localtunnel@2.0.2/node_modules/localtunnel/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/localtunnel@2.0.2/node_modules/localtunnel/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/localtunnel@2.0.2/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../localtunnel@2.0.2/node_modules/localtunnel/bin/lt.js" "$@"
else
  exec node  "$basedir/../../../../../../localtunnel@2.0.2/node_modules/localtunnel/bin/lt.js" "$@"
fi
