# Authentication Troubleshooting Guide

## 🚨 **Common Issues & Solutions**

### **1. "JWT secret not configured" Error**

**Problem**: API routes failing with JWT validation errors.

**Symptoms**:
```
JWTValidationError: JWT secret not configured
```

**Solution**:
```bash
# Add to your .env.local file
SUPABASE_JWT_SECRET=your-jwt-secret-from-supabase-dashboard
```

**How to find your JWT secret**:
1. Go to Supabase Dashboard → Settings → API
2. Copy the "JWT Secret" value
3. Add it to your environment variables

---

### **2. Calendar Dropdowns Empty**

**Problem**: Calendar event creation page shows empty dropdowns for cases, clients, users.

**Symptoms**:
- Dropdowns load but show no options
- Console errors about schema or table access

**Solution**:
```typescript
// ❌ OLD: Querying wrong schema
const { data } = await supabase
  .from('cases')  // Queries public.cases (legal precedents)
  .select('*');

// ✅ NEW: Query tenant schema
const { data } = await supabase
  .schema('tenants')
  .from('cases')  // Queries tenants.cases (client cases)
  .select('*')
  .eq('tenant_id', user.tenantId);
```

---

### **3. "useAuth must be used within an AuthProvider" Error**

**Problem**: Authentication hooks failing outside of provider context.

**Symptoms**:
```
Error: useAuth must be used within an AuthProvider
```

**Solution**:
```typescript
// Ensure your app is wrapped with AuthProvider
function MyApp({ Component, pageProps }) {
  return (
    <AuthProvider>
      <Component {...pageProps} />
    </AuthProvider>
  );
}
```

---

### **4. Infinite Redirect Loops**

**Problem**: Users get stuck in redirect loops between login and protected pages.

**Symptoms**:
- Page keeps redirecting between `/auth/login` and protected routes
- Browser history shows rapid navigation

**Solution**:
```typescript
// ❌ PROBLEMATIC: Not checking loading state
function ProtectedPage() {
  const { isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    router.push('/auth/login'); // Triggers during loading!
  }
}

// ✅ FIXED: Check loading state first
function ProtectedPage() {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) {
    router.push('/auth/login');
  }
}

// ✅ BETTER: Use the hook designed for this
function ProtectedPage() {
  useRequireAuth(); // Handles loading state automatically
  return <div>Protected content</div>;
}
```

---

### **5. Permission Denied Errors**

**Problem**: Users can't access resources they should have access to.

**Symptoms**:
```
Error: Insufficient permissions
403 Forbidden responses from API
```

**Debugging Steps**:

1. **Check user role**:
```typescript
const { user } = useAuth();
console.log('User role:', user?.role);
console.log('User permissions:', getUserPermissions(user));
```

2. **Verify permission definition**:
```typescript
// Check if permission exists for role
const hasPermission = checkPermission(user, 'read:cases');
console.log('Has read:cases permission:', hasPermission);
```

3. **Check tenant isolation**:
```typescript
// Ensure user belongs to correct tenant
console.log('User tenant:', user?.tenantId);
console.log('Resource tenant:', resourceTenantId);
console.log('Can access:', canAccessTenant(user, resourceTenantId));
```

---

### **6. Database Schema Errors**

**Problem**: Queries failing with "relation does not exist" errors.

**Symptoms**:
```
relation "public.cases" does not exist
relation "tenants.activities" does not exist
```

**Solution**:
```typescript
// ❌ WRONG: Missing schema specification
const { data } = await supabase
  .from('cases')  // Defaults to public schema
  .select('*');

// ✅ CORRECT: Specify tenant schema
const { data } = await supabase
  .schema('tenants')
  .from('cases')
  .select('*');

// ✅ CORRECT: For security events
const { data } = await supabase
  .schema('security')
  .from('events')
  .select('*');
```

---

### **7. TypeScript Type Errors**

**Problem**: TypeScript compilation errors with authentication types.

**Common Errors**:
```typescript
// Error: Property 'tenantId' does not exist on type 'User'
user.tenantId

// Error: Argument of type 'string | undefined' is not assignable
checkPermission(user, permission)
```

**Solutions**:
```typescript
// ✅ Use proper type guards
if (user && user.tenantId) {
  // Safe to use user.tenantId
}

// ✅ Use optional chaining
const tenantId = user?.tenantId;

// ✅ Use proper type assertions when safe
const authenticatedUser = user as AuthUser; // When you know user is authenticated
```

---

### **8. Session Persistence Issues**

**Problem**: Users get logged out unexpectedly or sessions don't persist.

**Symptoms**:
- Users logged out on page refresh
- Authentication state resets randomly

**Debugging**:
```typescript
// Check session storage
const { data: { session } } = await supabase.auth.getSession();
console.log('Current session:', session);

// Check token expiration
if (session?.expires_at) {
  const expiresAt = new Date(session.expires_at * 1000);
  console.log('Session expires at:', expiresAt);
  console.log('Is expired:', expiresAt < new Date());
}
```

**Solutions**:
1. **Check token refresh**:
```typescript
// Ensure automatic token refresh is enabled
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  }
});
```

2. **Verify environment variables**:
```bash
# Ensure these are set correctly
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

---

### **9. API Route Authentication Failures**

**Problem**: API routes return 401 even with valid authentication.

**Debugging Steps**:

1. **Check request headers**:
```typescript
// In your API route
export async function GET(request: NextRequest) {
  const authHeader = request.headers.get('Authorization');
  console.log('Auth header:', authHeader);
  
  const token = extractJwtFromRequest(request);
  console.log('Extracted token:', token ? 'Present' : 'Missing');
}
```

2. **Validate JWT manually**:
```typescript
try {
  const payload = await validateJwt(token);
  console.log('JWT payload:', payload);
} catch (error) {
  console.error('JWT validation failed:', error);
}
```

3. **Check client-side token sending**:
```typescript
// Ensure client sends token correctly
const response = await fetch('/api/protected', {
  headers: {
    'Authorization': `Bearer ${session?.access_token}`,
    'Content-Type': 'application/json'
  }
});
```

---

### **10. Role-Based Access Control Issues**

**Problem**: Users can access resources they shouldn't or can't access resources they should.

**Debugging**:
```typescript
// Check role hierarchy
console.log('User role:', user.role);
console.log('Required roles:', allowedRoles);
console.log('Has role:', hasRole(user, allowedRoles));

// Check specific permissions
console.log('Checking permission:', permission);
console.log('User permissions:', getUserPermissions(user));
console.log('Has permission:', checkPermission(user, permission));
```

**Common Issues**:
- Role names don't match (case sensitivity)
- User has multiple roles but checking for wrong one
- Permission not defined for user's role

---

## 🔧 **Debugging Tools**

### **Authentication Debug Component**

```typescript
function AuthDebug() {
  const { user, session, profile, isAuthenticated, isLoading } = useAuth();
  
  if (process.env.NODE_ENV !== 'development') return null;
  
  return (
    <div style={{ position: 'fixed', top: 0, right: 0, background: 'white', padding: '10px', border: '1px solid #ccc' }}>
      <h4>Auth Debug</h4>
      <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
      <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
      <p>User ID: {user?.id || 'None'}</p>
      <p>Role: {profile?.role || 'None'}</p>
      <p>Tenant: {profile?.tenant_id || 'None'}</p>
      <p>Session Expires: {session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'None'}</p>
    </div>
  );
}
```

### **API Debug Middleware**

```typescript
// Add to your API routes for debugging
function debugAuth(request: NextRequest) {
  if (process.env.NODE_ENV === 'development') {
    console.log('=== AUTH DEBUG ===');
    console.log('Method:', request.method);
    console.log('URL:', request.url);
    console.log('Headers:', Object.fromEntries(request.headers.entries()));
    console.log('==================');
  }
}
```

---

## 📞 **Getting Help**

If you're still experiencing issues:

1. **Check the logs** - Look for error messages in browser console and server logs
2. **Verify environment variables** - Ensure all required env vars are set
3. **Test with minimal example** - Create a simple test case to isolate the issue
4. **Check network requests** - Use browser dev tools to inspect API calls
5. **Review recent changes** - Consider what changed since it last worked

### **Useful Log Commands**

```bash
# Check Next.js server logs
npm run dev

# Check Supabase logs (if self-hosted)
docker logs supabase-db

# Check authentication-specific logs
grep -i "auth\|jwt\|permission" logs/app.log
```
