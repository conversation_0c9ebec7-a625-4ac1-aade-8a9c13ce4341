import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default from 'react';
import { WindowProps, ButtonProps, HeaderProps } from './props.js';
import { CopilotChatProps } from './Chat.js';
import '@copilotkit/runtime-client-gql';
import '../../types/suggestions.js';
import './ChatContext.js';
import '@copilotkit/react-core';

interface CopilotModalProps extends CopilotChatProps {
    /**
     * Whether the chat window should be open by default.
     * @default false
     */
    defaultOpen?: boolean;
    /**
     * If the chat window should close when the user clicks outside of it.
     * @default true
     */
    clickOutsideToClose?: boolean;
    /**
     * If the chat window should close when the user hits the Escape key.
     * @default true
     */
    hitEscapeToClose?: boolean;
    /**
     * The shortcut key to open the chat window.
     * Uses Command-[shortcut] on a Mac and Ctrl-[shortcut] on Windows.
     * @default '/'
     */
    shortcut?: string;
    /**
     * A callback that gets called when the chat window opens or closes.
     */
    onSetOpen?: (open: boolean) => void;
    /**
     * A custom Window component to use instead of the default.
     */
    Window?: React__default.ComponentType<WindowProps>;
    /**
     * A custom Button component to use instead of the default.
     */
    Button?: React__default.ComponentType<ButtonProps>;
    /**
     * A custom Header component to use instead of the default.
     */
    Header?: React__default.ComponentType<HeaderProps>;
}
declare const CopilotModal: ({ instructions, defaultOpen, clickOutsideToClose, hitEscapeToClose, onSetOpen, onSubmitMessage, onStopGeneration, onReloadMessages, shortcut, icons, labels, makeSystemMessage, onInProgress, Window, Button, Header, Messages, Input, AssistantMessage, UserMessage, onThumbsUp, onThumbsDown, onCopy, onRegenerate, markdownTagRenderers, className, children, ...props }: CopilotModalProps) => react_jsx_runtime.JSX.Element;

export { CopilotModal, CopilotModalProps };
