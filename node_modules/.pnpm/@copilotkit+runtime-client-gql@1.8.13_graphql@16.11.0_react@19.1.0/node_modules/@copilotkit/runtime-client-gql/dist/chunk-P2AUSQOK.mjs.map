{"version": 3, "sources": ["../src/client/conversion.ts"], "sourcesContent": ["import {\n  GenerateCopilotResponseMutation,\n  MessageInput,\n  MessageStatusCode,\n} from \"../graphql/@generated/graphql\";\nimport {\n  ActionExecutionMessage,\n  AgentStateMessage,\n  Message,\n  ResultMessage,\n  TextMessage,\n  ImageMessage,\n} from \"./types\";\n\nimport untruncate<PERSON><PERSON> from \"untruncate-json\";\nimport { parseJson } from \"@copilotkit/shared\";\n\nexport function filterAgentStateMessages(messages: Message[]): Message[] {\n  return messages.filter((message) => !message.isAgentStateMessage());\n}\n\nexport function convertMessagesToGqlInput(messages: Message[]): MessageInput[] {\n  return messages.map((message) => {\n    if (message.isTextMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        textMessage: {\n          content: message.content,\n          role: message.role as any,\n          parentMessageId: message.parentMessageId,\n        },\n      };\n    } else if (message.isActionExecutionMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        actionExecutionMessage: {\n          name: message.name,\n          arguments: JSON.stringify(message.arguments),\n          parentMessageId: message.parentMessageId,\n        },\n      };\n    } else if (message.isResultMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        resultMessage: {\n          result: message.result,\n          actionExecutionId: message.actionExecutionId,\n          actionName: message.actionName,\n        },\n      };\n    } else if (message.isAgentStateMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        agentStateMessage: {\n          threadId: message.threadId,\n          role: message.role,\n          agentName: message.agentName,\n          nodeName: message.nodeName,\n          runId: message.runId,\n          active: message.active,\n          running: message.running,\n          state: JSON.stringify(message.state),\n        },\n      };\n    } else if (message.isImageMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        imageMessage: {\n          format: message.format,\n          bytes: message.bytes,\n          role: message.role as any,\n          parentMessageId: message.parentMessageId,\n        },\n      };\n    } else {\n      throw new Error(\"Unknown message type\");\n    }\n  });\n}\n\nexport function filterAdjacentAgentStateMessages(\n  messages: GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"],\n): GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"] {\n  const filteredMessages: GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"] =\n    [];\n\n  messages.forEach((message, i) => {\n    // keep all other message types\n    if (message.__typename !== \"AgentStateMessageOutput\") {\n      filteredMessages.push(message);\n    } else {\n      const prevAgentStateMessageIndex = filteredMessages.findIndex(\n        // TODO: also check runId\n        (m) => m.__typename === \"AgentStateMessageOutput\" && m.agentName === message.agentName,\n      );\n      if (prevAgentStateMessageIndex === -1) {\n        filteredMessages.push(message);\n      } else {\n        filteredMessages[prevAgentStateMessageIndex] = message;\n      }\n    }\n  });\n\n  return filteredMessages;\n}\n\nexport function convertGqlOutputToMessages(\n  messages: GenerateCopilotResponseMutation[\"generateCopilotResponse\"][\"messages\"],\n): Message[] {\n  return messages.map((message) => {\n    if (message.__typename === \"TextMessageOutput\") {\n      return new TextMessage({\n        id: message.id,\n        role: message.role,\n        content: message.content.join(\"\"),\n        parentMessageId: message.parentMessageId,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    } else if (message.__typename === \"ActionExecutionMessageOutput\") {\n      return new ActionExecutionMessage({\n        id: message.id,\n        name: message.name,\n        arguments: getPartialArguments(message.arguments),\n        parentMessageId: message.parentMessageId,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    } else if (message.__typename === \"ResultMessageOutput\") {\n      return new ResultMessage({\n        id: message.id,\n        result: message.result,\n        actionExecutionId: message.actionExecutionId,\n        actionName: message.actionName,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    } else if (message.__typename === \"AgentStateMessageOutput\") {\n      return new AgentStateMessage({\n        id: message.id,\n        threadId: message.threadId,\n        role: message.role,\n        agentName: message.agentName,\n        nodeName: message.nodeName,\n        runId: message.runId,\n        active: message.active,\n        running: message.running,\n        state: parseJson(message.state, {}),\n        createdAt: new Date(),\n      });\n    } else if (message.__typename === \"ImageMessageOutput\") {\n      return new ImageMessage({\n        id: message.id,\n        format: message.format,\n        bytes: message.bytes,\n        role: message.role,\n        parentMessageId: message.parentMessageId,\n        createdAt: new Date(),\n        status: message.status || { code: MessageStatusCode.Pending },\n      });\n    }\n\n    throw new Error(\"Unknown message type\");\n  });\n}\n\nexport function loadMessagesFromJsonRepresentation(json: any[]): Message[] {\n  const result: Message[] = [];\n  for (const item of json) {\n    if (\"content\" in item) {\n      result.push(\n        new TextMessage({\n          id: item.id,\n          role: item.role,\n          content: item.content,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    } else if (\"arguments\" in item) {\n      result.push(\n        new ActionExecutionMessage({\n          id: item.id,\n          name: item.name,\n          arguments: item.arguments,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    } else if (\"result\" in item) {\n      result.push(\n        new ResultMessage({\n          id: item.id,\n          result: item.result,\n          actionExecutionId: item.actionExecutionId,\n          actionName: item.actionName,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    } else if (\"state\" in item) {\n      result.push(\n        new AgentStateMessage({\n          id: item.id,\n          threadId: item.threadId,\n          role: item.role,\n          agentName: item.agentName,\n          nodeName: item.nodeName,\n          runId: item.runId,\n          active: item.active,\n          running: item.running,\n          state: item.state,\n          createdAt: item.createdAt || new Date(),\n        }),\n      );\n    } else if (\"format\" in item && \"bytes\" in item) {\n      result.push(\n        new ImageMessage({\n          id: item.id,\n          format: item.format,\n          bytes: item.bytes,\n          role: item.role,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || new Date(),\n          status: item.status || { code: MessageStatusCode.Success },\n        }),\n      );\n    }\n  }\n  return result;\n}\n\nfunction getPartialArguments(args: string[]) {\n  try {\n    if (!args.length) return {};\n\n    return JSON.parse(untruncateJson(args.join(\"\")));\n  } catch (e) {\n    return {};\n  }\n}\n"], "mappings": ";;;;;;;;;AAcA,OAAO,oBAAoB;AAC3B,SAAS,iBAAiB;AAEnB,SAAS,yBAAyB,UAAgC;AACvE,SAAO,SAAS,OAAO,CAAC,YAAY,CAAC,QAAQ,oBAAoB,CAAC;AACpE;AAEO,SAAS,0BAA0B,UAAqC;AAC7E,SAAO,SAAS,IAAI,CAAC,YAAY;AAC/B,QAAI,QAAQ,cAAc,GAAG;AAC3B,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,aAAa;AAAA,UACX,SAAS,QAAQ;AAAA,UACjB,MAAM,QAAQ;AAAA,UACd,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,wBAAwB;AAAA,UACtB,MAAM,QAAQ;AAAA,UACd,WAAW,KAAK,UAAU,QAAQ,SAAS;AAAA,UAC3C,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,gBAAgB,GAAG;AACpC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,eAAe;AAAA,UACb,QAAQ,QAAQ;AAAA,UAChB,mBAAmB,QAAQ;AAAA,UAC3B,YAAY,QAAQ;AAAA,QACtB;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,oBAAoB,GAAG;AACxC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,mBAAmB;AAAA,UACjB,UAAU,QAAQ;AAAA,UAClB,MAAM,QAAQ;AAAA,UACd,WAAW,QAAQ;AAAA,UACnB,UAAU,QAAQ;AAAA,UAClB,OAAO,QAAQ;AAAA,UACf,QAAQ,QAAQ;AAAA,UAChB,SAAS,QAAQ;AAAA,UACjB,OAAO,KAAK,UAAU,QAAQ,KAAK;AAAA,QACrC;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,eAAe,GAAG;AACnC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,WAAW,QAAQ;AAAA,QACnB,cAAc;AAAA,UACZ,QAAQ,QAAQ;AAAA,UAChB,OAAO,QAAQ;AAAA,UACf,MAAM,QAAQ;AAAA,UACd,iBAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,sBAAsB;AAAA,IACxC;AAAA,EACF,CAAC;AACH;AAEO,SAAS,iCACd,UACwE;AACxE,QAAM,mBACJ,CAAC;AAEH,WAAS,QAAQ,CAAC,SAAS,MAAM;AAE/B,QAAI,QAAQ,eAAe,2BAA2B;AACpD,uBAAiB,KAAK,OAAO;AAAA,IAC/B,OAAO;AACL,YAAM,6BAA6B,iBAAiB;AAAA;AAAA,QAElD,CAAC,MAAM,EAAE,eAAe,6BAA6B,EAAE,cAAc,QAAQ;AAAA,MAC/E;AACA,UAAI,+BAA+B,IAAI;AACrC,yBAAiB,KAAK,OAAO;AAAA,MAC/B,OAAO;AACL,yBAAiB,0BAA0B,IAAI;AAAA,MACjD;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEO,SAAS,2BACd,UACW;AACX,SAAO,SAAS,IAAI,CAAC,YAAY;AAC/B,QAAI,QAAQ,eAAe,qBAAqB;AAC9C,aAAO,IAAI,YAAY;AAAA,QACrB,IAAI,QAAQ;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ,QAAQ,KAAK,EAAE;AAAA,QAChC,iBAAiB,QAAQ;AAAA,QACzB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,gCAAgC;AAChE,aAAO,IAAI,uBAAuB;AAAA,QAChC,IAAI,QAAQ;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,WAAW,oBAAoB,QAAQ,SAAS;AAAA,QAChD,iBAAiB,QAAQ;AAAA,QACzB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,uBAAuB;AACvD,aAAO,IAAI,cAAc;AAAA,QACvB,IAAI,QAAQ;AAAA,QACZ,QAAQ,QAAQ;AAAA,QAChB,mBAAmB,QAAQ;AAAA,QAC3B,YAAY,QAAQ;AAAA,QACpB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,2BAA2B;AAC3D,aAAO,IAAI,kBAAkB;AAAA,QAC3B,IAAI,QAAQ;AAAA,QACZ,UAAU,QAAQ;AAAA,QAClB,MAAM,QAAQ;AAAA,QACd,WAAW,QAAQ;AAAA,QACnB,UAAU,QAAQ;AAAA,QAClB,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ;AAAA,QAChB,SAAS,QAAQ;AAAA,QACjB,OAAO,UAAU,QAAQ,OAAO,CAAC,CAAC;AAAA,QAClC,WAAW,oBAAI,KAAK;AAAA,MACtB,CAAC;AAAA,IACH,WAAW,QAAQ,eAAe,sBAAsB;AACtD,aAAO,IAAI,aAAa;AAAA,QACtB,IAAI,QAAQ;AAAA,QACZ,QAAQ,QAAQ;AAAA,QAChB,OAAO,QAAQ;AAAA,QACf,MAAM,QAAQ;AAAA,QACd,iBAAiB,QAAQ;AAAA,QACzB,WAAW,oBAAI,KAAK;AAAA,QACpB,QAAQ,QAAQ,UAAU,EAAE,8BAAgC;AAAA,MAC9D,CAAC;AAAA,IACH;AAEA,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC,CAAC;AACH;AAEO,SAAS,mCAAmC,MAAwB;AACzE,QAAM,SAAoB,CAAC;AAC3B,aAAW,QAAQ,MAAM;AACvB,QAAI,aAAa,MAAM;AACrB,aAAO;AAAA,QACL,IAAI,YAAY;AAAA,UACd,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,SAAS,KAAK;AAAA,UACd,iBAAiB,KAAK;AAAA,UACtB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF,WAAW,eAAe,MAAM;AAC9B,aAAO;AAAA,QACL,IAAI,uBAAuB;AAAA,UACzB,IAAI,KAAK;AAAA,UACT,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,iBAAiB,KAAK;AAAA,UACtB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF,WAAW,YAAY,MAAM;AAC3B,aAAO;AAAA,QACL,IAAI,cAAc;AAAA,UAChB,IAAI,KAAK;AAAA,UACT,QAAQ,KAAK;AAAA,UACb,mBAAmB,KAAK;AAAA,UACxB,YAAY,KAAK;AAAA,UACjB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF,WAAW,WAAW,MAAM;AAC1B,aAAO;AAAA,QACL,IAAI,kBAAkB;AAAA,UACpB,IAAI,KAAK;AAAA,UACT,UAAU,KAAK;AAAA,UACf,MAAM,KAAK;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,UAAU,KAAK;AAAA,UACf,OAAO,KAAK;AAAA,UACZ,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK;AAAA,UACd,OAAO,KAAK;AAAA,UACZ,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,QACxC,CAAC;AAAA,MACH;AAAA,IACF,WAAW,YAAY,QAAQ,WAAW,MAAM;AAC9C,aAAO;AAAA,QACL,IAAI,aAAa;AAAA,UACf,IAAI,KAAK;AAAA,UACT,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,iBAAiB,KAAK;AAAA,UACtB,WAAW,KAAK,aAAa,oBAAI,KAAK;AAAA,UACtC,QAAQ,KAAK,UAAU,EAAE,8BAAgC;AAAA,QAC3D,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,MAAgB;AAC3C,MAAI;AACF,QAAI,CAAC,KAAK;AAAQ,aAAO,CAAC;AAE1B,WAAO,KAAK,MAAM,eAAe,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EACjD,SAAS,GAAP;AACA,WAAO,CAAC;AAAA,EACV;AACF;", "names": []}