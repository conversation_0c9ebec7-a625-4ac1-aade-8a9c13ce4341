{"version": 3, "sources": ["../../../src/components/copilot-provider/copilotkit.tsx", "../../../src/context/copilot-context.tsx", "../../../src/hooks/use-tree.ts", "../../../src/hooks/use-flat-category-store.ts", "../../../src/components/copilot-provider/copilot-messages.tsx", "../../../src/context/copilot-messages-context.tsx", "../../../src/components/toast/toast-provider.tsx", "../../../src/components/error-boundary/error-utils.tsx", "../../../src/components/toast/exclamation-mark-icon.tsx", "../../../src/hooks/use-copilot-runtime-client.ts", "../../../src/utils/dev-console.ts", "../../../src/components/error-boundary/error-boundary.tsx", "../../../src/lib/status-checker.ts", "../../../src/components/usage-banner.tsx"], "sourcesContent": ["/**\n * This component will typically wrap your entire application (or a sub-tree of your application where you want to have a copilot). It provides the copilot context to all other components and hooks.\n *\n * ## Example\n *\n * You can find more information about self-hosting CopilotKit [here](/guides/self-hosting).\n *\n * ```tsx\n * import { CopilotKit } from \"@copilotkit/react-core\";\n *\n * <CopilotKit runtimeUrl=\"<your-runtime-url>\">\n *   // ... your app ...\n * </CopilotKit>\n * ```\n */\n\nimport { useCallback, useEffect, useMemo, useRef, useState, SetStateAction } from \"react\";\nimport {\n  CopilotContext,\n  CopilotApiConfig,\n  ChatComponentsCache,\n  AgentSession,\n  AuthState,\n} from \"../../context/copilot-context\";\nimport useTree from \"../../hooks/use-tree\";\nimport { CopilotChatSuggestionConfiguration, DocumentPointer } from \"../../types\";\nimport { flushSync } from \"react-dom\";\nimport {\n  COPILOT_CLOUD_CHAT_URL,\n  CopilotCloudConfig,\n  FunctionCallHandler,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  randomUUID,\n  ConfigurationError,\n  MissingPublicApiKeyError,\n} from \"@copilotkit/shared\";\nimport { FrontendAction } from \"../../types/frontend-action\";\nimport useFlatCategoryStore from \"../../hooks/use-flat-category-store\";\nimport { CopilotKitProps } from \"./copilotkit-props\";\nimport { CoAgentStateRender } from \"../../types/coagent-action\";\nimport { CoagentState } from \"../../types/coagent-state\";\nimport { CopilotMessages } from \"./copilot-messages\";\nimport { ToastProvider } from \"../toast/toast-provider\";\nimport { useCopilotRuntimeClient } from \"../../hooks/use-copilot-runtime-client\";\nimport { shouldShowDevConsole } from \"../../utils\";\nimport { CopilotErrorBoundary } from \"../error-boundary/error-boundary\";\nimport { Agent, ExtensionsInput } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetterArgs,\n} from \"../../types/interrupt-action\";\n\nexport function CopilotKit({ children, ...props }: CopilotKitProps) {\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n  const enabled = shouldShowDevConsole(showDevConsole);\n\n  return (\n    <ToastProvider enabled={enabled}>\n      <CopilotErrorBoundary publicApiKey={props.publicApiKey} showUsageBanner={enabled}>\n        <CopilotKitInternal {...props}>{children}</CopilotKitInternal>\n      </CopilotErrorBoundary>\n    </ToastProvider>\n  );\n}\n\nexport function CopilotKitInternal(cpkProps: CopilotKitProps) {\n  const { children, ...props } = cpkProps;\n\n  /**\n   * This will throw an error if the props are invalid.\n   */\n  validateProps(cpkProps);\n\n  const chatApiEndpoint = props.runtimeUrl || COPILOT_CLOUD_CHAT_URL;\n\n  const [actions, setActions] = useState<Record<string, FrontendAction<any>>>({});\n  const [coAgentStateRenders, setCoAgentStateRenders] = useState<\n    Record<string, CoAgentStateRender<any>>\n  >({});\n\n  const chatComponentsCache = useRef<ChatComponentsCache>({\n    actions: {},\n    coAgentStateRenders: {},\n  });\n\n  const { addElement, removeElement, printTree } = useTree();\n  const [isLoading, setIsLoading] = useState(false);\n  const [chatInstructions, setChatInstructions] = useState(\"\");\n  const [authStates, setAuthStates] = useState<Record<string, AuthState>>({});\n  const [extensions, setExtensions] = useState<ExtensionsInput>({});\n  const [additionalInstructions, setAdditionalInstructions] = useState<string[]>([]);\n\n  const {\n    addElement: addDocument,\n    removeElement: removeDocument,\n    allElements: allDocuments,\n  } = useFlatCategoryStore<DocumentPointer>();\n\n  // Compute all the functions and properties that we need to pass\n\n  const setAction = useCallback((id: string, action: FrontendAction<any>) => {\n    setActions((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: action,\n      };\n    });\n  }, []);\n\n  const removeAction = useCallback((id: string) => {\n    setActions((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const setCoAgentStateRender = useCallback((id: string, stateRender: CoAgentStateRender<any>) => {\n    setCoAgentStateRenders((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: stateRender,\n      };\n    });\n  }, []);\n\n  const removeCoAgentStateRender = useCallback((id: string) => {\n    setCoAgentStateRenders((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const getContextString = useCallback(\n    (documents: DocumentPointer[], categories: string[]) => {\n      const documentsString = documents\n        .map((document) => {\n          return `${document.name} (${document.sourceApplication}):\\n${document.getContents()}`;\n        })\n        .join(\"\\n\\n\");\n\n      const nonDocumentStrings = printTree(categories);\n\n      return `${documentsString}\\n\\n${nonDocumentStrings}`;\n    },\n    [printTree],\n  );\n\n  const addContext = useCallback(\n    (\n      context: string,\n      parentId?: string,\n      categories: string[] = defaultCopilotContextCategories,\n    ) => {\n      return addElement(context, categories, parentId);\n    },\n    [addElement],\n  );\n\n  const removeContext = useCallback(\n    (id: string) => {\n      removeElement(id);\n    },\n    [removeElement],\n  );\n\n  const getFunctionCallHandler = useCallback(\n    (customEntryPoints?: Record<string, FrontendAction<any>>) => {\n      return entryPointsToFunctionCallHandler(Object.values(customEntryPoints || actions));\n    },\n    [actions],\n  );\n\n  const getDocumentsContext = useCallback(\n    (categories: string[]) => {\n      return allDocuments(categories);\n    },\n    [allDocuments],\n  );\n\n  const addDocumentContext = useCallback(\n    (documentPointer: DocumentPointer, categories: string[] = defaultCopilotContextCategories) => {\n      return addDocument(documentPointer, categories);\n    },\n    [addDocument],\n  );\n\n  const removeDocumentContext = useCallback(\n    (documentId: string) => {\n      removeDocument(documentId);\n    },\n    [removeDocument],\n  );\n\n  // get the appropriate CopilotApiConfig from the props\n  const copilotApiConfig: CopilotApiConfig = useMemo(() => {\n    let cloud: CopilotCloudConfig | undefined = undefined;\n    if (props.publicApiKey) {\n      cloud = {\n        guardrails: {\n          input: {\n            restrictToTopic: {\n              enabled: Boolean(props.guardrails_c),\n              validTopics: props.guardrails_c?.validTopics || [],\n              invalidTopics: props.guardrails_c?.invalidTopics || [],\n            },\n          },\n        },\n      };\n    }\n\n    return {\n      publicApiKey: props.publicApiKey,\n      ...(cloud ? { cloud } : {}),\n      chatApiEndpoint: chatApiEndpoint,\n      headers: props.headers || {},\n      properties: props.properties || {},\n      transcribeAudioUrl: props.transcribeAudioUrl,\n      textToSpeechUrl: props.textToSpeechUrl,\n      credentials: props.credentials,\n    };\n  }, [\n    props.publicApiKey,\n    props.headers,\n    props.properties,\n    props.transcribeAudioUrl,\n    props.textToSpeechUrl,\n    props.credentials,\n    props.cloudRestrictToTopic,\n    props.guardrails_c,\n  ]);\n\n  const headers = useMemo(() => {\n    const authHeaders = Object.values(authStates || {}).reduce((acc, state) => {\n      if (state.status === \"authenticated\" && state.authHeaders) {\n        return {\n          ...acc,\n          ...Object.entries(state.authHeaders).reduce(\n            (headers, [key, value]) => ({\n              ...headers,\n              [key.startsWith(\"X-Custom-\") ? key : `X-Custom-${key}`]: value,\n            }),\n            {},\n          ),\n        };\n      }\n      return acc;\n    }, {});\n\n    return {\n      ...(copilotApiConfig.headers || {}),\n      ...(copilotApiConfig.publicApiKey\n        ? { [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: copilotApiConfig.publicApiKey }\n        : {}),\n      ...authHeaders,\n    };\n  }, [copilotApiConfig.headers, copilotApiConfig.publicApiKey, authStates]);\n\n  const runtimeClient = useCopilotRuntimeClient({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    headers,\n    credentials: copilotApiConfig.credentials,\n  });\n\n  const [chatSuggestionConfiguration, setChatSuggestionConfiguration] = useState<{\n    [key: string]: CopilotChatSuggestionConfiguration;\n  }>({});\n\n  const addChatSuggestionConfiguration = (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => {\n    setChatSuggestionConfiguration((prev) => ({ ...prev, [id]: suggestion }));\n  };\n\n  const removeChatSuggestionConfiguration = (id: string) => {\n    setChatSuggestionConfiguration((prev) => {\n      const { [id]: _, ...rest } = prev;\n      return rest;\n    });\n  };\n\n  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);\n  const [coagentStates, setCoagentStates] = useState<Record<string, CoagentState>>({});\n  const coagentStatesRef = useRef<Record<string, CoagentState>>({});\n  const setCoagentStatesWithRef = useCallback(\n    (\n      value:\n        | Record<string, CoagentState>\n        | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n    ) => {\n      const newValue = typeof value === \"function\" ? value(coagentStatesRef.current) : value;\n      coagentStatesRef.current = newValue;\n      setCoagentStates((prev) => {\n        return newValue;\n      });\n    },\n    [],\n  );\n  const hasLoadedAgents = useRef(false);\n\n  useEffect(() => {\n    if (hasLoadedAgents.current) return;\n\n    const fetchData = async () => {\n      const result = await runtimeClient.availableAgents();\n      if (result.data?.availableAgents) {\n        setAvailableAgents(result.data.availableAgents.agents);\n      }\n      hasLoadedAgents.current = true;\n    };\n    void fetchData();\n  }, []);\n\n  let initialAgentSession: AgentSession | null = null;\n  if (props.agent) {\n    initialAgentSession = {\n      agentName: props.agent,\n    };\n  }\n\n  const [agentSession, setAgentSession] = useState<AgentSession | null>(initialAgentSession);\n\n  // Update agentSession when props.agent changes\n  useEffect(() => {\n    if (props.agent) {\n      setAgentSession({\n        agentName: props.agent,\n      });\n    } else {\n      setAgentSession(null);\n    }\n  }, [props.agent]);\n\n  const [internalThreadId, setInternalThreadId] = useState<string>(props.threadId || randomUUID());\n  const setThreadId = useCallback(\n    (value: SetStateAction<string>) => {\n      if (props.threadId) {\n        throw new Error(\"Cannot call setThreadId() when threadId is provided via props.\");\n      }\n      setInternalThreadId(value);\n    },\n    [props.threadId],\n  );\n\n  // update the internal threadId if the props.threadId changes\n  useEffect(() => {\n    if (props.threadId !== undefined) {\n      setInternalThreadId(props.threadId);\n    }\n  }, [props.threadId]);\n\n  const [runId, setRunId] = useState<string | null>(null);\n\n  const chatAbortControllerRef = useRef<AbortController | null>(null);\n\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n\n  const [langGraphInterruptAction, _setLangGraphInterruptAction] =\n    useState<LangGraphInterruptAction | null>(null);\n  const setLangGraphInterruptAction = useCallback((action: LangGraphInterruptActionSetterArgs) => {\n    _setLangGraphInterruptAction((prev) => {\n      if (prev == null) return action as LangGraphInterruptAction;\n      if (action == null) return null;\n      let event = prev.event;\n      if (action.event) {\n        // @ts-ignore\n        event = { ...prev.event, ...action.event };\n      }\n      return { ...prev, ...action, event };\n    });\n  }, []);\n  const removeLangGraphInterruptAction = useCallback((): void => {\n    setLangGraphInterruptAction(null);\n  }, []);\n\n  return (\n    <CopilotContext.Provider\n      value={{\n        actions,\n        chatComponentsCache,\n        getFunctionCallHandler,\n        setAction,\n        removeAction,\n        coAgentStateRenders,\n        setCoAgentStateRender,\n        removeCoAgentStateRender,\n        getContextString,\n        addContext,\n        removeContext,\n        getDocumentsContext,\n        addDocumentContext,\n        removeDocumentContext,\n        copilotApiConfig: copilotApiConfig,\n        isLoading,\n        setIsLoading,\n        chatSuggestionConfiguration,\n        addChatSuggestionConfiguration,\n        removeChatSuggestionConfiguration,\n        chatInstructions,\n        setChatInstructions,\n        additionalInstructions,\n        setAdditionalInstructions,\n        showDevConsole,\n        coagentStates,\n        setCoagentStates,\n        coagentStatesRef,\n        setCoagentStatesWithRef,\n        agentSession,\n        setAgentSession,\n        runtimeClient,\n        forwardedParameters: props.forwardedParameters || {},\n        agentLock: props.agent || null,\n        threadId: internalThreadId,\n        setThreadId,\n        runId,\n        setRunId,\n        chatAbortControllerRef,\n        availableAgents,\n        authConfig_c: props.authConfig_c,\n        authStates_c: authStates,\n        setAuthStates_c: setAuthStates,\n        extensions,\n        setExtensions,\n        langGraphInterruptAction,\n        setLangGraphInterruptAction,\n        removeLangGraphInterruptAction,\n      }}\n    >\n      <CopilotMessages>{children}</CopilotMessages>\n    </CopilotContext.Provider>\n  );\n}\n\nexport const defaultCopilotContextCategories = [\"global\"];\n\nfunction entryPointsToFunctionCallHandler(actions: FrontendAction<any>[]): FunctionCallHandler {\n  return async ({ name, args }) => {\n    let actionsByFunctionName: Record<string, FrontendAction<any>> = {};\n    for (let action of actions) {\n      actionsByFunctionName[action.name] = action;\n    }\n\n    const action = actionsByFunctionName[name];\n    let result: any = undefined;\n    if (action) {\n      await new Promise<void>((resolve, reject) => {\n        flushSync(async () => {\n          try {\n            result = await action.handler?.(args);\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n      });\n      await new Promise((resolve) => setTimeout(resolve, 20));\n    }\n    return result;\n  };\n}\n\nfunction formatFeatureName(featureName: string): string {\n  return featureName\n    .replace(/_c$/, \"\")\n    .split(\"_\")\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n    .join(\" \");\n}\n\nfunction validateProps(props: CopilotKitProps): never | void {\n  const cloudFeatures = Object.keys(props).filter((key) => key.endsWith(\"_c\"));\n\n  if (!props.runtimeUrl && !props.publicApiKey) {\n    throw new ConfigurationError(\"Missing required prop: 'runtimeUrl' or 'publicApiKey'\");\n  }\n\n  if (cloudFeatures.length > 0 && !props.publicApiKey) {\n    throw new MissingPublicApiKeyError(\n      `Missing required prop: 'publicApiKey' to use cloud features: ${cloudFeatures\n        .map(formatFeatureName)\n        .join(\", \")}`,\n    );\n  }\n}\n", "import { CopilotCloudConfig, FunctionCallHandler } from \"@copilotkit/shared\";\nimport {\n  ActionRenderProps,\n  CatchAllActionRenderProps,\n  FrontendAction,\n} from \"../types/frontend-action\";\nimport React from \"react\";\nimport { TreeNodeId } from \"../hooks/use-tree\";\nimport { DocumentPointer } from \"../types\";\nimport { CopilotChatSuggestionConfiguration } from \"../types/chat-suggestion-configuration\";\nimport { CoAgentStateRender, CoAgentStateRenderProps } from \"../types/coagent-action\";\nimport { CoagentState } from \"../types/coagent-state\";\nimport {\n  CopilotRuntimeClient,\n  ExtensionsInput,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { Agent } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetter,\n} from \"../types/interrupt-action\";\n\n/**\n * Interface for the configuration of the Copilot API.\n */\nexport interface CopilotApiConfig {\n  /**\n   * The public API key for Copilot Cloud.\n   */\n  publicApiKey?: string;\n\n  /**\n   * The configuration for Copilot Cloud.\n   */\n  cloud?: CopilotCloudConfig;\n\n  /**\n   * The endpoint for the chat API.\n   */\n  chatApiEndpoint: string;\n\n  /**\n   * The endpoint for the Copilot transcribe audio service.\n   */\n  transcribeAudioUrl?: string;\n\n  /**\n   * The endpoint for the Copilot text to speech service.\n   */\n  textToSpeechUrl?: string;\n\n  /**\n   * additional headers to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'Authorization': 'Bearer your_token_here'\n   * }\n   * ```\n   */\n  headers: Record<string, string>;\n\n  /**\n   * Custom properties to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'user_id': 'user_id'\n   * }\n   * ```\n   */\n  properties?: Record<string, any>;\n\n  /**\n   * Indicates whether the user agent should send or receive cookies from the other domain\n   * in the case of cross-origin requests.\n   */\n  credentials?: RequestCredentials;\n\n  /**\n   * Optional configuration for connecting to Model Context Protocol (MCP) servers.\n   * This is typically derived from the CopilotKitProps and used internally.\n   * @experimental\n   */\n  mcpServers?: Array<{ endpoint: string; apiKey?: string }>;\n}\n\nexport type InChatRenderFunction<TProps = ActionRenderProps<any> | CatchAllActionRenderProps<any>> =\n  (props: TProps) => string | JSX.Element;\nexport type CoagentInChatRenderFunction = (\n  props: CoAgentStateRenderProps<any>,\n) => string | JSX.Element | undefined | null;\n\nexport interface ChatComponentsCache {\n  actions: Record<string, InChatRenderFunction | string>;\n  coAgentStateRenders: Record<string, CoagentInChatRenderFunction | string>;\n}\n\nexport interface AgentSession {\n  agentName: string;\n  threadId?: string;\n  nodeName?: string;\n}\n\nexport interface AuthState {\n  status: \"authenticated\" | \"unauthenticated\";\n  authHeaders: Record<string, string>;\n  userId?: string;\n  metadata?: Record<string, any>;\n}\n\nexport type ActionName = string;\n\nexport interface CopilotContextParams {\n  // function-calling\n  actions: Record<string, FrontendAction<any>>;\n  setAction: (id: string, action: FrontendAction<any>) => void;\n  removeAction: (id: string) => void;\n\n  // coagent actions\n  coAgentStateRenders: Record<string, CoAgentStateRender<any>>;\n  setCoAgentStateRender: (id: string, stateRender: CoAgentStateRender<any>) => void;\n  removeCoAgentStateRender: (id: string) => void;\n\n  chatComponentsCache: React.RefObject<ChatComponentsCache>;\n\n  getFunctionCallHandler: (\n    customEntryPoints?: Record<string, FrontendAction<any>>,\n  ) => FunctionCallHandler;\n\n  // text context\n  addContext: (context: string, parentId?: string, categories?: string[]) => TreeNodeId;\n  removeContext: (id: TreeNodeId) => void;\n  getContextString: (documents: DocumentPointer[], categories: string[]) => string;\n\n  // document context\n  addDocumentContext: (documentPointer: DocumentPointer, categories?: string[]) => TreeNodeId;\n  removeDocumentContext: (documentId: string) => void;\n  getDocumentsContext: (categories: string[]) => DocumentPointer[];\n\n  isLoading: boolean;\n  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;\n\n  chatSuggestionConfiguration: { [key: string]: CopilotChatSuggestionConfiguration };\n  addChatSuggestionConfiguration: (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => void;\n  removeChatSuggestionConfiguration: (id: string) => void;\n\n  chatInstructions: string;\n  setChatInstructions: React.Dispatch<React.SetStateAction<string>>;\n\n  additionalInstructions?: string[];\n  setAdditionalInstructions: React.Dispatch<React.SetStateAction<string[]>>;\n\n  // api endpoints\n  copilotApiConfig: CopilotApiConfig;\n\n  showDevConsole: boolean | \"auto\";\n\n  // agents\n  coagentStates: Record<string, CoagentState>;\n  setCoagentStates: React.Dispatch<React.SetStateAction<Record<string, CoagentState>>>;\n  coagentStatesRef: React.RefObject<Record<string, CoagentState>>;\n  setCoagentStatesWithRef: (\n    value:\n      | Record<string, CoagentState>\n      | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n  ) => void;\n\n  agentSession: AgentSession | null;\n  setAgentSession: React.Dispatch<React.SetStateAction<AgentSession | null>>;\n\n  agentLock: string | null;\n\n  threadId: string;\n  setThreadId: React.Dispatch<React.SetStateAction<string>>;\n\n  runId: string | null;\n  setRunId: React.Dispatch<React.SetStateAction<string | null>>;\n\n  // The chat abort controller can be used to stop generation globally,\n  // i.e. when using `stop()` from `useChat`\n  chatAbortControllerRef: React.MutableRefObject<AbortController | null>;\n\n  // runtime\n  runtimeClient: CopilotRuntimeClient;\n\n  /**\n   * The forwarded parameters to use for the task.\n   */\n  forwardedParameters?: Pick<ForwardedParametersInput, \"temperature\">;\n  availableAgents: Agent[];\n\n  /**\n   * The auth states for the CopilotKit.\n   */\n  authStates_c?: Record<ActionName, AuthState>;\n  setAuthStates_c?: React.Dispatch<React.SetStateAction<Record<ActionName, AuthState>>>;\n\n  /**\n   * The auth config for the CopilotKit.\n   */\n  authConfig_c?: {\n    SignInComponent: React.ComponentType<{\n      onSignInComplete: (authState: AuthState) => void;\n    }>;\n  };\n\n  extensions: ExtensionsInput;\n  setExtensions: React.Dispatch<React.SetStateAction<ExtensionsInput>>;\n  langGraphInterruptAction: LangGraphInterruptAction | null;\n  setLangGraphInterruptAction: LangGraphInterruptActionSetter;\n  removeLangGraphInterruptAction: () => void;\n}\n\nconst emptyCopilotContext: CopilotContextParams = {\n  actions: {},\n  setAction: () => {},\n  removeAction: () => {},\n\n  coAgentStateRenders: {},\n  setCoAgentStateRender: () => {},\n  removeCoAgentStateRender: () => {},\n\n  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },\n  getContextString: (documents: DocumentPointer[], categories: string[]) =>\n    returnAndThrowInDebug(\"\"),\n  addContext: () => \"\",\n  removeContext: () => {},\n\n  getFunctionCallHandler: () => returnAndThrowInDebug(async () => {}),\n\n  isLoading: false,\n  setIsLoading: () => returnAndThrowInDebug(false),\n\n  chatInstructions: \"\",\n  setChatInstructions: () => returnAndThrowInDebug(\"\"),\n\n  additionalInstructions: [],\n  setAdditionalInstructions: () => returnAndThrowInDebug([]),\n\n  getDocumentsContext: (categories: string[]) => returnAndThrowInDebug([]),\n  addDocumentContext: () => returnAndThrowInDebug(\"\"),\n  removeDocumentContext: () => {},\n  runtimeClient: {} as any,\n\n  copilotApiConfig: new (class implements CopilotApiConfig {\n    get chatApiEndpoint(): string {\n      throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n    }\n\n    get headers(): Record<string, string> {\n      return {};\n    }\n    get body(): Record<string, any> {\n      return {};\n    }\n  })(),\n\n  chatSuggestionConfiguration: {},\n  addChatSuggestionConfiguration: () => {},\n  removeChatSuggestionConfiguration: () => {},\n  showDevConsole: \"auto\",\n  coagentStates: {},\n  setCoagentStates: () => {},\n  coagentStatesRef: { current: {} },\n  setCoagentStatesWithRef: () => {},\n  agentSession: null,\n  setAgentSession: () => {},\n  forwardedParameters: {},\n  agentLock: null,\n  threadId: \"\",\n  setThreadId: () => {},\n  runId: null,\n  setRunId: () => {},\n  chatAbortControllerRef: { current: null },\n  availableAgents: [],\n  extensions: {},\n  setExtensions: () => {},\n  langGraphInterruptAction: null,\n  setLangGraphInterruptAction: () => null,\n  removeLangGraphInterruptAction: () => null,\n};\n\nexport const CopilotContext = React.createContext<CopilotContextParams>(emptyCopilotContext);\n\nexport function useCopilotContext(): CopilotContextParams {\n  const context = React.useContext(CopilotContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n  }\n  return context;\n}\n\nfunction returnAndThrowInDebug<T>(_value: T): T {\n  throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n}\n", "import { randomId } from \"@copilotkit/shared\";\nimport { useCallback, useReducer } from \"react\";\n\nexport type TreeNodeId = string;\n\nexport interface TreeNode {\n  id: TreeNodeId;\n  value: string;\n  children: TreeNode[];\n  parentId?: TreeNodeId;\n  categories: Set<string>;\n}\n\nexport type Tree = TreeNode[];\n\nexport interface UseTreeReturn {\n  tree: Tree;\n  addElement: (value: string, categories: string[], parentId?: TreeNodeId) => TreeNodeId;\n  printTree: (categories: string[]) => string;\n  removeElement: (id: TreeNodeId) => void;\n}\n\nconst findNode = (nodes: Tree, id: TreeNodeId): TreeNode | undefined => {\n  for (const node of nodes) {\n    if (node.id === id) {\n      return node;\n    }\n    const result = findNode(node.children, id);\n    if (result) {\n      return result;\n    }\n  }\n  return undefined;\n};\n\nconst removeNode = (nodes: Tree, id: TreeNodeId): Tree => {\n  return nodes.reduce((result: Tree, node) => {\n    if (node.id !== id) {\n      const newNode = { ...node, children: removeNode(node.children, id) };\n      result.push(newNode);\n    }\n    return result;\n  }, []);\n};\n\nconst addNode = (nodes: Tree, newNode: TreeNode, parentId?: TreeNodeId): Tree => {\n  if (!parentId) {\n    return [...nodes, newNode];\n  }\n  return nodes.map((node) => {\n    if (node.id === parentId) {\n      return { ...node, children: [...node.children, newNode] };\n    } else if (node.children.length) {\n      return { ...node, children: addNode(node.children, newNode, parentId) };\n    }\n    return node;\n  });\n};\n\nconst treeIndentationRepresentation = (index: number, indentLevel: number): string => {\n  if (indentLevel === 0) {\n    return (index + 1).toString();\n  } else if (indentLevel === 1) {\n    return String.fromCharCode(65 + index); // 65 is the ASCII value for 'A'\n  } else if (indentLevel === 2) {\n    return String.fromCharCode(97 + index); // 97 is the ASCII value for 'a'\n  } else {\n    return \"-\";\n  }\n};\n\nconst printNode = (node: TreeNode, prefix = \"\", indentLevel = 0): string => {\n  const indent = \" \".repeat(3).repeat(indentLevel);\n\n  const prefixPlusIndentLength = prefix.length + indent.length;\n  const subsequentLinesPrefix = \" \".repeat(prefixPlusIndentLength);\n\n  const valueLines = node.value.split(\"\\n\");\n\n  const outputFirstLine = `${indent}${prefix}${valueLines[0]}`;\n  const outputSubsequentLines = valueLines\n    .slice(1)\n    .map((line) => `${subsequentLinesPrefix}${line}`)\n    .join(\"\\n\");\n\n  let output = `${outputFirstLine}\\n`;\n  if (outputSubsequentLines) {\n    output += `${outputSubsequentLines}\\n`;\n  }\n\n  const childPrePrefix = \" \".repeat(prefix.length);\n\n  node.children.forEach(\n    (child, index) =>\n      (output += printNode(\n        child,\n        `${childPrePrefix}${treeIndentationRepresentation(index, indentLevel + 1)}. `,\n        indentLevel + 1,\n      )),\n  );\n  return output;\n};\n\n// Action types\ntype Action =\n  | {\n      type: \"ADD_NODE\";\n      value: string;\n      parentId?: string;\n      id: string;\n      categories: string[];\n    }\n  | { type: \"REMOVE_NODE\"; id: string };\n\n// Reducer function\nfunction treeReducer(state: Tree, action: Action): Tree {\n  switch (action.type) {\n    case \"ADD_NODE\": {\n      const { value, parentId, id: newNodeId } = action;\n      const newNode: TreeNode = {\n        id: newNodeId,\n        value,\n        children: [],\n        categories: new Set(action.categories),\n      };\n\n      try {\n        return addNode(state, newNode, parentId);\n      } catch (error) {\n        console.error(`Error while adding node with id ${newNodeId}: ${error}`);\n        return state;\n      }\n    }\n    case \"REMOVE_NODE\":\n      return removeNode(state, action.id);\n    default:\n      return state;\n  }\n}\n\n// useTree hook\nconst useTree = (): UseTreeReturn => {\n  const [tree, dispatch] = useReducer(treeReducer, []);\n\n  const addElement = useCallback(\n    (value: string, categories: string[], parentId?: string): TreeNodeId => {\n      const newNodeId = randomId(); // Generate new ID outside of dispatch\n      dispatch({\n        type: \"ADD_NODE\",\n        value,\n        parentId,\n        id: newNodeId,\n        categories: categories,\n      });\n      return newNodeId; // Return the new ID\n    },\n    [],\n  );\n\n  const removeElement = useCallback((id: TreeNodeId): void => {\n    dispatch({ type: \"REMOVE_NODE\", id });\n  }, []);\n\n  const printTree = useCallback(\n    (categories: string[]): string => {\n      const categoriesSet = new Set(categories);\n\n      let output = \"\";\n      tree.forEach((node, index) => {\n        // if the node does not have any of the desired categories, continue to the next node\n        if (!setsHaveIntersection(categoriesSet, node.categories)) {\n          return;\n        }\n\n        // add a new line before each node except the first one\n        if (index !== 0) {\n          output += \"\\n\";\n        }\n\n        output += printNode(node, `${treeIndentationRepresentation(index, 0)}. `);\n      });\n      return output;\n    },\n    [tree],\n  );\n\n  return { tree, addElement, printTree, removeElement };\n};\n\nexport default useTree;\n\nfunction setsHaveIntersection<T>(setA: Set<T>, setB: Set<T>): boolean {\n  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];\n\n  for (let item of smallerSet) {\n    if (largerSet.has(item)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "import { useCallback, useReducer } from \"react\";\nimport { randomId } from \"@copilotkit/shared\";\n\nexport type FlatCategoryStoreId = string;\n\nexport interface UseFlatCategoryStoreReturn<T> {\n  addElement: (value: T, categories: string[]) => FlatCategoryStoreId;\n  removeElement: (id: FlatCategoryStoreId) => void;\n  allElements: (categories: string[]) => T[];\n}\n\ninterface FlatCategoryStoreElement<T> {\n  id: FlatCategoryStoreId;\n  value: T;\n  categories: Set<string>;\n}\n\nconst useFlatCategoryStore = <T>(): UseFlatCategoryStoreReturn<T> => {\n  const [elements, dispatch] = useReducer<\n    React.Reducer<Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>>, Action<T>>\n  >(flatCategoryStoreReducer, new Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>>());\n\n  const addElement = useCallback((value: T, categories: string[]): FlatCategoryStoreId => {\n    const newId = randomId();\n    dispatch({\n      type: \"ADD_ELEMENT\",\n      value,\n      id: newId,\n      categories,\n    });\n    return newId;\n  }, []);\n\n  const removeElement = useCallback((id: FlatCategoryStoreId): void => {\n    dispatch({ type: \"REMOVE_ELEMENT\", id });\n  }, []);\n\n  const allElements = useCallback(\n    (categories: string[]): T[] => {\n      const categoriesSet = new Set(categories);\n      const result: T[] = [];\n      elements.forEach((element) => {\n        if (setsHaveIntersection(categoriesSet, element.categories)) {\n          result.push(element.value);\n        }\n      });\n      return result;\n    },\n    [elements],\n  );\n\n  return { addElement, removeElement, allElements };\n};\n\nexport default useFlatCategoryStore;\n\n// Action types\ntype Action<T> =\n  | {\n      type: \"ADD_ELEMENT\";\n      value: T;\n      id: FlatCategoryStoreId;\n      categories: string[];\n    }\n  | { type: \"REMOVE_ELEMENT\"; id: FlatCategoryStoreId };\n\n// Reducer\nfunction flatCategoryStoreReducer<T>(\n  state: Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>>,\n  action: Action<T>,\n): Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>> {\n  switch (action.type) {\n    case \"ADD_ELEMENT\": {\n      const { value, id, categories } = action;\n      const newElement: FlatCategoryStoreElement<T> = {\n        id,\n        value,\n        categories: new Set(categories),\n      };\n      const newState = new Map(state);\n      newState.set(id, newElement);\n      return newState;\n    }\n    case \"REMOVE_ELEMENT\": {\n      const newState = new Map(state);\n      newState.delete(action.id);\n      return newState;\n    }\n    default:\n      return state;\n  }\n}\n\nfunction setsHaveIntersection<T>(setA: Set<T>, setB: Set<T>): boolean {\n  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];\n\n  for (let item of smallerSet) {\n    if (largerSet.has(item)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n", "/**\n * An internal context to separate the messages state (which is constantly changing) from the rest of CopilotKit context\n */\n\nimport { ReactNode, useEffect, useState, useRef } from \"react\";\nimport { CopilotMessagesContext } from \"../../context/copilot-messages-context\";\nimport { loadMessagesFromJsonRepresentation, Message } from \"@copilotkit/runtime-client-gql\";\nimport { useCopilotContext } from \"../../context/copilot-context\";\n\nexport function CopilotMessages({ children }: { children: ReactNode }) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const lastLoadedThreadId = useRef<string>();\n  const lastLoadedAgentName = useRef<string>();\n  const lastLoadedMessages = useRef<string>();\n\n  const { threadId, agentSession, runtimeClient } = useCopilotContext();\n\n  useEffect(() => {\n    if (!threadId || threadId === lastLoadedThreadId.current) return;\n    if (\n      threadId === lastLoadedThreadId.current &&\n      agentSession?.agentName === lastLoadedAgentName.current\n    ) {\n      return;\n    }\n\n    const fetchMessages = async () => {\n      if (!agentSession?.agentName) return;\n\n      const result = await runtimeClient.loadAgentState({\n        threadId,\n        agentName: agentSession?.agentName,\n      });\n\n      const newMessages = result.data?.loadAgentState?.messages;\n      if (newMessages === lastLoadedMessages.current) return;\n\n      if (result.data?.loadAgentState?.threadExists) {\n        lastLoadedMessages.current = newMessages;\n        lastLoadedThreadId.current = threadId;\n        lastLoadedAgentName.current = agentSession?.agentName;\n\n        const messages = loadMessagesFromJsonRepresentation(JSON.parse(newMessages || \"[]\"));\n        setMessages(messages);\n      }\n    };\n    void fetchMessages();\n  }, [threadId, agentSession?.agentName]);\n\n  return (\n    <CopilotMessagesContext.Provider\n      value={{\n        messages,\n        setMessages,\n      }}\n    >\n      {children}\n    </CopilotMessagesContext.Provider>\n  );\n}\n", "/**\n * An internal context to separate the messages state (which is constantly changing) from the rest of CopilotKit context\n */\n\nimport { Message } from \"@copilotkit/runtime-client-gql\";\nimport React from \"react\";\n\nexport interface CopilotMessagesContextParams {\n  messages: Message[];\n  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;\n}\n\nconst emptyCopilotContext: CopilotMessagesContextParams = {\n  messages: [],\n  setMessages: () => [],\n};\n\nexport const CopilotMessagesContext =\n  React.createContext<CopilotMessagesContextParams>(emptyCopilotContext);\n\nexport function useCopilotMessagesContext(): CopilotMessagesContextParams {\n  const context = React.useContext(CopilotMessagesContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\n      \"A messages consuming component was not wrapped with `<CopilotMessages> {...} </CopilotMessages>`\",\n    );\n  }\n  return context;\n}\n", "import { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport React, { createContext, useContext, useState, useCallback } from \"react\";\nimport { ErrorToast } from \"../error-boundary/error-utils\";\nimport { PartialBy } from \"@copilotkit/shared\";\n\ninterface Toast {\n  id: string;\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  duration?: number;\n}\n\ninterface ToastContextValue {\n  toasts: Toast[];\n  addToast: (toast: PartialBy<Toast, \"id\">) => void;\n  addGraphQLErrorsToast: (errors: GraphQLError[]) => void;\n  removeToast: (id: string) => void;\n  enabled: boolean;\n}\n\nconst ToastContext = createContext<ToastContextValue | undefined>(undefined);\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error(\"useToast must be used within a ToastProvider\");\n  }\n  return context;\n}\n\nexport function ToastProvider({\n  enabled,\n  children,\n}: {\n  enabled: boolean;\n  children: React.ReactNode;\n}) {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n  const addToast = useCallback(\n    (toast: PartialBy<Toast, \"id\">) => {\n      // We do not display these errors unless we are in dev mode.\n      if (!enabled) {\n        return;\n      }\n\n      const id = toast.id ?? Math.random().toString(36).substring(2, 9);\n\n      setToasts((currentToasts) => {\n        if (currentToasts.find((toast) => toast.id === id)) return currentToasts;\n        return [...currentToasts, { ...toast, id }];\n      });\n\n      if (toast.duration) {\n        setTimeout(() => {\n          removeToast(id);\n        }, toast.duration);\n      }\n    },\n    [enabled],\n  );\n\n  const addGraphQLErrorsToast = useCallback((errors: GraphQLError[]) => {\n    addToast({\n      type: \"error\",\n      message: <ErrorToast errors={errors} />,\n    });\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));\n  }, []);\n\n  const value = {\n    toasts,\n    addToast,\n    addGraphQLErrorsToast,\n    removeToast,\n    enabled,\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      <div\n        style={{\n          position: \"fixed\",\n          bottom: \"1rem\",\n          left: \"50%\",\n          transform: \"translateX(-50%)\",\n          zIndex: 50,\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"0.5rem\",\n        }}\n      >\n        {toasts.length > 1 && (\n          <div style={{ textAlign: \"right\" }}>\n            <button\n              onClick={() => setToasts([])}\n              style={{\n                padding: \"4px 8px\",\n                fontSize: \"12px\",\n                cursor: \"pointer\",\n                background: \"white\",\n                border: \"1px solid rgba(0,0,0,0.2)\",\n                borderRadius: \"4px\",\n              }}\n            >\n              Close All\n            </button>\n          </div>\n        )}\n        {toasts.map((toast) => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n      {children}\n    </ToastContext.Provider>\n  );\n}\n\nfunction Toast({\n  message,\n  type = \"info\",\n  onClose,\n}: {\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  onClose: () => void;\n}) {\n  const bgColors = {\n    info: \"#3b82f6\",\n    success: \"#22c55e\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  };\n\n  return (\n    <div\n      style={{\n        backgroundColor: bgColors[type],\n        color: \"white\",\n        padding: \"0.5rem 1.5rem\",\n        borderRadius: \"0.25rem\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        position: \"relative\",\n        minWidth: \"200px\",\n      }}\n    >\n      <div>{message}</div>\n      <button\n        onClick={onClose}\n        style={{\n          position: \"absolute\",\n          top: \"0\",\n          right: \"0\",\n          background: \"none\",\n          border: \"none\",\n          color: \"white\",\n          cursor: \"pointer\",\n          padding: \"0.5rem\",\n          fontSize: \"1rem\",\n        }}\n      >\n        ✕\n      </button>\n    </div>\n  );\n}\n", "import React, { useCallback } from \"react\";\nimport { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport { useToast } from \"../toast/toast-provider\";\nimport { ExclamationMarkIcon } from \"../toast/exclamation-mark-icon\";\nimport ReactMarkdown from \"react-markdown\";\n\ninterface OriginalError {\n  message?: string;\n  stack?: string;\n}\n\nexport function ErrorToast({ errors }: { errors: (Error | GraphQLError)[] }) {\n  const errorsToRender = errors.map((error, idx) => {\n    const originalError =\n      \"extensions\" in error ? (error.extensions?.originalError as undefined | OriginalError) : {};\n    const message = originalError?.message ?? error.message;\n    const code = \"extensions\" in error ? (error.extensions?.code as string) : null;\n\n    return (\n      <div\n        key={idx}\n        style={{\n          marginTop: idx === 0 ? 0 : 10,\n          marginBottom: 14,\n        }}\n      >\n        <ExclamationMarkIcon style={{ marginBottom: 4 }} />\n\n        {code && (\n          <div\n            style={{\n              fontWeight: \"600\",\n              marginBottom: 4,\n            }}\n          >\n            Copilot Cloud Error:{\" \"}\n            <span style={{ fontFamily: \"monospace\", fontWeight: \"normal\" }}>{code}</span>\n          </div>\n        )}\n        <ReactMarkdown>{message}</ReactMarkdown>\n      </div>\n    );\n  });\n  return (\n    <div\n      style={{\n        fontSize: \"13px\",\n        maxWidth: \"600px\",\n      }}\n    >\n      {errorsToRender}\n      <div style={{ fontSize: \"11px\", opacity: 0.75 }}>\n        NOTE: This error only displays during local development.\n      </div>\n    </div>\n  );\n}\n\nexport function useErrorToast() {\n  const { addToast } = useToast();\n\n  return useCallback(\n    (error: (Error | GraphQLError)[]) => {\n      const errorId = error\n        .map((err) => {\n          const message =\n            \"extensions\" in err\n              ? (err.extensions?.originalError as any)?.message || err.message\n              : err.message;\n          const stack = err.stack || \"\";\n          return btoa(message + stack).slice(0, 32); // Create hash from message + stack\n        })\n        .join(\"|\");\n\n      addToast({\n        type: \"error\",\n        id: errorId, // Toast libraries typically dedupe by id\n        message: <ErrorToast errors={error} />,\n      });\n    },\n    [addToast],\n  );\n}\n\nexport function useAsyncCallback<T extends (...args: any[]) => Promise<any>>(\n  callback: T,\n  deps: Parameters<typeof useCallback>[1],\n) {\n  const addErrorToast = useErrorToast();\n  return useCallback(async (...args: Parameters<T>) => {\n    try {\n      return await callback(...args);\n    } catch (error) {\n      console.error(\"Error in async callback:\", error);\n      // @ts-ignore\n      addErrorToast([error]);\n      throw error;\n    }\n  }, deps);\n}\n", "import React from \"react\";\n\nexport const ExclamationMarkIcon = ({\n  className,\n  style,\n}: {\n  className?: string;\n  style?: React.CSSProperties;\n}) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    className={`lucide lucide-circle-alert ${className ? className : \"\"}`}\n    style={style}\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <line x1=\"12\" x2=\"12\" y1=\"8\" y2=\"12\" />\n    <line x1=\"12\" x2=\"12.01\" y1=\"16\" y2=\"16\" />\n  </svg>\n);\n", "import {\n  CopilotRuntimeClient,\n  CopilotRuntimeClientOptions,\n  GraphQLError,\n} from \"@copilotkit/runtime-client-gql\";\nimport { useToast } from \"../components/toast/toast-provider\";\nimport { useMemo } from \"react\";\nimport { useErrorToast } from \"../components/error-boundary/error-utils\";\n\nexport const useCopilotRuntimeClient = (options: CopilotRuntimeClientOptions) => {\n  const { addGraphQLErrorsToast } = useToast();\n  const addErrorToast = useErrorToast();\n  const { addToast } = useToast();\n\n  const runtimeClient = useMemo(() => {\n    return new CopilotRuntimeClient({\n      ...options,\n      handleGQLErrors: (error) => {\n        if ((error as any).graphQLErrors.length) {\n          addGraphQLErrorsToast((error as any).graphQLErrors as GraphQLError[]);\n        } else {\n          addErrorToast([error]);\n        }\n      },\n      handleGQLWarning: (message: string) => {\n        console.warn(message);\n        addToast({ type: \"warning\", message });\n      },\n    });\n  }, [options, addGraphQLErrorsToast, addToast]);\n\n  return runtimeClient;\n};\n", "export function shouldShowDevConsole(showDevConsole: boolean | \"auto\"): boolean {\n  if (typeof showDevConsole === \"boolean\") {\n    return showDevConsole;\n  }\n  return (\n    getHostname() === \"localhost\" ||\n    getHostname() === \"127.0.0.1\" ||\n    getHostname() === \"0.0.0.0\" ||\n    getHostname() === \"::1\"\n  );\n}\n\nfunction getHostname(): string {\n  if (typeof window !== \"undefined\" && window.location) {\n    return window.location.hostname;\n  }\n  return \"\";\n}\n", "import React, { useEffect } from \"react\";\nimport { Severity, CopilotKitError } from \"@copilotkit/shared\";\nimport { StatusChecker } from \"../../lib/status-checker\";\nimport { renderCopilotKitUsage, UsageBanner } from \"../usage-banner\";\nimport { useErrorToast } from \"./error-utils\";\nimport { COPILOT_CLOUD_ERROR_NAMES } from \"@copilotkit/shared\";\n\nconst statusChecker = new StatusChecker();\n\ninterface Props {\n  children: React.ReactNode;\n  publicApiKey?: string;\n  showUsageBanner?: boolean;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: CopilotKitError;\n  status?: {\n    severity: Severity;\n    message: string;\n  };\n}\n\nexport class CopilotErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n    };\n  }\n\n  static getDerivedStateFromError(error: CopilotKitError): State {\n    return { hasError: true, error };\n  }\n\n  componentDidMount() {\n    if (this.props.publicApiKey) {\n      statusChecker.start(this.props.publicApiKey, (newStatus) => {\n        this.setState((prevState) => {\n          if (newStatus?.severity !== prevState.status?.severity) {\n            return { status: newStatus ?? undefined };\n          }\n          return null;\n        });\n      });\n    }\n  }\n\n  componentWillUnmount() {\n    statusChecker.stop();\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"CopilotKit Error:\", error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.state.error instanceof CopilotKitError) {\n        // @ts-expect-error -- It's a copilotkit error at this state. Name is valid\n        if (COPILOT_CLOUD_ERROR_NAMES.includes(this.state.error.name)) {\n          return (\n            <ErrorToast error={this.state.error}>\n              {renderCopilotKitUsage(this.state.error)}\n            </ErrorToast>\n          );\n        }\n\n        return (\n          <>\n            {this.props.children}\n            {this.props.showUsageBanner && (\n              <UsageBanner\n                severity={this.state.status?.severity}\n                message={this.state.status?.message}\n              />\n            )}\n          </>\n        );\n      }\n      throw this.state.error;\n    }\n\n    return this.props.children;\n  }\n}\n\nexport function ErrorToast({ error, children }: { error?: Error; children: React.ReactNode }) {\n  const addErrorToast = useErrorToast();\n\n  useEffect(() => {\n    if (error) {\n      addErrorToast([error]);\n    }\n  }, [error, addErrorToast]);\n\n  if (!error) throw error;\n  return children;\n}\n", "import {\n  COPILOT_CLOUD_API_URL,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  Severity,\n} from \"@copilotkit/shared\";\n\nconst STATUS_CHECK_INTERVAL = 1000 * 60 * 5; // 5 minutes\n\nexport type Status = {\n  severity: Severity;\n  message: string;\n};\n\nexport class StatusChecker {\n  private activeKey: string | null = null;\n  private intervalId: ReturnType<typeof setInterval> | null = null;\n  private instanceCount = 0;\n  private lastResponse: Status | null = null;\n\n  async start(publicApiKey: string, onUpdate?: (status: Status | null) => void) {\n    this.instanceCount++;\n    if (this.activeKey === publicApiKey) return;\n\n    if (this.intervalId) clearInterval(this.intervalId);\n\n    const checkStatus = async () => {\n      try {\n        const response = await fetch(`${COPILOT_CLOUD_API_URL}/ciu`, {\n          method: \"GET\",\n          headers: {\n            [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey,\n          },\n        }).then((response) => response.json() as Promise<Status>);\n        this.lastResponse = response;\n        onUpdate?.(response);\n        return response;\n      } catch (error) {\n        // Silently fail\n        return null;\n      }\n    };\n\n    const initialResponse = await checkStatus();\n    this.intervalId = setInterval(checkStatus, STATUS_CHECK_INTERVAL);\n    this.activeKey = publicApiKey;\n    return initialResponse;\n  }\n\n  getLastResponse() {\n    return this.lastResponse;\n  }\n\n  stop() {\n    this.instanceCount--;\n    if (this.instanceCount === 0) {\n      if (this.intervalId) {\n        clearInterval(this.intervalId);\n        this.intervalId = null;\n        this.activeKey = null;\n        this.lastResponse = null;\n      }\n    }\n  }\n}\n", "import { Severity, CopilotKitError, ERROR_NAMES } from \"@copilotkit/shared\";\n\ninterface UsageBannerProps {\n  severity?: Severity;\n  message?: string;\n  icon?: React.ReactNode;\n  actions?: {\n    primary?: {\n      label: string;\n      onClick: () => void;\n    };\n    secondary?: {\n      label: string;\n      onClick: () => void;\n    };\n  };\n}\n\nconst defaultIcons: Record<Severity, JSX.Element> = {\n  [Severity.Error]: (\n    <svg\n      viewBox=\"0 0 24 24\"\n      width=\"20\"\n      height=\"20\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      fill=\"none\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" />\n      <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" />\n      <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" />\n    </svg>\n  ),\n};\n\nexport function UsageBanner({\n  severity = Severity.Error,\n  message = \"\",\n  icon,\n  actions,\n}: UsageBannerProps) {\n  if (!message || !severity) {\n    return null;\n  }\n\n  const Icon = icon || defaultIcons[severity];\n\n  const bgColor = {\n    info: \"#dbeafe\",\n    warning: \"#fef3c7\",\n    error: \"#fee2e2\",\n  }[severity];\n\n  const textColor = {\n    info: \"#1e40af\",\n    warning: \"#854d0e\",\n    error: \"#991b1b\",\n  }[severity];\n\n  const iconColor = {\n    info: \"#3b82f6\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  }[severity];\n\n  const primaryButtonColor = {\n    info: \"#3b82f6\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  }[severity];\n\n  const primaryButtonHoverColor = {\n    info: \"#2563eb\",\n    warning: \"#ca8a04\",\n    error: \"#dc2626\",\n  }[severity];\n\n  return (\n    <div\n      style={{\n        position: \"fixed\",\n        bottom: \"16px\",\n        left: \"50%\",\n        transform: \"translateX(-50%)\",\n        maxWidth: \"90%\",\n        zIndex: 9999,\n      }}\n    >\n      <div\n        style={{\n          display: \"flex\",\n          flexWrap: \"wrap\",\n          alignItems: \"center\",\n          gap: \"12px\",\n          borderRadius: \"9999px\",\n          border: \"1px solid #e5e7eb\",\n          backgroundColor: bgColor,\n          padding: \"8px 16px\",\n          boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\",\n        }}\n      >\n        <div style={{ color: iconColor }}>{Icon}</div>\n        <span\n          style={{\n            flex: 1,\n            fontSize: \"14px\",\n            fontWeight: 500,\n            color: textColor,\n            whiteSpace: \"normal\",\n            wordBreak: \"break-word\",\n          }}\n        >\n          {message}\n        </span>\n        <div\n          style={{\n            display: \"flex\",\n            gap: \"8px\",\n            flexWrap: \"wrap\",\n          }}\n        >\n          {actions?.secondary && (\n            <button\n              onClick={actions.secondary.onClick}\n              style={{\n                borderRadius: \"9999px\",\n                padding: \"4px 12px\",\n                fontSize: \"14px\",\n                fontWeight: 500,\n                color: textColor,\n                backgroundColor: \"transparent\",\n                border: \"none\",\n                cursor: \"pointer\",\n                transition: \"background-color 0.2s\",\n              }}\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.5)\")}\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = \"transparent\")}\n            >\n              {actions.secondary.label}\n            </button>\n          )}\n          {actions?.primary && (\n            <button\n              onClick={actions.primary.onClick}\n              style={{\n                borderRadius: \"9999px\",\n                padding: \"4px 12px\",\n                fontSize: \"14px\",\n                fontWeight: 500,\n                color: \"#fff\",\n                backgroundColor: primaryButtonColor,\n                border: \"none\",\n                cursor: \"pointer\",\n                transition: \"background-color 0.2s\",\n              }}\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = primaryButtonHoverColor)}\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = primaryButtonColor)}\n            >\n              {actions.primary.label}\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function renderCopilotKitUsage(error: CopilotKitError) {\n  switch (error.name) {\n    case ERROR_NAMES.CONFIGURATION_ERROR:\n      return <UsageBanner severity={error.severity} message={error.message} />;\n    case ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR:\n      return (\n        <UsageBanner\n          severity={error.severity}\n          message={error.message}\n          actions={{\n            primary: {\n              label: \"Sign In\",\n              onClick: () => {\n                window.location.href = \"https://cloud.copilotkit.ai\";\n              },\n            },\n          }}\n        />\n      );\n    case ERROR_NAMES.UPGRADE_REQUIRED_ERROR:\n      return (\n        <UsageBanner\n          severity={error.severity}\n          message={error.message}\n          actions={{\n            primary: {\n              label: \"Upgrade\",\n              onClick: () => {\n                window.location.href = \"https://copilotkit.ai/\";\n              },\n            },\n          }}\n        />\n      );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,IAAAA,iBAAkF;;;ACVlF,mBAAkB;AAsNlB,IAAM,sBAA4C;AAAA,EAChD,SAAS,CAAC;AAAA,EACV,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,cAAc,MAAM;AAAA,EAAC;AAAA,EAErB,qBAAqB,CAAC;AAAA,EACtB,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,0BAA0B,MAAM;AAAA,EAAC;AAAA,EAEjC,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,qBAAqB,CAAC,EAAE,EAAE;AAAA,EACzE,kBAAkB,CAAC,WAA8B,eAC/C,sBAAsB,EAAE;AAAA,EAC1B,YAAY,MAAM;AAAA,EAClB,eAAe,MAAM;AAAA,EAAC;AAAA,EAEtB,wBAAwB,MAAM,sBAAsB,MAAY;AAAA,EAAC,EAAC;AAAA,EAElE,WAAW;AAAA,EACX,cAAc,MAAM,sBAAsB,KAAK;AAAA,EAE/C,kBAAkB;AAAA,EAClB,qBAAqB,MAAM,sBAAsB,EAAE;AAAA,EAEnD,wBAAwB,CAAC;AAAA,EACzB,2BAA2B,MAAM,sBAAsB,CAAC,CAAC;AAAA,EAEzD,qBAAqB,CAAC,eAAyB,sBAAsB,CAAC,CAAC;AAAA,EACvE,oBAAoB,MAAM,sBAAsB,EAAE;AAAA,EAClD,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,eAAe,CAAC;AAAA,EAEhB,kBAAkB,IAAK,MAAkC;AAAA,IACvD,IAAI,kBAA0B;AAC5B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAAA,IAEA,IAAI,UAAkC;AACpC,aAAO,CAAC;AAAA,IACV;AAAA,IACA,IAAI,OAA4B;AAC9B,aAAO,CAAC;AAAA,IACV;AAAA,EACF,EAAG;AAAA,EAEH,6BAA6B,CAAC;AAAA,EAC9B,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,mCAAmC,MAAM;AAAA,EAAC;AAAA,EAC1C,gBAAgB;AAAA,EAChB,eAAe,CAAC;AAAA,EAChB,kBAAkB,MAAM;AAAA,EAAC;AAAA,EACzB,kBAAkB,EAAE,SAAS,CAAC,EAAE;AAAA,EAChC,yBAAyB,MAAM;AAAA,EAAC;AAAA,EAChC,cAAc;AAAA,EACd,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,qBAAqB,CAAC;AAAA,EACtB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,OAAO;AAAA,EACP,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,wBAAwB,EAAE,SAAS,KAAK;AAAA,EACxC,iBAAiB,CAAC;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,0BAA0B;AAAA,EAC1B,6BAA6B,MAAM;AAAA,EACnC,gCAAgC,MAAM;AACxC;AAEO,IAAM,iBAAiB,aAAAC,QAAM,cAAoC,mBAAmB;AAEpF,SAAS,oBAA0C;AACxD,QAAM,UAAU,aAAAA,QAAM,WAAW,cAAc;AAC/C,MAAI,YAAY,qBAAqB;AACnC,UAAM,IAAI,MAAM,uEAAuE;AAAA,EACzF;AACA,SAAO;AACT;AAEA,SAAS,sBAAyB,QAAc;AAC9C,QAAM,IAAI,MAAM,uEAAuE;AACzF;;;AC7SA,oBAAyB;AACzB,IAAAC,gBAAwC;AAkCxC,IAAM,aAAa,CAAC,OAAa,OAAyB;AACxD,SAAO,MAAM,OAAO,CAAC,QAAc,SAAS;AAC1C,QAAI,KAAK,OAAO,IAAI;AAClB,YAAM,UAAU,iCAAK,OAAL,EAAW,UAAU,WAAW,KAAK,UAAU,EAAE,EAAE;AACnE,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,IAAM,UAAU,CAAC,OAAa,SAAmB,aAAgC;AAC/E,MAAI,CAAC,UAAU;AACb,WAAO,CAAC,GAAG,OAAO,OAAO;AAAA,EAC3B;AACA,SAAO,MAAM,IAAI,CAAC,SAAS;AACzB,QAAI,KAAK,OAAO,UAAU;AACxB,aAAO,iCAAK,OAAL,EAAW,UAAU,CAAC,GAAG,KAAK,UAAU,OAAO,EAAE;AAAA,IAC1D,WAAW,KAAK,SAAS,QAAQ;AAC/B,aAAO,iCAAK,OAAL,EAAW,UAAU,QAAQ,KAAK,UAAU,SAAS,QAAQ,EAAE;AAAA,IACxE;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAEA,IAAM,gCAAgC,CAAC,OAAe,gBAAgC;AACpF,MAAI,gBAAgB,GAAG;AACrB,YAAQ,QAAQ,GAAG,SAAS;AAAA,EAC9B,WAAW,gBAAgB,GAAG;AAC5B,WAAO,OAAO,aAAa,KAAK,KAAK;AAAA,EACvC,WAAW,gBAAgB,GAAG;AAC5B,WAAO,OAAO,aAAa,KAAK,KAAK;AAAA,EACvC,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,IAAM,YAAY,CAAC,MAAgB,SAAS,IAAI,cAAc,MAAc;AAC1E,QAAM,SAAS,IAAI,OAAO,CAAC,EAAE,OAAO,WAAW;AAE/C,QAAM,yBAAyB,OAAO,SAAS,OAAO;AACtD,QAAM,wBAAwB,IAAI,OAAO,sBAAsB;AAE/D,QAAM,aAAa,KAAK,MAAM,MAAM,IAAI;AAExC,QAAM,kBAAkB,GAAG,SAAS,SAAS,WAAW,CAAC;AACzD,QAAM,wBAAwB,WAC3B,MAAM,CAAC,EACP,IAAI,CAAC,SAAS,GAAG,wBAAwB,MAAM,EAC/C,KAAK,IAAI;AAEZ,MAAI,SAAS,GAAG;AAAA;AAChB,MAAI,uBAAuB;AACzB,cAAU,GAAG;AAAA;AAAA,EACf;AAEA,QAAM,iBAAiB,IAAI,OAAO,OAAO,MAAM;AAE/C,OAAK,SAAS;AAAA,IACZ,CAAC,OAAO,UACL,UAAU;AAAA,MACT;AAAA,MACA,GAAG,iBAAiB,8BAA8B,OAAO,cAAc,CAAC;AAAA,MACxE,cAAc;AAAA,IAChB;AAAA,EACJ;AACA,SAAO;AACT;AAcA,SAAS,YAAY,OAAa,QAAsB;AACtD,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK,YAAY;AACf,YAAM,EAAE,OAAO,UAAU,IAAI,UAAU,IAAI;AAC3C,YAAM,UAAoB;AAAA,QACxB,IAAI;AAAA,QACJ;AAAA,QACA,UAAU,CAAC;AAAA,QACX,YAAY,IAAI,IAAI,OAAO,UAAU;AAAA,MACvC;AAEA,UAAI;AACF,eAAO,QAAQ,OAAO,SAAS,QAAQ;AAAA,MACzC,SAAS,OAAP;AACA,gBAAQ,MAAM,mCAAmC,cAAc,OAAO;AACtE,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,KAAK;AACH,aAAO,WAAW,OAAO,OAAO,EAAE;AAAA,IACpC;AACE,aAAO;AAAA,EACX;AACF;AAGA,IAAM,UAAU,MAAqB;AACnC,QAAM,CAAC,MAAM,QAAQ,QAAI,0BAAW,aAAa,CAAC,CAAC;AAEnD,QAAM,iBAAa;AAAA,IACjB,CAAC,OAAe,YAAsB,aAAkC;AACtE,YAAM,gBAAY,wBAAS;AAC3B,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AAEA,QAAM,oBAAgB,2BAAY,CAAC,OAAyB;AAC1D,aAAS,EAAE,MAAM,eAAe,GAAG,CAAC;AAAA,EACtC,GAAG,CAAC,CAAC;AAEL,QAAM,gBAAY;AAAA,IAChB,CAAC,eAAiC;AAChC,YAAM,gBAAgB,IAAI,IAAI,UAAU;AAExC,UAAI,SAAS;AACb,WAAK,QAAQ,CAAC,MAAM,UAAU;AAE5B,YAAI,CAAC,qBAAqB,eAAe,KAAK,UAAU,GAAG;AACzD;AAAA,QACF;AAGA,YAAI,UAAU,GAAG;AACf,oBAAU;AAAA,QACZ;AAEA,kBAAU,UAAU,MAAM,GAAG,8BAA8B,OAAO,CAAC,KAAK;AAAA,MAC1E,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,CAAC,IAAI;AAAA,EACP;AAEA,SAAO,EAAE,MAAM,YAAY,WAAW,cAAc;AACtD;AAEA,IAAO,mBAAQ;AAEf,SAAS,qBAAwB,MAAc,MAAuB;AACpE,QAAM,CAAC,YAAY,SAAS,IAAI,KAAK,QAAQ,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI;AAEnF,WAAS,QAAQ,YAAY;AAC3B,QAAI,UAAU,IAAI,IAAI,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AF/KA,uBAA0B;AAC1B,IAAAC,iBAQO;;;AGnCP,IAAAC,gBAAwC;AACxC,IAAAC,iBAAyB;AAgBzB,IAAM,uBAAuB,MAAwC;AACnE,QAAM,CAAC,UAAU,QAAQ,QAAI,0BAE3B,0BAA0B,oBAAI,IAAsD,CAAC;AAEvF,QAAM,iBAAa,2BAAY,CAAC,OAAU,eAA8C;AACtF,UAAM,YAAQ,yBAAS;AACvB,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,IAAI;AAAA,MACJ;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,QAAM,oBAAgB,2BAAY,CAAC,OAAkC;AACnE,aAAS,EAAE,MAAM,kBAAkB,GAAG,CAAC;AAAA,EACzC,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc;AAAA,IAClB,CAAC,eAA8B;AAC7B,YAAM,gBAAgB,IAAI,IAAI,UAAU;AACxC,YAAM,SAAc,CAAC;AACrB,eAAS,QAAQ,CAAC,YAAY;AAC5B,YAAIC,sBAAqB,eAAe,QAAQ,UAAU,GAAG;AAC3D,iBAAO,KAAK,QAAQ,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,SAAO,EAAE,YAAY,eAAe,YAAY;AAClD;AAEA,IAAO,kCAAQ;AAaf,SAAS,yBACP,OACA,QACuD;AACvD,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK,eAAe;AAClB,YAAM,EAAE,OAAO,IAAI,WAAW,IAAI;AAClC,YAAM,aAA0C;AAAA,QAC9C;AAAA,QACA;AAAA,QACA,YAAY,IAAI,IAAI,UAAU;AAAA,MAChC;AACA,YAAM,WAAW,IAAI,IAAI,KAAK;AAC9B,eAAS,IAAI,IAAI,UAAU;AAC3B,aAAO;AAAA,IACT;AAAA,IACA,KAAK,kBAAkB;AACrB,YAAM,WAAW,IAAI,IAAI,KAAK;AAC9B,eAAS,OAAO,OAAO,EAAE;AACzB,aAAO;AAAA,IACT;AAAA,IACA;AACE,aAAO;AAAA,EACX;AACF;AAEA,SAASA,sBAAwB,MAAc,MAAuB;AACpE,QAAM,CAAC,YAAY,SAAS,IAAI,KAAK,QAAQ,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI;AAEnF,WAAS,QAAQ,YAAY;AAC3B,QAAI,UAAU,IAAI,IAAI,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACnGA,IAAAC,gBAAuD;;;ACCvD,IAAAC,gBAAkB;AAOlB,IAAMC,uBAAoD;AAAA,EACxD,UAAU,CAAC;AAAA,EACX,aAAa,MAAM,CAAC;AACtB;AAEO,IAAM,yBACX,cAAAC,QAAM,cAA4CD,oBAAmB;;;ADZvE,gCAA4D;AA4CxD;AAzCG,SAAS,gBAAgB,EAAE,SAAS,GAA4B;AACrE,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAoB,CAAC,CAAC;AACtD,QAAM,yBAAqB,sBAAe;AAC1C,QAAM,0BAAsB,sBAAe;AAC3C,QAAM,yBAAqB,sBAAe;AAE1C,QAAM,EAAE,UAAU,cAAc,cAAc,IAAI,kBAAkB;AAEpE,+BAAU,MAAM;AACd,QAAI,CAAC,YAAY,aAAa,mBAAmB;AAAS;AAC1D,QACE,aAAa,mBAAmB,YAChC,6CAAc,eAAc,oBAAoB,SAChD;AACA;AAAA,IACF;AAEA,UAAM,gBAAgB,MAAY;AA1BtC;AA2BM,UAAI,EAAC,6CAAc;AAAW;AAE9B,YAAM,SAAS,MAAM,cAAc,eAAe;AAAA,QAChD;AAAA,QACA,WAAW,6CAAc;AAAA,MAC3B,CAAC;AAED,YAAM,eAAc,kBAAO,SAAP,mBAAa,mBAAb,mBAA6B;AACjD,UAAI,gBAAgB,mBAAmB;AAAS;AAEhD,WAAI,kBAAO,SAAP,mBAAa,mBAAb,mBAA6B,cAAc;AAC7C,2BAAmB,UAAU;AAC7B,2BAAmB,UAAU;AAC7B,4BAAoB,UAAU,6CAAc;AAE5C,cAAME,gBAAW,8DAAmC,KAAK,MAAM,eAAe,IAAI,CAAC;AACnF,oBAAYA,SAAQ;AAAA,MACtB;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB,GAAG,CAAC,UAAU,6CAAc,SAAS,CAAC;AAEtC,SACE;AAAA,IAAC,uBAAuB;AAAA,IAAvB;AAAA,MACC,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MAEC;AAAA;AAAA,EACH;AAEJ;;;AE1DA,IAAAC,gBAAwE;;;ACDxE,IAAAC,gBAAmC;;;ACSjC,IAAAC,sBAAA;AAPK,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AACF,MAIE;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,QAAO;AAAA,IACP,aAAY;AAAA,IACZ,eAAc;AAAA,IACd,gBAAe;AAAA,IACf,WAAW,8BAA8B,YAAY,YAAY;AAAA,IACjE;AAAA,IAEA;AAAA,mDAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,MAC/B,6CAAC,UAAK,IAAG,MAAK,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK;AAAA,MACrC,6CAAC,UAAK,IAAG,MAAK,IAAG,SAAQ,IAAG,MAAK,IAAG,MAAK;AAAA;AAAA;AAC3C;;;ADrBF,4BAA0B;AAsBlB,IAAAC,sBAAA;AAfD,SAAS,WAAW,EAAE,OAAO,GAAyC;AAC3E,QAAM,iBAAiB,OAAO,IAAI,CAAC,OAAO,QAAQ;AAZpD;AAaI,UAAM,gBACJ,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,gBAA8C,CAAC;AAC5F,UAAM,WAAU,oDAAe,YAAf,YAA0B,MAAM;AAChD,UAAM,OAAO,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,OAAkB;AAE1E,WACE;AAAA,MAAC;AAAA;AAAA,QAEC,OAAO;AAAA,UACL,WAAW,QAAQ,IAAI,IAAI;AAAA,UAC3B,cAAc;AAAA,QAChB;AAAA,QAEA;AAAA,uDAAC,uBAAoB,OAAO,EAAE,cAAc,EAAE,GAAG;AAAA,UAEhD,QACC;AAAA,YAAC;AAAA;AAAA,cACC,OAAO;AAAA,gBACL,YAAY;AAAA,gBACZ,cAAc;AAAA,cAChB;AAAA,cACD;AAAA;AAAA,gBACsB;AAAA,gBACrB,6CAAC,UAAK,OAAO,EAAE,YAAY,aAAa,YAAY,SAAS,GAAI,gBAAK;AAAA;AAAA;AAAA,UACxE;AAAA,UAEF,6CAAC,sBAAAC,SAAA,EAAe,mBAAQ;AAAA;AAAA;AAAA,MAnBnB;AAAA,IAoBP;AAAA,EAEJ,CAAC;AACD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MAEC;AAAA;AAAA,QACD,6CAAC,SAAI,OAAO,EAAE,UAAU,QAAQ,SAAS,KAAK,GAAG,sEAEjD;AAAA;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,gBAAgB;AAC9B,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,aAAO;AAAA,IACL,CAAC,UAAoC;AACnC,YAAM,UAAU,MACb,IAAI,CAAC,QAAQ;AAhEtB;AAiEU,cAAM,UACJ,gBAAgB,QACX,eAAI,eAAJ,mBAAgB,kBAAhB,mBAAuC,YAAW,IAAI,UACvD,IAAI;AACV,cAAM,QAAQ,IAAI,SAAS;AAC3B,eAAO,KAAK,UAAU,KAAK,EAAE,MAAM,GAAG,EAAE;AAAA,MAC1C,CAAC,EACA,KAAK,GAAG;AAEX,eAAS;AAAA,QACP,MAAM;AAAA,QACN,IAAI;AAAA;AAAA,QACJ,SAAS,6CAAC,cAAW,QAAQ,OAAO;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACF;;;ADlBe,IAAAC,sBAAA;AA5Cf,IAAM,mBAAe,6BAA6C,MAAS;AAEpE,SAAS,WAAW;AACzB,QAAM,cAAU,0BAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,SAAO;AACT;AAEO,SAAS,cAAc;AAAA,EAC5B;AAAA,EACA;AACF,GAGG;AACD,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAkB,CAAC,CAAC;AAChD,QAAM,eAAW;AAAA,IACf,CAAC,UAAkC;AAvCvC;AAyCM,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,YAAM,MAAK,WAAM,OAAN,YAAY,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC;AAEhE,gBAAU,CAAC,kBAAkB;AAC3B,YAAI,cAAc,KAAK,CAACC,WAAUA,OAAM,OAAO,EAAE;AAAG,iBAAO;AAC3D,eAAO,CAAC,GAAG,eAAe,iCAAK,QAAL,EAAY,GAAG,EAAC;AAAA,MAC5C,CAAC;AAED,UAAI,MAAM,UAAU;AAClB,mBAAW,MAAM;AACf,sBAAY,EAAE;AAAA,QAChB,GAAG,MAAM,QAAQ;AAAA,MACnB;AAAA,IACF;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAEA,QAAM,4BAAwB,2BAAY,CAAC,WAA2B;AACpE,aAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,6CAAC,cAAW,QAAgB;AAAA,IACvC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc,2BAAY,CAAC,OAAe;AAC9C,cAAU,CAAC,kBAAkB,cAAc,OAAO,CAAC,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,EAC/E,GAAG,CAAC,CAAC;AAEL,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACE,8CAAC,aAAa,UAAb,EAAsB,OACrB;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,eAAe;AAAA,UACf,KAAK;AAAA,QACP;AAAA,QAEC;AAAA,iBAAO,SAAS,KACf,6CAAC,SAAI,OAAO,EAAE,WAAW,QAAQ,GAC/B;AAAA,YAAC;AAAA;AAAA,cACC,SAAS,MAAM,UAAU,CAAC,CAAC;AAAA,cAC3B,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,cAAc;AAAA,cAChB;AAAA,cACD;AAAA;AAAA,UAED,GACF;AAAA,UAED,OAAO,IAAI,CAAC,UACX;AAAA,YAAC;AAAA;AAAA,cAEC,SAAS,MAAM;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,SAAS,MAAM,YAAY,MAAM,EAAE;AAAA;AAAA,YAH9B,MAAM;AAAA,UAIb,CACD;AAAA;AAAA;AAAA,IACH;AAAA,IACC;AAAA,KACH;AAEJ;AAEA,SAAS,MAAM;AAAA,EACb;AAAA,EACA,OAAO;AAAA,EACP;AACF,GAIG;AACD,QAAM,WAAW;AAAA,IACf,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,iBAAiB,SAAS,IAAI;AAAA,QAC9B,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MAEA;AAAA,qDAAC,SAAK,mBAAQ;AAAA,QACd;AAAA,UAAC;AAAA;AAAA,YACC,SAAS;AAAA,YACT,OAAO;AAAA,cACL,UAAU;AAAA,cACV,KAAK;AAAA,cACL,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,YACD;AAAA;AAAA,QAED;AAAA;AAAA;AAAA,EACF;AAEJ;;;AG5KA,IAAAC,6BAIO;AAEP,IAAAC,gBAAwB;AAGjB,IAAM,0BAA0B,CAAC,YAAyC;AAC/E,QAAM,EAAE,sBAAsB,IAAI,SAAS;AAC3C,QAAM,gBAAgB,cAAc;AACpC,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,QAAM,oBAAgB,uBAAQ,MAAM;AAClC,WAAO,IAAI,gDAAqB,iCAC3B,UAD2B;AAAA,MAE9B,iBAAiB,CAAC,UAAU;AAC1B,YAAK,MAAc,cAAc,QAAQ;AACvC,gCAAuB,MAAc,aAA+B;AAAA,QACtE,OAAO;AACL,wBAAc,CAAC,KAAK,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,MACA,kBAAkB,CAAC,YAAoB;AACrC,gBAAQ,KAAK,OAAO;AACpB,iBAAS,EAAE,MAAM,WAAW,QAAQ,CAAC;AAAA,MACvC;AAAA,IACF,EAAC;AAAA,EACH,GAAG,CAAC,SAAS,uBAAuB,QAAQ,CAAC;AAE7C,SAAO;AACT;;;AChCO,SAAS,qBAAqB,gBAA2C;AAC9E,MAAI,OAAO,mBAAmB,WAAW;AACvC,WAAO;AAAA,EACT;AACA,SACE,YAAY,MAAM,eAClB,YAAY,MAAM,eAClB,YAAY,MAAM,aAClB,YAAY,MAAM;AAEtB;AAEA,SAAS,cAAsB;AAC7B,MAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,WAAO,OAAO,SAAS;AAAA,EACzB;AACA,SAAO;AACT;;;ACjBA,IAAAC,gBAAiC;AACjC,IAAAC,iBAA0C;;;ACD1C,IAAAC,iBAIO;AAEP,IAAM,wBAAwB,MAAO,KAAK;AAOnC,IAAM,gBAAN,MAAoB;AAAA,EAApB;AACL,SAAQ,YAA2B;AACnC,SAAQ,aAAoD;AAC5D,SAAQ,gBAAgB;AACxB,SAAQ,eAA8B;AAAA;AAAA,EAEhC,MAAM,cAAsB,UAA4C;AAAA;AAC5E,WAAK;AACL,UAAI,KAAK,cAAc;AAAc;AAErC,UAAI,KAAK;AAAY,sBAAc,KAAK,UAAU;AAElD,YAAM,cAAc,MAAY;AAC9B,YAAI;AACF,gBAAM,WAAW,MAAM,MAAM,GAAG,4CAA6B;AAAA,YAC3D,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,CAAC,kDAAmC,GAAG;AAAA,YACzC;AAAA,UACF,CAAC,EAAE,KAAK,CAACC,cAAaA,UAAS,KAAK,CAAoB;AACxD,eAAK,eAAe;AACpB,+CAAW;AACX,iBAAO;AAAA,QACT,SAAS,OAAP;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,kBAAkB,MAAM,YAAY;AAC1C,WAAK,aAAa,YAAY,aAAa,qBAAqB;AAChE,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO;AACL,SAAK;AACL,QAAI,KAAK,kBAAkB,GAAG;AAC5B,UAAI,KAAK,YAAY;AACnB,sBAAc,KAAK,UAAU;AAC7B,aAAK,aAAa;AAClB,aAAK,YAAY;AACjB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;;;AC/DA,IAAAC,iBAAuD;AAoBnD,IAAAC,sBAAA;AAFJ,IAAM,eAA8C;AAAA,EAClD,CAAC,wBAAS,KAAK,GACb;AAAA,IAAC;AAAA;AAAA,MACC,SAAQ;AAAA,MACR,OAAM;AAAA,MACN,QAAO;AAAA,MACP,QAAO;AAAA,MACP,aAAY;AAAA,MACZ,MAAK;AAAA,MACL,eAAc;AAAA,MACd,gBAAe;AAAA,MAEf;AAAA,qDAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,QAC/B,6CAAC,UAAK,IAAG,MAAK,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK;AAAA,QACpC,6CAAC,UAAK,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,MAAK;AAAA;AAAA;AAAA,EACtC;AAEJ;AAEO,SAAS,YAAY;AAAA,EAC1B,WAAW,wBAAS;AAAA,EACpB,UAAU;AAAA,EACV;AAAA,EACA;AACF,GAAqB;AACnB,MAAI,CAAC,WAAW,CAAC,UAAU;AACzB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,QAAQ,aAAa,QAAQ;AAE1C,QAAM,UAAU;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,qBAAqB;AAAA,IACzB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,0BAA0B;AAAA,IAC9B,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MAEA;AAAA,QAAC;AAAA;AAAA,UACC,OAAO;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,KAAK;AAAA,YACL,cAAc;AAAA,YACd,QAAQ;AAAA,YACR,iBAAiB;AAAA,YACjB,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UAEA;AAAA,yDAAC,SAAI,OAAO,EAAE,OAAO,UAAU,GAAI,gBAAK;AAAA,YACxC;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,MAAM;AAAA,kBACN,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,YAAY;AAAA,kBACZ,WAAW;AAAA,gBACb;AAAA,gBAEC;AAAA;AAAA,YACH;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,KAAK;AAAA,kBACL,UAAU;AAAA,gBACZ;AAAA,gBAEC;AAAA,sDAAS,cACR;AAAA,oBAAC;AAAA;AAAA,sBACC,SAAS,QAAQ,UAAU;AAAA,sBAC3B,OAAO;AAAA,wBACL,cAAc;AAAA,wBACd,SAAS;AAAA,wBACT,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO;AAAA,wBACP,iBAAiB;AAAA,wBACjB,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA,aAAa,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAC7D,YAAY,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAE3D,kBAAQ,UAAU;AAAA;AAAA,kBACrB;AAAA,mBAED,mCAAS,YACR;AAAA,oBAAC;AAAA;AAAA,sBACC,SAAS,QAAQ,QAAQ;AAAA,sBACzB,OAAO;AAAA,wBACL,cAAc;AAAA,wBACd,SAAS;AAAA,wBACT,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO;AAAA,wBACP,iBAAiB;AAAA,wBACjB,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA,aAAa,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAC7D,YAAY,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAE3D,kBAAQ,QAAQ;AAAA;AAAA,kBACnB;AAAA;AAAA;AAAA,YAEJ;AAAA;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,sBAAsB,OAAwB;AAC5D,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK,2BAAY;AACf,aAAO,6CAAC,eAAY,UAAU,MAAM,UAAU,SAAS,MAAM,SAAS;AAAA,IACxE,KAAK,2BAAY;AACf,aACE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,YACP,SAAS;AAAA,cACP,OAAO;AAAA,cACP,SAAS,MAAM;AACb,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,IAEJ,KAAK,2BAAY;AACf,aACE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,YACP,SAAS;AAAA,cACP,OAAO;AAAA,cACP,SAAS,MAAM;AACb,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,EAEN;AACF;;;AFvMA,IAAAC,iBAA0C;AA0D9B,IAAAC,sBAAA;AAxDZ,IAAM,gBAAgB,IAAI,cAAc;AAiBjC,IAAM,uBAAN,cAAmC,cAAAC,QAAM,UAAwB;AAAA,EACtE,YAAY,OAAc;AACxB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EAEA,OAAO,yBAAyB,OAA+B;AAC7D,WAAO,EAAE,UAAU,MAAM,MAAM;AAAA,EACjC;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,MAAM,cAAc;AAC3B,oBAAc,MAAM,KAAK,MAAM,cAAc,CAAC,cAAc;AAC1D,aAAK,SAAS,CAAC,cAAc;AAvCrC;AAwCU,eAAI,uCAAW,gBAAa,eAAU,WAAV,mBAAkB,WAAU;AACtD,mBAAO,EAAE,QAAQ,gCAAa,OAAU;AAAA,UAC1C;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,kBAAc,KAAK;AAAA,EACrB;AAAA,EAEA,kBAAkB,OAAc,WAA4B;AAC1D,YAAQ,MAAM,qBAAqB,OAAO,SAAS;AAAA,EACrD;AAAA,EAEA,SAAS;AAzDX;AA0DI,QAAI,KAAK,MAAM,UAAU;AACvB,UAAI,KAAK,MAAM,iBAAiB,gCAAiB;AAE/C,YAAI,yCAA0B,SAAS,KAAK,MAAM,MAAM,IAAI,GAAG;AAC7D,iBACE,6CAACC,aAAA,EAAW,OAAO,KAAK,MAAM,OAC3B,gCAAsB,KAAK,MAAM,KAAK,GACzC;AAAA,QAEJ;AAEA,eACE,8EACG;AAAA,eAAK,MAAM;AAAA,UACX,KAAK,MAAM,mBACV;AAAA,YAAC;AAAA;AAAA,cACC,WAAU,UAAK,MAAM,WAAX,mBAAmB;AAAA,cAC7B,UAAS,UAAK,MAAM,WAAX,mBAAmB;AAAA;AAAA,UAC9B;AAAA,WAEJ;AAAA,MAEJ;AACA,YAAM,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AAEO,SAASA,YAAW,EAAE,OAAO,SAAS,GAAiD;AAC5F,QAAM,gBAAgB,cAAc;AAEpC,+BAAU,MAAM;AACd,QAAI,OAAO;AACT,oBAAc,CAAC,KAAK,CAAC;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,OAAO,aAAa,CAAC;AAEzB,MAAI,CAAC;AAAO,UAAM;AAClB,SAAO;AACT;;;AXxCQ,IAAAC,sBAAA;AAPD,SAAS,WAAW,IAAyC;AAAzC,eAAE,WApD7B,IAoD2B,IAAe,kBAAf,IAAe,CAAb;AAC3B,QAAM,iBAAiB,MAAM,mBAAmB,SAAY,SAAS,MAAM;AAC3E,QAAM,UAAU,qBAAqB,cAAc;AAEnD,SACE,6CAAC,iBAAc,SACb,uDAAC,wBAAqB,cAAc,MAAM,cAAc,iBAAiB,SACvE,uDAAC,qDAAuB,QAAvB,EAA+B,WAAS,GAC3C,GACF;AAEJ;AAEO,SAAS,mBAAmB,UAA2B;AAC5D,QAA+B,eAAvB,WAlEV,IAkEiC,IAAV,kBAAU,IAAV,CAAb;AAKR,gBAAc,QAAQ;AAEtB,QAAM,kBAAkB,MAAM,cAAc;AAE5C,QAAM,CAAC,SAAS,UAAU,QAAI,yBAA8C,CAAC,CAAC;AAC9E,QAAM,CAAC,qBAAqB,sBAAsB,QAAI,yBAEpD,CAAC,CAAC;AAEJ,QAAM,0BAAsB,uBAA4B;AAAA,IACtD,SAAS,CAAC;AAAA,IACV,qBAAqB,CAAC;AAAA,EACxB,CAAC;AAED,QAAM,EAAE,YAAY,eAAe,UAAU,IAAI,iBAAQ;AACzD,QAAM,CAAC,WAAW,YAAY,QAAI,yBAAS,KAAK;AAChD,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,yBAAS,EAAE;AAC3D,QAAM,CAAC,YAAY,aAAa,QAAI,yBAAoC,CAAC,CAAC;AAC1E,QAAM,CAAC,YAAY,aAAa,QAAI,yBAA0B,CAAC,CAAC;AAChE,QAAM,CAAC,wBAAwB,yBAAyB,QAAI,yBAAmB,CAAC,CAAC;AAEjF,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,aAAa;AAAA,EACf,IAAI,gCAAsC;AAI1C,QAAM,gBAAY,4BAAY,CAAC,IAAY,WAAgC;AACzE,eAAW,CAAC,eAAe;AACzB,aAAO,iCACF,aADE;AAAA,QAEL,CAAC,EAAE,GAAG;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,mBAAe,4BAAY,CAAC,OAAe;AAC/C,eAAW,CAAC,eAAe;AACzB,YAAM,YAAY,mBAAK;AACvB,aAAO,UAAU,EAAE;AACnB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,4BAAwB,4BAAY,CAAC,IAAY,gBAAyC;AAC9F,2BAAuB,CAAC,eAAe;AACrC,aAAO,iCACF,aADE;AAAA,QAEL,CAAC,EAAE,GAAG;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,+BAA2B,4BAAY,CAAC,OAAe;AAC3D,2BAAuB,CAAC,eAAe;AACrC,YAAM,YAAY,mBAAK;AACvB,aAAO,UAAU,EAAE;AACnB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,uBAAmB;AAAA,IACvB,CAAC,WAA8B,eAAyB;AACtD,YAAM,kBAAkB,UACrB,IAAI,CAAC,aAAa;AACjB,eAAO,GAAG,SAAS,SAAS,SAAS;AAAA,EAAwB,SAAS,YAAY;AAAA,MACpF,CAAC,EACA,KAAK,MAAM;AAEd,YAAM,qBAAqB,UAAU,UAAU;AAE/C,aAAO,GAAG;AAAA;AAAA,EAAsB;AAAA,IAClC;AAAA,IACA,CAAC,SAAS;AAAA,EACZ;AAEA,QAAM,iBAAa;AAAA,IACjB,CACE,SACA,UACA,aAAuB,oCACpB;AACH,aAAO,WAAW,SAAS,YAAY,QAAQ;AAAA,IACjD;AAAA,IACA,CAAC,UAAU;AAAA,EACb;AAEA,QAAM,oBAAgB;AAAA,IACpB,CAAC,OAAe;AACd,oBAAc,EAAE;AAAA,IAClB;AAAA,IACA,CAAC,aAAa;AAAA,EAChB;AAEA,QAAM,6BAAyB;AAAA,IAC7B,CAAC,sBAA4D;AAC3D,aAAO,iCAAiC,OAAO,OAAO,qBAAqB,OAAO,CAAC;AAAA,IACrF;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAEA,QAAM,0BAAsB;AAAA,IAC1B,CAAC,eAAyB;AACxB,aAAO,aAAa,UAAU;AAAA,IAChC;AAAA,IACA,CAAC,YAAY;AAAA,EACf;AAEA,QAAM,yBAAqB;AAAA,IACzB,CAAC,iBAAkC,aAAuB,oCAAoC;AAC5F,aAAO,YAAY,iBAAiB,UAAU;AAAA,IAChD;AAAA,IACA,CAAC,WAAW;AAAA,EACd;AAEA,QAAM,4BAAwB;AAAA,IAC5B,CAAC,eAAuB;AACtB,qBAAe,UAAU;AAAA,IAC3B;AAAA,IACA,CAAC,cAAc;AAAA,EACjB;AAGA,QAAM,uBAAqC,wBAAQ,MAAM;AApM3D,QAAAC,KAAA;AAqMI,QAAI,QAAwC;AAC5C,QAAI,MAAM,cAAc;AACtB,cAAQ;AAAA,QACN,YAAY;AAAA,UACV,OAAO;AAAA,YACL,iBAAiB;AAAA,cACf,SAAS,QAAQ,MAAM,YAAY;AAAA,cACnC,eAAaA,MAAA,MAAM,iBAAN,gBAAAA,IAAoB,gBAAe,CAAC;AAAA,cACjD,iBAAe,WAAM,iBAAN,mBAAoB,kBAAiB,CAAC;AAAA,YACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,cAAc,MAAM;AAAA,OAChB,QAAQ,EAAE,MAAM,IAAI,CAAC,IAFpB;AAAA,MAGL;AAAA,MACA,SAAS,MAAM,WAAW,CAAC;AAAA,MAC3B,YAAY,MAAM,cAAc,CAAC;AAAA,MACjC,oBAAoB,MAAM;AAAA,MAC1B,iBAAiB,MAAM;AAAA,MACvB,aAAa,MAAM;AAAA,IACrB;AAAA,EACF,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AAED,QAAM,cAAU,wBAAQ,MAAM;AAC5B,UAAM,cAAc,OAAO,OAAO,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,UAAU;AACzE,UAAI,MAAM,WAAW,mBAAmB,MAAM,aAAa;AACzD,eAAO,kCACF,MACA,OAAO,QAAQ,MAAM,WAAW,EAAE;AAAA,UACnC,CAACC,UAAS,CAAC,KAAK,KAAK,MAAO,iCACvBA,WADuB;AAAA,YAE1B,CAAC,IAAI,WAAW,WAAW,IAAI,MAAM,YAAY,KAAK,GAAG;AAAA,UAC3D;AAAA,UACA,CAAC;AAAA,QACH;AAAA,MAEJ;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAEL,WAAO,iDACD,iBAAiB,WAAW,CAAC,IAC7B,iBAAiB,eACjB,EAAE,CAAC,kDAAmC,GAAG,iBAAiB,aAAa,IACvE,CAAC,IACF;AAAA,EAEP,GAAG,CAAC,iBAAiB,SAAS,iBAAiB,cAAc,UAAU,CAAC;AAExE,QAAM,gBAAgB,wBAAwB;AAAA,IAC5C,KAAK,iBAAiB;AAAA,IACtB,cAAc,iBAAiB;AAAA,IAC/B;AAAA,IACA,aAAa,iBAAiB;AAAA,EAChC,CAAC;AAED,QAAM,CAAC,6BAA6B,8BAA8B,QAAI,yBAEnE,CAAC,CAAC;AAEL,QAAM,iCAAiC,CACrC,IACA,eACG;AACH,mCAA+B,CAAC,SAAU,iCAAK,OAAL,EAAW,CAAC,EAAE,GAAG,WAAW,EAAE;AAAA,EAC1E;AAEA,QAAM,oCAAoC,CAAC,OAAe;AACxD,mCAA+B,CAAC,SAAS;AACvC,YAA6BD,MAAA,MAApB,EAvRf,CAuRe,KAAK,EAvRpB,IAuRmCA,KAAT,iBAASA,KAAT,CAAX;AACT,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,yBAAkB,CAAC,CAAC;AAClE,QAAM,CAAC,eAAe,gBAAgB,QAAI,yBAAuC,CAAC,CAAC;AACnF,QAAM,uBAAmB,uBAAqC,CAAC,CAAC;AAChE,QAAM,8BAA0B;AAAA,IAC9B,CACE,UAGG;AACH,YAAM,WAAW,OAAO,UAAU,aAAa,MAAM,iBAAiB,OAAO,IAAI;AACjF,uBAAiB,UAAU;AAC3B,uBAAiB,CAAC,SAAS;AACzB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACA,QAAM,sBAAkB,uBAAO,KAAK;AAEpC,gCAAU,MAAM;AACd,QAAI,gBAAgB;AAAS;AAE7B,UAAM,YAAY,MAAY;AAlTlC,UAAAA;AAmTM,YAAM,SAAS,MAAM,cAAc,gBAAgB;AACnD,WAAIA,MAAA,OAAO,SAAP,gBAAAA,IAAa,iBAAiB;AAChC,2BAAmB,OAAO,KAAK,gBAAgB,MAAM;AAAA,MACvD;AACA,sBAAgB,UAAU;AAAA,IAC5B;AACA,SAAK,UAAU;AAAA,EACjB,GAAG,CAAC,CAAC;AAEL,MAAI,sBAA2C;AAC/C,MAAI,MAAM,OAAO;AACf,0BAAsB;AAAA,MACpB,WAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,CAAC,cAAc,eAAe,QAAI,yBAA8B,mBAAmB;AAGzF,gCAAU,MAAM;AACd,QAAI,MAAM,OAAO;AACf,sBAAgB;AAAA,QACd,WAAW,MAAM;AAAA,MACnB,CAAC;AAAA,IACH,OAAO;AACL,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,QAAM,CAAC,kBAAkB,mBAAmB,QAAI,yBAAiB,MAAM,gBAAY,2BAAW,CAAC;AAC/F,QAAM,kBAAc;AAAA,IAClB,CAAC,UAAkC;AACjC,UAAI,MAAM,UAAU;AAClB,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AACA,0BAAoB,KAAK;AAAA,IAC3B;AAAA,IACA,CAAC,MAAM,QAAQ;AAAA,EACjB;AAGA,gCAAU,MAAM;AACd,QAAI,MAAM,aAAa,QAAW;AAChC,0BAAoB,MAAM,QAAQ;AAAA,IACpC;AAAA,EACF,GAAG,CAAC,MAAM,QAAQ,CAAC;AAEnB,QAAM,CAAC,OAAO,QAAQ,QAAI,yBAAwB,IAAI;AAEtD,QAAM,6BAAyB,uBAA+B,IAAI;AAElE,QAAM,iBAAiB,MAAM,mBAAmB,SAAY,SAAS,MAAM;AAE3E,QAAM,CAAC,0BAA0B,4BAA4B,QAC3D,yBAA0C,IAAI;AAChD,QAAM,kCAA8B,4BAAY,CAAC,WAA+C;AAC9F,iCAA6B,CAAC,SAAS;AACrC,UAAI,QAAQ;AAAM,eAAO;AACzB,UAAI,UAAU;AAAM,eAAO;AAC3B,UAAI,QAAQ,KAAK;AACjB,UAAI,OAAO,OAAO;AAEhB,gBAAQ,kCAAK,KAAK,QAAU,OAAO;AAAA,MACrC;AACA,aAAO,gDAAK,OAAS,SAAd,EAAsB,MAAM;AAAA,IACrC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,QAAM,qCAAiC,4BAAY,MAAY;AAC7D,gCAA4B,IAAI;AAAA,EAClC,GAAG,CAAC,CAAC;AAEL,SACE;AAAA,IAAC,eAAe;AAAA,IAAf;AAAA,MACC,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,qBAAqB,MAAM,uBAAuB,CAAC;AAAA,QACnD,WAAW,MAAM,SAAS;AAAA,QAC1B,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,MAAM;AAAA,QACpB,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MAEA,uDAAC,mBAAiB,UAAS;AAAA;AAAA,EAC7B;AAEJ;AAEO,IAAM,kCAAkC,CAAC,QAAQ;AAExD,SAAS,iCAAiC,SAAqD;AAC7F,SAAO,CAAO,OAAmB,eAAnB,KAAmB,WAAnB,EAAE,MAAM,KAAK,GAAM;AAC/B,QAAI,wBAA6D,CAAC;AAClE,aAASE,WAAU,SAAS;AAC1B,4BAAsBA,QAAO,IAAI,IAAIA;AAAA,IACvC;AAEA,UAAM,SAAS,sBAAsB,IAAI;AACzC,QAAI,SAAc;AAClB,QAAI,QAAQ;AACV,YAAM,IAAI,QAAc,CAAC,SAAS,WAAW;AAC3C,wCAAU,MAAY;AAjc9B;AAkcU,cAAI;AACF,qBAAS,OAAM,YAAO,YAAP,gCAAiB;AAChC,oBAAQ;AAAA,UACV,SAAS,OAAP;AACA,mBAAO,KAAK;AAAA,UACd;AAAA,QACF,EAAC;AAAA,MACH,CAAC;AACD,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,aAA6B;AACtD,SAAO,YACJ,QAAQ,OAAO,EAAE,EACjB,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC,EAAE,YAAY,CAAC,EACxE,KAAK,GAAG;AACb;AAEA,SAAS,cAAc,OAAsC;AAC3D,QAAM,gBAAgB,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC;AAE3E,MAAI,CAAC,MAAM,cAAc,CAAC,MAAM,cAAc;AAC5C,UAAM,IAAI,kCAAmB,uDAAuD;AAAA,EACtF;AAEA,MAAI,cAAc,SAAS,KAAK,CAAC,MAAM,cAAc;AACnD,UAAM,IAAI;AAAA,MACR,gEAAgE,cAC7D,IAAI,iBAAiB,EACrB,KAAK,IAAI;AAAA,IACd;AAAA,EACF;AACF;", "names": ["import_react", "React", "import_react", "import_shared", "import_react", "import_shared", "setsHaveIntersection", "import_react", "import_react", "emptyCopilotContext", "React", "messages", "import_react", "import_react", "import_jsx_runtime", "import_jsx_runtime", "ReactMarkdown", "import_jsx_runtime", "toast", "import_runtime_client_gql", "import_react", "import_react", "import_shared", "import_shared", "response", "import_shared", "import_jsx_runtime", "import_shared", "import_jsx_runtime", "React", "ErrorToast", "import_jsx_runtime", "_a", "headers", "action"]}