import {
  Window
} from "./chunk-TIDV74OE.mjs";
import {
  Header
} from "./chunk-UH2UFL5W.mjs";
import {
  Button
} from "./chunk-UFN2VWSR.mjs";
import {
  CopilotChat
} from "./chunk-IAHSHPRJ.mjs";
import {
  AssistantMessage
} from "./chunk-XMNTLIK5.mjs";
import {
  UserMessage
} from "./chunk-HWMFMBJC.mjs";
import {
  Input
} from "./chunk-EMIYIMQ6.mjs";
import {
  Messages
} from "./chunk-B3D7U7TJ.mjs";
import {
  ChatContextProvider
} from "./chunk-IEMQ2SQW.mjs";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-MRXNTQOX.mjs";

// src/components/chat/Modal.tsx
import React from "react";
import { jsx, jsxs } from "react/jsx-runtime";
var CopilotModal = (_a) => {
  var _b = _a, {
    instructions,
    defaultOpen = false,
    clickOutsideToClose = true,
    hitEscapeToClose = true,
    onSetOpen,
    onSubmitMessage,
    onStopGeneration,
    onReloadMessages,
    shortcut = "/",
    icons,
    labels,
    makeSystemMessage,
    onInProgress,
    Window: Window2 = Window,
    Button: Button2 = Button,
    Header: Header2 = Header,
    Messages: Messages2 = Messages,
    Input: Input2 = Input,
    AssistantMessage: AssistantMessage2 = AssistantMessage,
    UserMessage: UserMessage2 = UserMessage,
    onThumbsUp,
    onThumbsDown,
    onCopy,
    onRegenerate,
    markdownTagRenderers,
    className,
    children
  } = _b, props = __objRest(_b, [
    "instructions",
    "defaultOpen",
    "clickOutsideToClose",
    "hitEscapeToClose",
    "onSetOpen",
    "onSubmitMessage",
    "onStopGeneration",
    "onReloadMessages",
    "shortcut",
    "icons",
    "labels",
    "makeSystemMessage",
    "onInProgress",
    "Window",
    "Button",
    "Header",
    "Messages",
    "Input",
    "AssistantMessage",
    "UserMessage",
    "onThumbsUp",
    "onThumbsDown",
    "onCopy",
    "onRegenerate",
    "markdownTagRenderers",
    "className",
    "children"
  ]);
  const [openState, setOpenState] = React.useState(defaultOpen);
  const setOpen = (open) => {
    onSetOpen == null ? void 0 : onSetOpen(open);
    setOpenState(open);
  };
  return /* @__PURE__ */ jsxs(ChatContextProvider, { icons, labels, open: openState, setOpen, children: [
    children,
    /* @__PURE__ */ jsxs("div", { className, children: [
      /* @__PURE__ */ jsx(Button2, {}),
      /* @__PURE__ */ jsxs(
        Window2,
        {
          clickOutsideToClose,
          shortcut,
          hitEscapeToClose,
          children: [
            /* @__PURE__ */ jsx(Header2, {}),
            /* @__PURE__ */ jsx(
              CopilotChat,
              __spreadProps(__spreadValues({}, props), {
                instructions,
                onSubmitMessage,
                onStopGeneration,
                onReloadMessages,
                makeSystemMessage,
                onInProgress,
                Messages: Messages2,
                Input: Input2,
                AssistantMessage: AssistantMessage2,
                UserMessage: UserMessage2,
                onThumbsUp,
                onThumbsDown,
                onCopy,
                onRegenerate,
                markdownTagRenderers
              })
            )
          ]
        }
      )
    ] })
  ] });
};

export {
  CopilotModal
};
//# sourceMappingURL=chunk-FWKHST6I.mjs.map