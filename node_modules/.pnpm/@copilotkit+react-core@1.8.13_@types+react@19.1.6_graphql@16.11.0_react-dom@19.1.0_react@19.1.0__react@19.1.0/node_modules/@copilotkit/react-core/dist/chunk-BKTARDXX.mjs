import {
  useErrorToast,
  useToast
} from "./chunk-22ENANUU.mjs";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-SKC7AJIV.mjs";

// src/hooks/use-copilot-runtime-client.ts
import {
  CopilotRuntimeClient
} from "@copilotkit/runtime-client-gql";
import { useMemo } from "react";
var useCopilotRuntimeClient = (options) => {
  const { addGraphQLErrorsToast } = useToast();
  const addErrorToast = useErrorToast();
  const { addToast } = useToast();
  const runtimeClient = useMemo(() => {
    return new CopilotRuntimeClient(__spreadProps(__spreadValues({}, options), {
      handleGQLErrors: (error) => {
        if (error.graphQLErrors.length) {
          addGraphQLErrorsToast(error.graphQLErrors);
        } else {
          addErrorToast([error]);
        }
      },
      handleGQLWarning: (message) => {
        console.warn(message);
        addToast({ type: "warning", message });
      }
    }));
  }, [options, addGraphQLErrorsToast, addToast]);
  return runtimeClient;
};

export {
  useCopilotRuntimeClient
};
//# sourceMappingURL=chunk-BKTARDXX.mjs.map