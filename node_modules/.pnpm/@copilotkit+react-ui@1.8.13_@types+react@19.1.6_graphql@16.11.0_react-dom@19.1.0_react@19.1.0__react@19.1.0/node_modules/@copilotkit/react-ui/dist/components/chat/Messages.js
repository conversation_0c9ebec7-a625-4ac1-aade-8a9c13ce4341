"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/chat/Messages.tsx
var Messages_exports = {};
__export(Messages_exports, {
  Messages: () => Messages,
  useScrollToBottom: () => useScrollToBottom
});
module.exports = __toCommonJS(Messages_exports);
var import_react2 = require("react");

// src/components/chat/ChatContext.tsx
var import_react = __toESM(require("react"));
var import_jsx_runtime = require("react/jsx-runtime");
var ChatContext = import_react.default.createContext(void 0);
function useChatContext() {
  const context = import_react.default.useContext(ChatContext);
  if (context === void 0) {
    throw new Error(
      "Context not found. Did you forget to wrap your app in a <ChatContextProvider> component?"
    );
  }
  return context;
}

// src/components/chat/Messages.tsx
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_react_core = require("@copilotkit/react-core");
var import_jsx_runtime2 = require("react/jsx-runtime");
var Messages = ({
  messages,
  inProgress,
  children,
  RenderTextMessage,
  RenderActionExecutionMessage,
  RenderAgentStateMessage,
  RenderResultMessage,
  RenderImageMessage,
  AssistantMessage,
  UserMessage,
  onRegenerate,
  onCopy,
  onThumbsUp,
  onThumbsDown,
  markdownTagRenderers
}) => {
  const context = useChatContext();
  const initialMessages = (0, import_react2.useMemo)(
    () => makeInitialMessages(context.labels.initial),
    [context.labels.initial]
  );
  messages = [...initialMessages, ...messages];
  const actionResults = {};
  for (let i = 0; i < messages.length; i++) {
    if (messages[i].isActionExecutionMessage()) {
      const id = messages[i].id;
      const resultMessage = messages.find(
        (message) => message.isResultMessage() && message.actionExecutionId === id
      );
      if (resultMessage) {
        actionResults[id] = import_runtime_client_gql.ResultMessage.decodeResult(resultMessage.result || "");
      }
    }
  }
  const { messagesContainerRef, messagesEndRef } = useScrollToBottom(messages);
  const interrupt = (0, import_react_core.useLangGraphInterruptRender)();
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)("div", { className: "copilotKitMessages", ref: messagesContainerRef, children: [
    /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)("div", { className: "copilotKitMessagesContainer", children: [
      messages.map((message, index) => {
        const isCurrentMessage = index === messages.length - 1;
        if (message.isTextMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
            RenderTextMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage,
              onRegenerate,
              onCopy,
              onThumbsUp,
              onThumbsDown,
              markdownTagRenderers
            },
            index
          );
        } else if (message.isActionExecutionMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
            RenderActionExecutionMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              actionResult: actionResults[message.id],
              AssistantMessage,
              UserMessage
            },
            index
          );
        } else if (message.isAgentStateMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
            RenderAgentStateMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage
            },
            index
          );
        } else if (message.isResultMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
            RenderResultMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage
            },
            index
          );
        } else if (message.isImageMessage && message.isImageMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
            RenderImageMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage,
              onRegenerate,
              onCopy,
              onThumbsUp,
              onThumbsDown
            },
            index
          );
        }
      }),
      interrupt
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("footer", { className: "copilotKitMessagesFooter", ref: messagesEndRef, children })
  ] });
};
function makeInitialMessages(initial) {
  let initialArray = [];
  if (initial) {
    if (Array.isArray(initial)) {
      initialArray.push(...initial);
    } else {
      initialArray.push(initial);
    }
  }
  return initialArray.map(
    (message) => new import_runtime_client_gql.TextMessage({
      role: import_runtime_client_gql.Role.Assistant,
      content: message
    })
  );
}
function useScrollToBottom(messages) {
  const messagesEndRef = (0, import_react2.useRef)(null);
  const messagesContainerRef = (0, import_react2.useRef)(null);
  const isProgrammaticScrollRef = (0, import_react2.useRef)(false);
  const isUserScrollUpRef = (0, import_react2.useRef)(false);
  const scrollToBottom = () => {
    if (messagesContainerRef.current && messagesEndRef.current) {
      isProgrammaticScrollRef.current = true;
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };
  const handleScroll = () => {
    if (isProgrammaticScrollRef.current) {
      isProgrammaticScrollRef.current = false;
      return;
    }
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      isUserScrollUpRef.current = scrollTop + clientHeight < scrollHeight;
    }
  };
  (0, import_react2.useEffect)(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  (0, import_react2.useEffect)(() => {
    const container = messagesContainerRef.current;
    if (!container) {
      return;
    }
    const mutationObserver = new MutationObserver(() => {
      if (!isUserScrollUpRef.current) {
        scrollToBottom();
      }
    });
    mutationObserver.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    });
    return () => {
      mutationObserver.disconnect();
    };
  }, []);
  (0, import_react2.useEffect)(() => {
    isUserScrollUpRef.current = false;
    scrollToBottom();
  }, [messages.filter((m) => m.isTextMessage() && m.role === import_runtime_client_gql.Role.User).length]);
  return { messagesEndRef, messagesContainerRef };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Messages,
  useScrollToBottom
});
//# sourceMappingURL=Messages.js.map