# Subscription UI Audit

## Overview
This audit examines the existing subscription-related UI components and identifies gaps for implementing the subscription badge and upgrade modal system.

## Existing Components

### 1. Subscription Status Components

#### `frontend/src/components/subscription/subscription-status.tsx`
- **Purpose**: Displays subscription status in a card format
- **Features**: 
  - Shows status badges (Trial, Active, Canceled, Unknown)
  - Displays trial/renewal countdown
  - Shows usage progress bars
  - Includes action buttons (Upgrade Plan, Manage Subscription, Reactivate)
- **Badge Colors**: 
  - Trial: Blue (`bg-blue-50 text-blue-700 border-blue-200`)
  - Active: Green (`bg-green-50 text-green-700 border-green-200`)
  - Canceled: Red (`bg-red-50 text-red-700 border-red-200`)
- **Location**: Used in subscription settings pages

#### `frontend/src/components/subscription/subscription-status-test.tsx`
- **Purpose**: Test version of subscription status component
- **Features**: Similar to main component but with simplified badge rendering
- **Badge Colors**: 
  - Active: Green (`bg-green-500`)
  - Trial: Blue (`bg-blue-500`)
  - Canceled: Red (`bg-red-500`)
  - Inactive: <PERSON> (`bg-gray-500`)

### 2. Usage Dashboard Components

#### `frontend/src/components/subscription/usage-dashboard.tsx`
- **Purpose**: Displays usage metrics and quotas
- **Features**:
  - Progress bars for different usage types
  - Chart visualization
  - Date range selection
  - Usage type filtering (document_upload, api_calls, storage, voice agents)
- **Integration**: Uses `/api/subscription/usage` endpoint

### 3. Subscription Management Pages

#### `frontend/src/app/(authenticated)/settings/subscription/addons/page.tsx`
- **Purpose**: Manage subscription add-ons
- **Features**: Add/remove add-ons, view pricing
- **UI Elements**: Cards with badges, buttons for actions

#### `frontend/src/app/(dashboard)/admin/subscriptions/page.tsx`
- **Purpose**: Admin view of all tenant subscriptions
- **Features**: Tables for plans, add-ons, and tenant subscriptions
- **Components**: Uses specialized table components

#### `frontend/src/app/superadmin/subscription-management/page.tsx`
- **Purpose**: Super admin subscription management
- **Features**: CRUD operations for plans and add-ons
- **UI Elements**: Tabs, tables, dialogs for editing

### 4. Navigation Components

#### `frontend/src/components/layout/navbar.tsx`
- **Current Badge Usage**: 
  - Notification badges (red, destructive variant)
  - Deadline badges (red, destructive variant)
- **User Avatar Location**: Right side of navbar with dropdown menu
- **Available Space**: Next to user avatar for subscription badge

#### `frontend/src/components/layout/client-navbar.tsx`
- **Current Badge Usage**: Notification badges
- **Similar Structure**: Could accommodate subscription badge

### 5. Existing Badge Component

#### `frontend/src/components/ui/badge.tsx`
- **Variants Available**: 
  - `default`: Primary color
  - `secondary`: Secondary color
  - `destructive`: Red/error color
  - `outline`: Border only
- **Customization**: Supports custom className for colors

## Existing Services & APIs

### 1. Subscription Service
#### `frontend/src/lib/services/subscription-service.ts`
- **Methods Available**:
  - `getSubscriptionPlans()`
  - `getTenantSubscription()`
  - `createSubscription()`
  - `updateSubscription()`
  - `addAddon()`
- **DTOs**: `SubscriptionPlanDTO`, `TenantSubscriptionDTO`, `SubscriptionAddonDTO`

### 2. API Routes
- **Webhook Handler**: `/api/webhooks/subscription/route.ts` (Stripe integration ready)
- **Usage Tracking**: `/api/subscription/usage` endpoint exists

## Environment Variables

### Current Configuration
- **Supabase**: Fully configured
- **Turnstile**: Configured for bot protection
- **CopilotKit**: Configured

### Missing for Stripe Integration
- `STRIPE_PUBLIC_KEY` - Not present in `.env.example`
- `STRIPE_SECRET_KEY` - Not present in `.env.example`
- `STRIPE_WEBHOOK_SECRET` - Not present in `.env.example`

## Gaps Identified

### 1. No Navbar Subscription Badge
- **Gap**: No subscription plan indicator in the main navigation
- **Current**: Only notification and deadline badges exist
- **Needed**: Plan-specific badge next to user avatar

### 2. No Unified Upgrade Modal
- **Gap**: Upgrade buttons exist but no centralized modal
- **Current**: Buttons redirect to separate pages
- **Needed**: Modal with plan comparison and Stripe checkout

### 3. No Plan Determination Helper
- **Gap**: No utility to determine current plan from subscription data
- **Current**: Components fetch subscription data individually
- **Needed**: Helper function to map subscription to plan codes

### 4. No Feature Lock Overlays
- **Gap**: No UI to block features when not available in plan
- **Current**: Feature access controlled at route level
- **Needed**: Overlay components for feature-locked areas

### 5. Limited Stripe Integration
- **Gap**: Webhook handler exists but no checkout flow
- **Current**: Payment provider references in database
- **Needed**: Complete Stripe checkout integration

### 6. No Voice Feature Integration
- **Gap**: Voice admin pages exist but no subscription integration
- **Current**: Voice pages at `/admin/voice/*` exist
- **Needed**: Feature lock overlays for voice_intake feature

## Recommendations

### Phase B Implementation Strategy

1. **Extend Existing Badge System**: 
   - Build on existing badge component and navbar structure
   - Add subscription badge next to existing notification badges

2. **Create Upgrade Modal**:
   - New modal component that can be triggered from existing upgrade buttons
   - Integrate with existing subscription service

3. **Enhance Existing Components**:
   - Extend subscription-status component to support new badge
   - Add feature lock overlays to voice admin pages

4. **Complete Stripe Integration**:
   - Add missing environment variables
   - Create checkout API route
   - Enhance webhook handler

## File Structure for Implementation

```
frontend/src/components/subscription/
├── subscription-badge.tsx          # NEW: Navbar badge component
├── upgrade-modal.tsx               # NEW: Upgrade/manage modal
├── feature-lock.tsx                # NEW: Feature lock overlay
├── subscription-status.tsx         # EXTEND: Add badge support
└── usage-dashboard.tsx             # EXISTS: No changes needed

frontend/src/lib/subscription/
├── getCurrentPlan.ts               # NEW: Plan determination helper
└── index.ts                        # NEW: Export all helpers

frontend/src/app/api/billing/
└── checkout/route.ts               # NEW: Stripe checkout endpoint
```

## Testing Requirements

### Existing Test Coverage
- Cypress tests exist for subscription UI components
- Jest tests exist for usage dashboard
- Test data factory exists for mock data

### Additional Tests Needed
- Badge color mapping tests
- Upgrade modal flow tests
- Feature lock overlay tests
- Stripe checkout redirect tests
