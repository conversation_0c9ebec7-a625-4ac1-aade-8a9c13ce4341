{"version": 3, "sources": ["../src/lib/integrations/nextjs/app-router.ts", "../src/lib/integrations/nextjs/pages-router.ts"], "sourcesContent": ["import { createYoga } from \"graphql-yoga\";\nimport { CreateCopilotRuntimeServerOptions, getCommonConfig } from \"../shared\";\nimport telemetry, { getRuntimeInstanceTelemetryInfo } from \"../../telemetry-client\";\n\nexport function copilotRuntimeNextJSAppRouterEndpoint(options: CreateCopilotRuntimeServerOptions) {\n  const commonConfig = getCommonConfig(options);\n\n  telemetry.setGlobalProperties({\n    runtime: {\n      framework: \"nextjs-app-router\",\n    },\n  });\n\n  if (options.properties?._copilotkit) {\n    telemetry.setGlobalProperties({\n      _copilotkit: options.properties._copilotkit,\n    });\n  }\n\n  telemetry.capture(\n    \"oss.runtime.instance_created\",\n    getRuntimeInstanceTelemetryInfo(options.runtime),\n  );\n\n  const logger = commonConfig.logging;\n  logger.debug(\"Creating NextJS App Router endpoint\");\n\n  const yoga = createYoga({\n    ...commonConfig,\n    graphqlEndpoint: options.endpoint,\n    fetchAPI: { Response: globalThis.Response },\n  });\n\n  return {\n    handleRequest: yoga,\n    GET: yoga as any,\n    POST: yoga as any,\n    OPTIONS: yoga as any,\n  };\n}\n", "import { YogaServerInstance, createYoga } from \"graphql-yoga\";\nimport { CreateCopilotRuntimeServerOptions, GraphQLContext, getCommonConfig } from \"../shared\";\nimport telemetry, { getRuntimeInstanceTelemetryInfo } from \"../../telemetry-client\";\n\nexport const config = {\n  api: {\n    bodyParser: false,\n  },\n};\n\nexport type CopilotRuntimeServerInstance<T> = YogaServerInstance<T, Partial<GraphQLContext>>;\n\n// This import is needed to fix the type error\n// Fix is currently in TypeScript 5.5 beta, waiting for stable version\n// https://github.com/microsoft/TypeScript/issues/42873#issuecomment-2066874644\nexport type {} from \"@whatwg-node/server\";\n\nexport function copilotRuntimeNextJSPagesRouterEndpoint(\n  options: CreateCopilotRuntimeServerOptions,\n): CopilotRuntimeServerInstance<GraphQLContext> {\n  const commonConfig = getCommonConfig(options);\n\n  telemetry.setGlobalProperties({\n    runtime: {\n      framework: \"nextjs-pages-router\",\n    },\n  });\n\n  if (options.properties?._copilotkit) {\n    telemetry.setGlobalProperties({\n      _copilotkit: options.properties._copilotkit,\n    });\n  }\n\n  telemetry.capture(\n    \"oss.runtime.instance_created\",\n    getRuntimeInstanceTelemetryInfo(options.runtime),\n  );\n\n  const logger = commonConfig.logging;\n  logger.debug(\"Creating NextJS Pages Router endpoint\");\n\n  const yoga = createYoga({\n    ...commonConfig,\n    graphqlEndpoint: options.endpoint,\n  });\n\n  return yoga;\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAASA,kBAAkB;AAIpB,SAASC,sCAAsCC,SAA0C;AAJhG;AAKE,QAAMC,eAAeC,gBAAgBF,OAAAA;AAErCG,2BAAUC,oBAAoB;IAC5BC,SAAS;MACPC,WAAW;IACb;EACF,CAAA;AAEA,OAAIN,aAAQO,eAARP,mBAAoBQ,aAAa;AACnCL,6BAAUC,oBAAoB;MAC5BI,aAAaR,QAAQO,WAAWC;IAClC,CAAA;EACF;AAEAL,2BAAUM,QACR,gCACAC,gCAAgCV,QAAQK,OAAO,CAAA;AAGjD,QAAMM,SAASV,aAAaW;AAC5BD,SAAOE,MAAM,qCAAA;AAEb,QAAMC,OAAOC,WAAW;IACtB,GAAGd;IACHe,iBAAiBhB,QAAQiB;IACzBC,UAAU;MAAEC,UAAUC,WAAWD;IAAS;EAC5C,CAAA;AAEA,SAAO;IACLE,eAAeP;IACfQ,KAAKR;IACLS,MAAMT;IACNU,SAASV;EACX;AACF;AAnCgBf;;;ACJhB,SAA6B0B,cAAAA,mBAAkB;AAIxC,IAAMC,SAAS;EACpBC,KAAK;IACHC,YAAY;EACd;AACF;AASO,SAASC,wCACdC,SAA0C;AAlB5C;AAoBE,QAAMC,eAAeC,gBAAgBF,OAAAA;AAErCG,2BAAUC,oBAAoB;IAC5BC,SAAS;MACPC,WAAW;IACb;EACF,CAAA;AAEA,OAAIN,aAAQO,eAARP,mBAAoBQ,aAAa;AACnCL,6BAAUC,oBAAoB;MAC5BI,aAAaR,QAAQO,WAAWC;IAClC,CAAA;EACF;AAEAL,2BAAUM,QACR,gCACAC,gCAAgCV,QAAQK,OAAO,CAAA;AAGjD,QAAMM,SAASV,aAAaW;AAC5BD,SAAOE,MAAM,uCAAA;AAEb,QAAMC,OAAOC,YAAW;IACtB,GAAGd;IACHe,iBAAiBhB,QAAQiB;EAC3B,CAAA;AAEA,SAAOH;AACT;AA/BgBf;", "names": ["createYoga", "copilotRuntimeNextJSAppRouterEndpoint", "options", "commonConfig", "getCommonConfig", "telemetry", "setGlobalProperties", "runtime", "framework", "properties", "_copilotkit", "capture", "getRuntimeInstanceTelemetryInfo", "logger", "logging", "debug", "yoga", "createYoga", "graphqlEndpoint", "endpoint", "fetchAPI", "Response", "globalThis", "handleRequest", "GET", "POST", "OPTIONS", "createYoga", "config", "api", "<PERSON><PERSON><PERSON><PERSON>", "copilotRuntimeNextJSPagesRouterEndpoint", "options", "commonConfig", "getCommonConfig", "telemetry", "setGlobalProperties", "runtime", "framework", "properties", "_copilotkit", "capture", "getRuntimeInstanceTelemetryInfo", "logger", "logging", "debug", "yoga", "createYoga", "graphqlEndpoint", "endpoint"]}