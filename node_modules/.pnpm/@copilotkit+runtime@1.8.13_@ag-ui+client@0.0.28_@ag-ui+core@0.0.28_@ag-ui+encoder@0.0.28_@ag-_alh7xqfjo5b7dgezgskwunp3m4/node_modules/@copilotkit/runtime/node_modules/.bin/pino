#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/pino@9.7.0/node_modules/pino/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/pino@9.7.0/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/pino@9.7.0/node_modules/pino/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/pino@9.7.0/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../pino@9.7.0/node_modules/pino/bin.js" "$@"
else
  exec node  "$basedir/../../../../../../pino@9.7.0/node_modules/pino/bin.js" "$@"
fi
