{"version": 3, "sources": ["../../src/utils/detect-endpoint-type.utils.ts"], "sourcesContent": ["import { TRPCError } from \"@trpc/server\";\nimport { z } from \"zod\";\n\nexport enum RemoteEndpointType {\n  LangGraphPlatform = 'LangGraphPlatform',\n  CopilotKit = 'CopilotKit',\n  Invalid = 'Invalid',\n}\n\nconst removeTrailingSlash = (url: string) => url.replace(/\\/$/, '');\n\nexport const getHumanReadableEndpointType = (type: RemoteEndpointType) => {\n  switch (type) {\n    case RemoteEndpointType.LangGraphPlatform:\n      return 'LangGraph Platform';\n    case RemoteEndpointType.CopilotKit:\n      return 'CopilotKit';\n    default:\n      return 'Invalid';\n  }\n}\n\nexport async function detectRemoteEndpointType(url: string): Promise<{\n  url: string;\n  type: RemoteEndpointType;\n  humanReadableType: string;\n}> {\n\n  const promises = [\n    isLangGraphPlatformEndpoint(url),\n    isCopilotKitEndpoint(url),\n  ];\n\n  if (!url.endsWith('/copilotkit')) {\n    promises.push(isCopilotKitEndpoint(`${removeTrailingSlash(url)}/copilotkit`));\n  }\n\n  const results = await Promise.all(promises);\n\n  // LangGraph Platform\n  if (results[0]) {\n    return {\n      url,\n      type: RemoteEndpointType.LangGraphPlatform,\n      humanReadableType: 'LangGraph Platform',\n    }\n  }\n\n  // CopilotKit\n  if (results[1]) {\n    return {\n      url,\n      type: RemoteEndpointType.CopilotKit,\n      humanReadableType: 'CopilotKit',\n    }\n  }\n\n  // CopilotKit with appended /copilotkit\n  if (results[2]) {\n    return {\n      url: `${removeTrailingSlash(url)}/copilotkit`,\n      type: RemoteEndpointType.CopilotKit,\n      humanReadableType: 'CopilotKit',\n    }\n  }\n\n  return {\n    url,\n    type: RemoteEndpointType.Invalid,\n    humanReadableType: 'Invalid',\n  };\n}\n\nasync function isLangGraphPlatformEndpoint(url: string, retries: number = 0): Promise<boolean> {\n  let response\n\n  try {\n    response = await fetch(`${url}/assistants/search`, {\n      method: 'POST',\n\n      body: JSON.stringify({\n        metadata: {},\n        limit: 99,\n        offset: 0,\n      }),\n    });\n  } catch (error) {\n    return false;\n  }\n\n  if (!response.ok) {\n    if (response.status === 502) {\n      if (retries < 3) {\n        console.log(\"RETRYING LGC\", retries + 1);\n        return isLangGraphPlatformEndpoint(url, retries + 1);\n      }\n    }\n\n    if (response.status === 403) {\n      return true;\n    }\n\n    return false;\n  }\n\n  const data = await response.json();\n\n  if (data[0].assistant_id) {\n    return true;\n  }\n\n  return false;\n}\n\nasync function isCopilotKitEndpoint(url: string, retries: number = 0): Promise<boolean> {\n  let response\n\n  try {\n    response = await fetch(`${url}/info`, {\n      method: \"POST\",\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    return false;\n  }\n\n  if (!response.ok) {\n    if (response.status === 502) {\n      if (retries < 3) {\n        console.log(\"RETRYING CK\", retries + 1);\n        return isCopilotKitEndpoint(url, retries + 1);\n      }\n    }\n\n    return false;\n  }\n\n  const data = await response.json();\n\n  if (data.agents && data.actions) {\n    return true;\n  }\n\n  return false;\n}"], "mappings": ";AAGO,IAAK,qBAAL,kBAAKA,wBAAL;AACL,EAAAA,oBAAA,uBAAoB;AACpB,EAAAA,oBAAA,gBAAa;AACb,EAAAA,oBAAA,aAAU;AAHA,SAAAA;AAAA,GAAA;AAMZ,IAAM,sBAAsB,CAAC,QAAgB,IAAI,QAAQ,OAAO,EAAE;AAE3D,IAAM,+BAA+B,CAAC,SAA6B;AACxE,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEA,eAAsB,yBAAyB,KAI5C;AAED,QAAM,WAAW;AAAA,IACf,4BAA4B,GAAG;AAAA,IAC/B,qBAAqB,GAAG;AAAA,EAC1B;AAEA,MAAI,CAAC,IAAI,SAAS,aAAa,GAAG;AAChC,aAAS,KAAK,qBAAqB,GAAG,oBAAoB,GAAG,CAAC,aAAa,CAAC;AAAA,EAC9E;AAEA,QAAM,UAAU,MAAM,QAAQ,IAAI,QAAQ;AAG1C,MAAI,QAAQ,CAAC,GAAG;AACd,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,EACF;AAGA,MAAI,QAAQ,CAAC,GAAG;AACd,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,EACF;AAGA,MAAI,QAAQ,CAAC,GAAG;AACd,WAAO;AAAA,MACL,KAAK,GAAG,oBAAoB,GAAG,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,mBAAmB;AAAA,EACrB;AACF;AAEA,eAAe,4BAA4B,KAAa,UAAkB,GAAqB;AAC7F,MAAI;AAEJ,MAAI;AACF,eAAW,MAAM,MAAM,GAAG,GAAG,sBAAsB;AAAA,MACjD,QAAQ;AAAA,MAER,MAAM,KAAK,UAAU;AAAA,QACnB,UAAU,CAAC;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,QAAI,SAAS,WAAW,KAAK;AAC3B,UAAI,UAAU,GAAG;AACf,gBAAQ,IAAI,gBAAgB,UAAU,CAAC;AACvC,eAAO,4BAA4B,KAAK,UAAU,CAAC;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,SAAS,WAAW,KAAK;AAC3B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,MAAI,KAAK,CAAC,EAAE,cAAc;AACxB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,eAAe,qBAAqB,KAAa,UAAkB,GAAqB;AACtF,MAAI;AAEJ,MAAI;AACF,eAAW,MAAM,MAAM,GAAG,GAAG,SAAS;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,IACzB,CAAC;AAAA,EACH,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,QAAI,SAAS,WAAW,KAAK;AAC3B,UAAI,UAAU,GAAG;AACf,gBAAQ,IAAI,eAAe,UAAU,CAAC;AACtC,eAAO,qBAAqB,KAAK,UAAU,CAAC;AAAA,MAC9C;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,MAAI,KAAK,UAAU,KAAK,SAAS;AAC/B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": ["RemoteEndpointType"]}