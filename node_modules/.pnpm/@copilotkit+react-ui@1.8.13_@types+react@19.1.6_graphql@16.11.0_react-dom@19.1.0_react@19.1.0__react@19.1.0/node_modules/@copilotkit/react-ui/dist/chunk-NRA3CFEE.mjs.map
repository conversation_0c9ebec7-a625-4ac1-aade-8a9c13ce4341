{"version": 3, "sources": ["../src/components/help-modal/modal.tsx"], "sourcesContent": ["import React, { useMemo, useState, useRef, useEffect } from \"react\";\nimport { CloseIcon } from \"./icons\";\n\nexport function CopilotKitHelpModal() {\n  const [showHelpModal, setShowHelpModal] = useState(false);\n  const buttonRef = useRef<HTMLButtonElement>(null);\n  const popoverRef = useRef<HTMLDivElement>(null);\n\n  // Close popover when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        popoverRef.current &&\n        !popoverRef.current.contains(event.target as Node) &&\n        buttonRef.current &&\n        !buttonRef.current.contains(event.target as Node)\n      ) {\n        setShowHelpModal(false);\n      }\n    };\n\n    if (showHelpModal) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [showHelpModal]);\n\n  const HelpButton = () => (\n    <button\n      ref={buttonRef}\n      onClick={() => setShowHelpModal(!showHelpModal)}\n      className=\"copilotKitDebugMenuTriggerButton relative\"\n      aria-label=\"Open Help\"\n    >\n      Help\n    </button>\n  );\n\n  return (\n    <div className=\"relative\">\n      <HelpButton />\n      {showHelpModal && (\n        <div\n          ref={popoverRef}\n          className=\"absolute mt-2 z-50\"\n          style={{\n            top: \"100%\",\n            right: \"-120px\",\n            width: \"380px\",\n          }}\n        >\n          <div className=\"copilotKitHelpModal rounded-lg shadow-xl w-full p-4 flex-col relative\">\n            <button\n              className=\"copilotKitHelpModalCloseButton absolute text-gray-400 hover:text-gray-600 focus:outline-none\"\n              style={{ top: \"10px\", right: \"10px\" }}\n              onClick={() => setShowHelpModal(false)}\n              aria-label=\"Close\"\n            >\n              <CloseIcon />\n            </button>\n            <div className=\"w-full flex mb-6 justify-center\">\n              <h2 className=\"text-2xl font-bold\">Help Options</h2>\n            </div>\n            <div className=\"space-y-4 mb-4\">\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://docs.copilotkit.ai/coagents/troubleshooting/common-issues\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Visit the Troubleshooting and FAQ section in the docs\n                </a>\n              </div>\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://go.copilotkit.ai/dev-console-support-discord\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Go to Discord Support Channel (Community Support)\n                </a>\n              </div>\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://go.copilotkit.ai/dev-console-support-slack\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Apply for Priority Direct Slack Support\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;;;;AAAA,SAAyB,UAAU,QAAQ,iBAAiB;AA+BxD,cAmCQ,YAnCR;AA5BG,SAAS,sBAAsB;AACpC,QAAM,CAAC,eAAe,gBAAgB,IAAI,SAAS,KAAK;AACxD,QAAM,YAAY,OAA0B,IAAI;AAChD,QAAM,aAAa,OAAuB,IAAI;AAG9C,YAAU,MAAM;AACd,UAAM,qBAAqB,CAAC,UAAsB;AAChD,UACE,WAAW,WACX,CAAC,WAAW,QAAQ,SAAS,MAAM,MAAc,KACjD,UAAU,WACV,CAAC,UAAU,QAAQ,SAAS,MAAM,MAAc,GAChD;AACA,yBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,eAAe;AACjB,eAAS,iBAAiB,aAAa,kBAAkB;AAAA,IAC3D;AAEA,WAAO,MAAM;AACX,eAAS,oBAAoB,aAAa,kBAAkB;AAAA,IAC9D;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,aAAa,MACjB;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,SAAS,MAAM,iBAAiB,CAAC,aAAa;AAAA,MAC9C,WAAU;AAAA,MACV,cAAW;AAAA,MACZ;AAAA;AAAA,EAED;AAGF,SACE,qBAAC,SAAI,WAAU,YACb;AAAA,wBAAC,cAAW;AAAA,IACX,iBACC;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,WAAU;AAAA,QACV,OAAO;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QAEA,+BAAC,SAAI,WAAU,yEACb;AAAA;AAAA,YAAC;AAAA;AAAA,cACC,WAAU;AAAA,cACV,OAAO,EAAE,KAAK,QAAQ,OAAO,OAAO;AAAA,cACpC,SAAS,MAAM,iBAAiB,KAAK;AAAA,cACrC,cAAW;AAAA,cAEX,8BAAC,aAAU;AAAA;AAAA,UACb;AAAA,UACA,oBAAC,SAAI,WAAU,mCACb,8BAAC,QAAG,WAAU,sBAAqB,0BAAY,GACjD;AAAA,UACA,qBAAC,SAAI,WAAU,kBACb;AAAA,gCAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,YACA,oBAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,YACA,oBAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,aACF;AAAA,WACF;AAAA;AAAA,IACF;AAAA,KAEJ;AAEJ;", "names": []}