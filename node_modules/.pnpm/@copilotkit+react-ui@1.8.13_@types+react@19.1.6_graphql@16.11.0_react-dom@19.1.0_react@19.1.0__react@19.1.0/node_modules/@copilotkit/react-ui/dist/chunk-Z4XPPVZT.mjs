// src/hooks/use-copilot-chat-suggestions.tsx
import { useEffect } from "react";
import { useCopilotContext } from "@copilotkit/react-core";
import { randomId } from "@copilotkit/shared";
function useCopilotChatSuggestions({
  available = "enabled",
  instructions,
  className,
  minSuggestions = 1,
  maxSuggestions = 3
}, dependencies = []) {
  const context = useCopilotContext();
  useEffect(() => {
    if (available === "disabled")
      return;
    const id = randomId();
    context.addChatSuggestionConfiguration(id, {
      instructions,
      minSuggestions,
      maxSuggestions,
      className
    });
    return () => {
      context.removeChatSuggestionConfiguration(id);
    };
  }, [...dependencies, instructions, minSuggestions, maxSuggestions, className, available]);
}

export {
  useCopilotChatSuggestions
};
//# sourceMappingURL=chunk-Z4XPPVZT.mjs.map