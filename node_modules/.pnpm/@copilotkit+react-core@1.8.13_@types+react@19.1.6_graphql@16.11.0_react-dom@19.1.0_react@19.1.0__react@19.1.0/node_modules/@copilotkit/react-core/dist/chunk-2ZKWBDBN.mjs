import {
  useCopilotContext
} from "./chunk-7WG4MNRX.mjs";

// src/hooks/use-copilot-readable.ts
import { useEffect, useRef } from "react";
function convertToJSON(description, value) {
  return `${description}: ${typeof value === "string" ? value : JSON.stringify(value)}`;
}
function useCopilotReadable({
  description,
  value,
  parentId,
  categories,
  convert,
  available = "enabled"
}, dependencies) {
  const { addContext, removeContext } = useCopilotContext();
  const idRef = useRef();
  convert = convert || convertToJSON;
  const information = convert(description, value);
  useEffect(() => {
    if (available === "disabled")
      return;
    const id = addContext(information, parentId, categories);
    idRef.current = id;
    return () => {
      removeContext(id);
    };
  }, [available, information, parentId, addContext, removeContext, ...dependencies || []]);
  return idRef.current;
}

export {
  useCopilotReadable
};
//# sourceMappingURL=chunk-2ZKWBDBN.mjs.map