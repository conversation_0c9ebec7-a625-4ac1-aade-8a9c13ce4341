import React from 'react';
import { render, screen } from '@testing-library/react';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/useAuth';
import { useFeatures } from '@/hooks/useFeatures';
import AdminSidebar from '../admin-sidebar';

// Mock the hooks
jest.mock('next/navigation', () => ({
  usePathname: jest.fn(),
}));

jest.mock('@/lib/auth/useAuth');
jest.mock('@/hooks/useFeatures');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseFeatures = useFeatures as jest.MockedFunction<typeof useFeatures>;
const mockUsePathname = usePathname as jest.MockedFunction<typeof usePathname>;

describe('AdminSidebar Voice Menu', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUsePathname.mockReturnValue('/admin');
  });

  it('should show voice menu for partner with voice_intake feature', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'partner', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
      features: ['voice_intake'],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<AdminSidebar />);

    expect(screen.getByText('Voice Receptionist')).toBeInTheDocument();
    expect(screen.getByText('Call Logs')).toBeInTheDocument();
    expect(screen.getByText('Phone Numbers')).toBeInTheDocument();
    expect(screen.getByText('Failed Notifications')).toBeInTheDocument();
  });

  it('should hide voice menu for partner without voice_intake feature', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'partner', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<AdminSidebar />);

    expect(screen.queryByText('Voice Receptionist')).not.toBeInTheDocument();
    expect(screen.queryByText('Call Logs')).not.toBeInTheDocument();
    expect(screen.queryByText('Phone Numbers')).not.toBeInTheDocument();
    expect(screen.queryByText('Failed Notifications')).not.toBeInTheDocument();
  });

  it('should hide voice menu for attorney with voice_intake feature', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'attorney', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
      features: ['voice_intake'],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<AdminSidebar />);

    expect(screen.queryByText('Voice Receptionist')).not.toBeInTheDocument();
    expect(screen.queryByText('Call Logs')).not.toBeInTheDocument();
    expect(screen.queryByText('Phone Numbers')).not.toBeInTheDocument();
    expect(screen.queryByText('Failed Notifications')).not.toBeInTheDocument();
  });

  it('should hide voice menu when user profile is null', () => {
    mockUseAuth.mockReturnValue({
      profile: null,
      isAuthenticated: false,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn(),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<AdminSidebar />);

    expect(screen.queryByText('Voice Receptionist')).not.toBeInTheDocument();
    expect(screen.queryByText('Call Logs')).not.toBeInTheDocument();
    expect(screen.queryByText('Phone Numbers')).not.toBeInTheDocument();
    expect(screen.queryByText('Failed Notifications')).not.toBeInTheDocument();
  });

  it('should always show standard admin menu items', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'partner', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<AdminSidebar />);

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Tenants')).toBeInTheDocument();
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('Subscriptions')).toBeInTheDocument();
    expect(screen.getByText('Usage')).toBeInTheDocument();
    expect(screen.getByText('Security')).toBeInTheDocument();
  });
});
