{"version": 3, "sources": ["../src/components/error-boundary/error-boundary.tsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { Severity, CopilotKitError } from \"@copilotkit/shared\";\nimport { StatusChecker } from \"../../lib/status-checker\";\nimport { renderCopilotKitUsage, UsageBanner } from \"../usage-banner\";\nimport { useErrorToast } from \"./error-utils\";\nimport { COPILOT_CLOUD_ERROR_NAMES } from \"@copilotkit/shared\";\n\nconst statusChecker = new StatusChecker();\n\ninterface Props {\n  children: React.ReactNode;\n  publicApiKey?: string;\n  showUsageBanner?: boolean;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: CopilotKitError;\n  status?: {\n    severity: Severity;\n    message: string;\n  };\n}\n\nexport class CopilotErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n    };\n  }\n\n  static getDerivedStateFromError(error: CopilotKitError): State {\n    return { hasError: true, error };\n  }\n\n  componentDidMount() {\n    if (this.props.publicApiKey) {\n      statusChecker.start(this.props.publicApiKey, (newStatus) => {\n        this.setState((prevState) => {\n          if (newStatus?.severity !== prevState.status?.severity) {\n            return { status: newStatus ?? undefined };\n          }\n          return null;\n        });\n      });\n    }\n  }\n\n  componentWillUnmount() {\n    statusChecker.stop();\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"CopilotKit Error:\", error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.state.error instanceof CopilotKitError) {\n        // @ts-expect-error -- It's a copilotkit error at this state. Name is valid\n        if (COPILOT_CLOUD_ERROR_NAMES.includes(this.state.error.name)) {\n          return (\n            <ErrorToast error={this.state.error}>\n              {renderCopilotKitUsage(this.state.error)}\n            </ErrorToast>\n          );\n        }\n\n        return (\n          <>\n            {this.props.children}\n            {this.props.showUsageBanner && (\n              <UsageBanner\n                severity={this.state.status?.severity}\n                message={this.state.status?.message}\n              />\n            )}\n          </>\n        );\n      }\n      throw this.state.error;\n    }\n\n    return this.props.children;\n  }\n}\n\nexport function ErrorToast({ error, children }: { error?: Error; children: React.ReactNode }) {\n  const addErrorToast = useErrorToast();\n\n  useEffect(() => {\n    if (error) {\n      addErrorToast([error]);\n    }\n  }, [error, addErrorToast]);\n\n  if (!error) throw error;\n  return children;\n}\n"], "mappings": ";;;;;;;;;;;;AAAA,OAAO,SAAS,iBAAiB;AACjC,SAAmB,uBAAuB;AAI1C,SAAS,iCAAiC;AA0D9B,SAOF,UAPE,KAOF,YAPE;AAxDZ,IAAM,gBAAgB,IAAI,cAAc;AAiBjC,IAAM,uBAAN,cAAmC,MAAM,UAAwB;AAAA,EACtE,YAAY,OAAc;AACxB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EAEA,OAAO,yBAAyB,OAA+B;AAC7D,WAAO,EAAE,UAAU,MAAM,MAAM;AAAA,EACjC;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,MAAM,cAAc;AAC3B,oBAAc,MAAM,KAAK,MAAM,cAAc,CAAC,cAAc;AAC1D,aAAK,SAAS,CAAC,cAAc;AAvCrC;AAwCU,eAAI,uCAAW,gBAAa,eAAU,WAAV,mBAAkB,WAAU;AACtD,mBAAO,EAAE,QAAQ,gCAAa,OAAU;AAAA,UAC1C;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,kBAAc,KAAK;AAAA,EACrB;AAAA,EAEA,kBAAkB,OAAc,WAA4B;AAC1D,YAAQ,MAAM,qBAAqB,OAAO,SAAS;AAAA,EACrD;AAAA,EAEA,SAAS;AAzDX;AA0DI,QAAI,KAAK,MAAM,UAAU;AACvB,UAAI,KAAK,MAAM,iBAAiB,iBAAiB;AAE/C,YAAI,0BAA0B,SAAS,KAAK,MAAM,MAAM,IAAI,GAAG;AAC7D,iBACE,oBAAC,cAAW,OAAO,KAAK,MAAM,OAC3B,gCAAsB,KAAK,MAAM,KAAK,GACzC;AAAA,QAEJ;AAEA,eACE,iCACG;AAAA,eAAK,MAAM;AAAA,UACX,KAAK,MAAM,mBACV;AAAA,YAAC;AAAA;AAAA,cACC,WAAU,UAAK,MAAM,WAAX,mBAAmB;AAAA,cAC7B,UAAS,UAAK,MAAM,WAAX,mBAAmB;AAAA;AAAA,UAC9B;AAAA,WAEJ;AAAA,MAEJ;AACA,YAAM,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AAEO,SAAS,WAAW,EAAE,OAAO,SAAS,GAAiD;AAC5F,QAAM,gBAAgB,cAAc;AAEpC,YAAU,MAAM;AACd,QAAI,OAAO;AACT,oBAAc,CAAC,KAAK,CAAC;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,OAAO,aAAa,CAAC;AAEzB,MAAI,CAAC;AAAO,UAAM;AAClB,SAAO;AACT;", "names": []}