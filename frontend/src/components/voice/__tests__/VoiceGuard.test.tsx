import React from 'react';
import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/useAuth';
import { useFeatures } from '@/hooks/useFeatures';
import VoiceOverviewPage from '@/app/(dashboard)/admin/voice/page';

// Mock the hooks
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
  useRouter: jest.fn(),
}));

jest.mock('@/lib/auth/useAuth');
jest.mock('@/hooks/useFeatures');

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseFeatures = useFeatures as jest.MockedFunction<typeof useFeatures>;
const mockRedirect = require('next/navigation').redirect;

describe('Voice Guard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should redirect when user is not a partner', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'attorney', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
      features: ['voice_intake'],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(mockRedirect).toHaveBeenCalledWith('/');
  });

  it('should redirect when user does not have voice_intake feature', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'partner', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(false),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(mockRedirect).toHaveBeenCalledWith('/');
  });

  it('should render voice overview when user is partner with voice_intake feature', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'partner', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn().mockReturnValue(true),
      isLoading: false,
      features: ['voice_intake'],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(screen.getByText('Voice Receptionist')).toBeInTheDocument();
    expect(screen.getByText('Call Logs')).toBeInTheDocument();
    expect(screen.getByText('Phone Numbers')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
  });

  it('should show loading state when features are loading', () => {
    mockUseAuth.mockReturnValue({
      profile: { role: 'partner', tenant_id: 'test-tenant' },
      isAuthenticated: true,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn(),
      isLoading: true,
      features: [],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should redirect when user profile is null', () => {
    mockUseAuth.mockReturnValue({
      profile: null,
      isAuthenticated: false,
      isLoading: false,
    } as any);

    mockUseFeatures.mockReturnValue({
      hasFeature: jest.fn(),
      isLoading: false,
      features: [],
      error: null,
      hasAllFeatures: jest.fn(),
      hasAnyFeature: jest.fn(),
    });

    render(<VoiceOverviewPage />);

    expect(mockRedirect).toHaveBeenCalledWith('/');
  });
});
