"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/chat/Markdown.tsx
var Markdown_exports = {};
__export(Markdown_exports, {
  Markdown: () => Markdown
});
module.exports = __toCommonJS(Markdown_exports);
var import_react2 = require("react");
var import_react_markdown = __toESM(require("react-markdown"));

// src/components/chat/CodeBlock.tsx
var import_react = require("react");
var import_react_syntax_highlighter = require("react-syntax-highlighter");

// src/hooks/use-copy-to-clipboard.tsx
var React = __toESM(require("react"));
function useCopyToClipboard({ timeout = 2e3 }) {
  const [isCopied, setIsCopied] = React.useState(false);
  const copyToClipboard = (value) => {
    var _a;
    if (typeof window === "undefined" || !((_a = navigator.clipboard) == null ? void 0 : _a.writeText)) {
      return;
    }
    if (!value) {
      return;
    }
    navigator.clipboard.writeText(value).then(() => {
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
      }, timeout);
    });
  };
  return { isCopied, copyToClipboard };
}

// src/components/chat/Icons.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var CopyIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
      }
    )
  }
);
var DownloadIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"
      }
    )
  }
);
var CheckIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M4.5 12.75l6 6 9-13.5" })
  }
);

// src/components/chat/CodeBlock.tsx
var import_jsx_runtime2 = require("react/jsx-runtime");
var programmingLanguages = {
  javascript: ".js",
  python: ".py",
  java: ".java",
  c: ".c",
  cpp: ".cpp",
  "c++": ".cpp",
  "c#": ".cs",
  ruby: ".rb",
  php: ".php",
  swift: ".swift",
  "objective-c": ".m",
  kotlin: ".kt",
  typescript: ".ts",
  go: ".go",
  perl: ".pl",
  rust: ".rs",
  scala: ".scala",
  haskell: ".hs",
  lua: ".lua",
  shell: ".sh",
  sql: ".sql",
  html: ".html",
  css: ".css"
  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component
};
var generateRandomString = (length, lowercase = false) => {
  const chars = "ABCDEFGHJKLMNPQRSTUVWXY3456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return lowercase ? result.toLowerCase() : result;
};
var CodeBlock = (0, import_react.memo)(({ language, value }) => {
  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 2e3 });
  const downloadAsFile = () => {
    if (typeof window === "undefined") {
      return;
    }
    const fileExtension = programmingLanguages[language] || ".file";
    const suggestedFileName = `file-${generateRandomString(3, true)}${fileExtension}`;
    const fileName = window.prompt("Enter file name", suggestedFileName);
    if (!fileName) {
      return;
    }
    const blob = new Blob([value], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.download = fileName;
    link.href = url;
    link.style.display = "none";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  const onCopy = () => {
    if (isCopied)
      return;
    copyToClipboard(value);
  };
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)("div", { className: "copilotKitCodeBlock", children: [
    /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)("div", { className: "copilotKitCodeBlockToolbar", children: [
      /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { className: "copilotKitCodeBlockToolbarLanguage", children: language }),
      /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)("div", { className: "copilotKitCodeBlockToolbarButtons", children: [
        /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("button", { className: "copilotKitCodeBlockToolbarButton", onClick: downloadAsFile, children: DownloadIcon }),
        /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("button", { className: "copilotKitCodeBlockToolbarButton", onClick: onCopy, children: isCopied ? CheckIcon : CopyIcon })
      ] })
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
      import_react_syntax_highlighter.Prism,
      {
        language,
        style: highlightStyle,
        PreTag: "div",
        customStyle: {
          margin: 0,
          borderBottomLeftRadius: "0.375rem",
          borderBottomRightRadius: "0.375rem"
        },
        children: value
      }
    )
  ] });
});
CodeBlock.displayName = "CodeBlock";
var highlightStyle = {
  'pre[class*="language-"]': {
    color: "#d4d4d4",
    fontSize: "13px",
    textShadow: "none",
    fontFamily: 'Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace',
    direction: "ltr",
    textAlign: "left",
    whiteSpace: "pre",
    wordSpacing: "normal",
    wordBreak: "normal",
    lineHeight: "1.5",
    MozTabSize: "4",
    OTabSize: "4",
    tabSize: "4",
    WebkitHyphens: "none",
    MozHyphens: "none",
    msHyphens: "none",
    hyphens: "none",
    padding: "1em",
    margin: ".5em 0",
    overflow: "auto",
    background: "#1e1e1e"
  },
  'code[class*="language-"]': {
    color: "#d4d4d4",
    fontSize: "13px",
    textShadow: "none",
    fontFamily: 'Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace',
    direction: "ltr",
    textAlign: "left",
    whiteSpace: "pre",
    wordSpacing: "normal",
    wordBreak: "normal",
    lineHeight: "1.5",
    MozTabSize: "4",
    OTabSize: "4",
    tabSize: "4",
    WebkitHyphens: "none",
    MozHyphens: "none",
    msHyphens: "none",
    hyphens: "none"
  },
  'pre[class*="language-"]::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  'code[class*="language-"]::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  'pre[class*="language-"] *::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  'code[class*="language-"] *::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  ':not(pre) > code[class*="language-"]': {
    padding: ".1em .3em",
    borderRadius: ".3em",
    color: "#db4c69",
    background: "#1e1e1e"
  },
  ".namespace": {
    Opacity: ".7"
  },
  "doctype.doctype-tag": {
    color: "#569CD6"
  },
  "doctype.name": {
    color: "#9cdcfe"
  },
  comment: {
    color: "#6a9955"
  },
  prolog: {
    color: "#6a9955"
  },
  punctuation: {
    color: "#d4d4d4"
  },
  ".language-html .language-css .token.punctuation": {
    color: "#d4d4d4"
  },
  ".language-html .language-javascript .token.punctuation": {
    color: "#d4d4d4"
  },
  property: {
    color: "#9cdcfe"
  },
  tag: {
    color: "#569cd6"
  },
  boolean: {
    color: "#569cd6"
  },
  number: {
    color: "#b5cea8"
  },
  constant: {
    color: "#9cdcfe"
  },
  symbol: {
    color: "#b5cea8"
  },
  inserted: {
    color: "#b5cea8"
  },
  unit: {
    color: "#b5cea8"
  },
  selector: {
    color: "#d7ba7d"
  },
  "attr-name": {
    color: "#9cdcfe"
  },
  string: {
    color: "#ce9178"
  },
  char: {
    color: "#ce9178"
  },
  builtin: {
    color: "#ce9178"
  },
  deleted: {
    color: "#ce9178"
  },
  ".language-css .token.string.url": {
    textDecoration: "underline"
  },
  operator: {
    color: "#d4d4d4"
  },
  entity: {
    color: "#569cd6"
  },
  "operator.arrow": {
    color: "#569CD6"
  },
  atrule: {
    color: "#ce9178"
  },
  "atrule.rule": {
    color: "#c586c0"
  },
  "atrule.url": {
    color: "#9cdcfe"
  },
  "atrule.url.function": {
    color: "#dcdcaa"
  },
  "atrule.url.punctuation": {
    color: "#d4d4d4"
  },
  keyword: {
    color: "#569CD6"
  },
  "keyword.module": {
    color: "#c586c0"
  },
  "keyword.control-flow": {
    color: "#c586c0"
  },
  function: {
    color: "#dcdcaa"
  },
  "function.maybe-class-name": {
    color: "#dcdcaa"
  },
  regex: {
    color: "#d16969"
  },
  important: {
    color: "#569cd6"
  },
  italic: {
    fontStyle: "italic"
  },
  "class-name": {
    color: "#4ec9b0"
  },
  "maybe-class-name": {
    color: "#4ec9b0"
  },
  console: {
    color: "#9cdcfe"
  },
  parameter: {
    color: "#9cdcfe"
  },
  interpolation: {
    color: "#9cdcfe"
  },
  "punctuation.interpolation-punctuation": {
    color: "#569cd6"
  },
  variable: {
    color: "#9cdcfe"
  },
  "imports.maybe-class-name": {
    color: "#9cdcfe"
  },
  "exports.maybe-class-name": {
    color: "#9cdcfe"
  },
  escape: {
    color: "#d7ba7d"
  },
  "tag.punctuation": {
    color: "#808080"
  },
  cdata: {
    color: "#808080"
  },
  "attr-value": {
    color: "#ce9178"
  },
  "attr-value.punctuation": {
    color: "#ce9178"
  },
  "attr-value.punctuation.attr-equals": {
    color: "#d4d4d4"
  },
  namespace: {
    color: "#4ec9b0"
  },
  'pre[class*="language-javascript"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-javascript"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-jsx"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-jsx"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-typescript"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-typescript"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-tsx"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-tsx"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-css"]': {
    color: "#ce9178"
  },
  'code[class*="language-css"]': {
    color: "#ce9178"
  },
  'pre[class*="language-html"]': {
    color: "#d4d4d4"
  },
  'code[class*="language-html"]': {
    color: "#d4d4d4"
  },
  ".language-regex .token.anchor": {
    color: "#dcdcaa"
  },
  ".language-html .token.punctuation": {
    color: "#808080"
  },
  'pre[class*="language-"] > code[class*="language-"]': {
    position: "relative",
    zIndex: "1"
  },
  ".line-highlight.line-highlight": {
    background: "#f7ebc6",
    boxShadow: "inset 5px 0 0 #f7d87c",
    zIndex: "0"
  }
};

// src/components/chat/Markdown.tsx
var import_remark_gfm = __toESM(require("remark-gfm"));
var import_remark_math = __toESM(require("remark-math"));
var import_rehype_raw = __toESM(require("rehype-raw"));
var import_jsx_runtime3 = require("react/jsx-runtime");
var defaultComponents = {
  a(_a) {
    var _b = _a, { children } = _b, props = __objRest(_b, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("a", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { target: "_blank", rel: "noopener noreferrer", children }));
  },
  // @ts-expect-error -- inline
  code(_c) {
    var _d = _c, { children, className, inline } = _d, props = __objRest(_d, ["children", "className", "inline"]);
    if (Array.isArray(children) && children.length) {
      if (children[0] == "\u258D") {
        return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
          "span",
          {
            style: {
              animation: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
              marginTop: "0.25rem"
            },
            children: "\u258D"
          }
        );
      }
      children[0] = (children == null ? void 0 : children[0]).replace("`\u258D`", "\u258D");
    }
    const match = /language-(\w+)/.exec(className || "");
    if (inline) {
      return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("code", __spreadProps(__spreadValues({ className }, props), { children }));
    }
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
      CodeBlock,
      __spreadValues({
        language: match && match[1] || "",
        value: String(children).replace(/\n$/, "")
      }, props),
      Math.random()
    );
  },
  h1: (_e) => {
    var _f = _e, { children } = _f, props = __objRest(_f, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("h1", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h2: (_g) => {
    var _h = _g, { children } = _h, props = __objRest(_h, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("h2", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h3: (_i) => {
    var _j = _i, { children } = _j, props = __objRest(_j, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("h3", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h4: (_k) => {
    var _l = _k, { children } = _l, props = __objRest(_l, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("h4", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h5: (_m) => {
    var _n = _m, { children } = _n, props = __objRest(_n, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("h5", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h6: (_o) => {
    var _p = _o, { children } = _p, props = __objRest(_p, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("h6", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  p: (_q) => {
    var _r = _q, { children } = _r, props = __objRest(_r, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("p", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  pre: (_s) => {
    var _t = _s, { children } = _t, props = __objRest(_t, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("pre", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  blockquote: (_u) => {
    var _v = _u, { children } = _v, props = __objRest(_v, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("blockquote", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  ul: (_w) => {
    var _x = _w, { children } = _x, props = __objRest(_x, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("ul", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  li: (_y) => {
    var _z = _y, { children } = _z, props = __objRest(_z, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("li", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  }
};
var MemoizedReactMarkdown = (0, import_react2.memo)(
  import_react_markdown.default,
  (prevProps, nextProps) => prevProps.children === nextProps.children && prevProps.components === nextProps.components
);
var Markdown = ({ content, components }) => {
  return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("div", { className: "copilotKitMarkdown", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
    MemoizedReactMarkdown,
    {
      components: __spreadValues(__spreadValues({}, defaultComponents), components),
      remarkPlugins: [import_remark_gfm.default, import_remark_math.default],
      rehypePlugins: [import_rehype_raw.default],
      children: content
    }
  ) });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Markdown
});
//# sourceMappingURL=Markdown.js.map