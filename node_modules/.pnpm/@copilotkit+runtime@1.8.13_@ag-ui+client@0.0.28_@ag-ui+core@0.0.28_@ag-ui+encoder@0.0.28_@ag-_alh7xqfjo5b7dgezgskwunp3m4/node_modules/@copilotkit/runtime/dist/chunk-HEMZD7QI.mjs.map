{"version": 3, "sources": ["../src/lib/integrations/node-express/index.ts"], "sourcesContent": ["import { CreateCopilotRuntimeServerOptions } from \"../shared\";\nimport { copilotRuntimeNodeHttpEndpoint } from \"../node-http\";\nimport telemetry, { getRuntimeInstanceTelemetryInfo } from \"../../telemetry-client\";\n\nexport function copilotRuntimeNodeExpressEndpoint(options: CreateCopilotRuntimeServerOptions) {\n  telemetry.setGlobalProperties({\n    runtime: {\n      framework: \"node-express\",\n    },\n  });\n\n  telemetry.capture(\n    \"oss.runtime.instance_created\",\n    getRuntimeInstanceTelemetryInfo(options.runtime),\n  );\n  return copilotRuntimeNodeHttpEndpoint(options);\n}\n"], "mappings": ";;;;;;;;;;AAIO,SAASA,kCAAkCC,SAA0C;AAC1FC,2BAAUC,oBAAoB;IAC5BC,SAAS;MACPC,WAAW;IACb;EACF,CAAA;AAEAH,2BAAUI,QACR,gCACAC,gCAAgCN,QAAQG,OAAO,CAAA;AAEjD,SAAOI,+BAA+BP,OAAAA;AACxC;AAZgBD;", "names": ["copilotRuntimeNodeExpressEndpoint", "options", "telemetry", "setGlobalProperties", "runtime", "framework", "capture", "getRuntimeInstanceTelemetryInfo", "copilotRuntimeNodeHttpEndpoint"]}