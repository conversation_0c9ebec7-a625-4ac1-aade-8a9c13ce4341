import * as react_jsx_runtime from 'react/jsx-runtime';

declare const OpenIcon: react_jsx_runtime.JSX.Element;
declare const CloseIcon: react_jsx_runtime.JSX.Element;
declare const HeaderCloseIcon: react_jsx_runtime.JSX.Element;
declare const SendIcon: react_jsx_runtime.JSX.Element;
declare const MicrophoneIcon: react_jsx_runtime.JSX.Element;
declare const StopIcon: react_jsx_runtime.JSX.Element;
declare const RegenerateIcon: react_jsx_runtime.JSX.Element;
declare const CopyIcon: react_jsx_runtime.JSX.Element;
declare const SmallSpinnerIcon: react_jsx_runtime.JSX.Element;
declare const SpinnerIcon: react_jsx_runtime.JSX.Element;
declare const ActivityIcon: react_jsx_runtime.JSX.Element;
declare const ThumbsUpIcon: react_jsx_runtime.JSX.Element;
declare const ThumbsDownIcon: react_jsx_runtime.JSX.Element;
declare const DownloadIcon: react_jsx_runtime.JSX.Element;
declare const UploadIcon: react_jsx_runtime.JSX.Element;
declare const CheckIcon: react_jsx_runtime.JSX.Element;

export { ActivityIcon, CheckIcon, CloseIcon, CopyIcon, DownloadIcon, HeaderCloseIcon, MicrophoneIcon, OpenIcon, RegenerateIcon, SendIcon, SmallSpinnerIcon, SpinnerIcon, StopIcon, ThumbsDownIcon, ThumbsUpIcon, UploadIcon };
