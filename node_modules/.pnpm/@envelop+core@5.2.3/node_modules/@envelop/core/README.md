## `@envelop/core`

This is the core package for Envelop. You can find a complete documentation here:
https://github.com/n1ru4l/envelop

### Built-in plugins

- [`useSchema`](./docs/use-schema.md)
- [`useSchemaByContext`](./docs/use-schema-by-context.md)
- [`useErrorHandler`](./docs/use-error-handler.md)
- [`useExtendContext`](./docs/use-extend-context.md)
- [`useLogger`](./docs/use-logger.md)
- [`useMaskedErrors`](./docs/use-masked-errors.md)
- [`usePayloadFormatter`](./docs/use-payload-formatter.md)
- [`useEngine`](./docs/use-engine.md)
- [`useValidationRule`](./docs/use-validation-rule.md)
