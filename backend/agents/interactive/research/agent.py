"""
AiLex Research Agent Implementation

This module provides the ResearchAgent class, which implements the research agent
for the AiLex system. The research agent handles quick research queries and queues
longer research tasks.

The agent uses a hybrid retrieval pipeline with:
- Query generation using LLMs
- Legal document search via laws-API service
- Graph expansion using laws-API knowledge graph
- Document recommendations via laws-API
- Web search as an alternative path

The agent supports multiple research paths:
1. Legal research path: Uses laws-API for search, recommendations, and graph expansion
2. Web search path: Uses web search as an alternative to legal database search
3. Long research path: Queues extensive research tasks for asynchronous processing
"""

import logging
from typing import Any, Dict, List, Optional

from pydantic import Field

from backend.agents.interactive.research.graph import create_research_graph
from backend.agents.interactive.research.state import (
    ResearchState,
    UserContext,
)
from shared.core.base_agent import AgentConfig, BaseAgent
from shared.core.state import AiLexState

# Configure logger
logger = logging.getLogger(__name__)


class ResearchAgentConfig(AgentConfig):
    """
    Configuration for the Research Agent.
    
    Attributes:
        name: The name of the agent
        description: A description of the agent
        version: The version of the agent
        tools: List of tool names that the agent can use
        model: The LLM model to use for the agent
        temperature: The temperature to use for LLM generation
        vector_search_provider: The vector search provider to use
        reranking_provider: The reranking provider to use
        web_search_provider: The web search provider to use
        max_documents: The maximum number of documents to return
        max_web_results: The maximum number of web search results to return
    """
    name: str = "research_agent"
    description: str = "Research agent for legal research and web search"
    version: str = "1.0.0"
    tools: List[str] = Field(default_factory=lambda: [
        "legal_search",
        "legal_recommend",
        "legal_graph",
        "web_search",
        "collect_citations",
    ])
    model: str = "gpt-4o"
    temperature: float = 0.3
    legal_search_provider: str = "laws_api"
    reranking_provider: str = "voyage"
    web_search_provider: str = "perplexity"
    max_documents: int = 10
    max_web_results: int = 5


class ResearchAgent(BaseAgent):
    """
    Research Agent for the AiLex system.
    
    This agent handles quick research queries and queues longer research tasks.
    It supports both legal database search and web search paths.
    """
    
    def __init__(self, config: Optional[ResearchAgentConfig] = None):
        """
        Initialize the Research Agent.
        
        Args:
            config: Configuration for the agent
        """
        super().__init__(config or ResearchAgentConfig())
        self.graph = create_research_graph()
        
    def _get_default_config(self) -> ResearchAgentConfig:
        """
        Get the default configuration for the agent.
        
        Returns:
            The default configuration
        """
        return ResearchAgentConfig()
    
    def _register_tools(self) -> None:
        """Register tools for the agent."""
        # Register legal search tool (replaces vector_search)
        self.register_tool("legal_search", self._legal_search)

        # Register legal recommendation tool
        self.register_tool("legal_recommend", self._legal_recommend)

        # Register legal graph tool (replaces graph_expand)
        self.register_tool("legal_graph", self._legal_graph)

        # Register web search tool
        self.register_tool("web_search", self._web_search)

        # Register citation collection tool
        self.register_tool("collect_citations", self._collect_citations)
    
    async def _legal_search(self, query: str, jurisdiction: str, practice_areas: List[str], state: AiLexState) -> List[Dict[str, Any]]:
        """
        Perform legal document search using laws-API.

        Args:
            query: The search query
            jurisdiction: The jurisdiction to search
            practice_areas: The practice areas to search
            state: The current state

        Returns:
            List of search results
        """
        from backend.services.laws_api_client import LawsApiClient, SearchRequest, PracticeArea

        try:
            async with LawsApiClient() as client:
                search_request = SearchRequest(
                    query=query,
                    jurisdiction=[jurisdiction] if jurisdiction else ['texas'],
                    limit=self.config.max_documents,
                    filters={
                        'practice_area': [
                            PracticeArea.PERSONAL_INJURY.value
                            if area.lower() in ['personal_injury', 'personal injury', 'pi']
                            else area.lower()
                            for area in practice_areas
                        ]
                    } if practice_areas else None
                )

                results = await client.search(search_request)

                # Convert to dict format
                return [
                    {
                        'id': result.id,
                        'title': result.title,
                        'content': result.content,
                        'jurisdiction': result.jurisdiction,
                        'relevance_score': result.relevance_score,
                        'citation': result.citation,
                        'url': result.url
                    }
                    for result in results
                ]
        except Exception as e:
            logger.error(f"Legal search error: {e}")
            return []

    async def _legal_recommend(self, content: str, document_id: Optional[str], state: AiLexState) -> List[Dict[str, Any]]:
        """
        Get legal document recommendations using laws-API.

        Args:
            content: Content to get recommendations for
            document_id: Optional document ID for recommendations
            state: The current state

        Returns:
            List of recommended documents
        """
        from backend.services.laws_api_client import LawsApiClient, RecommendRequest

        try:
            async with LawsApiClient() as client:
                recommend_request = RecommendRequest(
                    content=content,
                    document_id=document_id,
                    jurisdiction=['texas'],
                    limit=5
                )

                results = await client.recommend(recommend_request)

                # Convert to dict format
                return [
                    {
                        'id': result.id,
                        'title': result.title,
                        'content': result.content,
                        'jurisdiction': result.jurisdiction,
                        'similarity_score': result.similarity_score,
                        'relationship_type': result.relationship_type.value
                    }
                    for result in results
                ]
        except Exception as e:
            logger.error(f"Legal recommendation error: {e}")
            return []

    async def _legal_graph(self, entity_id: str, entity_type: Optional[str], state: AiLexState) -> List[Dict[str, Any]]:
        """
        Query legal knowledge graph using laws-API.

        Args:
            entity_id: ID of the entity to explore
            entity_type: Type of entity
            state: The current state

        Returns:
            List of related entities and relationships
        """
        from backend.services.laws_api_client import LawsApiClient, GraphRequest, EntityType

        try:
            async with LawsApiClient() as client:
                graph_request = GraphRequest(
                    entity_id=entity_id,
                    entity_type=EntityType(entity_type) if entity_type else None,
                    depth=2,
                    limit=20
                )

                result = await client.graph_query(graph_request)

                # Convert to dict format
                return [
                    {
                        'nodes': [
                            {
                                'id': node.id,
                                'label': node.label,
                                'type': node.type.value,
                                'properties': node.properties
                            }
                            for node in result.nodes
                        ],
                        'edges': [
                            {
                                'source': edge.source,
                                'target': edge.target,
                                'relationship': edge.relationship.value,
                                'weight': edge.weight
                            }
                            for edge in result.edges
                        ]
                    }
                ]
        except Exception as e:
            logger.error(f"Legal graph query error: {e}")
            return []
    
    async def _web_search(self, query: str, state: AiLexState) -> List[Dict[str, Any]]:
        """
        Perform web search as an alternative research path.
        
        Args:
            query: The search query
            state: The current state
            
        Returns:
            List of web search results
        """
        # Implementation would use Perplexity API or similar
        logger.info(f"Web search for query: {query}")
        return []
    
    async def _collect_citations(self, documents: List[Dict[str, Any]], state: AiLexState) -> List[Dict[str, Any]]:
        """
        Collect and format citations from search results.
        
        Args:
            documents: List of documents to format as citations
            state: The current state
            
        Returns:
            List of formatted citations
        """
        logger.info(f"Collecting citations for {len(documents)} documents")
        return []
    
    async def initialize(self, state: AiLexState) -> AiLexState:
        """
        Initialize the agent with the given state.
        
        This method converts the generic AiLexState to a ResearchState and
        initializes it with the user's question and context.
        
        Args:
            state: The initial state
            
        Returns:
            The updated state
        """
        logger.info("Initializing Research Agent")
        
        # Get the user's question from the last message
        last_message = state.get_last_message()
        if not last_message or last_message["role"] != "user":
            state.add_message("system", "Please provide a research question.")
            return state
        
        question = last_message["content"]
        
        # Create a ResearchState from the AiLexState
        research_state = ResearchState(
            question=question,
            user_context=UserContext(**state.user_context),
            messages=state.messages,
            metadata=state.metadata,
            thread_id=state.thread_id,
            agent_id=state.agent_id,
        )
        
        # Add a system message indicating the research has started
        research_state.add_message(
            "system",
            f"Researching: {question}",
            {"status": "researching"}
        )
        
        # Update the original state with the research state
        state.messages = research_state.messages
        state.metadata = research_state.metadata
        
        # Store the research state in the metadata for later use
        state.metadata["research_state"] = research_state.to_dict()
        
        return state
    
    async def execute(self, state: AiLexState) -> AiLexState:
        """
        Execute the agent with the given state.
        
        This method runs the research graph with the ResearchState and
        updates the AiLexState with the results.
        
        Args:
            state: The current state
            
        Returns:
            The updated state
        """
        logger.info("Executing Research Agent")
        
        # Get the research state from the metadata
        research_state_dict = state.metadata.get("research_state", {})
        research_state = ResearchState.from_dict(research_state_dict)
        
        # Run the research graph
        try:
            result = await self.graph.ainvoke(research_state)
            
            # Update the research state with the result
            research_state = result
            
            # Add the answer to the messages
            if research_state.answer:
                state.add_message(
                    "assistant",
                    research_state.answer,
                    {
                        "citations": research_state.get_formatted_citations(),
                        "query_type": research_state.query_type,
                        "data_source": research_state.data_source,
                    }
                )
            else:
                state.add_message(
                    "assistant",
                    "I couldn't find any relevant information for your question. Please try rephrasing or providing more context.",
                    {"error": "no_results"}
                )
            
            # Update the metadata with the updated research state
            state.metadata["research_state"] = research_state.to_dict()
            
        except Exception as e:
            logger.error(f"Error executing research graph: {str(e)}")
            state.add_message(
                "assistant",
                "I encountered an error while researching your question. Please try again later.",
                {"error": str(e)}
            )
        
        return state
    
    async def cleanup(self, state: AiLexState) -> AiLexState:
        """
        Clean up after the agent's execution.
        
        This method performs any necessary cleanup and returns the final state.
        
        Args:
            state: The current state
            
        Returns:
            The final state
        """
        logger.info("Cleaning up Research Agent")
        
        # Add a system message indicating the research is complete
        state.add_message(
            "system",
            "Research complete.",
            {"status": "complete"}
        )
        
        return state
