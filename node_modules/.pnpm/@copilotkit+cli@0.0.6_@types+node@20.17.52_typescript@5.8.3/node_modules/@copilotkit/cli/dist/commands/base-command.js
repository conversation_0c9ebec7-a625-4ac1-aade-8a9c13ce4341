// src/commands/base-command.ts
import { Command } from "@oclif/core";
import Sentry, { consoleIntegration } from "@sentry/node";

// src/utils/version.ts
var LIB_VERSION = "0.0.6";

// src/utils/trpc.ts
import { createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink } from "@trpc/client";
import superjson from "superjson";
var COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || "https://cloud.copilotkit.ai";

// src/commands/base-command.ts
import chalk from "chalk";
var BaseCommand = class extends Command {
  async init() {
    await this.checkCLIVersion();
    if (process.env.SENTRY_DISABLED === "true") {
      return;
    }
    Sentry.init({
      dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4508581448581120",
      integrations: [
        consoleIntegration()
      ],
      // Tracing
      tracesSampleRate: 1
      //  Capture 100% of the transactions
    });
  }
  async catch(err) {
    if (process.env.SENTRY_DISABLED === "true") {
      super.catch(err);
      return;
    }
    Sentry.captureException(err);
    super.catch(err);
  }
  async finally() {
    if (process.env.SENTRY_DISABLED === "true") {
      return;
    }
    Sentry.close();
  }
  async run() {
  }
  async checkCLIVersion() {
    const response = await fetch(`${COPILOT_CLOUD_BASE_URL}/api/healthz`);
    const data = await response.json();
    const cloudVersion = data.cliVersion;
    if (cloudVersion === LIB_VERSION) {
      return;
    }
    this.log(chalk.yellow("================ New version available! =================\n"));
    this.log(`A new CopilotKit CLI version is available (${LIB_VERSION}).
`);
    this.log("Please update your CLI to the latest version:\n\n");
    this.log(`${chalk.cyan(chalk.underline(chalk.bold("npm:")))}	 npm install -g @copilotkit/cli@latest
`);
    this.log(`${chalk.cyan(chalk.underline(chalk.bold("pnpm:")))}	 pnpm install -g @copilotkit/cli@latest
`);
    this.log(`${chalk.cyan(chalk.underline(chalk.bold("yarn:")))}	 yarn global add @copilotkit/cli@latest
`);
    process.exit(0);
  }
};
export {
  BaseCommand
};
//# sourceMappingURL=base-command.js.map