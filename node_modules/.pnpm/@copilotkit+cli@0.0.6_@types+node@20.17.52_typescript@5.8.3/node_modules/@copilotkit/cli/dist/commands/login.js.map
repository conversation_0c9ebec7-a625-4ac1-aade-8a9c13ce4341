{"version": 3, "sources": ["../../src/services/auth.service.ts", "../../src/utils/trpc.ts", "../../src/services/analytics.service.ts", "../../src/commands/base-command.ts", "../../src/utils/version.ts", "../../src/commands/login.ts"], "sourcesContent": ["// @ts-ignore\nimport Conf from 'conf'\nimport cors from 'cors'\nimport express from 'express'\nimport crypto from 'node:crypto'\nimport open from 'open'\nimport getPort from 'get-port'\nimport ora from 'ora'\nimport chalk from 'chalk'\nimport inquirer from 'inquirer'\nimport {Command} from '@oclif/core'\nimport {createTRPCClient} from '../utils/trpc.js'\nimport { AnalyticsService } from '../services/analytics.service.js'\n\ninterface LoginResponse {\n  cliToken: string\n  user: {\n    email: string\n    id: string\n  }\n  organization: {\n    id: string\n  }\n}\n\nexport class AuthService {\n  private readonly config = new Conf({projectName: 'CopilotKitCLI'})\n  private readonly COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || 'https://cloud.copilotkit.ai'\n\n  getToken(): string | undefined {\n    return this.config.get('cliToken') as string | undefined\n  }\n\n  getCLIToken(): string | undefined {\n    const cliToken = this.config.get('cliToken') as string | undefined\n    return cliToken\n  }\n\n  async logout(): Promise<void> {\n    const cliToken = this.getCLIToken();\n    \n    if (!cliToken) {\n      throw new Error('You are not logged in');\n    }\n\n    const trpcClient = createTRPCClient(cliToken)\n    const me = await trpcClient.me.query()\n\n    const analytics = new AnalyticsService({ userId: me.user!.id, organizationId: me.organization!.id, email: me.user!.email });\n    \n    this.config.delete('cliToken')\n    \n    analytics.track({\n      event: \"cli.logout\",\n      properties: {\n        organizationId: me.organization!.id,\n        userId: me.user!.id,\n        email: me.user!.email,\n      }\n    });\n  }\n\n  async requireLogin(cmd: Command): Promise<LoginResponse> {\n    let cliToken = this.getCLIToken()\n\n    // Check authentication\n    if (!cliToken) {\n      try {\n        const {shouldLogin} = await inquirer.prompt([\n          {\n            name: 'shouldLogin',\n            type: 'confirm',\n            message: 'You are not yet authenticated. Authenticate with Copilot Cloud? (press Enter to confirm)',\n            default: true,\n          },\n        ])\n\n        if (shouldLogin) {\n          const loginResult = await this.login()\n          cliToken = loginResult.cliToken\n          cmd.log(`🪁 Logged in as ${chalk.hex('#7553fc')(loginResult.user.email)}\\n`)\n          return loginResult\n        } else {\n          cmd.error('Authentication required to proceed.')\n        }\n      } catch (error) {\n        if (error instanceof Error && error.name === 'ExitPromptError') {\n          cmd.error(chalk.yellow('\\nAuthentication cancelled'))\n        }\n\n        throw error\n      }\n    }\n\n    let me;\n\n    const trpcClient = createTRPCClient(cliToken)\n    try {\n      me = await trpcClient.me.query()\n    } catch (error) {\n      cmd.log(chalk.red(\"Could not authenticate with Copilot Cloud. Please try again.\"))\n      process.exit(1)\n    }\n\n    if (!me.organization || !me.user) {\n      cmd.error('Authentication required to proceed.')\n    }\n\n    return {cliToken, user: me.user, organization: me.organization}\n  }\n\n  async login(): Promise<LoginResponse> {\n    let analytics: AnalyticsService;\n    analytics = new AnalyticsService();\n\n    const app = express()\n    app.use(cors())\n    app.use(express.urlencoded({extended: true}))\n    app.use(express.json())\n\n    const port = await getPort()\n    const state = crypto.randomBytes(16).toString('hex')\n\n    return new Promise((resolve) => {\n      const server = app.listen(port, () => {})\n\n      analytics.track({\n        event: \"cli.login.initiated\",\n        properties: {}\n      });\n\n      const spinner = ora('Waiting for browser authentication to complete...\\n').start()\n\n      app.post('/callback', async (req, res) => {\n        const {cliToken, user, organization} = req.body\n\n        analytics = new AnalyticsService({ userId: user.id, organizationId: organization.id, email: user.email });\n        analytics.track({\n          event: \"cli.login.success\",\n          properties: {\n            organizationId: organization.id,\n            userId: user.id,\n            email: user.email,\n          }\n        });\n\n        if (state !== req.query.state) {\n          res.status(401).json({message: 'Invalid state'})\n          spinner.fail('Invalid state')\n          return\n        }\n\n        this.config.set('cliToken', cliToken)\n        res.status(200).json({message: 'Callback called'})\n        spinner.succeed(`🪁 Successfully logged in as ${chalk.hex('#7553fc')(user.email)}\\n`)\n        server.close()\n        resolve({\n          cliToken,\n          organization,\n          user,\n        })\n      })\n\n      open(`${this.COPILOT_CLOUD_BASE_URL}/cli-auth?callbackUrl=http://localhost:${port}/callback&state=${state}`)\n    })\n  }\n}\n", "import {createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink} from '@trpc/client'\nimport type {CLIRouter} from '@repo/trpc-cli'\nimport superjson from 'superjson'\n\nexport const COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || 'https://cloud.copilotkit.ai'\n\nexport function createTRPCClient(cliToken: string) {\n  return createTRPClient_<CLIRouter>({\n    links: [\n      unstable_httpBatchStreamLink({\n        transformer: superjson,\n        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,\n        headers: () => {\n          const headers = new Headers()\n          headers.set('x-trpc-source', 'cli')\n          headers.set('x-cli-token', cliToken)\n          return headers\n        },\n      }),\n    ],\n  })\n}\n", "import {Analytics} from '@segment/analytics-node'\nimport {AnalyticsEvents} from './events.js'\nimport Conf from 'conf'\n\nexport class AnalyticsService {\n  private segment: Analytics | undefined\n  private globalProperties: Record<string, any> = {}\n  private userId: string | undefined;\n  private email: string | undefined;\n  private organizationId: string | undefined;\n  private config = new Conf({projectName: 'CopilotKitCLI'})\n\n  constructor(private readonly authData?: {\n    userId: string,\n    email: string,\n    organizationId: string,\n  }) {\n    if (process.env.SEGMENT_DISABLED === 'true') {\n      return;\n    }\n\n    const segmentWriteKey = process.env.SEGMENT_WRITE_KEY || \"9Pv6QyExYef2P4hPz4gks6QAvNMi2AOf\"\n\n    this.globalProperties = {\n      service: 'cli',\n    }\n\n\n    if (this.authData?.userId) {\n      this.userId = this.authData.userId\n    }\n\n    if (this.authData?.email) {\n      this.email = this.authData.email\n      this.globalProperties.email = this.authData.email\n    }\n\n    if (this.authData?.organizationId) {\n      this.organizationId = this.authData.organizationId\n    }\n\n    this.segment = new Analytics({\n      writeKey: segmentWriteKey,\n      disable: process.env.SEGMENT_DISABLE === 'true',\n    })\n\n    const config = new Conf({projectName: 'CopilotKitCLI'})\n    if (!config.get('anonymousId')) {\n      config.set('anonymousId', crypto.randomUUID())\n    }\n  }\n\n  private getAnonymousId(): string {\n    const anonymousId = this.config.get('anonymousId')\n    if (!anonymousId) {\n      const anonymousId = crypto.randomUUID()\n      this.config.set('anonymousId', anonymousId)\n      return anonymousId\n    }\n\n    return anonymousId as string;\n  }\n\n  public track<K extends keyof AnalyticsEvents>(\n    event: Omit<Parameters<Analytics['track']>[0], 'userId'> & {\n      event: K\n      properties: AnalyticsEvents[K]\n    },\n  ): void {\n    if (!this.segment) {\n      return;\n    }\n\n    const payload = {\n      userId: this.userId ? this.userId : undefined,\n      email: this.email ? this.email : undefined,\n      anonymousId: this.getAnonymousId(),\n      event: event.event,\n      properties: {\n        ...this.globalProperties,\n        ...event.properties,\n        $groups: this.organizationId ? {\n          segment_group: this.organizationId,\n        } : undefined,\n        eventProperties: {\n          ...event.properties,\n          ...this.globalProperties,\n        },\n      },\n    }\n\n    this.segment.track(payload)\n  }\n}\n", "import { Command } from \"@oclif/core\";\nimport Sentry, { consoleIntegration } from \"@sentry/node\";\nimport { LIB_VERSION } from \"../utils/version.js\";\nimport { COPILOT_CLOUD_BASE_URL } from \"../utils/trpc.js\";\nimport chalk from \"chalk\";\n\nexport class BaseCommand extends Command {\n  async init() {\n    await this.checkCLIVersion();\n\n    if (process.env.SENTRY_DISABLED === 'true') {\n      return;\n    }\n\n    Sentry.init({\n      dsn: process.env.SENTRY_DSN || \"https://<EMAIL>/4508581448581120\",\n      integrations: [\n        consoleIntegration(),\n      ],\n      // Tracing\n      tracesSampleRate: 1.0, //  Capture 100% of the transactions\n    });\n  }\n\n  async catch(err: any) {\n    if (process.env.SENTRY_DISABLED === 'true') {\n      super.catch(err)\n      return;\n    }\n\n    Sentry.captureException(err)\n    super.catch(err)\n  }\n\n  async finally() {\n    if (process.env.SENTRY_DISABLED === 'true') {\n      return;\n    }\n\n    Sentry.close()\n  }\n  \n  async run() {}\n\n  async checkCLIVersion() {\n    const response = await fetch(`${COPILOT_CLOUD_BASE_URL}/api/healthz`)\n    const data = await response.json()\n    const cloudVersion = data.cliVersion\n\n    if (cloudVersion === LIB_VERSION) {\n      return;\n    }\n\n    this.log(chalk.yellow('================ New version available! =================\\n'))\n    this.log(`A new CopilotKit CLI version is available (${LIB_VERSION}).\\n`)\n    this.log('Please update your CLI to the latest version:\\n\\n')\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('npm:')))}\\t npm install -g @copilotkit/cli@latest\\n`)\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('pnpm:')))}\\t pnpm install -g @copilotkit/cli@latest\\n`)\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('yarn:')))}\\t yarn global add @copilotkit/cli@latest\\n`)\n\n    process.exit(0)\n  }\n}\n", "// This is auto generated!\nexport const LIB_VERSION = \"0.0.6\";\n", "import {Config} from '@oclif/core'\n\nimport {AuthService} from '../services/auth.service.js'\nimport { BaseCommand } from './base-command.js'\n\nexport default class CloudLogin extends BaseCommand {\n  static override description = 'Authenticate with Copilot Cloud'\n\n  static override examples = ['<%= config.bin %> login']\n\n  constructor(argv: string[], config: Config, private authService = new AuthService()) {\n    super(argv, config)\n  }\n\n  public async run(): Promise<void> {\n    await this.parse(CloudLogin)\n\n    try {\n      await this.authService.login()\n    } catch (error: unknown) {\n      this.error((error as Error).message)\n    }\n  }\n}\n"], "mappings": ";AACA,OAAOA,WAAU;AACjB,OAAO,UAAU;AACjB,OAAO,aAAa;AACpB,OAAOC,aAAY;AACnB,OAAO,UAAU;AACjB,OAAO,aAAa;AACpB,OAAO,SAAS;AAChB,OAAO,WAAW;AAClB,OAAO,cAAc;;;ACTrB,SAAQ,oBAAoB,kBAAkB,oCAAmC;AAEjF,OAAO,eAAe;AAEf,IAAM,yBAAyB,QAAQ,IAAI,0BAA0B;AAErE,SAAS,iBAAiB,UAAkB;AACjD,SAAO,iBAA4B;AAAA,IACjC,OAAO;AAAA,MACL,6BAA6B;AAAA,QAC3B,aAAa;AAAA,QACb,KAAK,GAAG,sBAAsB;AAAA,QAC9B,SAAS,MAAM;AACb,gBAAM,UAAU,IAAI,QAAQ;AAC5B,kBAAQ,IAAI,iBAAiB,KAAK;AAClC,kBAAQ,IAAI,eAAe,QAAQ;AACnC,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;;;ACrBA,SAAQ,iBAAgB;AAExB,OAAO,UAAU;AAEV,IAAM,mBAAN,MAAuB;AAAA,EAQ5B,YAA6B,UAI1B;AAJ0B;AAK3B,QAAI,QAAQ,IAAI,qBAAqB,QAAQ;AAC3C;AAAA,IACF;AAEA,UAAM,kBAAkB,QAAQ,IAAI,qBAAqB;AAEzD,SAAK,mBAAmB;AAAA,MACtB,SAAS;AAAA,IACX;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,SAAS,KAAK,SAAS;AAAA,IAC9B;AAEA,QAAI,KAAK,UAAU,OAAO;AACxB,WAAK,QAAQ,KAAK,SAAS;AAC3B,WAAK,iBAAiB,QAAQ,KAAK,SAAS;AAAA,IAC9C;AAEA,QAAI,KAAK,UAAU,gBAAgB;AACjC,WAAK,iBAAiB,KAAK,SAAS;AAAA,IACtC;AAEA,SAAK,UAAU,IAAI,UAAU;AAAA,MAC3B,UAAU;AAAA,MACV,SAAS,QAAQ,IAAI,oBAAoB;AAAA,IAC3C,CAAC;AAED,UAAM,SAAS,IAAI,KAAK,EAAC,aAAa,gBAAe,CAAC;AACtD,QAAI,CAAC,OAAO,IAAI,aAAa,GAAG;AAC9B,aAAO,IAAI,eAAe,OAAO,WAAW,CAAC;AAAA,IAC/C;AAAA,EACF;AAAA,EA7CQ;AAAA,EACA,mBAAwC,CAAC;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,IAAI,KAAK,EAAC,aAAa,gBAAe,CAAC;AAAA,EA0ChD,iBAAyB;AAC/B,UAAM,cAAc,KAAK,OAAO,IAAI,aAAa;AACjD,QAAI,CAAC,aAAa;AAChB,YAAMC,eAAc,OAAO,WAAW;AACtC,WAAK,OAAO,IAAI,eAAeA,YAAW;AAC1C,aAAOA;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,MACL,OAIM;AACN,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AAEA,UAAM,UAAU;AAAA,MACd,QAAQ,KAAK,SAAS,KAAK,SAAS;AAAA,MACpC,OAAO,KAAK,QAAQ,KAAK,QAAQ;AAAA,MACjC,aAAa,KAAK,eAAe;AAAA,MACjC,OAAO,MAAM;AAAA,MACb,YAAY;AAAA,QACV,GAAG,KAAK;AAAA,QACR,GAAG,MAAM;AAAA,QACT,SAAS,KAAK,iBAAiB;AAAA,UAC7B,eAAe,KAAK;AAAA,QACtB,IAAI;AAAA,QACJ,iBAAiB;AAAA,UACf,GAAG,MAAM;AAAA,UACT,GAAG,KAAK;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,OAAO;AAAA,EAC5B;AACF;;;AFpEO,IAAM,cAAN,MAAkB;AAAA,EACN,SAAS,IAAIC,MAAK,EAAC,aAAa,gBAAe,CAAC;AAAA,EAChD,yBAAyB,QAAQ,IAAI,0BAA0B;AAAA,EAEhF,WAA+B;AAC7B,WAAO,KAAK,OAAO,IAAI,UAAU;AAAA,EACnC;AAAA,EAEA,cAAkC;AAChC,UAAM,WAAW,KAAK,OAAO,IAAI,UAAU;AAC3C,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAwB;AAC5B,UAAM,WAAW,KAAK,YAAY;AAElC,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAEA,UAAM,aAAa,iBAAiB,QAAQ;AAC5C,UAAM,KAAK,MAAM,WAAW,GAAG,MAAM;AAErC,UAAM,YAAY,IAAI,iBAAiB,EAAE,QAAQ,GAAG,KAAM,IAAI,gBAAgB,GAAG,aAAc,IAAI,OAAO,GAAG,KAAM,MAAM,CAAC;AAE1H,SAAK,OAAO,OAAO,UAAU;AAE7B,cAAU,MAAM;AAAA,MACd,OAAO;AAAA,MACP,YAAY;AAAA,QACV,gBAAgB,GAAG,aAAc;AAAA,QACjC,QAAQ,GAAG,KAAM;AAAA,QACjB,OAAO,GAAG,KAAM;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,aAAa,KAAsC;AACvD,QAAI,WAAW,KAAK,YAAY;AAGhC,QAAI,CAAC,UAAU;AACb,UAAI;AACF,cAAM,EAAC,YAAW,IAAI,MAAM,SAAS,OAAO;AAAA,UAC1C;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAED,YAAI,aAAa;AACf,gBAAM,cAAc,MAAM,KAAK,MAAM;AACrC,qBAAW,YAAY;AACvB,cAAI,IAAI,0BAAmB,MAAM,IAAI,SAAS,EAAE,YAAY,KAAK,KAAK,CAAC;AAAA,CAAI;AAC3E,iBAAO;AAAA,QACT,OAAO;AACL,cAAI,MAAM,qCAAqC;AAAA,QACjD;AAAA,MACF,SAAS,OAAO;AACd,YAAI,iBAAiB,SAAS,MAAM,SAAS,mBAAmB;AAC9D,cAAI,MAAM,MAAM,OAAO,4BAA4B,CAAC;AAAA,QACtD;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI;AAEJ,UAAM,aAAa,iBAAiB,QAAQ;AAC5C,QAAI;AACF,WAAK,MAAM,WAAW,GAAG,MAAM;AAAA,IACjC,SAAS,OAAO;AACd,UAAI,IAAI,MAAM,IAAI,8DAA8D,CAAC;AACjF,cAAQ,KAAK,CAAC;AAAA,IAChB;AAEA,QAAI,CAAC,GAAG,gBAAgB,CAAC,GAAG,MAAM;AAChC,UAAI,MAAM,qCAAqC;AAAA,IACjD;AAEA,WAAO,EAAC,UAAU,MAAM,GAAG,MAAM,cAAc,GAAG,aAAY;AAAA,EAChE;AAAA,EAEA,MAAM,QAAgC;AACpC,QAAI;AACJ,gBAAY,IAAI,iBAAiB;AAEjC,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,IAAI,QAAQ,WAAW,EAAC,UAAU,KAAI,CAAC,CAAC;AAC5C,QAAI,IAAI,QAAQ,KAAK,CAAC;AAEtB,UAAM,OAAO,MAAM,QAAQ;AAC3B,UAAM,QAAQC,QAAO,YAAY,EAAE,EAAE,SAAS,KAAK;AAEnD,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAM,SAAS,IAAI,OAAO,MAAM,MAAM;AAAA,MAAC,CAAC;AAExC,gBAAU,MAAM;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC;AAAA,MACf,CAAC;AAED,YAAM,UAAU,IAAI,qDAAqD,EAAE,MAAM;AAEjF,UAAI,KAAK,aAAa,OAAO,KAAK,QAAQ;AACxC,cAAM,EAAC,UAAU,MAAM,aAAY,IAAI,IAAI;AAE3C,oBAAY,IAAI,iBAAiB,EAAE,QAAQ,KAAK,IAAI,gBAAgB,aAAa,IAAI,OAAO,KAAK,MAAM,CAAC;AACxG,kBAAU,MAAM;AAAA,UACd,OAAO;AAAA,UACP,YAAY;AAAA,YACV,gBAAgB,aAAa;AAAA,YAC7B,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,UACd;AAAA,QACF,CAAC;AAED,YAAI,UAAU,IAAI,MAAM,OAAO;AAC7B,cAAI,OAAO,GAAG,EAAE,KAAK,EAAC,SAAS,gBAAe,CAAC;AAC/C,kBAAQ,KAAK,eAAe;AAC5B;AAAA,QACF;AAEA,aAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,YAAI,OAAO,GAAG,EAAE,KAAK,EAAC,SAAS,kBAAiB,CAAC;AACjD,gBAAQ,QAAQ,uCAAgC,MAAM,IAAI,SAAS,EAAE,KAAK,KAAK,CAAC;AAAA,CAAI;AACpF,eAAO,MAAM;AACb,gBAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,WAAK,GAAG,KAAK,sBAAsB,0CAA0C,IAAI,mBAAmB,KAAK,EAAE;AAAA,IAC7G,CAAC;AAAA,EACH;AACF;;;AGtKA,SAAS,eAAe;AACxB,OAAO,UAAU,0BAA0B;;;ACApC,IAAM,cAAc;;;ADG3B,OAAOC,YAAW;AAEX,IAAM,cAAN,cAA0B,QAAQ;AAAA,EACvC,MAAM,OAAO;AACX,UAAM,KAAK,gBAAgB;AAE3B,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C;AAAA,IACF;AAEA,WAAO,KAAK;AAAA,MACV,KAAK,QAAQ,IAAI,cAAc;AAAA,MAC/B,cAAc;AAAA,QACZ,mBAAmB;AAAA,MACrB;AAAA;AAAA,MAEA,kBAAkB;AAAA;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,MAAM,KAAU;AACpB,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C,YAAM,MAAM,GAAG;AACf;AAAA,IACF;AAEA,WAAO,iBAAiB,GAAG;AAC3B,UAAM,MAAM,GAAG;AAAA,EACjB;AAAA,EAEA,MAAM,UAAU;AACd,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C;AAAA,IACF;AAEA,WAAO,MAAM;AAAA,EACf;AAAA,EAEA,MAAM,MAAM;AAAA,EAAC;AAAA,EAEb,MAAM,kBAAkB;AACtB,UAAM,WAAW,MAAM,MAAM,GAAG,sBAAsB,cAAc;AACpE,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAM,eAAe,KAAK;AAE1B,QAAI,iBAAiB,aAAa;AAChC;AAAA,IACF;AAEA,SAAK,IAAIA,OAAM,OAAO,6DAA6D,CAAC;AACpF,SAAK,IAAI,8CAA8C,WAAW;AAAA,CAAM;AACxE,SAAK,IAAI,mDAAmD;AAC5D,SAAK,IAAI,GAAGA,OAAM,KAAKA,OAAM,UAAUA,OAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,CAA4C;AACvG,SAAK,IAAI,GAAGA,OAAM,KAAKA,OAAM,UAAUA,OAAM,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,CAA6C;AACzG,SAAK,IAAI,GAAGA,OAAM,KAAKA,OAAM,UAAUA,OAAM,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,CAA6C;AAEzG,YAAQ,KAAK,CAAC;AAAA,EAChB;AACF;;;AEzDA,IAAqB,aAArB,MAAqB,oBAAmB,YAAY;AAAA,EAKlD,YAAY,MAAgB,QAAwB,cAAc,IAAI,YAAY,GAAG;AACnF,UAAM,MAAM,MAAM;AADgC;AAAA,EAEpD;AAAA,EANA,OAAgB,cAAc;AAAA,EAE9B,OAAgB,WAAW,CAAC,yBAAyB;AAAA,EAMrD,MAAa,MAAqB;AAChC,UAAM,KAAK,MAAM,WAAU;AAE3B,QAAI;AACF,YAAM,KAAK,YAAY,MAAM;AAAA,IAC/B,SAAS,OAAgB;AACvB,WAAK,MAAO,MAAgB,OAAO;AAAA,IACrC;AAAA,EACF;AACF;", "names": ["Conf", "crypto", "anonymousId", "Conf", "crypto", "chalk"]}