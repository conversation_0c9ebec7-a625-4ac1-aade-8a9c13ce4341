import { Message } from '@copilotkit/runtime-client-gql';
import { SystemMessageFunction } from '../types/system-message.js';
import { AppendMessageOptions } from './use-chat.js';
import 'react';
import '@copilotkit/shared';
import '../copilot-context-8fb74a85.js';
import '../types/frontend-action.js';
import './use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';

interface UseCopilotChatOptions {
    /**
     * A unique identifier for the chat. If not provided, a random one will be
     * generated. When provided, the `useChat` hook with the same `id` will
     * have shared states across components.
     */
    id?: string;
    /**
     * HTTP headers to be sent with the API request.
     */
    headers?: Record<string, string> | Headers;
    /**
     * System messages of the chat. Defaults to an empty array.
     */
    initialMessages?: Message[];
    /**
     * A function to generate the system message. Defaults to `defaultSystemMessage`.
     */
    makeSystemMessage?: SystemMessageFunction;
}
interface MCPServerConfig {
    endpoint: string;
    apiKey?: string;
}
interface UseCopilotChatReturn {
    visibleMessages: Message[];
    appendMessage: (message: Message, options?: AppendMessageOptions) => Promise<void>;
    setMessages: (messages: Message[]) => void;
    deleteMessage: (messageId: string) => void;
    reloadMessages: (messageId: string) => Promise<void>;
    stopGeneration: () => void;
    reset: () => void;
    isLoading: boolean;
    runChatCompletion: () => Promise<Message[]>;
    mcpServers: MCPServerConfig[];
    setMcpServers: (mcpServers: MCPServerConfig[]) => void;
}
declare function useCopilotChat({ makeSystemMessage, ...options }?: UseCopilotChatOptions): UseCopilotChatReturn;
declare function defaultSystemMessage(contextString: string, additionalInstructions?: string): string;

export { MCPServerConfig, UseCopilotChatOptions, UseCopilotChatReturn, defaultSystemMessage, useCopilotChat };
