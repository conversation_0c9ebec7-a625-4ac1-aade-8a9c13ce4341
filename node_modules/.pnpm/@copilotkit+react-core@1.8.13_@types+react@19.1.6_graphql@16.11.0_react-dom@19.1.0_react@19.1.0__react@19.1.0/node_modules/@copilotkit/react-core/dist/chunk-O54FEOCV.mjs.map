{"version": 3, "sources": ["../src/utils/extract.ts", "../src/components/copilot-provider/copilotkit.tsx"], "sourcesContent": ["import {\n  Action,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  MappedParameterTypes,\n  Parameter,\n  actionParametersToJsonSchema,\n} from \"@copilotkit/shared\";\nimport {\n  ActionExecutionMessage,\n  Message,\n  Role,\n  TextMessage,\n  convertGqlOutputToMessages,\n  CopilotRequestType,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { CopilotContextParams, CopilotMessagesContextParams } from \"../context\";\nimport { defaultCopilotContextCategories } from \"../components\";\nimport { CopilotRuntimeClient } from \"@copilotkit/runtime-client-gql\";\nimport {\n  convertMessagesToGqlInput,\n  filterAgentStateMessages,\n} from \"@copilotkit/runtime-client-gql\";\n\ninterface InitialState<T extends Parameter[] | [] = []> {\n  status: \"initial\";\n  args: Partial<MappedParameterTypes<T>>;\n}\n\ninterface InProgressState<T extends Parameter[] | [] = []> {\n  status: \"inProgress\";\n  args: Partial<MappedParameterTypes<T>>;\n}\n\ninterface CompleteState<T extends Parameter[] | [] = []> {\n  status: \"complete\";\n  args: MappedParameterTypes<T>;\n}\n\ntype StreamHandlerArgs<T extends Parameter[] | [] = []> =\n  | InitialState<T>\n  | InProgressState<T>\n  | CompleteState<T>;\n\ninterface ExtractOptions<T extends Parameter[]> {\n  context: CopilotContextParams & CopilotMessagesContextParams;\n  instructions: string;\n  parameters: T;\n  include?: IncludeOptions;\n  data?: any;\n  abortSignal?: AbortSignal;\n  stream?: (args: StreamHandlerArgs<T>) => void;\n  requestType?: CopilotRequestType;\n  forwardedParameters?: ForwardedParametersInput;\n}\n\ninterface IncludeOptions {\n  readable?: boolean;\n  messages?: boolean;\n}\n\nexport async function extract<const T extends Parameter[]>({\n  context,\n  instructions,\n  parameters,\n  include,\n  data,\n  abortSignal,\n  stream,\n  requestType = CopilotRequestType.Task,\n  forwardedParameters,\n}: ExtractOptions<T>): Promise<MappedParameterTypes<T>> {\n  const { messages } = context;\n\n  const action: Action<any> = {\n    name: \"extract\",\n    description: instructions,\n    parameters,\n    handler: (args: any) => {},\n  };\n\n  const includeReadable = include?.readable ?? false;\n  const includeMessages = include?.messages ?? false;\n\n  let contextString = \"\";\n\n  if (data) {\n    contextString = (typeof data === \"string\" ? data : JSON.stringify(data)) + \"\\n\\n\";\n  }\n\n  if (includeReadable) {\n    contextString += context.getContextString([], defaultCopilotContextCategories);\n  }\n\n  const systemMessage: Message = new TextMessage({\n    content: makeSystemMessage(contextString, instructions),\n    role: Role.System,\n  });\n\n  const instructionsMessage: Message = new TextMessage({\n    content: makeInstructionsMessage(instructions),\n    role: Role.User,\n  });\n\n  const response = context.runtimeClient.asStream(\n    context.runtimeClient.generateCopilotResponse({\n      data: {\n        frontend: {\n          actions: [\n            {\n              name: action.name,\n              description: action.description || \"\",\n              jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters || [])),\n            },\n          ],\n          url: window.location.href,\n        },\n\n        messages: convertMessagesToGqlInput(\n          includeMessages\n            ? [systemMessage, instructionsMessage, ...filterAgentStateMessages(messages)]\n            : [systemMessage, instructionsMessage],\n        ),\n        metadata: {\n          requestType: requestType,\n        },\n        forwardedParameters: {\n          ...(forwardedParameters ?? {}),\n          toolChoice: \"function\",\n          toolChoiceFunctionName: action.name,\n        },\n      },\n      properties: context.copilotApiConfig.properties,\n      signal: abortSignal,\n    }),\n  );\n\n  const reader = response.getReader();\n\n  let isInitial = true;\n\n  let actionExecutionMessage: ActionExecutionMessage | undefined = undefined;\n\n  while (true) {\n    const { done, value } = await reader.read();\n\n    if (done) {\n      break;\n    }\n\n    if (abortSignal?.aborted) {\n      throw new Error(\"Aborted\");\n    }\n\n    actionExecutionMessage = convertGqlOutputToMessages(\n      value.generateCopilotResponse.messages,\n    ).find((msg) => msg.isActionExecutionMessage()) as ActionExecutionMessage | undefined;\n\n    if (!actionExecutionMessage) {\n      continue;\n    }\n\n    stream?.({\n      status: isInitial ? \"initial\" : \"inProgress\",\n      args: actionExecutionMessage.arguments as Partial<MappedParameterTypes<T>>,\n    });\n\n    isInitial = false;\n  }\n\n  if (!actionExecutionMessage) {\n    throw new Error(\"extract() failed: No function call occurred\");\n  }\n\n  stream?.({\n    status: \"complete\",\n    args: actionExecutionMessage.arguments as MappedParameterTypes<T>,\n  });\n\n  return actionExecutionMessage.arguments as MappedParameterTypes<T>;\n}\n\n// We need to put this in a user message since some LLMs need\n// at least one user message to function\nfunction makeInstructionsMessage(instructions: string): string {\n  return `\nThe user has given you the following task to complete:\n\n\\`\\`\\`\n${instructions}\n\\`\\`\\`\n\nAny additional messages provided are for providing context only and should not be used to ask questions or engage in conversation.\n`;\n}\n\nfunction makeSystemMessage(contextString: string, instructions: string): string {\n  return `\nPlease act as an efficient, competent, conscientious, and industrious professional assistant.\n\nHelp the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.\nAlways be polite and respectful, and prefer brevity over verbosity.\n\nThe user has provided you with the following context:\n\\`\\`\\`\n${contextString}\n\\`\\`\\`\n\nThey have also provided you with a function called extract you MUST call to initiate actions on their behalf.\n\nPlease assist them as best you can.\n\nThis is not a conversation, so please do not ask questions. Just call the function without saying anything else.\n`;\n}\n", "/**\n * This component will typically wrap your entire application (or a sub-tree of your application where you want to have a copilot). It provides the copilot context to all other components and hooks.\n *\n * ## Example\n *\n * You can find more information about self-hosting CopilotKit [here](/guides/self-hosting).\n *\n * ```tsx\n * import { CopilotKit } from \"@copilotkit/react-core\";\n *\n * <CopilotKit runtimeUrl=\"<your-runtime-url>\">\n *   // ... your app ...\n * </CopilotKit>\n * ```\n */\n\nimport { useCallback, useEffect, useMemo, useRef, useState, SetStateAction } from \"react\";\nimport {\n  CopilotContext,\n  CopilotApiConfig,\n  ChatComponentsCache,\n  AgentSession,\n  AuthState,\n} from \"../../context/copilot-context\";\nimport useTree from \"../../hooks/use-tree\";\nimport { CopilotChatSuggestionConfiguration, DocumentPointer } from \"../../types\";\nimport { flushSync } from \"react-dom\";\nimport {\n  COPILOT_CLOUD_CHAT_URL,\n  CopilotCloudConfig,\n  FunctionCallHandler,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  randomUUID,\n  ConfigurationError,\n  MissingPublicApiKeyError,\n} from \"@copilotkit/shared\";\nimport { FrontendAction } from \"../../types/frontend-action\";\nimport useFlatCategoryStore from \"../../hooks/use-flat-category-store\";\nimport { CopilotKitProps } from \"./copilotkit-props\";\nimport { CoAgentStateRender } from \"../../types/coagent-action\";\nimport { CoagentState } from \"../../types/coagent-state\";\nimport { CopilotMessages } from \"./copilot-messages\";\nimport { ToastProvider } from \"../toast/toast-provider\";\nimport { useCopilotRuntimeClient } from \"../../hooks/use-copilot-runtime-client\";\nimport { shouldShowDevConsole } from \"../../utils\";\nimport { CopilotErrorBoundary } from \"../error-boundary/error-boundary\";\nimport { Agent, ExtensionsInput } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetterArgs,\n} from \"../../types/interrupt-action\";\n\nexport function CopilotKit({ children, ...props }: CopilotKitProps) {\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n  const enabled = shouldShowDevConsole(showDevConsole);\n\n  return (\n    <ToastProvider enabled={enabled}>\n      <CopilotErrorBoundary publicApiKey={props.publicApiKey} showUsageBanner={enabled}>\n        <CopilotKitInternal {...props}>{children}</CopilotKitInternal>\n      </CopilotErrorBoundary>\n    </ToastProvider>\n  );\n}\n\nexport function CopilotKitInternal(cpkProps: CopilotKitProps) {\n  const { children, ...props } = cpkProps;\n\n  /**\n   * This will throw an error if the props are invalid.\n   */\n  validateProps(cpkProps);\n\n  const chatApiEndpoint = props.runtimeUrl || COPILOT_CLOUD_CHAT_URL;\n\n  const [actions, setActions] = useState<Record<string, FrontendAction<any>>>({});\n  const [coAgentStateRenders, setCoAgentStateRenders] = useState<\n    Record<string, CoAgentStateRender<any>>\n  >({});\n\n  const chatComponentsCache = useRef<ChatComponentsCache>({\n    actions: {},\n    coAgentStateRenders: {},\n  });\n\n  const { addElement, removeElement, printTree } = useTree();\n  const [isLoading, setIsLoading] = useState(false);\n  const [chatInstructions, setChatInstructions] = useState(\"\");\n  const [authStates, setAuthStates] = useState<Record<string, AuthState>>({});\n  const [extensions, setExtensions] = useState<ExtensionsInput>({});\n  const [additionalInstructions, setAdditionalInstructions] = useState<string[]>([]);\n\n  const {\n    addElement: addDocument,\n    removeElement: removeDocument,\n    allElements: allDocuments,\n  } = useFlatCategoryStore<DocumentPointer>();\n\n  // Compute all the functions and properties that we need to pass\n\n  const setAction = useCallback((id: string, action: FrontendAction<any>) => {\n    setActions((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: action,\n      };\n    });\n  }, []);\n\n  const removeAction = useCallback((id: string) => {\n    setActions((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const setCoAgentStateRender = useCallback((id: string, stateRender: CoAgentStateRender<any>) => {\n    setCoAgentStateRenders((prevPoints) => {\n      return {\n        ...prevPoints,\n        [id]: stateRender,\n      };\n    });\n  }, []);\n\n  const removeCoAgentStateRender = useCallback((id: string) => {\n    setCoAgentStateRenders((prevPoints) => {\n      const newPoints = { ...prevPoints };\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n\n  const getContextString = useCallback(\n    (documents: DocumentPointer[], categories: string[]) => {\n      const documentsString = documents\n        .map((document) => {\n          return `${document.name} (${document.sourceApplication}):\\n${document.getContents()}`;\n        })\n        .join(\"\\n\\n\");\n\n      const nonDocumentStrings = printTree(categories);\n\n      return `${documentsString}\\n\\n${nonDocumentStrings}`;\n    },\n    [printTree],\n  );\n\n  const addContext = useCallback(\n    (\n      context: string,\n      parentId?: string,\n      categories: string[] = defaultCopilotContextCategories,\n    ) => {\n      return addElement(context, categories, parentId);\n    },\n    [addElement],\n  );\n\n  const removeContext = useCallback(\n    (id: string) => {\n      removeElement(id);\n    },\n    [removeElement],\n  );\n\n  const getFunctionCallHandler = useCallback(\n    (customEntryPoints?: Record<string, FrontendAction<any>>) => {\n      return entryPointsToFunctionCallHandler(Object.values(customEntryPoints || actions));\n    },\n    [actions],\n  );\n\n  const getDocumentsContext = useCallback(\n    (categories: string[]) => {\n      return allDocuments(categories);\n    },\n    [allDocuments],\n  );\n\n  const addDocumentContext = useCallback(\n    (documentPointer: DocumentPointer, categories: string[] = defaultCopilotContextCategories) => {\n      return addDocument(documentPointer, categories);\n    },\n    [addDocument],\n  );\n\n  const removeDocumentContext = useCallback(\n    (documentId: string) => {\n      removeDocument(documentId);\n    },\n    [removeDocument],\n  );\n\n  // get the appropriate CopilotApiConfig from the props\n  const copilotApiConfig: CopilotApiConfig = useMemo(() => {\n    let cloud: CopilotCloudConfig | undefined = undefined;\n    if (props.publicApiKey) {\n      cloud = {\n        guardrails: {\n          input: {\n            restrictToTopic: {\n              enabled: Boolean(props.guardrails_c),\n              validTopics: props.guardrails_c?.validTopics || [],\n              invalidTopics: props.guardrails_c?.invalidTopics || [],\n            },\n          },\n        },\n      };\n    }\n\n    return {\n      publicApiKey: props.publicApiKey,\n      ...(cloud ? { cloud } : {}),\n      chatApiEndpoint: chatApiEndpoint,\n      headers: props.headers || {},\n      properties: props.properties || {},\n      transcribeAudioUrl: props.transcribeAudioUrl,\n      textToSpeechUrl: props.textToSpeechUrl,\n      credentials: props.credentials,\n    };\n  }, [\n    props.publicApiKey,\n    props.headers,\n    props.properties,\n    props.transcribeAudioUrl,\n    props.textToSpeechUrl,\n    props.credentials,\n    props.cloudRestrictToTopic,\n    props.guardrails_c,\n  ]);\n\n  const headers = useMemo(() => {\n    const authHeaders = Object.values(authStates || {}).reduce((acc, state) => {\n      if (state.status === \"authenticated\" && state.authHeaders) {\n        return {\n          ...acc,\n          ...Object.entries(state.authHeaders).reduce(\n            (headers, [key, value]) => ({\n              ...headers,\n              [key.startsWith(\"X-Custom-\") ? key : `X-Custom-${key}`]: value,\n            }),\n            {},\n          ),\n        };\n      }\n      return acc;\n    }, {});\n\n    return {\n      ...(copilotApiConfig.headers || {}),\n      ...(copilotApiConfig.publicApiKey\n        ? { [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: copilotApiConfig.publicApiKey }\n        : {}),\n      ...authHeaders,\n    };\n  }, [copilotApiConfig.headers, copilotApiConfig.publicApiKey, authStates]);\n\n  const runtimeClient = useCopilotRuntimeClient({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    headers,\n    credentials: copilotApiConfig.credentials,\n  });\n\n  const [chatSuggestionConfiguration, setChatSuggestionConfiguration] = useState<{\n    [key: string]: CopilotChatSuggestionConfiguration;\n  }>({});\n\n  const addChatSuggestionConfiguration = (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => {\n    setChatSuggestionConfiguration((prev) => ({ ...prev, [id]: suggestion }));\n  };\n\n  const removeChatSuggestionConfiguration = (id: string) => {\n    setChatSuggestionConfiguration((prev) => {\n      const { [id]: _, ...rest } = prev;\n      return rest;\n    });\n  };\n\n  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);\n  const [coagentStates, setCoagentStates] = useState<Record<string, CoagentState>>({});\n  const coagentStatesRef = useRef<Record<string, CoagentState>>({});\n  const setCoagentStatesWithRef = useCallback(\n    (\n      value:\n        | Record<string, CoagentState>\n        | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n    ) => {\n      const newValue = typeof value === \"function\" ? value(coagentStatesRef.current) : value;\n      coagentStatesRef.current = newValue;\n      setCoagentStates((prev) => {\n        return newValue;\n      });\n    },\n    [],\n  );\n  const hasLoadedAgents = useRef(false);\n\n  useEffect(() => {\n    if (hasLoadedAgents.current) return;\n\n    const fetchData = async () => {\n      const result = await runtimeClient.availableAgents();\n      if (result.data?.availableAgents) {\n        setAvailableAgents(result.data.availableAgents.agents);\n      }\n      hasLoadedAgents.current = true;\n    };\n    void fetchData();\n  }, []);\n\n  let initialAgentSession: AgentSession | null = null;\n  if (props.agent) {\n    initialAgentSession = {\n      agentName: props.agent,\n    };\n  }\n\n  const [agentSession, setAgentSession] = useState<AgentSession | null>(initialAgentSession);\n\n  // Update agentSession when props.agent changes\n  useEffect(() => {\n    if (props.agent) {\n      setAgentSession({\n        agentName: props.agent,\n      });\n    } else {\n      setAgentSession(null);\n    }\n  }, [props.agent]);\n\n  const [internalThreadId, setInternalThreadId] = useState<string>(props.threadId || randomUUID());\n  const setThreadId = useCallback(\n    (value: SetStateAction<string>) => {\n      if (props.threadId) {\n        throw new Error(\"Cannot call setThreadId() when threadId is provided via props.\");\n      }\n      setInternalThreadId(value);\n    },\n    [props.threadId],\n  );\n\n  // update the internal threadId if the props.threadId changes\n  useEffect(() => {\n    if (props.threadId !== undefined) {\n      setInternalThreadId(props.threadId);\n    }\n  }, [props.threadId]);\n\n  const [runId, setRunId] = useState<string | null>(null);\n\n  const chatAbortControllerRef = useRef<AbortController | null>(null);\n\n  const showDevConsole = props.showDevConsole === undefined ? \"auto\" : props.showDevConsole;\n\n  const [langGraphInterruptAction, _setLangGraphInterruptAction] =\n    useState<LangGraphInterruptAction | null>(null);\n  const setLangGraphInterruptAction = useCallback((action: LangGraphInterruptActionSetterArgs) => {\n    _setLangGraphInterruptAction((prev) => {\n      if (prev == null) return action as LangGraphInterruptAction;\n      if (action == null) return null;\n      let event = prev.event;\n      if (action.event) {\n        // @ts-ignore\n        event = { ...prev.event, ...action.event };\n      }\n      return { ...prev, ...action, event };\n    });\n  }, []);\n  const removeLangGraphInterruptAction = useCallback((): void => {\n    setLangGraphInterruptAction(null);\n  }, []);\n\n  return (\n    <CopilotContext.Provider\n      value={{\n        actions,\n        chatComponentsCache,\n        getFunctionCallHandler,\n        setAction,\n        removeAction,\n        coAgentStateRenders,\n        setCoAgentStateRender,\n        removeCoAgentStateRender,\n        getContextString,\n        addContext,\n        removeContext,\n        getDocumentsContext,\n        addDocumentContext,\n        removeDocumentContext,\n        copilotApiConfig: copilotApiConfig,\n        isLoading,\n        setIsLoading,\n        chatSuggestionConfiguration,\n        addChatSuggestionConfiguration,\n        removeChatSuggestionConfiguration,\n        chatInstructions,\n        setChatInstructions,\n        additionalInstructions,\n        setAdditionalInstructions,\n        showDevConsole,\n        coagentStates,\n        setCoagentStates,\n        coagentStatesRef,\n        setCoagentStatesWithRef,\n        agentSession,\n        setAgentSession,\n        runtimeClient,\n        forwardedParameters: props.forwardedParameters || {},\n        agentLock: props.agent || null,\n        threadId: internalThreadId,\n        setThreadId,\n        runId,\n        setRunId,\n        chatAbortControllerRef,\n        availableAgents,\n        authConfig_c: props.authConfig_c,\n        authStates_c: authStates,\n        setAuthStates_c: setAuthStates,\n        extensions,\n        setExtensions,\n        langGraphInterruptAction,\n        setLangGraphInterruptAction,\n        removeLangGraphInterruptAction,\n      }}\n    >\n      <CopilotMessages>{children}</CopilotMessages>\n    </CopilotContext.Provider>\n  );\n}\n\nexport const defaultCopilotContextCategories = [\"global\"];\n\nfunction entryPointsToFunctionCallHandler(actions: FrontendAction<any>[]): FunctionCallHandler {\n  return async ({ name, args }) => {\n    let actionsByFunctionName: Record<string, FrontendAction<any>> = {};\n    for (let action of actions) {\n      actionsByFunctionName[action.name] = action;\n    }\n\n    const action = actionsByFunctionName[name];\n    let result: any = undefined;\n    if (action) {\n      await new Promise<void>((resolve, reject) => {\n        flushSync(async () => {\n          try {\n            result = await action.handler?.(args);\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n      });\n      await new Promise((resolve) => setTimeout(resolve, 20));\n    }\n    return result;\n  };\n}\n\nfunction formatFeatureName(featureName: string): string {\n  return featureName\n    .replace(/_c$/, \"\")\n    .split(\"_\")\n    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n    .join(\" \");\n}\n\nfunction validateProps(props: CopilotKitProps): never | void {\n  const cloudFeatures = Object.keys(props).filter((key) => key.endsWith(\"_c\"));\n\n  if (!props.runtimeUrl && !props.publicApiKey) {\n    throw new ConfigurationError(\"Missing required prop: 'runtimeUrl' or 'publicApiKey'\");\n  }\n\n  if (cloudFeatures.length > 0 && !props.publicApiKey) {\n    throw new MissingPublicApiKeyError(\n      `Missing required prop: 'publicApiKey' to use cloud features: ${cloudFeatures\n        .map(formatFeatureName)\n        .join(\", \")}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,EAKE;AAAA,OACK;AACP;AAAA,EAGE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAEK;;;ACCP,SAAS,aAAa,WAAW,SAAS,QAAQ,gBAAgC;AAUlF,SAAS,iBAAiB;AAC1B;AAAA,EACE;AAAA,EAGA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAwBC;AAPD,SAAS,WAAW,IAAyC;AAAzC,eAAE,WApD7B,IAoD2B,IAAe,kBAAf,IAAe,CAAb;AAC3B,QAAM,iBAAiB,MAAM,mBAAmB,SAAY,SAAS,MAAM;AAC3E,QAAM,UAAU,qBAAqB,cAAc;AAEnD,SACE,oBAAC,iBAAc,SACb,8BAAC,wBAAqB,cAAc,MAAM,cAAc,iBAAiB,SACvE,8BAAC,qDAAuB,QAAvB,EAA+B,WAAS,GAC3C,GACF;AAEJ;AAEO,SAAS,mBAAmB,UAA2B;AAC5D,QAA+B,eAAvB,WAlEV,IAkEiC,IAAV,kBAAU,IAAV,CAAb;AAKR,gBAAc,QAAQ;AAEtB,QAAM,kBAAkB,MAAM,cAAc;AAE5C,QAAM,CAAC,SAAS,UAAU,IAAI,SAA8C,CAAC,CAAC;AAC9E,QAAM,CAAC,qBAAqB,sBAAsB,IAAI,SAEpD,CAAC,CAAC;AAEJ,QAAM,sBAAsB,OAA4B;AAAA,IACtD,SAAS,CAAC;AAAA,IACV,qBAAqB,CAAC;AAAA,EACxB,CAAC;AAED,QAAM,EAAE,YAAY,eAAe,UAAU,IAAI,iBAAQ;AACzD,QAAM,CAAC,WAAW,YAAY,IAAI,SAAS,KAAK;AAChD,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,SAAS,EAAE;AAC3D,QAAM,CAAC,YAAY,aAAa,IAAI,SAAoC,CAAC,CAAC;AAC1E,QAAM,CAAC,YAAY,aAAa,IAAI,SAA0B,CAAC,CAAC;AAChE,QAAM,CAAC,wBAAwB,yBAAyB,IAAI,SAAmB,CAAC,CAAC;AAEjF,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,aAAa;AAAA,EACf,IAAI,gCAAsC;AAI1C,QAAM,YAAY,YAAY,CAAC,IAAY,WAAgC;AACzE,eAAW,CAAC,eAAe;AACzB,aAAO,iCACF,aADE;AAAA,QAEL,CAAC,EAAE,GAAG;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,eAAe,YAAY,CAAC,OAAe;AAC/C,eAAW,CAAC,eAAe;AACzB,YAAM,YAAY,mBAAK;AACvB,aAAO,UAAU,EAAE;AACnB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,wBAAwB,YAAY,CAAC,IAAY,gBAAyC;AAC9F,2BAAuB,CAAC,eAAe;AACrC,aAAO,iCACF,aADE;AAAA,QAEL,CAAC,EAAE,GAAG;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,2BAA2B,YAAY,CAAC,OAAe;AAC3D,2BAAuB,CAAC,eAAe;AACrC,YAAM,YAAY,mBAAK;AACvB,aAAO,UAAU,EAAE;AACnB,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,mBAAmB;AAAA,IACvB,CAAC,WAA8B,eAAyB;AACtD,YAAM,kBAAkB,UACrB,IAAI,CAAC,aAAa;AACjB,eAAO,GAAG,SAAS,SAAS,SAAS;AAAA,EAAwB,SAAS,YAAY;AAAA,MACpF,CAAC,EACA,KAAK,MAAM;AAEd,YAAM,qBAAqB,UAAU,UAAU;AAE/C,aAAO,GAAG;AAAA;AAAA,EAAsB;AAAA,IAClC;AAAA,IACA,CAAC,SAAS;AAAA,EACZ;AAEA,QAAM,aAAa;AAAA,IACjB,CACE,SACA,UACA,aAAuB,oCACpB;AACH,aAAO,WAAW,SAAS,YAAY,QAAQ;AAAA,IACjD;AAAA,IACA,CAAC,UAAU;AAAA,EACb;AAEA,QAAM,gBAAgB;AAAA,IACpB,CAAC,OAAe;AACd,oBAAc,EAAE;AAAA,IAClB;AAAA,IACA,CAAC,aAAa;AAAA,EAChB;AAEA,QAAM,yBAAyB;AAAA,IAC7B,CAAC,sBAA4D;AAC3D,aAAO,iCAAiC,OAAO,OAAO,qBAAqB,OAAO,CAAC;AAAA,IACrF;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAEA,QAAM,sBAAsB;AAAA,IAC1B,CAAC,eAAyB;AACxB,aAAO,aAAa,UAAU;AAAA,IAChC;AAAA,IACA,CAAC,YAAY;AAAA,EACf;AAEA,QAAM,qBAAqB;AAAA,IACzB,CAAC,iBAAkC,aAAuB,oCAAoC;AAC5F,aAAO,YAAY,iBAAiB,UAAU;AAAA,IAChD;AAAA,IACA,CAAC,WAAW;AAAA,EACd;AAEA,QAAM,wBAAwB;AAAA,IAC5B,CAAC,eAAuB;AACtB,qBAAe,UAAU;AAAA,IAC3B;AAAA,IACA,CAAC,cAAc;AAAA,EACjB;AAGA,QAAM,mBAAqC,QAAQ,MAAM;AApM3D,QAAAA,KAAA;AAqMI,QAAI,QAAwC;AAC5C,QAAI,MAAM,cAAc;AACtB,cAAQ;AAAA,QACN,YAAY;AAAA,UACV,OAAO;AAAA,YACL,iBAAiB;AAAA,cACf,SAAS,QAAQ,MAAM,YAAY;AAAA,cACnC,eAAaA,MAAA,MAAM,iBAAN,gBAAAA,IAAoB,gBAAe,CAAC;AAAA,cACjD,iBAAe,WAAM,iBAAN,mBAAoB,kBAAiB,CAAC;AAAA,YACvD;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,cAAc,MAAM;AAAA,OAChB,QAAQ,EAAE,MAAM,IAAI,CAAC,IAFpB;AAAA,MAGL;AAAA,MACA,SAAS,MAAM,WAAW,CAAC;AAAA,MAC3B,YAAY,MAAM,cAAc,CAAC;AAAA,MACjC,oBAAoB,MAAM;AAAA,MAC1B,iBAAiB,MAAM;AAAA,MACvB,aAAa,MAAM;AAAA,IACrB;AAAA,EACF,GAAG;AAAA,IACD,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR,CAAC;AAED,QAAM,UAAU,QAAQ,MAAM;AAC5B,UAAM,cAAc,OAAO,OAAO,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,UAAU;AACzE,UAAI,MAAM,WAAW,mBAAmB,MAAM,aAAa;AACzD,eAAO,kCACF,MACA,OAAO,QAAQ,MAAM,WAAW,EAAE;AAAA,UACnC,CAACC,UAAS,CAAC,KAAK,KAAK,MAAO,iCACvBA,WADuB;AAAA,YAE1B,CAAC,IAAI,WAAW,WAAW,IAAI,MAAM,YAAY,KAAK,GAAG;AAAA,UAC3D;AAAA,UACA,CAAC;AAAA,QACH;AAAA,MAEJ;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAEL,WAAO,iDACD,iBAAiB,WAAW,CAAC,IAC7B,iBAAiB,eACjB,EAAE,CAAC,mCAAmC,GAAG,iBAAiB,aAAa,IACvE,CAAC,IACF;AAAA,EAEP,GAAG,CAAC,iBAAiB,SAAS,iBAAiB,cAAc,UAAU,CAAC;AAExE,QAAM,gBAAgB,wBAAwB;AAAA,IAC5C,KAAK,iBAAiB;AAAA,IACtB,cAAc,iBAAiB;AAAA,IAC/B;AAAA,IACA,aAAa,iBAAiB;AAAA,EAChC,CAAC;AAED,QAAM,CAAC,6BAA6B,8BAA8B,IAAI,SAEnE,CAAC,CAAC;AAEL,QAAM,iCAAiC,CACrC,IACA,eACG;AACH,mCAA+B,CAAC,SAAU,iCAAK,OAAL,EAAW,CAAC,EAAE,GAAG,WAAW,EAAE;AAAA,EAC1E;AAEA,QAAM,oCAAoC,CAAC,OAAe;AACxD,mCAA+B,CAAC,SAAS;AACvC,YAA6BD,MAAA,MAApB,EAvRf,CAuRe,KAAK,EAvRpB,IAuRmCA,KAAT,iBAASA,KAAT,CAAX;AACT,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAEA,QAAM,CAAC,iBAAiB,kBAAkB,IAAI,SAAkB,CAAC,CAAC;AAClE,QAAM,CAAC,eAAe,gBAAgB,IAAI,SAAuC,CAAC,CAAC;AACnF,QAAM,mBAAmB,OAAqC,CAAC,CAAC;AAChE,QAAM,0BAA0B;AAAA,IAC9B,CACE,UAGG;AACH,YAAM,WAAW,OAAO,UAAU,aAAa,MAAM,iBAAiB,OAAO,IAAI;AACjF,uBAAiB,UAAU;AAC3B,uBAAiB,CAAC,SAAS;AACzB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,CAAC;AAAA,EACH;AACA,QAAM,kBAAkB,OAAO,KAAK;AAEpC,YAAU,MAAM;AACd,QAAI,gBAAgB;AAAS;AAE7B,UAAM,YAAY,MAAY;AAlTlC,UAAAA;AAmTM,YAAM,SAAS,MAAM,cAAc,gBAAgB;AACnD,WAAIA,MAAA,OAAO,SAAP,gBAAAA,IAAa,iBAAiB;AAChC,2BAAmB,OAAO,KAAK,gBAAgB,MAAM;AAAA,MACvD;AACA,sBAAgB,UAAU;AAAA,IAC5B;AACA,SAAK,UAAU;AAAA,EACjB,GAAG,CAAC,CAAC;AAEL,MAAI,sBAA2C;AAC/C,MAAI,MAAM,OAAO;AACf,0BAAsB;AAAA,MACpB,WAAW,MAAM;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,CAAC,cAAc,eAAe,IAAI,SAA8B,mBAAmB;AAGzF,YAAU,MAAM;AACd,QAAI,MAAM,OAAO;AACf,sBAAgB;AAAA,QACd,WAAW,MAAM;AAAA,MACnB,CAAC;AAAA,IACH,OAAO;AACL,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,MAAM,KAAK,CAAC;AAEhB,QAAM,CAAC,kBAAkB,mBAAmB,IAAI,SAAiB,MAAM,YAAY,WAAW,CAAC;AAC/F,QAAM,cAAc;AAAA,IAClB,CAAC,UAAkC;AACjC,UAAI,MAAM,UAAU;AAClB,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AACA,0BAAoB,KAAK;AAAA,IAC3B;AAAA,IACA,CAAC,MAAM,QAAQ;AAAA,EACjB;AAGA,YAAU,MAAM;AACd,QAAI,MAAM,aAAa,QAAW;AAChC,0BAAoB,MAAM,QAAQ;AAAA,IACpC;AAAA,EACF,GAAG,CAAC,MAAM,QAAQ,CAAC;AAEnB,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAwB,IAAI;AAEtD,QAAM,yBAAyB,OAA+B,IAAI;AAElE,QAAM,iBAAiB,MAAM,mBAAmB,SAAY,SAAS,MAAM;AAE3E,QAAM,CAAC,0BAA0B,4BAA4B,IAC3D,SAA0C,IAAI;AAChD,QAAM,8BAA8B,YAAY,CAAC,WAA+C;AAC9F,iCAA6B,CAAC,SAAS;AACrC,UAAI,QAAQ;AAAM,eAAO;AACzB,UAAI,UAAU;AAAM,eAAO;AAC3B,UAAI,QAAQ,KAAK;AACjB,UAAI,OAAO,OAAO;AAEhB,gBAAQ,kCAAK,KAAK,QAAU,OAAO;AAAA,MACrC;AACA,aAAO,gDAAK,OAAS,SAAd,EAAsB,MAAM;AAAA,IACrC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACL,QAAM,iCAAiC,YAAY,MAAY;AAC7D,gCAA4B,IAAI;AAAA,EAClC,GAAG,CAAC,CAAC;AAEL,SACE;AAAA,IAAC,eAAe;AAAA,IAAf;AAAA,MACC,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,qBAAqB,MAAM,uBAAuB,CAAC;AAAA,QACnD,WAAW,MAAM,SAAS;AAAA,QAC1B,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,cAAc,MAAM;AAAA,QACpB,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MAEA,8BAAC,mBAAiB,UAAS;AAAA;AAAA,EAC7B;AAEJ;AAEO,IAAM,kCAAkC,CAAC,QAAQ;AAExD,SAAS,iCAAiC,SAAqD;AAC7F,SAAO,CAAO,OAAmB,eAAnB,KAAmB,WAAnB,EAAE,MAAM,KAAK,GAAM;AAC/B,QAAI,wBAA6D,CAAC;AAClE,aAASE,WAAU,SAAS;AAC1B,4BAAsBA,QAAO,IAAI,IAAIA;AAAA,IACvC;AAEA,UAAM,SAAS,sBAAsB,IAAI;AACzC,QAAI,SAAc;AAClB,QAAI,QAAQ;AACV,YAAM,IAAI,QAAc,CAAC,SAAS,WAAW;AAC3C,kBAAU,MAAY;AAjc9B;AAkcU,cAAI;AACF,qBAAS,OAAM,YAAO,YAAP,gCAAiB;AAChC,oBAAQ;AAAA,UACV,SAAS,OAAP;AACA,mBAAO,KAAK;AAAA,UACd;AAAA,QACF,EAAC;AAAA,MACH,CAAC;AACD,YAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,aAA6B;AACtD,SAAO,YACJ,QAAQ,OAAO,EAAE,EACjB,MAAM,GAAG,EACT,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC,EAAE,YAAY,CAAC,EACxE,KAAK,GAAG;AACb;AAEA,SAAS,cAAc,OAAsC;AAC3D,QAAM,gBAAgB,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC;AAE3E,MAAI,CAAC,MAAM,cAAc,CAAC,MAAM,cAAc;AAC5C,UAAM,IAAI,mBAAmB,uDAAuD;AAAA,EACtF;AAEA,MAAI,cAAc,SAAS,KAAK,CAAC,MAAM,cAAc;AACnD,UAAM,IAAI;AAAA,MACR,gEAAgE,cAC7D,IAAI,iBAAiB,EACrB,KAAK,IAAI;AAAA,IACd;AAAA,EACF;AACF;;;ADndA;AAAA,EACE;AAAA,EACA;AAAA,OACK;AAuCP,SAAsB,QAAqC,IAUH;AAAA,6CAVG;AAAA,IACzD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,mBAAmB;AAAA,IACjC;AAAA,EACF,GAAwD;AAvExD;AAwEE,UAAM,EAAE,SAAS,IAAI;AAErB,UAAM,SAAsB;AAAA,MAC1B,MAAM;AAAA,MACN,aAAa;AAAA,MACb;AAAA,MACA,SAAS,CAAC,SAAc;AAAA,MAAC;AAAA,IAC3B;AAEA,UAAM,mBAAkB,wCAAS,aAAT,YAAqB;AAC7C,UAAM,mBAAkB,wCAAS,aAAT,YAAqB;AAE7C,QAAI,gBAAgB;AAEpB,QAAI,MAAM;AACR,uBAAiB,OAAO,SAAS,WAAW,OAAO,KAAK,UAAU,IAAI,KAAK;AAAA,IAC7E;AAEA,QAAI,iBAAiB;AACnB,uBAAiB,QAAQ,iBAAiB,CAAC,GAAG,+BAA+B;AAAA,IAC/E;AAEA,UAAM,gBAAyB,IAAI,YAAY;AAAA,MAC7C,SAAS,kBAAkB,eAAe,YAAY;AAAA,MACtD,MAAM,KAAK;AAAA,IACb,CAAC;AAED,UAAM,sBAA+B,IAAI,YAAY;AAAA,MACnD,SAAS,wBAAwB,YAAY;AAAA,MAC7C,MAAM,KAAK;AAAA,IACb,CAAC;AAED,UAAM,WAAW,QAAQ,cAAc;AAAA,MACrC,QAAQ,cAAc,wBAAwB;AAAA,QAC5C,MAAM;AAAA,UACJ,UAAU;AAAA,YACR,SAAS;AAAA,cACP;AAAA,gBACE,MAAM,OAAO;AAAA,gBACb,aAAa,OAAO,eAAe;AAAA,gBACnC,YAAY,KAAK,UAAU,6BAA6B,OAAO,cAAc,CAAC,CAAC,CAAC;AAAA,cAClF;AAAA,YACF;AAAA,YACA,KAAK,OAAO,SAAS;AAAA,UACvB;AAAA,UAEA,UAAU;AAAA,YACR,kBACI,CAAC,eAAe,qBAAqB,GAAG,yBAAyB,QAAQ,CAAC,IAC1E,CAAC,eAAe,mBAAmB;AAAA,UACzC;AAAA,UACA,UAAU;AAAA,YACR;AAAA,UACF;AAAA,UACA,qBAAqB,iCACf,oDAAuB,CAAC,IADT;AAAA,YAEnB,YAAY;AAAA,YACZ,wBAAwB,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,QACA,YAAY,QAAQ,iBAAiB;AAAA,QACrC,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAEA,UAAM,SAAS,SAAS,UAAU;AAElC,QAAI,YAAY;AAEhB,QAAI,yBAA6D;AAEjE,WAAO,MAAM;AACX,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAE1C,UAAI,MAAM;AACR;AAAA,MACF;AAEA,UAAI,2CAAa,SAAS;AACxB,cAAM,IAAI,MAAM,SAAS;AAAA,MAC3B;AAEA,+BAAyB;AAAA,QACvB,MAAM,wBAAwB;AAAA,MAChC,EAAE,KAAK,CAAC,QAAQ,IAAI,yBAAyB,CAAC;AAE9C,UAAI,CAAC,wBAAwB;AAC3B;AAAA,MACF;AAEA,uCAAS;AAAA,QACP,QAAQ,YAAY,YAAY;AAAA,QAChC,MAAM,uBAAuB;AAAA,MAC/B;AAEA,kBAAY;AAAA,IACd;AAEA,QAAI,CAAC,wBAAwB;AAC3B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC/D;AAEA,qCAAS;AAAA,MACP,QAAQ;AAAA,MACR,MAAM,uBAAuB;AAAA,IAC/B;AAEA,WAAO,uBAAuB;AAAA,EAChC;AAAA;AAIA,SAAS,wBAAwB,cAA8B;AAC7D,SAAO;AAAA;AAAA;AAAA;AAAA,EAIP;AAAA;AAAA;AAAA;AAAA;AAKF;AAEA,SAAS,kBAAkB,eAAuB,cAA8B;AAC9E,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASF;", "names": ["_a", "headers", "action"]}