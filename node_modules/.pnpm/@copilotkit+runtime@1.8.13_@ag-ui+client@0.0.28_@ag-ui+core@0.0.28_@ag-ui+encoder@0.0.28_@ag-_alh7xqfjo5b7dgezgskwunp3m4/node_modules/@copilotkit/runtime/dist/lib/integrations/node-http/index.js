var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from3, except, desc) => {
  if (from3 && typeof from3 === "object" || typeof from3 === "function") {
    for (let key of __getOwnPropNames(from3))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from3[key], enumerable: !(desc = __getOwnPropDesc(from3, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// package.json
var require_package = __commonJS({
  "package.json"(exports, module2) {
    module2.exports = {
      name: "@copilotkit/runtime",
      private: false,
      homepage: "https://github.com/CopilotKit/CopilotKit",
      repository: {
        type: "git",
        url: "https://github.com/CopilotKit/CopilotKit.git"
      },
      publishConfig: {
        access: "public"
      },
      version: "1.8.13",
      sideEffects: false,
      main: "./dist/index.js",
      module: "./dist/index.mjs",
      exports: {
        ".": "./dist/index.js"
      },
      types: "./dist/index.d.ts",
      license: "MIT",
      scripts: {
        build: 'tsup --onSuccess "pnpm run generate-graphql-schema"',
        dev: 'tsup --watch --onSuccess "pnpm run generate-graphql-schema"',
        test: "jest --passWithNoTests",
        "check-types": "tsc --noEmit",
        clean: "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next && rm -rf __snapshots__",
        "generate-graphql-schema": "rm -rf __snapshots__ && ts-node ./scripts/generate-gql-schema.ts",
        "link:global": "pnpm link --global",
        "unlink:global": "pnpm unlink --global"
      },
      devDependencies: {
        "@jest/globals": "^29.7.0",
        "@swc/core": "1.5.28",
        "@types/express": "^4.17.21",
        "@types/jest": "^29.5.12",
        "@types/node": "^18.11.17",
        "@whatwg-node/server": "^0.9.34",
        eslint: "^8.56.0",
        "eslint-config-custom": "workspace:*",
        jest: "^29.6.4",
        nodemon: "^3.1.3",
        "ts-jest": "^29.1.1",
        "ts-node": "^10.9.2",
        tsconfig: "workspace:*",
        tsup: "^6.7.0",
        typescript: "^5.2.3",
        "zod-to-json-schema": "^3.23.5"
      },
      dependencies: {
        "@ag-ui/client": "0.0.28",
        "@ag-ui/core": "0.0.28",
        "@ag-ui/encoder": "0.0.28",
        "@ag-ui/proto": "0.0.28",
        "@anthropic-ai/sdk": "^0.27.3",
        "@copilotkit/shared": "workspace:*",
        "@graphql-yoga/plugin-defer-stream": "^3.3.1",
        "@langchain/community": "^0.3.29",
        "@langchain/core": "^0.3.38",
        "@langchain/google-gauth": "^0.1.0",
        "@langchain/langgraph-sdk": "^0.0.70",
        "@langchain/openai": "^0.4.2",
        "class-transformer": "^0.5.1",
        "class-validator": "^0.14.1",
        express: "^4.19.2",
        graphql: "^16.8.1",
        "graphql-scalars": "^1.23.0",
        "graphql-yoga": "^5.3.1",
        "groq-sdk": "^0.5.0",
        langchain: "^0.3.3",
        openai: "^4.85.1",
        "partial-json": "^0.1.7",
        pino: "^9.2.0",
        "pino-pretty": "^11.2.1",
        "reflect-metadata": "^0.2.2",
        rxjs: "^7.8.1",
        "type-graphql": "2.0.0-rc.1",
        zod: "^3.23.3"
      },
      peerDependencies: {
        "@ag-ui/client": ">=0.0.28",
        "@ag-ui/core": ">=0.0.28",
        "@ag-ui/encoder": ">=0.0.28",
        "@ag-ui/proto": ">=0.0.28"
      },
      keywords: [
        "copilotkit",
        "copilot",
        "react",
        "nextjs",
        "nodejs",
        "ai",
        "assistant",
        "javascript",
        "automation",
        "textarea"
      ]
    };
  }
});

// src/lib/integrations/node-http/index.ts
var node_http_exports = {};
__export(node_http_exports, {
  copilotRuntimeNodeHttpEndpoint: () => copilotRuntimeNodeHttpEndpoint
});
module.exports = __toCommonJS(node_http_exports);
var import_graphql_yoga2 = require("graphql-yoga");

// src/lib/integrations/shared.ts
var import_type_graphql26 = require("type-graphql");

// src/graphql/resolvers/copilot.resolver.ts
var import_type_graphql20 = require("type-graphql");
var import_rxjs3 = require("rxjs");

// src/graphql/inputs/generate-copilot-response.input.ts
var import_type_graphql18 = require("type-graphql");

// src/graphql/inputs/message.input.ts
var import_type_graphql3 = require("type-graphql");

// src/graphql/types/enums.ts
var import_type_graphql = require("type-graphql");
var MessageRole;
(function(MessageRole2) {
  MessageRole2["user"] = "user";
  MessageRole2["assistant"] = "assistant";
  MessageRole2["system"] = "system";
  MessageRole2["tool"] = "tool";
  MessageRole2["developer"] = "developer";
})(MessageRole || (MessageRole = {}));
var CopilotRequestType;
(function(CopilotRequestType2) {
  CopilotRequestType2["Chat"] = "Chat";
  CopilotRequestType2["Task"] = "Task";
  CopilotRequestType2["TextareaCompletion"] = "TextareaCompletion";
  CopilotRequestType2["TextareaPopover"] = "TextareaPopover";
  CopilotRequestType2["Suggestion"] = "Suggestion";
})(CopilotRequestType || (CopilotRequestType = {}));
var ActionInputAvailability;
(function(ActionInputAvailability2) {
  ActionInputAvailability2["disabled"] = "disabled";
  ActionInputAvailability2["enabled"] = "enabled";
  ActionInputAvailability2["remote"] = "remote";
})(ActionInputAvailability || (ActionInputAvailability = {}));
(0, import_type_graphql.registerEnumType)(MessageRole, {
  name: "MessageRole",
  description: "The role of the message"
});
(0, import_type_graphql.registerEnumType)(CopilotRequestType, {
  name: "CopilotRequestType",
  description: "The type of Copilot request"
});
(0, import_type_graphql.registerEnumType)(ActionInputAvailability, {
  name: "ActionInputAvailability",
  description: "The availability of the frontend action"
});

// src/graphql/types/base/index.ts
var import_type_graphql2 = require("type-graphql");
function _ts_decorate(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate, "_ts_decorate");
function _ts_metadata(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata, "_ts_metadata");
var BaseMessageInput = class {
  id;
  createdAt;
};
__name(BaseMessageInput, "BaseMessageInput");
_ts_decorate([
  (0, import_type_graphql2.Field)(() => String),
  _ts_metadata("design:type", String)
], BaseMessageInput.prototype, "id", void 0);
_ts_decorate([
  (0, import_type_graphql2.Field)(() => Date),
  _ts_metadata("design:type", typeof Date === "undefined" ? Object : Date)
], BaseMessageInput.prototype, "createdAt", void 0);
BaseMessageInput = _ts_decorate([
  (0, import_type_graphql2.InputType)()
], BaseMessageInput);

// src/graphql/inputs/message.input.ts
function _ts_decorate2(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate2, "_ts_decorate");
function _ts_metadata2(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata2, "_ts_metadata");
var MessageInput = class extends BaseMessageInput {
  textMessage;
  actionExecutionMessage;
  resultMessage;
  agentStateMessage;
  imageMessage;
};
__name(MessageInput, "MessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => TextMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof TextMessageInput === "undefined" ? Object : TextMessageInput)
], MessageInput.prototype, "textMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => ActionExecutionMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof ActionExecutionMessageInput === "undefined" ? Object : ActionExecutionMessageInput)
], MessageInput.prototype, "actionExecutionMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => ResultMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof ResultMessageInput === "undefined" ? Object : ResultMessageInput)
], MessageInput.prototype, "resultMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => AgentStateMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof AgentStateMessageInput === "undefined" ? Object : AgentStateMessageInput)
], MessageInput.prototype, "agentStateMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => ImageMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof ImageMessageInput === "undefined" ? Object : ImageMessageInput)
], MessageInput.prototype, "imageMessage", void 0);
MessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], MessageInput);
var TextMessageInput = class {
  content;
  parentMessageId;
  role;
};
__name(TextMessageInput, "TextMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], TextMessageInput.prototype, "content", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], TextMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => MessageRole),
  _ts_metadata2("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], TextMessageInput.prototype, "role", void 0);
TextMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], TextMessageInput);
var ActionExecutionMessageInput = class {
  name;
  arguments;
  parentMessageId;
  scope;
};
__name(ActionExecutionMessageInput, "ActionExecutionMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ActionExecutionMessageInput.prototype, "name", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ActionExecutionMessageInput.prototype, "arguments", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], ActionExecutionMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true,
    deprecationReason: "This field will be removed in a future version"
  }),
  _ts_metadata2("design:type", typeof String === "undefined" ? Object : String)
], ActionExecutionMessageInput.prototype, "scope", void 0);
ActionExecutionMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], ActionExecutionMessageInput);
var ResultMessageInput = class {
  actionExecutionId;
  actionName;
  parentMessageId;
  result;
};
__name(ResultMessageInput, "ResultMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "actionExecutionId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "actionName", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "result", void 0);
ResultMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], ResultMessageInput);
var AgentStateMessageInput = class {
  threadId;
  agentName;
  role;
  state;
  running;
  nodeName;
  runId;
  active;
};
__name(AgentStateMessageInput, "AgentStateMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "threadId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "agentName", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => MessageRole),
  _ts_metadata2("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], AgentStateMessageInput.prototype, "role", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "state", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => Boolean),
  _ts_metadata2("design:type", Boolean)
], AgentStateMessageInput.prototype, "running", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "nodeName", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "runId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => Boolean),
  _ts_metadata2("design:type", Boolean)
], AgentStateMessageInput.prototype, "active", void 0);
AgentStateMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], AgentStateMessageInput);
var ImageMessageInput = class {
  format;
  bytes;
  parentMessageId;
  role;
};
__name(ImageMessageInput, "ImageMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ImageMessageInput.prototype, "format", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ImageMessageInput.prototype, "bytes", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], ImageMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => MessageRole),
  _ts_metadata2("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], ImageMessageInput.prototype, "role", void 0);
ImageMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], ImageMessageInput);

// src/graphql/inputs/frontend.input.ts
var import_type_graphql5 = require("type-graphql");

// src/graphql/inputs/action.input.ts
var import_type_graphql4 = require("type-graphql");
function _ts_decorate3(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate3, "_ts_decorate");
function _ts_metadata3(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata3, "_ts_metadata");
var ActionInput = class {
  name;
  description;
  jsonSchema;
  available;
};
__name(ActionInput, "ActionInput");
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => String),
  _ts_metadata3("design:type", String)
], ActionInput.prototype, "name", void 0);
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => String),
  _ts_metadata3("design:type", String)
], ActionInput.prototype, "description", void 0);
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => String),
  _ts_metadata3("design:type", String)
], ActionInput.prototype, "jsonSchema", void 0);
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => ActionInputAvailability, {
    nullable: true
  }),
  _ts_metadata3("design:type", typeof ActionInputAvailability === "undefined" ? Object : ActionInputAvailability)
], ActionInput.prototype, "available", void 0);
ActionInput = _ts_decorate3([
  (0, import_type_graphql4.InputType)()
], ActionInput);

// src/graphql/inputs/frontend.input.ts
function _ts_decorate4(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate4, "_ts_decorate");
function _ts_metadata4(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata4, "_ts_metadata");
var FrontendInput = class {
  toDeprecate_fullContext;
  actions;
  url;
};
__name(FrontendInput, "FrontendInput");
_ts_decorate4([
  (0, import_type_graphql5.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata4("design:type", String)
], FrontendInput.prototype, "toDeprecate_fullContext", void 0);
_ts_decorate4([
  (0, import_type_graphql5.Field)(() => [
    ActionInput
  ]),
  _ts_metadata4("design:type", Array)
], FrontendInput.prototype, "actions", void 0);
_ts_decorate4([
  (0, import_type_graphql5.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata4("design:type", String)
], FrontendInput.prototype, "url", void 0);
FrontendInput = _ts_decorate4([
  (0, import_type_graphql5.InputType)()
], FrontendInput);

// src/graphql/inputs/cloud.input.ts
var import_type_graphql7 = require("type-graphql");

// src/graphql/inputs/cloud-guardrails.input.ts
var import_type_graphql6 = require("type-graphql");
function _ts_decorate5(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate5, "_ts_decorate");
function _ts_metadata5(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata5, "_ts_metadata");
var GuardrailsRuleInput = class {
  allowList = [];
  denyList = [];
};
__name(GuardrailsRuleInput, "GuardrailsRuleInput");
_ts_decorate5([
  (0, import_type_graphql6.Field)(() => [
    String
  ], {
    nullable: true
  }),
  _ts_metadata5("design:type", Array)
], GuardrailsRuleInput.prototype, "allowList", void 0);
_ts_decorate5([
  (0, import_type_graphql6.Field)(() => [
    String
  ], {
    nullable: true
  }),
  _ts_metadata5("design:type", Array)
], GuardrailsRuleInput.prototype, "denyList", void 0);
GuardrailsRuleInput = _ts_decorate5([
  (0, import_type_graphql6.InputType)()
], GuardrailsRuleInput);
var GuardrailsInput = class {
  inputValidationRules;
};
__name(GuardrailsInput, "GuardrailsInput");
_ts_decorate5([
  (0, import_type_graphql6.Field)(() => GuardrailsRuleInput, {
    nullable: false
  }),
  _ts_metadata5("design:type", typeof GuardrailsRuleInput === "undefined" ? Object : GuardrailsRuleInput)
], GuardrailsInput.prototype, "inputValidationRules", void 0);
GuardrailsInput = _ts_decorate5([
  (0, import_type_graphql6.InputType)()
], GuardrailsInput);

// src/graphql/inputs/cloud.input.ts
function _ts_decorate6(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate6, "_ts_decorate");
function _ts_metadata6(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata6, "_ts_metadata");
var CloudInput = class {
  guardrails;
};
__name(CloudInput, "CloudInput");
_ts_decorate6([
  (0, import_type_graphql7.Field)(() => GuardrailsInput, {
    nullable: true
  }),
  _ts_metadata6("design:type", typeof GuardrailsInput === "undefined" ? Object : GuardrailsInput)
], CloudInput.prototype, "guardrails", void 0);
CloudInput = _ts_decorate6([
  (0, import_type_graphql7.InputType)()
], CloudInput);

// src/graphql/inputs/forwarded-parameters.input.ts
var import_type_graphql8 = require("type-graphql");
function _ts_decorate7(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate7, "_ts_decorate");
function _ts_metadata7(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata7, "_ts_metadata");
var ForwardedParametersInput = class {
  model;
  maxTokens;
  stop;
  toolChoice;
  toolChoiceFunctionName;
  temperature;
};
__name(ForwardedParametersInput, "ForwardedParametersInput");
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata7("design:type", String)
], ForwardedParametersInput.prototype, "model", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => Number, {
    nullable: true
  }),
  _ts_metadata7("design:type", Number)
], ForwardedParametersInput.prototype, "maxTokens", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => [
    String
  ], {
    nullable: true
  }),
  _ts_metadata7("design:type", Array)
], ForwardedParametersInput.prototype, "stop", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata7("design:type", typeof String === "undefined" ? Object : String)
], ForwardedParametersInput.prototype, "toolChoice", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata7("design:type", String)
], ForwardedParametersInput.prototype, "toolChoiceFunctionName", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => Number, {
    nullable: true
  }),
  _ts_metadata7("design:type", Number)
], ForwardedParametersInput.prototype, "temperature", void 0);
ForwardedParametersInput = _ts_decorate7([
  (0, import_type_graphql8.InputType)()
], ForwardedParametersInput);

// src/graphql/inputs/agent-session.input.ts
var import_type_graphql9 = require("type-graphql");
function _ts_decorate8(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate8, "_ts_decorate");
function _ts_metadata8(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata8, "_ts_metadata");
var AgentSessionInput = class {
  agentName;
  threadId;
  nodeName;
};
__name(AgentSessionInput, "AgentSessionInput");
_ts_decorate8([
  (0, import_type_graphql9.Field)(() => String),
  _ts_metadata8("design:type", String)
], AgentSessionInput.prototype, "agentName", void 0);
_ts_decorate8([
  (0, import_type_graphql9.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata8("design:type", String)
], AgentSessionInput.prototype, "threadId", void 0);
_ts_decorate8([
  (0, import_type_graphql9.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata8("design:type", String)
], AgentSessionInput.prototype, "nodeName", void 0);
AgentSessionInput = _ts_decorate8([
  (0, import_type_graphql9.InputType)()
], AgentSessionInput);

// src/graphql/inputs/agent-state.input.ts
var import_type_graphql10 = require("type-graphql");
function _ts_decorate9(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate9, "_ts_decorate");
function _ts_metadata9(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata9, "_ts_metadata");
var AgentStateInput = class {
  agentName;
  state;
  config;
};
__name(AgentStateInput, "AgentStateInput");
_ts_decorate9([
  (0, import_type_graphql10.Field)(() => String),
  _ts_metadata9("design:type", String)
], AgentStateInput.prototype, "agentName", void 0);
_ts_decorate9([
  (0, import_type_graphql10.Field)(() => String),
  _ts_metadata9("design:type", String)
], AgentStateInput.prototype, "state", void 0);
_ts_decorate9([
  (0, import_type_graphql10.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata9("design:type", String)
], AgentStateInput.prototype, "config", void 0);
AgentStateInput = _ts_decorate9([
  (0, import_type_graphql10.InputType)()
], AgentStateInput);

// src/graphql/inputs/extensions.input.ts
var import_type_graphql11 = require("type-graphql");
function _ts_decorate10(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate10, "_ts_decorate");
function _ts_metadata10(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata10, "_ts_metadata");
var ExtensionsInput = class {
  openaiAssistantAPI;
};
__name(ExtensionsInput, "ExtensionsInput");
_ts_decorate10([
  (0, import_type_graphql11.Field)(() => OpenAIApiAssistantAPIInput, {
    nullable: true
  }),
  _ts_metadata10("design:type", typeof OpenAIApiAssistantAPIInput === "undefined" ? Object : OpenAIApiAssistantAPIInput)
], ExtensionsInput.prototype, "openaiAssistantAPI", void 0);
ExtensionsInput = _ts_decorate10([
  (0, import_type_graphql11.InputType)()
], ExtensionsInput);
var OpenAIApiAssistantAPIInput = class {
  runId;
  threadId;
};
__name(OpenAIApiAssistantAPIInput, "OpenAIApiAssistantAPIInput");
_ts_decorate10([
  (0, import_type_graphql11.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata10("design:type", String)
], OpenAIApiAssistantAPIInput.prototype, "runId", void 0);
_ts_decorate10([
  (0, import_type_graphql11.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata10("design:type", String)
], OpenAIApiAssistantAPIInput.prototype, "threadId", void 0);
OpenAIApiAssistantAPIInput = _ts_decorate10([
  (0, import_type_graphql11.InputType)()
], OpenAIApiAssistantAPIInput);

// src/graphql/inputs/meta-event.input.ts
var import_type_graphql17 = require("type-graphql");

// src/graphql/types/meta-events.type.ts
var import_type_graphql16 = require("type-graphql");

// src/graphql/types/copilot-response.type.ts
var import_type_graphql15 = require("type-graphql");

// src/graphql/types/message-status.type.ts
var import_type_graphql12 = require("type-graphql");
function _ts_decorate11(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate11, "_ts_decorate");
function _ts_metadata11(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata11, "_ts_metadata");
var MessageStatusCode;
(function(MessageStatusCode2) {
  MessageStatusCode2["Pending"] = "pending";
  MessageStatusCode2["Success"] = "success";
  MessageStatusCode2["Failed"] = "failed";
})(MessageStatusCode || (MessageStatusCode = {}));
(0, import_type_graphql12.registerEnumType)(MessageStatusCode, {
  name: "MessageStatusCode"
});
var BaseMessageStatus = /* @__PURE__ */ __name(class BaseMessageStatus2 {
  code;
}, "BaseMessageStatus");
_ts_decorate11([
  (0, import_type_graphql12.Field)(() => MessageStatusCode),
  _ts_metadata11("design:type", String)
], BaseMessageStatus.prototype, "code", void 0);
BaseMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], BaseMessageStatus);
var PendingMessageStatus = class extends BaseMessageStatus {
  code = "pending";
};
__name(PendingMessageStatus, "PendingMessageStatus");
PendingMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], PendingMessageStatus);
var SuccessMessageStatus = class extends BaseMessageStatus {
  code = "success";
};
__name(SuccessMessageStatus, "SuccessMessageStatus");
SuccessMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], SuccessMessageStatus);
var FailedMessageStatus = class extends BaseMessageStatus {
  code = "failed";
  reason;
};
__name(FailedMessageStatus, "FailedMessageStatus");
_ts_decorate11([
  (0, import_type_graphql12.Field)(() => String),
  _ts_metadata11("design:type", String)
], FailedMessageStatus.prototype, "reason", void 0);
FailedMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], FailedMessageStatus);
var MessageStatusUnion = (0, import_type_graphql12.createUnionType)({
  name: "MessageStatus",
  types: () => [
    PendingMessageStatus,
    SuccessMessageStatus,
    FailedMessageStatus
  ]
});

// src/graphql/types/response-status.type.ts
var import_graphql_scalars = require("graphql-scalars");
var import_type_graphql13 = require("type-graphql");
function _ts_decorate12(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate12, "_ts_decorate");
function _ts_metadata12(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata12, "_ts_metadata");
var ResponseStatusCode;
(function(ResponseStatusCode2) {
  ResponseStatusCode2["Pending"] = "pending";
  ResponseStatusCode2["Success"] = "success";
  ResponseStatusCode2["Failed"] = "failed";
})(ResponseStatusCode || (ResponseStatusCode = {}));
(0, import_type_graphql13.registerEnumType)(ResponseStatusCode, {
  name: "ResponseStatusCode"
});
var BaseResponseStatus = /* @__PURE__ */ __name(class BaseResponseStatus2 {
  code;
}, "BaseResponseStatus");
_ts_decorate12([
  (0, import_type_graphql13.Field)(() => ResponseStatusCode),
  _ts_metadata12("design:type", String)
], BaseResponseStatus.prototype, "code", void 0);
BaseResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.InterfaceType)({
    resolveType(value) {
      if (value.code === "success") {
        return SuccessResponseStatus;
      } else if (value.code === "failed") {
        return FailedResponseStatus;
      } else if (value.code === "pending") {
        return PendingResponseStatus;
      }
      return void 0;
    }
  }),
  (0, import_type_graphql13.ObjectType)()
], BaseResponseStatus);
var PendingResponseStatus = class extends BaseResponseStatus {
  code = "pending";
};
__name(PendingResponseStatus, "PendingResponseStatus");
PendingResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.ObjectType)({
    implements: BaseResponseStatus
  })
], PendingResponseStatus);
var SuccessResponseStatus = class extends BaseResponseStatus {
  code = "success";
};
__name(SuccessResponseStatus, "SuccessResponseStatus");
SuccessResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.ObjectType)({
    implements: BaseResponseStatus
  })
], SuccessResponseStatus);
var FailedResponseStatusReason;
(function(FailedResponseStatusReason2) {
  FailedResponseStatusReason2["GUARDRAILS_VALIDATION_FAILED"] = "GUARDRAILS_VALIDATION_FAILED";
  FailedResponseStatusReason2["MESSAGE_STREAM_INTERRUPTED"] = "MESSAGE_STREAM_INTERRUPTED";
  FailedResponseStatusReason2["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(FailedResponseStatusReason || (FailedResponseStatusReason = {}));
(0, import_type_graphql13.registerEnumType)(FailedResponseStatusReason, {
  name: "FailedResponseStatusReason"
});
var FailedResponseStatus = class extends BaseResponseStatus {
  code = "failed";
  reason;
  details = null;
};
__name(FailedResponseStatus, "FailedResponseStatus");
_ts_decorate12([
  (0, import_type_graphql13.Field)(() => FailedResponseStatusReason),
  _ts_metadata12("design:type", String)
], FailedResponseStatus.prototype, "reason", void 0);
_ts_decorate12([
  (0, import_type_graphql13.Field)(() => import_graphql_scalars.GraphQLJSON, {
    nullable: true
  }),
  _ts_metadata12("design:type", typeof Record === "undefined" ? Object : Record)
], FailedResponseStatus.prototype, "details", void 0);
FailedResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.ObjectType)({
    implements: BaseResponseStatus
  })
], FailedResponseStatus);
var ResponseStatusUnion = (0, import_type_graphql13.createUnionType)({
  name: "ResponseStatus",
  types: () => [
    PendingResponseStatus,
    SuccessResponseStatus,
    FailedResponseStatus
  ]
});

// src/graphql/types/extensions-response.type.ts
var import_type_graphql14 = require("type-graphql");
function _ts_decorate13(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate13, "_ts_decorate");
function _ts_metadata13(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata13, "_ts_metadata");
var ExtensionsResponse = class {
  openaiAssistantAPI;
};
__name(ExtensionsResponse, "ExtensionsResponse");
_ts_decorate13([
  (0, import_type_graphql14.Field)(() => OpenAIApiAssistantAPIResponse, {
    nullable: true
  }),
  _ts_metadata13("design:type", typeof OpenAIApiAssistantAPIResponse === "undefined" ? Object : OpenAIApiAssistantAPIResponse)
], ExtensionsResponse.prototype, "openaiAssistantAPI", void 0);
ExtensionsResponse = _ts_decorate13([
  (0, import_type_graphql14.ObjectType)()
], ExtensionsResponse);
var OpenAIApiAssistantAPIResponse = class {
  runId;
  threadId;
};
__name(OpenAIApiAssistantAPIResponse, "OpenAIApiAssistantAPIResponse");
_ts_decorate13([
  (0, import_type_graphql14.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata13("design:type", String)
], OpenAIApiAssistantAPIResponse.prototype, "runId", void 0);
_ts_decorate13([
  (0, import_type_graphql14.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata13("design:type", String)
], OpenAIApiAssistantAPIResponse.prototype, "threadId", void 0);
OpenAIApiAssistantAPIResponse = _ts_decorate13([
  (0, import_type_graphql14.ObjectType)()
], OpenAIApiAssistantAPIResponse);

// src/graphql/types/copilot-response.type.ts
function _ts_decorate14(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate14, "_ts_decorate");
function _ts_metadata14(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata14, "_ts_metadata");
var BaseMessageOutput = class {
  id;
  createdAt;
  status;
};
__name(BaseMessageOutput, "BaseMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], BaseMessageOutput.prototype, "id", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => Date),
  _ts_metadata14("design:type", typeof Date === "undefined" ? Object : Date)
], BaseMessageOutput.prototype, "createdAt", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageStatusUnion),
  _ts_metadata14("design:type", Object)
], BaseMessageOutput.prototype, "status", void 0);
BaseMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.InterfaceType)({
    resolveType(value) {
      if (value.hasOwnProperty("content")) {
        return TextMessageOutput;
      } else if (value.hasOwnProperty("name")) {
        return ActionExecutionMessageOutput;
      } else if (value.hasOwnProperty("result")) {
        return ResultMessageOutput;
      } else if (value.hasOwnProperty("state")) {
        return AgentStateMessageOutput;
      } else if (value.hasOwnProperty("format") && value.hasOwnProperty("bytes")) {
        return ImageMessageOutput;
      }
      return void 0;
    }
  })
], BaseMessageOutput);
var TextMessageOutput = class {
  role;
  content;
  parentMessageId;
};
__name(TextMessageOutput, "TextMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageRole),
  _ts_metadata14("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], TextMessageOutput.prototype, "role", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    String
  ]),
  _ts_metadata14("design:type", Array)
], TextMessageOutput.prototype, "content", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], TextMessageOutput.prototype, "parentMessageId", void 0);
TextMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], TextMessageOutput);
var ActionExecutionMessageOutput = class {
  name;
  scope;
  arguments;
  parentMessageId;
};
__name(ActionExecutionMessageOutput, "ActionExecutionMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ActionExecutionMessageOutput.prototype, "name", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true,
    deprecationReason: "This field will be removed in a future version"
  }),
  _ts_metadata14("design:type", String)
], ActionExecutionMessageOutput.prototype, "scope", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    String
  ]),
  _ts_metadata14("design:type", Array)
], ActionExecutionMessageOutput.prototype, "arguments", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], ActionExecutionMessageOutput.prototype, "parentMessageId", void 0);
ActionExecutionMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], ActionExecutionMessageOutput);
var ResultMessageOutput = class {
  actionExecutionId;
  actionName;
  result;
};
__name(ResultMessageOutput, "ResultMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ResultMessageOutput.prototype, "actionExecutionId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ResultMessageOutput.prototype, "actionName", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ResultMessageOutput.prototype, "result", void 0);
ResultMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], ResultMessageOutput);
var AgentStateMessageOutput = class {
  threadId;
  agentName;
  nodeName;
  runId;
  active;
  role;
  state;
  running;
};
__name(AgentStateMessageOutput, "AgentStateMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "threadId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "agentName", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "nodeName", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "runId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => Boolean),
  _ts_metadata14("design:type", Boolean)
], AgentStateMessageOutput.prototype, "active", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageRole),
  _ts_metadata14("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], AgentStateMessageOutput.prototype, "role", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "state", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => Boolean),
  _ts_metadata14("design:type", Boolean)
], AgentStateMessageOutput.prototype, "running", void 0);
AgentStateMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], AgentStateMessageOutput);
var ImageMessageOutput = class {
  format;
  bytes;
  role;
  parentMessageId;
};
__name(ImageMessageOutput, "ImageMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ImageMessageOutput.prototype, "format", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ImageMessageOutput.prototype, "bytes", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageRole),
  _ts_metadata14("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], ImageMessageOutput.prototype, "role", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], ImageMessageOutput.prototype, "parentMessageId", void 0);
ImageMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], ImageMessageOutput);
var CopilotResponse = class {
  threadId;
  status;
  runId;
  messages;
  extensions;
  metaEvents;
};
__name(CopilotResponse, "CopilotResponse");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], CopilotResponse.prototype, "threadId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => ResponseStatusUnion),
  _ts_metadata14("design:type", Object)
], CopilotResponse.prototype, "status", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)({
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], CopilotResponse.prototype, "runId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    BaseMessageOutput
  ]),
  _ts_metadata14("design:type", Array)
], CopilotResponse.prototype, "messages", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => ExtensionsResponse, {
    nullable: true
  }),
  _ts_metadata14("design:type", typeof ExtensionsResponse === "undefined" ? Object : ExtensionsResponse)
], CopilotResponse.prototype, "extensions", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    BaseMetaEvent
  ], {
    nullable: true
  }),
  _ts_metadata14("design:type", Array)
], CopilotResponse.prototype, "metaEvents", void 0);
CopilotResponse = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)()
], CopilotResponse);

// src/graphql/types/meta-events.type.ts
function _ts_decorate15(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate15, "_ts_decorate");
function _ts_metadata15(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata15, "_ts_metadata");
var MetaEventName;
(function(MetaEventName2) {
  MetaEventName2["LangGraphInterruptEvent"] = "LangGraphInterruptEvent";
  MetaEventName2["CopilotKitLangGraphInterruptEvent"] = "CopilotKitLangGraphInterruptEvent";
})(MetaEventName || (MetaEventName = {}));
(0, import_type_graphql16.registerEnumType)(MetaEventName, {
  name: "MetaEventName",
  description: "Meta event types"
});
var BaseMetaEvent = class {
  type = "MetaEvent";
  name;
};
__name(BaseMetaEvent, "BaseMetaEvent");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String),
  _ts_metadata15("design:type", String)
], BaseMetaEvent.prototype, "type", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => MetaEventName),
  _ts_metadata15("design:type", String)
], BaseMetaEvent.prototype, "name", void 0);
BaseMetaEvent = _ts_decorate15([
  (0, import_type_graphql16.InterfaceType)({
    resolveType(value) {
      if (value.name === "LangGraphInterruptEvent") {
        return LangGraphInterruptEvent;
      } else if (value.name === "CopilotKitLangGraphInterruptEvent") {
        return CopilotKitLangGraphInterruptEvent;
      }
      return void 0;
    }
  }),
  (0, import_type_graphql16.InterfaceType)()
], BaseMetaEvent);
var CopilotKitLangGraphInterruptEventData = class {
  value;
  messages;
};
__name(CopilotKitLangGraphInterruptEventData, "CopilotKitLangGraphInterruptEventData");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String),
  _ts_metadata15("design:type", String)
], CopilotKitLangGraphInterruptEventData.prototype, "value", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => [
    BaseMessageOutput
  ]),
  _ts_metadata15("design:type", Array)
], CopilotKitLangGraphInterruptEventData.prototype, "messages", void 0);
CopilotKitLangGraphInterruptEventData = _ts_decorate15([
  (0, import_type_graphql16.ObjectType)()
], CopilotKitLangGraphInterruptEventData);
var LangGraphInterruptEvent = class {
  name = "LangGraphInterruptEvent";
  value;
  response;
};
__name(LangGraphInterruptEvent, "LangGraphInterruptEvent");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => MetaEventName),
  _ts_metadata15("design:type", typeof MetaEventName === "undefined" || false ? Object : "LangGraphInterruptEvent")
], LangGraphInterruptEvent.prototype, "name", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String),
  _ts_metadata15("design:type", String)
], LangGraphInterruptEvent.prototype, "value", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata15("design:type", String)
], LangGraphInterruptEvent.prototype, "response", void 0);
LangGraphInterruptEvent = _ts_decorate15([
  (0, import_type_graphql16.ObjectType)({
    implements: BaseMetaEvent
  })
], LangGraphInterruptEvent);
var CopilotKitLangGraphInterruptEvent = class {
  name = "CopilotKitLangGraphInterruptEvent";
  data;
  response;
};
__name(CopilotKitLangGraphInterruptEvent, "CopilotKitLangGraphInterruptEvent");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => MetaEventName),
  _ts_metadata15("design:type", typeof MetaEventName === "undefined" || false ? Object : "CopilotKitLangGraphInterruptEvent")
], CopilotKitLangGraphInterruptEvent.prototype, "name", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => CopilotKitLangGraphInterruptEventData),
  _ts_metadata15("design:type", typeof CopilotKitLangGraphInterruptEventData === "undefined" ? Object : CopilotKitLangGraphInterruptEventData)
], CopilotKitLangGraphInterruptEvent.prototype, "data", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata15("design:type", String)
], CopilotKitLangGraphInterruptEvent.prototype, "response", void 0);
CopilotKitLangGraphInterruptEvent = _ts_decorate15([
  (0, import_type_graphql16.ObjectType)({
    implements: BaseMetaEvent
  })
], CopilotKitLangGraphInterruptEvent);

// src/graphql/inputs/meta-event.input.ts
function _ts_decorate16(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate16, "_ts_decorate");
function _ts_metadata16(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata16, "_ts_metadata");
var MetaEventInput = class {
  name;
  value;
  response;
  messages;
};
__name(MetaEventInput, "MetaEventInput");
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => MetaEventName),
  _ts_metadata16("design:type", typeof MetaEventName === "undefined" ? Object : MetaEventName)
], MetaEventInput.prototype, "name", void 0);
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => String),
  _ts_metadata16("design:type", String)
], MetaEventInput.prototype, "value", void 0);
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata16("design:type", String)
], MetaEventInput.prototype, "response", void 0);
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => [
    MessageInput
  ], {
    nullable: true
  }),
  _ts_metadata16("design:type", Array)
], MetaEventInput.prototype, "messages", void 0);
MetaEventInput = _ts_decorate16([
  (0, import_type_graphql17.InputType)()
], MetaEventInput);

// src/graphql/inputs/generate-copilot-response.input.ts
function _ts_decorate17(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate17, "_ts_decorate");
function _ts_metadata17(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata17, "_ts_metadata");
var GenerateCopilotResponseMetadataInput = class {
  requestType;
};
__name(GenerateCopilotResponseMetadataInput, "GenerateCopilotResponseMetadataInput");
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => CopilotRequestType, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof CopilotRequestType === "undefined" ? Object : CopilotRequestType)
], GenerateCopilotResponseMetadataInput.prototype, "requestType", void 0);
GenerateCopilotResponseMetadataInput = _ts_decorate17([
  (0, import_type_graphql18.InputType)()
], GenerateCopilotResponseMetadataInput);
var GenerateCopilotResponseInput = class {
  metadata;
  threadId;
  runId;
  messages;
  frontend;
  cloud;
  forwardedParameters;
  agentSession;
  agentState;
  agentStates;
  extensions;
  metaEvents;
};
__name(GenerateCopilotResponseInput, "GenerateCopilotResponseInput");
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => GenerateCopilotResponseMetadataInput, {
    nullable: false
  }),
  _ts_metadata17("design:type", typeof GenerateCopilotResponseMetadataInput === "undefined" ? Object : GenerateCopilotResponseMetadataInput)
], GenerateCopilotResponseInput.prototype, "metadata", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata17("design:type", String)
], GenerateCopilotResponseInput.prototype, "threadId", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata17("design:type", String)
], GenerateCopilotResponseInput.prototype, "runId", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => [
    MessageInput
  ]),
  _ts_metadata17("design:type", Array)
], GenerateCopilotResponseInput.prototype, "messages", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => FrontendInput),
  _ts_metadata17("design:type", typeof FrontendInput === "undefined" ? Object : FrontendInput)
], GenerateCopilotResponseInput.prototype, "frontend", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => CloudInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof CloudInput === "undefined" ? Object : CloudInput)
], GenerateCopilotResponseInput.prototype, "cloud", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => ForwardedParametersInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof ForwardedParametersInput === "undefined" ? Object : ForwardedParametersInput)
], GenerateCopilotResponseInput.prototype, "forwardedParameters", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => AgentSessionInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof AgentSessionInput === "undefined" ? Object : AgentSessionInput)
], GenerateCopilotResponseInput.prototype, "agentSession", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => AgentStateInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof AgentStateInput === "undefined" ? Object : AgentStateInput)
], GenerateCopilotResponseInput.prototype, "agentState", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => [
    AgentStateInput
  ], {
    nullable: true
  }),
  _ts_metadata17("design:type", Array)
], GenerateCopilotResponseInput.prototype, "agentStates", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => ExtensionsInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof ExtensionsInput === "undefined" ? Object : ExtensionsInput)
], GenerateCopilotResponseInput.prototype, "extensions", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => [
    MetaEventInput
  ], {
    nullable: true
  }),
  _ts_metadata17("design:type", Array)
], GenerateCopilotResponseInput.prototype, "metaEvents", void 0);
GenerateCopilotResponseInput = _ts_decorate17([
  (0, import_type_graphql18.InputType)()
], GenerateCopilotResponseInput);

// src/graphql/resolvers/copilot.resolver.ts
var import_graphql_yoga = require("graphql-yoga");

// src/service-adapters/events.ts
var import_shared5 = require("@copilotkit/shared");
var import_rxjs2 = require("rxjs");

// src/lib/telemetry-client.ts
var import_shared4 = require("@copilotkit/shared");

// src/lib/runtime/remote-actions.ts
var import_shared = require("@copilotkit/shared");
var import_shared2 = require("@copilotkit/shared");
var EndpointType;
(function(EndpointType2) {
  EndpointType2["CopilotKit"] = "copilotKit";
  EndpointType2["LangGraphPlatform"] = "langgraph-platform";
})(EndpointType || (EndpointType = {}));

// src/lib/telemetry-client.ts
var import_node_crypto = require("crypto");

// src/lib/runtime/copilot-runtime.ts
var import_shared3 = require("@copilotkit/shared");

// src/graphql/types/converted/index.ts
var Message = class extends BaseMessageInput {
  type;
  isTextMessage() {
    return this.type === "TextMessage";
  }
  isActionExecutionMessage() {
    return this.type === "ActionExecutionMessage";
  }
  isResultMessage() {
    return this.type === "ResultMessage";
  }
  isAgentStateMessage() {
    return this.type === "AgentStateMessage";
  }
  isImageMessage() {
    return this.type === "ImageMessage";
  }
};
__name(Message, "Message");
var TextMessage = class extends Message {
  type = "TextMessage";
  content;
  role;
  parentMessageId;
};
__name(TextMessage, "TextMessage");
var ActionExecutionMessage = class extends Message {
  type = "ActionExecutionMessage";
  name;
  arguments;
  parentMessageId;
};
__name(ActionExecutionMessage, "ActionExecutionMessage");
var ResultMessage = class extends Message {
  type = "ResultMessage";
  actionExecutionId;
  actionName;
  result;
  static encodeResult(result, error) {
    const errorObj = error ? typeof error === "string" ? {
      code: "ERROR",
      message: error
    } : error instanceof Error ? {
      code: "ERROR",
      message: error.message
    } : error : void 0;
    if (errorObj) {
      return JSON.stringify({
        error: errorObj,
        result: result || ""
      });
    }
    if (result === void 0) {
      return "";
    }
    return typeof result === "string" ? result : JSON.stringify(result);
  }
  static decodeResult(result) {
    if (!result) {
      return {
        result: ""
      };
    }
    try {
      const parsed = JSON.parse(result);
      if (parsed && typeof parsed === "object") {
        if ("error" in parsed) {
          return {
            error: parsed.error,
            result: parsed.result || ""
          };
        }
        return {
          result: JSON.stringify(parsed)
        };
      }
      return {
        result
      };
    } catch (e) {
      return {
        result
      };
    }
  }
  hasError() {
    try {
      const { error } = ResultMessage.decodeResult(this.result);
      return !!error;
    } catch {
      return false;
    }
  }
  getError() {
    try {
      const { error } = ResultMessage.decodeResult(this.result);
      return error;
    } catch {
      return void 0;
    }
  }
};
__name(ResultMessage, "ResultMessage");
var AgentStateMessage = class extends Message {
  type = "AgentStateMessage";
  threadId;
  agentName;
  nodeName;
  runId;
  active;
  role;
  state;
  running;
};
__name(AgentStateMessage, "AgentStateMessage");

// src/lib/runtime/copilot-runtime.ts
var import_rxjs = require("rxjs");
var import_langgraph_sdk = require("@langchain/langgraph-sdk");
function resolveEndpointType(endpoint) {
  if (!endpoint.type) {
    if ("deploymentUrl" in endpoint && "agents" in endpoint) {
      return EndpointType.LangGraphPlatform;
    } else {
      return EndpointType.CopilotKit;
    }
  }
  return endpoint.type;
}
__name(resolveEndpointType, "resolveEndpointType");

// src/lib/telemetry-client.ts
var packageJson = require_package();
var telemetryClient = new import_shared4.TelemetryClient({
  packageName: packageJson.name,
  packageVersion: packageJson.version
});
function getRuntimeInstanceTelemetryInfo(runtime) {
  const endpointsInfo = runtime.remoteEndpointDefinitions.reduce((acc, endpoint) => {
    let info = {
      ...acc
    };
    const endpointType = resolveEndpointType(endpoint);
    if (!info.endpointTypes.includes(endpointType)) {
      info = {
        ...info,
        endpointTypes: [
          ...info.endpointTypes,
          endpointType
        ]
      };
    }
    if (endpointType === EndpointType.LangGraphPlatform) {
      const ep = endpoint;
      info = {
        ...info,
        agentsAmount: ep.agents.length,
        hashedKey: ep.langsmithApiKey ? (0, import_node_crypto.createHash)("sha256").update(ep.langsmithApiKey).digest("hex") : null
      };
    }
    return info;
  }, {
    endpointTypes: [],
    agentsAmount: null,
    hashedKey: null
  });
  return {
    actionsAmount: runtime.actions.length,
    endpointsAmount: runtime.remoteEndpointDefinitions.length,
    endpointTypes: endpointsInfo.endpointTypes,
    agentsAmount: endpointsInfo.agentsAmount,
    hashedLgcKey: endpointsInfo.hashedKey
  };
}
__name(getRuntimeInstanceTelemetryInfo, "getRuntimeInstanceTelemetryInfo");
var telemetry_client_default = telemetryClient;

// src/service-adapters/events.ts
var import_class_transformer = require("class-transformer");
var RuntimeEventTypes;
(function(RuntimeEventTypes2) {
  RuntimeEventTypes2["TextMessageStart"] = "TextMessageStart";
  RuntimeEventTypes2["TextMessageContent"] = "TextMessageContent";
  RuntimeEventTypes2["TextMessageEnd"] = "TextMessageEnd";
  RuntimeEventTypes2["ActionExecutionStart"] = "ActionExecutionStart";
  RuntimeEventTypes2["ActionExecutionArgs"] = "ActionExecutionArgs";
  RuntimeEventTypes2["ActionExecutionEnd"] = "ActionExecutionEnd";
  RuntimeEventTypes2["ActionExecutionResult"] = "ActionExecutionResult";
  RuntimeEventTypes2["AgentStateMessage"] = "AgentStateMessage";
  RuntimeEventTypes2["MetaEvent"] = "MetaEvent";
})(RuntimeEventTypes || (RuntimeEventTypes = {}));
var RuntimeMetaEventName;
(function(RuntimeMetaEventName2) {
  RuntimeMetaEventName2["LangGraphInterruptEvent"] = "LangGraphInterruptEvent";
  RuntimeMetaEventName2["LangGraphInterruptResumeEvent"] = "LangGraphInterruptResumeEvent";
  RuntimeMetaEventName2["CopilotKitLangGraphInterruptEvent"] = "CopilotKitLangGraphInterruptEvent";
})(RuntimeMetaEventName || (RuntimeMetaEventName = {}));

// src/graphql/resolvers/copilot.resolver.ts
var import_graphql_scalars2 = require("graphql-scalars");
var import_class_transformer2 = require("class-transformer");
var import_graphql = require("graphql");

// src/utils/failed-response-status-reasons.ts
var GuardrailsValidationFailureResponse = class extends FailedResponseStatus {
  reason = FailedResponseStatusReason.GUARDRAILS_VALIDATION_FAILED;
  constructor({ guardrailsReason }) {
    super();
    this.details = {
      guardrailsReason
    };
  }
};
__name(GuardrailsValidationFailureResponse, "GuardrailsValidationFailureResponse");
var MessageStreamInterruptedResponse = class extends FailedResponseStatus {
  reason = FailedResponseStatusReason.MESSAGE_STREAM_INTERRUPTED;
  constructor({ messageId }) {
    super();
    this.details = {
      messageId,
      description: "Check the message for mode details"
    };
  }
};
__name(MessageStreamInterruptedResponse, "MessageStreamInterruptedResponse");
var UnknownErrorResponse = class extends FailedResponseStatus {
  reason = FailedResponseStatusReason.UNKNOWN_ERROR;
  constructor({ description }) {
    super();
    this.details = {
      description
    };
  }
};
__name(UnknownErrorResponse, "UnknownErrorResponse");

// src/graphql/resolvers/copilot.resolver.ts
var import_shared6 = require("@copilotkit/shared");

// src/graphql/types/agents-response.type.ts
var import_type_graphql19 = require("type-graphql");
function _ts_decorate18(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate18, "_ts_decorate");
function _ts_metadata18(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata18, "_ts_metadata");
var Agent = class {
  id;
  name;
  description;
};
__name(Agent, "Agent");
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => String),
  _ts_metadata18("design:type", String)
], Agent.prototype, "id", void 0);
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => String),
  _ts_metadata18("design:type", String)
], Agent.prototype, "name", void 0);
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => String),
  _ts_metadata18("design:type", String)
], Agent.prototype, "description", void 0);
Agent = _ts_decorate18([
  (0, import_type_graphql19.ObjectType)()
], Agent);
var AgentsResponse = class {
  agents;
};
__name(AgentsResponse, "AgentsResponse");
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => [
    Agent
  ]),
  _ts_metadata18("design:type", Array)
], AgentsResponse.prototype, "agents", void 0);
AgentsResponse = _ts_decorate18([
  (0, import_type_graphql19.ObjectType)()
], AgentsResponse);

// src/graphql/resolvers/copilot.resolver.ts
function _ts_decorate19(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate19, "_ts_decorate");
function _ts_metadata19(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata19, "_ts_metadata");
function _ts_param(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
}
__name(_ts_param, "_ts_param");
var invokeGuardrails = /* @__PURE__ */ __name(async ({ baseUrl, copilotCloudPublicApiKey, data, onResult, onError }) => {
  var _a;
  if (data.messages.length && ((_a = data.messages[data.messages.length - 1].textMessage) == null ? void 0 : _a.role) === MessageRole.user) {
    const messages = data.messages.filter((m) => m.textMessage !== void 0 && (m.textMessage.role === MessageRole.user || m.textMessage.role === MessageRole.assistant)).map((m) => ({
      role: m.textMessage.role,
      content: m.textMessage.content
    }));
    const lastMessage = messages[messages.length - 1];
    const restOfMessages = messages.slice(0, -1);
    const body = {
      input: lastMessage.content,
      validTopics: data.cloud.guardrails.inputValidationRules.allowList,
      invalidTopics: data.cloud.guardrails.inputValidationRules.denyList,
      messages: restOfMessages
    };
    const guardrailsResult = await fetch(`${baseUrl}/guardrails/validate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CopilotCloud-Public-API-Key": copilotCloudPublicApiKey
      },
      body: JSON.stringify(body)
    });
    if (guardrailsResult.ok) {
      const resultJson = await guardrailsResult.json();
      onResult(resultJson);
    } else {
      onError(await guardrailsResult.json());
    }
  }
}, "invokeGuardrails");
var CopilotResolver = class {
  async hello() {
    return "Hello World";
  }
  async availableAgents(ctx) {
    let logger2 = ctx.logger.child({
      component: "CopilotResolver.availableAgents"
    });
    logger2.debug("Processing");
    const agentsWithEndpoints = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);
    logger2.debug("Event source created, creating response");
    return {
      agents: agentsWithEndpoints.map(({ endpoint, ...agentWithoutEndpoint }) => agentWithoutEndpoint)
    };
  }
  async generateCopilotResponse(ctx, data, properties) {
    var _a, _b, _c, _d;
    telemetry_client_default.capture("oss.runtime.copilot_request_created", {
      "cloud.guardrails.enabled": ((_a = data.cloud) == null ? void 0 : _a.guardrails) !== void 0,
      requestType: data.metadata.requestType
    });
    let logger2 = ctx.logger.child({
      component: "CopilotResolver.generateCopilotResponse"
    });
    logger2.debug({
      data
    }, "Generating Copilot response");
    if (properties) {
      logger2.debug("Properties provided, merging with context properties");
      ctx.properties = {
        ...ctx.properties,
        ...properties
      };
    }
    const copilotRuntime = ctx._copilotkit.runtime;
    const serviceAdapter = ctx._copilotkit.serviceAdapter;
    let copilotCloudPublicApiKey = null;
    let copilotCloudBaseUrl;
    if (data.cloud) {
      logger2 = logger2.child({
        cloud: true
      });
      logger2.debug("Cloud configuration provided, checking for public API key in headers");
      const key = ctx.request.headers.get("x-copilotcloud-public-api-key");
      if (key) {
        logger2.debug("Public API key found in headers");
        copilotCloudPublicApiKey = key;
      } else {
        logger2.error("Public API key not found in headers");
        throw new import_graphql.GraphQLError("X-CopilotCloud-Public-API-Key header is required");
      }
      if (process.env.COPILOT_CLOUD_BASE_URL) {
        copilotCloudBaseUrl = process.env.COPILOT_CLOUD_BASE_URL;
      } else if ((_b = ctx._copilotkit.cloud) == null ? void 0 : _b.baseUrl) {
        copilotCloudBaseUrl = (_c = ctx._copilotkit.cloud) == null ? void 0 : _c.baseUrl;
      } else {
        copilotCloudBaseUrl = "https://api.cloud.copilotkit.ai";
      }
      logger2 = logger2.child({
        copilotCloudBaseUrl
      });
    }
    logger2.debug("Setting up subjects");
    const responseStatus$ = new import_rxjs3.ReplaySubject();
    const interruptStreaming$ = new import_rxjs3.ReplaySubject();
    const guardrailsResult$ = new import_rxjs3.ReplaySubject();
    let outputMessages = [];
    let resolveOutputMessagesPromise;
    let rejectOutputMessagesPromise;
    const outputMessagesPromise = new Promise((resolve, reject) => {
      resolveOutputMessagesPromise = resolve;
      rejectOutputMessagesPromise = reject;
    });
    if (copilotCloudPublicApiKey) {
      ctx.properties["copilotCloudPublicApiKey"] = copilotCloudPublicApiKey;
    }
    logger2.debug("Processing");
    const { eventSource, threadId = (0, import_shared6.randomId)(), runId, serverSideActions, actionInputsWithoutAgents, extensions } = await copilotRuntime.processRuntimeRequest({
      serviceAdapter,
      messages: data.messages,
      actions: data.frontend.actions.filter((action) => action.available !== ActionInputAvailability.disabled),
      threadId: data.threadId,
      runId: data.runId,
      publicApiKey: copilotCloudPublicApiKey,
      outputMessagesPromise,
      graphqlContext: ctx,
      forwardedParameters: data.forwardedParameters,
      agentSession: data.agentSession,
      agentStates: data.agentStates,
      url: data.frontend.url,
      extensions: data.extensions,
      metaEvents: data.metaEvents
    });
    logger2.debug("Event source created, creating response");
    const eventStream = eventSource.processRuntimeEvents({
      serverSideActions,
      guardrailsResult$: ((_d = data.cloud) == null ? void 0 : _d.guardrails) ? guardrailsResult$ : null,
      actionInputsWithoutAgents: actionInputsWithoutAgents.filter(
        // TODO-AGENTS: do not exclude ALL server side actions
        (action) => !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name)
      ),
      threadId
    }).pipe(
      // shareReplay() ensures that later subscribers will see the whole stream instead of
      // just the events that were emitted after the subscriber was added.
      (0, import_rxjs3.shareReplay)(),
      (0, import_rxjs3.finalize)(() => {
        logger2.debug("Event stream finalized");
      })
    );
    const response = {
      threadId,
      runId,
      status: (0, import_rxjs3.firstValueFrom)(responseStatus$),
      extensions,
      metaEvents: new import_graphql_yoga.Repeater(async (push, stop) => {
        let eventStreamSubscription;
        eventStreamSubscription = eventStream.subscribe({
          next: async (event) => {
            if (event.type != RuntimeEventTypes.MetaEvent) {
              return;
            }
            switch (event.name) {
              case RuntimeMetaEventName.LangGraphInterruptEvent:
                push((0, import_class_transformer2.plainToInstance)(LangGraphInterruptEvent, {
                  type: event.type,
                  name: event.name,
                  value: event.value
                }));
                break;
              case RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent:
                push((0, import_class_transformer2.plainToInstance)(CopilotKitLangGraphInterruptEvent, {
                  type: event.type,
                  name: event.name,
                  data: {
                    value: event.data.value,
                    messages: event.data.messages.map((message) => {
                      if (message.type === "TextMessage" || "content" in message && "role" in message) {
                        return (0, import_class_transformer2.plainToInstance)(TextMessage, {
                          id: message.id,
                          createdAt: /* @__PURE__ */ new Date(),
                          content: [
                            message.content
                          ],
                          role: message.role,
                          status: new SuccessMessageStatus()
                        });
                      }
                      if ("arguments" in message) {
                        return (0, import_class_transformer2.plainToInstance)(ActionExecutionMessage, {
                          name: message.name,
                          id: message.id,
                          arguments: [
                            JSON.stringify(message.arguments)
                          ],
                          createdAt: /* @__PURE__ */ new Date(),
                          status: new SuccessMessageStatus()
                        });
                      }
                      throw new Error("Unknown message in metaEvents copilot resolver");
                    })
                  }
                }));
                break;
            }
          },
          error: (err) => {
            logger2.error({
              err
            }, "Error in meta events stream");
            responseStatus$.next(new UnknownErrorResponse({
              description: `An unknown error has occurred in the event stream`
            }));
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stop();
          },
          complete: async () => {
            logger2.debug("Meta events stream completed");
            responseStatus$.next(new SuccessResponseStatus());
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stop();
          }
        });
      }),
      messages: new import_graphql_yoga.Repeater(async (pushMessage, stopStreamingMessages) => {
        var _a2;
        logger2.debug("Messages repeater created");
        if ((_a2 = data.cloud) == null ? void 0 : _a2.guardrails) {
          logger2 = logger2.child({
            guardrails: true
          });
          logger2.debug("Guardrails is enabled, validating input");
          invokeGuardrails({
            baseUrl: copilotCloudBaseUrl,
            copilotCloudPublicApiKey,
            data,
            onResult: (result) => {
              logger2.debug({
                status: result.status
              }, "Guardrails validation done");
              guardrailsResult$.next(result);
              if (result.status === "denied") {
                responseStatus$.next(new GuardrailsValidationFailureResponse({
                  guardrailsReason: result.reason
                }));
                interruptStreaming$.next({
                  reason: `Interrupted due to Guardrails validation failure. Reason: ${result.reason}`
                });
                outputMessages = [
                  (0, import_class_transformer2.plainToInstance)(TextMessage, {
                    id: (0, import_shared6.randomId)(),
                    createdAt: /* @__PURE__ */ new Date(),
                    content: result.reason,
                    role: MessageRole.assistant
                  })
                ];
                resolveOutputMessagesPromise(outputMessages);
              }
            },
            onError: (err) => {
              logger2.error({
                err
              }, "Error in guardrails validation");
              responseStatus$.next(new UnknownErrorResponse({
                description: `An unknown error has occurred in the guardrails validation`
              }));
              interruptStreaming$.next({
                reason: `Interrupted due to unknown error in guardrails validation`
              });
              rejectOutputMessagesPromise(err);
            }
          });
        }
        let eventStreamSubscription;
        logger2.debug("Event stream created, subscribing to event stream");
        eventStreamSubscription = eventStream.subscribe({
          next: async (event) => {
            switch (event.type) {
              case RuntimeEventTypes.MetaEvent:
                break;
              case RuntimeEventTypes.TextMessageStart:
                const textMessageContentStream = eventStream.pipe(
                  // skip until this message start event
                  (0, import_rxjs3.skipWhile)((e) => e !== event),
                  // take until the message end event
                  (0, import_rxjs3.takeWhile)((e) => !(e.type === RuntimeEventTypes.TextMessageEnd && e.messageId == event.messageId)),
                  // filter out any other message events or message ids
                  (0, import_rxjs3.filter)((e) => e.type == RuntimeEventTypes.TextMessageContent && e.messageId == event.messageId)
                );
                const streamingTextStatus = new import_rxjs3.Subject();
                const messageId = event.messageId;
                pushMessage({
                  id: messageId,
                  parentMessageId: event.parentMessageId,
                  status: (0, import_rxjs3.firstValueFrom)(streamingTextStatus),
                  createdAt: /* @__PURE__ */ new Date(),
                  role: MessageRole.assistant,
                  content: new import_graphql_yoga.Repeater(async (pushTextChunk, stopStreamingText) => {
                    logger2.debug("Text message content repeater created");
                    const textChunks = [];
                    let textSubscription;
                    interruptStreaming$.pipe((0, import_rxjs3.shareReplay)(), (0, import_rxjs3.take)(1), (0, import_rxjs3.tap)(({ reason, messageId: messageId2 }) => {
                      logger2.debug({
                        reason,
                        messageId: messageId2
                      }, "Text streaming interrupted");
                      streamingTextStatus.next((0, import_class_transformer2.plainToInstance)(FailedMessageStatus, {
                        reason
                      }));
                      responseStatus$.next(new MessageStreamInterruptedResponse({
                        messageId: messageId2
                      }));
                      stopStreamingText();
                      textSubscription == null ? void 0 : textSubscription.unsubscribe();
                    })).subscribe();
                    logger2.debug("Subscribing to text message content stream");
                    textSubscription = textMessageContentStream.subscribe({
                      next: async (e) => {
                        if (e.type == RuntimeEventTypes.TextMessageContent) {
                          await pushTextChunk(e.content);
                          textChunks.push(e.content);
                        }
                      },
                      error: (err) => {
                        logger2.error({
                          err
                        }, "Error in text message content stream");
                        interruptStreaming$.next({
                          reason: "Error streaming message content",
                          messageId
                        });
                        stopStreamingText();
                        textSubscription == null ? void 0 : textSubscription.unsubscribe();
                      },
                      complete: () => {
                        logger2.debug("Text message content stream completed");
                        streamingTextStatus.next(new SuccessMessageStatus());
                        stopStreamingText();
                        textSubscription == null ? void 0 : textSubscription.unsubscribe();
                        outputMessages.push((0, import_class_transformer2.plainToInstance)(TextMessage, {
                          id: messageId,
                          createdAt: /* @__PURE__ */ new Date(),
                          content: textChunks.join(""),
                          role: MessageRole.assistant
                        }));
                      }
                    });
                  })
                });
                break;
              case RuntimeEventTypes.ActionExecutionStart:
                logger2.debug("Action execution start event received");
                const actionExecutionArgumentStream = eventStream.pipe(
                  (0, import_rxjs3.skipWhile)((e) => e !== event),
                  // take until the action execution end event
                  (0, import_rxjs3.takeWhile)((e) => !(e.type === RuntimeEventTypes.ActionExecutionEnd && e.actionExecutionId == event.actionExecutionId)),
                  // filter out any other action execution events or action execution ids
                  (0, import_rxjs3.filter)((e) => e.type == RuntimeEventTypes.ActionExecutionArgs && e.actionExecutionId == event.actionExecutionId)
                );
                const streamingArgumentsStatus = new import_rxjs3.Subject();
                pushMessage({
                  id: event.actionExecutionId,
                  parentMessageId: event.parentMessageId,
                  status: (0, import_rxjs3.firstValueFrom)(streamingArgumentsStatus),
                  createdAt: /* @__PURE__ */ new Date(),
                  name: event.actionName,
                  arguments: new import_graphql_yoga.Repeater(async (pushArgumentsChunk, stopStreamingArguments) => {
                    logger2.debug("Action execution argument stream created");
                    const argumentChunks = [];
                    let actionExecutionArgumentSubscription;
                    actionExecutionArgumentSubscription = actionExecutionArgumentStream.subscribe({
                      next: async (e) => {
                        if (e.type == RuntimeEventTypes.ActionExecutionArgs) {
                          await pushArgumentsChunk(e.args);
                          argumentChunks.push(e.args);
                        }
                      },
                      error: (err) => {
                        logger2.error({
                          err
                        }, "Error in action execution argument stream");
                        streamingArgumentsStatus.next((0, import_class_transformer2.plainToInstance)(FailedMessageStatus, {
                          reason: "An unknown error has occurred in the action execution argument stream"
                        }));
                        stopStreamingArguments();
                        actionExecutionArgumentSubscription == null ? void 0 : actionExecutionArgumentSubscription.unsubscribe();
                      },
                      complete: () => {
                        logger2.debug("Action execution argument stream completed");
                        streamingArgumentsStatus.next(new SuccessMessageStatus());
                        stopStreamingArguments();
                        actionExecutionArgumentSubscription == null ? void 0 : actionExecutionArgumentSubscription.unsubscribe();
                        outputMessages.push((0, import_class_transformer2.plainToInstance)(ActionExecutionMessage, {
                          id: event.actionExecutionId,
                          createdAt: /* @__PURE__ */ new Date(),
                          name: event.actionName,
                          arguments: argumentChunks.join("")
                        }));
                      }
                    });
                  })
                });
                break;
              case RuntimeEventTypes.ActionExecutionResult:
                logger2.debug({
                  result: event.result
                }, "Action execution result event received");
                pushMessage({
                  id: "result-" + event.actionExecutionId,
                  status: new SuccessMessageStatus(),
                  createdAt: /* @__PURE__ */ new Date(),
                  actionExecutionId: event.actionExecutionId,
                  actionName: event.actionName,
                  result: event.result
                });
                outputMessages.push((0, import_class_transformer2.plainToInstance)(ResultMessage, {
                  id: "result-" + event.actionExecutionId,
                  createdAt: /* @__PURE__ */ new Date(),
                  actionExecutionId: event.actionExecutionId,
                  actionName: event.actionName,
                  result: event.result
                }));
                break;
              case RuntimeEventTypes.AgentStateMessage:
                logger2.debug({
                  event
                }, "Agent message event received");
                pushMessage({
                  id: (0, import_shared6.randomId)(),
                  status: new SuccessMessageStatus(),
                  threadId: event.threadId,
                  agentName: event.agentName,
                  nodeName: event.nodeName,
                  runId: event.runId,
                  active: event.active,
                  state: event.state,
                  running: event.running,
                  role: MessageRole.assistant,
                  createdAt: /* @__PURE__ */ new Date()
                });
                outputMessages.push((0, import_class_transformer2.plainToInstance)(AgentStateMessage, {
                  id: (0, import_shared6.randomId)(),
                  threadId: event.threadId,
                  agentName: event.agentName,
                  nodeName: event.nodeName,
                  runId: event.runId,
                  active: event.active,
                  state: event.state,
                  running: event.running,
                  role: MessageRole.assistant,
                  createdAt: /* @__PURE__ */ new Date()
                }));
                break;
            }
          },
          error: (err) => {
            logger2.error({
              err
            }, "Error in event stream");
            responseStatus$.next(new UnknownErrorResponse({
              description: `An unknown error has occurred in the event stream`
            }));
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stopStreamingMessages();
            rejectOutputMessagesPromise(err);
          },
          complete: async () => {
            var _a3;
            logger2.debug("Event stream completed");
            if ((_a3 = data.cloud) == null ? void 0 : _a3.guardrails) {
              logger2.debug("Guardrails is enabled, waiting for guardrails result");
              await (0, import_rxjs3.firstValueFrom)(guardrailsResult$);
            }
            responseStatus$.next(new SuccessResponseStatus());
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stopStreamingMessages();
            resolveOutputMessagesPromise(outputMessages);
          }
        });
      })
    };
    return response;
  }
};
__name(CopilotResolver, "CopilotResolver");
_ts_decorate19([
  (0, import_type_graphql20.Query)(() => String),
  _ts_metadata19("design:type", Function),
  _ts_metadata19("design:paramtypes", []),
  _ts_metadata19("design:returntype", Promise)
], CopilotResolver.prototype, "hello", null);
_ts_decorate19([
  (0, import_type_graphql20.Query)(() => AgentsResponse),
  _ts_param(0, (0, import_type_graphql20.Ctx)()),
  _ts_metadata19("design:type", Function),
  _ts_metadata19("design:paramtypes", [
    typeof GraphQLContext === "undefined" ? Object : GraphQLContext
  ]),
  _ts_metadata19("design:returntype", Promise)
], CopilotResolver.prototype, "availableAgents", null);
_ts_decorate19([
  (0, import_type_graphql20.Mutation)(() => CopilotResponse),
  _ts_param(0, (0, import_type_graphql20.Ctx)()),
  _ts_param(1, (0, import_type_graphql20.Arg)("data")),
  _ts_param(2, (0, import_type_graphql20.Arg)("properties", () => import_graphql_scalars2.GraphQLJSONObject, {
    nullable: true
  })),
  _ts_metadata19("design:type", Function),
  _ts_metadata19("design:paramtypes", [
    typeof GraphQLContext === "undefined" ? Object : GraphQLContext,
    typeof GenerateCopilotResponseInput === "undefined" ? Object : GenerateCopilotResponseInput,
    typeof CopilotRequestContextProperties === "undefined" ? Object : CopilotRequestContextProperties
  ]),
  _ts_metadata19("design:returntype", Promise)
], CopilotResolver.prototype, "generateCopilotResponse", null);
CopilotResolver = _ts_decorate19([
  (0, import_type_graphql20.Resolver)(() => CopilotResponse)
], CopilotResolver);

// src/lib/integrations/shared.ts
var import_plugin_defer_stream = require("@graphql-yoga/plugin-defer-stream");

// src/lib/logger.ts
var import_pino = __toESM(require("pino"));
var import_pino_pretty = __toESM(require("pino-pretty"));
function createLogger(options) {
  const { level, component } = options || {};
  const stream = (0, import_pino_pretty.default)({
    colorize: true
  });
  const logger2 = (0, import_pino.default)({
    level: process.env.LOG_LEVEL || level || "error",
    redact: {
      paths: [
        "pid",
        "hostname"
      ],
      remove: true
    }
  }, stream);
  if (component) {
    return logger2.child({
      component
    });
  } else {
    return logger2;
  }
}
__name(createLogger, "createLogger");

// src/graphql/resolvers/state.resolver.ts
var import_type_graphql23 = require("type-graphql");
var import_type_graphql24 = require("type-graphql");
var import_type_graphql25 = require("type-graphql");

// src/graphql/types/load-agent-state-response.type.ts
var import_type_graphql21 = require("type-graphql");
function _ts_decorate20(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate20, "_ts_decorate");
function _ts_metadata20(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata20, "_ts_metadata");
var LoadAgentStateResponse = class {
  threadId;
  threadExists;
  state;
  messages;
};
__name(LoadAgentStateResponse, "LoadAgentStateResponse");
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => String),
  _ts_metadata20("design:type", String)
], LoadAgentStateResponse.prototype, "threadId", void 0);
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => Boolean),
  _ts_metadata20("design:type", Boolean)
], LoadAgentStateResponse.prototype, "threadExists", void 0);
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => String),
  _ts_metadata20("design:type", String)
], LoadAgentStateResponse.prototype, "state", void 0);
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => String),
  _ts_metadata20("design:type", String)
], LoadAgentStateResponse.prototype, "messages", void 0);
LoadAgentStateResponse = _ts_decorate20([
  (0, import_type_graphql21.ObjectType)()
], LoadAgentStateResponse);

// src/graphql/inputs/load-agent-state.input.ts
var import_type_graphql22 = require("type-graphql");
function _ts_decorate21(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate21, "_ts_decorate");
function _ts_metadata21(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata21, "_ts_metadata");
var LoadAgentStateInput = class {
  threadId;
  agentName;
};
__name(LoadAgentStateInput, "LoadAgentStateInput");
_ts_decorate21([
  (0, import_type_graphql22.Field)(() => String),
  _ts_metadata21("design:type", String)
], LoadAgentStateInput.prototype, "threadId", void 0);
_ts_decorate21([
  (0, import_type_graphql22.Field)(() => String),
  _ts_metadata21("design:type", String)
], LoadAgentStateInput.prototype, "agentName", void 0);
LoadAgentStateInput = _ts_decorate21([
  (0, import_type_graphql22.InputType)()
], LoadAgentStateInput);

// src/graphql/resolvers/state.resolver.ts
function _ts_decorate22(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate22, "_ts_decorate");
function _ts_metadata22(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata22, "_ts_metadata");
function _ts_param2(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
}
__name(_ts_param2, "_ts_param");
var StateResolver = class {
  async loadAgentState(ctx, data) {
    const agents = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);
    const agent = agents.find((agent2) => agent2.name === data.agentName);
    if (!agent) {
      return {
        threadId: data.threadId || "",
        threadExists: false,
        state: JSON.stringify({}),
        messages: JSON.stringify([])
      };
    }
    const state = await ctx._copilotkit.runtime.loadAgentState(ctx, data.threadId, data.agentName);
    return state;
  }
};
__name(StateResolver, "StateResolver");
_ts_decorate22([
  (0, import_type_graphql25.Query)(() => LoadAgentStateResponse),
  _ts_param2(0, (0, import_type_graphql24.Ctx)()),
  _ts_param2(1, (0, import_type_graphql23.Arg)("data")),
  _ts_metadata22("design:type", Function),
  _ts_metadata22("design:paramtypes", [
    typeof GraphQLContext === "undefined" ? Object : GraphQLContext,
    typeof LoadAgentStateInput === "undefined" ? Object : LoadAgentStateInput
  ]),
  _ts_metadata22("design:returntype", Promise)
], StateResolver.prototype, "loadAgentState", null);
StateResolver = _ts_decorate22([
  (0, import_type_graphql23.Resolver)(() => LoadAgentStateResponse)
], StateResolver);

// src/lib/integrations/shared.ts
var packageJson2 = __toESM(require_package());
var logger = createLogger();
var addCustomHeaderPlugin = {
  onResponse({ response }) {
    response.headers.set("X-CopilotKit-Runtime-Version", packageJson2.version);
  }
};
async function createContext(initialContext, copilotKitContext, contextLogger, properties = {}) {
  logger.debug({
    copilotKitContext
  }, "Creating GraphQL context");
  const ctx = {
    ...initialContext,
    _copilotkit: {
      ...copilotKitContext
    },
    properties: {
      ...properties
    },
    logger: contextLogger
  };
  return ctx;
}
__name(createContext, "createContext");
function buildSchema(options = {}) {
  logger.debug("Building GraphQL schema...");
  const schema = (0, import_type_graphql26.buildSchemaSync)({
    resolvers: [
      CopilotResolver,
      StateResolver
    ],
    emitSchemaFile: options.emitSchemaFile
  });
  logger.debug("GraphQL schema built successfully");
  return schema;
}
__name(buildSchema, "buildSchema");
function getCommonConfig(options) {
  var _a;
  const logLevel = process.env.LOG_LEVEL || options.logLevel || "error";
  const logger2 = createLogger({
    level: logLevel,
    component: "getCommonConfig"
  });
  const contextLogger = createLogger({
    level: logLevel
  });
  if (options.cloud) {
    telemetry_client_default.setCloudConfiguration({
      publicApiKey: options.cloud.publicApiKey,
      baseUrl: options.cloud.baseUrl
    });
  }
  if ((_a = options.properties) == null ? void 0 : _a._copilotkit) {
    telemetry_client_default.setGlobalProperties({
      _copilotkit: {
        ...options.properties._copilotkit
      }
    });
  }
  telemetry_client_default.setGlobalProperties({
    runtime: {
      serviceAdapter: options.serviceAdapter.constructor.name
    }
  });
  return {
    logging: createLogger({
      component: "Yoga GraphQL",
      level: logLevel
    }),
    schema: buildSchema(),
    plugins: [
      (0, import_plugin_defer_stream.useDeferStream)(),
      addCustomHeaderPlugin
    ],
    context: (ctx) => createContext(ctx, options, contextLogger, options.properties)
  };
}
__name(getCommonConfig, "getCommonConfig");

// src/lib/integrations/node-http/index.ts
function copilotRuntimeNodeHttpEndpoint(options) {
  var _a;
  const commonConfig = getCommonConfig(options);
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "node-http"
    }
  });
  if ((_a = options.properties) == null ? void 0 : _a._copilotkit) {
    telemetry_client_default.setGlobalProperties({
      _copilotkit: options.properties._copilotkit
    });
  }
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  const logger2 = commonConfig.logging;
  logger2.debug("Creating Node HTTP endpoint");
  const yoga = (0, import_graphql_yoga2.createYoga)({
    ...commonConfig,
    graphqlEndpoint: options.endpoint
  });
  return yoga;
}
__name(copilotRuntimeNodeHttpEndpoint, "copilotRuntimeNodeHttpEndpoint");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  copilotRuntimeNodeHttpEndpoint
});
//# sourceMappingURL=index.js.map