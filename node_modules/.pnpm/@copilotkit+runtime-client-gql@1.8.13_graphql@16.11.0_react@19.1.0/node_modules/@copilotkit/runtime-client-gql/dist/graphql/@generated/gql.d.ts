import { GenerateCopilotResponseMutation, Exact, GenerateCopilotResponseInput, InputMaybe, Scalars, AvailableAgentsQuery, LoadAgentStateQuery, LoadAgentStateInput } from './graphql.js';
import { TypedDocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
declare const documents: {
    "\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\n    generateCopilotResponse(data: $data, properties: $properties) {\n      threadId\n      runId\n      extensions {\n        openaiAssistantAPI {\n          runId\n          threadId\n        }\n      }\n      ... on CopilotResponse @defer {\n        status {\n          ... on BaseResponseStatus {\n            code\n          }\n          ... on FailedResponseStatus {\n            reason\n            details\n          }\n        }\n      }\n      messages @stream {\n        __typename\n        ... on BaseMessageOutput {\n          id\n          createdAt\n        }\n        ... on BaseMessageOutput @defer {\n          status {\n            ... on SuccessMessageStatus {\n              code\n            }\n            ... on FailedMessageStatus {\n              code\n              reason\n            }\n            ... on PendingMessageStatus {\n              code\n            }\n          }\n        }\n        ... on TextMessageOutput {\n          content @stream\n          role\n          parentMessageId\n        }\n        ... on ImageMessageOutput {\n          format\n          bytes\n          role\n          parentMessageId\n        }\n        ... on ActionExecutionMessageOutput {\n          name\n          arguments @stream\n          parentMessageId\n        }\n        ... on ResultMessageOutput {\n          result\n          actionExecutionId\n          actionName\n        }\n        ... on AgentStateMessageOutput {\n          threadId\n          state\n          running\n          agentName\n          nodeName\n          runId\n          active\n          role\n        }\n      }\n      metaEvents @stream {\n        ... on LangGraphInterruptEvent {\n          type\n          name\n          value\n        }\n\n        ... on CopilotKitLangGraphInterruptEvent {\n          type\n          name\n          data {\n            messages {\n              __typename\n              ... on BaseMessageOutput {\n                id\n                createdAt\n              }\n              ... on BaseMessageOutput @defer {\n                status {\n                  ... on SuccessMessageStatus {\n                    code\n                  }\n                  ... on FailedMessageStatus {\n                    code\n                    reason\n                  }\n                  ... on PendingMessageStatus {\n                    code\n                  }\n                }\n              }\n              ... on TextMessageOutput {\n                content\n                role\n                parentMessageId\n              }\n              ... on ActionExecutionMessageOutput {\n                name\n                arguments\n                parentMessageId\n              }\n              ... on ResultMessageOutput {\n                result\n                actionExecutionId\n                actionName\n              }\n            }\n            value\n          }\n        }\n      }\n    }\n  }\n": TypedDocumentNode<GenerateCopilotResponseMutation, Exact<{
        data: GenerateCopilotResponseInput;
        properties?: InputMaybe<Scalars["JSONObject"]["input"]>;
    }>>;
    "\n  query availableAgents {\n    availableAgents {\n      agents {\n        name\n        id\n        description\n      }\n    }\n  }\n": TypedDocumentNode<AvailableAgentsQuery, Exact<{
        [key: string]: never;
    }>>;
    "\n  query loadAgentState($data: LoadAgentStateInput!) {\n    loadAgentState(data: $data) {\n      threadId\n      threadExists\n      state\n      messages\n    }\n  }\n": TypedDocumentNode<LoadAgentStateQuery, Exact<{
        data: LoadAgentStateInput;
    }>>;
};
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
declare function graphql(source: string): unknown;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
declare function graphql(source: "\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\n    generateCopilotResponse(data: $data, properties: $properties) {\n      threadId\n      runId\n      extensions {\n        openaiAssistantAPI {\n          runId\n          threadId\n        }\n      }\n      ... on CopilotResponse @defer {\n        status {\n          ... on BaseResponseStatus {\n            code\n          }\n          ... on FailedResponseStatus {\n            reason\n            details\n          }\n        }\n      }\n      messages @stream {\n        __typename\n        ... on BaseMessageOutput {\n          id\n          createdAt\n        }\n        ... on BaseMessageOutput @defer {\n          status {\n            ... on SuccessMessageStatus {\n              code\n            }\n            ... on FailedMessageStatus {\n              code\n              reason\n            }\n            ... on PendingMessageStatus {\n              code\n            }\n          }\n        }\n        ... on TextMessageOutput {\n          content @stream\n          role\n          parentMessageId\n        }\n        ... on ImageMessageOutput {\n          format\n          bytes\n          role\n          parentMessageId\n        }\n        ... on ActionExecutionMessageOutput {\n          name\n          arguments @stream\n          parentMessageId\n        }\n        ... on ResultMessageOutput {\n          result\n          actionExecutionId\n          actionName\n        }\n        ... on AgentStateMessageOutput {\n          threadId\n          state\n          running\n          agentName\n          nodeName\n          runId\n          active\n          role\n        }\n      }\n      metaEvents @stream {\n        ... on LangGraphInterruptEvent {\n          type\n          name\n          value\n        }\n\n        ... on CopilotKitLangGraphInterruptEvent {\n          type\n          name\n          data {\n            messages {\n              __typename\n              ... on BaseMessageOutput {\n                id\n                createdAt\n              }\n              ... on BaseMessageOutput @defer {\n                status {\n                  ... on SuccessMessageStatus {\n                    code\n                  }\n                  ... on FailedMessageStatus {\n                    code\n                    reason\n                  }\n                  ... on PendingMessageStatus {\n                    code\n                  }\n                }\n              }\n              ... on TextMessageOutput {\n                content\n                role\n                parentMessageId\n              }\n              ... on ActionExecutionMessageOutput {\n                name\n                arguments\n                parentMessageId\n              }\n              ... on ResultMessageOutput {\n                result\n                actionExecutionId\n                actionName\n              }\n            }\n            value\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\n    generateCopilotResponse(data: $data, properties: $properties) {\n      threadId\n      runId\n      extensions {\n        openaiAssistantAPI {\n          runId\n          threadId\n        }\n      }\n      ... on CopilotResponse @defer {\n        status {\n          ... on BaseResponseStatus {\n            code\n          }\n          ... on FailedResponseStatus {\n            reason\n            details\n          }\n        }\n      }\n      messages @stream {\n        __typename\n        ... on BaseMessageOutput {\n          id\n          createdAt\n        }\n        ... on BaseMessageOutput @defer {\n          status {\n            ... on SuccessMessageStatus {\n              code\n            }\n            ... on FailedMessageStatus {\n              code\n              reason\n            }\n            ... on PendingMessageStatus {\n              code\n            }\n          }\n        }\n        ... on TextMessageOutput {\n          content @stream\n          role\n          parentMessageId\n        }\n        ... on ImageMessageOutput {\n          format\n          bytes\n          role\n          parentMessageId\n        }\n        ... on ActionExecutionMessageOutput {\n          name\n          arguments @stream\n          parentMessageId\n        }\n        ... on ResultMessageOutput {\n          result\n          actionExecutionId\n          actionName\n        }\n        ... on AgentStateMessageOutput {\n          threadId\n          state\n          running\n          agentName\n          nodeName\n          runId\n          active\n          role\n        }\n      }\n      metaEvents @stream {\n        ... on LangGraphInterruptEvent {\n          type\n          name\n          value\n        }\n\n        ... on CopilotKitLangGraphInterruptEvent {\n          type\n          name\n          data {\n            messages {\n              __typename\n              ... on BaseMessageOutput {\n                id\n                createdAt\n              }\n              ... on BaseMessageOutput @defer {\n                status {\n                  ... on SuccessMessageStatus {\n                    code\n                  }\n                  ... on FailedMessageStatus {\n                    code\n                    reason\n                  }\n                  ... on PendingMessageStatus {\n                    code\n                  }\n                }\n              }\n              ... on TextMessageOutput {\n                content\n                role\n                parentMessageId\n              }\n              ... on ActionExecutionMessageOutput {\n                name\n                arguments\n                parentMessageId\n              }\n              ... on ResultMessageOutput {\n                result\n                actionExecutionId\n                actionName\n              }\n            }\n            value\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
declare function graphql(source: "\n  query availableAgents {\n    availableAgents {\n      agents {\n        name\n        id\n        description\n      }\n    }\n  }\n"): (typeof documents)["\n  query availableAgents {\n    availableAgents {\n      agents {\n        name\n        id\n        description\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
declare function graphql(source: "\n  query loadAgentState($data: LoadAgentStateInput!) {\n    loadAgentState(data: $data) {\n      threadId\n      threadExists\n      state\n      messages\n    }\n  }\n"): (typeof documents)["\n  query loadAgentState($data: LoadAgentStateInput!) {\n    loadAgentState(data: $data) {\n      threadId\n      threadExists\n      state\n      messages\n    }\n  }\n"];
type DocumentType<TDocumentNode extends TypedDocumentNode<any, any>> = TDocumentNode extends TypedDocumentNode<infer TType, any> ? TType : never;

export { DocumentType, graphql };
