import { CopilotKitProps } from './copilotkit-props.js';
import * as react_jsx_runtime from 'react/jsx-runtime';
import '@copilotkit/runtime-client-gql';
import 'react';
import '../../copilot-context-8fb74a85.js';
import '@copilotkit/shared';
import '../../types/frontend-action.js';
import '../../hooks/use-tree.js';
import '../../types/document-pointer.js';
import '../../types/chat-suggestion-configuration.js';
import '../../types/coagent-action.js';
import '../../types/coagent-state.js';

declare function CopilotKit({ children, ...props }: CopilotKitProps): react_jsx_runtime.JSX.Element;
declare function CopilotKitInternal(cpkProps: CopilotKitProps): react_jsx_runtime.JSX.Element;
declare const defaultCopilotContextCategories: string[];

export { CopilotKit, CopilotKitInternal, defaultCopilotContextCategories };
