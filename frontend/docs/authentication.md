# Authentication System Documentation

## 🏗️ **Architecture Overview**

The PI Lawyer AI authentication system uses a **hybrid approach** combining Supabase Auth with custom JWT validation for different contexts:

- **Client-side**: React hooks with Supabase Auth integration
- **Server-side**: JWT validation with tenant isolation
- **API Routes**: Enhanced client with schema-aware database access
- **Security**: Role-based access control (RBAC) with tenant isolation

## 📁 **File Structure**

```
src/lib/auth/
├── client.ts              # Client-side Supabase configuration
├── server.ts              # Server-side auth utilities
├── server-exports.ts      # Server-side auth functions
├── jwt-utils.ts           # JWT token handling utilities
├── permissions.ts         # RBAC and permission checking
├── permissions-server.ts  # Server-side permission utilities
├── useAuth.ts            # Main authentication hook
├── useRequireAuth.ts     # Authentication requirement hook
├── useRequireRole.ts     # Role requirement hook
├── AuthProvider.tsx      # Authentication context provider
├── types.ts              # TypeScript type definitions
└── __tests__/            # Comprehensive test suite
```

## 🔐 **Authentication Patterns**

### **Client-Side Authentication**

Use React hooks for client-side authentication:

```typescript
import { useAuth } from '@/lib/auth/useAuth';
import { useRequireAuth } from '@/lib/auth/useRequireAuth';
import { useRequireRole } from '@/lib/auth/useRequireRole';

// Basic authentication check
function MyComponent() {
  const { user, profile, isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please log in</div>;
  
  return <div>Welcome, {profile?.first_name}!</div>;
}

// Automatic redirect if not authenticated
function ProtectedPage() {
  useRequireAuth(); // Redirects to /auth/login if not authenticated
  
  return <div>Protected content</div>;
}

// Role-based access control
function AdminPanel() {
  const { isAuthorized } = useRequireRole(['admin', 'partner']);
  
  if (!isAuthorized) return <div>Access denied</div>;
  
  return <div>Admin content</div>;
}
```

### **Server-Side Authentication**

Use server utilities for API routes and server components:

```typescript
import { createClient, requireAuth, hasRole } from '@/lib/auth/server';
import { validateJwt, extractJwtFromRequest } from '@/lib/auth/jwt-utils';

// API Route with authentication
export async function GET(request: NextRequest) {
  // Method 1: Using enhanced client (recommended)
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { user, supabase } = authResult;
  
  // Method 2: Manual JWT validation
  const token = extractJwtFromRequest(request);
  if (!token) {
    return NextResponse.json({ error: 'No token' }, { status: 401 });
  }
  
  try {
    const payload = await validateJwt(token);
    // Use payload for authorization
  } catch (error) {
    return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
  }
}

// Role-based API protection
export async function DELETE(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  if (!hasRole(authResult.user, ['admin', 'partner'])) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  // Proceed with deletion
}
```

## 🎭 **Role-Based Access Control (RBAC)**

### **Role Hierarchy**

```typescript
enum UserRole {
  Superadmin = 'superadmin',  // Full system access
  Partner = 'partner',        // Firm management + all attorney permissions
  Attorney = 'attorney',      // Case management, client interaction
  Paralegal = 'paralegal',    // Case support, document management
  Staff = 'staff',           // Administrative tasks, read-only access
  Client = 'client'          // Own case access only
}
```

### **Permission System**

```typescript
import { checkPermission, getUserPermissions } from '@/lib/auth/permissions';

// Check specific permission
if (checkPermission(user, 'write:cases')) {
  // User can create/edit cases
}

// Check resource-specific permission
if (checkPermission(user, 'read:own_case', 'case', caseId)) {
  // Client can read their own case
}

// Get all user permissions
const permissions = getUserPermissions(user);
console.log(permissions); // ['read:cases', 'write:cases', ...]
```

### **Tenant Isolation**

```typescript
import { canAccessTenant } from '@/lib/auth/permissions';

// Ensure user can access tenant data
if (!canAccessTenant(user, tenantId)) {
  throw new Error('Access denied to tenant data');
}

// Database queries automatically use tenant isolation
const { data } = await supabase
  .schema('tenants')
  .from('cases')
  .select('*')
  .eq('tenant_id', user.tenantId); // Automatic tenant filtering
```

## 🔄 **Authentication Flows**

### **Login Flow**

```mermaid
sequenceDiagram
    participant U as User
    participant C as Client
    participant S as Supabase
    participant A as API
    
    U->>C: Enter credentials
    C->>S: signInWithPassword()
    S->>C: Return session + JWT
    C->>A: API calls with JWT
    A->>A: Validate JWT + permissions
    A->>C: Return data
    C->>U: Show dashboard
```

### **Protected Route Access**

```mermaid
sequenceDiagram
    participant U as User
    participant C as Client
    participant H as useRequireAuth
    participant R as Router
    
    U->>C: Navigate to protected route
    C->>H: Check authentication
    alt Not authenticated
        H->>R: Redirect to /auth/login
        R->>U: Show login page
    else Authenticated
        C->>U: Show protected content
    end
```

## 🛡️ **Security Best Practices**

### **JWT Token Handling**

```typescript
// ✅ DO: Use server-side validation
const payload = await validateJwt(token);

// ✅ DO: Check token expiration
if (payload.exp < Date.now() / 1000) {
  throw new JWTValidationError('Token expired');
}

// ❌ DON'T: Trust client-side tokens without validation
// ❌ DON'T: Store sensitive data in JWT payload
```

### **Permission Checking**

```typescript
// ✅ DO: Check permissions on every sensitive operation
if (!checkPermission(user, 'delete:cases')) {
  throw new Error('Insufficient permissions');
}

// ✅ DO: Use tenant isolation
const cases = await supabase
  .schema('tenants')
  .from('cases')
  .select('*')
  .eq('tenant_id', user.tenantId);

// ❌ DON'T: Rely only on client-side permission checks
// ❌ DON'T: Skip tenant isolation in database queries
```

### **Error Handling**

```typescript
// ✅ DO: Use specific error types
try {
  const payload = await validateJwt(token);
} catch (error) {
  if (error instanceof JWTValidationError) {
    return NextResponse.json(
      { error: 'Authentication failed' }, 
      { status: error.status }
    );
  }
  // Handle other errors
}

// ✅ DO: Log security events
console.error('Authentication failed:', {
  userId: user?.id,
  tenantId: user?.tenantId,
  error: error.message,
  timestamp: new Date().toISOString()
});
```

## 🧪 **Testing Authentication**

### **Unit Tests**

```typescript
import { validateJwt, JWTValidationError } from '@/lib/auth/jwt-utils';
import { checkPermission } from '@/lib/auth/permissions';

describe('Authentication', () => {
  it('should validate JWT tokens', async () => {
    await expect(validateJwt('invalid.token')).rejects.toThrow(JWTValidationError);
  });
  
  it('should check permissions correctly', () => {
    const user = { role: 'attorney', tenantId: 'tenant-123' };
    expect(checkPermission(user, 'read:cases')).toBe(true);
    expect(checkPermission(user, 'delete:cases')).toBe(false);
  });
});
```

### **Integration Tests**

```typescript
import { render, screen } from '@testing-library/react';
import { AuthProvider } from '@/lib/auth/AuthProvider';
import { useRequireAuth } from '@/lib/auth/useRequireAuth';

describe('Authentication Integration', () => {
  it('should redirect unauthenticated users', () => {
    const TestComponent = () => {
      useRequireAuth();
      return <div>Protected content</div>;
    };
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Should redirect to login
  });
});
```

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Required for JWT validation
SUPABASE_JWT_SECRET=your-jwt-secret-key

# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Authentication mode
USE_SUPABASE_AUTH=true  # or false for legacy mode
```

### **Super Admin Configuration**

```typescript
// Add super admin emails to the configuration
export const SUPER_ADMIN_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
];
```

## 🚨 **Common Pitfalls**

### **❌ Don't Do This**

```typescript
// DON'T: Skip authentication checks
export async function GET() {
  // Missing authentication!
  const data = await supabase.from('cases').select('*');
  return NextResponse.json(data);
}

// DON'T: Use client-side auth for sensitive operations
function DeleteButton({ caseId }) {
  const { user } = useAuth();
  if (user.role === 'admin') { // Client-side check only!
    return <button onClick={() => deleteCase(caseId)}>Delete</button>;
  }
}

// DON'T: Forget tenant isolation
const cases = await supabase
  .from('cases') // Missing .schema('tenants')
  .select('*');  // Missing .eq('tenant_id', user.tenantId)
```

### **✅ Do This Instead**

```typescript
// DO: Always authenticate API routes
export async function GET(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { user, supabase } = authResult;
  const data = await supabase
    .schema('tenants')
    .from('cases')
    .select('*')
    .eq('tenant_id', user.tenantId);
    
  return NextResponse.json(data);
}

// DO: Validate permissions on both client and server
function DeleteButton({ caseId }) {
  const { user } = useAuth();
  
  if (!checkPermission(user, 'delete:cases')) {
    return null; // Don't show button
  }
  
  const handleDelete = async () => {
    // Server will also validate permissions
    await fetch(`/api/cases/${caseId}`, { method: 'DELETE' });
  };
  
  return <button onClick={handleDelete}>Delete</button>;
}
```

## 🔄 **Migration from Legacy Patterns**

### **Old Pattern → New Pattern**

#### **Database Queries**

```typescript
// ❌ OLD: Direct Supabase queries
const { data } = await supabase
  .from('cases')
  .select('*');

// ✅ NEW: Schema-aware queries with tenant isolation
const { data } = await supabase
  .schema('tenants')
  .from('cases')
  .select('*')
  .eq('tenant_id', user.tenantId);
```

#### **API Route Authentication**

```typescript
// ❌ OLD: Manual session checking
export async function GET(request: NextRequest) {
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // Manual permission checking...
}

// ✅ NEW: Enhanced client with built-in auth
export async function GET(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { user, supabase } = authResult; // Pre-configured with tenant context
}
```

#### **Component Authentication**

```typescript
// ❌ OLD: Manual authentication checks
function MyComponent() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Manual session management...
  }, []);

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please log in</div>;
}

// ✅ NEW: Hook-based authentication
function MyComponent() {
  const { user, isAuthenticated, isLoading } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please log in</div>;
}
```

## 📚 **Additional Resources**

- [Testing Guide](./testing.md) - Comprehensive testing strategies
- [Security Guide](./security.md) - Security best practices
- [API Reference](./api-reference.md) - Complete API documentation
- [Troubleshooting Guide](./troubleshooting.md) - Common issues and solutions
