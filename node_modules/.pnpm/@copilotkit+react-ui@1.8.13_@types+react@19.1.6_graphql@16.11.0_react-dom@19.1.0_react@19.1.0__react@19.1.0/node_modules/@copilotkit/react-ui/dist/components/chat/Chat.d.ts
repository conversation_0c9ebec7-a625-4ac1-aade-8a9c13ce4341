import * as react_jsx_runtime from 'react/jsx-runtime';
import { CopilotChatIcons, CopilotChatLabels } from './ChatContext.js';
import React__default from 'react';
import { SystemMessageFunction, HintFunction } from '@copilotkit/react-core';
import { CopilotChatSuggestion } from '../../types/suggestions.js';
import { Message } from '@copilotkit/runtime-client-gql';
import { ComponentsMap, AssistantMessageProps, UserMessageProps, MessagesProps, RenderMessageProps, RenderSuggestionsListProps, InputProps } from './props.js';

/**
 * Props for CopilotChat component.
 */
interface CopilotChatProps {
    /**
     * Custom instructions to be added to the system message. Use this property to
     * provide additional context or guidance to the language model, influencing
     * its responses. These instructions can include specific directions,
     * preferences, or criteria that the model should consider when generating
     * its output, thereby tailoring the conversation more precisely to the
     * user's needs or the application's requirements.
     */
    instructions?: string;
    /**
     * A callback that gets called when the in progress state changes.
     */
    onInProgress?: (inProgress: boolean) => void;
    /**
     * A callback that gets called when a new message it submitted.
     */
    onSubmitMessage?: (message: string) => void | Promise<void>;
    /**
     * A custom stop generation function.
     */
    onStopGeneration?: OnStopGeneration;
    /**
     * A custom reload messages function.
     */
    onReloadMessages?: OnReloadMessages;
    /**
     * A callback function to regenerate the assistant's response
     */
    onRegenerate?: (messageId: string) => void;
    /**
     * A callback function when the message is copied
     */
    onCopy?: (message: string) => void;
    /**
     * A callback function for thumbs up feedback
     */
    onThumbsUp?: (message: string) => void;
    /**
     * A callback function for thumbs down feedback
     */
    onThumbsDown?: (message: string) => void;
    /**
     * A list of markdown components to render in assistant message.
     * Useful when you want to render custom elements in the message (e.g a reference tag element)
     */
    markdownTagRenderers?: ComponentsMap;
    /**
     * Icons can be used to set custom icons for the chat window.
     */
    icons?: CopilotChatIcons;
    /**
     * Labels can be used to set custom labels for the chat window.
     */
    labels?: CopilotChatLabels;
    /**
     * Enable image upload button (image inputs only supported on some models)
     */
    imageUploadsEnabled?: boolean;
    /**
     * The 'accept' attribute for the file input used for image uploads.
     * Defaults to "image/*".
     */
    inputFileAccept?: string;
    /**
     * A function that takes in context string and instructions and returns
     * the system message to include in the chat request.
     * Use this to completely override the system message, when providing
     * instructions is not enough.
     */
    makeSystemMessage?: SystemMessageFunction;
    /**
     * A custom assistant message component to use instead of the default.
     */
    AssistantMessage?: React__default.ComponentType<AssistantMessageProps>;
    /**
     * A custom user message component to use instead of the default.
     */
    UserMessage?: React__default.ComponentType<UserMessageProps>;
    /**
     * A custom Messages component to use instead of the default.
     */
    Messages?: React__default.ComponentType<MessagesProps>;
    /**
     * A custom RenderTextMessage component to use instead of the default.
     */
    RenderTextMessage?: React__default.ComponentType<RenderMessageProps>;
    /**
     * A custom RenderActionExecutionMessage component to use instead of the default.
     */
    RenderActionExecutionMessage?: React__default.ComponentType<RenderMessageProps>;
    /**
     * A custom RenderAgentStateMessage component to use instead of the default.
     */
    RenderAgentStateMessage?: React__default.ComponentType<RenderMessageProps>;
    /**
     * A custom RenderResultMessage component to use instead of the default.
     */
    RenderResultMessage?: React__default.ComponentType<RenderMessageProps>;
    /**
     * A custom RenderImageMessage component to use instead of the default.
     */
    RenderImageMessage?: React__default.ComponentType<RenderMessageProps>;
    /**
     * A custom suggestions list component to use instead of the default.
     */
    RenderSuggestionsList?: React__default.ComponentType<RenderSuggestionsListProps>;
    /**
     * A custom Input component to use instead of the default.
     */
    Input?: React__default.ComponentType<InputProps>;
    /**
     * A class name to apply to the root element.
     */
    className?: string;
    /**
     * Children to render.
     */
    children?: React__default.ReactNode;
}
interface OnStopGenerationArguments {
    /**
     * The name of the currently executing agent.
     */
    currentAgentName: string | undefined;
    /**
     * The messages in the chat.
     */
    messages: Message[];
    /**
     * Set the messages in the chat.
     */
    setMessages: (messages: Message[]) => void;
    /**
     * Stop chat generation.
     */
    stopGeneration: () => void;
    /**
     * Restart the currently executing agent.
     */
    restartCurrentAgent: () => void;
    /**
     * Stop the currently executing agent.
     */
    stopCurrentAgent: () => void;
    /**
     * Run the currently executing agent.
     */
    runCurrentAgent: (hint?: HintFunction) => Promise<void>;
    /**
     * Set the state of the currently executing agent.
     */
    setCurrentAgentState: (state: any) => void;
}
type OnReloadMessagesArguments = OnStopGenerationArguments & {
    /**
     * The message on which "regenerate" was pressed
     */
    messageId: string;
};
type OnStopGeneration = (args: OnStopGenerationArguments) => void;
type OnReloadMessages = (args: OnReloadMessagesArguments) => void;
type ImageUpload = {
    contentType: string;
    bytes: string;
};
declare function CopilotChat({ instructions, onSubmitMessage, makeSystemMessage, onInProgress, onStopGeneration, onReloadMessages, onRegenerate, onCopy, onThumbsUp, onThumbsDown, markdownTagRenderers, Messages, RenderTextMessage, RenderActionExecutionMessage, RenderAgentStateMessage, RenderResultMessage, RenderImageMessage, RenderSuggestionsList, Input, className, icons, labels, AssistantMessage, UserMessage, imageUploadsEnabled, inputFileAccept, }: CopilotChatProps): react_jsx_runtime.JSX.Element;
declare function WrappedCopilotChat({ children, icons, labels, className, }: {
    children: React__default.ReactNode;
    icons?: CopilotChatIcons;
    labels?: CopilotChatLabels;
    className?: string;
}): react_jsx_runtime.JSX.Element;
declare const useCopilotChatLogic: (makeSystemMessage?: SystemMessageFunction, onInProgress?: (isLoading: boolean) => void, onSubmitMessage?: (messageContent: string) => Promise<void> | void, onStopGeneration?: OnStopGeneration, onReloadMessages?: OnReloadMessages) => {
    visibleMessages: Message[];
    isLoading: boolean;
    currentSuggestions: CopilotChatSuggestion[];
    sendMessage: (messageContent: string, imagesToUse?: Array<{
        contentType: string;
        bytes: string;
    }>) => Promise<Message>;
    stopGeneration: () => void;
    reloadMessages: (messageId: string) => void;
};

export { CopilotChat, CopilotChatProps, ImageUpload, OnReloadMessages, OnReloadMessagesArguments, OnStopGeneration, WrappedCopilotChat, useCopilotChatLogic };
