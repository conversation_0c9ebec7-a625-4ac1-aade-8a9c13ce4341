import {
  useCopilotContext
} from "./chunk-7WG4MNRX.mjs";

// src/hooks/use-make-copilot-document-readable.ts
import { useEffect, useRef } from "react";
function useMakeCopilotDocumentReadable(document, categories, dependencies = []) {
  const { addDocumentContext, removeDocumentContext } = useCopilotContext();
  const idRef = useRef();
  useEffect(() => {
    const id = addDocumentContext(document, categories);
    idRef.current = id;
    return () => {
      removeDocumentContext(id);
    };
  }, [addDocumentContext, removeDocumentContext, ...dependencies]);
  return idRef.current;
}

export {
  useMakeCopilotDocumentReadable
};
//# sourceMappingURL=chunk-GBAZTXPQ.mjs.map