{"name": "@graphql-yoga/logger", "version": "2.0.1", "description": "Simple logger", "sideEffects": true, "dependencies": {"tslib": "^2.8.1"}, "repository": {"type": "git", "url": "https://github.com/dotansimha/graphql-yoga.git", "directory": "packages/logger"}, "keywords": ["logger"], "license": "MIT", "engines": {"node": ">=18.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./typings/*.d.cts", "default": "./cjs/*.js"}, "import": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}, "default": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}}, "./package.json": "./package.json"}}