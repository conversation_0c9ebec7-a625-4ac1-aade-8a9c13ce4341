{"version": 3, "sources": ["../../src/lib/utils.ts"], "sourcesContent": ["export async function fetcher<JSON = any>(input: RequestInfo, init?: RequestInit): Promise<JSON> {\n  const res = await fetch(input, init);\n\n  if (!res.ok) {\n    const json = await res.json();\n    if (json.error) {\n      const error = new Error(json.error) as Error & {\n        status: number;\n      };\n      error.status = res.status;\n      throw error;\n    } else {\n      throw new Error(\"An unexpected error occurred\");\n    }\n  }\n\n  return res.json();\n}\n\nexport function formatDate(input: string | number | Date): string {\n  const date = new Date(input);\n  return date.toLocaleDateString(\"en-US\", {\n    month: \"long\",\n    day: \"numeric\",\n    year: \"numeric\",\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAsB,QAAoB,OAAoB,MAAmC;AAAA;AAC/F,UAAM,MAAM,MAAM,MAAM,OAAO,IAAI;AAEnC,QAAI,CAAC,IAAI,IAAI;AACX,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,UAAI,KAAK,OAAO;AACd,cAAM,QAAQ,IAAI,MAAM,KAAK,KAAK;AAGlC,cAAM,SAAS,IAAI;AACnB,cAAM;AAAA,MACR,OAAO;AACL,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AAAA,IACF;AAEA,WAAO,IAAI,KAAK;AAAA,EAClB;AAAA;AAEO,SAAS,WAAW,OAAuC;AAChE,QAAM,OAAO,IAAI,KAAK,KAAK;AAC3B,SAAO,KAAK,mBAAmB,SAAS;AAAA,IACtC,OAAO;AAAA,IACP,KAAK;AAAA,IACL,MAAM;AAAA,EACR,CAAC;AACH;", "names": []}