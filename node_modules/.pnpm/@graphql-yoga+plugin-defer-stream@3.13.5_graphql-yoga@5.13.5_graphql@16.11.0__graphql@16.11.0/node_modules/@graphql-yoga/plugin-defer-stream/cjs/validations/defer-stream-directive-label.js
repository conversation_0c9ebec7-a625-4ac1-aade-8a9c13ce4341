"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeferStreamDirectiveLabelRule = DeferStreamDirectiveLabelRule;
const graphql_1 = require("graphql");
const graphql_yoga_1 = require("graphql-yoga");
const utils_1 = require("@graphql-tools/utils");
/**
 * Stream directive on list field
 *
 * A GraphQL document is only valid if defer and stream directives' label argument is static and unique.
 */
function DeferStreamDirectiveLabelRule(context) {
    const knownLabels = Object.create(null);
    return {
        Directive(node) {
            if (node.name.value === utils_1.GraphQLDeferDirective.name ||
                node.name.value === utils_1.GraphQLStreamDirective.name) {
                const labelArgument = node.arguments?.find(arg => arg.name.value === 'label');
                const labelValue = labelArgument?.value;
                if (!labelValue) {
                    return;
                }
                if (labelValue.kind !== graphql_1.Kind.STRING) {
                    context.reportError((0, graphql_yoga_1.createGraphQLError)(`Directive "${node.name.value}"'s label argument must be a static string.`, { nodes: node }));
                }
                else if (knownLabels[labelValue.value]) {
                    context.reportError((0, graphql_yoga_1.createGraphQLError)('Defer/Stream directive label argument must be unique.', {
                        nodes: [knownLabels[labelValue.value], node],
                    }));
                }
                else {
                    knownLabels[labelValue.value] = node;
                }
            }
        },
    };
}
