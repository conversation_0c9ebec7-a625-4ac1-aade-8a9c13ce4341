import {
  <PERSON><PERSON><PERSON><PERSON>
} from "./chunk-PMAFHQ7P.mjs";
import {
  UsageBanner,
  renderCopilotKitUsage
} from "./chunk-D34OH4VN.mjs";
import {
  useErrorToast
} from "./chunk-22ENANUU.mjs";

// src/components/error-boundary/error-boundary.tsx
import React, { useEffect } from "react";
import { CopilotKitError } from "@copilotkit/shared";
import { COPILOT_CLOUD_ERROR_NAMES } from "@copilotkit/shared";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var statusChecker = new StatusChecker();
var CopilotErrorBoundary = class extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false
    };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  componentDidMount() {
    if (this.props.publicApiKey) {
      statusChecker.start(this.props.publicApiKey, (newStatus) => {
        this.setState((prevState) => {
          var _a;
          if ((newStatus == null ? void 0 : newStatus.severity) !== ((_a = prevState.status) == null ? void 0 : _a.severity)) {
            return { status: newStatus != null ? newStatus : void 0 };
          }
          return null;
        });
      });
    }
  }
  componentWillUnmount() {
    statusChecker.stop();
  }
  componentDidCatch(error, errorInfo) {
    console.error("CopilotKit Error:", error, errorInfo);
  }
  render() {
    var _a, _b;
    if (this.state.hasError) {
      if (this.state.error instanceof CopilotKitError) {
        if (COPILOT_CLOUD_ERROR_NAMES.includes(this.state.error.name)) {
          return /* @__PURE__ */ jsx(ErrorToast, { error: this.state.error, children: renderCopilotKitUsage(this.state.error) });
        }
        return /* @__PURE__ */ jsxs(Fragment, { children: [
          this.props.children,
          this.props.showUsageBanner && /* @__PURE__ */ jsx(
            UsageBanner,
            {
              severity: (_a = this.state.status) == null ? void 0 : _a.severity,
              message: (_b = this.state.status) == null ? void 0 : _b.message
            }
          )
        ] });
      }
      throw this.state.error;
    }
    return this.props.children;
  }
};
function ErrorToast({ error, children }) {
  const addErrorToast = useErrorToast();
  useEffect(() => {
    if (error) {
      addErrorToast([error]);
    }
  }, [error, addErrorToast]);
  if (!error)
    throw error;
  return children;
}

export {
  CopilotErrorBoundary,
  ErrorToast
};
//# sourceMappingURL=chunk-534J55RX.mjs.map