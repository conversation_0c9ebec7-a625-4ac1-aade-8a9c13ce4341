{"version": 3, "sources": ["../src/components/chat/messages/RenderTextMessage.tsx"], "sourcesContent": ["import { RenderMessageProps } from \"../props\";\nimport { UserMessage as DefaultUserMessage } from \"./UserMessage\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderTextMessage({\n  UserMessage = DefaultUserMessage,\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const {\n    message,\n    inProgress,\n    index,\n    isCurrentMessage,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n    markdownTagRenderers,\n  } = props;\n\n  if (message.isTextMessage()) {\n    if (message.role === \"user\") {\n      return (\n        <UserMessage\n          key={index}\n          data-message-role=\"user\"\n          message={message.content}\n          rawData={message}\n        />\n      );\n    } else if (message.role == \"assistant\") {\n      return (\n        <AssistantMessage\n          key={index}\n          data-message-role=\"assistant\"\n          message={message.content}\n          rawData={message}\n          isLoading={inProgress && isCurrentMessage && !message.content}\n          isGenerating={inProgress && isCurrentMessage && !!message.content}\n          isCurrentMessage={isCurrentMessage}\n          onRegenerate={() => onRegenerate?.(message.id)}\n          onCopy={onCopy}\n          onThumbsUp={onThumbsUp}\n          onThumbsDown={onThumbsDown}\n          markdownTagRenderers={markdownTagRenderers}\n        />\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAwBQ;AApBD,SAAS,kBAAkB,IAIX;AAJW,eAChC;AAAA,iBAAAA,eAAc;AAAA,IACd,kBAAAC,oBAAmB;AAAA,EANrB,IAIkC,IAG7B,kBAH6B,IAG7B;AAAA,IAFH;AAAA,IACA;AAAA;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,QAAQ,cAAc,GAAG;AAC3B,QAAI,QAAQ,SAAS,QAAQ;AAC3B,aACE;AAAA,QAACD;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,SAAS;AAAA;AAAA,QAHJ;AAAA,MAIP;AAAA,IAEJ,WAAW,QAAQ,QAAQ,aAAa;AACtC,aACE;AAAA,QAACC;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,SAAS;AAAA,UACT,WAAW,cAAc,oBAAoB,CAAC,QAAQ;AAAA,UACtD,cAAc,cAAc,oBAAoB,CAAC,CAAC,QAAQ;AAAA,UAC1D;AAAA,UACA,cAAc,MAAM,6CAAe,QAAQ;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,QAXK;AAAA,MAYP;AAAA,IAEJ;AAAA,EACF;AACF;", "names": ["UserMessage", "AssistantM<PERSON><PERSON>"]}