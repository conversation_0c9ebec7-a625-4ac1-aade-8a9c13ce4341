{"version": 3, "sources": ["../../src/context/copilot-messages-context.tsx"], "sourcesContent": ["/**\n * An internal context to separate the messages state (which is constantly changing) from the rest of CopilotKit context\n */\n\nimport { Message } from \"@copilotkit/runtime-client-gql\";\nimport React from \"react\";\n\nexport interface CopilotMessagesContextParams {\n  messages: Message[];\n  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;\n}\n\nconst emptyCopilotContext: CopilotMessagesContextParams = {\n  messages: [],\n  setMessages: () => [],\n};\n\nexport const CopilotMessagesContext =\n  React.createContext<CopilotMessagesContextParams>(emptyCopilotContext);\n\nexport function useCopilotMessagesContext(): CopilotMessagesContextParams {\n  const context = React.useContext(CopilotMessagesContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\n      \"A messages consuming component was not wrapped with `<CopilotMessages> {...} </CopilotMessages>`\",\n    );\n  }\n  return context;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,mBAAkB;AAOlB,IAAM,sBAAoD;AAAA,EACxD,UAAU,CAAC;AAAA,EACX,aAAa,MAAM,CAAC;AACtB;AAEO,IAAM,yBACX,aAAAA,QAAM,cAA4C,mBAAmB;AAEhE,SAAS,4BAA0D;AACxE,QAAM,UAAU,aAAAA,QAAM,WAAW,sBAAsB;AACvD,MAAI,YAAY,qBAAqB;AACnC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;", "names": ["React"]}