// Auto-generated by `scripts/create-entrypoints.js`. Do not edit manually.
export const optionalImportEntrypoints = [
    "langchain_community/tools/aws_lambda",
    "langchain_community/tools/aws_sfn",
    "langchain_community/tools/discord",
    "langchain_community/tools/gmail",
    "langchain_community/tools/google_calendar",
    "langchain_community/agents/toolkits/aws_sfn",
    "langchain_community/embeddings/bedrock",
    "langchain_community/embeddings/cloudflare_workersai",
    "langchain_community/embeddings/cohere",
    "langchain_community/embeddings/googlepalm",
    "langchain_community/embeddings/googlevertexai",
    "langchain_community/embeddings/gradient_ai",
    "langchain_community/embeddings/hf",
    "langchain_community/embeddings/hf_transformers",
    "langchain_community/embeddings/llama_cpp",
    "langchain_community/embeddings/tensorflow",
    "langchain_community/llms/bedrock",
    "langchain_community/llms/bedrock/web",
    "langchain_community/llms/cohere",
    "langchain_community/llms/friendli",
    "langchain_community/llms/googlepalm",
    "langchain_community/llms/googlevertexai",
    "langchain_community/llms/googlevertexai/web",
    "langchain_community/llms/gradient_ai",
    "langchain_community/llms/hf",
    "langchain_community/llms/llama_cpp",
    "langchain_community/llms/portkey",
    "langchain_community/llms/raycast",
    "langchain_community/llms/replicate",
    "langchain_community/llms/sagemaker_endpoint",
    "langchain_community/llms/watsonx_ai",
    "langchain_community/llms/writer",
    "langchain_community/vectorstores/analyticdb",
    "langchain_community/vectorstores/astradb",
    "langchain_community/vectorstores/azure_aisearch",
    "langchain_community/vectorstores/azure_cosmosdb",
    "langchain_community/vectorstores/cassandra",
    "langchain_community/vectorstores/chroma",
    "langchain_community/vectorstores/clickhouse",
    "langchain_community/vectorstores/closevector/node",
    "langchain_community/vectorstores/closevector/web",
    "langchain_community/vectorstores/cloudflare_vectorize",
    "langchain_community/vectorstores/convex",
    "langchain_community/vectorstores/couchbase",
    "langchain_community/vectorstores/elasticsearch",
    "langchain_community/vectorstores/faiss",
    "langchain_community/vectorstores/googlevertexai",
    "langchain_community/vectorstores/hnswlib",
    "langchain_community/vectorstores/lancedb",
    "langchain_community/vectorstores/milvus",
    "langchain_community/vectorstores/momento_vector_index",
    "langchain_community/vectorstores/mongodb_atlas",
    "langchain_community/vectorstores/myscale",
    "langchain_community/vectorstores/neo4j_vector",
    "langchain_community/vectorstores/opensearch",
    "langchain_community/vectorstores/pgvector",
    "langchain_community/vectorstores/pinecone",
    "langchain_community/vectorstores/qdrant",
    "langchain_community/vectorstores/redis",
    "langchain_community/vectorstores/rockset",
    "langchain_community/vectorstores/singlestore",
    "langchain_community/vectorstores/supabase",
    "langchain_community/vectorstores/tigris",
    "langchain_community/vectorstores/typeorm",
    "langchain_community/vectorstores/typesense",
    "langchain_community/vectorstores/usearch",
    "langchain_community/vectorstores/vercel_postgres",
    "langchain_community/vectorstores/voy",
    "langchain_community/vectorstores/weaviate",
    "langchain_community/vectorstores/xata",
    "langchain_community/vectorstores/zep",
    "langchain_community/chat_models/bedrock",
    "langchain_community/chat_models/bedrock/web",
    "langchain_community/chat_models/friendli",
    "langchain_community/chat_models/googlevertexai",
    "langchain_community/chat_models/googlevertexai/web",
    "langchain_community/chat_models/googlepalm",
    "langchain_community/chat_models/iflytek_xinghuo",
    "langchain_community/chat_models/iflytek_xinghuo/web",
    "langchain_community/chat_models/llama_cpp",
    "langchain_community/chat_models/portkey",
    "langchain_community/chat_models/premai",
    "langchain_community/chat_models/webllm",
    "langchain_community/chat_models/zhipuai",
    "langchain_community/callbacks/handlers/llmonitor",
    "langchain_community/callbacks/handlers/lunary",
    "langchain_community/retrievers/amazon_kendra",
    "langchain_community/retrievers/amazon_knowledge_base",
    "langchain_community/retrievers/metal",
    "langchain_community/retrievers/supabase",
    "langchain_community/retrievers/vectara_summary",
    "langchain_community/retrievers/zep",
    "langchain_community/graphs/neo4j_graph",
    "langchain_community/graphs/memgraph_graph",
    "langchain_community/document_transformers/html_to_text",
    "langchain_community/document_transformers/mozilla_readability",
    "langchain_community/storage/convex",
    "langchain_community/storage/ioredis",
    "langchain_community/storage/upstash_redis",
    "langchain_community/storage/vercel_kv",
    "langchain_community/stores/message/cassandra",
    "langchain_community/stores/message/cloudflare_d1",
    "langchain_community/stores/message/convex",
    "langchain_community/stores/message/dynamodb",
    "langchain_community/stores/message/firestore",
    "langchain_community/stores/message/ioredis",
    "langchain_community/stores/message/momento",
    "langchain_community/stores/message/mongodb",
    "langchain_community/stores/message/planetscale",
    "langchain_community/stores/message/redis",
    "langchain_community/stores/message/upstash_redis",
    "langchain_community/stores/message/xata",
    "langchain_community/memory/motorhead_memory",
    "langchain_community/memory/zep",
    "langchain_community/indexes/postgres",
    "langchain_community/util/convex",
];
