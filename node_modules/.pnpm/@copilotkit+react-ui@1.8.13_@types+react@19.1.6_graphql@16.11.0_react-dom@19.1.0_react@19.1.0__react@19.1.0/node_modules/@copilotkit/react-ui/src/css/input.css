.copilotKitInput {
  cursor: text;
  position: relative;
  background-color: var(--copilot-kit-input-background-color);
  border-radius: 20px;
  border: 1px solid var(--copilot-kit-separator-color);
  padding: 12px 14px;
  min-height: 75px;
  margin: 0 auto;
  width: 95%;
}

.copilotKitInputContainer {
  width: 100%;
  padding: 0;
  padding-bottom: 15px;
  background: var(--copilot-kit-background-color);
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

.copilotKitSidebar .copilotKitInputContainer {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.copilotKitInputControlButton {
  padding: 0;
  cursor: pointer;
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  transform: scale(1);
  color: rgba(0, 0, 0, 0.25);
  -webkit-appearance: button;
  appearance: button;
  background-color: transparent;
  background-image: none;
  text-transform: none;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  border: 0;
  margin: 0;
  text-indent: 0px;
  text-shadow: none;
  display: inline-block;
  text-align: center;
  width: 24px;
  height: 24px;
}

.copilotKitInputControlButton:not([disabled]) {
  color: var(--copilot-kit-primary-color);
}

.copilotKitInputControlButton:not([disabled]):hover {
  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);
  transform: scale(1.05);
}

.copilotKitInputControlButton[disabled] {
  color: var(--copilot-kit-muted-color);
  cursor: default;
}

.copilotKitInputControls {
  display: flex;
  gap: 3px;
}

.copilotKitInput > textarea {
  flex: 1; /* Allow textarea to take up remaining space */
  outline: 2px solid transparent;
  outline-offset: 2px;
  resize: none;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: text;
  font-size: 0.875rem;
  line-height: 1.5rem;
  margin: 0;
  padding: 0;
  font-family: inherit;
  font-weight: inherit;
  color: var(--copilot-kit-secondary-contrast-color);
  border: 0px;
  background-color: transparent;
  width: 100%;
}

.copilotKitInput > textarea::placeholder {
  color: #808080;
  opacity: 1;
}

.copilotKitInputControlButton.copilotKitPushToTalkRecording {
  background-color: #ec0000;
  color: white;
  border-radius: 50%;
  animation: copilotKitPulseAnimation 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Scrollbar styles */
.copilotKitInput textarea::-webkit-scrollbar {
  width: 9px; /* Width of the entire scrollbar */
}

.copilotKitInput textarea::-webkit-scrollbar-track {
  background: transparent; /* Color of the tracking area */
}

.copilotKitInput textarea::-webkit-scrollbar-thumb {
  background-color: rgb(200 200 200); /* Color of the scroll thumb */
  border-radius: 10px; /* Roundness of the scroll thumb */
  border: 2px solid transparent; /* Creates padding around scroll thumb */
  background-clip: content-box;
  cursor: pointer;
}

.copilotKitInput textarea::-webkit-scrollbar-thumb:hover {
  background-color: color-mix(in srgb, rgb(200 200 200) 80%, black); /* Darker color on hover */
}

.poweredByContainer {
  padding: 0;
}

.poweredBy {
  background: var(--copilot-kit-background-color) !important;
  visibility: visible !important;
  display: block !important;
  position: static !important;
  text-align: center !important;
  font-size: 12px !important;
  padding: 3px 0 !important;
  color: rgb(214, 214, 214) !important;
  margin: 0 !important;
}

.dark,
html.dark,
body.dark,
[data-theme="dark"],
html[style*="color-scheme: dark"],
body[style*="color-scheme: dark"] .poweredBy {
  color: rgb(69, 69, 69) !important;
}

