{"version": 3, "sources": ["../../src/hooks/index.ts", "../../src/hooks/use-copilot-chat-suggestions.tsx"], "sourcesContent": ["export * from \"./use-copilot-chat-suggestions\";\n", "/**\n * <Callout type=\"warning\">\n *   useCopilotChatSuggestions is experimental. The interface is not final and\n *   can change without notice.\n * </Callout>\n *\n * `useCopilotReadable` is a React hook that provides app-state and other information\n * to the Copilot. Optionally, the hook can also handle hierarchical state within your\n * application, passing these parent-child relationships to the Copilot.\n *\n * <br/>\n * <img src=\"/images/use-copilot-chat-suggestions/use-copilot-chat-suggestions.gif\" width=\"500\" />\n *\n * ## Usage\n *\n * ### Install Dependencies\n *\n * This component is part of the [@copilotkit/react-ui](https://npmjs.com/package/@copilotkit/react-ui) package.\n *\n * ```shell npm2yarn \\\"@copilotkit/react-ui\"\\\n * npm install @copilotkit/react-core @copilotkit/react-ui\n * ```\n *\n * ### Simple Usage\n *\n * ```tsx\n * import { useCopilotChatSuggestions } from \"@copilotkit/react-ui\";\n *\n * export function MyComponent() {\n *   const [employees, setEmployees] = useState([]);\n *\n *   useCopilotChatSuggestions({\n *     instructions: `The following employees are on duty: ${JSON.stringify(employees)}`,\n *   });\n * }\n * ```\n *\n * ### Dependency Management\n *\n * ```tsx\n * import { useCopilotChatSuggestions } from \"@copilotkit/react-ui\";\n *\n * export function MyComponent() {\n *   useCopilotChatSuggestions(\n *     {\n *       instructions: \"Suggest the most relevant next actions.\",\n *     },\n *     [appState],\n *   );\n * }\n * ```\n *\n * In the example above, the suggestions are generated based on the given instructions.\n * The hook monitors `appState`, and updates suggestions accordingly whenever it changes.\n *\n * ### Behavior and Lifecycle\n *\n * The hook registers the configuration with the chat context upon component mount and\n * removes it on unmount, ensuring a clean and efficient lifecycle management.\n */\n\nimport { useEffect } from \"react\";\nimport { useCopilotContext } from \"@copilotkit/react-core\";\nimport { randomId } from \"@copilotkit/shared\";\n\ninterface UseCopilotChatSuggestionsConfiguration {\n  /**\n   * A prompt or instructions for the GPT to generate suggestions.\n   */\n  instructions: string;\n  /**\n   * The minimum number of suggestions to generate. Defaults to `1`.\n   * @default 1\n   */\n  minSuggestions?: number;\n  /**\n   * The maximum number of suggestions to generate. Defaults to `3`.\n   * @default 1\n   */\n  maxSuggestions?: number;\n\n  /**\n   * Whether the suggestions are available. Defaults to `enabled`.\n   * @default enabled\n   */\n  available?: \"enabled\" | \"disabled\";\n\n  /**\n   * An optional class name to apply to the suggestions.\n   */\n  className?: string;\n}\n\nexport function useCopilotChatSuggestions(\n  {\n    available = \"enabled\",\n    instructions,\n    className,\n    minSuggestions = 1,\n    maxSuggestions = 3,\n  }: UseCopilotChatSuggestionsConfiguration,\n  dependencies: any[] = [],\n) {\n  const context = useCopilotContext();\n\n  useEffect(() => {\n    if (available === \"disabled\") return;\n\n    const id = randomId();\n\n    context.addChatSuggestionConfiguration(id, {\n      instructions,\n      minSuggestions,\n      maxSuggestions,\n      className,\n    });\n\n    return () => {\n      context.removeChatSuggestionConfiguration(id);\n    };\n  }, [...dependencies, instructions, minSuggestions, maxSuggestions, className, available]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;AC6DA,mBAA0B;AAC1B,wBAAkC;AAClC,oBAAyB;AA8BlB,SAAS,0BACd;AAAA,EACE,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,iBAAiB;AACnB,GACA,eAAsB,CAAC,GACvB;AACA,QAAM,cAAU,qCAAkB;AAElC,8BAAU,MAAM;AACd,QAAI,cAAc;AAAY;AAE9B,UAAM,SAAK,wBAAS;AAEpB,YAAQ,+BAA+B,IAAI;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,WAAO,MAAM;AACX,cAAQ,kCAAkC,EAAE;AAAA,IAC9C;AAAA,EACF,GAAG,CAAC,GAAG,cAAc,cAAc,gBAAgB,gBAAgB,WAAW,SAAS,CAAC;AAC1F;", "names": []}