/**
 * Comprehensive Permissions System Tests
 * 
 * Tests for role-based access control, permissions checking,
 * tenant isolation, and super admin functionality.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  hasRole,
  isSuperAdmin,
  isAuthenticated,
  isAuthenticatedLegacy,
  checkPermission,
  getUserPermissions,
  canAccessTenant,
  hasRoleLegacy
} from '../permissions';
import { AuthUser, UserRole, SUPER_ADMIN_EMAILS } from '../types';

describe('Permissions System', () => {
  // Test user fixtures
  const createTestUser = (overrides: Partial<AuthUser> = {}): AuthUser => ({
    id: 'user-123',
    email: '<EMAIL>',
    role: UserRole.Attorney,
    tenantId: 'tenant-456',
    metadata: {},
    ...overrides
  });

  describe('hasRole', () => {
    it('should return true when user has the specified role', () => {
      const user = createTestUser({ role: UserRole.Attorney });
      expect(hasRole(user, [UserRole.Attorney])).toBe(true);
    });

    it('should return true when user has one of multiple specified roles', () => {
      const user = createTestUser({ role: UserRole.Partner });
      expect(hasRole(user, [UserRole.Attorney, UserRole.Partner])).toBe(true);
    });

    it('should return false when user does not have any specified roles', () => {
      const user = createTestUser({ role: UserRole.Staff });
      expect(hasRole(user, [UserRole.Attorney, UserRole.Partner])).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasRole(null, [UserRole.Attorney])).toBe(false);
    });

    it('should check metadata.roles array for legacy support', () => {
      const user = createTestUser({
        role: UserRole.Staff,
        metadata: { roles: ['attorney', 'partner'] }
      });
      expect(hasRole(user, [UserRole.Attorney])).toBe(true);
    });

    it('should prioritize primary role over metadata roles', () => {
      const user = createTestUser({
        role: UserRole.Partner,
        metadata: { roles: ['staff'] }
      });
      expect(hasRole(user, [UserRole.Partner])).toBe(true);
    });
  });

  describe('isSuperAdmin', () => {
    it('should return true for user with superadmin role', () => {
      const user = createTestUser({ role: UserRole.Superadmin });
      expect(isSuperAdmin(user)).toBe(true);
    });

    it('should return true for user with email in SUPER_ADMIN_EMAILS', () => {
      const superAdminEmail = SUPER_ADMIN_EMAILS[0];
      const user = createTestUser({ 
        email: superAdminEmail,
        role: UserRole.Attorney 
      });
      expect(isSuperAdmin(user)).toBe(true);
    });

    it('should return true for user with superadmin in metadata roles', () => {
      const user = createTestUser({
        role: UserRole.Attorney,
        metadata: { roles: ['superadmin'] }
      });
      expect(isSuperAdmin(user)).toBe(true);
    });

    it('should return false for regular user', () => {
      const user = createTestUser({ role: UserRole.Attorney });
      expect(isSuperAdmin(user)).toBe(false);
    });

    it('should return false for null user', () => {
      expect(isSuperAdmin(null)).toBe(false);
    });

    it('should return false for user without email', () => {
      const user = createTestUser({ 
        email: undefined,
        role: UserRole.Attorney 
      });
      expect(isSuperAdmin(user)).toBe(false);
    });
  });

  describe('isAuthenticated', () => {
    it('should return true for valid user with ID', () => {
      const user = createTestUser();
      expect(isAuthenticated(user)).toBe(true);
    });

    it('should return false for null user', () => {
      expect(isAuthenticated(null)).toBe(false);
    });

    it('should return false for user without ID', () => {
      const user = createTestUser({ id: undefined as any });
      expect(isAuthenticated(user)).toBe(false);
    });

    it('should return false for user with empty ID', () => {
      const user = createTestUser({ id: '' });
      expect(isAuthenticated(user)).toBe(false);
    });
  });

  describe('isAuthenticatedLegacy', () => {
    it('should return true for valid Supabase user with ID', () => {
      const supabaseUser = { id: 'user-123', email: '<EMAIL>' };
      expect(isAuthenticatedLegacy(supabaseUser)).toBe(true);
    });

    it('should return false for null user', () => {
      expect(isAuthenticatedLegacy(null)).toBe(false);
    });

    it('should return false for user without ID', () => {
      const supabaseUser = { email: '<EMAIL>' };
      expect(isAuthenticatedLegacy(supabaseUser)).toBe(false);
    });
  });

  describe('checkPermission', () => {
    it('should return true for super admin with any permission', () => {
      const user = createTestUser({ role: UserRole.Superadmin });
      expect(checkPermission(user, 'any:permission')).toBe(true);
    });

    it('should return true when user role has the permission', () => {
      const user = createTestUser({ role: UserRole.Attorney });
      expect(checkPermission(user, 'read:cases')).toBe(true);
      expect(checkPermission(user, 'write:cases')).toBe(true);
    });

    it('should return false when user role does not have the permission', () => {
      const user = createTestUser({ role: UserRole.Staff });
      expect(checkPermission(user, 'write:cases')).toBe(false);
      expect(checkPermission(user, 'delete:cases')).toBe(false);
    });

    it('should return false for null user', () => {
      expect(checkPermission(null, 'read:cases')).toBe(false);
    });

    it('should handle client role with resource-specific permissions', () => {
      const user = createTestUser({ role: UserRole.Client });
      expect(checkPermission(user, 'read:own_case', 'case', 'case-123')).toBe(true);
      expect(checkPermission(user, 'read:own_documents', 'document', 'doc-123')).toBe(true);
    });

    it('should return false for unknown role', () => {
      const user = createTestUser({ role: 'unknown' as UserRole });
      expect(checkPermission(user, 'read:cases')).toBe(false);
    });

    describe('role-specific permissions', () => {
      it('should validate Partner permissions', () => {
        const user = createTestUser({ role: UserRole.Partner });
        expect(checkPermission(user, 'read:cases')).toBe(true);
        expect(checkPermission(user, 'write:cases')).toBe(true);
        expect(checkPermission(user, 'delete:cases')).toBe(true);
        expect(checkPermission(user, 'admin:tenant')).toBe(true);
      });

      it('should validate Attorney permissions', () => {
        const user = createTestUser({ role: UserRole.Attorney });
        expect(checkPermission(user, 'read:cases')).toBe(true);
        expect(checkPermission(user, 'write:cases')).toBe(true);
        expect(checkPermission(user, 'delete:cases')).toBe(false);
        expect(checkPermission(user, 'admin:tenant')).toBe(false);
      });

      it('should validate Paralegal permissions', () => {
        const user = createTestUser({ role: UserRole.Paralegal });
        expect(checkPermission(user, 'read:cases')).toBe(true);
        expect(checkPermission(user, 'write:cases')).toBe(true);
        expect(checkPermission(user, 'delete:clients')).toBe(false);
      });

      it('should validate Staff permissions', () => {
        const user = createTestUser({ role: UserRole.Staff });
        expect(checkPermission(user, 'read:cases')).toBe(true);
        expect(checkPermission(user, 'write:cases')).toBe(false);
        expect(checkPermission(user, 'read:documents')).toBe(true);
      });
    });
  });

  describe('getUserPermissions', () => {
    it('should return all permissions for super admin', () => {
      const user = createTestUser({ role: UserRole.Superadmin });
      const permissions = getUserPermissions(user);
      expect(permissions).toEqual(['*']);
    });

    it('should return role-specific permissions for Attorney', () => {
      const user = createTestUser({ role: UserRole.Attorney });
      const permissions = getUserPermissions(user);
      expect(permissions).toContain('read:cases');
      expect(permissions).toContain('write:cases');
      expect(permissions).not.toContain('delete:cases');
    });

    it('should return empty array for null user', () => {
      const permissions = getUserPermissions(null);
      expect(permissions).toEqual([]);
    });

    it('should return empty array for unknown role', () => {
      const user = createTestUser({ role: 'unknown' as UserRole });
      const permissions = getUserPermissions(user);
      expect(permissions).toEqual([]);
    });
  });

  describe('canAccessTenant', () => {
    it('should return true when user belongs to the tenant', () => {
      const user = createTestUser({ tenantId: 'tenant-456' });
      expect(canAccessTenant(user, 'tenant-456')).toBe(true);
    });

    it('should return false when user belongs to different tenant', () => {
      const user = createTestUser({ tenantId: 'tenant-456' });
      expect(canAccessTenant(user, 'tenant-789')).toBe(false);
    });

    it('should return true for super admin accessing any tenant', () => {
      const user = createTestUser({ 
        role: UserRole.Superadmin,
        tenantId: 'tenant-456' 
      });
      expect(canAccessTenant(user, 'tenant-789')).toBe(true);
    });

    it('should return false for null user', () => {
      expect(canAccessTenant(null, 'tenant-456')).toBe(false);
    });
  });

  describe('hasRoleLegacy', () => {
    it('should check role from user.role property', () => {
      const supabaseUser = { 
        id: 'user-123',
        role: 'attorney' 
      };
      expect(hasRoleLegacy(supabaseUser, [UserRole.Attorney])).toBe(true);
    });

    it('should check role from user_metadata.role', () => {
      const supabaseUser = { 
        id: 'user-123',
        user_metadata: { role: 'partner' }
      };
      expect(hasRoleLegacy(supabaseUser, [UserRole.Partner])).toBe(true);
    });

    it('should check roles from user_metadata.roles array', () => {
      const supabaseUser = { 
        id: 'user-123',
        user_metadata: { roles: ['attorney', 'staff'] }
      };
      expect(hasRoleLegacy(supabaseUser, [UserRole.Attorney])).toBe(true);
    });

    it('should check roles from app_metadata.roles array', () => {
      const supabaseUser = { 
        id: 'user-123',
        app_metadata: { roles: ['partner'] }
      };
      expect(hasRoleLegacy(supabaseUser, [UserRole.Partner])).toBe(true);
    });

    it('should return false for null user', () => {
      expect(hasRoleLegacy(null, [UserRole.Attorney])).toBe(false);
    });

    it('should return false when user has no matching roles', () => {
      const supabaseUser = { 
        id: 'user-123',
        role: 'staff' 
      };
      expect(hasRoleLegacy(supabaseUser, [UserRole.Attorney, UserRole.Partner])).toBe(false);
    });
  });
});
