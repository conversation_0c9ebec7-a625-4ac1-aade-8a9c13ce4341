/**
 * Server-only Authentication Exports
 * 
 * This file exports server-side authentication functions that use next/headers
 * Import from '@/lib/auth/server-exports' in server components and API routes
 */

// Server client creation (server-only)
export {
  createClient,
  createServiceClient,
  createServerClientForUser
} from './server';

// Server session management
export {
  withAuth,
  withServiceRole,
  withAuthCallback,
  getServerSession,
  getUnifiedServerSession,
  getUser,
  requireAuth,
  createAuthUserFromSession
} from './session-server';

// Unified session management (server-only)
export * from './getUnifiedSession';

// Server permissions (client-safe functions)
export {
  hasRole,
  isSuperAdmin,
  isAuthenticated,
  checkPermission,
  getUserPermissions,
  canAccessTenant,
  hasRoleLegacy,
  isAuthenticatedLegacy
} from './permissions';

// Server permissions (server-only functions)
export {
  hasRoleUnified,
  isAuthenticatedUnified,
  getCurrentUserRoles
} from './permissions-server';

// JWT utilities (server-safe)
export {
  verifyJwt,
  verifyJWT,
  debugJwtClaims,
  parseClaims,
  extractTenantId,
  extractRole,
  isTokenExpired,
  getTokenTimeRemaining
} from './jwt';

// Types
export {
  UserRole,
  SUPER_ADMIN_EMAILS,
  isValidUserRole,
  type AuthUser,
  type TenantClaims,
  type AuthRouteHandler,
  type AuthResult,
  type SessionExt,
  type SuperAdminEmail
} from './types';
