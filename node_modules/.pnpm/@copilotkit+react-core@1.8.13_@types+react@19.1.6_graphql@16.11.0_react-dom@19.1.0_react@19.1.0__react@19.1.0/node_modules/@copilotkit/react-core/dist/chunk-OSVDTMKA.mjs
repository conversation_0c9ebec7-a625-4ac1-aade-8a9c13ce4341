import {
  useCopilotContext
} from "./chunk-7WG4MNRX.mjs";

// src/hooks/use-langgraph-interrupt-render.ts
import React, { useCallback } from "react";
var InterruptRenderer = ({ event, result, render, resolve }) => {
  return render({ event, result, resolve });
};
function useLangGraphInterruptRender() {
  const { langGraphInterruptAction, setLangGraphInterruptAction, agentSession } = useCopilotContext();
  const responseRef = React.useRef();
  const resolveInterrupt = useCallback(
    (response) => {
      responseRef.current = response;
      setTimeout(() => {
        setLangGraphInterruptAction({ event: { response } });
      }, 0);
    },
    [setLangGraphInterruptAction]
  );
  if (!langGraphInterruptAction || !langGraphInterruptAction.event || !langGraphInterruptAction.render)
    return null;
  const { render, handler, event, enabled } = langGraphInterruptAction;
  const conditionsMet = !agentSession || !enabled ? true : enabled({ eventValue: event.value, agentMetadata: agentSession });
  if (!conditionsMet) {
    return null;
  }
  let result = null;
  if (handler) {
    result = handler({
      event,
      resolve: resolveInterrupt
    });
  }
  return React.createElement(InterruptRenderer, {
    event,
    result,
    render,
    resolve: resolveInterrupt
  });
}

export {
  useLangGraphInterruptRender
};
//# sourceMappingURL=chunk-OSVDTMKA.mjs.map