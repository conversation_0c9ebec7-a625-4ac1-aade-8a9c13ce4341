import {
  useChatContext
} from "./chunk-IEMQ2SQW.mjs";

// src/components/chat/Messages.tsx
import { useEffect, useMemo, useRef } from "react";
import { ResultMessage, TextMessage, Role } from "@copilotkit/runtime-client-gql";
import { useLangGraphInterruptRender } from "@copilotkit/react-core";
import { jsx, jsxs } from "react/jsx-runtime";
var Messages = ({
  messages,
  inProgress,
  children,
  RenderTextMessage,
  RenderActionExecutionMessage,
  RenderAgentStateMessage,
  RenderResultMessage,
  RenderImageMessage,
  AssistantMessage,
  UserMessage,
  onRegenerate,
  onCopy,
  onThumbsUp,
  onThumbsDown,
  markdownTagRenderers
}) => {
  const context = useChatContext();
  const initialMessages = useMemo(
    () => makeInitialMessages(context.labels.initial),
    [context.labels.initial]
  );
  messages = [...initialMessages, ...messages];
  const actionResults = {};
  for (let i = 0; i < messages.length; i++) {
    if (messages[i].isActionExecutionMessage()) {
      const id = messages[i].id;
      const resultMessage = messages.find(
        (message) => message.isResultMessage() && message.actionExecutionId === id
      );
      if (resultMessage) {
        actionResults[id] = ResultMessage.decodeResult(resultMessage.result || "");
      }
    }
  }
  const { messagesContainerRef, messagesEndRef } = useScrollToBottom(messages);
  const interrupt = useLangGraphInterruptRender();
  return /* @__PURE__ */ jsxs("div", { className: "copilotKitMessages", ref: messagesContainerRef, children: [
    /* @__PURE__ */ jsxs("div", { className: "copilotKitMessagesContainer", children: [
      messages.map((message, index) => {
        const isCurrentMessage = index === messages.length - 1;
        if (message.isTextMessage()) {
          return /* @__PURE__ */ jsx(
            RenderTextMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage,
              onRegenerate,
              onCopy,
              onThumbsUp,
              onThumbsDown,
              markdownTagRenderers
            },
            index
          );
        } else if (message.isActionExecutionMessage()) {
          return /* @__PURE__ */ jsx(
            RenderActionExecutionMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              actionResult: actionResults[message.id],
              AssistantMessage,
              UserMessage
            },
            index
          );
        } else if (message.isAgentStateMessage()) {
          return /* @__PURE__ */ jsx(
            RenderAgentStateMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage
            },
            index
          );
        } else if (message.isResultMessage()) {
          return /* @__PURE__ */ jsx(
            RenderResultMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage
            },
            index
          );
        } else if (message.isImageMessage && message.isImageMessage()) {
          return /* @__PURE__ */ jsx(
            RenderImageMessage,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage,
              UserMessage,
              onRegenerate,
              onCopy,
              onThumbsUp,
              onThumbsDown
            },
            index
          );
        }
      }),
      interrupt
    ] }),
    /* @__PURE__ */ jsx("footer", { className: "copilotKitMessagesFooter", ref: messagesEndRef, children })
  ] });
};
function makeInitialMessages(initial) {
  let initialArray = [];
  if (initial) {
    if (Array.isArray(initial)) {
      initialArray.push(...initial);
    } else {
      initialArray.push(initial);
    }
  }
  return initialArray.map(
    (message) => new TextMessage({
      role: Role.Assistant,
      content: message
    })
  );
}
function useScrollToBottom(messages) {
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const isProgrammaticScrollRef = useRef(false);
  const isUserScrollUpRef = useRef(false);
  const scrollToBottom = () => {
    if (messagesContainerRef.current && messagesEndRef.current) {
      isProgrammaticScrollRef.current = true;
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };
  const handleScroll = () => {
    if (isProgrammaticScrollRef.current) {
      isProgrammaticScrollRef.current = false;
      return;
    }
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      isUserScrollUpRef.current = scrollTop + clientHeight < scrollHeight;
    }
  };
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) {
      return;
    }
    const mutationObserver = new MutationObserver(() => {
      if (!isUserScrollUpRef.current) {
        scrollToBottom();
      }
    });
    mutationObserver.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    });
    return () => {
      mutationObserver.disconnect();
    };
  }, []);
  useEffect(() => {
    isUserScrollUpRef.current = false;
    scrollToBottom();
  }, [messages.filter((m) => m.isTextMessage() && m.role === Role.User).length]);
  return { messagesEndRef, messagesContainerRef };
}

export {
  Messages,
  useScrollToBottom
};
//# sourceMappingURL=chunk-B3D7U7TJ.mjs.map