import {
  graphql
} from "./chunk-4KTMZMM2.mjs";

// src/graphql/definitions/queries.ts
var getAvailableAgentsQuery = graphql(
  /** GraphQL **/
  `
  query availableAgents {
    availableAgents {
      agents {
        name
        id
        description
      }
    }
  }
`
);
var loadAgentStateQuery = graphql(
  /** GraphQL **/
  `
  query loadAgentState($data: LoadAgentStateInput!) {
    loadAgentState(data: $data) {
      threadId
      threadExists
      state
      messages
    }
  }
`
);

export {
  getAvailableAgentsQuery,
  loadAgentStateQuery
};
//# sourceMappingURL=chunk-X2UAP3QY.mjs.map