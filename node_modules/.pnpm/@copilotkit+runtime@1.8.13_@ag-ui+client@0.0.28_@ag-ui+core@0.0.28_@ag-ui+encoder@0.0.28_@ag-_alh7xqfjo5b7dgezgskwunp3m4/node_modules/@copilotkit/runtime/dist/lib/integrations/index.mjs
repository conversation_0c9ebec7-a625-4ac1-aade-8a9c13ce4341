import {
  config,
  copilotRuntimeNextJSAppRouterEndpoint,
  copilotRuntimeNextJSPagesRouterEndpoint
} from "../../chunk-2HJVWDWR.mjs";
import {
  copilotRuntimeNestEndpoint
} from "../../chunk-35NR7QOB.mjs";
import {
  copilotRuntimeNodeExpressEndpoint
} from "../../chunk-HEMZD7QI.mjs";
import {
  addCustomHeaderPlugin,
  buildSchema,
  copilotRuntimeNodeHttpEndpoint,
  createContext,
  getCommonConfig
} from "../../chunk-XOH4CYV6.mjs";
import "../../chunk-MVKCCH5U.mjs";
import "../../chunk-5BIEM2UU.mjs";
import "../../chunk-SHBDMA63.mjs";
import "../../chunk-2OZAGFV3.mjs";
import "../../chunk-FHD4JECV.mjs";
export {
  addCustomHeaderPlugin,
  buildSchema,
  config,
  copilotRuntimeNestEndpoint,
  copilotRuntimeNextJSAppRouterEndpoint,
  copilotRuntimeNextJSPagesRouterEndpoint,
  copilotRuntimeNodeExpressEndpoint,
  copilotRuntimeNodeHttpEndpoint,
  createContext,
  getCommonConfig
};
//# sourceMappingURL=index.mjs.map