/**
 * Options for the useCopilotReadable hook.
 */
interface UseCopilotReadableOptions {
    /**
     * The description of the information to be added to the Copilot context.
     */
    description: string;
    /**
     * The value to be added to the Copilot context. Object values are automatically stringified.
     */
    value: any;
    /**
     * The ID of the parent context, if any.
     */
    parentId?: string;
    /**
     * An array of categories to control which context are visible where. Particularly useful
     * with CopilotTextarea (see `useMakeAutosuggestionFunction`)
     */
    categories?: string[];
    /**
     * Whether the context is available to the Copilot.
     */
    available?: "enabled" | "disabled";
    /**
     * A custom conversion function to use to serialize the value to a string. If not provided, the value
     * will be serialized using `JSON.stringify`.
     */
    convert?: (description: string, value: any) => string;
}
/**
 * Adds the given information to the Copilot context to make it readable by Copilot.
 */
declare function useCopilotReadable({ description, value, parentId, categories, convert, available, }: UseCopilotReadableOptions, dependencies?: any[]): string | undefined;

export { UseCopilotReadableOptions, useCopilotReadable };
