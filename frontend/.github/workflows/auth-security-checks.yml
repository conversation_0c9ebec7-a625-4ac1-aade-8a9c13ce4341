name: Authentication Security Checks

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/lib/auth/**'
      - 'src/app/api/**'
      - 'src/components/auth/**'
      - 'src/app/**/page.tsx'
      - 'src/app/**/layout.tsx'
  push:
    branches: [ main ]
    paths:
      - 'src/lib/auth/**'
      - 'src/app/api/**'

jobs:
  authentication-tests:
    name: Authentication Test Suite
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run authentication unit tests
        run: |
          npm run test -- src/lib/auth/__tests__/ --coverage --reporter=verbose
          
      - name: Run authentication integration tests
        run: |
          npm run test -- src/app/api/__tests__/ --coverage --reporter=verbose
          
      - name: Upload test coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: authentication
          name: auth-coverage

  typescript-checks:
    name: TypeScript Security Checks
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: TypeScript strict mode check
        run: npm run type-check:strict
        
      - name: TypeScript auth-specific check
        run: |
          npx tsc --noEmit --strict src/lib/auth/**/*.ts
          
      - name: Check for any types in auth code
        run: |
          if grep -r "any" src/lib/auth/ --include="*.ts" --include="*.tsx"; then
            echo "❌ Found 'any' types in authentication code"
            exit 1
          else
            echo "✅ No 'any' types found in authentication code"
          fi

  security-audit:
    name: Security Vulnerability Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run npm audit
        run: |
          npm audit --audit-level moderate
          
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium
          
      - name: Check for hardcoded secrets
        run: |
          # Check for potential secrets in auth code
          if grep -r -E "(password|secret|key|token).*=.*['\"][^'\"]{8,}" src/lib/auth/ --include="*.ts" --include="*.tsx"; then
            echo "❌ Potential hardcoded secrets found"
            exit 1
          else
            echo "✅ No hardcoded secrets detected"
          fi

  eslint-security:
    name: ESLint Security Rules
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint with security rules
        run: |
          npx eslint src/lib/auth/ --ext .ts,.tsx --config .eslintrc.security.js
          
      - name: Run custom authentication pattern checks
        run: |
          npx eslint src/app/api/ --ext .ts,.tsx --rule "custom/require-auth-in-api: error"
          npx eslint src/ --ext .ts,.tsx --rule "custom/require-schema-in-queries: error"

  authentication-patterns:
    name: Authentication Pattern Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check API route authentication
        run: |
          # Check that all API routes have authentication
          echo "🔍 Checking API routes for authentication patterns..."
          
          # Find API route files
          api_files=$(find src/app/api -name "route.ts" -o -name "route.tsx")
          
          for file in $api_files; do
            echo "Checking $file..."
            
            # Check for authentication patterns
            if grep -q "requireAuth\|validateJwt\|getSession" "$file"; then
              echo "✅ $file has authentication"
            else
              # Check if it's a public endpoint
              if grep -q "// @public\|// public endpoint" "$file"; then
                echo "ℹ️ $file is marked as public endpoint"
              else
                echo "❌ $file missing authentication check"
                exit 1
              fi
            fi
          done
          
      - name: Check database schema usage
        run: |
          # Check that database queries use proper schema
          echo "🔍 Checking database queries for schema specification..."
          
          # Find files with Supabase queries
          query_files=$(grep -r -l "\.from(" src/ --include="*.ts" --include="*.tsx")
          
          for file in $query_files; do
            echo "Checking $file..."
            
            # Check for schema specification
            if grep -q "\.schema(" "$file"; then
              echo "✅ $file uses schema specification"
            else
              echo "⚠️ $file may be missing schema specification"
              # Show the problematic lines
              grep -n "\.from(" "$file"
            fi
          done
          
      - name: Check tenant isolation
        run: |
          # Check that tenant queries include tenant_id filtering
          echo "🔍 Checking for tenant isolation in queries..."
          
          # Find tenant-specific table queries
          tenant_tables="cases|clients|users|activities|insights|tasks|documents"
          
          grep -r -n "\.from(['\"]($tenant_tables)['\"])" src/ --include="*.ts" --include="*.tsx" | while read -r line; do
            file=$(echo "$line" | cut -d: -f1)
            line_num=$(echo "$line" | cut -d: -f2)
            
            echo "Checking tenant query in $file:$line_num"
            
            # Check if tenant_id filtering is present in the same file
            if grep -q "tenant_id" "$file"; then
              echo "✅ $file includes tenant_id filtering"
            else
              echo "❌ $file missing tenant isolation"
              echo "$line"
            fi
          done

  performance-impact:
    name: Performance Impact Assessment
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build and analyze bundle
        run: |
          npm run build
          
      - name: Check bundle size impact
        run: |
          # Check if authentication changes significantly impact bundle size
          echo "📊 Analyzing bundle size impact..."
          
          # This would compare with main branch in a real scenario
          # For now, just check that auth code doesn't bloat the bundle
          if [ -f ".next/static/chunks/pages/_app-*.js" ]; then
            app_size=$(stat -c%s .next/static/chunks/pages/_app-*.js)
            echo "App bundle size: $app_size bytes"
            
            # Warn if bundle is unusually large (> 1MB)
            if [ $app_size -gt 1048576 ]; then
              echo "⚠️ App bundle size is large, check for unnecessary imports"
            fi
          fi

  documentation-check:
    name: Documentation Validation
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check documentation completeness
        run: |
          echo "📚 Checking authentication documentation..."
          
          # Check that key documentation files exist
          required_docs=(
            "docs/authentication.md"
            "docs/api-reference.md"
            "docs/troubleshooting.md"
            "docs/security-guide.md"
            "docs/code-review-standards.md"
          )
          
          for doc in "${required_docs[@]}"; do
            if [ -f "$doc" ]; then
              echo "✅ $doc exists"
            else
              echo "❌ Missing required documentation: $doc"
              exit 1
            fi
          done
          
      - name: Validate documentation links
        run: |
          # Check for broken internal links in documentation
          echo "🔗 Checking documentation links..."
          
          # This is a simplified check - in practice you'd use a proper link checker
          grep -r "\[.*\](\./" docs/ | while read -r line; do
            file=$(echo "$line" | cut -d: -f1)
            link=$(echo "$line" | sed -n 's/.*](\.\([^)]*\)).*/\1/p')
            
            if [ -n "$link" ] && [ ! -f "docs$link" ]; then
              echo "❌ Broken link in $file: $link"
            fi
          done

  security-summary:
    name: Security Check Summary
    runs-on: ubuntu-latest
    needs: [authentication-tests, typescript-checks, security-audit, eslint-security, authentication-patterns]
    if: always()
    
    steps:
      - name: Generate security summary
        run: |
          echo "## 🛡️ Authentication Security Check Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.authentication-tests.result }}" == "success" ]; then
            echo "✅ Authentication tests passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Authentication tests failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.typescript-checks.result }}" == "success" ]; then
            echo "✅ TypeScript security checks passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ TypeScript security checks failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.security-audit.result }}" == "success" ]; then
            echo "✅ Security audit passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Security audit failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.eslint-security.result }}" == "success" ]; then
            echo "✅ ESLint security rules passed" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ ESLint security rules failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.authentication-patterns.result }}" == "success" ]; then
            echo "✅ Authentication patterns validated" >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ Authentication pattern validation failed" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Review any failed checks above" >> $GITHUB_STEP_SUMMARY
          echo "- Ensure all security requirements are met" >> $GITHUB_STEP_SUMMARY
          echo "- Request security review if needed" >> $GITHUB_STEP_SUMMARY
