var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from3, except, desc) => {
  if (from3 && typeof from3 === "object" || typeof from3 === "function") {
    for (let key of __getOwnPropNames(from3))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from3[key], enumerable: !(desc = __getOwnPropDesc(from3, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// package.json
var require_package = __commonJS({
  "package.json"(exports, module2) {
    module2.exports = {
      name: "@copilotkit/runtime",
      private: false,
      homepage: "https://github.com/CopilotKit/CopilotKit",
      repository: {
        type: "git",
        url: "https://github.com/CopilotKit/CopilotKit.git"
      },
      publishConfig: {
        access: "public"
      },
      version: "1.8.13",
      sideEffects: false,
      main: "./dist/index.js",
      module: "./dist/index.mjs",
      exports: {
        ".": "./dist/index.js"
      },
      types: "./dist/index.d.ts",
      license: "MIT",
      scripts: {
        build: 'tsup --onSuccess "pnpm run generate-graphql-schema"',
        dev: 'tsup --watch --onSuccess "pnpm run generate-graphql-schema"',
        test: "jest --passWithNoTests",
        "check-types": "tsc --noEmit",
        clean: "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next && rm -rf __snapshots__",
        "generate-graphql-schema": "rm -rf __snapshots__ && ts-node ./scripts/generate-gql-schema.ts",
        "link:global": "pnpm link --global",
        "unlink:global": "pnpm unlink --global"
      },
      devDependencies: {
        "@jest/globals": "^29.7.0",
        "@swc/core": "1.5.28",
        "@types/express": "^4.17.21",
        "@types/jest": "^29.5.12",
        "@types/node": "^18.11.17",
        "@whatwg-node/server": "^0.9.34",
        eslint: "^8.56.0",
        "eslint-config-custom": "workspace:*",
        jest: "^29.6.4",
        nodemon: "^3.1.3",
        "ts-jest": "^29.1.1",
        "ts-node": "^10.9.2",
        tsconfig: "workspace:*",
        tsup: "^6.7.0",
        typescript: "^5.2.3",
        "zod-to-json-schema": "^3.23.5"
      },
      dependencies: {
        "@ag-ui/client": "0.0.28",
        "@ag-ui/core": "0.0.28",
        "@ag-ui/encoder": "0.0.28",
        "@ag-ui/proto": "0.0.28",
        "@anthropic-ai/sdk": "^0.27.3",
        "@copilotkit/shared": "workspace:*",
        "@graphql-yoga/plugin-defer-stream": "^3.3.1",
        "@langchain/community": "^0.3.29",
        "@langchain/core": "^0.3.38",
        "@langchain/google-gauth": "^0.1.0",
        "@langchain/langgraph-sdk": "^0.0.70",
        "@langchain/openai": "^0.4.2",
        "class-transformer": "^0.5.1",
        "class-validator": "^0.14.1",
        express: "^4.19.2",
        graphql: "^16.8.1",
        "graphql-scalars": "^1.23.0",
        "graphql-yoga": "^5.3.1",
        "groq-sdk": "^0.5.0",
        langchain: "^0.3.3",
        openai: "^4.85.1",
        "partial-json": "^0.1.7",
        pino: "^9.2.0",
        "pino-pretty": "^11.2.1",
        "reflect-metadata": "^0.2.2",
        rxjs: "^7.8.1",
        "type-graphql": "2.0.0-rc.1",
        zod: "^3.23.3"
      },
      peerDependencies: {
        "@ag-ui/client": ">=0.0.28",
        "@ag-ui/core": ">=0.0.28",
        "@ag-ui/encoder": ">=0.0.28",
        "@ag-ui/proto": ">=0.0.28"
      },
      keywords: [
        "copilotkit",
        "copilot",
        "react",
        "nextjs",
        "nodejs",
        "ai",
        "assistant",
        "javascript",
        "automation",
        "textarea"
      ]
    };
  }
});

// src/lib/index.ts
var lib_exports = {};
__export(lib_exports, {
  CopilotRuntime: () => CopilotRuntime,
  GoogleGenerativeAIAdapter: () => GoogleGenerativeAIAdapter,
  GroqAdapter: () => GroqAdapter,
  LangChainAdapter: () => LangChainAdapter,
  OpenAIAdapter: () => OpenAIAdapter,
  OpenAIAssistantAdapter: () => OpenAIAssistantAdapter,
  UnifyAdapter: () => UnifyAdapter,
  addCustomHeaderPlugin: () => addCustomHeaderPlugin,
  buildSchema: () => buildSchema,
  config: () => config,
  convertMCPToolsToActions: () => convertMCPToolsToActions,
  copilotKitEndpoint: () => copilotKitEndpoint,
  copilotRuntimeNestEndpoint: () => copilotRuntimeNestEndpoint,
  copilotRuntimeNextJSAppRouterEndpoint: () => copilotRuntimeNextJSAppRouterEndpoint,
  copilotRuntimeNextJSPagesRouterEndpoint: () => copilotRuntimeNextJSPagesRouterEndpoint,
  copilotRuntimeNodeExpressEndpoint: () => copilotRuntimeNodeExpressEndpoint,
  copilotRuntimeNodeHttpEndpoint: () => copilotRuntimeNodeHttpEndpoint,
  createContext: () => createContext,
  createLogger: () => createLogger,
  extractParametersFromSchema: () => extractParametersFromSchema,
  flattenToolCallsNoDuplicates: () => flattenToolCallsNoDuplicates,
  generateMcpToolInstructions: () => generateMcpToolInstructions,
  getCommonConfig: () => getCommonConfig,
  langGraphPlatformEndpoint: () => langGraphPlatformEndpoint,
  resolveEndpointType: () => resolveEndpointType
});
module.exports = __toCommonJS(lib_exports);

// src/service-adapters/openai/openai-adapter.ts
var import_openai = __toESM(require("openai"));

// src/service-adapters/openai/utils.ts
var import_shared = require("@copilotkit/shared");
function limitMessagesToTokenCount(messages, tools, model, maxTokens) {
  maxTokens || (maxTokens = maxTokensForOpenAIModel(model));
  const result = [];
  const toolsNumTokens = countToolsTokens(model, tools);
  if (toolsNumTokens > maxTokens) {
    throw new Error(`Too many tokens in function definitions: ${toolsNumTokens} > ${maxTokens}`);
  }
  maxTokens -= toolsNumTokens;
  for (const message of messages) {
    if ([
      "system",
      "developer"
    ].includes(message.role)) {
      const numTokens = countMessageTokens(model, message);
      maxTokens -= numTokens;
      if (maxTokens < 0) {
        throw new Error("Not enough tokens for system message.");
      }
    }
  }
  let cutoff = false;
  const reversedMessages = [
    ...messages
  ].reverse();
  for (const message of reversedMessages) {
    if ([
      "system",
      "developer"
    ].includes(message.role)) {
      result.unshift(message);
      continue;
    } else if (cutoff) {
      continue;
    }
    let numTokens = countMessageTokens(model, message);
    if (maxTokens < numTokens) {
      cutoff = true;
      continue;
    }
    result.unshift(message);
    maxTokens -= numTokens;
  }
  return result;
}
__name(limitMessagesToTokenCount, "limitMessagesToTokenCount");
function maxTokensForOpenAIModel(model) {
  return maxTokensByModel[model] || DEFAULT_MAX_TOKENS;
}
__name(maxTokensForOpenAIModel, "maxTokensForOpenAIModel");
var DEFAULT_MAX_TOKENS = 128e3;
var maxTokensByModel = {
  // o1
  o1: 2e5,
  "o1-2024-12-17": 2e5,
  "o1-mini": 128e3,
  "o1-mini-2024-09-12": 128e3,
  "o1-preview": 128e3,
  "o1-preview-2024-09-12": 128e3,
  // o3-mini
  "o3-mini": 2e5,
  "o3-mini-2025-01-31": 2e5,
  // GPT-4
  "gpt-4o": 128e3,
  "chatgpt-4o-latest": 128e3,
  "gpt-4o-2024-08-06": 128e3,
  "gpt-4o-2024-05-13": 128e3,
  "gpt-4o-mini": 128e3,
  "gpt-4o-mini-2024-07-18": 128e3,
  "gpt-4-turbo": 128e3,
  "gpt-4-turbo-2024-04-09": 128e3,
  "gpt-4-0125-preview": 128e3,
  "gpt-4-turbo-preview": 128e3,
  "gpt-4-1106-preview": 128e3,
  "gpt-4-vision-preview": 128e3,
  "gpt-4-1106-vision-preview": 128e3,
  "gpt-4-32k": 32768,
  "gpt-4-32k-0613": 32768,
  "gpt-4-32k-0314": 32768,
  "gpt-4": 8192,
  "gpt-4-0613": 8192,
  "gpt-4-0314": 8192,
  // GPT-3.5
  "gpt-3.5-turbo-0125": 16385,
  "gpt-3.5-turbo": 16385,
  "gpt-3.5-turbo-1106": 16385,
  "gpt-3.5-turbo-instruct": 4096,
  "gpt-3.5-turbo-16k": 16385,
  "gpt-3.5-turbo-0613": 4096,
  "gpt-3.5-turbo-16k-0613": 16385,
  "gpt-3.5-turbo-0301": 4097
};
function countToolsTokens(model, tools) {
  if (tools.length === 0) {
    return 0;
  }
  const json = JSON.stringify(tools);
  return countTokens(model, json);
}
__name(countToolsTokens, "countToolsTokens");
function countMessageTokens(model, message) {
  return countTokens(model, message.content || "");
}
__name(countMessageTokens, "countMessageTokens");
function countTokens(model, text) {
  return text.length / 3;
}
__name(countTokens, "countTokens");
function convertActionInputToOpenAITool(action) {
  return {
    type: "function",
    function: {
      name: action.name,
      description: action.description,
      parameters: (0, import_shared.parseJson)(action.jsonSchema, {})
    }
  };
}
__name(convertActionInputToOpenAITool, "convertActionInputToOpenAITool");
function convertMessageToOpenAIMessage(message, options) {
  const { keepSystemRole } = options || {
    keepSystemRole: false
  };
  if (message.isTextMessage()) {
    let role = message.role;
    if (message.role === "system" && !keepSystemRole) {
      role = "developer";
    }
    return {
      role,
      content: message.content
    };
  } else if (message.isImageMessage()) {
    return {
      role: "user",
      content: [
        {
          type: "image_url",
          image_url: {
            url: `data:image/${message.format};base64,${message.bytes}`
          }
        }
      ]
    };
  } else if (message.isActionExecutionMessage()) {
    return {
      role: "assistant",
      tool_calls: [
        {
          id: message.id,
          type: "function",
          function: {
            name: message.name,
            arguments: JSON.stringify(message.arguments)
          }
        }
      ]
    };
  } else if (message.isResultMessage()) {
    return {
      role: "tool",
      content: message.result,
      tool_call_id: message.actionExecutionId
    };
  }
}
__name(convertMessageToOpenAIMessage, "convertMessageToOpenAIMessage");
function convertSystemMessageToAssistantAPI(message) {
  return {
    ...message,
    ...[
      "system",
      "developer"
    ].includes(message.role) && {
      role: "assistant",
      content: "THE FOLLOWING MESSAGE IS A SYSTEM MESSAGE: " + message.content
    }
  };
}
__name(convertSystemMessageToAssistantAPI, "convertSystemMessageToAssistantAPI");

// src/service-adapters/openai/openai-adapter.ts
var import_shared2 = require("@copilotkit/shared");
var DEFAULT_MODEL = "gpt-4o";
var OpenAIAdapter = class {
  model = DEFAULT_MODEL;
  disableParallelToolCalls = false;
  _openai;
  keepSystemRole = false;
  get openai() {
    return this._openai;
  }
  constructor(params) {
    this._openai = (params == null ? void 0 : params.openai) || new import_openai.default({});
    if (params == null ? void 0 : params.model) {
      this.model = params.model;
    }
    this.disableParallelToolCalls = (params == null ? void 0 : params.disableParallelToolCalls) || false;
    this.keepSystemRole = (params == null ? void 0 : params.keepSystemRole) ?? false;
  }
  async process(request) {
    const { threadId: threadIdFromRequest, model = this.model, messages, actions, eventSource, forwardedParameters } = request;
    const tools = actions.map(convertActionInputToOpenAITool);
    const threadId = threadIdFromRequest ?? (0, import_shared2.randomUUID)();
    const validToolUseIds = /* @__PURE__ */ new Set();
    for (const message of messages) {
      if (message.isActionExecutionMessage()) {
        validToolUseIds.add(message.id);
      }
    }
    const filteredMessages = messages.filter((message) => {
      if (message.isResultMessage()) {
        if (!validToolUseIds.has(message.actionExecutionId)) {
          return false;
        }
        validToolUseIds.delete(message.actionExecutionId);
        return true;
      }
      return true;
    });
    let openaiMessages = filteredMessages.map((m) => convertMessageToOpenAIMessage(m, {
      keepSystemRole: this.keepSystemRole
    }));
    openaiMessages = limitMessagesToTokenCount(openaiMessages, tools, model);
    let toolChoice = forwardedParameters == null ? void 0 : forwardedParameters.toolChoice;
    if ((forwardedParameters == null ? void 0 : forwardedParameters.toolChoice) === "function") {
      toolChoice = {
        type: "function",
        function: {
          name: forwardedParameters.toolChoiceFunctionName
        }
      };
    }
    try {
      const stream = this.openai.beta.chat.completions.stream({
        model,
        stream: true,
        messages: openaiMessages,
        ...tools.length > 0 && {
          tools
        },
        ...(forwardedParameters == null ? void 0 : forwardedParameters.maxTokens) && {
          max_tokens: forwardedParameters.maxTokens
        },
        ...(forwardedParameters == null ? void 0 : forwardedParameters.stop) && {
          stop: forwardedParameters.stop
        },
        ...toolChoice && {
          tool_choice: toolChoice
        },
        ...this.disableParallelToolCalls && {
          parallel_tool_calls: false
        },
        ...(forwardedParameters == null ? void 0 : forwardedParameters.temperature) && {
          temperature: forwardedParameters.temperature
        }
      });
      eventSource.stream(async (eventStream$) => {
        var _a, _b;
        let mode = null;
        let currentMessageId;
        let currentToolCallId;
        try {
          for await (const chunk of stream) {
            if (chunk.choices.length === 0) {
              continue;
            }
            const toolCall = (_a = chunk.choices[0].delta.tool_calls) == null ? void 0 : _a[0];
            const content = chunk.choices[0].delta.content;
            if (mode === "message" && (toolCall == null ? void 0 : toolCall.id)) {
              mode = null;
              eventStream$.sendTextMessageEnd({
                messageId: currentMessageId
              });
            } else if (mode === "function" && (toolCall === void 0 || (toolCall == null ? void 0 : toolCall.id))) {
              mode = null;
              eventStream$.sendActionExecutionEnd({
                actionExecutionId: currentToolCallId
              });
            }
            if (mode === null) {
              if (toolCall == null ? void 0 : toolCall.id) {
                mode = "function";
                currentToolCallId = toolCall.id;
                eventStream$.sendActionExecutionStart({
                  actionExecutionId: currentToolCallId,
                  parentMessageId: chunk.id,
                  actionName: toolCall.function.name
                });
              } else if (content) {
                mode = "message";
                currentMessageId = chunk.id;
                eventStream$.sendTextMessageStart({
                  messageId: currentMessageId
                });
              }
            }
            if (mode === "message" && content) {
              eventStream$.sendTextMessageContent({
                messageId: currentMessageId,
                content
              });
            } else if (mode === "function" && ((_b = toolCall == null ? void 0 : toolCall.function) == null ? void 0 : _b.arguments)) {
              eventStream$.sendActionExecutionArgs({
                actionExecutionId: currentToolCallId,
                args: toolCall.function.arguments
              });
            }
          }
          if (mode === "message") {
            eventStream$.sendTextMessageEnd({
              messageId: currentMessageId
            });
          } else if (mode === "function") {
            eventStream$.sendActionExecutionEnd({
              actionExecutionId: currentToolCallId
            });
          }
        } catch (error) {
          console.error("[OpenAI] Error processing stream:", error);
          throw error;
        }
        eventStream$.complete();
      });
    } catch (error) {
      console.error("[OpenAI] Error during API call:", error);
      throw error;
    }
    return {
      threadId
    };
  }
};
__name(OpenAIAdapter, "OpenAIAdapter");

// src/service-adapters/langchain/utils.ts
var import_messages = require("@langchain/core/messages");
var import_tools = require("@langchain/core/tools");
var import_shared3 = require("@copilotkit/shared");
function convertMessageToLangChainMessage(message) {
  if (message.isTextMessage()) {
    if (message.role == "user") {
      return new import_messages.HumanMessage(message.content);
    } else if (message.role == "assistant") {
      return new import_messages.AIMessage(message.content);
    } else if (message.role === "system") {
      return new import_messages.SystemMessage(message.content);
    }
  } else if (message.isActionExecutionMessage()) {
    return new import_messages.AIMessage({
      content: "",
      tool_calls: [
        {
          id: message.id,
          args: message.arguments,
          name: message.name
        }
      ]
    });
  } else if (message.isResultMessage()) {
    return new import_messages.ToolMessage({
      content: message.result,
      tool_call_id: message.actionExecutionId
    });
  }
}
__name(convertMessageToLangChainMessage, "convertMessageToLangChainMessage");
function convertActionInputToLangChainTool(actionInput) {
  return new import_tools.DynamicStructuredTool({
    name: actionInput.name,
    description: actionInput.description,
    schema: (0, import_shared3.convertJsonSchemaToZodSchema)(JSON.parse(actionInput.jsonSchema), true),
    func: async () => {
      return "";
    }
  });
}
__name(convertActionInputToLangChainTool, "convertActionInputToLangChainTool");
function isAIMessage(message) {
  return Object.prototype.toString.call(message) === "[object AIMessage]";
}
__name(isAIMessage, "isAIMessage");
function isAIMessageChunk(message) {
  return Object.prototype.toString.call(message) === "[object AIMessageChunk]";
}
__name(isAIMessageChunk, "isAIMessageChunk");
function isBaseMessageChunk(message) {
  return Object.prototype.toString.call(message) === "[object BaseMessageChunk]";
}
__name(isBaseMessageChunk, "isBaseMessageChunk");
function maybeSendActionExecutionResultIsMessage(eventStream$, actionExecution) {
  if (actionExecution) {
    eventStream$.sendActionExecutionResult({
      actionExecutionId: actionExecution.id,
      actionName: actionExecution.name,
      result: "Sending a message"
    });
  }
}
__name(maybeSendActionExecutionResultIsMessage, "maybeSendActionExecutionResultIsMessage");
async function streamLangChainResponse({ result, eventStream$, actionExecution }) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
  if (typeof result === "string") {
    if (!actionExecution) {
      eventStream$.sendTextMessage((0, import_shared3.randomId)(), result);
    } else {
      eventStream$.sendActionExecutionResult({
        actionExecutionId: actionExecution.id,
        actionName: actionExecution.name,
        result
      });
    }
  } else if (isAIMessage(result)) {
    maybeSendActionExecutionResultIsMessage(eventStream$, actionExecution);
    if (result.content) {
      eventStream$.sendTextMessage((0, import_shared3.randomId)(), result.content);
    }
    for (const toolCall of result.tool_calls) {
      eventStream$.sendActionExecution({
        actionExecutionId: toolCall.id || (0, import_shared3.randomId)(),
        actionName: toolCall.name,
        args: JSON.stringify(toolCall.args)
      });
    }
  } else if (isBaseMessageChunk(result)) {
    maybeSendActionExecutionResultIsMessage(eventStream$, actionExecution);
    if ((_a = result.lc_kwargs) == null ? void 0 : _a.content) {
      eventStream$.sendTextMessage((0, import_shared3.randomId)(), result.content);
    }
    if ((_b = result.lc_kwargs) == null ? void 0 : _b.tool_calls) {
      for (const toolCall of (_c = result.lc_kwargs) == null ? void 0 : _c.tool_calls) {
        eventStream$.sendActionExecution({
          actionExecutionId: toolCall.id || (0, import_shared3.randomId)(),
          actionName: toolCall.name,
          args: JSON.stringify(toolCall.args)
        });
      }
    }
  } else if (result && "getReader" in result) {
    maybeSendActionExecutionResultIsMessage(eventStream$, actionExecution);
    let reader = result.getReader();
    let mode = null;
    let currentMessageId;
    const toolCallDetails = {
      name: null,
      id: null,
      index: null,
      prevIndex: null
    };
    while (true) {
      try {
        const { done, value } = await reader.read();
        let toolCallName = void 0;
        let toolCallId = void 0;
        let toolCallArgs = void 0;
        let hasToolCall = false;
        let content = "";
        if (value && value.content) {
          content = Array.isArray(value.content) ? ((_d = value.content[0]) == null ? void 0 : _d.text) ?? "" : value.content;
        }
        if (isAIMessageChunk(value)) {
          let chunk = (_e = value.tool_call_chunks) == null ? void 0 : _e[0];
          toolCallArgs = chunk == null ? void 0 : chunk.args;
          hasToolCall = chunk != void 0;
          if (chunk == null ? void 0 : chunk.name)
            toolCallDetails.name = chunk.name;
          if ((chunk == null ? void 0 : chunk.index) != null) {
            toolCallDetails.index = chunk.index;
            if (toolCallDetails.prevIndex == null)
              toolCallDetails.prevIndex = chunk.index;
          }
          if (chunk == null ? void 0 : chunk.id)
            toolCallDetails.id = chunk.index != null ? `${chunk.id}-idx-${chunk.index}` : chunk.id;
          toolCallName = toolCallDetails.name;
          toolCallId = toolCallDetails.id;
        } else if (isBaseMessageChunk(value)) {
          let chunk = (_g = (_f = value.additional_kwargs) == null ? void 0 : _f.tool_calls) == null ? void 0 : _g[0];
          toolCallName = (_h = chunk == null ? void 0 : chunk.function) == null ? void 0 : _h.name;
          toolCallId = chunk == null ? void 0 : chunk.id;
          toolCallArgs = (_i = chunk == null ? void 0 : chunk.function) == null ? void 0 : _i.arguments;
          hasToolCall = (chunk == null ? void 0 : chunk.function) != void 0;
        }
        if (mode === "message" && (toolCallId || done)) {
          mode = null;
          eventStream$.sendTextMessageEnd({
            messageId: currentMessageId
          });
        } else if (mode === "function" && (!hasToolCall || done)) {
          mode = null;
          eventStream$.sendActionExecutionEnd({
            actionExecutionId: toolCallId
          });
        }
        if (done) {
          break;
        }
        if (mode === null) {
          if (hasToolCall && toolCallId && toolCallName) {
            mode = "function";
            eventStream$.sendActionExecutionStart({
              actionExecutionId: toolCallId,
              actionName: toolCallName,
              parentMessageId: (_j = value.lc_kwargs) == null ? void 0 : _j.id
            });
          } else if (content) {
            mode = "message";
            currentMessageId = ((_k = value.lc_kwargs) == null ? void 0 : _k.id) || (0, import_shared3.randomId)();
            eventStream$.sendTextMessageStart({
              messageId: currentMessageId
            });
          }
        }
        if (mode === "message" && content) {
          eventStream$.sendTextMessageContent({
            messageId: currentMessageId,
            content
          });
        } else if (mode === "function" && toolCallArgs) {
          if (toolCallDetails.index !== toolCallDetails.prevIndex) {
            eventStream$.sendActionExecutionEnd({
              actionExecutionId: toolCallId
            });
            eventStream$.sendActionExecutionStart({
              actionExecutionId: toolCallId,
              actionName: toolCallName,
              parentMessageId: (_l = value.lc_kwargs) == null ? void 0 : _l.id
            });
            toolCallDetails.prevIndex = toolCallDetails.index;
          }
          eventStream$.sendActionExecutionArgs({
            actionExecutionId: toolCallId,
            args: toolCallArgs
          });
        }
      } catch (error) {
        console.error("Error reading from stream", error);
        break;
      }
    }
  } else if (actionExecution) {
    eventStream$.sendActionExecutionResult({
      actionExecutionId: actionExecution.id,
      actionName: actionExecution.name,
      result: encodeResult(result)
    });
  } else {
    throw new Error("Invalid return type from LangChain function.");
  }
  eventStream$.complete();
}
__name(streamLangChainResponse, "streamLangChainResponse");
function encodeResult(result) {
  if (result === void 0) {
    return "";
  } else if (typeof result === "string") {
    return result;
  } else {
    return JSON.stringify(result);
  }
}
__name(encodeResult, "encodeResult");

// src/service-adapters/langchain/langchain-adapter.ts
var import_shared4 = require("@copilotkit/shared");
var import_promises = require("@langchain/core/callbacks/promises");
var LangChainAdapter = class {
  options;
  /**
  * To use LangChain as a backend, provide a handler function to the adapter with your custom LangChain logic.
  */
  constructor(options) {
    this.options = options;
  }
  async process(request) {
    try {
      const { eventSource, model, actions, messages, runId, threadId: threadIdFromRequest } = request;
      const threadId = threadIdFromRequest ?? (0, import_shared4.randomUUID)();
      const result = await this.options.chainFn({
        messages: messages.map(convertMessageToLangChainMessage),
        tools: actions.map(convertActionInputToLangChainTool),
        model,
        threadId,
        runId
      });
      eventSource.stream(async (eventStream$) => {
        await streamLangChainResponse({
          result,
          eventStream$
        });
      });
      return {
        threadId
      };
    } finally {
      await (0, import_promises.awaitAllCallbacks)();
    }
  }
};
__name(LangChainAdapter, "LangChainAdapter");

// src/service-adapters/google/google-genai-adapter.ts
var import_google_gauth = require("@langchain/google-gauth");
var GoogleGenerativeAIAdapter = class extends LangChainAdapter {
  constructor(options) {
    super({
      chainFn: async ({ messages, tools, threadId }) => {
        const model = new import_google_gauth.ChatGoogle({
          modelName: (options == null ? void 0 : options.model) ?? "gemini-1.5-pro",
          apiVersion: "v1beta"
        }).bindTools(tools);
        return model.stream(messages, {
          metadata: {
            conversation_id: threadId
          }
        });
      }
    });
  }
};
__name(GoogleGenerativeAIAdapter, "GoogleGenerativeAIAdapter");

// src/service-adapters/openai/openai-assistant-adapter.ts
var import_openai2 = __toESM(require("openai"));
var OpenAIAssistantAdapter = class {
  openai;
  codeInterpreterEnabled;
  assistantId;
  fileSearchEnabled;
  disableParallelToolCalls;
  keepSystemRole = false;
  constructor(params) {
    this.openai = params.openai || new import_openai2.default({});
    this.codeInterpreterEnabled = params.codeInterpreterEnabled === false || true;
    this.fileSearchEnabled = params.fileSearchEnabled === false || true;
    this.assistantId = params.assistantId;
    this.disableParallelToolCalls = (params == null ? void 0 : params.disableParallelToolCalls) || false;
    this.keepSystemRole = (params == null ? void 0 : params.keepSystemRole) ?? false;
  }
  async process(request) {
    var _a, _b;
    const { messages, actions, eventSource, runId, forwardedParameters } = request;
    let threadId = (_b = (_a = request.extensions) == null ? void 0 : _a.openaiAssistantAPI) == null ? void 0 : _b.threadId;
    if (!threadId) {
      threadId = (await this.openai.beta.threads.create()).id;
    }
    const lastMessage = messages.at(-1);
    let nextRunId = void 0;
    if (lastMessage.isResultMessage() && runId) {
      nextRunId = await this.submitToolOutputs(threadId, runId, messages, eventSource);
    } else if (lastMessage.isTextMessage()) {
      nextRunId = await this.submitUserMessage(threadId, messages, actions, eventSource, forwardedParameters);
    } else {
      throw new Error("No actionable message found in the messages");
    }
    return {
      runId: nextRunId,
      threadId,
      extensions: {
        ...request.extensions,
        openaiAssistantAPI: {
          threadId,
          runId: nextRunId
        }
      }
    };
  }
  async submitToolOutputs(threadId, runId, messages, eventSource) {
    let run = await this.openai.beta.threads.runs.retrieve(threadId, runId);
    if (!run.required_action) {
      throw new Error("No tool outputs required");
    }
    const toolCallsIds = run.required_action.submit_tool_outputs.tool_calls.map((toolCall) => toolCall.id);
    const resultMessages = messages.filter((message) => message.isResultMessage() && toolCallsIds.includes(message.actionExecutionId));
    if (toolCallsIds.length != resultMessages.length) {
      throw new Error("Number of function results does not match the number of tool calls");
    }
    const toolOutputs = resultMessages.map((message) => {
      return {
        tool_call_id: message.actionExecutionId,
        output: message.result
      };
    });
    const stream = this.openai.beta.threads.runs.submitToolOutputsStream(threadId, runId, {
      tool_outputs: toolOutputs,
      ...this.disableParallelToolCalls && {
        parallel_tool_calls: false
      }
    });
    await this.streamResponse(stream, eventSource);
    return runId;
  }
  async submitUserMessage(threadId, messages, actions, eventSource, forwardedParameters) {
    messages = [
      ...messages
    ];
    const instructionsMessage = messages.shift();
    const instructions = instructionsMessage.isTextMessage() ? instructionsMessage.content : "";
    const userMessage = messages.map((m) => convertMessageToOpenAIMessage(m, {
      keepSystemRole: this.keepSystemRole
    })).map(convertSystemMessageToAssistantAPI).at(-1);
    if (userMessage.role !== "user") {
      throw new Error("No user message found");
    }
    await this.openai.beta.threads.messages.create(threadId, {
      role: "user",
      content: userMessage.content
    });
    const openaiTools = actions.map(convertActionInputToOpenAITool);
    const tools = [
      ...openaiTools,
      ...this.codeInterpreterEnabled ? [
        {
          type: "code_interpreter"
        }
      ] : [],
      ...this.fileSearchEnabled ? [
        {
          type: "file_search"
        }
      ] : []
    ];
    let stream = this.openai.beta.threads.runs.stream(threadId, {
      assistant_id: this.assistantId,
      instructions,
      tools,
      ...(forwardedParameters == null ? void 0 : forwardedParameters.maxTokens) && {
        max_completion_tokens: forwardedParameters.maxTokens
      },
      ...this.disableParallelToolCalls && {
        parallel_tool_calls: false
      }
    });
    await this.streamResponse(stream, eventSource);
    return getRunIdFromStream(stream);
  }
  async streamResponse(stream, eventSource) {
    eventSource.stream(async (eventStream$) => {
      var _a, _b, _c, _d, _e, _f;
      let inFunctionCall = false;
      let currentMessageId;
      let currentToolCallId;
      for await (const chunk of stream) {
        switch (chunk.event) {
          case "thread.message.created":
            if (inFunctionCall) {
              eventStream$.sendActionExecutionEnd({
                actionExecutionId: currentToolCallId
              });
            }
            currentMessageId = chunk.data.id;
            eventStream$.sendTextMessageStart({
              messageId: currentMessageId
            });
            break;
          case "thread.message.delta":
            if (((_a = chunk.data.delta.content) == null ? void 0 : _a[0].type) === "text") {
              eventStream$.sendTextMessageContent({
                messageId: currentMessageId,
                content: (_b = chunk.data.delta.content) == null ? void 0 : _b[0].text.value
              });
            }
            break;
          case "thread.message.completed":
            eventStream$.sendTextMessageEnd({
              messageId: currentMessageId
            });
            break;
          case "thread.run.step.delta":
            let toolCallId;
            let toolCallName;
            let toolCallArgs;
            if (chunk.data.delta.step_details.type === "tool_calls" && ((_c = chunk.data.delta.step_details.tool_calls) == null ? void 0 : _c[0].type) === "function") {
              toolCallId = (_d = chunk.data.delta.step_details.tool_calls) == null ? void 0 : _d[0].id;
              toolCallName = (_e = chunk.data.delta.step_details.tool_calls) == null ? void 0 : _e[0].function.name;
              toolCallArgs = (_f = chunk.data.delta.step_details.tool_calls) == null ? void 0 : _f[0].function.arguments;
            }
            if (toolCallName && toolCallId) {
              if (inFunctionCall) {
                eventStream$.sendActionExecutionEnd({
                  actionExecutionId: currentToolCallId
                });
              }
              inFunctionCall = true;
              currentToolCallId = toolCallId;
              eventStream$.sendActionExecutionStart({
                actionExecutionId: currentToolCallId,
                parentMessageId: chunk.data.id,
                actionName: toolCallName
              });
            } else if (toolCallArgs) {
              eventStream$.sendActionExecutionArgs({
                actionExecutionId: currentToolCallId,
                args: toolCallArgs
              });
            }
            break;
        }
      }
      if (inFunctionCall) {
        eventStream$.sendActionExecutionEnd({
          actionExecutionId: currentToolCallId
        });
      }
      eventStream$.complete();
    });
  }
};
__name(OpenAIAssistantAdapter, "OpenAIAssistantAdapter");
function getRunIdFromStream(stream) {
  return new Promise((resolve, reject) => {
    let runIdGetter = /* @__PURE__ */ __name((event) => {
      if (event.event === "thread.run.created") {
        const runId = event.data.id;
        stream.off("event", runIdGetter);
        resolve(runId);
      }
    }, "runIdGetter");
    stream.on("event", runIdGetter);
  });
}
__name(getRunIdFromStream, "getRunIdFromStream");

// src/service-adapters/unify/unify-adapter.ts
var import_openai3 = __toESM(require("openai"));
var import_shared5 = require("@copilotkit/shared");
var UnifyAdapter = class {
  apiKey;
  model;
  start;
  constructor(options) {
    if (options == null ? void 0 : options.apiKey) {
      this.apiKey = options.apiKey;
    } else {
      this.apiKey = "UNIFY_API_KEY";
    }
    this.model = options == null ? void 0 : options.model;
    this.start = true;
  }
  async process(request) {
    const tools = request.actions.map(convertActionInputToOpenAITool);
    const openai = new import_openai3.default({
      apiKey: this.apiKey,
      baseURL: "https://api.unify.ai/v0/"
    });
    const forwardedParameters = request.forwardedParameters;
    const messages = request.messages.map((m) => convertMessageToOpenAIMessage(m));
    const stream = await openai.chat.completions.create({
      model: this.model,
      messages,
      stream: true,
      ...tools.length > 0 && {
        tools
      },
      ...(forwardedParameters == null ? void 0 : forwardedParameters.temperature) && {
        temperature: forwardedParameters.temperature
      }
    });
    let model = null;
    let currentMessageId;
    let currentToolCallId;
    request.eventSource.stream(async (eventStream$) => {
      var _a, _b;
      let mode = null;
      for await (const chunk of stream) {
        if (this.start) {
          model = chunk.model;
          currentMessageId = (0, import_shared5.randomId)();
          eventStream$.sendTextMessageStart({
            messageId: currentMessageId
          });
          eventStream$.sendTextMessageContent({
            messageId: currentMessageId,
            content: `Model used: ${model}
`
          });
          eventStream$.sendTextMessageEnd({
            messageId: currentMessageId
          });
          this.start = false;
        }
        const toolCall = (_a = chunk.choices[0].delta.tool_calls) == null ? void 0 : _a[0];
        const content = chunk.choices[0].delta.content;
        if (mode === "message" && (toolCall == null ? void 0 : toolCall.id)) {
          mode = null;
          eventStream$.sendTextMessageEnd({
            messageId: currentMessageId
          });
        } else if (mode === "function" && (toolCall === void 0 || (toolCall == null ? void 0 : toolCall.id))) {
          mode = null;
          eventStream$.sendActionExecutionEnd({
            actionExecutionId: currentToolCallId
          });
        }
        if (mode === null) {
          if (toolCall == null ? void 0 : toolCall.id) {
            mode = "function";
            currentToolCallId = toolCall.id;
            eventStream$.sendActionExecutionStart({
              actionExecutionId: currentToolCallId,
              actionName: toolCall.function.name
            });
          } else if (content) {
            mode = "message";
            currentMessageId = chunk.id;
            eventStream$.sendTextMessageStart({
              messageId: currentMessageId
            });
          }
        }
        if (mode === "message" && content) {
          eventStream$.sendTextMessageContent({
            messageId: currentMessageId,
            content
          });
        } else if (mode === "function" && ((_b = toolCall == null ? void 0 : toolCall.function) == null ? void 0 : _b.arguments)) {
          eventStream$.sendActionExecutionArgs({
            actionExecutionId: currentToolCallId,
            args: toolCall.function.arguments
          });
        }
      }
      if (mode === "message") {
        eventStream$.sendTextMessageEnd({
          messageId: currentMessageId
        });
      } else if (mode === "function") {
        eventStream$.sendActionExecutionEnd({
          actionExecutionId: currentToolCallId
        });
      }
      eventStream$.complete();
    });
    return {
      threadId: request.threadId || (0, import_shared5.randomUUID)()
    };
  }
};
__name(UnifyAdapter, "UnifyAdapter");

// src/service-adapters/groq/groq-adapter.ts
var import_groq_sdk = require("groq-sdk");
var import_shared6 = require("@copilotkit/shared");
var DEFAULT_MODEL2 = "llama-3.3-70b-versatile";
var GroqAdapter = class {
  model = DEFAULT_MODEL2;
  disableParallelToolCalls = false;
  _groq;
  get groq() {
    return this._groq;
  }
  constructor(params) {
    this._groq = (params == null ? void 0 : params.groq) || new import_groq_sdk.Groq({});
    if (params == null ? void 0 : params.model) {
      this.model = params.model;
    }
    this.disableParallelToolCalls = (params == null ? void 0 : params.disableParallelToolCalls) || false;
  }
  async process(request) {
    const { threadId, model = this.model, messages, actions, eventSource, forwardedParameters } = request;
    const tools = actions.map(convertActionInputToOpenAITool);
    let openaiMessages = messages.map((m) => convertMessageToOpenAIMessage(m, {
      keepSystemRole: true
    }));
    openaiMessages = limitMessagesToTokenCount(openaiMessages, tools, model);
    let toolChoice = forwardedParameters == null ? void 0 : forwardedParameters.toolChoice;
    if ((forwardedParameters == null ? void 0 : forwardedParameters.toolChoice) === "function") {
      toolChoice = {
        type: "function",
        function: {
          name: forwardedParameters.toolChoiceFunctionName
        }
      };
    }
    const stream = await this.groq.chat.completions.create({
      model,
      stream: true,
      messages: openaiMessages,
      ...tools.length > 0 && {
        tools
      },
      ...(forwardedParameters == null ? void 0 : forwardedParameters.maxTokens) && {
        max_tokens: forwardedParameters.maxTokens
      },
      ...(forwardedParameters == null ? void 0 : forwardedParameters.stop) && {
        stop: forwardedParameters.stop
      },
      ...toolChoice && {
        tool_choice: toolChoice
      },
      ...this.disableParallelToolCalls && {
        parallel_tool_calls: false
      },
      ...(forwardedParameters == null ? void 0 : forwardedParameters.temperature) && {
        temperature: forwardedParameters.temperature
      }
    });
    eventSource.stream(async (eventStream$) => {
      var _a, _b;
      let mode = null;
      let currentMessageId;
      let currentToolCallId;
      for await (const chunk of stream) {
        const toolCall = (_a = chunk.choices[0].delta.tool_calls) == null ? void 0 : _a[0];
        const content = chunk.choices[0].delta.content;
        if (mode === "message" && (toolCall == null ? void 0 : toolCall.id)) {
          mode = null;
          eventStream$.sendTextMessageEnd({
            messageId: currentMessageId
          });
        } else if (mode === "function" && (toolCall === void 0 || (toolCall == null ? void 0 : toolCall.id))) {
          mode = null;
          eventStream$.sendActionExecutionEnd({
            actionExecutionId: currentToolCallId
          });
        }
        if (mode === null) {
          if (toolCall == null ? void 0 : toolCall.id) {
            mode = "function";
            currentToolCallId = toolCall.id;
            eventStream$.sendActionExecutionStart({
              actionExecutionId: currentToolCallId,
              actionName: toolCall.function.name,
              parentMessageId: chunk.id
            });
          } else if (content) {
            mode = "message";
            currentMessageId = chunk.id;
            eventStream$.sendTextMessageStart({
              messageId: currentMessageId
            });
          }
        }
        if (mode === "message" && content) {
          eventStream$.sendTextMessageContent({
            messageId: currentMessageId,
            content
          });
        } else if (mode === "function" && ((_b = toolCall == null ? void 0 : toolCall.function) == null ? void 0 : _b.arguments)) {
          eventStream$.sendActionExecutionArgs({
            actionExecutionId: currentToolCallId,
            args: toolCall.function.arguments
          });
        }
      }
      if (mode === "message") {
        eventStream$.sendTextMessageEnd({
          messageId: currentMessageId
        });
      } else if (mode === "function") {
        eventStream$.sendActionExecutionEnd({
          actionExecutionId: currentToolCallId
        });
      }
      eventStream$.complete();
    });
    return {
      threadId: request.threadId || (0, import_shared6.randomUUID)()
    };
  }
};
__name(GroqAdapter, "GroqAdapter");

// src/lib/integrations/shared.ts
var import_type_graphql26 = require("type-graphql");

// src/graphql/resolvers/copilot.resolver.ts
var import_type_graphql20 = require("type-graphql");
var import_rxjs4 = require("rxjs");

// src/graphql/inputs/generate-copilot-response.input.ts
var import_type_graphql18 = require("type-graphql");

// src/graphql/inputs/message.input.ts
var import_type_graphql3 = require("type-graphql");

// src/graphql/types/enums.ts
var import_type_graphql = require("type-graphql");
var MessageRole;
(function(MessageRole2) {
  MessageRole2["user"] = "user";
  MessageRole2["assistant"] = "assistant";
  MessageRole2["system"] = "system";
  MessageRole2["tool"] = "tool";
  MessageRole2["developer"] = "developer";
})(MessageRole || (MessageRole = {}));
var CopilotRequestType;
(function(CopilotRequestType2) {
  CopilotRequestType2["Chat"] = "Chat";
  CopilotRequestType2["Task"] = "Task";
  CopilotRequestType2["TextareaCompletion"] = "TextareaCompletion";
  CopilotRequestType2["TextareaPopover"] = "TextareaPopover";
  CopilotRequestType2["Suggestion"] = "Suggestion";
})(CopilotRequestType || (CopilotRequestType = {}));
var ActionInputAvailability;
(function(ActionInputAvailability2) {
  ActionInputAvailability2["disabled"] = "disabled";
  ActionInputAvailability2["enabled"] = "enabled";
  ActionInputAvailability2["remote"] = "remote";
})(ActionInputAvailability || (ActionInputAvailability = {}));
(0, import_type_graphql.registerEnumType)(MessageRole, {
  name: "MessageRole",
  description: "The role of the message"
});
(0, import_type_graphql.registerEnumType)(CopilotRequestType, {
  name: "CopilotRequestType",
  description: "The type of Copilot request"
});
(0, import_type_graphql.registerEnumType)(ActionInputAvailability, {
  name: "ActionInputAvailability",
  description: "The availability of the frontend action"
});

// src/graphql/types/base/index.ts
var import_type_graphql2 = require("type-graphql");
function _ts_decorate(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate, "_ts_decorate");
function _ts_metadata(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata, "_ts_metadata");
var BaseMessageInput = class {
  id;
  createdAt;
};
__name(BaseMessageInput, "BaseMessageInput");
_ts_decorate([
  (0, import_type_graphql2.Field)(() => String),
  _ts_metadata("design:type", String)
], BaseMessageInput.prototype, "id", void 0);
_ts_decorate([
  (0, import_type_graphql2.Field)(() => Date),
  _ts_metadata("design:type", typeof Date === "undefined" ? Object : Date)
], BaseMessageInput.prototype, "createdAt", void 0);
BaseMessageInput = _ts_decorate([
  (0, import_type_graphql2.InputType)()
], BaseMessageInput);

// src/graphql/inputs/message.input.ts
function _ts_decorate2(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate2, "_ts_decorate");
function _ts_metadata2(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata2, "_ts_metadata");
var MessageInput = class extends BaseMessageInput {
  textMessage;
  actionExecutionMessage;
  resultMessage;
  agentStateMessage;
  imageMessage;
};
__name(MessageInput, "MessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => TextMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof TextMessageInput === "undefined" ? Object : TextMessageInput)
], MessageInput.prototype, "textMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => ActionExecutionMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof ActionExecutionMessageInput === "undefined" ? Object : ActionExecutionMessageInput)
], MessageInput.prototype, "actionExecutionMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => ResultMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof ResultMessageInput === "undefined" ? Object : ResultMessageInput)
], MessageInput.prototype, "resultMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => AgentStateMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof AgentStateMessageInput === "undefined" ? Object : AgentStateMessageInput)
], MessageInput.prototype, "agentStateMessage", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => ImageMessageInput, {
    nullable: true
  }),
  _ts_metadata2("design:type", typeof ImageMessageInput === "undefined" ? Object : ImageMessageInput)
], MessageInput.prototype, "imageMessage", void 0);
MessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], MessageInput);
var TextMessageInput = class {
  content;
  parentMessageId;
  role;
};
__name(TextMessageInput, "TextMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], TextMessageInput.prototype, "content", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], TextMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => MessageRole),
  _ts_metadata2("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], TextMessageInput.prototype, "role", void 0);
TextMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], TextMessageInput);
var ActionExecutionMessageInput = class {
  name;
  arguments;
  parentMessageId;
  scope;
};
__name(ActionExecutionMessageInput, "ActionExecutionMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ActionExecutionMessageInput.prototype, "name", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ActionExecutionMessageInput.prototype, "arguments", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], ActionExecutionMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true,
    deprecationReason: "This field will be removed in a future version"
  }),
  _ts_metadata2("design:type", typeof String === "undefined" ? Object : String)
], ActionExecutionMessageInput.prototype, "scope", void 0);
ActionExecutionMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], ActionExecutionMessageInput);
var ResultMessageInput = class {
  actionExecutionId;
  actionName;
  parentMessageId;
  result;
};
__name(ResultMessageInput, "ResultMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "actionExecutionId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "actionName", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ResultMessageInput.prototype, "result", void 0);
ResultMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], ResultMessageInput);
var AgentStateMessageInput = class {
  threadId;
  agentName;
  role;
  state;
  running;
  nodeName;
  runId;
  active;
};
__name(AgentStateMessageInput, "AgentStateMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "threadId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "agentName", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => MessageRole),
  _ts_metadata2("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], AgentStateMessageInput.prototype, "role", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "state", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => Boolean),
  _ts_metadata2("design:type", Boolean)
], AgentStateMessageInput.prototype, "running", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "nodeName", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], AgentStateMessageInput.prototype, "runId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => Boolean),
  _ts_metadata2("design:type", Boolean)
], AgentStateMessageInput.prototype, "active", void 0);
AgentStateMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], AgentStateMessageInput);
var ImageMessageInput = class {
  format;
  bytes;
  parentMessageId;
  role;
};
__name(ImageMessageInput, "ImageMessageInput");
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ImageMessageInput.prototype, "format", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String),
  _ts_metadata2("design:type", String)
], ImageMessageInput.prototype, "bytes", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata2("design:type", String)
], ImageMessageInput.prototype, "parentMessageId", void 0);
_ts_decorate2([
  (0, import_type_graphql3.Field)(() => MessageRole),
  _ts_metadata2("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], ImageMessageInput.prototype, "role", void 0);
ImageMessageInput = _ts_decorate2([
  (0, import_type_graphql3.InputType)()
], ImageMessageInput);

// src/graphql/inputs/frontend.input.ts
var import_type_graphql5 = require("type-graphql");

// src/graphql/inputs/action.input.ts
var import_type_graphql4 = require("type-graphql");
function _ts_decorate3(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate3, "_ts_decorate");
function _ts_metadata3(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata3, "_ts_metadata");
var ActionInput = class {
  name;
  description;
  jsonSchema;
  available;
};
__name(ActionInput, "ActionInput");
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => String),
  _ts_metadata3("design:type", String)
], ActionInput.prototype, "name", void 0);
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => String),
  _ts_metadata3("design:type", String)
], ActionInput.prototype, "description", void 0);
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => String),
  _ts_metadata3("design:type", String)
], ActionInput.prototype, "jsonSchema", void 0);
_ts_decorate3([
  (0, import_type_graphql4.Field)(() => ActionInputAvailability, {
    nullable: true
  }),
  _ts_metadata3("design:type", typeof ActionInputAvailability === "undefined" ? Object : ActionInputAvailability)
], ActionInput.prototype, "available", void 0);
ActionInput = _ts_decorate3([
  (0, import_type_graphql4.InputType)()
], ActionInput);

// src/graphql/inputs/frontend.input.ts
function _ts_decorate4(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate4, "_ts_decorate");
function _ts_metadata4(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata4, "_ts_metadata");
var FrontendInput = class {
  toDeprecate_fullContext;
  actions;
  url;
};
__name(FrontendInput, "FrontendInput");
_ts_decorate4([
  (0, import_type_graphql5.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata4("design:type", String)
], FrontendInput.prototype, "toDeprecate_fullContext", void 0);
_ts_decorate4([
  (0, import_type_graphql5.Field)(() => [
    ActionInput
  ]),
  _ts_metadata4("design:type", Array)
], FrontendInput.prototype, "actions", void 0);
_ts_decorate4([
  (0, import_type_graphql5.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata4("design:type", String)
], FrontendInput.prototype, "url", void 0);
FrontendInput = _ts_decorate4([
  (0, import_type_graphql5.InputType)()
], FrontendInput);

// src/graphql/inputs/cloud.input.ts
var import_type_graphql7 = require("type-graphql");

// src/graphql/inputs/cloud-guardrails.input.ts
var import_type_graphql6 = require("type-graphql");
function _ts_decorate5(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate5, "_ts_decorate");
function _ts_metadata5(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata5, "_ts_metadata");
var GuardrailsRuleInput = class {
  allowList = [];
  denyList = [];
};
__name(GuardrailsRuleInput, "GuardrailsRuleInput");
_ts_decorate5([
  (0, import_type_graphql6.Field)(() => [
    String
  ], {
    nullable: true
  }),
  _ts_metadata5("design:type", Array)
], GuardrailsRuleInput.prototype, "allowList", void 0);
_ts_decorate5([
  (0, import_type_graphql6.Field)(() => [
    String
  ], {
    nullable: true
  }),
  _ts_metadata5("design:type", Array)
], GuardrailsRuleInput.prototype, "denyList", void 0);
GuardrailsRuleInput = _ts_decorate5([
  (0, import_type_graphql6.InputType)()
], GuardrailsRuleInput);
var GuardrailsInput = class {
  inputValidationRules;
};
__name(GuardrailsInput, "GuardrailsInput");
_ts_decorate5([
  (0, import_type_graphql6.Field)(() => GuardrailsRuleInput, {
    nullable: false
  }),
  _ts_metadata5("design:type", typeof GuardrailsRuleInput === "undefined" ? Object : GuardrailsRuleInput)
], GuardrailsInput.prototype, "inputValidationRules", void 0);
GuardrailsInput = _ts_decorate5([
  (0, import_type_graphql6.InputType)()
], GuardrailsInput);

// src/graphql/inputs/cloud.input.ts
function _ts_decorate6(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate6, "_ts_decorate");
function _ts_metadata6(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata6, "_ts_metadata");
var CloudInput = class {
  guardrails;
};
__name(CloudInput, "CloudInput");
_ts_decorate6([
  (0, import_type_graphql7.Field)(() => GuardrailsInput, {
    nullable: true
  }),
  _ts_metadata6("design:type", typeof GuardrailsInput === "undefined" ? Object : GuardrailsInput)
], CloudInput.prototype, "guardrails", void 0);
CloudInput = _ts_decorate6([
  (0, import_type_graphql7.InputType)()
], CloudInput);

// src/graphql/inputs/forwarded-parameters.input.ts
var import_type_graphql8 = require("type-graphql");
function _ts_decorate7(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate7, "_ts_decorate");
function _ts_metadata7(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata7, "_ts_metadata");
var ForwardedParametersInput = class {
  model;
  maxTokens;
  stop;
  toolChoice;
  toolChoiceFunctionName;
  temperature;
};
__name(ForwardedParametersInput, "ForwardedParametersInput");
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata7("design:type", String)
], ForwardedParametersInput.prototype, "model", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => Number, {
    nullable: true
  }),
  _ts_metadata7("design:type", Number)
], ForwardedParametersInput.prototype, "maxTokens", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => [
    String
  ], {
    nullable: true
  }),
  _ts_metadata7("design:type", Array)
], ForwardedParametersInput.prototype, "stop", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata7("design:type", typeof String === "undefined" ? Object : String)
], ForwardedParametersInput.prototype, "toolChoice", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata7("design:type", String)
], ForwardedParametersInput.prototype, "toolChoiceFunctionName", void 0);
_ts_decorate7([
  (0, import_type_graphql8.Field)(() => Number, {
    nullable: true
  }),
  _ts_metadata7("design:type", Number)
], ForwardedParametersInput.prototype, "temperature", void 0);
ForwardedParametersInput = _ts_decorate7([
  (0, import_type_graphql8.InputType)()
], ForwardedParametersInput);

// src/graphql/inputs/agent-session.input.ts
var import_type_graphql9 = require("type-graphql");
function _ts_decorate8(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate8, "_ts_decorate");
function _ts_metadata8(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata8, "_ts_metadata");
var AgentSessionInput = class {
  agentName;
  threadId;
  nodeName;
};
__name(AgentSessionInput, "AgentSessionInput");
_ts_decorate8([
  (0, import_type_graphql9.Field)(() => String),
  _ts_metadata8("design:type", String)
], AgentSessionInput.prototype, "agentName", void 0);
_ts_decorate8([
  (0, import_type_graphql9.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata8("design:type", String)
], AgentSessionInput.prototype, "threadId", void 0);
_ts_decorate8([
  (0, import_type_graphql9.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata8("design:type", String)
], AgentSessionInput.prototype, "nodeName", void 0);
AgentSessionInput = _ts_decorate8([
  (0, import_type_graphql9.InputType)()
], AgentSessionInput);

// src/graphql/inputs/agent-state.input.ts
var import_type_graphql10 = require("type-graphql");
function _ts_decorate9(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate9, "_ts_decorate");
function _ts_metadata9(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata9, "_ts_metadata");
var AgentStateInput = class {
  agentName;
  state;
  config;
};
__name(AgentStateInput, "AgentStateInput");
_ts_decorate9([
  (0, import_type_graphql10.Field)(() => String),
  _ts_metadata9("design:type", String)
], AgentStateInput.prototype, "agentName", void 0);
_ts_decorate9([
  (0, import_type_graphql10.Field)(() => String),
  _ts_metadata9("design:type", String)
], AgentStateInput.prototype, "state", void 0);
_ts_decorate9([
  (0, import_type_graphql10.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata9("design:type", String)
], AgentStateInput.prototype, "config", void 0);
AgentStateInput = _ts_decorate9([
  (0, import_type_graphql10.InputType)()
], AgentStateInput);

// src/graphql/inputs/extensions.input.ts
var import_type_graphql11 = require("type-graphql");
function _ts_decorate10(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate10, "_ts_decorate");
function _ts_metadata10(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata10, "_ts_metadata");
var ExtensionsInput = class {
  openaiAssistantAPI;
};
__name(ExtensionsInput, "ExtensionsInput");
_ts_decorate10([
  (0, import_type_graphql11.Field)(() => OpenAIApiAssistantAPIInput, {
    nullable: true
  }),
  _ts_metadata10("design:type", typeof OpenAIApiAssistantAPIInput === "undefined" ? Object : OpenAIApiAssistantAPIInput)
], ExtensionsInput.prototype, "openaiAssistantAPI", void 0);
ExtensionsInput = _ts_decorate10([
  (0, import_type_graphql11.InputType)()
], ExtensionsInput);
var OpenAIApiAssistantAPIInput = class {
  runId;
  threadId;
};
__name(OpenAIApiAssistantAPIInput, "OpenAIApiAssistantAPIInput");
_ts_decorate10([
  (0, import_type_graphql11.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata10("design:type", String)
], OpenAIApiAssistantAPIInput.prototype, "runId", void 0);
_ts_decorate10([
  (0, import_type_graphql11.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata10("design:type", String)
], OpenAIApiAssistantAPIInput.prototype, "threadId", void 0);
OpenAIApiAssistantAPIInput = _ts_decorate10([
  (0, import_type_graphql11.InputType)()
], OpenAIApiAssistantAPIInput);

// src/graphql/inputs/meta-event.input.ts
var import_type_graphql17 = require("type-graphql");

// src/graphql/types/meta-events.type.ts
var import_type_graphql16 = require("type-graphql");

// src/graphql/types/copilot-response.type.ts
var import_type_graphql15 = require("type-graphql");

// src/graphql/types/message-status.type.ts
var import_type_graphql12 = require("type-graphql");
function _ts_decorate11(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate11, "_ts_decorate");
function _ts_metadata11(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata11, "_ts_metadata");
var MessageStatusCode;
(function(MessageStatusCode2) {
  MessageStatusCode2["Pending"] = "pending";
  MessageStatusCode2["Success"] = "success";
  MessageStatusCode2["Failed"] = "failed";
})(MessageStatusCode || (MessageStatusCode = {}));
(0, import_type_graphql12.registerEnumType)(MessageStatusCode, {
  name: "MessageStatusCode"
});
var BaseMessageStatus = /* @__PURE__ */ __name(class BaseMessageStatus2 {
  code;
}, "BaseMessageStatus");
_ts_decorate11([
  (0, import_type_graphql12.Field)(() => MessageStatusCode),
  _ts_metadata11("design:type", String)
], BaseMessageStatus.prototype, "code", void 0);
BaseMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], BaseMessageStatus);
var PendingMessageStatus = class extends BaseMessageStatus {
  code = "pending";
};
__name(PendingMessageStatus, "PendingMessageStatus");
PendingMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], PendingMessageStatus);
var SuccessMessageStatus = class extends BaseMessageStatus {
  code = "success";
};
__name(SuccessMessageStatus, "SuccessMessageStatus");
SuccessMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], SuccessMessageStatus);
var FailedMessageStatus = class extends BaseMessageStatus {
  code = "failed";
  reason;
};
__name(FailedMessageStatus, "FailedMessageStatus");
_ts_decorate11([
  (0, import_type_graphql12.Field)(() => String),
  _ts_metadata11("design:type", String)
], FailedMessageStatus.prototype, "reason", void 0);
FailedMessageStatus = _ts_decorate11([
  (0, import_type_graphql12.ObjectType)()
], FailedMessageStatus);
var MessageStatusUnion = (0, import_type_graphql12.createUnionType)({
  name: "MessageStatus",
  types: () => [
    PendingMessageStatus,
    SuccessMessageStatus,
    FailedMessageStatus
  ]
});

// src/graphql/types/response-status.type.ts
var import_graphql_scalars = require("graphql-scalars");
var import_type_graphql13 = require("type-graphql");
function _ts_decorate12(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate12, "_ts_decorate");
function _ts_metadata12(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata12, "_ts_metadata");
var ResponseStatusCode;
(function(ResponseStatusCode2) {
  ResponseStatusCode2["Pending"] = "pending";
  ResponseStatusCode2["Success"] = "success";
  ResponseStatusCode2["Failed"] = "failed";
})(ResponseStatusCode || (ResponseStatusCode = {}));
(0, import_type_graphql13.registerEnumType)(ResponseStatusCode, {
  name: "ResponseStatusCode"
});
var BaseResponseStatus = /* @__PURE__ */ __name(class BaseResponseStatus2 {
  code;
}, "BaseResponseStatus");
_ts_decorate12([
  (0, import_type_graphql13.Field)(() => ResponseStatusCode),
  _ts_metadata12("design:type", String)
], BaseResponseStatus.prototype, "code", void 0);
BaseResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.InterfaceType)({
    resolveType(value) {
      if (value.code === "success") {
        return SuccessResponseStatus;
      } else if (value.code === "failed") {
        return FailedResponseStatus;
      } else if (value.code === "pending") {
        return PendingResponseStatus;
      }
      return void 0;
    }
  }),
  (0, import_type_graphql13.ObjectType)()
], BaseResponseStatus);
var PendingResponseStatus = class extends BaseResponseStatus {
  code = "pending";
};
__name(PendingResponseStatus, "PendingResponseStatus");
PendingResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.ObjectType)({
    implements: BaseResponseStatus
  })
], PendingResponseStatus);
var SuccessResponseStatus = class extends BaseResponseStatus {
  code = "success";
};
__name(SuccessResponseStatus, "SuccessResponseStatus");
SuccessResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.ObjectType)({
    implements: BaseResponseStatus
  })
], SuccessResponseStatus);
var FailedResponseStatusReason;
(function(FailedResponseStatusReason2) {
  FailedResponseStatusReason2["GUARDRAILS_VALIDATION_FAILED"] = "GUARDRAILS_VALIDATION_FAILED";
  FailedResponseStatusReason2["MESSAGE_STREAM_INTERRUPTED"] = "MESSAGE_STREAM_INTERRUPTED";
  FailedResponseStatusReason2["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(FailedResponseStatusReason || (FailedResponseStatusReason = {}));
(0, import_type_graphql13.registerEnumType)(FailedResponseStatusReason, {
  name: "FailedResponseStatusReason"
});
var FailedResponseStatus = class extends BaseResponseStatus {
  code = "failed";
  reason;
  details = null;
};
__name(FailedResponseStatus, "FailedResponseStatus");
_ts_decorate12([
  (0, import_type_graphql13.Field)(() => FailedResponseStatusReason),
  _ts_metadata12("design:type", String)
], FailedResponseStatus.prototype, "reason", void 0);
_ts_decorate12([
  (0, import_type_graphql13.Field)(() => import_graphql_scalars.GraphQLJSON, {
    nullable: true
  }),
  _ts_metadata12("design:type", typeof Record === "undefined" ? Object : Record)
], FailedResponseStatus.prototype, "details", void 0);
FailedResponseStatus = _ts_decorate12([
  (0, import_type_graphql13.ObjectType)({
    implements: BaseResponseStatus
  })
], FailedResponseStatus);
var ResponseStatusUnion = (0, import_type_graphql13.createUnionType)({
  name: "ResponseStatus",
  types: () => [
    PendingResponseStatus,
    SuccessResponseStatus,
    FailedResponseStatus
  ]
});

// src/graphql/types/extensions-response.type.ts
var import_type_graphql14 = require("type-graphql");
function _ts_decorate13(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate13, "_ts_decorate");
function _ts_metadata13(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata13, "_ts_metadata");
var ExtensionsResponse = class {
  openaiAssistantAPI;
};
__name(ExtensionsResponse, "ExtensionsResponse");
_ts_decorate13([
  (0, import_type_graphql14.Field)(() => OpenAIApiAssistantAPIResponse, {
    nullable: true
  }),
  _ts_metadata13("design:type", typeof OpenAIApiAssistantAPIResponse === "undefined" ? Object : OpenAIApiAssistantAPIResponse)
], ExtensionsResponse.prototype, "openaiAssistantAPI", void 0);
ExtensionsResponse = _ts_decorate13([
  (0, import_type_graphql14.ObjectType)()
], ExtensionsResponse);
var OpenAIApiAssistantAPIResponse = class {
  runId;
  threadId;
};
__name(OpenAIApiAssistantAPIResponse, "OpenAIApiAssistantAPIResponse");
_ts_decorate13([
  (0, import_type_graphql14.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata13("design:type", String)
], OpenAIApiAssistantAPIResponse.prototype, "runId", void 0);
_ts_decorate13([
  (0, import_type_graphql14.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata13("design:type", String)
], OpenAIApiAssistantAPIResponse.prototype, "threadId", void 0);
OpenAIApiAssistantAPIResponse = _ts_decorate13([
  (0, import_type_graphql14.ObjectType)()
], OpenAIApiAssistantAPIResponse);

// src/graphql/types/copilot-response.type.ts
function _ts_decorate14(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate14, "_ts_decorate");
function _ts_metadata14(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata14, "_ts_metadata");
var BaseMessageOutput = class {
  id;
  createdAt;
  status;
};
__name(BaseMessageOutput, "BaseMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], BaseMessageOutput.prototype, "id", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => Date),
  _ts_metadata14("design:type", typeof Date === "undefined" ? Object : Date)
], BaseMessageOutput.prototype, "createdAt", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageStatusUnion),
  _ts_metadata14("design:type", Object)
], BaseMessageOutput.prototype, "status", void 0);
BaseMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.InterfaceType)({
    resolveType(value) {
      if (value.hasOwnProperty("content")) {
        return TextMessageOutput;
      } else if (value.hasOwnProperty("name")) {
        return ActionExecutionMessageOutput;
      } else if (value.hasOwnProperty("result")) {
        return ResultMessageOutput;
      } else if (value.hasOwnProperty("state")) {
        return AgentStateMessageOutput;
      } else if (value.hasOwnProperty("format") && value.hasOwnProperty("bytes")) {
        return ImageMessageOutput;
      }
      return void 0;
    }
  })
], BaseMessageOutput);
var TextMessageOutput = class {
  role;
  content;
  parentMessageId;
};
__name(TextMessageOutput, "TextMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageRole),
  _ts_metadata14("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], TextMessageOutput.prototype, "role", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    String
  ]),
  _ts_metadata14("design:type", Array)
], TextMessageOutput.prototype, "content", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], TextMessageOutput.prototype, "parentMessageId", void 0);
TextMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], TextMessageOutput);
var ActionExecutionMessageOutput = class {
  name;
  scope;
  arguments;
  parentMessageId;
};
__name(ActionExecutionMessageOutput, "ActionExecutionMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ActionExecutionMessageOutput.prototype, "name", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true,
    deprecationReason: "This field will be removed in a future version"
  }),
  _ts_metadata14("design:type", String)
], ActionExecutionMessageOutput.prototype, "scope", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    String
  ]),
  _ts_metadata14("design:type", Array)
], ActionExecutionMessageOutput.prototype, "arguments", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], ActionExecutionMessageOutput.prototype, "parentMessageId", void 0);
ActionExecutionMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], ActionExecutionMessageOutput);
var ResultMessageOutput = class {
  actionExecutionId;
  actionName;
  result;
};
__name(ResultMessageOutput, "ResultMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ResultMessageOutput.prototype, "actionExecutionId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ResultMessageOutput.prototype, "actionName", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ResultMessageOutput.prototype, "result", void 0);
ResultMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], ResultMessageOutput);
var AgentStateMessageOutput = class {
  threadId;
  agentName;
  nodeName;
  runId;
  active;
  role;
  state;
  running;
};
__name(AgentStateMessageOutput, "AgentStateMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "threadId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "agentName", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "nodeName", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "runId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => Boolean),
  _ts_metadata14("design:type", Boolean)
], AgentStateMessageOutput.prototype, "active", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageRole),
  _ts_metadata14("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], AgentStateMessageOutput.prototype, "role", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], AgentStateMessageOutput.prototype, "state", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => Boolean),
  _ts_metadata14("design:type", Boolean)
], AgentStateMessageOutput.prototype, "running", void 0);
AgentStateMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], AgentStateMessageOutput);
var ImageMessageOutput = class {
  format;
  bytes;
  role;
  parentMessageId;
};
__name(ImageMessageOutput, "ImageMessageOutput");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ImageMessageOutput.prototype, "format", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], ImageMessageOutput.prototype, "bytes", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => MessageRole),
  _ts_metadata14("design:type", typeof MessageRole === "undefined" ? Object : MessageRole)
], ImageMessageOutput.prototype, "role", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], ImageMessageOutput.prototype, "parentMessageId", void 0);
ImageMessageOutput = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)({
    implements: BaseMessageOutput
  })
], ImageMessageOutput);
var CopilotResponse = class {
  threadId;
  status;
  runId;
  messages;
  extensions;
  metaEvents;
};
__name(CopilotResponse, "CopilotResponse");
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => String),
  _ts_metadata14("design:type", String)
], CopilotResponse.prototype, "threadId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => ResponseStatusUnion),
  _ts_metadata14("design:type", Object)
], CopilotResponse.prototype, "status", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)({
    nullable: true
  }),
  _ts_metadata14("design:type", String)
], CopilotResponse.prototype, "runId", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    BaseMessageOutput
  ]),
  _ts_metadata14("design:type", Array)
], CopilotResponse.prototype, "messages", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => ExtensionsResponse, {
    nullable: true
  }),
  _ts_metadata14("design:type", typeof ExtensionsResponse === "undefined" ? Object : ExtensionsResponse)
], CopilotResponse.prototype, "extensions", void 0);
_ts_decorate14([
  (0, import_type_graphql15.Field)(() => [
    BaseMetaEvent
  ], {
    nullable: true
  }),
  _ts_metadata14("design:type", Array)
], CopilotResponse.prototype, "metaEvents", void 0);
CopilotResponse = _ts_decorate14([
  (0, import_type_graphql15.ObjectType)()
], CopilotResponse);

// src/graphql/types/meta-events.type.ts
function _ts_decorate15(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate15, "_ts_decorate");
function _ts_metadata15(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata15, "_ts_metadata");
var MetaEventName;
(function(MetaEventName2) {
  MetaEventName2["LangGraphInterruptEvent"] = "LangGraphInterruptEvent";
  MetaEventName2["CopilotKitLangGraphInterruptEvent"] = "CopilotKitLangGraphInterruptEvent";
})(MetaEventName || (MetaEventName = {}));
(0, import_type_graphql16.registerEnumType)(MetaEventName, {
  name: "MetaEventName",
  description: "Meta event types"
});
var BaseMetaEvent = class {
  type = "MetaEvent";
  name;
};
__name(BaseMetaEvent, "BaseMetaEvent");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String),
  _ts_metadata15("design:type", String)
], BaseMetaEvent.prototype, "type", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => MetaEventName),
  _ts_metadata15("design:type", String)
], BaseMetaEvent.prototype, "name", void 0);
BaseMetaEvent = _ts_decorate15([
  (0, import_type_graphql16.InterfaceType)({
    resolveType(value) {
      if (value.name === "LangGraphInterruptEvent") {
        return LangGraphInterruptEvent;
      } else if (value.name === "CopilotKitLangGraphInterruptEvent") {
        return CopilotKitLangGraphInterruptEvent;
      }
      return void 0;
    }
  }),
  (0, import_type_graphql16.InterfaceType)()
], BaseMetaEvent);
var CopilotKitLangGraphInterruptEventData = class {
  value;
  messages;
};
__name(CopilotKitLangGraphInterruptEventData, "CopilotKitLangGraphInterruptEventData");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String),
  _ts_metadata15("design:type", String)
], CopilotKitLangGraphInterruptEventData.prototype, "value", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => [
    BaseMessageOutput
  ]),
  _ts_metadata15("design:type", Array)
], CopilotKitLangGraphInterruptEventData.prototype, "messages", void 0);
CopilotKitLangGraphInterruptEventData = _ts_decorate15([
  (0, import_type_graphql16.ObjectType)()
], CopilotKitLangGraphInterruptEventData);
var LangGraphInterruptEvent = class {
  name = "LangGraphInterruptEvent";
  value;
  response;
};
__name(LangGraphInterruptEvent, "LangGraphInterruptEvent");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => MetaEventName),
  _ts_metadata15("design:type", typeof MetaEventName === "undefined" || false ? Object : "LangGraphInterruptEvent")
], LangGraphInterruptEvent.prototype, "name", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String),
  _ts_metadata15("design:type", String)
], LangGraphInterruptEvent.prototype, "value", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata15("design:type", String)
], LangGraphInterruptEvent.prototype, "response", void 0);
LangGraphInterruptEvent = _ts_decorate15([
  (0, import_type_graphql16.ObjectType)({
    implements: BaseMetaEvent
  })
], LangGraphInterruptEvent);
var CopilotKitLangGraphInterruptEvent = class {
  name = "CopilotKitLangGraphInterruptEvent";
  data;
  response;
};
__name(CopilotKitLangGraphInterruptEvent, "CopilotKitLangGraphInterruptEvent");
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => MetaEventName),
  _ts_metadata15("design:type", typeof MetaEventName === "undefined" || false ? Object : "CopilotKitLangGraphInterruptEvent")
], CopilotKitLangGraphInterruptEvent.prototype, "name", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => CopilotKitLangGraphInterruptEventData),
  _ts_metadata15("design:type", typeof CopilotKitLangGraphInterruptEventData === "undefined" ? Object : CopilotKitLangGraphInterruptEventData)
], CopilotKitLangGraphInterruptEvent.prototype, "data", void 0);
_ts_decorate15([
  (0, import_type_graphql16.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata15("design:type", String)
], CopilotKitLangGraphInterruptEvent.prototype, "response", void 0);
CopilotKitLangGraphInterruptEvent = _ts_decorate15([
  (0, import_type_graphql16.ObjectType)({
    implements: BaseMetaEvent
  })
], CopilotKitLangGraphInterruptEvent);

// src/graphql/inputs/meta-event.input.ts
function _ts_decorate16(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate16, "_ts_decorate");
function _ts_metadata16(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata16, "_ts_metadata");
var MetaEventInput = class {
  name;
  value;
  response;
  messages;
};
__name(MetaEventInput, "MetaEventInput");
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => MetaEventName),
  _ts_metadata16("design:type", typeof MetaEventName === "undefined" ? Object : MetaEventName)
], MetaEventInput.prototype, "name", void 0);
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => String),
  _ts_metadata16("design:type", String)
], MetaEventInput.prototype, "value", void 0);
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata16("design:type", String)
], MetaEventInput.prototype, "response", void 0);
_ts_decorate16([
  (0, import_type_graphql17.Field)(() => [
    MessageInput
  ], {
    nullable: true
  }),
  _ts_metadata16("design:type", Array)
], MetaEventInput.prototype, "messages", void 0);
MetaEventInput = _ts_decorate16([
  (0, import_type_graphql17.InputType)()
], MetaEventInput);

// src/graphql/inputs/generate-copilot-response.input.ts
function _ts_decorate17(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate17, "_ts_decorate");
function _ts_metadata17(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata17, "_ts_metadata");
var GenerateCopilotResponseMetadataInput = class {
  requestType;
};
__name(GenerateCopilotResponseMetadataInput, "GenerateCopilotResponseMetadataInput");
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => CopilotRequestType, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof CopilotRequestType === "undefined" ? Object : CopilotRequestType)
], GenerateCopilotResponseMetadataInput.prototype, "requestType", void 0);
GenerateCopilotResponseMetadataInput = _ts_decorate17([
  (0, import_type_graphql18.InputType)()
], GenerateCopilotResponseMetadataInput);
var GenerateCopilotResponseInput = class {
  metadata;
  threadId;
  runId;
  messages;
  frontend;
  cloud;
  forwardedParameters;
  agentSession;
  agentState;
  agentStates;
  extensions;
  metaEvents;
};
__name(GenerateCopilotResponseInput, "GenerateCopilotResponseInput");
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => GenerateCopilotResponseMetadataInput, {
    nullable: false
  }),
  _ts_metadata17("design:type", typeof GenerateCopilotResponseMetadataInput === "undefined" ? Object : GenerateCopilotResponseMetadataInput)
], GenerateCopilotResponseInput.prototype, "metadata", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata17("design:type", String)
], GenerateCopilotResponseInput.prototype, "threadId", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => String, {
    nullable: true
  }),
  _ts_metadata17("design:type", String)
], GenerateCopilotResponseInput.prototype, "runId", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => [
    MessageInput
  ]),
  _ts_metadata17("design:type", Array)
], GenerateCopilotResponseInput.prototype, "messages", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => FrontendInput),
  _ts_metadata17("design:type", typeof FrontendInput === "undefined" ? Object : FrontendInput)
], GenerateCopilotResponseInput.prototype, "frontend", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => CloudInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof CloudInput === "undefined" ? Object : CloudInput)
], GenerateCopilotResponseInput.prototype, "cloud", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => ForwardedParametersInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof ForwardedParametersInput === "undefined" ? Object : ForwardedParametersInput)
], GenerateCopilotResponseInput.prototype, "forwardedParameters", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => AgentSessionInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof AgentSessionInput === "undefined" ? Object : AgentSessionInput)
], GenerateCopilotResponseInput.prototype, "agentSession", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => AgentStateInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof AgentStateInput === "undefined" ? Object : AgentStateInput)
], GenerateCopilotResponseInput.prototype, "agentState", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => [
    AgentStateInput
  ], {
    nullable: true
  }),
  _ts_metadata17("design:type", Array)
], GenerateCopilotResponseInput.prototype, "agentStates", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => ExtensionsInput, {
    nullable: true
  }),
  _ts_metadata17("design:type", typeof ExtensionsInput === "undefined" ? Object : ExtensionsInput)
], GenerateCopilotResponseInput.prototype, "extensions", void 0);
_ts_decorate17([
  (0, import_type_graphql18.Field)(() => [
    MetaEventInput
  ], {
    nullable: true
  }),
  _ts_metadata17("design:type", Array)
], GenerateCopilotResponseInput.prototype, "metaEvents", void 0);
GenerateCopilotResponseInput = _ts_decorate17([
  (0, import_type_graphql18.InputType)()
], GenerateCopilotResponseInput);

// src/graphql/resolvers/copilot.resolver.ts
var import_graphql_yoga = require("graphql-yoga");

// src/service-adapters/events.ts
var import_shared20 = require("@copilotkit/shared");
var import_rxjs3 = require("rxjs");

// src/lib/telemetry-client.ts
var import_shared19 = require("@copilotkit/shared");

// src/lib/runtime/remote-actions.ts
var import_shared14 = require("@copilotkit/shared");

// src/lib/runtime/remote-action-constructors.ts
var import_node_crypto2 = require("crypto");

// src/agents/langgraph/event-source.ts
var import_rxjs = require("rxjs");

// src/agents/langgraph/events.ts
var LangGraphEventTypes;
(function(LangGraphEventTypes2) {
  LangGraphEventTypes2["OnChainStart"] = "on_chain_start";
  LangGraphEventTypes2["OnChainStream"] = "on_chain_stream";
  LangGraphEventTypes2["OnChainEnd"] = "on_chain_end";
  LangGraphEventTypes2["OnChatModelStart"] = "on_chat_model_start";
  LangGraphEventTypes2["OnChatModelStream"] = "on_chat_model_stream";
  LangGraphEventTypes2["OnChatModelEnd"] = "on_chat_model_end";
  LangGraphEventTypes2["OnToolStart"] = "on_tool_start";
  LangGraphEventTypes2["OnToolEnd"] = "on_tool_end";
  LangGraphEventTypes2["OnCopilotKitStateSync"] = "on_copilotkit_state_sync";
  LangGraphEventTypes2["OnCopilotKitEmitMessage"] = "on_copilotkit_emit_message";
  LangGraphEventTypes2["OnCopilotKitEmitToolCall"] = "on_copilotkit_emit_tool_call";
  LangGraphEventTypes2["OnCustomEvent"] = "on_custom_event";
  LangGraphEventTypes2["OnInterrupt"] = "on_interrupt";
  LangGraphEventTypes2["OnCopilotKitInterrupt"] = "on_copilotkit_interrupt";
})(LangGraphEventTypes || (LangGraphEventTypes = {}));
var MetaEventNames;
(function(MetaEventNames2) {
  MetaEventNames2["LangGraphInterruptEvent"] = "LangGraphInterruptEvent";
  MetaEventNames2["CopilotKitLangGraphInterruptEvent"] = "CopilotKitLangGraphInterruptEvent";
})(MetaEventNames || (MetaEventNames = {}));
var CustomEventNames;
(function(CustomEventNames2) {
  CustomEventNames2["CopilotKitManuallyEmitMessage"] = "copilotkit_manually_emit_message";
  CustomEventNames2["CopilotKitManuallyEmitToolCall"] = "copilotkit_manually_emit_tool_call";
  CustomEventNames2["CopilotKitManuallyEmitIntermediateState"] = "copilotkit_manually_emit_intermediate_state";
  CustomEventNames2["CopilotKitExit"] = "copilotkit_exit";
})(CustomEventNames || (CustomEventNames = {}));

// src/agents/langgraph/event-source.ts
var import_shared7 = require("@copilotkit/shared");
var RemoteLangGraphEventSource = class {
  eventStream$ = new import_rxjs.ReplaySubject();
  shouldEmitToolCall(shouldEmitToolCalls, toolCallName) {
    if (typeof shouldEmitToolCalls === "boolean") {
      return shouldEmitToolCalls;
    }
    if (Array.isArray(shouldEmitToolCalls)) {
      return shouldEmitToolCalls.includes(toolCallName);
    }
    return shouldEmitToolCalls === toolCallName;
  }
  getCurrentContent(event) {
    var _a, _b, _c, _d, _e;
    const content = ((_c = (_b = (_a = event.data) == null ? void 0 : _a.chunk) == null ? void 0 : _b.kwargs) == null ? void 0 : _c.content) ?? ((_e = (_d = event.data) == null ? void 0 : _d.chunk) == null ? void 0 : _e.content);
    if (!content) {
      const toolCallChunks = this.getCurrentToolCallChunks(event) ?? [];
      for (const chunk of toolCallChunks) {
        if (chunk.args) {
          return chunk.args;
        }
      }
    }
    if (typeof content === "string") {
      return content;
    } else if (Array.isArray(content) && content.length > 0) {
      return content[0].text;
    }
    return null;
  }
  getCurrentMessageId(event) {
    var _a, _b, _c, _d, _e;
    return ((_c = (_b = (_a = event.data) == null ? void 0 : _a.chunk) == null ? void 0 : _b.kwargs) == null ? void 0 : _c.id) ?? ((_e = (_d = event.data) == null ? void 0 : _d.chunk) == null ? void 0 : _e.id);
  }
  getCurrentToolCallChunks(event) {
    var _a, _b, _c, _d, _e;
    return ((_c = (_b = (_a = event.data) == null ? void 0 : _a.chunk) == null ? void 0 : _b.kwargs) == null ? void 0 : _c.tool_call_chunks) ?? ((_e = (_d = event.data) == null ? void 0 : _d.chunk) == null ? void 0 : _e.tool_call_chunks);
  }
  getResponseMetadata(event) {
    var _a, _b, _c, _d, _e;
    return ((_c = (_b = (_a = event.data) == null ? void 0 : _a.chunk) == null ? void 0 : _b.kwargs) == null ? void 0 : _c.response_metadata) ?? ((_e = (_d = event.data) == null ? void 0 : _d.chunk) == null ? void 0 : _e.response_metadata);
  }
  processLangGraphEvents() {
    let lastEventWithState = null;
    return this.eventStream$.pipe((0, import_rxjs.scan)((acc, event) => {
      if (event.event === LangGraphEventTypes.OnChatModelStream) {
        const prevMessageId = acc.lastMessageId;
        acc.currentContent = this.getCurrentContent(event);
        acc.lastMessageId = this.getCurrentMessageId(event) ?? acc.lastMessageId;
        const toolCallChunks = this.getCurrentToolCallChunks(event) ?? [];
        const responseMetadata = this.getResponseMetadata(event);
        const toolCallCheck = toolCallChunks && toolCallChunks.length > 0;
        let isToolCallEnd = (responseMetadata == null ? void 0 : responseMetadata.finish_reason) === "tool_calls";
        acc.isToolCallStart = toolCallChunks.some((chunk) => chunk.name && chunk.id);
        acc.isMessageStart = prevMessageId !== acc.lastMessageId && !acc.isToolCallStart;
        let previousRoundHadToolCall = acc.isToolCall;
        acc.isToolCall = toolCallCheck;
        if (previousRoundHadToolCall && !toolCallCheck) {
          isToolCallEnd = true;
        }
        acc.isToolCallEnd = isToolCallEnd;
        acc.isMessageEnd = (responseMetadata == null ? void 0 : responseMetadata.finish_reason) === "stop";
        ({ name: acc.lastToolCallName, id: acc.lastToolCallId } = toolCallChunks.find((chunk) => chunk.name && chunk.id) ?? {
          name: acc.lastToolCallName,
          id: acc.lastToolCallId
        });
      }
      acc.event = event;
      lastEventWithState = acc;
      return acc;
    }, {
      event: null,
      isMessageStart: false,
      isMessageEnd: false,
      isToolCallStart: false,
      isToolCallEnd: false,
      isToolCall: false,
      lastMessageId: null,
      lastToolCallId: null,
      lastToolCallName: null,
      currentContent: null,
      processedToolCallIds: /* @__PURE__ */ new Set()
    }), (0, import_rxjs.mergeMap)((acc) => {
      const events = [];
      let shouldEmitMessages = true;
      let shouldEmitToolCalls = true;
      if (acc.event.event == LangGraphEventTypes.OnChatModelStream) {
        if ("copilotkit:emit-tool-calls" in (acc.event.metadata || {})) {
          shouldEmitToolCalls = acc.event.metadata["copilotkit:emit-tool-calls"];
        }
        if ("copilotkit:emit-messages" in (acc.event.metadata || {})) {
          shouldEmitMessages = acc.event.metadata["copilotkit:emit-messages"];
        }
      }
      if (acc.event.event === LangGraphEventTypes.OnInterrupt) {
        events.push({
          type: RuntimeEventTypes.MetaEvent,
          name: RuntimeMetaEventName.LangGraphInterruptEvent,
          value: acc.event.value
        });
      }
      if (acc.event.event === LangGraphEventTypes.OnCopilotKitInterrupt) {
        events.push({
          type: RuntimeEventTypes.MetaEvent,
          name: RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent,
          data: acc.event.data
        });
      }
      const responseMetadata = this.getResponseMetadata(acc.event);
      if (acc.isToolCallEnd && this.shouldEmitToolCall(shouldEmitToolCalls, acc.lastToolCallName) && acc.lastToolCallId && !acc.processedToolCallIds.has(acc.lastToolCallId)) {
        acc.processedToolCallIds.add(acc.lastToolCallId);
        events.push({
          type: RuntimeEventTypes.ActionExecutionEnd,
          actionExecutionId: acc.lastToolCallId
        });
      } else if ((responseMetadata == null ? void 0 : responseMetadata.finish_reason) === "stop" && shouldEmitMessages) {
        events.push({
          type: RuntimeEventTypes.TextMessageEnd,
          messageId: acc.lastMessageId
        });
      }
      switch (acc.event.event) {
        case LangGraphEventTypes.OnCustomEvent:
          if (acc.event.name === CustomEventNames.CopilotKitManuallyEmitMessage) {
            events.push({
              type: RuntimeEventTypes.TextMessageStart,
              messageId: acc.event.data.message_id
            });
            events.push({
              type: RuntimeEventTypes.TextMessageContent,
              messageId: acc.event.data.message_id,
              content: acc.event.data.message
            });
            events.push({
              type: RuntimeEventTypes.TextMessageEnd,
              messageId: acc.event.data.message_id
            });
          } else if (acc.event.name === CustomEventNames.CopilotKitManuallyEmitToolCall) {
            events.push({
              type: RuntimeEventTypes.ActionExecutionStart,
              actionExecutionId: acc.event.data.id,
              actionName: acc.event.data.name,
              parentMessageId: acc.event.data.id
            });
            events.push({
              type: RuntimeEventTypes.ActionExecutionArgs,
              actionExecutionId: acc.event.data.id,
              args: JSON.stringify(acc.event.data.args)
            });
            events.push({
              type: RuntimeEventTypes.ActionExecutionEnd,
              actionExecutionId: acc.event.data.id
            });
          }
          break;
        case LangGraphEventTypes.OnCopilotKitStateSync:
          events.push({
            type: RuntimeEventTypes.AgentStateMessage,
            threadId: acc.event.thread_id,
            role: acc.event.role,
            agentName: acc.event.agent_name,
            nodeName: acc.event.node_name,
            runId: acc.event.run_id,
            active: acc.event.active,
            state: JSON.stringify(acc.event.state),
            running: acc.event.running
          });
          break;
        case LangGraphEventTypes.OnChatModelStream:
          if (acc.isToolCallStart && this.shouldEmitToolCall(shouldEmitToolCalls, acc.lastToolCallName)) {
            events.push({
              type: RuntimeEventTypes.ActionExecutionStart,
              actionExecutionId: acc.lastToolCallId,
              actionName: acc.lastToolCallName,
              parentMessageId: acc.lastMessageId
            });
          } else if (acc.isMessageStart && shouldEmitMessages) {
            acc.processedToolCallIds.clear();
            events.push({
              type: RuntimeEventTypes.TextMessageStart,
              messageId: acc.lastMessageId
            });
          }
          if (acc.isToolCall && acc.currentContent && this.shouldEmitToolCall(shouldEmitToolCalls, acc.lastToolCallName)) {
            events.push({
              type: RuntimeEventTypes.ActionExecutionArgs,
              actionExecutionId: acc.lastToolCallId,
              args: acc.currentContent
            });
          } else if (!acc.isToolCall && acc.currentContent && shouldEmitMessages) {
            events.push({
              type: RuntimeEventTypes.TextMessageContent,
              messageId: acc.lastMessageId,
              content: acc.currentContent
            });
          }
          break;
      }
      return events;
    }), (0, import_rxjs.catchError)((error) => {
      console.error(error);
      const events = [];
      if ((lastEventWithState == null ? void 0 : lastEventWithState.lastMessageId) && !lastEventWithState.isToolCall) {
        events.push({
          type: RuntimeEventTypes.TextMessageEnd,
          messageId: lastEventWithState.lastMessageId
        });
      }
      if (lastEventWithState == null ? void 0 : lastEventWithState.lastToolCallId) {
        events.push({
          type: RuntimeEventTypes.ActionExecutionEnd,
          actionExecutionId: lastEventWithState.lastToolCallId
        });
      }
      const messageId = (0, import_shared7.randomId)();
      events.push({
        type: RuntimeEventTypes.TextMessageStart,
        messageId
      });
      events.push({
        type: RuntimeEventTypes.TextMessageContent,
        messageId,
        content: "\u274C An error occurred. Please try again."
      });
      events.push({
        type: RuntimeEventTypes.TextMessageEnd,
        messageId
      });
      return events;
    }));
  }
};
__name(RemoteLangGraphEventSource, "RemoteLangGraphEventSource");

// src/lib/runtime/remote-lg-action.ts
var import_langgraph_sdk = require("@langchain/langgraph-sdk");
var import_node_crypto = require("crypto");
var import_shared8 = require("@copilotkit/shared");
var import_partial_json = require("partial-json");
var import_shared9 = require("@copilotkit/shared");
var import_messages2 = require("@langchain/core/messages");
var activeInterruptEvent = false;
async function execute(args) {
  return new ReadableStream({
    async start(controller) {
      try {
        await streamEvents(controller, args);
        controller.close();
      } catch (err) {
        const cause = err == null ? void 0 : err.cause;
        const errorCode = (cause == null ? void 0 : cause.code) || (err == null ? void 0 : err.code);
        if (errorCode === "ECONNREFUSED") {
          throw new import_shared9.CopilotKitMisuseError({
            message: `
              The LangGraph client could not connect to the graph. Please further check previous logs, which includes further details.
              
              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`
          });
        } else {
          throw new import_shared9.CopilotKitMisuseError({
            message: `
              The LangGraph client threw unhandled error ${err}.
              
              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`
          });
        }
      }
    }
  });
}
__name(execute, "execute");
async function streamEvents(controller, args) {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  const { deploymentUrl, langsmithApiKey, threadId: argsInitialThreadId, agent, nodeName: initialNodeName, state: initialState, config: explicitConfig, messages, actions, logger: logger2, properties, metaEvents } = args;
  let nodeName = initialNodeName;
  let state = initialState;
  const { name, assistantId: initialAssistantId } = agent;
  const propertyHeaders = properties.authorization ? {
    authorization: `Bearer ${properties.authorization}`
  } : null;
  const client = new import_langgraph_sdk.Client({
    apiUrl: deploymentUrl,
    apiKey: langsmithApiKey,
    defaultHeaders: {
      ...propertyHeaders
    }
  });
  let threadId = argsInitialThreadId ?? (0, import_shared8.randomUUID)();
  if (argsInitialThreadId && argsInitialThreadId.startsWith("ck-")) {
    threadId = argsInitialThreadId.substring(3);
  }
  if (!(0, import_shared8.isValidUUID)(threadId)) {
    console.warn(`Cannot use the threadId ${threadId} with LangGraph Platform. Must be a valid UUID.`);
  }
  let wasInitiatedWithExistingThread = true;
  try {
    await client.threads.get(threadId);
  } catch (error) {
    wasInitiatedWithExistingThread = false;
    await client.threads.create({
      threadId
    });
  }
  let agentState = {
    values: {}
  };
  if (wasInitiatedWithExistingThread) {
    agentState = await client.threads.getState(threadId);
  }
  const agentStateValues = agentState.values;
  state.messages = agentStateValues.messages;
  const mode = threadId && nodeName != "__end__" && nodeName != void 0 && nodeName != null ? "continue" : "start";
  let formattedMessages = [];
  try {
    formattedMessages = copilotkitMessagesToLangChain(messages);
  } catch (e) {
    logger2.error(e, `Error event thrown: ${e.message}`);
  }
  state = langGraphDefaultMergeState(state, formattedMessages, actions, name);
  const streamInput = mode === "start" ? state : null;
  const payload = {
    input: streamInput,
    streamMode: [
      "events",
      "values",
      "updates"
    ],
    command: void 0
  };
  const lgInterruptMetaEvent = metaEvents == null ? void 0 : metaEvents.find((ev) => ev.name === MetaEventName.LangGraphInterruptEvent);
  if (activeInterruptEvent && !lgInterruptMetaEvent) {
    payload.command = {
      resume: state.messages
    };
  }
  if (lgInterruptMetaEvent == null ? void 0 : lgInterruptMetaEvent.response) {
    let response = lgInterruptMetaEvent.response;
    payload.command = {
      resume: (0, import_shared9.parseJson)(response, response)
    };
  }
  if (mode === "continue" && !activeInterruptEvent) {
    await client.threads.updateState(threadId, {
      values: state,
      asNode: nodeName
    });
  }
  let streamInfo = {
    hashedLgcKey: langsmithApiKey ? (0, import_node_crypto.createHash)("sha256").update(langsmithApiKey).digest("hex") : null
  };
  const assistants = await client.assistants.search();
  const retrievedAssistant = assistants.find((a) => a.name === name || a.assistant_id === initialAssistantId);
  if (!retrievedAssistant) {
    telemetry_client_default.capture("oss.runtime.agent_execution_stream_errored", {
      ...streamInfo,
      error: `Found no assistants for given information, while ${assistants.length} assistants exists`
    });
    console.error(`
      No agent found for the agent name specified in CopilotKit provider
      Please check your available agents or provide an agent ID in the LangGraph Platform endpoint definition.

      
      These are the available agents: [${assistants.map((a) => `${a.name} (ID: ${a.assistant_id})`).join(", ")}]
      `);
    throw new Error("No agent id found");
  }
  const assistantId = retrievedAssistant.assistant_id;
  const graphInfo = await client.assistants.getGraph(assistantId);
  const graphSchema = await client.assistants.getSchemas(assistantId);
  const schemaKeys = getSchemaKeys(graphSchema);
  if (explicitConfig) {
    let filteredConfigurable = retrievedAssistant.config.configurable;
    if (explicitConfig.configurable) {
      filteredConfigurable = (schemaKeys == null ? void 0 : schemaKeys.config) ? filterObjectBySchemaKeys(explicitConfig == null ? void 0 : explicitConfig.configurable, schemaKeys == null ? void 0 : schemaKeys.config) : explicitConfig == null ? void 0 : explicitConfig.configurable;
    }
    const newConfig = {
      ...retrievedAssistant.config,
      ...explicitConfig,
      configurable: filteredConfigurable
    };
    const isRecursionLimitSetToDefault = retrievedAssistant.config.recursion_limit == null && explicitConfig.recursion_limit === 25;
    const configsAreDifferent = JSON.stringify(newConfig) !== JSON.stringify(retrievedAssistant.config);
    const isOnlyRecursionLimitDifferent = isRecursionLimitSetToDefault && JSON.stringify({
      ...newConfig,
      recursion_limit: null
    }) === JSON.stringify({
      ...retrievedAssistant.config,
      recursion_limit: null
    });
    if (configsAreDifferent && !isOnlyRecursionLimitDifferent) {
      await client.assistants.update(assistantId, {
        config: newConfig
      });
    }
  }
  if (payload.input && (schemaKeys == null ? void 0 : schemaKeys.input)) {
    payload.input = filterObjectBySchemaKeys(payload.input, schemaKeys.input);
  }
  let streamingStateExtractor = new StreamingStateExtractor([]);
  let prevNodeName = null;
  let emitIntermediateStateUntilEnd = null;
  let shouldExit = false;
  let externalRunId = null;
  const streamResponse = client.runs.stream(threadId, assistantId, payload);
  const emit = /* @__PURE__ */ __name((message) => controller.enqueue(new TextEncoder().encode(message)), "emit");
  let latestStateValues = {};
  let updatedState = state;
  let manuallyEmittedState = null;
  activeInterruptEvent = false;
  try {
    telemetry_client_default.capture("oss.runtime.agent_execution_stream_started", {
      hashedLgcKey: streamInfo.hashedLgcKey
    });
    for await (let streamResponseChunk of streamResponse) {
      if (![
        "events",
        "values",
        "error",
        "updates"
      ].includes(streamResponseChunk.event))
        continue;
      if (streamResponseChunk.event === "error") {
        throw new Error(`Error event thrown: ${streamResponseChunk.data.message}`);
      }
      const chunk = streamResponseChunk;
      const interruptEvents = chunk.data.__interrupt__;
      if (interruptEvents == null ? void 0 : interruptEvents.length) {
        activeInterruptEvent = true;
        const interruptValue = interruptEvents == null ? void 0 : interruptEvents[0].value;
        if (typeof interruptValue != "string" && "__copilotkit_interrupt_value__" in interruptValue) {
          const evValue = interruptValue.__copilotkit_interrupt_value__;
          emit(JSON.stringify({
            event: LangGraphEventTypes.OnCopilotKitInterrupt,
            data: {
              value: typeof evValue === "string" ? evValue : JSON.stringify(evValue),
              messages: langchainMessagesToCopilotKit(interruptValue.__copilotkit_messages__)
            }
          }) + "\n");
        } else {
          emit(JSON.stringify({
            event: LangGraphEventTypes.OnInterrupt,
            value: typeof interruptValue === "string" ? interruptValue : JSON.stringify(interruptValue)
          }) + "\n");
        }
        continue;
      }
      if (streamResponseChunk.event === "updates")
        continue;
      if (streamResponseChunk.event === "values") {
        latestStateValues = chunk.data;
        continue;
      }
      const chunkData = chunk.data;
      const currentNodeName = chunkData.metadata.langgraph_node;
      const eventType = chunkData.event;
      const runId = chunkData.metadata.run_id;
      externalRunId = runId;
      const metadata = chunkData.metadata;
      if (((_b = (_a = chunkData.data) == null ? void 0 : _a.output) == null ? void 0 : _b.model) != null && ((_d = (_c = chunkData.data) == null ? void 0 : _c.output) == null ? void 0 : _d.model) != "") {
        streamInfo.provider = (_f = (_e = chunkData.data) == null ? void 0 : _e.output) == null ? void 0 : _f.model;
      }
      if (metadata.langgraph_host != null && metadata.langgraph_host != "") {
        streamInfo.langGraphHost = metadata.langgraph_host;
      }
      if (metadata.langgraph_version != null && metadata.langgraph_version != "") {
        streamInfo.langGraphVersion = metadata.langgraph_version;
      }
      shouldExit = shouldExit || eventType === LangGraphEventTypes.OnCustomEvent && chunkData.name === CustomEventNames.CopilotKitExit;
      const emitIntermediateState = metadata["copilotkit:emit-intermediate-state"];
      const manuallyEmitIntermediateState = eventType === LangGraphEventTypes.OnCustomEvent && chunkData.name === CustomEventNames.CopilotKitManuallyEmitIntermediateState;
      const exitingNode = nodeName === currentNodeName && eventType === LangGraphEventTypes.OnChainEnd;
      if (exitingNode) {
        manuallyEmittedState = null;
      }
      if (graphInfo["nodes"].some((node) => node.id === currentNodeName)) {
        nodeName = currentNodeName;
      }
      updatedState = manuallyEmittedState ?? latestStateValues;
      if (!nodeName) {
        continue;
      }
      if (manuallyEmitIntermediateState) {
        manuallyEmittedState = chunkData.data;
        emit(getStateSyncEvent({
          threadId,
          runId,
          agentName: agent.name,
          nodeName,
          state: manuallyEmittedState,
          running: true,
          active: true,
          schemaKeys
        }));
        continue;
      }
      if (emitIntermediateState && emitIntermediateStateUntilEnd == null) {
        emitIntermediateStateUntilEnd = nodeName;
      }
      if (emitIntermediateState && eventType === LangGraphEventTypes.OnChatModelStart) {
        streamingStateExtractor = new StreamingStateExtractor(emitIntermediateState);
      }
      if (emitIntermediateState && eventType === LangGraphEventTypes.OnChatModelStream) {
        streamingStateExtractor.bufferToolCalls(chunkData);
      }
      if (emitIntermediateStateUntilEnd !== null) {
        updatedState = {
          ...updatedState,
          ...streamingStateExtractor.extractState()
        };
      }
      if (!emitIntermediateState && currentNodeName === emitIntermediateStateUntilEnd && eventType === LangGraphEventTypes.OnChainEnd) {
        emitIntermediateStateUntilEnd = null;
      }
      if (JSON.stringify(updatedState) !== JSON.stringify(state) || prevNodeName != nodeName || exitingNode) {
        state = updatedState;
        prevNodeName = nodeName;
        emit(getStateSyncEvent({
          threadId,
          runId,
          agentName: agent.name,
          nodeName,
          state,
          running: true,
          active: !exitingNode,
          schemaKeys
        }));
      }
      emit(JSON.stringify(chunkData) + "\n");
    }
    state = await client.threads.getState(threadId);
    const interrupts = (_h = (_g = state.tasks) == null ? void 0 : _g[0]) == null ? void 0 : _h.interrupts;
    nodeName = interrupts ? nodeName : Object.keys(state.metadata.writes)[0];
    const isEndNode = state.next.length === 0 && !interrupts;
    telemetry_client_default.capture("oss.runtime.agent_execution_stream_ended", streamInfo);
    emit(getStateSyncEvent({
      threadId,
      runId: externalRunId,
      agentName: agent.name,
      nodeName: isEndNode ? "__end__" : nodeName,
      state: state.values,
      running: !shouldExit,
      active: false,
      includeMessages: true,
      schemaKeys
    }));
    return Promise.resolve();
  } catch (e) {
    logger2.error(e);
    telemetry_client_default.capture("oss.runtime.agent_execution_stream_errored", {
      ...streamInfo,
      error: e.message
    });
    return Promise.resolve();
  }
}
__name(streamEvents, "streamEvents");
function getStateSyncEvent({ threadId, runId, agentName, nodeName, state, running, active, includeMessages = false, schemaKeys }) {
  if (!includeMessages) {
    state = Object.keys(state).reduce((acc, key) => {
      if (key !== "messages") {
        acc[key] = state[key];
      }
      return acc;
    }, {});
  } else {
    state = {
      ...state,
      messages: langchainMessagesToCopilotKit(state.messages || [])
    };
  }
  if (schemaKeys == null ? void 0 : schemaKeys.output) {
    state = filterObjectBySchemaKeys(state, schemaKeys.output);
  }
  return JSON.stringify({
    event: LangGraphEventTypes.OnCopilotKitStateSync,
    thread_id: threadId,
    run_id: runId,
    agent_name: agentName,
    node_name: nodeName,
    active,
    state,
    running,
    role: "assistant"
  }) + "\n";
}
__name(getStateSyncEvent, "getStateSyncEvent");
var StreamingStateExtractor = /* @__PURE__ */ __name(class StreamingStateExtractor2 {
  emitIntermediateState;
  toolCallBuffer;
  currentToolCall;
  previouslyParsableState;
  constructor(emitIntermediateState) {
    this.emitIntermediateState = emitIntermediateState;
    this.toolCallBuffer = {};
    this.currentToolCall = null;
    this.previouslyParsableState = {};
  }
  bufferToolCalls(event) {
    if (event.data.chunk.tool_call_chunks.length > 0) {
      const chunk = event.data.chunk.tool_call_chunks[0];
      if (chunk.name !== null && chunk.name !== void 0) {
        this.currentToolCall = chunk.name;
        this.toolCallBuffer[this.currentToolCall] = chunk.args;
      } else if (this.currentToolCall !== null && this.currentToolCall !== void 0) {
        this.toolCallBuffer[this.currentToolCall] += chunk.args;
      }
    }
  }
  getEmitStateConfig(currentToolName) {
    for (const config2 of this.emitIntermediateState) {
      const stateKey = config2["state_key"];
      const tool = config2["tool"];
      const toolArgument = config2["tool_argument"];
      if (currentToolName === tool) {
        return [
          toolArgument,
          stateKey
        ];
      }
    }
    return [
      null,
      null
    ];
  }
  extractState() {
    const state = {};
    for (const [key, value] of Object.entries(this.toolCallBuffer)) {
      const [argumentName, stateKey] = this.getEmitStateConfig(key);
      if (stateKey === null) {
        continue;
      }
      let parsedValue;
      try {
        parsedValue = (0, import_partial_json.parse)(value);
      } catch (error) {
        if (key in this.previouslyParsableState) {
          parsedValue = this.previouslyParsableState[key];
        } else {
          continue;
        }
      }
      this.previouslyParsableState[key] = parsedValue;
      if (!argumentName) {
        state[stateKey] = parsedValue;
      } else {
        state[stateKey] = parsedValue[argumentName];
      }
    }
    return state;
  }
}, "StreamingStateExtractor");
function langGraphDefaultMergeState(state, messages, actions, agentName) {
  if (messages.length > 0 && "role" in messages[0] && messages[0].role === "system") {
    messages = messages.slice(1);
  }
  const existingMessages = state.messages || [];
  const existingMessageIds = new Set(existingMessages.map((message) => message.id));
  const messageIds = new Set(messages.map((message) => message.id));
  let removedMessages = [];
  if (messages.length < existingMessages.length) {
    removedMessages = existingMessages.filter((m) => !messageIds.has(m.id)).map((m) => new import_messages2.RemoveMessage({
      id: m.id
    }));
  }
  const newMessages = messages.filter((message) => !existingMessageIds.has(message.id));
  return {
    ...state,
    messages: [
      ...removedMessages,
      ...newMessages
    ],
    copilotkit: {
      actions
    }
  };
}
__name(langGraphDefaultMergeState, "langGraphDefaultMergeState");
function langchainMessagesToCopilotKit(messages) {
  const result = [];
  const tool_call_names = {};
  for (const message of messages) {
    if (message.type === "ai") {
      for (const tool_call of message.tool_calls) {
        tool_call_names[tool_call.id] = tool_call.name;
      }
    }
  }
  for (const message of messages) {
    let content = message.content;
    if (content instanceof Array) {
      content = content[0];
    }
    if (content instanceof Object) {
      content = content.text;
    }
    if (message.type === "human") {
      result.push({
        role: "user",
        content,
        id: message.id
      });
    } else if (message.type === "system") {
      result.push({
        role: "system",
        content,
        id: message.id
      });
    } else if (message.type === "ai") {
      if (message.tool_calls && message.tool_calls.length > 0) {
        for (const tool_call of message.tool_calls) {
          result.push({
            id: tool_call.id,
            name: tool_call.name,
            arguments: tool_call.args,
            parentMessageId: message.id
          });
        }
      } else {
        result.push({
          role: "assistant",
          content,
          id: message.id,
          parentMessageId: message.id
        });
      }
    } else if (message.type === "tool") {
      const actionName = tool_call_names[message.tool_call_id] || message.name || "";
      result.push({
        actionExecutionId: message.tool_call_id,
        actionName,
        result: content,
        id: message.id
      });
    }
  }
  const resultsDict = {};
  for (const msg of result) {
    if (msg.actionExecutionId) {
      resultsDict[msg.actionExecutionId] = msg;
    }
  }
  const reorderedResult = [];
  for (const msg of result) {
    if (!("actionExecutionId" in msg)) {
      reorderedResult.push(msg);
    }
    if ("arguments" in msg) {
      const msgId = msg.id;
      if (msgId in resultsDict) {
        reorderedResult.push(resultsDict[msgId]);
      }
    }
  }
  return reorderedResult;
}
__name(langchainMessagesToCopilotKit, "langchainMessagesToCopilotKit");
function copilotkitMessagesToLangChain(messages) {
  const result = [];
  const processedActionExecutions = /* @__PURE__ */ new Set();
  for (const message of messages) {
    if (message.isTextMessage()) {
      if (message.role === "user") {
        result.push({
          ...message,
          role: MessageRole.user
        });
      } else if (message.role === "system") {
        result.push({
          ...message,
          role: MessageRole.system
        });
      } else if (message.role === "assistant") {
        result.push({
          ...message,
          role: MessageRole.assistant
        });
      }
      continue;
    }
    if (message.isImageMessage()) {
      if (message.role === "user") {
        result.push({
          ...message,
          role: MessageRole.user,
          content: ""
        });
      } else if (message.role === "assistant") {
        result.push({
          ...message,
          role: MessageRole.assistant,
          content: ""
        });
      }
      continue;
    }
    if (message.isActionExecutionMessage()) {
      const messageId = message.parentMessageId ?? message.id;
      if (processedActionExecutions.has(messageId)) {
        continue;
      }
      processedActionExecutions.add(messageId);
      const relatedActionExecutions = messages.filter((m) => m.isActionExecutionMessage() && (m.parentMessageId && m.parentMessageId === messageId || m.id === messageId));
      const tool_calls = relatedActionExecutions.map((m) => ({
        name: m.name,
        args: m.arguments,
        id: m.id
      }));
      result.push({
        id: messageId,
        type: "ActionExecutionMessage",
        content: "",
        tool_calls,
        role: MessageRole.assistant
      });
      continue;
    }
    if (message.isResultMessage()) {
      result.push({
        type: message.type,
        content: message.result,
        id: message.id,
        tool_call_id: message.actionExecutionId,
        name: message.actionName,
        role: MessageRole.tool
      });
      continue;
    }
    throw new Error(`Unknown message type ${message.type}`);
  }
  return result;
}
__name(copilotkitMessagesToLangChain, "copilotkitMessagesToLangChain");
function getSchemaKeys(graphSchema) {
  const CONSTANT_KEYS = [
    "messages",
    "copilotkit"
  ];
  let configSchema = null;
  if (graphSchema.config_schema.properties) {
    configSchema = Object.keys(graphSchema.config_schema.properties);
  }
  if (!graphSchema.input_schema.properties || !graphSchema.output_schema.properties) {
    return configSchema;
  }
  const inputSchema = Object.keys(graphSchema.input_schema.properties);
  const outputSchema = Object.keys(graphSchema.output_schema.properties);
  return {
    input: inputSchema && inputSchema.length ? [
      ...inputSchema,
      ...CONSTANT_KEYS
    ] : null,
    output: outputSchema && outputSchema.length ? [
      ...outputSchema,
      ...CONSTANT_KEYS
    ] : null,
    config: configSchema
  };
}
__name(getSchemaKeys, "getSchemaKeys");
function filterObjectBySchemaKeys(obj, schemaKeys) {
  return Object.fromEntries(Object.entries(obj).filter(([key]) => schemaKeys.includes(key)));
}
__name(filterObjectBySchemaKeys, "filterObjectBySchemaKeys");

// src/lib/runtime/remote-action-constructors.ts
var import_shared10 = require("@copilotkit/shared");

// src/lib/streaming.ts
async function writeJsonLineResponseToEventStream(response, eventStream$) {
  const reader = response.getReader();
  const decoder = new TextDecoder();
  let buffer = [];
  function flushBuffer() {
    const currentBuffer = buffer.join("");
    if (currentBuffer.trim().length === 0) {
      return;
    }
    const parts = currentBuffer.split("\n");
    if (parts.length === 0) {
      return;
    }
    const lastPartIsComplete = currentBuffer.endsWith("\n");
    buffer = [];
    if (!lastPartIsComplete) {
      buffer.push(parts.pop());
    }
    parts.map((part) => part.trim()).filter((part) => part != "").forEach((part) => {
      eventStream$.next(JSON.parse(part));
    });
  }
  __name(flushBuffer, "flushBuffer");
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (!done) {
        buffer.push(decoder.decode(value, {
          stream: true
        }));
      }
      flushBuffer();
      if (done) {
        break;
      }
    }
  } catch (error) {
    console.error("Error in stream", error);
    eventStream$.error(error);
    return;
  }
  eventStream$.complete();
}
__name(writeJsonLineResponseToEventStream, "writeJsonLineResponseToEventStream");

// src/lib/runtime/remote-action-constructors.ts
var import_shared11 = require("@copilotkit/shared");
var import_shared12 = require("@copilotkit/shared");
function constructLGCRemoteAction({ endpoint, graphqlContext, logger: logger2, messages, agentStates }) {
  const agents = endpoint.agents.map((agent) => ({
    name: agent.name,
    description: agent.description,
    parameters: [],
    handler: async (_args) => {
    },
    remoteAgentHandler: async ({ name, actionInputsWithoutAgents, threadId, nodeName, additionalMessages = [], metaEvents }) => {
      logger2.debug({
        actionName: agent.name
      }, "Executing LangGraph Platform agent");
      telemetry_client_default.capture("oss.runtime.remote_action_executed", {
        agentExecution: true,
        type: "langgraph-platform",
        agentsAmount: endpoint.agents.length,
        hashedLgcKey: endpoint.langsmithApiKey ? (0, import_node_crypto2.createHash)("sha256").update(endpoint.langsmithApiKey).digest("hex") : null
      });
      let state = {};
      let config2 = {};
      if (agentStates) {
        const jsonState = agentStates.find((state2) => state2.agentName === name);
        if (jsonState) {
          state = (0, import_shared12.parseJson)(jsonState.state, {});
          config2 = (0, import_shared12.parseJson)(jsonState.config, {});
        }
      }
      try {
        const response = await execute({
          logger: logger2.child({
            component: "remote-actions.remote-lg-action.streamEvents"
          }),
          deploymentUrl: endpoint.deploymentUrl,
          langsmithApiKey: endpoint.langsmithApiKey,
          agent,
          threadId,
          nodeName,
          messages: [
            ...messages,
            ...additionalMessages
          ],
          state,
          config: config2,
          properties: graphqlContext.properties,
          actions: (0, import_shared12.tryMap)(actionInputsWithoutAgents, (action) => ({
            name: action.name,
            description: action.description,
            parameters: JSON.parse(action.jsonSchema)
          })),
          metaEvents
        });
        const eventSource = new RemoteLangGraphEventSource();
        writeJsonLineResponseToEventStream(response, eventSource.eventStream$);
        return eventSource.processLangGraphEvents();
      } catch (error) {
        logger2.error({
          url: endpoint.deploymentUrl,
          status: 500,
          body: error.message
        }, "Failed to execute LangGraph Platform agent");
        throw new Error("Failed to execute LangGraph Platform agent");
      }
    }
  }));
  return [
    ...agents
  ];
}
__name(constructLGCRemoteAction, "constructLGCRemoteAction");
var RemoteAgentType;
(function(RemoteAgentType2) {
  RemoteAgentType2["LangGraph"] = "langgraph";
  RemoteAgentType2["CrewAI"] = "crewai";
})(RemoteAgentType || (RemoteAgentType = {}));
function constructRemoteActions({ json, url, onBeforeRequest, graphqlContext, logger: logger2, messages, agentStates }) {
  const totalAgents = Array.isArray(json["agents"]) ? json["agents"].length : 0;
  const actions = json["actions"].map((action) => ({
    name: action.name,
    description: action.description,
    parameters: action.parameters,
    handler: async (args) => {
      logger2.debug({
        actionName: action.name,
        args
      }, "Executing remote action");
      const headers = createHeaders(onBeforeRequest, graphqlContext);
      telemetry_client_default.capture("oss.runtime.remote_action_executed", {
        agentExecution: false,
        type: "self-hosted",
        agentsAmount: totalAgents
      });
      const fetchUrl = `${url}/actions/execute`;
      try {
        const response = await fetch(fetchUrl, {
          method: "POST",
          headers,
          body: JSON.stringify({
            name: action.name,
            arguments: args,
            properties: graphqlContext.properties
          })
        });
        if (!response.ok) {
          logger2.error({
            url,
            status: response.status,
            body: await response.text()
          }, "Failed to execute remote action");
          if (response.status === 404) {
            throw new import_shared11.CopilotKitApiDiscoveryError({
              url: fetchUrl
            });
          }
          throw new import_shared11.ResolvedCopilotKitError({
            status: response.status,
            url: fetchUrl,
            isRemoteEndpoint: true
          });
        }
        const requestResult = await response.json();
        const result = requestResult["result"];
        logger2.debug({
          actionName: action.name,
          result
        }, "Executed remote action");
        return result;
      } catch (error) {
        if (error instanceof import_shared10.CopilotKitError) {
          throw error;
        }
        throw new import_shared10.CopilotKitLowLevelError({
          error,
          url: fetchUrl
        });
      }
    }
  }));
  const agents = totalAgents ? json["agents"].map((agent) => ({
    name: agent.name,
    description: agent.description,
    parameters: [],
    handler: async (_args) => {
    },
    remoteAgentHandler: async ({ name, actionInputsWithoutAgents, threadId, nodeName, additionalMessages = [], metaEvents }) => {
      logger2.debug({
        actionName: agent.name
      }, "Executing remote agent");
      const headers = createHeaders(onBeforeRequest, graphqlContext);
      telemetry_client_default.capture("oss.runtime.remote_action_executed", {
        agentExecution: true,
        type: "self-hosted",
        agentsAmount: json["agents"].length
      });
      let state = {};
      let config2 = {};
      if (agentStates) {
        const jsonState = agentStates.find((state2) => state2.agentName === name);
        if (jsonState) {
          state = (0, import_shared12.parseJson)(jsonState.state, {});
          config2 = (0, import_shared12.parseJson)(jsonState.config, {});
        }
      }
      const fetchUrl = `${url}/agents/execute`;
      try {
        const response = await fetch(fetchUrl, {
          method: "POST",
          headers,
          body: JSON.stringify({
            name,
            threadId,
            nodeName,
            messages: [
              ...messages,
              ...additionalMessages
            ],
            state,
            config: config2,
            properties: graphqlContext.properties,
            actions: (0, import_shared12.tryMap)(actionInputsWithoutAgents, (action) => ({
              name: action.name,
              description: action.description,
              parameters: JSON.parse(action.jsonSchema)
            })),
            metaEvents
          })
        });
        if (!response.ok) {
          logger2.error({
            url,
            status: response.status,
            body: await response.text()
          }, "Failed to execute remote agent");
          if (response.status === 404) {
            throw new import_shared11.CopilotKitApiDiscoveryError({
              url: fetchUrl
            });
          }
          throw new import_shared11.ResolvedCopilotKitError({
            status: response.status,
            url: fetchUrl,
            isRemoteEndpoint: true
          });
        }
        if (agent.type === "langgraph") {
          const eventSource = new RemoteLangGraphEventSource();
          writeJsonLineResponseToEventStream(response.body, eventSource.eventStream$);
          return eventSource.processLangGraphEvents();
        } else if (agent.type === "crewai") {
          const eventStream$ = new RuntimeEventSubject();
          writeJsonLineResponseToEventStream(response.body, eventStream$);
          return eventStream$;
        } else {
          throw new Error("Unsupported agent type");
        }
      } catch (error) {
        if (error instanceof import_shared10.CopilotKitError) {
          throw error;
        }
        throw new import_shared10.CopilotKitLowLevelError({
          error,
          url: fetchUrl
        });
      }
    }
  })) : [];
  return [
    ...actions,
    ...agents
  ];
}
__name(constructRemoteActions, "constructRemoteActions");
function createHeaders(onBeforeRequest, graphqlContext) {
  const headers = {
    "Content-Type": "application/json"
  };
  if (onBeforeRequest) {
    const { headers: additionalHeaders } = onBeforeRequest({
      ctx: graphqlContext
    });
    if (additionalHeaders) {
      Object.assign(headers, additionalHeaders);
    }
  }
  return headers;
}
__name(createHeaders, "createHeaders");

// src/lib/runtime/remote-actions.ts
var import_shared15 = require("@copilotkit/shared");

// src/lib/runtime/agentwire-action.ts
var import_shared13 = require("@copilotkit/shared");
function constructAgentWireRemoteAction({ logger: logger2, messages, agentStates, agent }) {
  const action = {
    name: agent.agentId,
    description: agent.description,
    parameters: [],
    handler: async (_args) => {
    },
    remoteAgentHandler: async ({ actionInputsWithoutAgents, threadId }) => {
      logger2.debug({
        actionName: agent.agentId
      }, "Executing remote agent");
      const agentWireMessages = convertMessagesToAgentWire(messages);
      agent.messages = agentWireMessages;
      agent.threadId = threadId;
      telemetry_client_default.capture("oss.runtime.remote_action_executed", {
        agentExecution: true,
        type: "self-hosted",
        agentsAmount: 1
      });
      let state = {};
      if (agentStates) {
        const jsonState = agentStates.find((state2) => state2.agentName === agent.agentId);
        if (jsonState) {
          state = (0, import_shared13.parseJson)(jsonState.state, {});
        }
      }
      agent.state = state;
      const tools = actionInputsWithoutAgents.map((input) => {
        return {
          name: input.name,
          description: input.description,
          parameters: JSON.parse(input.jsonSchema)
        };
      });
      return agent.legacy_to_be_removed_runAgentBridged({
        tools
      });
    }
  };
  return [
    action
  ];
}
__name(constructAgentWireRemoteAction, "constructAgentWireRemoteAction");
function convertMessagesToAgentWire(messages) {
  const result = [];
  for (const message of messages) {
    if (message.isTextMessage()) {
      result.push({
        id: message.id,
        role: message.role,
        content: message.content
      });
    } else if (message.isActionExecutionMessage()) {
      const toolCall = {
        id: message.id,
        type: "function",
        function: {
          name: message.name,
          arguments: JSON.stringify(message.arguments)
        }
      };
      if (message.parentMessageId && result.some((m) => m.id === message.parentMessageId)) {
        const parentMessage = result.find((m) => m.id === message.parentMessageId);
        if (parentMessage.toolCalls === void 0) {
          parentMessage.toolCalls = [];
        }
        parentMessage.toolCalls.push(toolCall);
      } else {
        result.push({
          id: message.parentMessageId ?? message.id,
          role: "assistant",
          toolCalls: [
            toolCall
          ]
        });
      }
    } else if (message.isResultMessage()) {
      result.push({
        id: message.id,
        role: "tool",
        content: message.result,
        toolCallId: message.actionExecutionId
      });
    }
  }
  return result;
}
__name(convertMessagesToAgentWire, "convertMessagesToAgentWire");

// src/lib/runtime/remote-actions.ts
var EndpointType;
(function(EndpointType2) {
  EndpointType2["CopilotKit"] = "copilotKit";
  EndpointType2["LangGraphPlatform"] = "langgraph-platform";
})(EndpointType || (EndpointType = {}));
function isRemoteAgentAction(action) {
  if (!action) {
    return false;
  }
  return typeof action.remoteAgentHandler === "function";
}
__name(isRemoteAgentAction, "isRemoteAgentAction");
async function fetchRemoteInfo({ url, onBeforeRequest, graphqlContext, logger: logger2, frontendUrl }) {
  logger2.debug({
    url
  }, "Fetching actions from url");
  const headers = createHeaders(onBeforeRequest, graphqlContext);
  const fetchUrl = `${url}/info`;
  try {
    const response = await fetch(fetchUrl, {
      method: "POST",
      headers,
      body: JSON.stringify({
        properties: graphqlContext.properties,
        frontendUrl
      })
    });
    if (!response.ok) {
      logger2.error({
        url,
        status: response.status,
        body: await response.text()
      }, "Failed to fetch actions from url");
      throw new import_shared15.ResolvedCopilotKitError({
        status: response.status,
        url: fetchUrl,
        isRemoteEndpoint: true
      });
    }
    const json = await response.json();
    logger2.debug({
      json
    }, "Fetched actions from url");
    return json;
  } catch (error) {
    if (error instanceof import_shared15.CopilotKitError) {
      throw error;
    }
    throw new import_shared15.CopilotKitLowLevelError({
      error,
      url: fetchUrl
    });
  }
}
__name(fetchRemoteInfo, "fetchRemoteInfo");
async function setupRemoteActions({ remoteEndpointDefinitions, graphqlContext, messages, agentStates, frontendUrl, agents }) {
  const logger2 = graphqlContext.logger.child({
    component: "remote-actions.fetchRemoteActions"
  });
  logger2.debug({
    remoteEndpointDefinitions
  }, "Fetching from remote endpoints");
  const filtered = remoteEndpointDefinitions.filter((value, index, self) => {
    if (value.type === "langgraph-platform") {
      return value;
    }
    return index === self.findIndex((t) => t.url === value.url);
  });
  const result = await Promise.all(filtered.map(async (endpoint) => {
    if (endpoint.type === "langgraph-platform") {
      return constructLGCRemoteAction({
        endpoint,
        messages,
        graphqlContext,
        logger: logger2.child({
          component: "remote-actions.constructLGCRemoteAction",
          endpoint
        }),
        agentStates
      });
    }
    const json = await fetchRemoteInfo({
      url: endpoint.url,
      onBeforeRequest: endpoint.onBeforeRequest,
      graphqlContext,
      logger: logger2.child({
        component: "remote-actions.fetchActionsFromUrl",
        endpoint
      }),
      frontendUrl
    });
    return constructRemoteActions({
      json,
      messages,
      url: endpoint.url,
      onBeforeRequest: endpoint.onBeforeRequest,
      graphqlContext,
      logger: logger2.child({
        component: "remote-actions.constructActions",
        endpoint
      }),
      agentStates
    });
  }));
  for (const [key, agent] of Object.entries(agents)) {
    if (agent.agentId !== void 0 && agent.agentId !== key) {
      throw new import_shared15.CopilotKitError({
        message: `Agent ${key} has agentId ${agent.agentId} which does not match the key ${key}`,
        code: import_shared14.CopilotKitErrorCode.UNKNOWN
      });
    } else if (agent.agentId === void 0) {
      agent.agentId = key;
    }
    result.push(constructAgentWireRemoteAction({
      logger: logger2,
      messages,
      agentStates,
      agent
    }));
  }
  return result.flat();
}
__name(setupRemoteActions, "setupRemoteActions");

// src/lib/telemetry-client.ts
var import_node_crypto3 = require("crypto");

// src/lib/runtime/copilot-runtime.ts
var import_shared18 = require("@copilotkit/shared");

// src/service-adapters/langchain/langserve.ts
var import_remote = require("langchain/runnables/remote");
var RemoteChain = class {
  name;
  description;
  chainUrl;
  parameters;
  parameterType;
  constructor(options) {
    this.name = options.name;
    this.description = options.description;
    this.chainUrl = options.chainUrl;
    this.parameters = options.parameters;
    this.parameterType = options.parameterType || "multi";
  }
  async toAction() {
    if (!this.parameters) {
      await this.inferLangServeParameters();
    }
    return {
      name: this.name,
      description: this.description,
      parameters: this.parameters,
      handler: async (args) => {
        const runnable = new import_remote.RemoteRunnable({
          url: this.chainUrl
        });
        let input;
        if (this.parameterType === "single") {
          input = args[Object.keys(args)[0]];
        } else {
          input = args;
        }
        return await runnable.invoke(input);
      }
    };
  }
  async inferLangServeParameters() {
    const supportedTypes = [
      "string",
      "number",
      "boolean"
    ];
    let schemaUrl = this.chainUrl.replace(/\/+$/, "") + "/input_schema";
    let schema = await fetch(schemaUrl).then((res) => res.json()).catch(() => {
      throw new Error("Failed to fetch langserve schema at " + schemaUrl);
    });
    if (supportedTypes.includes(schema.type)) {
      this.parameterType = "single";
      this.parameters = [
        {
          name: "input",
          type: schema.type,
          description: "The input to the chain"
        }
      ];
    } else if (schema.type === "object") {
      this.parameterType = "multi";
      this.parameters = Object.keys(schema.properties).map((key) => {
        var _a;
        let property = schema.properties[key];
        if (!supportedTypes.includes(property.type)) {
          throw new Error("Unsupported schema type");
        }
        return {
          name: key,
          type: property.type,
          description: property.description || "",
          required: ((_a = schema.required) == null ? void 0 : _a.includes(key)) || false
        };
      });
    } else {
      throw new Error("Unsupported schema type");
    }
  }
};
__name(RemoteChain, "RemoteChain");

// src/service-adapters/empty/empty-adapter.ts
var import_shared16 = require("@copilotkit/shared");
var EmptyAdapter = class {
  async process(request) {
    return {
      threadId: request.threadId || (0, import_shared16.randomUUID)()
    };
  }
};
__name(EmptyAdapter, "EmptyAdapter");

// src/graphql/types/converted/index.ts
var Message = class extends BaseMessageInput {
  type;
  isTextMessage() {
    return this.type === "TextMessage";
  }
  isActionExecutionMessage() {
    return this.type === "ActionExecutionMessage";
  }
  isResultMessage() {
    return this.type === "ResultMessage";
  }
  isAgentStateMessage() {
    return this.type === "AgentStateMessage";
  }
  isImageMessage() {
    return this.type === "ImageMessage";
  }
};
__name(Message, "Message");
var TextMessage = class extends Message {
  type = "TextMessage";
  content;
  role;
  parentMessageId;
};
__name(TextMessage, "TextMessage");
var ActionExecutionMessage = class extends Message {
  type = "ActionExecutionMessage";
  name;
  arguments;
  parentMessageId;
};
__name(ActionExecutionMessage, "ActionExecutionMessage");
var ResultMessage = class extends Message {
  type = "ResultMessage";
  actionExecutionId;
  actionName;
  result;
  static encodeResult(result, error) {
    const errorObj = error ? typeof error === "string" ? {
      code: "ERROR",
      message: error
    } : error instanceof Error ? {
      code: "ERROR",
      message: error.message
    } : error : void 0;
    if (errorObj) {
      return JSON.stringify({
        error: errorObj,
        result: result || ""
      });
    }
    if (result === void 0) {
      return "";
    }
    return typeof result === "string" ? result : JSON.stringify(result);
  }
  static decodeResult(result) {
    if (!result) {
      return {
        result: ""
      };
    }
    try {
      const parsed = JSON.parse(result);
      if (parsed && typeof parsed === "object") {
        if ("error" in parsed) {
          return {
            error: parsed.error,
            result: parsed.result || ""
          };
        }
        return {
          result: JSON.stringify(parsed)
        };
      }
      return {
        result
      };
    } catch (e) {
      return {
        result
      };
    }
  }
  hasError() {
    try {
      const { error } = ResultMessage.decodeResult(this.result);
      return !!error;
    } catch {
      return false;
    }
  }
  getError() {
    try {
      const { error } = ResultMessage.decodeResult(this.result);
      return error;
    } catch {
      return void 0;
    }
  }
};
__name(ResultMessage, "ResultMessage");
var AgentStateMessage = class extends Message {
  type = "AgentStateMessage";
  threadId;
  agentName;
  nodeName;
  runId;
  active;
  role;
  state;
  running;
};
__name(AgentStateMessage, "AgentStateMessage");
var ImageMessage = class extends Message {
  type = "ImageMessage";
  format;
  bytes;
  role;
  parentMessageId;
};
__name(ImageMessage, "ImageMessage");

// src/service-adapters/conversion.ts
var import_class_transformer = require("class-transformer");
var import_shared17 = require("@copilotkit/shared");
function convertGqlInputToMessages(inputMessages) {
  const messages = (0, import_shared17.tryMap)(inputMessages, (message) => {
    if (message.textMessage) {
      return (0, import_class_transformer.plainToInstance)(TextMessage, {
        id: message.id,
        createdAt: message.createdAt,
        role: message.textMessage.role,
        content: message.textMessage.content,
        parentMessageId: message.textMessage.parentMessageId
      });
    } else if (message.imageMessage) {
      return (0, import_class_transformer.plainToInstance)(ImageMessage, {
        id: message.id,
        createdAt: message.createdAt,
        role: message.imageMessage.role,
        bytes: message.imageMessage.bytes,
        format: message.imageMessage.format,
        parentMessageId: message.imageMessage.parentMessageId
      });
    } else if (message.actionExecutionMessage) {
      return (0, import_class_transformer.plainToInstance)(ActionExecutionMessage, {
        id: message.id,
        createdAt: message.createdAt,
        name: message.actionExecutionMessage.name,
        arguments: JSON.parse(message.actionExecutionMessage.arguments),
        parentMessageId: message.actionExecutionMessage.parentMessageId
      });
    } else if (message.resultMessage) {
      return (0, import_class_transformer.plainToInstance)(ResultMessage, {
        id: message.id,
        createdAt: message.createdAt,
        actionExecutionId: message.resultMessage.actionExecutionId,
        actionName: message.resultMessage.actionName,
        result: message.resultMessage.result
      });
    } else if (message.agentStateMessage) {
      return (0, import_class_transformer.plainToInstance)(AgentStateMessage, {
        id: message.id,
        threadId: message.agentStateMessage.threadId,
        createdAt: message.createdAt,
        agentName: message.agentStateMessage.agentName,
        nodeName: message.agentStateMessage.nodeName,
        runId: message.agentStateMessage.runId,
        active: message.agentStateMessage.active,
        role: message.agentStateMessage.role,
        state: JSON.parse(message.agentStateMessage.state),
        running: message.agentStateMessage.running
      });
    } else {
      return null;
    }
  });
  return messages.filter((m) => m);
}
__name(convertGqlInputToMessages, "convertGqlInputToMessages");

// src/lib/runtime/copilot-runtime.ts
var import_rxjs2 = require("rxjs");
var import_langgraph_sdk2 = require("@langchain/langgraph-sdk");

// src/lib/runtime/mcp-tools-utils.ts
function extractParametersFromSchema(toolOrSchema) {
  var _a;
  const parameters = [];
  const schema = "schema" in (toolOrSchema || {}) ? toolOrSchema.schema : toolOrSchema;
  const toolParameters = (schema == null ? void 0 : schema.parameters) || ((_a = schema == null ? void 0 : schema.parameters) == null ? void 0 : _a.jsonSchema);
  const properties = toolParameters == null ? void 0 : toolParameters.properties;
  const requiredParams = new Set((toolParameters == null ? void 0 : toolParameters.required) || []);
  if (!properties) {
    return parameters;
  }
  for (const paramName in properties) {
    if (Object.prototype.hasOwnProperty.call(properties, paramName)) {
      const paramDef = properties[paramName];
      parameters.push({
        name: paramName,
        // Infer type, default to string. MCP schemas might have more complex types.
        // This might need refinement based on common MCP schema practices.
        type: paramDef.type || "string",
        description: paramDef.description,
        required: requiredParams.has(paramName)
      });
    }
  }
  return parameters;
}
__name(extractParametersFromSchema, "extractParametersFromSchema");
function convertMCPToolsToActions(mcpTools, mcpEndpoint) {
  const actions = [];
  for (const [toolName, tool] of Object.entries(mcpTools)) {
    const parameters = extractParametersFromSchema(tool);
    const handler = /* @__PURE__ */ __name(async (params) => {
      try {
        const result = await tool.execute({
          params
        });
        return typeof result === "string" ? result : JSON.stringify(result);
      } catch (error) {
        console.error(`Error executing MCP tool '${toolName}' from endpoint ${mcpEndpoint}:`, error);
        throw new Error(`Execution failed for MCP tool '${toolName}': ${error instanceof Error ? error.message : String(error)}`);
      }
    }, "handler");
    actions.push({
      name: toolName,
      description: tool.description || `MCP tool: ${toolName} (from ${mcpEndpoint})`,
      parameters,
      handler,
      // Add metadata for easier identification/debugging
      _isMCPTool: true,
      _mcpEndpoint: mcpEndpoint
    });
  }
  return actions;
}
__name(convertMCPToolsToActions, "convertMCPToolsToActions");
function generateMcpToolInstructions(toolsMap) {
  if (!toolsMap || Object.keys(toolsMap).length === 0) {
    return "";
  }
  const toolEntries = Object.entries(toolsMap);
  const toolsDoc = toolEntries.map(([name, tool]) => {
    let paramsDoc = "    No parameters required";
    try {
      if (tool.schema && typeof tool.schema === "object") {
        const schema = tool.schema;
        if (schema.properties) {
          const requiredParams = schema.required || [];
          const paramsList = Object.entries(schema.properties).map(([paramName, propSchema]) => {
            const propDetails = propSchema;
            const requiredMark = requiredParams.includes(paramName) ? "*" : "";
            const typeInfo = propDetails.type || "any";
            const description = propDetails.description ? ` - ${propDetails.description}` : "";
            return `    - ${paramName}${requiredMark} (${typeInfo})${description}`;
          });
          if (paramsList.length > 0) {
            paramsDoc = paramsList.join("\n");
          }
        }
      }
    } catch (e) {
      console.error(`Error parsing schema for tool ${name}:`, e);
    }
    return `- ${name}: ${tool.description || ""}
${paramsDoc}`;
  }).join("\n\n");
  return `You have access to the following external tools provided by Model Context Protocol (MCP) servers:

${toolsDoc}

When using these tools:
1. Only provide valid parameters according to their type requirements
2. Required parameters are marked with *
3. Format API calls correctly with the expected parameter structure
4. Always check tool responses to determine your next action`;
}
__name(generateMcpToolInstructions, "generateMcpToolInstructions");

// src/lib/runtime/copilot-runtime.ts
var CopilotRuntime = class {
  actions;
  agents;
  remoteEndpointDefinitions;
  langserve = [];
  onBeforeRequest;
  onAfterRequest;
  delegateAgentProcessingToServiceAdapter;
  observability;
  availableAgents;
  // +++ MCP Properties +++
  mcpServersConfig;
  mcpActionCache = /* @__PURE__ */ new Map();
  // --- MCP Properties ---
  // +++ MCP Client Factory +++
  createMCPClientImpl;
  // --- MCP Client Factory ---
  constructor(params) {
    var _a, _b, _c, _d;
    if ((params == null ? void 0 : params.actions) && (params == null ? void 0 : params.remoteEndpoints) && (params == null ? void 0 : params.remoteEndpoints.some((e) => e.type === EndpointType.LangGraphPlatform))) {
      console.warn("Actions set in runtime instance will not be available for the agent");
    }
    this.actions = (params == null ? void 0 : params.actions) || [];
    this.availableAgents = [];
    for (const chain of (params == null ? void 0 : params.langserve) || []) {
      const remoteChain = new RemoteChain(chain);
      this.langserve.push(remoteChain.toAction());
    }
    this.remoteEndpointDefinitions = (params == null ? void 0 : params.remoteEndpoints) ?? (params == null ? void 0 : params.remoteActions) ?? [];
    this.onBeforeRequest = (_a = params == null ? void 0 : params.middleware) == null ? void 0 : _a.onBeforeRequest;
    this.onAfterRequest = (_b = params == null ? void 0 : params.middleware) == null ? void 0 : _b.onAfterRequest;
    this.delegateAgentProcessingToServiceAdapter = (params == null ? void 0 : params.delegateAgentProcessingToServiceAdapter) || false;
    this.observability = params == null ? void 0 : params.observability_c;
    this.agents = (params == null ? void 0 : params.agents) ?? {};
    this.mcpServersConfig = params == null ? void 0 : params.mcpServers;
    this.createMCPClientImpl = params == null ? void 0 : params.createMCPClient;
    if (this.mcpServersConfig && this.mcpServersConfig.length > 0 && !this.createMCPClientImpl) {
      throw new import_shared18.CopilotKitMisuseError({
        message: "MCP Integration Error: `mcpServers` were provided, but the `createMCPClient` function was not passed to the CopilotRuntime constructor. Please provide an implementation for `createMCPClient`."
      });
    }
    if ((params == null ? void 0 : params.actions) && (((_c = params == null ? void 0 : params.remoteEndpoints) == null ? void 0 : _c.some((e) => e.type === EndpointType.LangGraphPlatform)) || ((_d = this.mcpServersConfig) == null ? void 0 : _d.length))) {
      console.warn("Local 'actions' defined in CopilotRuntime might not be available to remote agents (LangGraph, MCP). Consider defining actions closer to the agent implementation if needed.");
    }
  }
  // +++ MCP Instruction Injection Method +++
  injectMCPToolInstructions(messages, currentActions) {
    const mcpActionsForRequest = currentActions.filter((action) => action._isMCPTool);
    if (!mcpActionsForRequest || mcpActionsForRequest.length === 0) {
      return messages;
    }
    const uniqueMcpTools = /* @__PURE__ */ new Map();
    mcpActionsForRequest.forEach((action) => {
      uniqueMcpTools.set(action.name, action);
    });
    const toolsMap = {};
    Array.from(uniqueMcpTools.values()).forEach((action) => {
      toolsMap[action.name] = {
        description: action.description || "",
        schema: action.parameters ? {
          parameters: {
            properties: action.parameters.reduce((acc, p) => ({
              ...acc,
              [p.name]: {
                type: p.type,
                description: p.description
              }
            }), {}),
            required: action.parameters.filter((p) => p.required).map((p) => p.name)
          }
        } : {},
        execute: async () => ({})
      };
    });
    const mcpToolInstructions = generateMcpToolInstructions(toolsMap);
    if (!mcpToolInstructions) {
      return messages;
    }
    const instructions = "You have access to the following tools provided by external Model Context Protocol (MCP) servers:\n" + mcpToolInstructions + "\nUse them when appropriate to fulfill the user's request.";
    const systemMessageIndex = messages.findIndex((msg) => {
      var _a;
      return ((_a = msg.textMessage) == null ? void 0 : _a.role) === "system";
    });
    const newMessages = [
      ...messages
    ];
    if (systemMessageIndex !== -1) {
      const existingMsg = newMessages[systemMessageIndex];
      if (existingMsg.textMessage) {
        existingMsg.textMessage.content = (existingMsg.textMessage.content ? existingMsg.textMessage.content + "\n\n" : "") + instructions;
      }
    } else {
      newMessages.unshift({
        id: (0, import_shared18.randomId)(),
        createdAt: /* @__PURE__ */ new Date(),
        textMessage: {
          role: MessageRole.system,
          content: instructions
        },
        actionExecutionMessage: void 0,
        resultMessage: void 0,
        agentStateMessage: void 0
      });
    }
    return newMessages;
  }
  // --- MCP Instruction Injection Method ---
  async processRuntimeRequest(request) {
    var _a, _b, _c, _d, _e;
    const { serviceAdapter, messages: rawMessages, actions: clientSideActionsInput, threadId, runId, outputMessagesPromise, graphqlContext, forwardedParameters, url, extensions, agentSession, agentStates, publicApiKey } = request;
    const eventSource = new RuntimeEventSource();
    const requestStartTime = Date.now();
    const streamedChunks = [];
    try {
      if (agentSession && !this.delegateAgentProcessingToServiceAdapter) {
        return await this.processAgentRequest(request);
      }
      if (serviceAdapter instanceof EmptyAdapter) {
        throw new import_shared18.CopilotKitMisuseError({
          message: `Invalid adapter configuration: EmptyAdapter is only meant to be used with agent lock mode. 
For non-agent components like useCopilotChatSuggestions, CopilotTextarea, or CopilotTask, 
please use an LLM adapter instead.`
        });
      }
      const serverSideActions = await this.getServerSideActions(request);
      const filteredRawMessages = rawMessages.filter((message) => !message.agentStateMessage);
      const messagesWithInjectedInstructions = this.injectMCPToolInstructions(filteredRawMessages, serverSideActions);
      const inputMessages = convertGqlInputToMessages(messagesWithInjectedInstructions);
      if (((_a = this.observability) == null ? void 0 : _a.enabled) && publicApiKey) {
        try {
          const requestData = {
            threadId,
            runId,
            model: forwardedParameters == null ? void 0 : forwardedParameters.model,
            messages: inputMessages,
            actions: clientSideActionsInput,
            forwardedParameters,
            timestamp: requestStartTime,
            provider: this.detectProvider(serviceAdapter)
          };
          await this.observability.hooks.handleRequest(requestData);
        } catch (error) {
          console.error("Error logging LLM request:", error);
        }
      }
      const serverSideActionsInput = serverSideActions.map((action) => ({
        name: action.name,
        description: action.description,
        jsonSchema: JSON.stringify((0, import_shared18.actionParametersToJsonSchema)(action.parameters))
      }));
      const actionInputs = flattenToolCallsNoDuplicates([
        ...serverSideActionsInput,
        ...clientSideActionsInput.filter(
          // Filter remote actions from CopilotKit core loop
          (action) => action.available !== ActionInputAvailability.remote
        )
      ]);
      await ((_b = this.onBeforeRequest) == null ? void 0 : _b.call(this, {
        threadId,
        runId,
        inputMessages,
        properties: graphqlContext.properties,
        url
      }));
      const result = await serviceAdapter.process({
        messages: inputMessages,
        actions: actionInputs,
        threadId,
        runId,
        eventSource,
        forwardedParameters,
        extensions,
        agentSession,
        agentStates
      });
      const nonEmptyThreadId = threadId ?? result.threadId;
      outputMessagesPromise.then((outputMessages) => {
        var _a2;
        (_a2 = this.onAfterRequest) == null ? void 0 : _a2.call(this, {
          threadId: nonEmptyThreadId,
          runId: result.runId,
          inputMessages,
          outputMessages,
          properties: graphqlContext.properties,
          url
        });
      }).catch((_error) => {
      });
      if (((_c = this.observability) == null ? void 0 : _c.enabled) && publicApiKey) {
        try {
          outputMessagesPromise.then((outputMessages) => {
            const responseData = {
              threadId: result.threadId,
              runId: result.runId,
              model: forwardedParameters == null ? void 0 : forwardedParameters.model,
              // Use collected chunks for progressive mode or outputMessages for regular mode
              output: this.observability.progressive ? streamedChunks : outputMessages,
              latency: Date.now() - requestStartTime,
              timestamp: Date.now(),
              provider: this.detectProvider(serviceAdapter),
              // Indicate this is the final response
              isFinalResponse: true
            };
            try {
              this.observability.hooks.handleResponse(responseData);
            } catch (logError) {
              console.error("Error logging LLM response:", logError);
            }
          }).catch((error) => {
            console.error("Failed to get output messages for logging:", error);
          });
        } catch (error) {
          console.error("Error setting up logging for LLM response:", error);
        }
      }
      if (((_d = this.observability) == null ? void 0 : _d.enabled) && this.observability.progressive && publicApiKey) {
        const originalStream = eventSource.stream.bind(eventSource);
        eventSource.stream = async (callback) => {
          await originalStream(async (eventStream$) => {
            eventStream$.subscribe({
              next: (event) => {
                if (event.type === RuntimeEventTypes.TextMessageContent) {
                  streamedChunks.push(event.content);
                  try {
                    const progressiveData = {
                      threadId: threadId || "",
                      runId,
                      model: forwardedParameters == null ? void 0 : forwardedParameters.model,
                      output: event.content,
                      latency: Date.now() - requestStartTime,
                      timestamp: Date.now(),
                      provider: this.detectProvider(serviceAdapter),
                      isProgressiveChunk: true
                    };
                    Promise.resolve().then(() => {
                      this.observability.hooks.handleResponse(progressiveData);
                    }).catch((error) => {
                      console.error("Error in progressive logging:", error);
                    });
                  } catch (error) {
                    console.error("Error preparing progressive log data:", error);
                  }
                }
              }
            });
            await callback(eventStream$);
          });
        };
      }
      return {
        threadId: nonEmptyThreadId,
        runId: result.runId,
        eventSource,
        serverSideActions,
        actionInputsWithoutAgents: actionInputs.filter((action) => (
          // TODO-AGENTS: do not exclude ALL server side actions
          !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name)
        )),
        extensions: result.extensions
      };
    } catch (error) {
      if (((_e = this.observability) == null ? void 0 : _e.enabled) && publicApiKey) {
        try {
          const errorData = {
            threadId,
            runId,
            model: forwardedParameters == null ? void 0 : forwardedParameters.model,
            error: error instanceof Error ? error : String(error),
            timestamp: Date.now(),
            latency: Date.now() - requestStartTime,
            provider: this.detectProvider(serviceAdapter)
          };
          await this.observability.hooks.handleError(errorData);
        } catch (logError) {
          console.error("Error logging LLM error:", logError);
        }
      }
      if (error instanceof import_shared18.CopilotKitError) {
        throw error;
      }
      console.error("Error getting response:", error);
      eventSource.sendErrorMessageToChat();
      throw error;
    }
  }
  async discoverAgentsFromEndpoints(graphqlContext) {
    const agents = this.remoteEndpointDefinitions.reduce(async (acc, endpoint) => {
      const agents2 = await acc;
      if (endpoint.type === EndpointType.LangGraphPlatform) {
        const propertyHeaders = graphqlContext.properties.authorization ? {
          authorization: `Bearer ${graphqlContext.properties.authorization}`
        } : null;
        const client = new import_langgraph_sdk2.Client({
          apiUrl: endpoint.deploymentUrl,
          apiKey: endpoint.langsmithApiKey,
          defaultHeaders: {
            ...propertyHeaders
          }
        });
        let data = [];
        try {
          data = await client.assistants.search();
          if (data && "detail" in data && data.detail.toLowerCase() === "not found") {
            throw new import_shared18.CopilotKitAgentDiscoveryError({
              availableAgents: this.availableAgents
            });
          }
        } catch (e) {
          throw new import_shared18.CopilotKitMisuseError({
            message: `
              Failed to find or contact remote endpoint at url ${endpoint.deploymentUrl}.
              Make sure the API is running and that it's indeed a LangGraph platform url.
              
              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`
          });
        }
        const endpointAgents = data.map((entry) => ({
          name: entry.graph_id,
          id: entry.assistant_id,
          description: "",
          endpoint
        }));
        return [
          ...agents2,
          ...endpointAgents
        ];
      }
      const cpkEndpoint = endpoint;
      const fetchUrl = `${endpoint.url}/info`;
      try {
        const response = await fetch(fetchUrl, {
          method: "POST",
          headers: createHeaders(cpkEndpoint.onBeforeRequest, graphqlContext),
          body: JSON.stringify({
            properties: graphqlContext.properties
          })
        });
        if (!response.ok) {
          if (response.status === 404) {
            throw new import_shared18.CopilotKitApiDiscoveryError({
              url: fetchUrl
            });
          }
          throw new import_shared18.ResolvedCopilotKitError({
            status: response.status,
            url: fetchUrl,
            isRemoteEndpoint: true
          });
        }
        const data = await response.json();
        const endpointAgents = ((data == null ? void 0 : data.agents) ?? []).map((agent) => ({
          name: agent.name,
          description: agent.description ?? "" ?? "",
          id: (0, import_shared18.randomId)(),
          endpoint
        }));
        return [
          ...agents2,
          ...endpointAgents
        ];
      } catch (error) {
        if (error instanceof import_shared18.CopilotKitError) {
          throw error;
        }
        throw new import_shared18.CopilotKitLowLevelError({
          error,
          url: fetchUrl
        });
      }
    }, Promise.resolve([]));
    this.availableAgents = (await agents ?? []).map((a) => ({
      name: a.name,
      id: a.id
    }));
    return agents;
  }
  async loadAgentState(graphqlContext, threadId, agentName) {
    const agentsWithEndpoints = await this.discoverAgentsFromEndpoints(graphqlContext);
    const agentWithEndpoint = agentsWithEndpoints.find((agent) => agent.name === agentName);
    if (!agentWithEndpoint) {
      throw new Error("Agent not found");
    }
    if (agentWithEndpoint.endpoint.type === EndpointType.LangGraphPlatform) {
      const propertyHeaders = graphqlContext.properties.authorization ? {
        authorization: `Bearer ${graphqlContext.properties.authorization}`
      } : null;
      const client = new import_langgraph_sdk2.Client({
        apiUrl: agentWithEndpoint.endpoint.deploymentUrl,
        apiKey: agentWithEndpoint.endpoint.langsmithApiKey,
        defaultHeaders: {
          ...propertyHeaders
        }
      });
      let state = {};
      try {
        state = (await client.threads.getState(threadId)).values;
      } catch (error) {
      }
      if (Object.keys(state).length === 0) {
        return {
          threadId: threadId || "",
          threadExists: false,
          state: JSON.stringify({}),
          messages: JSON.stringify([])
        };
      } else {
        const { messages, ...stateWithoutMessages } = state;
        const copilotkitMessages = langchainMessagesToCopilotKit(messages);
        return {
          threadId: threadId || "",
          threadExists: true,
          state: JSON.stringify(stateWithoutMessages),
          messages: JSON.stringify(copilotkitMessages)
        };
      }
    } else if (agentWithEndpoint.endpoint.type === EndpointType.CopilotKit || !("type" in agentWithEndpoint.endpoint)) {
      const cpkEndpoint = agentWithEndpoint.endpoint;
      const fetchUrl = `${cpkEndpoint.url}/agents/state`;
      try {
        const response = await fetch(fetchUrl, {
          method: "POST",
          headers: createHeaders(cpkEndpoint.onBeforeRequest, graphqlContext),
          body: JSON.stringify({
            properties: graphqlContext.properties,
            threadId,
            name: agentName
          })
        });
        if (!response.ok) {
          if (response.status === 404) {
            throw new import_shared18.CopilotKitApiDiscoveryError({
              url: fetchUrl
            });
          }
          throw new import_shared18.ResolvedCopilotKitError({
            status: response.status,
            url: fetchUrl,
            isRemoteEndpoint: true
          });
        }
        const data = await response.json();
        return {
          ...data,
          state: JSON.stringify(data.state),
          messages: JSON.stringify(data.messages)
        };
      } catch (error) {
        if (error instanceof import_shared18.CopilotKitError) {
          throw error;
        }
        throw new import_shared18.CopilotKitLowLevelError({
          error,
          url: fetchUrl
        });
      }
    } else {
      throw new Error(`Unknown endpoint type: ${agentWithEndpoint.endpoint.type}`);
    }
  }
  async processAgentRequest(request) {
    var _a, _b, _c, _d, _e;
    const { messages: rawMessages, outputMessagesPromise, graphqlContext, agentSession, threadId: threadIdFromRequest, metaEvents, publicApiKey, forwardedParameters } = request;
    const { agentName, nodeName } = agentSession;
    const requestStartTime = Date.now();
    const streamedChunks = [];
    const threadId = threadIdFromRequest ?? agentSession.threadId;
    const serverSideActions = await this.getServerSideActions(request);
    const messages = convertGqlInputToMessages(rawMessages);
    const currentAgent = serverSideActions.find((action) => action.name === agentName && isRemoteAgentAction(action));
    if (!currentAgent) {
      throw new import_shared18.CopilotKitAgentDiscoveryError({
        agentName,
        availableAgents: this.availableAgents
      });
    }
    const availableActionsForCurrentAgent = serverSideActions.filter((action) => (
      // Case 1: Keep all regular (non-agent) actions
      !isRemoteAgentAction(action) || // Case 2: For agent actions, keep all except self (prevent infinite loops)
      isRemoteAgentAction(action) && action.name !== agentName
    )).map((action) => ({
      name: action.name,
      description: action.description,
      jsonSchema: JSON.stringify((0, import_shared18.actionParametersToJsonSchema)(action.parameters))
    }));
    const allAvailableActions = flattenToolCallsNoDuplicates([
      ...availableActionsForCurrentAgent,
      ...request.actions
    ]);
    if (((_a = this.observability) == null ? void 0 : _a.enabled) && publicApiKey) {
      try {
        const requestData = {
          threadId,
          runId: void 0,
          model: forwardedParameters == null ? void 0 : forwardedParameters.model,
          messages,
          actions: allAvailableActions,
          forwardedParameters,
          timestamp: requestStartTime,
          provider: "agent",
          agentName,
          nodeName
        };
        await this.observability.hooks.handleRequest(requestData);
      } catch (error) {
        console.error("Error logging agent request:", error);
      }
    }
    await ((_b = this.onBeforeRequest) == null ? void 0 : _b.call(this, {
      threadId,
      runId: void 0,
      inputMessages: messages,
      properties: graphqlContext.properties
    }));
    try {
      const eventSource = new RuntimeEventSource();
      const stream = await currentAgent.remoteAgentHandler({
        name: agentName,
        threadId,
        nodeName,
        metaEvents,
        actionInputsWithoutAgents: allAvailableActions
      });
      if (((_c = this.observability) == null ? void 0 : _c.enabled) && this.observability.progressive && publicApiKey) {
        const originalStream = eventSource.stream.bind(eventSource);
        eventSource.stream = async (callback) => {
          await originalStream(async (eventStream$) => {
            eventStream$.subscribe({
              next: (event) => {
                if (event.type === RuntimeEventTypes.TextMessageContent) {
                  streamedChunks.push(event.content);
                  try {
                    const progressiveData = {
                      threadId: threadId || "",
                      runId: void 0,
                      model: forwardedParameters == null ? void 0 : forwardedParameters.model,
                      output: event.content,
                      latency: Date.now() - requestStartTime,
                      timestamp: Date.now(),
                      provider: "agent",
                      isProgressiveChunk: true,
                      agentName,
                      nodeName
                    };
                    Promise.resolve().then(() => {
                      this.observability.hooks.handleResponse(progressiveData);
                    }).catch((error) => {
                      console.error("Error in progressive agent logging:", error);
                    });
                  } catch (error) {
                    console.error("Error preparing progressive agent log data:", error);
                  }
                }
              }
            });
            await callback(eventStream$);
          });
        };
      }
      eventSource.stream(async (eventStream$) => {
        (0, import_rxjs2.from)(stream).subscribe({
          next: (event) => eventStream$.next(event),
          error: (err) => {
            var _a2;
            console.error("Error in stream", err);
            if (((_a2 = this.observability) == null ? void 0 : _a2.enabled) && publicApiKey) {
              try {
                const errorData = {
                  threadId,
                  runId: void 0,
                  model: forwardedParameters == null ? void 0 : forwardedParameters.model,
                  error: err instanceof Error ? err : String(err),
                  timestamp: Date.now(),
                  latency: Date.now() - requestStartTime,
                  provider: "agent",
                  agentName,
                  nodeName
                };
                this.observability.hooks.handleError(errorData);
              } catch (logError) {
                console.error("Error logging agent error:", logError);
              }
            }
            eventStream$.error(err);
            eventStream$.complete();
          },
          complete: () => eventStream$.complete()
        });
      });
      if (((_d = this.observability) == null ? void 0 : _d.enabled) && publicApiKey) {
        outputMessagesPromise.then((outputMessages) => {
          const responseData = {
            threadId,
            runId: void 0,
            model: forwardedParameters == null ? void 0 : forwardedParameters.model,
            // Use collected chunks for progressive mode or outputMessages for regular mode
            output: this.observability.progressive ? streamedChunks : outputMessages,
            latency: Date.now() - requestStartTime,
            timestamp: Date.now(),
            provider: "agent",
            isFinalResponse: true,
            agentName,
            nodeName
          };
          try {
            this.observability.hooks.handleResponse(responseData);
          } catch (logError) {
            console.error("Error logging agent response:", logError);
          }
        }).catch((error) => {
          console.error("Failed to get output messages for agent logging:", error);
        });
      }
      outputMessagesPromise.then((outputMessages) => {
        var _a2;
        (_a2 = this.onAfterRequest) == null ? void 0 : _a2.call(this, {
          threadId,
          runId: void 0,
          inputMessages: messages,
          outputMessages,
          properties: graphqlContext.properties
        });
      }).catch((_error) => {
      });
      return {
        threadId,
        runId: void 0,
        eventSource,
        serverSideActions,
        actionInputsWithoutAgents: allAvailableActions
      };
    } catch (error) {
      if (((_e = this.observability) == null ? void 0 : _e.enabled) && publicApiKey) {
        try {
          const errorData = {
            threadId,
            runId: void 0,
            model: forwardedParameters == null ? void 0 : forwardedParameters.model,
            error: error instanceof Error ? error : String(error),
            timestamp: Date.now(),
            latency: Date.now() - requestStartTime,
            provider: "agent",
            agentName,
            nodeName
          };
          await this.observability.hooks.handleError(errorData);
        } catch (logError) {
          console.error("Error logging agent error:", logError);
        }
      }
      console.error("Error getting response:", error);
      throw error;
    }
  }
  async getServerSideActions(request) {
    var _a, _b;
    const { graphqlContext, messages: rawMessages, agentStates, url } = request;
    const inputMessages = convertGqlInputToMessages(rawMessages);
    const langserveFunctions = [];
    for (const chainPromise of this.langserve) {
      try {
        const chain = await chainPromise;
        langserveFunctions.push(chain);
      } catch (error) {
        console.error("Error loading langserve chain:", error);
      }
    }
    const remoteEndpointDefinitions = this.remoteEndpointDefinitions.map((endpoint) => ({
      ...endpoint,
      type: resolveEndpointType(endpoint)
    }));
    const remoteActions = await setupRemoteActions({
      remoteEndpointDefinitions,
      graphqlContext,
      messages: inputMessages,
      agentStates,
      frontendUrl: url,
      agents: this.agents
    });
    const configuredActions = typeof this.actions === "function" ? this.actions({
      properties: graphqlContext.properties,
      url
    }) : this.actions;
    const requestSpecificMCPActions = [];
    if (this.createMCPClientImpl) {
      const baseEndpoints = this.mcpServersConfig || [];
      const requestEndpoints = ((_a = graphqlContext.properties) == null ? void 0 : _a.mcpServers) || ((_b = graphqlContext.properties) == null ? void 0 : _b.mcpEndpoints) || [];
      const effectiveEndpointsMap = /* @__PURE__ */ new Map();
      [
        ...baseEndpoints
      ].forEach((ep) => {
        if (ep && ep.endpoint) {
          effectiveEndpointsMap.set(ep.endpoint, ep);
        }
      });
      [
        ...requestEndpoints
      ].forEach((ep) => {
        if (ep && ep.endpoint) {
          effectiveEndpointsMap.set(ep.endpoint, ep);
        }
      });
      const effectiveEndpoints = Array.from(effectiveEndpointsMap.values());
      for (const config2 of effectiveEndpoints) {
        const endpointUrl = config2.endpoint;
        let actionsForEndpoint = this.mcpActionCache.get(endpointUrl);
        if (!actionsForEndpoint) {
          let client = null;
          try {
            client = await this.createMCPClientImpl(config2);
            const tools = await client.tools();
            actionsForEndpoint = convertMCPToolsToActions(tools, endpointUrl);
            this.mcpActionCache.set(endpointUrl, actionsForEndpoint);
          } catch (error) {
            console.error(`MCP: Failed to fetch tools from endpoint ${endpointUrl}. Skipping. Error:`, error);
            actionsForEndpoint = [];
            this.mcpActionCache.set(endpointUrl, actionsForEndpoint);
          }
        }
        requestSpecificMCPActions.push(...actionsForEndpoint || []);
      }
    }
    return [
      ...configuredActions,
      ...langserveFunctions,
      ...remoteActions,
      ...requestSpecificMCPActions
    ];
  }
  // Add helper method to detect provider
  detectProvider(serviceAdapter) {
    const adapterName = serviceAdapter.constructor.name;
    if (adapterName.includes("OpenAI"))
      return "openai";
    if (adapterName.includes("Anthropic"))
      return "anthropic";
    if (adapterName.includes("Google"))
      return "google";
    if (adapterName.includes("Groq"))
      return "groq";
    if (adapterName.includes("LangChain"))
      return "langchain";
    return void 0;
  }
};
__name(CopilotRuntime, "CopilotRuntime");
function flattenToolCallsNoDuplicates(toolsByPriority) {
  let allTools = [];
  const allToolNames = [];
  for (const tool of toolsByPriority) {
    if (!allToolNames.includes(tool.name)) {
      allTools.push(tool);
      allToolNames.push(tool.name);
    }
  }
  return allTools;
}
__name(flattenToolCallsNoDuplicates, "flattenToolCallsNoDuplicates");
function copilotKitEndpoint(config2) {
  return {
    ...config2,
    type: EndpointType.CopilotKit
  };
}
__name(copilotKitEndpoint, "copilotKitEndpoint");
function langGraphPlatformEndpoint(config2) {
  return {
    ...config2,
    type: EndpointType.LangGraphPlatform
  };
}
__name(langGraphPlatformEndpoint, "langGraphPlatformEndpoint");
function resolveEndpointType(endpoint) {
  if (!endpoint.type) {
    if ("deploymentUrl" in endpoint && "agents" in endpoint) {
      return EndpointType.LangGraphPlatform;
    } else {
      return EndpointType.CopilotKit;
    }
  }
  return endpoint.type;
}
__name(resolveEndpointType, "resolveEndpointType");

// src/lib/telemetry-client.ts
var packageJson = require_package();
var telemetryClient = new import_shared19.TelemetryClient({
  packageName: packageJson.name,
  packageVersion: packageJson.version
});
function getRuntimeInstanceTelemetryInfo(runtime) {
  const endpointsInfo = runtime.remoteEndpointDefinitions.reduce((acc, endpoint) => {
    let info = {
      ...acc
    };
    const endpointType = resolveEndpointType(endpoint);
    if (!info.endpointTypes.includes(endpointType)) {
      info = {
        ...info,
        endpointTypes: [
          ...info.endpointTypes,
          endpointType
        ]
      };
    }
    if (endpointType === EndpointType.LangGraphPlatform) {
      const ep = endpoint;
      info = {
        ...info,
        agentsAmount: ep.agents.length,
        hashedKey: ep.langsmithApiKey ? (0, import_node_crypto3.createHash)("sha256").update(ep.langsmithApiKey).digest("hex") : null
      };
    }
    return info;
  }, {
    endpointTypes: [],
    agentsAmount: null,
    hashedKey: null
  });
  return {
    actionsAmount: runtime.actions.length,
    endpointsAmount: runtime.remoteEndpointDefinitions.length,
    endpointTypes: endpointsInfo.endpointTypes,
    agentsAmount: endpointsInfo.agentsAmount,
    hashedLgcKey: endpointsInfo.hashedKey
  };
}
__name(getRuntimeInstanceTelemetryInfo, "getRuntimeInstanceTelemetryInfo");
var telemetry_client_default = telemetryClient;

// src/service-adapters/events.ts
var import_class_transformer2 = require("class-transformer");
var RuntimeEventTypes;
(function(RuntimeEventTypes2) {
  RuntimeEventTypes2["TextMessageStart"] = "TextMessageStart";
  RuntimeEventTypes2["TextMessageContent"] = "TextMessageContent";
  RuntimeEventTypes2["TextMessageEnd"] = "TextMessageEnd";
  RuntimeEventTypes2["ActionExecutionStart"] = "ActionExecutionStart";
  RuntimeEventTypes2["ActionExecutionArgs"] = "ActionExecutionArgs";
  RuntimeEventTypes2["ActionExecutionEnd"] = "ActionExecutionEnd";
  RuntimeEventTypes2["ActionExecutionResult"] = "ActionExecutionResult";
  RuntimeEventTypes2["AgentStateMessage"] = "AgentStateMessage";
  RuntimeEventTypes2["MetaEvent"] = "MetaEvent";
})(RuntimeEventTypes || (RuntimeEventTypes = {}));
var RuntimeMetaEventName;
(function(RuntimeMetaEventName2) {
  RuntimeMetaEventName2["LangGraphInterruptEvent"] = "LangGraphInterruptEvent";
  RuntimeMetaEventName2["LangGraphInterruptResumeEvent"] = "LangGraphInterruptResumeEvent";
  RuntimeMetaEventName2["CopilotKitLangGraphInterruptEvent"] = "CopilotKitLangGraphInterruptEvent";
})(RuntimeMetaEventName || (RuntimeMetaEventName = {}));
var RuntimeEventSubject = class extends import_rxjs3.ReplaySubject {
  constructor() {
    super();
  }
  sendTextMessageStart({ messageId, parentMessageId }) {
    this.next({
      type: "TextMessageStart",
      messageId,
      parentMessageId
    });
  }
  sendTextMessageContent({ messageId, content }) {
    this.next({
      type: "TextMessageContent",
      content,
      messageId
    });
  }
  sendTextMessageEnd({ messageId }) {
    this.next({
      type: "TextMessageEnd",
      messageId
    });
  }
  sendTextMessage(messageId, content) {
    this.sendTextMessageStart({
      messageId
    });
    this.sendTextMessageContent({
      messageId,
      content
    });
    this.sendTextMessageEnd({
      messageId
    });
  }
  sendActionExecutionStart({ actionExecutionId, actionName, parentMessageId }) {
    this.next({
      type: "ActionExecutionStart",
      actionExecutionId,
      actionName,
      parentMessageId
    });
  }
  sendActionExecutionArgs({ actionExecutionId, args }) {
    this.next({
      type: "ActionExecutionArgs",
      args,
      actionExecutionId
    });
  }
  sendActionExecutionEnd({ actionExecutionId }) {
    this.next({
      type: "ActionExecutionEnd",
      actionExecutionId
    });
  }
  sendActionExecution({ actionExecutionId, actionName, args, parentMessageId }) {
    this.sendActionExecutionStart({
      actionExecutionId,
      actionName,
      parentMessageId
    });
    this.sendActionExecutionArgs({
      actionExecutionId,
      args
    });
    this.sendActionExecutionEnd({
      actionExecutionId
    });
  }
  sendActionExecutionResult({ actionExecutionId, actionName, result, error }) {
    this.next({
      type: "ActionExecutionResult",
      actionName,
      actionExecutionId,
      result: ResultMessage.encodeResult(result, error)
    });
  }
  sendAgentStateMessage({ threadId, agentName, nodeName, runId, active, role, state, running }) {
    this.next({
      type: "AgentStateMessage",
      threadId,
      agentName,
      nodeName,
      runId,
      active,
      role,
      state,
      running
    });
  }
};
__name(RuntimeEventSubject, "RuntimeEventSubject");
var RuntimeEventSource = class {
  eventStream$ = new RuntimeEventSubject();
  callback;
  async stream(callback) {
    this.callback = callback;
  }
  sendErrorMessageToChat(message = "An error occurred. Please try again.") {
    const errorMessage = `\u274C ${message}`;
    if (!this.callback) {
      this.stream(async (eventStream$) => {
        eventStream$.sendTextMessage((0, import_shared20.randomId)(), errorMessage);
      });
    } else {
      this.eventStream$.sendTextMessage((0, import_shared20.randomId)(), errorMessage);
    }
  }
  processRuntimeEvents({ serverSideActions, guardrailsResult$, actionInputsWithoutAgents, threadId }) {
    this.callback(this.eventStream$).catch((error) => {
      console.error("Error in event source callback", error);
      this.sendErrorMessageToChat();
      this.eventStream$.complete();
    });
    return this.eventStream$.pipe(
      // track state
      (0, import_rxjs3.scan)((acc, event) => {
        acc = {
          ...acc
        };
        if (event.type === "ActionExecutionStart") {
          acc.callActionServerSide = serverSideActions.find((action) => action.name === event.actionName) !== void 0;
          acc.args = "";
          acc.actionExecutionId = event.actionExecutionId;
          if (acc.callActionServerSide) {
            acc.action = serverSideActions.find((action) => action.name === event.actionName);
          }
          acc.actionExecutionParentMessageId = event.parentMessageId;
        } else if (event.type === "ActionExecutionArgs") {
          acc.args += event.args;
        }
        acc.event = event;
        return acc;
      }, {
        event: null,
        callActionServerSide: false,
        args: "",
        actionExecutionId: null,
        action: null,
        actionExecutionParentMessageId: null
      }),
      (0, import_rxjs3.concatMap)((eventWithState) => {
        if (eventWithState.event.type === "ActionExecutionEnd" && eventWithState.callActionServerSide) {
          const toolCallEventStream$ = new RuntimeEventSubject();
          executeAction(toolCallEventStream$, guardrailsResult$ ? guardrailsResult$ : null, eventWithState.action, eventWithState.args, eventWithState.actionExecutionParentMessageId, eventWithState.actionExecutionId, actionInputsWithoutAgents, threadId).catch((error) => {
            console.error(error);
          });
          telemetry_client_default.capture("oss.runtime.server_action_executed", {});
          return (0, import_rxjs3.concat)((0, import_rxjs3.of)(eventWithState.event), toolCallEventStream$).pipe((0, import_rxjs3.catchError)((error) => {
            console.error("Error in tool call stream", error);
            this.sendErrorMessageToChat();
            return import_rxjs3.EMPTY;
          }));
        } else {
          return (0, import_rxjs3.of)(eventWithState.event);
        }
      })
    );
  }
};
__name(RuntimeEventSource, "RuntimeEventSource");
async function executeAction(eventStream$, guardrailsResult$, action, actionArguments, actionExecutionParentMessageId, actionExecutionId, actionInputsWithoutAgents, threadId) {
  var _a;
  if (guardrailsResult$) {
    const { status } = await (0, import_rxjs3.firstValueFrom)(guardrailsResult$);
    if (status === "denied") {
      eventStream$.complete();
      return;
    }
  }
  let args = [];
  if (actionArguments) {
    try {
      args = JSON.parse(actionArguments);
    } catch (e) {
      console.error("Action argument unparsable", {
        actionArguments
      });
      eventStream$.sendActionExecutionResult({
        actionExecutionId,
        actionName: action.name,
        error: {
          code: "INVALID_ARGUMENTS",
          message: "Failed to parse action arguments"
        }
      });
      return;
    }
  }
  if (isRemoteAgentAction(action)) {
    const result = `${action.name} agent started`;
    const agentExecution = (0, import_class_transformer2.plainToInstance)(ActionExecutionMessage, {
      id: actionExecutionId,
      createdAt: /* @__PURE__ */ new Date(),
      name: action.name,
      arguments: JSON.parse(actionArguments),
      parentMessageId: actionExecutionParentMessageId ?? actionExecutionId
    });
    const agentExecutionResult = (0, import_class_transformer2.plainToInstance)(ResultMessage, {
      id: "result-" + actionExecutionId,
      createdAt: /* @__PURE__ */ new Date(),
      actionExecutionId,
      actionName: action.name,
      result
    });
    eventStream$.sendActionExecutionResult({
      actionExecutionId,
      actionName: action.name,
      result
    });
    const stream = await action.remoteAgentHandler({
      name: action.name,
      threadId,
      actionInputsWithoutAgents,
      additionalMessages: [
        agentExecution,
        agentExecutionResult
      ]
    });
    (0, import_rxjs3.from)(stream).subscribe({
      next: (event) => eventStream$.next(event),
      error: (err) => {
        console.error("Error in stream", err);
        eventStream$.sendActionExecutionResult({
          actionExecutionId,
          actionName: action.name,
          error: {
            code: "STREAM_ERROR",
            message: err.message
          }
        });
        eventStream$.complete();
      },
      complete: () => eventStream$.complete()
    });
  } else {
    try {
      const result = await ((_a = action.handler) == null ? void 0 : _a.call(action, args));
      await streamLangChainResponse({
        result,
        eventStream$,
        actionExecution: {
          name: action.name,
          id: actionExecutionId
        }
      });
    } catch (e) {
      console.error("Error in action handler", e);
      eventStream$.sendActionExecutionResult({
        actionExecutionId,
        actionName: action.name,
        error: {
          code: "HANDLER_ERROR",
          message: e.message
        }
      });
      eventStream$.complete();
    }
  }
}
__name(executeAction, "executeAction");

// src/graphql/resolvers/copilot.resolver.ts
var import_graphql_scalars2 = require("graphql-scalars");
var import_class_transformer3 = require("class-transformer");
var import_graphql = require("graphql");

// src/utils/failed-response-status-reasons.ts
var GuardrailsValidationFailureResponse = class extends FailedResponseStatus {
  reason = FailedResponseStatusReason.GUARDRAILS_VALIDATION_FAILED;
  constructor({ guardrailsReason }) {
    super();
    this.details = {
      guardrailsReason
    };
  }
};
__name(GuardrailsValidationFailureResponse, "GuardrailsValidationFailureResponse");
var MessageStreamInterruptedResponse = class extends FailedResponseStatus {
  reason = FailedResponseStatusReason.MESSAGE_STREAM_INTERRUPTED;
  constructor({ messageId }) {
    super();
    this.details = {
      messageId,
      description: "Check the message for mode details"
    };
  }
};
__name(MessageStreamInterruptedResponse, "MessageStreamInterruptedResponse");
var UnknownErrorResponse = class extends FailedResponseStatus {
  reason = FailedResponseStatusReason.UNKNOWN_ERROR;
  constructor({ description }) {
    super();
    this.details = {
      description
    };
  }
};
__name(UnknownErrorResponse, "UnknownErrorResponse");

// src/graphql/resolvers/copilot.resolver.ts
var import_shared21 = require("@copilotkit/shared");

// src/graphql/types/agents-response.type.ts
var import_type_graphql19 = require("type-graphql");
function _ts_decorate18(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate18, "_ts_decorate");
function _ts_metadata18(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata18, "_ts_metadata");
var Agent = class {
  id;
  name;
  description;
};
__name(Agent, "Agent");
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => String),
  _ts_metadata18("design:type", String)
], Agent.prototype, "id", void 0);
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => String),
  _ts_metadata18("design:type", String)
], Agent.prototype, "name", void 0);
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => String),
  _ts_metadata18("design:type", String)
], Agent.prototype, "description", void 0);
Agent = _ts_decorate18([
  (0, import_type_graphql19.ObjectType)()
], Agent);
var AgentsResponse = class {
  agents;
};
__name(AgentsResponse, "AgentsResponse");
_ts_decorate18([
  (0, import_type_graphql19.Field)(() => [
    Agent
  ]),
  _ts_metadata18("design:type", Array)
], AgentsResponse.prototype, "agents", void 0);
AgentsResponse = _ts_decorate18([
  (0, import_type_graphql19.ObjectType)()
], AgentsResponse);

// src/graphql/resolvers/copilot.resolver.ts
function _ts_decorate19(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate19, "_ts_decorate");
function _ts_metadata19(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata19, "_ts_metadata");
function _ts_param(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
}
__name(_ts_param, "_ts_param");
var invokeGuardrails = /* @__PURE__ */ __name(async ({ baseUrl, copilotCloudPublicApiKey, data, onResult, onError }) => {
  var _a;
  if (data.messages.length && ((_a = data.messages[data.messages.length - 1].textMessage) == null ? void 0 : _a.role) === MessageRole.user) {
    const messages = data.messages.filter((m) => m.textMessage !== void 0 && (m.textMessage.role === MessageRole.user || m.textMessage.role === MessageRole.assistant)).map((m) => ({
      role: m.textMessage.role,
      content: m.textMessage.content
    }));
    const lastMessage = messages[messages.length - 1];
    const restOfMessages = messages.slice(0, -1);
    const body = {
      input: lastMessage.content,
      validTopics: data.cloud.guardrails.inputValidationRules.allowList,
      invalidTopics: data.cloud.guardrails.inputValidationRules.denyList,
      messages: restOfMessages
    };
    const guardrailsResult = await fetch(`${baseUrl}/guardrails/validate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CopilotCloud-Public-API-Key": copilotCloudPublicApiKey
      },
      body: JSON.stringify(body)
    });
    if (guardrailsResult.ok) {
      const resultJson = await guardrailsResult.json();
      onResult(resultJson);
    } else {
      onError(await guardrailsResult.json());
    }
  }
}, "invokeGuardrails");
var CopilotResolver = class {
  async hello() {
    return "Hello World";
  }
  async availableAgents(ctx) {
    let logger2 = ctx.logger.child({
      component: "CopilotResolver.availableAgents"
    });
    logger2.debug("Processing");
    const agentsWithEndpoints = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);
    logger2.debug("Event source created, creating response");
    return {
      agents: agentsWithEndpoints.map(({ endpoint, ...agentWithoutEndpoint }) => agentWithoutEndpoint)
    };
  }
  async generateCopilotResponse(ctx, data, properties) {
    var _a, _b, _c, _d;
    telemetry_client_default.capture("oss.runtime.copilot_request_created", {
      "cloud.guardrails.enabled": ((_a = data.cloud) == null ? void 0 : _a.guardrails) !== void 0,
      requestType: data.metadata.requestType
    });
    let logger2 = ctx.logger.child({
      component: "CopilotResolver.generateCopilotResponse"
    });
    logger2.debug({
      data
    }, "Generating Copilot response");
    if (properties) {
      logger2.debug("Properties provided, merging with context properties");
      ctx.properties = {
        ...ctx.properties,
        ...properties
      };
    }
    const copilotRuntime = ctx._copilotkit.runtime;
    const serviceAdapter = ctx._copilotkit.serviceAdapter;
    let copilotCloudPublicApiKey = null;
    let copilotCloudBaseUrl;
    if (data.cloud) {
      logger2 = logger2.child({
        cloud: true
      });
      logger2.debug("Cloud configuration provided, checking for public API key in headers");
      const key = ctx.request.headers.get("x-copilotcloud-public-api-key");
      if (key) {
        logger2.debug("Public API key found in headers");
        copilotCloudPublicApiKey = key;
      } else {
        logger2.error("Public API key not found in headers");
        throw new import_graphql.GraphQLError("X-CopilotCloud-Public-API-Key header is required");
      }
      if (process.env.COPILOT_CLOUD_BASE_URL) {
        copilotCloudBaseUrl = process.env.COPILOT_CLOUD_BASE_URL;
      } else if ((_b = ctx._copilotkit.cloud) == null ? void 0 : _b.baseUrl) {
        copilotCloudBaseUrl = (_c = ctx._copilotkit.cloud) == null ? void 0 : _c.baseUrl;
      } else {
        copilotCloudBaseUrl = "https://api.cloud.copilotkit.ai";
      }
      logger2 = logger2.child({
        copilotCloudBaseUrl
      });
    }
    logger2.debug("Setting up subjects");
    const responseStatus$ = new import_rxjs4.ReplaySubject();
    const interruptStreaming$ = new import_rxjs4.ReplaySubject();
    const guardrailsResult$ = new import_rxjs4.ReplaySubject();
    let outputMessages = [];
    let resolveOutputMessagesPromise;
    let rejectOutputMessagesPromise;
    const outputMessagesPromise = new Promise((resolve, reject) => {
      resolveOutputMessagesPromise = resolve;
      rejectOutputMessagesPromise = reject;
    });
    if (copilotCloudPublicApiKey) {
      ctx.properties["copilotCloudPublicApiKey"] = copilotCloudPublicApiKey;
    }
    logger2.debug("Processing");
    const { eventSource, threadId = (0, import_shared21.randomId)(), runId, serverSideActions, actionInputsWithoutAgents, extensions } = await copilotRuntime.processRuntimeRequest({
      serviceAdapter,
      messages: data.messages,
      actions: data.frontend.actions.filter((action) => action.available !== ActionInputAvailability.disabled),
      threadId: data.threadId,
      runId: data.runId,
      publicApiKey: copilotCloudPublicApiKey,
      outputMessagesPromise,
      graphqlContext: ctx,
      forwardedParameters: data.forwardedParameters,
      agentSession: data.agentSession,
      agentStates: data.agentStates,
      url: data.frontend.url,
      extensions: data.extensions,
      metaEvents: data.metaEvents
    });
    logger2.debug("Event source created, creating response");
    const eventStream = eventSource.processRuntimeEvents({
      serverSideActions,
      guardrailsResult$: ((_d = data.cloud) == null ? void 0 : _d.guardrails) ? guardrailsResult$ : null,
      actionInputsWithoutAgents: actionInputsWithoutAgents.filter(
        // TODO-AGENTS: do not exclude ALL server side actions
        (action) => !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name)
      ),
      threadId
    }).pipe(
      // shareReplay() ensures that later subscribers will see the whole stream instead of
      // just the events that were emitted after the subscriber was added.
      (0, import_rxjs4.shareReplay)(),
      (0, import_rxjs4.finalize)(() => {
        logger2.debug("Event stream finalized");
      })
    );
    const response = {
      threadId,
      runId,
      status: (0, import_rxjs4.firstValueFrom)(responseStatus$),
      extensions,
      metaEvents: new import_graphql_yoga.Repeater(async (push, stop) => {
        let eventStreamSubscription;
        eventStreamSubscription = eventStream.subscribe({
          next: async (event) => {
            if (event.type != RuntimeEventTypes.MetaEvent) {
              return;
            }
            switch (event.name) {
              case RuntimeMetaEventName.LangGraphInterruptEvent:
                push((0, import_class_transformer3.plainToInstance)(LangGraphInterruptEvent, {
                  type: event.type,
                  name: event.name,
                  value: event.value
                }));
                break;
              case RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent:
                push((0, import_class_transformer3.plainToInstance)(CopilotKitLangGraphInterruptEvent, {
                  type: event.type,
                  name: event.name,
                  data: {
                    value: event.data.value,
                    messages: event.data.messages.map((message) => {
                      if (message.type === "TextMessage" || "content" in message && "role" in message) {
                        return (0, import_class_transformer3.plainToInstance)(TextMessage, {
                          id: message.id,
                          createdAt: /* @__PURE__ */ new Date(),
                          content: [
                            message.content
                          ],
                          role: message.role,
                          status: new SuccessMessageStatus()
                        });
                      }
                      if ("arguments" in message) {
                        return (0, import_class_transformer3.plainToInstance)(ActionExecutionMessage, {
                          name: message.name,
                          id: message.id,
                          arguments: [
                            JSON.stringify(message.arguments)
                          ],
                          createdAt: /* @__PURE__ */ new Date(),
                          status: new SuccessMessageStatus()
                        });
                      }
                      throw new Error("Unknown message in metaEvents copilot resolver");
                    })
                  }
                }));
                break;
            }
          },
          error: (err) => {
            logger2.error({
              err
            }, "Error in meta events stream");
            responseStatus$.next(new UnknownErrorResponse({
              description: `An unknown error has occurred in the event stream`
            }));
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stop();
          },
          complete: async () => {
            logger2.debug("Meta events stream completed");
            responseStatus$.next(new SuccessResponseStatus());
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stop();
          }
        });
      }),
      messages: new import_graphql_yoga.Repeater(async (pushMessage, stopStreamingMessages) => {
        var _a2;
        logger2.debug("Messages repeater created");
        if ((_a2 = data.cloud) == null ? void 0 : _a2.guardrails) {
          logger2 = logger2.child({
            guardrails: true
          });
          logger2.debug("Guardrails is enabled, validating input");
          invokeGuardrails({
            baseUrl: copilotCloudBaseUrl,
            copilotCloudPublicApiKey,
            data,
            onResult: (result) => {
              logger2.debug({
                status: result.status
              }, "Guardrails validation done");
              guardrailsResult$.next(result);
              if (result.status === "denied") {
                responseStatus$.next(new GuardrailsValidationFailureResponse({
                  guardrailsReason: result.reason
                }));
                interruptStreaming$.next({
                  reason: `Interrupted due to Guardrails validation failure. Reason: ${result.reason}`
                });
                outputMessages = [
                  (0, import_class_transformer3.plainToInstance)(TextMessage, {
                    id: (0, import_shared21.randomId)(),
                    createdAt: /* @__PURE__ */ new Date(),
                    content: result.reason,
                    role: MessageRole.assistant
                  })
                ];
                resolveOutputMessagesPromise(outputMessages);
              }
            },
            onError: (err) => {
              logger2.error({
                err
              }, "Error in guardrails validation");
              responseStatus$.next(new UnknownErrorResponse({
                description: `An unknown error has occurred in the guardrails validation`
              }));
              interruptStreaming$.next({
                reason: `Interrupted due to unknown error in guardrails validation`
              });
              rejectOutputMessagesPromise(err);
            }
          });
        }
        let eventStreamSubscription;
        logger2.debug("Event stream created, subscribing to event stream");
        eventStreamSubscription = eventStream.subscribe({
          next: async (event) => {
            switch (event.type) {
              case RuntimeEventTypes.MetaEvent:
                break;
              case RuntimeEventTypes.TextMessageStart:
                const textMessageContentStream = eventStream.pipe(
                  // skip until this message start event
                  (0, import_rxjs4.skipWhile)((e) => e !== event),
                  // take until the message end event
                  (0, import_rxjs4.takeWhile)((e) => !(e.type === RuntimeEventTypes.TextMessageEnd && e.messageId == event.messageId)),
                  // filter out any other message events or message ids
                  (0, import_rxjs4.filter)((e) => e.type == RuntimeEventTypes.TextMessageContent && e.messageId == event.messageId)
                );
                const streamingTextStatus = new import_rxjs4.Subject();
                const messageId = event.messageId;
                pushMessage({
                  id: messageId,
                  parentMessageId: event.parentMessageId,
                  status: (0, import_rxjs4.firstValueFrom)(streamingTextStatus),
                  createdAt: /* @__PURE__ */ new Date(),
                  role: MessageRole.assistant,
                  content: new import_graphql_yoga.Repeater(async (pushTextChunk, stopStreamingText) => {
                    logger2.debug("Text message content repeater created");
                    const textChunks = [];
                    let textSubscription;
                    interruptStreaming$.pipe((0, import_rxjs4.shareReplay)(), (0, import_rxjs4.take)(1), (0, import_rxjs4.tap)(({ reason, messageId: messageId2 }) => {
                      logger2.debug({
                        reason,
                        messageId: messageId2
                      }, "Text streaming interrupted");
                      streamingTextStatus.next((0, import_class_transformer3.plainToInstance)(FailedMessageStatus, {
                        reason
                      }));
                      responseStatus$.next(new MessageStreamInterruptedResponse({
                        messageId: messageId2
                      }));
                      stopStreamingText();
                      textSubscription == null ? void 0 : textSubscription.unsubscribe();
                    })).subscribe();
                    logger2.debug("Subscribing to text message content stream");
                    textSubscription = textMessageContentStream.subscribe({
                      next: async (e) => {
                        if (e.type == RuntimeEventTypes.TextMessageContent) {
                          await pushTextChunk(e.content);
                          textChunks.push(e.content);
                        }
                      },
                      error: (err) => {
                        logger2.error({
                          err
                        }, "Error in text message content stream");
                        interruptStreaming$.next({
                          reason: "Error streaming message content",
                          messageId
                        });
                        stopStreamingText();
                        textSubscription == null ? void 0 : textSubscription.unsubscribe();
                      },
                      complete: () => {
                        logger2.debug("Text message content stream completed");
                        streamingTextStatus.next(new SuccessMessageStatus());
                        stopStreamingText();
                        textSubscription == null ? void 0 : textSubscription.unsubscribe();
                        outputMessages.push((0, import_class_transformer3.plainToInstance)(TextMessage, {
                          id: messageId,
                          createdAt: /* @__PURE__ */ new Date(),
                          content: textChunks.join(""),
                          role: MessageRole.assistant
                        }));
                      }
                    });
                  })
                });
                break;
              case RuntimeEventTypes.ActionExecutionStart:
                logger2.debug("Action execution start event received");
                const actionExecutionArgumentStream = eventStream.pipe(
                  (0, import_rxjs4.skipWhile)((e) => e !== event),
                  // take until the action execution end event
                  (0, import_rxjs4.takeWhile)((e) => !(e.type === RuntimeEventTypes.ActionExecutionEnd && e.actionExecutionId == event.actionExecutionId)),
                  // filter out any other action execution events or action execution ids
                  (0, import_rxjs4.filter)((e) => e.type == RuntimeEventTypes.ActionExecutionArgs && e.actionExecutionId == event.actionExecutionId)
                );
                const streamingArgumentsStatus = new import_rxjs4.Subject();
                pushMessage({
                  id: event.actionExecutionId,
                  parentMessageId: event.parentMessageId,
                  status: (0, import_rxjs4.firstValueFrom)(streamingArgumentsStatus),
                  createdAt: /* @__PURE__ */ new Date(),
                  name: event.actionName,
                  arguments: new import_graphql_yoga.Repeater(async (pushArgumentsChunk, stopStreamingArguments) => {
                    logger2.debug("Action execution argument stream created");
                    const argumentChunks = [];
                    let actionExecutionArgumentSubscription;
                    actionExecutionArgumentSubscription = actionExecutionArgumentStream.subscribe({
                      next: async (e) => {
                        if (e.type == RuntimeEventTypes.ActionExecutionArgs) {
                          await pushArgumentsChunk(e.args);
                          argumentChunks.push(e.args);
                        }
                      },
                      error: (err) => {
                        logger2.error({
                          err
                        }, "Error in action execution argument stream");
                        streamingArgumentsStatus.next((0, import_class_transformer3.plainToInstance)(FailedMessageStatus, {
                          reason: "An unknown error has occurred in the action execution argument stream"
                        }));
                        stopStreamingArguments();
                        actionExecutionArgumentSubscription == null ? void 0 : actionExecutionArgumentSubscription.unsubscribe();
                      },
                      complete: () => {
                        logger2.debug("Action execution argument stream completed");
                        streamingArgumentsStatus.next(new SuccessMessageStatus());
                        stopStreamingArguments();
                        actionExecutionArgumentSubscription == null ? void 0 : actionExecutionArgumentSubscription.unsubscribe();
                        outputMessages.push((0, import_class_transformer3.plainToInstance)(ActionExecutionMessage, {
                          id: event.actionExecutionId,
                          createdAt: /* @__PURE__ */ new Date(),
                          name: event.actionName,
                          arguments: argumentChunks.join("")
                        }));
                      }
                    });
                  })
                });
                break;
              case RuntimeEventTypes.ActionExecutionResult:
                logger2.debug({
                  result: event.result
                }, "Action execution result event received");
                pushMessage({
                  id: "result-" + event.actionExecutionId,
                  status: new SuccessMessageStatus(),
                  createdAt: /* @__PURE__ */ new Date(),
                  actionExecutionId: event.actionExecutionId,
                  actionName: event.actionName,
                  result: event.result
                });
                outputMessages.push((0, import_class_transformer3.plainToInstance)(ResultMessage, {
                  id: "result-" + event.actionExecutionId,
                  createdAt: /* @__PURE__ */ new Date(),
                  actionExecutionId: event.actionExecutionId,
                  actionName: event.actionName,
                  result: event.result
                }));
                break;
              case RuntimeEventTypes.AgentStateMessage:
                logger2.debug({
                  event
                }, "Agent message event received");
                pushMessage({
                  id: (0, import_shared21.randomId)(),
                  status: new SuccessMessageStatus(),
                  threadId: event.threadId,
                  agentName: event.agentName,
                  nodeName: event.nodeName,
                  runId: event.runId,
                  active: event.active,
                  state: event.state,
                  running: event.running,
                  role: MessageRole.assistant,
                  createdAt: /* @__PURE__ */ new Date()
                });
                outputMessages.push((0, import_class_transformer3.plainToInstance)(AgentStateMessage, {
                  id: (0, import_shared21.randomId)(),
                  threadId: event.threadId,
                  agentName: event.agentName,
                  nodeName: event.nodeName,
                  runId: event.runId,
                  active: event.active,
                  state: event.state,
                  running: event.running,
                  role: MessageRole.assistant,
                  createdAt: /* @__PURE__ */ new Date()
                }));
                break;
            }
          },
          error: (err) => {
            logger2.error({
              err
            }, "Error in event stream");
            responseStatus$.next(new UnknownErrorResponse({
              description: `An unknown error has occurred in the event stream`
            }));
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stopStreamingMessages();
            rejectOutputMessagesPromise(err);
          },
          complete: async () => {
            var _a3;
            logger2.debug("Event stream completed");
            if ((_a3 = data.cloud) == null ? void 0 : _a3.guardrails) {
              logger2.debug("Guardrails is enabled, waiting for guardrails result");
              await (0, import_rxjs4.firstValueFrom)(guardrailsResult$);
            }
            responseStatus$.next(new SuccessResponseStatus());
            eventStreamSubscription == null ? void 0 : eventStreamSubscription.unsubscribe();
            stopStreamingMessages();
            resolveOutputMessagesPromise(outputMessages);
          }
        });
      })
    };
    return response;
  }
};
__name(CopilotResolver, "CopilotResolver");
_ts_decorate19([
  (0, import_type_graphql20.Query)(() => String),
  _ts_metadata19("design:type", Function),
  _ts_metadata19("design:paramtypes", []),
  _ts_metadata19("design:returntype", Promise)
], CopilotResolver.prototype, "hello", null);
_ts_decorate19([
  (0, import_type_graphql20.Query)(() => AgentsResponse),
  _ts_param(0, (0, import_type_graphql20.Ctx)()),
  _ts_metadata19("design:type", Function),
  _ts_metadata19("design:paramtypes", [
    typeof GraphQLContext === "undefined" ? Object : GraphQLContext
  ]),
  _ts_metadata19("design:returntype", Promise)
], CopilotResolver.prototype, "availableAgents", null);
_ts_decorate19([
  (0, import_type_graphql20.Mutation)(() => CopilotResponse),
  _ts_param(0, (0, import_type_graphql20.Ctx)()),
  _ts_param(1, (0, import_type_graphql20.Arg)("data")),
  _ts_param(2, (0, import_type_graphql20.Arg)("properties", () => import_graphql_scalars2.GraphQLJSONObject, {
    nullable: true
  })),
  _ts_metadata19("design:type", Function),
  _ts_metadata19("design:paramtypes", [
    typeof GraphQLContext === "undefined" ? Object : GraphQLContext,
    typeof GenerateCopilotResponseInput === "undefined" ? Object : GenerateCopilotResponseInput,
    typeof CopilotRequestContextProperties === "undefined" ? Object : CopilotRequestContextProperties
  ]),
  _ts_metadata19("design:returntype", Promise)
], CopilotResolver.prototype, "generateCopilotResponse", null);
CopilotResolver = _ts_decorate19([
  (0, import_type_graphql20.Resolver)(() => CopilotResponse)
], CopilotResolver);

// src/lib/integrations/shared.ts
var import_plugin_defer_stream = require("@graphql-yoga/plugin-defer-stream");

// src/lib/logger.ts
var import_pino = __toESM(require("pino"));
var import_pino_pretty = __toESM(require("pino-pretty"));
function createLogger(options) {
  const { level, component } = options || {};
  const stream = (0, import_pino_pretty.default)({
    colorize: true
  });
  const logger2 = (0, import_pino.default)({
    level: process.env.LOG_LEVEL || level || "error",
    redact: {
      paths: [
        "pid",
        "hostname"
      ],
      remove: true
    }
  }, stream);
  if (component) {
    return logger2.child({
      component
    });
  } else {
    return logger2;
  }
}
__name(createLogger, "createLogger");

// src/graphql/resolvers/state.resolver.ts
var import_type_graphql23 = require("type-graphql");
var import_type_graphql24 = require("type-graphql");
var import_type_graphql25 = require("type-graphql");

// src/graphql/types/load-agent-state-response.type.ts
var import_type_graphql21 = require("type-graphql");
function _ts_decorate20(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate20, "_ts_decorate");
function _ts_metadata20(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata20, "_ts_metadata");
var LoadAgentStateResponse = class {
  threadId;
  threadExists;
  state;
  messages;
};
__name(LoadAgentStateResponse, "LoadAgentStateResponse");
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => String),
  _ts_metadata20("design:type", String)
], LoadAgentStateResponse.prototype, "threadId", void 0);
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => Boolean),
  _ts_metadata20("design:type", Boolean)
], LoadAgentStateResponse.prototype, "threadExists", void 0);
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => String),
  _ts_metadata20("design:type", String)
], LoadAgentStateResponse.prototype, "state", void 0);
_ts_decorate20([
  (0, import_type_graphql21.Field)(() => String),
  _ts_metadata20("design:type", String)
], LoadAgentStateResponse.prototype, "messages", void 0);
LoadAgentStateResponse = _ts_decorate20([
  (0, import_type_graphql21.ObjectType)()
], LoadAgentStateResponse);

// src/graphql/inputs/load-agent-state.input.ts
var import_type_graphql22 = require("type-graphql");
function _ts_decorate21(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate21, "_ts_decorate");
function _ts_metadata21(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata21, "_ts_metadata");
var LoadAgentStateInput = class {
  threadId;
  agentName;
};
__name(LoadAgentStateInput, "LoadAgentStateInput");
_ts_decorate21([
  (0, import_type_graphql22.Field)(() => String),
  _ts_metadata21("design:type", String)
], LoadAgentStateInput.prototype, "threadId", void 0);
_ts_decorate21([
  (0, import_type_graphql22.Field)(() => String),
  _ts_metadata21("design:type", String)
], LoadAgentStateInput.prototype, "agentName", void 0);
LoadAgentStateInput = _ts_decorate21([
  (0, import_type_graphql22.InputType)()
], LoadAgentStateInput);

// src/graphql/resolvers/state.resolver.ts
function _ts_decorate22(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function")
    r = Reflect.decorate(decorators, target, key, desc);
  else
    for (var i = decorators.length - 1; i >= 0; i--)
      if (d = decorators[i])
        r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
}
__name(_ts_decorate22, "_ts_decorate");
function _ts_metadata22(k, v) {
  if (typeof Reflect === "object" && typeof Reflect.metadata === "function")
    return Reflect.metadata(k, v);
}
__name(_ts_metadata22, "_ts_metadata");
function _ts_param2(paramIndex, decorator) {
  return function(target, key) {
    decorator(target, key, paramIndex);
  };
}
__name(_ts_param2, "_ts_param");
var StateResolver = class {
  async loadAgentState(ctx, data) {
    const agents = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);
    const agent = agents.find((agent2) => agent2.name === data.agentName);
    if (!agent) {
      return {
        threadId: data.threadId || "",
        threadExists: false,
        state: JSON.stringify({}),
        messages: JSON.stringify([])
      };
    }
    const state = await ctx._copilotkit.runtime.loadAgentState(ctx, data.threadId, data.agentName);
    return state;
  }
};
__name(StateResolver, "StateResolver");
_ts_decorate22([
  (0, import_type_graphql25.Query)(() => LoadAgentStateResponse),
  _ts_param2(0, (0, import_type_graphql24.Ctx)()),
  _ts_param2(1, (0, import_type_graphql23.Arg)("data")),
  _ts_metadata22("design:type", Function),
  _ts_metadata22("design:paramtypes", [
    typeof GraphQLContext === "undefined" ? Object : GraphQLContext,
    typeof LoadAgentStateInput === "undefined" ? Object : LoadAgentStateInput
  ]),
  _ts_metadata22("design:returntype", Promise)
], StateResolver.prototype, "loadAgentState", null);
StateResolver = _ts_decorate22([
  (0, import_type_graphql23.Resolver)(() => LoadAgentStateResponse)
], StateResolver);

// src/lib/integrations/shared.ts
var packageJson2 = __toESM(require_package());
var logger = createLogger();
var addCustomHeaderPlugin = {
  onResponse({ response }) {
    response.headers.set("X-CopilotKit-Runtime-Version", packageJson2.version);
  }
};
async function createContext(initialContext, copilotKitContext, contextLogger, properties = {}) {
  logger.debug({
    copilotKitContext
  }, "Creating GraphQL context");
  const ctx = {
    ...initialContext,
    _copilotkit: {
      ...copilotKitContext
    },
    properties: {
      ...properties
    },
    logger: contextLogger
  };
  return ctx;
}
__name(createContext, "createContext");
function buildSchema(options = {}) {
  logger.debug("Building GraphQL schema...");
  const schema = (0, import_type_graphql26.buildSchemaSync)({
    resolvers: [
      CopilotResolver,
      StateResolver
    ],
    emitSchemaFile: options.emitSchemaFile
  });
  logger.debug("GraphQL schema built successfully");
  return schema;
}
__name(buildSchema, "buildSchema");
function getCommonConfig(options) {
  var _a;
  const logLevel = process.env.LOG_LEVEL || options.logLevel || "error";
  const logger2 = createLogger({
    level: logLevel,
    component: "getCommonConfig"
  });
  const contextLogger = createLogger({
    level: logLevel
  });
  if (options.cloud) {
    telemetry_client_default.setCloudConfiguration({
      publicApiKey: options.cloud.publicApiKey,
      baseUrl: options.cloud.baseUrl
    });
  }
  if ((_a = options.properties) == null ? void 0 : _a._copilotkit) {
    telemetry_client_default.setGlobalProperties({
      _copilotkit: {
        ...options.properties._copilotkit
      }
    });
  }
  telemetry_client_default.setGlobalProperties({
    runtime: {
      serviceAdapter: options.serviceAdapter.constructor.name
    }
  });
  return {
    logging: createLogger({
      component: "Yoga GraphQL",
      level: logLevel
    }),
    schema: buildSchema(),
    plugins: [
      (0, import_plugin_defer_stream.useDeferStream)(),
      addCustomHeaderPlugin
    ],
    context: (ctx) => createContext(ctx, options, contextLogger, options.properties)
  };
}
__name(getCommonConfig, "getCommonConfig");

// src/lib/integrations/nextjs/app-router.ts
var import_graphql_yoga2 = require("graphql-yoga");
function copilotRuntimeNextJSAppRouterEndpoint(options) {
  var _a;
  const commonConfig = getCommonConfig(options);
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "nextjs-app-router"
    }
  });
  if ((_a = options.properties) == null ? void 0 : _a._copilotkit) {
    telemetry_client_default.setGlobalProperties({
      _copilotkit: options.properties._copilotkit
    });
  }
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  const logger2 = commonConfig.logging;
  logger2.debug("Creating NextJS App Router endpoint");
  const yoga = (0, import_graphql_yoga2.createYoga)({
    ...commonConfig,
    graphqlEndpoint: options.endpoint,
    fetchAPI: {
      Response: globalThis.Response
    }
  });
  return {
    handleRequest: yoga,
    GET: yoga,
    POST: yoga,
    OPTIONS: yoga
  };
}
__name(copilotRuntimeNextJSAppRouterEndpoint, "copilotRuntimeNextJSAppRouterEndpoint");

// src/lib/integrations/nextjs/pages-router.ts
var import_graphql_yoga3 = require("graphql-yoga");
var config = {
  api: {
    bodyParser: false
  }
};
function copilotRuntimeNextJSPagesRouterEndpoint(options) {
  var _a;
  const commonConfig = getCommonConfig(options);
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "nextjs-pages-router"
    }
  });
  if ((_a = options.properties) == null ? void 0 : _a._copilotkit) {
    telemetry_client_default.setGlobalProperties({
      _copilotkit: options.properties._copilotkit
    });
  }
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  const logger2 = commonConfig.logging;
  logger2.debug("Creating NextJS Pages Router endpoint");
  const yoga = (0, import_graphql_yoga3.createYoga)({
    ...commonConfig,
    graphqlEndpoint: options.endpoint
  });
  return yoga;
}
__name(copilotRuntimeNextJSPagesRouterEndpoint, "copilotRuntimeNextJSPagesRouterEndpoint");

// src/lib/integrations/node-http/index.ts
var import_graphql_yoga4 = require("graphql-yoga");
function copilotRuntimeNodeHttpEndpoint(options) {
  var _a;
  const commonConfig = getCommonConfig(options);
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "node-http"
    }
  });
  if ((_a = options.properties) == null ? void 0 : _a._copilotkit) {
    telemetry_client_default.setGlobalProperties({
      _copilotkit: options.properties._copilotkit
    });
  }
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  const logger2 = commonConfig.logging;
  logger2.debug("Creating Node HTTP endpoint");
  const yoga = (0, import_graphql_yoga4.createYoga)({
    ...commonConfig,
    graphqlEndpoint: options.endpoint
  });
  return yoga;
}
__name(copilotRuntimeNodeHttpEndpoint, "copilotRuntimeNodeHttpEndpoint");

// src/lib/integrations/node-express/index.ts
function copilotRuntimeNodeExpressEndpoint(options) {
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "node-express"
    }
  });
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  return copilotRuntimeNodeHttpEndpoint(options);
}
__name(copilotRuntimeNodeExpressEndpoint, "copilotRuntimeNodeExpressEndpoint");

// src/lib/integrations/nest/index.ts
function copilotRuntimeNestEndpoint(options) {
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "nest"
    }
  });
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  return copilotRuntimeNodeHttpEndpoint(options);
}
__name(copilotRuntimeNestEndpoint, "copilotRuntimeNestEndpoint");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CopilotRuntime,
  GoogleGenerativeAIAdapter,
  GroqAdapter,
  LangChainAdapter,
  OpenAIAdapter,
  OpenAIAssistantAdapter,
  UnifyAdapter,
  addCustomHeaderPlugin,
  buildSchema,
  config,
  convertMCPToolsToActions,
  copilotKitEndpoint,
  copilotRuntimeNestEndpoint,
  copilotRuntimeNextJSAppRouterEndpoint,
  copilotRuntimeNextJSPagesRouterEndpoint,
  copilotRuntimeNodeExpressEndpoint,
  copilotRuntimeNodeHttpEndpoint,
  createContext,
  createLogger,
  extractParametersFromSchema,
  flattenToolCallsNoDuplicates,
  generateMcpToolInstructions,
  getCommonConfig,
  langGraphPlatformEndpoint,
  resolveEndpointType
});
//# sourceMappingURL=index.js.map