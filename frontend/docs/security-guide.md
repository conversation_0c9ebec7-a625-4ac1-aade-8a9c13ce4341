# Authentication Security Guide

## 🛡️ **Security Architecture Overview**

The PI Lawyer AI authentication system implements multiple layers of security to protect sensitive legal data and ensure compliance with legal industry standards.

### **Security Layers**

1. **Transport Security**: HTTPS/TLS encryption
2. **Authentication**: JWT-based token validation
3. **Authorization**: Role-based access control (RBAC)
4. **Tenant Isolation**: Multi-tenant data segregation
5. **Input Validation**: Comprehensive input sanitization
6. **Audit Logging**: Security event tracking

## 🔐 **Authentication Security**

### **JWT Token Security**

#### **Token Generation**
```typescript
// ✅ SECURE: Use strong secrets
const jwtSecret = process.env.SUPABASE_JWT_SECRET; // 256-bit secret
if (!jwtSecret || jwtSecret.length < 32) {
  throw new Error('JWT secret must be at least 32 characters');
}

// ✅ SECURE: Set appropriate expiration
const token = await new SignJWT(payload)
  .setProtectedHeader({ alg: 'HS256' })
  .setIssuedAt()
  .setExpirationTime('1h') // Short-lived tokens
  .sign(secret);
```

#### **Token Validation**
```typescript
// ✅ SECURE: Always validate tokens server-side
export async function GET(request: NextRequest) {
  const token = extractJwtFromRequest(request);
  if (!token) {
    return NextResponse.json({ error: 'No token provided' }, { status: 401 });
  }
  
  try {
    const payload = await validateJwt(token);
    // Token is valid, proceed with request
  } catch (error) {
    // Log security event
    console.error('JWT validation failed:', {
      error: error.message,
      ip: request.ip,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
  }
}
```

#### **Token Storage**
```typescript
// ✅ SECURE: Use httpOnly cookies for sensitive tokens
const cookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: 3600 // 1 hour
};

// ❌ INSECURE: Don't store tokens in localStorage
localStorage.setItem('token', token); // Vulnerable to XSS!
```

### **Session Management**

#### **Session Security**
```typescript
// ✅ SECURE: Implement session timeout
const SESSION_TIMEOUT = 60 * 60 * 1000; // 1 hour

function isSessionExpired(session: Session): boolean {
  if (!session.expires_at) return false;
  return new Date(session.expires_at * 1000) < new Date();
}

// ✅ SECURE: Automatic session refresh
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  }
  if (event === 'SIGNED_OUT') {
    // Clear all client-side data
    clearUserData();
  }
});
```

#### **Concurrent Session Handling**
```typescript
// ✅ SECURE: Detect concurrent sessions
function detectConcurrentSessions(userId: string) {
  // Track active sessions per user
  const activeSessions = getActiveSessions(userId);
  if (activeSessions.length > MAX_CONCURRENT_SESSIONS) {
    // Invalidate oldest sessions
    invalidateOldestSessions(userId, activeSessions);
  }
}
```

## 🎭 **Authorization Security**

### **Role-Based Access Control (RBAC)**

#### **Permission Validation**
```typescript
// ✅ SECURE: Always validate permissions server-side
export async function DELETE(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { user } = authResult;
  
  // Check specific permission
  if (!checkPermission(user, 'delete:cases')) {
    // Log unauthorized access attempt
    logSecurityEvent({
      type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
      userId: user.id,
      tenantId: user.tenantId,
      action: 'delete:cases',
      resource: 'cases',
      ip: request.ip,
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  // Proceed with deletion
}
```

#### **Privilege Escalation Prevention**
```typescript
// ✅ SECURE: Prevent privilege escalation
function updateUserRole(currentUser: AuthUser, targetUserId: string, newRole: UserRole) {
  // Users cannot elevate their own privileges
  if (currentUser.id === targetUserId && isHigherRole(newRole, currentUser.role)) {
    throw new Error('Cannot elevate own privileges');
  }
  
  // Users cannot assign roles higher than their own
  if (isHigherRole(newRole, currentUser.role)) {
    throw new Error('Cannot assign higher role than own role');
  }
  
  // Super admins can assign any role
  if (!isSuperAdmin(currentUser)) {
    if (newRole === UserRole.Superadmin) {
      throw new Error('Only super admins can assign super admin role');
    }
  }
}
```

### **Tenant Isolation Security**

#### **Data Segregation**
```typescript
// ✅ SECURE: Enforce tenant isolation at database level
async function getCases(user: AuthUser) {
  // Always filter by tenant
  const { data, error } = await supabase
    .schema('tenants')
    .from('cases')
    .select('*')
    .eq('tenant_id', user.tenantId); // Critical: tenant isolation
    
  if (error) {
    throw new Error('Database query failed');
  }
  
  return data;
}

// ❌ INSECURE: Missing tenant isolation
async function getCasesInsecure() {
  const { data } = await supabase
    .from('cases')
    .select('*'); // Returns data from all tenants!
    
  return data;
}
```

#### **Cross-Tenant Access Prevention**
```typescript
// ✅ SECURE: Validate tenant access
export async function GET(request: NextRequest, { params }: { params: { tenantId: string } }) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { user } = authResult;
  const requestedTenantId = params.tenantId;
  
  // Prevent cross-tenant access
  if (!canAccessTenant(user, requestedTenantId)) {
    logSecurityEvent({
      type: 'CROSS_TENANT_ACCESS_ATTEMPT',
      userId: user.id,
      userTenantId: user.tenantId,
      requestedTenantId,
      ip: request.ip,
      timestamp: new Date().toISOString()
    });
    
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }
  
  // Proceed with tenant-specific operation
}
```

## 🔍 **Input Validation Security**

### **Parameter Validation**
```typescript
// ✅ SECURE: Validate all inputs
import { z } from 'zod';

const CreateCaseSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(5000),
  clientId: z.string().uuid(),
  status: z.enum(['open', 'closed', 'pending'])
});

export async function POST(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await request.json();
    const validatedData = CreateCaseSchema.parse(body);
    
    // Use validated data
    const newCase = await createCase(validatedData);
    return NextResponse.json(newCase);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.errors }, { status: 400 });
    }
    throw error;
  }
}
```

### **SQL Injection Prevention**
```typescript
// ✅ SECURE: Use parameterized queries (Supabase handles this)
const cases = await supabase
  .schema('tenants')
  .from('cases')
  .select('*')
  .eq('status', userProvidedStatus) // Automatically parameterized
  .eq('tenant_id', user.tenantId);

// ❌ INSECURE: Raw SQL (avoid if possible)
const query = `SELECT * FROM cases WHERE status = '${userProvidedStatus}'`; // SQL injection risk!
```

### **XSS Prevention**
```typescript
// ✅ SECURE: Sanitize user input for display
import DOMPurify from 'dompurify';

function DisplayUserContent({ content }: { content: string }) {
  const sanitizedContent = DOMPurify.sanitize(content);
  return <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />;
}

// ✅ SECURE: Use React's built-in XSS protection
function DisplayUserText({ text }: { text: string }) {
  return <div>{text}</div>; // React automatically escapes
}
```

## 📊 **Security Monitoring & Logging**

### **Security Event Logging**
```typescript
interface SecurityEvent {
  type: 'LOGIN_ATTEMPT' | 'LOGIN_SUCCESS' | 'LOGIN_FAILURE' | 'UNAUTHORIZED_ACCESS_ATTEMPT' | 'PERMISSION_DENIED' | 'CROSS_TENANT_ACCESS_ATTEMPT';
  userId?: string;
  tenantId?: string;
  ip: string;
  userAgent?: string;
  resource?: string;
  action?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

async function logSecurityEvent(event: SecurityEvent) {
  // Log to security schema
  await supabase
    .schema('security')
    .from('events')
    .insert({
      ...event,
      id: crypto.randomUUID()
    });
    
  // Also log to external security monitoring if configured
  if (process.env.SECURITY_WEBHOOK_URL) {
    await fetch(process.env.SECURITY_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(event)
    });
  }
}
```

### **Anomaly Detection**
```typescript
// ✅ SECURE: Detect suspicious patterns
async function detectSuspiciousActivity(userId: string) {
  const recentEvents = await supabase
    .schema('security')
    .from('events')
    .select('*')
    .eq('userId', userId)
    .gte('timestamp', new Date(Date.now() - 60 * 60 * 1000).toISOString()) // Last hour
    .order('timestamp', { ascending: false });
    
  // Check for rapid login attempts
  const loginAttempts = recentEvents.filter(e => e.type === 'LOGIN_ATTEMPT');
  if (loginAttempts.length > 10) {
    await lockAccount(userId, 'Suspicious login activity');
  }
  
  // Check for cross-tenant access attempts
  const crossTenantAttempts = recentEvents.filter(e => e.type === 'CROSS_TENANT_ACCESS_ATTEMPT');
  if (crossTenantAttempts.length > 3) {
    await flagAccount(userId, 'Cross-tenant access attempts');
  }
}
```

## 🚨 **Incident Response**

### **Security Incident Handling**
```typescript
enum IncidentSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

async function handleSecurityIncident(incident: {
  type: string;
  severity: IncidentSeverity;
  userId?: string;
  tenantId?: string;
  description: string;
}) {
  // Log incident
  await logSecurityEvent({
    type: 'SECURITY_INCIDENT' as any,
    ...incident,
    ip: 'system',
    timestamp: new Date().toISOString()
  });
  
  // Take immediate action based on severity
  switch (incident.severity) {
    case IncidentSeverity.CRITICAL:
      // Lock account immediately
      if (incident.userId) {
        await lockAccount(incident.userId, incident.description);
      }
      // Alert security team
      await alertSecurityTeam(incident);
      break;
      
    case IncidentSeverity.HIGH:
      // Flag account for review
      if (incident.userId) {
        await flagAccount(incident.userId, incident.description);
      }
      break;
      
    case IncidentSeverity.MEDIUM:
      // Increase monitoring
      await increaseMonitoring(incident.userId);
      break;
  }
}
```

## 🔧 **Security Configuration**

### **Environment Security**
```bash
# Production environment variables
NODE_ENV=production
HTTPS_ONLY=true
SECURE_COOKIES=true

# JWT Configuration
SUPABASE_JWT_SECRET=your-256-bit-secret-key
JWT_EXPIRATION=3600 # 1 hour

# Rate Limiting
RATE_LIMIT_WINDOW=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Security Headers
CONTENT_SECURITY_POLICY=default-src 'self'
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
```

### **Security Headers**
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // CSP header
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
  );
  
  return response;
}
```

## 📋 **Security Checklist**

### **Pre-Deployment Security Review**
- [ ] All API routes have authentication
- [ ] Permission checks are implemented
- [ ] Tenant isolation is enforced
- [ ] Input validation is comprehensive
- [ ] Error messages don't leak information
- [ ] Security logging is implemented
- [ ] Rate limiting is configured
- [ ] Security headers are set
- [ ] JWT secrets are properly configured
- [ ] HTTPS is enforced in production

### **Regular Security Audits**
- [ ] Review user permissions quarterly
- [ ] Audit security logs monthly
- [ ] Update dependencies regularly
- [ ] Penetration testing annually
- [ ] Security training for developers
- [ ] Incident response plan updated
- [ ] Backup and recovery tested
- [ ] Compliance requirements met

## 📚 **Security Resources**

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [JWT Security Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [Supabase Security Guide](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)
- [Next.js Security Headers](https://nextjs.org/docs/advanced-features/security-headers)
