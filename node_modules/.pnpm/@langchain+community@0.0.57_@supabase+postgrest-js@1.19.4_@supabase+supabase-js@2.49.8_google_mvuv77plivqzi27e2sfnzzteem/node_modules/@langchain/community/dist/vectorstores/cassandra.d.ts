import type { EmbeddingsInterface } from "@langchain/core/embeddings";
import { VectorStore, MaxMarginalRelevanceSearchOptions } from "@langchain/core/vectorstores";
import { Document } from "@langchain/core/documents";
import { CassandraClientArgs, Column, Filter, Index, Where<PERSON><PERSON><PERSON>, CassandraTableArgs, CassandraTable } from "../utils/cassandra.js";
/**
 * @deprecated
 * Import from "../utils/cassandra.js" instead.
 */
export { Column, Filter, Index, WhereClause };
export type SupportedVectorTypes = "cosine" | "dot_product" | "euclidean";
export interface CassandraLibArgs extends CassandraClientArgs, Omit<CassandraTableArgs, "nonKeyColumns" | "keyspace"> {
    keyspace: string;
    vectorType?: SupportedVectorTypes;
    dimensions: number;
    metadataColumns?: Column[];
    nonKeyColumns?: Column | Column[];
}
/**
 * Class for interacting with the Cassandra database. It extends the
 * VectorStore class and provides methods for adding vectors and
 * documents, searching for similar vectors, and creating instances from
 * texts or documents.
 */
export declare class CassandraStore extends VectorStore {
    FilterType: WhereClause;
    private readonly table;
    private readonly idColumnAutoName;
    private readonly idColumnAutoGenerated;
    private readonly vectorColumnName;
    private readonly vectorColumn;
    private readonly textColumnName;
    private readonly textColumn;
    private readonly metadataColumnDefaultName;
    private readonly metadataColumns;
    private readonly similarityColumn;
    private readonly embeddingColumnAlias;
    _vectorstoreType(): string;
    private _cleanArgs;
    private _getColumnByName;
    constructor(embeddings: EmbeddingsInterface, args: CassandraLibArgs);
    /**
     * Method to save vectors to the Cassandra database.
     * @param vectors Vectors to save.
     * @param documents The documents associated with the vectors.
     * @returns Promise that resolves when the vectors have been added.
     */
    addVectors(vectors: number[][], documents: Document[]): Promise<void>;
    getCassandraTable(): CassandraTable;
    /**
     * Method to add documents to the Cassandra database.
     * @param documents The documents to add.
     * @returns Promise that resolves when the documents have been added.
     */
    addDocuments(documents: Document[]): Promise<void>;
    /**
     * Helper method to search for vectors that are similar to a given query vector.
     * @param query The query vector.
     * @param k The number of similar Documents to return.
     * @param filter Optional filter to be applied as a WHERE clause.
     * @param includeEmbedding Whether to include the embedding vectors in the results.
     * @returns Promise that resolves with an array of tuples, each containing a Document and a score.
     */
    search(query: number[], k: number, filter?: WhereClause, includeEmbedding?: boolean): Promise<[Document, number][]>;
    /**
     * Method to search for vectors that are similar to a given query vector.
     * @param query The query vector.
     * @param k The number of similar Documents to return.
     * @param filter Optional filter to be applied as a WHERE clause.
     * @returns Promise that resolves with an array of tuples, each containing a Document and a score.
     */
    similaritySearchVectorWithScore(query: number[], k: number, filter?: WhereClause): Promise<[Document, number][]>;
    /**
     * Method to search for vectors that are similar to a given query vector, but with
     * the results selected using the maximal marginal relevance.
     * @param query The query string.
     * @param options.k The number of similar Documents to return.
     * @param options.fetchK=4*k The number of records to fetch before passing to the MMR algorithm.
     * @param options.lambda=0.5 The degree of diversity among the results between 0 (maximum diversity) and 1 (minimum diversity).
     * @param options.filter Optional filter to be applied as a WHERE clause.
     * @returns List of documents selected by maximal marginal relevance.
     */
    maxMarginalRelevanceSearch(query: string, options: MaxMarginalRelevanceSearchOptions<this["FilterType"]>): Promise<Document[]>;
    /**
     * Static method to create an instance of CassandraStore from texts.
     * @param texts The texts to use.
     * @param metadatas The metadata associated with the texts.
     * @param embeddings The embeddings to use.
     * @param args The arguments for the CassandraStore.
     * @returns Promise that resolves with a new instance of CassandraStore.
     */
    static fromTexts(texts: string[], metadatas: object | object[], embeddings: EmbeddingsInterface, args: CassandraLibArgs): Promise<CassandraStore>;
    /**
     * Static method to create an instance of CassandraStore from documents.
     * @param docs The documents to use.
     * @param embeddings The embeddings to use.
     * @param args The arguments for the CassandraStore.
     * @returns Promise that resolves with a new instance of CassandraStore.
     */
    static fromDocuments(docs: Document[], embeddings: EmbeddingsInterface, args: CassandraLibArgs): Promise<CassandraStore>;
    /**
     * Static method to create an instance of CassandraStore from an existing
     * index.
     * @param embeddings The embeddings to use.
     * @param args The arguments for the CassandraStore.
     * @returns Promise that resolves with a new instance of CassandraStore.
     */
    static fromExistingIndex(embeddings: EmbeddingsInterface, args: CassandraLibArgs): Promise<CassandraStore>;
}
