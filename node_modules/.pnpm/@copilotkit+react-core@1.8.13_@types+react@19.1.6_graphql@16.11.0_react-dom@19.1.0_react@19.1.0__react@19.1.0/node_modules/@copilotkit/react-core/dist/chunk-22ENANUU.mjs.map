{"version": 3, "sources": ["../src/components/error-boundary/error-utils.tsx", "../src/components/toast/toast-provider.tsx"], "sourcesContent": ["import React, { useCallback } from \"react\";\nimport { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport { useToast } from \"../toast/toast-provider\";\nimport { ExclamationMarkIcon } from \"../toast/exclamation-mark-icon\";\nimport ReactMarkdown from \"react-markdown\";\n\ninterface OriginalError {\n  message?: string;\n  stack?: string;\n}\n\nexport function ErrorToast({ errors }: { errors: (Error | GraphQLError)[] }) {\n  const errorsToRender = errors.map((error, idx) => {\n    const originalError =\n      \"extensions\" in error ? (error.extensions?.originalError as undefined | OriginalError) : {};\n    const message = originalError?.message ?? error.message;\n    const code = \"extensions\" in error ? (error.extensions?.code as string) : null;\n\n    return (\n      <div\n        key={idx}\n        style={{\n          marginTop: idx === 0 ? 0 : 10,\n          marginBottom: 14,\n        }}\n      >\n        <ExclamationMarkIcon style={{ marginBottom: 4 }} />\n\n        {code && (\n          <div\n            style={{\n              fontWeight: \"600\",\n              marginBottom: 4,\n            }}\n          >\n            Copilot Cloud Error:{\" \"}\n            <span style={{ fontFamily: \"monospace\", fontWeight: \"normal\" }}>{code}</span>\n          </div>\n        )}\n        <ReactMarkdown>{message}</ReactMarkdown>\n      </div>\n    );\n  });\n  return (\n    <div\n      style={{\n        fontSize: \"13px\",\n        maxWidth: \"600px\",\n      }}\n    >\n      {errorsToRender}\n      <div style={{ fontSize: \"11px\", opacity: 0.75 }}>\n        NOTE: This error only displays during local development.\n      </div>\n    </div>\n  );\n}\n\nexport function useErrorToast() {\n  const { addToast } = useToast();\n\n  return useCallback(\n    (error: (Error | GraphQLError)[]) => {\n      const errorId = error\n        .map((err) => {\n          const message =\n            \"extensions\" in err\n              ? (err.extensions?.originalError as any)?.message || err.message\n              : err.message;\n          const stack = err.stack || \"\";\n          return btoa(message + stack).slice(0, 32); // Create hash from message + stack\n        })\n        .join(\"|\");\n\n      addToast({\n        type: \"error\",\n        id: errorId, // Toast libraries typically dedupe by id\n        message: <ErrorToast errors={error} />,\n      });\n    },\n    [addToast],\n  );\n}\n\nexport function useAsyncCallback<T extends (...args: any[]) => Promise<any>>(\n  callback: T,\n  deps: Parameters<typeof useCallback>[1],\n) {\n  const addErrorToast = useErrorToast();\n  return useCallback(async (...args: Parameters<T>) => {\n    try {\n      return await callback(...args);\n    } catch (error) {\n      console.error(\"Error in async callback:\", error);\n      // @ts-ignore\n      addErrorToast([error]);\n      throw error;\n    }\n  }, deps);\n}\n", "import { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport React, { createContext, useContext, useState, useCallback } from \"react\";\nimport { ErrorToast } from \"../error-boundary/error-utils\";\nimport { PartialBy } from \"@copilotkit/shared\";\n\ninterface Toast {\n  id: string;\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  duration?: number;\n}\n\ninterface ToastContextValue {\n  toasts: Toast[];\n  addToast: (toast: PartialBy<Toast, \"id\">) => void;\n  addGraphQLErrorsToast: (errors: GraphQLError[]) => void;\n  removeToast: (id: string) => void;\n  enabled: boolean;\n}\n\nconst ToastContext = createContext<ToastContextValue | undefined>(undefined);\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error(\"useToast must be used within a ToastProvider\");\n  }\n  return context;\n}\n\nexport function ToastProvider({\n  enabled,\n  children,\n}: {\n  enabled: boolean;\n  children: React.ReactNode;\n}) {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n  const addToast = useCallback(\n    (toast: PartialBy<Toast, \"id\">) => {\n      // We do not display these errors unless we are in dev mode.\n      if (!enabled) {\n        return;\n      }\n\n      const id = toast.id ?? Math.random().toString(36).substring(2, 9);\n\n      setToasts((currentToasts) => {\n        if (currentToasts.find((toast) => toast.id === id)) return currentToasts;\n        return [...currentToasts, { ...toast, id }];\n      });\n\n      if (toast.duration) {\n        setTimeout(() => {\n          removeToast(id);\n        }, toast.duration);\n      }\n    },\n    [enabled],\n  );\n\n  const addGraphQLErrorsToast = useCallback((errors: GraphQLError[]) => {\n    addToast({\n      type: \"error\",\n      message: <ErrorToast errors={errors} />,\n    });\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));\n  }, []);\n\n  const value = {\n    toasts,\n    addToast,\n    addGraphQLErrorsToast,\n    removeToast,\n    enabled,\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      <div\n        style={{\n          position: \"fixed\",\n          bottom: \"1rem\",\n          left: \"50%\",\n          transform: \"translateX(-50%)\",\n          zIndex: 50,\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"0.5rem\",\n        }}\n      >\n        {toasts.length > 1 && (\n          <div style={{ textAlign: \"right\" }}>\n            <button\n              onClick={() => setToasts([])}\n              style={{\n                padding: \"4px 8px\",\n                fontSize: \"12px\",\n                cursor: \"pointer\",\n                background: \"white\",\n                border: \"1px solid rgba(0,0,0,0.2)\",\n                borderRadius: \"4px\",\n              }}\n            >\n              Close All\n            </button>\n          </div>\n        )}\n        {toasts.map((toast) => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n      {children}\n    </ToastContext.Provider>\n  );\n}\n\nfunction Toast({\n  message,\n  type = \"info\",\n  onClose,\n}: {\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  onClose: () => void;\n}) {\n  const bgColors = {\n    info: \"#3b82f6\",\n    success: \"#22c55e\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  };\n\n  return (\n    <div\n      style={{\n        backgroundColor: bgColors[type],\n        color: \"white\",\n        padding: \"0.5rem 1.5rem\",\n        borderRadius: \"0.25rem\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        position: \"relative\",\n        minWidth: \"200px\",\n      }}\n    >\n      <div>{message}</div>\n      <button\n        onClick={onClose}\n        style={{\n          position: \"absolute\",\n          top: \"0\",\n          right: \"0\",\n          background: \"none\",\n          border: \"none\",\n          color: \"white\",\n          cursor: \"pointer\",\n          padding: \"0.5rem\",\n          fontSize: \"1rem\",\n        }}\n      >\n        ✕\n      </button>\n    </div>\n  );\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAgB,eAAAA,oBAAmB;;;ACCnC,SAAgB,eAAe,YAAY,UAAU,mBAAmB;AA+DzD,cAkBT,YAlBS;AA5Cf,IAAM,eAAe,cAA6C,MAAS;AAEpE,SAAS,WAAW;AACzB,QAAM,UAAU,WAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,SAAO;AACT;AAEO,SAAS,cAAc;AAAA,EAC5B;AAAA,EACA;AACF,GAGG;AACD,QAAM,CAAC,QAAQ,SAAS,IAAI,SAAkB,CAAC,CAAC;AAChD,QAAM,WAAW;AAAA,IACf,CAAC,UAAkC;AAvCvC;AAyCM,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AAEA,YAAM,MAAK,WAAM,OAAN,YAAY,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,CAAC;AAEhE,gBAAU,CAAC,kBAAkB;AAC3B,YAAI,cAAc,KAAK,CAACC,WAAUA,OAAM,OAAO,EAAE;AAAG,iBAAO;AAC3D,eAAO,CAAC,GAAG,eAAe,iCAAK,QAAL,EAAY,GAAG,EAAC;AAAA,MAC5C,CAAC;AAED,UAAI,MAAM,UAAU;AAClB,mBAAW,MAAM;AACf,sBAAY,EAAE;AAAA,QAChB,GAAG,MAAM,QAAQ;AAAA,MACnB;AAAA,IACF;AAAA,IACA,CAAC,OAAO;AAAA,EACV;AAEA,QAAM,wBAAwB,YAAY,CAAC,WAA2B;AACpE,aAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,oBAAC,cAAW,QAAgB;AAAA,IACvC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AAEL,QAAM,cAAc,YAAY,CAAC,OAAe;AAC9C,cAAU,CAAC,kBAAkB,cAAc,OAAO,CAAC,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,EAC/E,GAAG,CAAC,CAAC;AAEL,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,SACE,qBAAC,aAAa,UAAb,EAAsB,OACrB;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,eAAe;AAAA,UACf,KAAK;AAAA,QACP;AAAA,QAEC;AAAA,iBAAO,SAAS,KACf,oBAAC,SAAI,OAAO,EAAE,WAAW,QAAQ,GAC/B;AAAA,YAAC;AAAA;AAAA,cACC,SAAS,MAAM,UAAU,CAAC,CAAC;AAAA,cAC3B,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,QAAQ;AAAA,gBACR,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,cAAc;AAAA,cAChB;AAAA,cACD;AAAA;AAAA,UAED,GACF;AAAA,UAED,OAAO,IAAI,CAAC,UACX;AAAA,YAAC;AAAA;AAAA,cAEC,SAAS,MAAM;AAAA,cACf,MAAM,MAAM;AAAA,cACZ,SAAS,MAAM,YAAY,MAAM,EAAE;AAAA;AAAA,YAH9B,MAAM;AAAA,UAIb,CACD;AAAA;AAAA;AAAA,IACH;AAAA,IACC;AAAA,KACH;AAEJ;AAEA,SAAS,MAAM;AAAA,EACb;AAAA,EACA,OAAO;AAAA,EACP;AACF,GAIG;AACD,QAAM,WAAW;AAAA,IACf,MAAM;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAEA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,iBAAiB,SAAS,IAAI;AAAA,QAC9B,OAAO;AAAA,QACP,SAAS;AAAA,QACT,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MAEA;AAAA,4BAAC,SAAK,mBAAQ;AAAA,QACd;AAAA,UAAC;AAAA;AAAA,YACC,SAAS;AAAA,YACT,OAAO;AAAA,cACL,UAAU;AAAA,cACV,KAAK;AAAA,cACL,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,UAAU;AAAA,YACZ;AAAA,YACD;AAAA;AAAA,QAED;AAAA;AAAA;AAAA,EACF;AAEJ;;;ADxKA,OAAO,mBAAmB;AAsBlB,gBAAAC,MAGE,QAAAC,aAHF;AAfD,SAAS,WAAW,EAAE,OAAO,GAAyC;AAC3E,QAAM,iBAAiB,OAAO,IAAI,CAAC,OAAO,QAAQ;AAZpD;AAaI,UAAM,gBACJ,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,gBAA8C,CAAC;AAC5F,UAAM,WAAU,oDAAe,YAAf,YAA0B,MAAM;AAChD,UAAM,OAAO,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,OAAkB;AAE1E,WACE,gBAAAA;AAAA,MAAC;AAAA;AAAA,QAEC,OAAO;AAAA,UACL,WAAW,QAAQ,IAAI,IAAI;AAAA,UAC3B,cAAc;AAAA,QAChB;AAAA,QAEA;AAAA,0BAAAD,KAAC,uBAAoB,OAAO,EAAE,cAAc,EAAE,GAAG;AAAA,UAEhD,QACC,gBAAAC;AAAA,YAAC;AAAA;AAAA,cACC,OAAO;AAAA,gBACL,YAAY;AAAA,gBACZ,cAAc;AAAA,cAChB;AAAA,cACD;AAAA;AAAA,gBACsB;AAAA,gBACrB,gBAAAD,KAAC,UAAK,OAAO,EAAE,YAAY,aAAa,YAAY,SAAS,GAAI,gBAAK;AAAA;AAAA;AAAA,UACxE;AAAA,UAEF,gBAAAA,KAAC,iBAAe,mBAAQ;AAAA;AAAA;AAAA,MAnBnB;AAAA,IAoBP;AAAA,EAEJ,CAAC;AACD,SACE,gBAAAC;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MAEC;AAAA;AAAA,QACD,gBAAAD,KAAC,SAAI,OAAO,EAAE,UAAU,QAAQ,SAAS,KAAK,GAAG,sEAEjD;AAAA;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,gBAAgB;AAC9B,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,SAAOE;AAAA,IACL,CAAC,UAAoC;AACnC,YAAM,UAAU,MACb,IAAI,CAAC,QAAQ;AAhEtB;AAiEU,cAAM,UACJ,gBAAgB,QACX,eAAI,eAAJ,mBAAgB,kBAAhB,mBAAuC,YAAW,IAAI,UACvD,IAAI;AACV,cAAM,QAAQ,IAAI,SAAS;AAC3B,eAAO,KAAK,UAAU,KAAK,EAAE,MAAM,GAAG,EAAE;AAAA,MAC1C,CAAC,EACA,KAAK,GAAG;AAEX,eAAS;AAAA,QACP,MAAM;AAAA,QACN,IAAI;AAAA;AAAA,QACJ,SAAS,gBAAAF,KAAC,cAAW,QAAQ,OAAO;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACF;AAEO,SAAS,iBACd,UACA,MACA;AACA,QAAM,gBAAgB,cAAc;AACpC,SAAOE,aAAY,IAAU,SAAwB;AACnD,QAAI;AACF,aAAO,MAAM,SAAS,GAAG,IAAI;AAAA,IAC/B,SAAS,OAAP;AACA,cAAQ,MAAM,4BAA4B,KAAK;AAE/C,oBAAc,CAAC,KAAK,CAAC;AACrB,YAAM;AAAA,IACR;AAAA,EACF,IAAG,IAAI;AACT;", "names": ["useCallback", "toast", "jsx", "jsxs", "useCallback"]}