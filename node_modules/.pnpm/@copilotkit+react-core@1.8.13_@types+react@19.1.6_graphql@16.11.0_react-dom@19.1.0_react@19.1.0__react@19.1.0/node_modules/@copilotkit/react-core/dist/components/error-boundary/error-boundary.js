"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/components/error-boundary/error-boundary.tsx
var error_boundary_exports = {};
__export(error_boundary_exports, {
  CopilotErrorBoundary: () => CopilotErrorBoundary,
  ErrorToast: () => ErrorToast2
});
module.exports = __toCommonJS(error_boundary_exports);
var import_react3 = __toESM(require("react"));
var import_shared3 = require("@copilotkit/shared");

// src/lib/status-checker.ts
var import_shared = require("@copilotkit/shared");
var STATUS_CHECK_INTERVAL = 1e3 * 60 * 5;
var StatusChecker = class {
  constructor() {
    this.activeKey = null;
    this.intervalId = null;
    this.instanceCount = 0;
    this.lastResponse = null;
  }
  start(publicApiKey, onUpdate) {
    return __async(this, null, function* () {
      this.instanceCount++;
      if (this.activeKey === publicApiKey)
        return;
      if (this.intervalId)
        clearInterval(this.intervalId);
      const checkStatus = () => __async(this, null, function* () {
        try {
          const response = yield fetch(`${import_shared.COPILOT_CLOUD_API_URL}/ciu`, {
            method: "GET",
            headers: {
              [import_shared.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey
            }
          }).then((response2) => response2.json());
          this.lastResponse = response;
          onUpdate == null ? void 0 : onUpdate(response);
          return response;
        } catch (error) {
          return null;
        }
      });
      const initialResponse = yield checkStatus();
      this.intervalId = setInterval(checkStatus, STATUS_CHECK_INTERVAL);
      this.activeKey = publicApiKey;
      return initialResponse;
    });
  }
  getLastResponse() {
    return this.lastResponse;
  }
  stop() {
    this.instanceCount--;
    if (this.instanceCount === 0) {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        this.activeKey = null;
        this.lastResponse = null;
      }
    }
  }
};

// src/components/usage-banner.tsx
var import_shared2 = require("@copilotkit/shared");
var import_jsx_runtime = require("react/jsx-runtime");
var defaultIcons = {
  [import_shared2.Severity.Error]: /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(
    "svg",
    {
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      stroke: "currentColor",
      strokeWidth: "2",
      fill: "none",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime.jsx)("circle", { cx: "12", cy: "12", r: "10" }),
        /* @__PURE__ */ (0, import_jsx_runtime.jsx)("line", { x1: "15", y1: "9", x2: "9", y2: "15" }),
        /* @__PURE__ */ (0, import_jsx_runtime.jsx)("line", { x1: "9", y1: "9", x2: "15", y2: "15" })
      ]
    }
  )
};
function UsageBanner({
  severity = import_shared2.Severity.Error,
  message = "",
  icon,
  actions
}) {
  if (!message || !severity) {
    return null;
  }
  const Icon = icon || defaultIcons[severity];
  const bgColor = {
    info: "#dbeafe",
    warning: "#fef3c7",
    error: "#fee2e2"
  }[severity];
  const textColor = {
    info: "#1e40af",
    warning: "#854d0e",
    error: "#991b1b"
  }[severity];
  const iconColor = {
    info: "#3b82f6",
    warning: "#eab308",
    error: "#ef4444"
  }[severity];
  const primaryButtonColor = {
    info: "#3b82f6",
    warning: "#eab308",
    error: "#ef4444"
  }[severity];
  const primaryButtonHoverColor = {
    info: "#2563eb",
    warning: "#ca8a04",
    error: "#dc2626"
  }[severity];
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
    "div",
    {
      style: {
        position: "fixed",
        bottom: "16px",
        left: "50%",
        transform: "translateX(-50%)",
        maxWidth: "90%",
        zIndex: 9999
      },
      children: /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(
        "div",
        {
          style: {
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            gap: "12px",
            borderRadius: "9999px",
            border: "1px solid #e5e7eb",
            backgroundColor: bgColor,
            padding: "8px 16px",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
          },
          children: [
            /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { style: { color: iconColor }, children: Icon }),
            /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
              "span",
              {
                style: {
                  flex: 1,
                  fontSize: "14px",
                  fontWeight: 500,
                  color: textColor,
                  whiteSpace: "normal",
                  wordBreak: "break-word"
                },
                children: message
              }
            ),
            /* @__PURE__ */ (0, import_jsx_runtime.jsxs)(
              "div",
              {
                style: {
                  display: "flex",
                  gap: "8px",
                  flexWrap: "wrap"
                },
                children: [
                  (actions == null ? void 0 : actions.secondary) && /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
                    "button",
                    {
                      onClick: actions.secondary.onClick,
                      style: {
                        borderRadius: "9999px",
                        padding: "4px 12px",
                        fontSize: "14px",
                        fontWeight: 500,
                        color: textColor,
                        backgroundColor: "transparent",
                        border: "none",
                        cursor: "pointer",
                        transition: "background-color 0.2s"
                      },
                      onMouseOver: (e) => e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.5)",
                      onMouseOut: (e) => e.currentTarget.style.backgroundColor = "transparent",
                      children: actions.secondary.label
                    }
                  ),
                  (actions == null ? void 0 : actions.primary) && /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
                    "button",
                    {
                      onClick: actions.primary.onClick,
                      style: {
                        borderRadius: "9999px",
                        padding: "4px 12px",
                        fontSize: "14px",
                        fontWeight: 500,
                        color: "#fff",
                        backgroundColor: primaryButtonColor,
                        border: "none",
                        cursor: "pointer",
                        transition: "background-color 0.2s"
                      },
                      onMouseOver: (e) => e.currentTarget.style.backgroundColor = primaryButtonHoverColor,
                      onMouseOut: (e) => e.currentTarget.style.backgroundColor = primaryButtonColor,
                      children: actions.primary.label
                    }
                  )
                ]
              }
            )
          ]
        }
      )
    }
  );
}
function renderCopilotKitUsage(error) {
  switch (error.name) {
    case import_shared2.ERROR_NAMES.CONFIGURATION_ERROR:
      return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(UsageBanner, { severity: error.severity, message: error.message });
    case import_shared2.ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR:
      return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
        UsageBanner,
        {
          severity: error.severity,
          message: error.message,
          actions: {
            primary: {
              label: "Sign In",
              onClick: () => {
                window.location.href = "https://cloud.copilotkit.ai";
              }
            }
          }
        }
      );
    case import_shared2.ERROR_NAMES.UPGRADE_REQUIRED_ERROR:
      return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
        UsageBanner,
        {
          severity: error.severity,
          message: error.message,
          actions: {
            primary: {
              label: "Upgrade",
              onClick: () => {
                window.location.href = "https://copilotkit.ai/";
              }
            }
          }
        }
      );
  }
}

// src/components/error-boundary/error-utils.tsx
var import_react2 = require("react");

// src/components/toast/toast-provider.tsx
var import_react = require("react");
var import_jsx_runtime2 = require("react/jsx-runtime");
var ToastContext = (0, import_react.createContext)(void 0);
function useToast() {
  const context = (0, import_react.useContext)(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}

// src/components/toast/exclamation-mark-icon.tsx
var import_jsx_runtime3 = require("react/jsx-runtime");
var ExclamationMarkIcon = ({
  className,
  style
}) => /* @__PURE__ */ (0, import_jsx_runtime3.jsxs)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    className: `lucide lucide-circle-alert ${className ? className : ""}`,
    style,
    children: [
      /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("circle", { cx: "12", cy: "12", r: "10" }),
      /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("line", { x1: "12", x2: "12", y1: "8", y2: "12" }),
      /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("line", { x1: "12", x2: "12.01", y1: "16", y2: "16" })
    ]
  }
);

// src/components/error-boundary/error-utils.tsx
var import_react_markdown = __toESM(require("react-markdown"));
var import_jsx_runtime4 = require("react/jsx-runtime");
function ErrorToast({ errors }) {
  const errorsToRender = errors.map((error, idx) => {
    var _a, _b, _c;
    const originalError = "extensions" in error ? (_a = error.extensions) == null ? void 0 : _a.originalError : {};
    const message = (_b = originalError == null ? void 0 : originalError.message) != null ? _b : error.message;
    const code = "extensions" in error ? (_c = error.extensions) == null ? void 0 : _c.code : null;
    return /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(
      "div",
      {
        style: {
          marginTop: idx === 0 ? 0 : 10,
          marginBottom: 14
        },
        children: [
          /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(ExclamationMarkIcon, { style: { marginBottom: 4 } }),
          code && /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(
            "div",
            {
              style: {
                fontWeight: "600",
                marginBottom: 4
              },
              children: [
                "Copilot Cloud Error:",
                " ",
                /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("span", { style: { fontFamily: "monospace", fontWeight: "normal" }, children: code })
              ]
            }
          ),
          /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(import_react_markdown.default, { children: message })
        ]
      },
      idx
    );
  });
  return /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(
    "div",
    {
      style: {
        fontSize: "13px",
        maxWidth: "600px"
      },
      children: [
        errorsToRender,
        /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { style: { fontSize: "11px", opacity: 0.75 }, children: "NOTE: This error only displays during local development." })
      ]
    }
  );
}
function useErrorToast() {
  const { addToast } = useToast();
  return (0, import_react2.useCallback)(
    (error) => {
      const errorId = error.map((err) => {
        var _a, _b;
        const message = "extensions" in err ? ((_b = (_a = err.extensions) == null ? void 0 : _a.originalError) == null ? void 0 : _b.message) || err.message : err.message;
        const stack = err.stack || "";
        return btoa(message + stack).slice(0, 32);
      }).join("|");
      addToast({
        type: "error",
        id: errorId,
        // Toast libraries typically dedupe by id
        message: /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(ErrorToast, { errors: error })
      });
    },
    [addToast]
  );
}

// src/components/error-boundary/error-boundary.tsx
var import_shared4 = require("@copilotkit/shared");
var import_jsx_runtime5 = require("react/jsx-runtime");
var statusChecker = new StatusChecker();
var CopilotErrorBoundary = class extends import_react3.default.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false
    };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  componentDidMount() {
    if (this.props.publicApiKey) {
      statusChecker.start(this.props.publicApiKey, (newStatus) => {
        this.setState((prevState) => {
          var _a;
          if ((newStatus == null ? void 0 : newStatus.severity) !== ((_a = prevState.status) == null ? void 0 : _a.severity)) {
            return { status: newStatus != null ? newStatus : void 0 };
          }
          return null;
        });
      });
    }
  }
  componentWillUnmount() {
    statusChecker.stop();
  }
  componentDidCatch(error, errorInfo) {
    console.error("CopilotKit Error:", error, errorInfo);
  }
  render() {
    var _a, _b;
    if (this.state.hasError) {
      if (this.state.error instanceof import_shared3.CopilotKitError) {
        if (import_shared4.COPILOT_CLOUD_ERROR_NAMES.includes(this.state.error.name)) {
          return /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(ErrorToast2, { error: this.state.error, children: renderCopilotKitUsage(this.state.error) });
        }
        return /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)(import_jsx_runtime5.Fragment, { children: [
          this.props.children,
          this.props.showUsageBanner && /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
            UsageBanner,
            {
              severity: (_a = this.state.status) == null ? void 0 : _a.severity,
              message: (_b = this.state.status) == null ? void 0 : _b.message
            }
          )
        ] });
      }
      throw this.state.error;
    }
    return this.props.children;
  }
};
function ErrorToast2({ error, children }) {
  const addErrorToast = useErrorToast();
  (0, import_react3.useEffect)(() => {
    if (error) {
      addErrorToast([error]);
    }
  }, [error, addErrorToast]);
  if (!error)
    throw error;
  return children;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CopilotErrorBoundary,
  ErrorToast
});
//# sourceMappingURL=error-boundary.js.map