{"version": 3, "sources": ["../src/hooks/use-make-copilot-document-readable.ts"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\nimport { useCopilotContext } from \"../context/copilot-context\";\nimport { DocumentPointer } from \"../types\";\n\n/**\n * Makes a document readable by Copilot.\n * @param document The document to make readable.\n * @param categories The categories to associate with the document.\n * @param dependencies The dependencies to use for the effect.\n * @returns The id of the document.\n */\nexport function useMakeCopilotDocumentReadable(\n  document: DocumentPointer,\n  categories?: string[],\n  dependencies: any[] = [],\n): string | undefined {\n  const { addDocumentContext, removeDocumentContext } = useCopilotContext();\n  const idRef = useRef<string>();\n\n  useEffect(() => {\n    const id = addDocumentContext(document, categories);\n    idRef.current = id;\n\n    return () => {\n      removeDocumentContext(id);\n    };\n  }, [addDocumentContext, removeDocumentContext, ...dependencies]);\n\n  return idRef.current;\n}\n"], "mappings": ";;;;;AAAA,SAAS,WAAW,cAAc;AAW3B,SAAS,+BACd,UACA,YACA,eAAsB,CAAC,GACH;AACpB,QAAM,EAAE,oBAAoB,sBAAsB,IAAI,kBAAkB;AACxE,QAAM,QAAQ,OAAe;AAE7B,YAAU,MAAM;AACd,UAAM,KAAK,mBAAmB,UAAU,UAAU;AAClD,UAAM,UAAU;AAEhB,WAAO,MAAM;AACX,4BAAsB,EAAE;AAAA,IAC1B;AAAA,EACF,GAAG,CAAC,oBAAoB,uBAAuB,GAAG,YAAY,CAAC;AAE/D,SAAO,MAAM;AACf;", "names": []}