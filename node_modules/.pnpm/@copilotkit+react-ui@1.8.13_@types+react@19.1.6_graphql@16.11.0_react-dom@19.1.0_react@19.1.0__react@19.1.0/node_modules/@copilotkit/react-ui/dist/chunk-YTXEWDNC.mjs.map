{"version": 3, "sources": ["../src/components/chat/Markdown.tsx"], "sourcesContent": ["import { FC, memo } from \"react\";\nimport ReactMarkdown, { Options, Components } from \"react-markdown\";\nimport { CodeBlock } from \"./CodeBlock\";\nimport remarkGfm from \"remark-gfm\";\nimport remarkMath from \"remark-math\";\nimport rehypeRaw from \"rehype-raw\";\n\nconst defaultComponents: Components = {\n  a({ children, ...props }) {\n    return (\n      <a className=\"copilotKitMarkdownElement\" {...props} target=\"_blank\" rel=\"noopener noreferrer\">\n        {children}\n      </a>\n    );\n  },\n  // @ts-expect-error -- inline\n  code({ children, className, inline, ...props }) {\n    if (Array.isArray(children) && children.length) {\n      if (children[0] == \"▍\") {\n        return (\n          <span\n            style={{\n              animation: \"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite\",\n              marginTop: \"0.25rem\",\n            }}\n          >\n            ▍\n          </span>\n        );\n      }\n\n      children[0] = (children?.[0] as string).replace(\"`▍`\", \"▍\");\n    }\n\n    const match = /language-(\\w+)/.exec(className || \"\");\n\n    if (inline) {\n      return (\n        <code className={className} {...props}>\n          {children}\n        </code>\n      );\n    }\n\n    return (\n      <CodeBlock\n        key={Math.random()}\n        language={(match && match[1]) || \"\"}\n        value={String(children).replace(/\\n$/, \"\")}\n        {...props}\n      />\n    );\n  },\n  h1: ({ children, ...props }) => (\n    <h1 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h1>\n  ),\n  h2: ({ children, ...props }) => (\n    <h2 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h2>\n  ),\n  h3: ({ children, ...props }) => (\n    <h3 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h3>\n  ),\n  h4: ({ children, ...props }) => (\n    <h4 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h4>\n  ),\n  h5: ({ children, ...props }) => (\n    <h5 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h5>\n  ),\n  h6: ({ children, ...props }) => (\n    <h6 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h6>\n  ),\n  p: ({ children, ...props }) => (\n    <p className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </p>\n  ),\n  pre: ({ children, ...props }) => (\n    <pre className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </pre>\n  ),\n  blockquote: ({ children, ...props }) => (\n    <blockquote className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </blockquote>\n  ),\n  ul: ({ children, ...props }) => (\n    <ul className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </ul>\n  ),\n  li: ({ children, ...props }) => (\n    <li className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </li>\n  ),\n};\n\nconst MemoizedReactMarkdown: FC<Options> = memo(\n  ReactMarkdown,\n  (prevProps, nextProps) =>\n    prevProps.children === nextProps.children && prevProps.components === nextProps.components,\n);\n\ntype MarkdownProps = {\n  content: string;\n  components?: Components;\n};\n\nexport const Markdown = ({ content, components }: MarkdownProps) => {\n  return (\n    <div className=\"copilotKitMarkdown\">\n      <MemoizedReactMarkdown\n        components={{ ...defaultComponents, ...components }}\n        remarkPlugins={[remarkGfm, remarkMath]}\n        rehypePlugins={[rehypeRaw]}\n      >\n        {content}\n      </MemoizedReactMarkdown>\n    </div>\n  );\n};\n"], "mappings": ";;;;;;;;;;AAAA,SAAa,YAAY;AACzB,OAAO,mBAA4C;AAEnD,OAAO,eAAe;AACtB,OAAO,gBAAgB;AACvB,OAAO,eAAe;AAKhB;AAHN,IAAM,oBAAgC;AAAA,EACpC,EAAE,IAAwB;AAAxB,iBAAE,WARN,IAQI,IAAe,kBAAf,IAAe,CAAb;AACF,WACE,oBAAC,oCAAE,WAAU,+BAAgC,QAA5C,EAAmD,QAAO,UAAS,KAAI,uBACrE,WACH;AAAA,EAEJ;AAAA;AAAA,EAEA,KAAK,IAA2C;AAA3C,iBAAE,YAAU,WAAW,OAhB9B,IAgBO,IAAkC,kBAAlC,IAAkC,CAAhC,YAAU,aAAW;AAC1B,QAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AAC9C,UAAI,SAAS,CAAC,KAAK,UAAK;AACtB,eACE;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,cACL,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,YACD;AAAA;AAAA,QAED;AAAA,MAEJ;AAEA,eAAS,CAAC,KAAK,qCAAW,IAAc,QAAQ,YAAO,QAAG;AAAA,IAC5D;AAEA,UAAM,QAAQ,iBAAiB,KAAK,aAAa,EAAE;AAEnD,QAAI,QAAQ;AACV,aACE,oBAAC,uCAAK,aAA0B,QAA/B,EACE,WACH;AAAA,IAEJ;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QAEC,UAAW,SAAS,MAAM,CAAC,KAAM;AAAA,QACjC,OAAO,OAAO,QAAQ,EAAE,QAAQ,OAAO,EAAE;AAAA,SACrC;AAAA,MAHC,KAAK,OAAO;AAAA,IAInB;AAAA,EAEJ;AAAA,EACA,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WArDT,IAqDO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA1DT,IA0DO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA/DT,IA+DO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WApET,IAoEO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAzET,IAyEO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA9ET,IA8EO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,GAAG,CAAC,OAAwB;AAAxB,iBAAE,WAnFR,IAmFM,IAAe,kBAAf,IAAe,CAAb;AACJ,+BAAC,oCAAE,WAAU,+BAAgC,QAA5C,EACE,WACH;AAAA;AAAA,EAEF,KAAK,CAAC,OAAwB;AAAxB,iBAAE,WAxFV,IAwFQ,IAAe,kBAAf,IAAe,CAAb;AACN,+BAAC,sCAAI,WAAU,+BAAgC,QAA9C,EACE,WACH;AAAA;AAAA,EAEF,YAAY,CAAC,OAAwB;AAAxB,iBAAE,WA7FjB,IA6Fe,IAAe,kBAAf,IAAe,CAAb;AACb,+BAAC,6CAAW,WAAU,+BAAgC,QAArD,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAlGT,IAkGO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAvGT,IAuGO,IAAe,kBAAf,IAAe,CAAb;AACL,+BAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAEJ;AAEA,IAAM,wBAAqC;AAAA,EACzC;AAAA,EACA,CAAC,WAAW,cACV,UAAU,aAAa,UAAU,YAAY,UAAU,eAAe,UAAU;AACpF;AAOO,IAAM,WAAW,CAAC,EAAE,SAAS,WAAW,MAAqB;AAClE,SACE,oBAAC,SAAI,WAAU,sBACb;AAAA,IAAC;AAAA;AAAA,MACC,YAAY,kCAAK,oBAAsB;AAAA,MACvC,eAAe,CAAC,WAAW,UAAU;AAAA,MACrC,eAAe,CAAC,SAAS;AAAA,MAExB;AAAA;AAAA,EACH,GACF;AAEJ;", "names": []}