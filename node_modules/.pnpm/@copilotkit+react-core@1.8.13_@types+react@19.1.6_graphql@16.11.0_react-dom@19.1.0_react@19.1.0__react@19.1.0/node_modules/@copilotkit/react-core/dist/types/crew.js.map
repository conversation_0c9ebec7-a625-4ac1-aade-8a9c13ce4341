{"version": 3, "sources": ["../../src/types/crew.ts"], "sourcesContent": ["/**\n * Status of a response or action that requires user input\n */\nexport type CrewsResponseStatus = \"inProgress\" | \"complete\" | \"executing\";\n\n/**\n * Response data structure for the ResponseRenderer\n */\nexport interface CrewsResponse {\n  /**\n   * Unique identifier for the response\n   */\n  id: string;\n\n  /**\n   * The content of the response to display\n   */\n  content: string;\n\n  /**\n   * Optional metadata for the response\n   */\n  metadata?: Record<string, any>;\n}\n\n/**\n * Base state item interface for agent state items\n */\nexport interface CrewsStateItem {\n  /**\n   * Unique identifier for the item\n   */\n  id: string;\n\n  /**\n   * Timestamp when the item was created\n   */\n  timestamp: string;\n}\n\n/**\n * Tool execution state item\n */\nexport interface CrewsToolStateItem extends CrewsStateItem {\n  /**\n   * Name of the tool that was executed\n   */\n  tool: string;\n\n  /**\n   * Optional thought process for the tool execution\n   */\n  thought?: string;\n\n  /**\n   * Result of the tool execution\n   */\n  result?: any;\n}\n\n/**\n * Task state item\n */\nexport interface CrewsTaskStateItem extends CrewsStateItem {\n  /**\n   * Name of the task\n   */\n  name: string;\n\n  /**\n   * Description of the task\n   */\n  description?: string;\n}\n\n/**\n * AgentState containing information about steps and tasks\n */\nexport interface CrewsAgentState {\n  /**\n   * Array of tool execution steps\n   */\n  steps?: CrewsToolStateItem[];\n\n  /**\n   * Array of tasks\n   */\n  tasks?: CrewsTaskStateItem[];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}