"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/chat/messages/UserMessage.tsx
var UserMessage_exports = {};
__export(UserMessage_exports, {
  UserMessage: () => UserMessage
});
module.exports = __toCommonJS(UserMessage_exports);
var import_jsx_runtime = require("react/jsx-runtime");
var UserMessage = (props) => {
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { className: "copilotKitMessage copilotKitUserMessage", children: props.subComponent || props.message });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  UserMessage
});
//# sourceMappingURL=UserMessage.js.map