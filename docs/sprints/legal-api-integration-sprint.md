# Legal-API Integration Sprint

## 🎯 Goal

Migrate the front-end & LangGraph agents to use the new laws-API service for all legal-corpus queries, retire any browser-side Pinecone/Neo4j calls, and keep role/tenant security intact.

## Base Facts

- **LAWS_API_BASE**: `https://legal-api-stg-gfunh6mfpa-uc.a.run.app`
- **AUTH_HEADER**: `Authorization: Bearer <Supabase JWT>`
- **RATE LIMIT**: 100 req/min/user (Retry-After header enforced)
- **Endpoints**: `/v0/search`, `/v0/recommend`, `/v0/graph`

### JWT Claims Structure
JWT already contains: `sub`, `email`, `tenant_id`, `role`
- **TODO**: Add `jurisdictions` if not yet there

## 1. Repo Tasks (Frontend + Backend)

| Task | Folder / File Hints | Status |
|------|-------------------|--------|
| **Replace direct legal-search calls** with fetches to `/v0/search` | `frontend/src/lib/pinecone/*`, `hooks/useLegalSearch.ts` | 🔄 |
| **Create shared types** (`SearchResult`, `GraphNode`) in `frontend/src/types/laws-api.ts` | Use OpenAPI generator or hand-write | 🔄 |
| **LangGraph tool wrappers** – add `legal_search`, `legal_recommend`, `legal_graph` | `frontend/src/lib/langgraph/tools/` | 🔄 |
| **Auth hook** – ensure `useAuthenticatedFetch` injects JWT for laws-API domain | `frontend/src/hooks/useAuthenticatedFetch.ts` | 🔄 |
| **Error handling** – surface `429` with retry timer; show toast if JWT expired (401) | Global Axios/SWR interceptor | 🔄 |
| **Remove browser Pinecone/Neo4j bundles** – drop unused deps to cut bundle size | `package.json`, tree-shake check | 🔄 |

## 2. Backend Tasks (Core FastAPI)

| Task | Folder | Status |
|------|--------|--------|
| **Agent update** – ResearchCopilot now calls laws-API search & recommend | `backend/agents/interactive/research/*` | 🔄 |
| **Tenant data merge** – Keep internal DB calls for case/task data; merge with laws-API results | `services/insight_service.py` | 🔄 |
| **Shared RBAC module** – import `rbac.py` (same as laws-API) for consistent role hierarchy | `backend/middleware/auth_middleware.py` | 🔄 |

## 3. Deliverables

### PR #1 – Frontend Migration
- [ ] New `laws-api.ts` client
- [ ] Updated search page & LangGraph tools
- [ ] Bundle analysis screenshot (size reduced)

### PR #2 – Agent Update
- [ ] ResearchCopilot uses `/v0/search` & `/v0/recommend`
- [ ] Unit test with mocked responses

### Documentation
- [ ] `docs/integration/laws-api.md` explaining base URL, rate limits, error codes
- [ ] `.env.example` updated (`LAWS_API_BASE`)

## 4. Open Questions

**Please answer in kickoff comment:**

1. Does JWT already include `jurisdictions`? If not, do we need it for FE filtering?
2. Any components besides ResearchCopilot that require raw Pinecone vectors?
3. Confirm we can delete Pinecone keys from browser env after migration.

## Success Criteria

✅ **When complete:**
- [ ] FE & agents use only HTTPS calls to laws-API for legal data
- [ ] No 401/403/429 errors in normal flow
- [ ] Bundle size drops; no Pinecone/Neo4j client in `npm run analyze`
- [ ] QA confirms p95 page load < 1.2s on search pages

## Implementation Plan

### Phase 1: Frontend Migration (Days 1-3)
1. Create laws-API client and types
2. Update search components to use new API
3. Modify authentication hooks
4. Remove direct Pinecone/Neo4j dependencies

### Phase 2: Backend Integration (Days 4-5)
1. Update ResearchCopilot agent
2. Implement tenant data merging
3. Add shared RBAC module

### Phase 3: Testing & Optimization (Days 6-7)
1. Comprehensive testing with mocked responses
2. Bundle size analysis and optimization
3. Performance testing and QA validation

## Risk Mitigation

- **Rate Limiting**: Implement exponential backoff with Retry-After header respect
- **Auth Failures**: Graceful degradation with clear user messaging
- **Performance**: Monitor bundle size and page load metrics
- **Rollback Plan**: Feature flags to switch back to direct calls if needed

## Branch Information

- **Branch**: `feature/legal-api-integration-sprint`
- **Base**: `develop`
- **Target**: `develop`

## Related Documentation

- [Core AiLex Architecture](../README-AiLex-Architecture.md)
- [Authentication Architecture](../authentication-architecture.md)
- [LangGraph Integration](../LangGraph-Implementation-Details.md)
