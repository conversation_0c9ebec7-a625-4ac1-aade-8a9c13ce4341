'use client';

import { useState, useEffect } from 'react';
import { useAuthenticatedFetch } from './useAuthenticatedFetch';
import { useAuth } from '@/lib/auth/useAuth';

/**
 * Hook to check tenant feature access based on subscription
 */
export function useFeatures() {
  const [features, setFeatures] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authedFetch, isReady } = useAuthenticatedFetch();
  const { profile } = useAuth();

  useEffect(() => {
    if (!isReady || !profile?.tenant_id) {
      return;
    }

    const fetchFeatures = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Call the backend API to get tenant features
        const response = await authedFetch<{ features: string[] }>(
          `/api/subscription/${profile.tenant_id}/features`
        );
        
        setFeatures(response.features || []);
      } catch (err) {
        console.error('Error fetching features:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch features');
        setFeatures([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeatures();
  }, [isReady, profile?.tenant_id, authedFetch]);

  /**
   * Check if a specific feature is enabled for the current tenant
   */
  const hasFeature = (featureKey: string): boolean => {
    return features.includes(featureKey);
  };

  /**
   * Check if multiple features are enabled
   */
  const hasAllFeatures = (featureKeys: string[]): boolean => {
    return featureKeys.every(key => features.includes(key));
  };

  /**
   * Check if any of the provided features are enabled
   */
  const hasAnyFeature = (featureKeys: string[]): boolean => {
    return featureKeys.some(key => features.includes(key));
  };

  return {
    features,
    isLoading,
    error,
    hasFeature,
    hasAllFeatures,
    hasAnyFeature,
  };
}
