import * as react_jsx_runtime from 'react/jsx-runtime';
import { useCallback } from 'react';
import { GraphQLError } from '@copilotkit/runtime-client-gql';

declare function ErrorToast({ errors }: {
    errors: (Error | GraphQLError)[];
}): react_jsx_runtime.JSX.Element;
declare function useErrorToast(): (error: (Error | GraphQLError)[]) => void;
declare function useAsyncCallback<T extends (...args: any[]) => Promise<any>>(callback: T, deps: Parameters<typeof useCallback>[1]): (...args: Parameters<T>) => Promise<any>;

export { ErrorToast, useAsyncCallback, useErrorToast };
