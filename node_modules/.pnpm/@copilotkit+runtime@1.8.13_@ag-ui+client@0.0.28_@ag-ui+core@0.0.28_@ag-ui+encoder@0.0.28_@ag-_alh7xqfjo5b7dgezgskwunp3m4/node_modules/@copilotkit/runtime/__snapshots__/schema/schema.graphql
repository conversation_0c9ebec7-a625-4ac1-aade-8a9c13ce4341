# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

input ActionExecutionMessageInput {
  arguments: String!
  name: String!
  parentMessageId: String
  scope: String @deprecated(reason: "This field will be removed in a future version")
}

type ActionExecutionMessageOutput implements BaseMessageOutput {
  arguments: [String!]!
  createdAt: DateTimeISO!
  id: String!
  name: String!
  parentMessageId: String
  scope: String @deprecated(reason: "This field will be removed in a future version")
  status: MessageStatus!
}

input ActionInput {
  available: ActionInputAvailability
  description: String!
  jsonSchema: String!
  name: String!
}

"""The availability of the frontend action"""
enum ActionInputAvailability {
  disabled
  enabled
  remote
}

type Agent {
  description: String!
  id: String!
  name: String!
}

input AgentSessionInput {
  agentName: String!
  nodeName: String
  threadId: String
}

input AgentStateInput {
  agentName: String!
  config: String
  state: String!
}

input AgentStateMessageInput {
  active: Boolean!
  agentName: String!
  nodeName: String!
  role: MessageRole!
  runId: String!
  running: Boolean!
  state: String!
  threadId: String!
}

type AgentStateMessageOutput implements BaseMessageOutput {
  active: Boolean!
  agentName: String!
  createdAt: DateTimeISO!
  id: String!
  nodeName: String!
  role: MessageRole!
  runId: String!
  running: Boolean!
  state: String!
  status: MessageStatus!
  threadId: String!
}

type AgentsResponse {
  agents: [Agent!]!
}

interface BaseMessageOutput {
  createdAt: DateTimeISO!
  id: String!
  status: MessageStatus!
}

interface BaseMetaEvent {
  name: MetaEventName!
  type: String!
}

interface BaseResponseStatus {
  code: ResponseStatusCode!
}

input CloudInput {
  guardrails: GuardrailsInput
}

type CopilotKitLangGraphInterruptEvent implements BaseMetaEvent {
  data: CopilotKitLangGraphInterruptEventData!
  name: MetaEventName!
  response: String
  type: String!
}

type CopilotKitLangGraphInterruptEventData {
  messages: [BaseMessageOutput!]!
  value: String!
}

"""The type of Copilot request"""
enum CopilotRequestType {
  Chat
  Suggestion
  Task
  TextareaCompletion
  TextareaPopover
}

type CopilotResponse {
  extensions: ExtensionsResponse
  messages: [BaseMessageOutput!]!
  metaEvents: [BaseMetaEvent!]
  runId: String
  status: ResponseStatus!
  threadId: String!
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format.
"""
scalar DateTimeISO

input ExtensionsInput {
  openaiAssistantAPI: OpenAIApiAssistantAPIInput
}

type ExtensionsResponse {
  openaiAssistantAPI: OpenAIApiAssistantAPIResponse
}

type FailedMessageStatus {
  code: MessageStatusCode!
  reason: String!
}

type FailedResponseStatus implements BaseResponseStatus {
  code: ResponseStatusCode!
  details: JSON
  reason: FailedResponseStatusReason!
}

enum FailedResponseStatusReason {
  GUARDRAILS_VALIDATION_FAILED
  MESSAGE_STREAM_INTERRUPTED
  UNKNOWN_ERROR
}

input ForwardedParametersInput {
  maxTokens: Float
  model: String
  stop: [String!]
  temperature: Float
  toolChoice: String
  toolChoiceFunctionName: String
}

input FrontendInput {
  actions: [ActionInput!]!
  toDeprecate_fullContext: String
  url: String
}

input GenerateCopilotResponseInput {
  agentSession: AgentSessionInput
  agentState: AgentStateInput
  agentStates: [AgentStateInput!]
  cloud: CloudInput
  extensions: ExtensionsInput
  forwardedParameters: ForwardedParametersInput
  frontend: FrontendInput!
  messages: [MessageInput!]!
  metaEvents: [MetaEventInput!]
  metadata: GenerateCopilotResponseMetadataInput!
  runId: String
  threadId: String
}

input GenerateCopilotResponseMetadataInput {
  requestType: CopilotRequestType
}

input GuardrailsInput {
  inputValidationRules: GuardrailsRuleInput!
}

input GuardrailsRuleInput {
  allowList: [String!] = []
  denyList: [String!] = []
}

input ImageMessageInput {
  bytes: String!
  format: String!
  parentMessageId: String
  role: MessageRole!
}

type ImageMessageOutput implements BaseMessageOutput {
  bytes: String!
  createdAt: DateTimeISO!
  format: String!
  id: String!
  parentMessageId: String
  role: MessageRole!
  status: MessageStatus!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type LangGraphInterruptEvent implements BaseMetaEvent {
  name: MetaEventName!
  response: String
  type: String!
  value: String!
}

input LoadAgentStateInput {
  agentName: String!
  threadId: String!
}

type LoadAgentStateResponse {
  messages: String!
  state: String!
  threadExists: Boolean!
  threadId: String!
}

input MessageInput {
  actionExecutionMessage: ActionExecutionMessageInput
  agentStateMessage: AgentStateMessageInput
  createdAt: DateTimeISO!
  id: String!
  imageMessage: ImageMessageInput
  resultMessage: ResultMessageInput
  textMessage: TextMessageInput
}

"""The role of the message"""
enum MessageRole {
  assistant
  developer
  system
  tool
  user
}

union MessageStatus = FailedMessageStatus | PendingMessageStatus | SuccessMessageStatus

enum MessageStatusCode {
  Failed
  Pending
  Success
}

input MetaEventInput {
  messages: [MessageInput!]
  name: MetaEventName!
  response: String
  value: String!
}

"""Meta event types"""
enum MetaEventName {
  CopilotKitLangGraphInterruptEvent
  LangGraphInterruptEvent
}

type Mutation {
  generateCopilotResponse(data: GenerateCopilotResponseInput!, properties: JSONObject): CopilotResponse!
}

input OpenAIApiAssistantAPIInput {
  runId: String
  threadId: String
}

type OpenAIApiAssistantAPIResponse {
  runId: String
  threadId: String
}

type PendingMessageStatus {
  code: MessageStatusCode!
}

type PendingResponseStatus implements BaseResponseStatus {
  code: ResponseStatusCode!
}

type Query {
  availableAgents: AgentsResponse!
  hello: String!
  loadAgentState(data: LoadAgentStateInput!): LoadAgentStateResponse!
}

union ResponseStatus = FailedResponseStatus | PendingResponseStatus | SuccessResponseStatus

enum ResponseStatusCode {
  Failed
  Pending
  Success
}

input ResultMessageInput {
  actionExecutionId: String!
  actionName: String!
  parentMessageId: String
  result: String!
}

type ResultMessageOutput implements BaseMessageOutput {
  actionExecutionId: String!
  actionName: String!
  createdAt: DateTimeISO!
  id: String!
  result: String!
  status: MessageStatus!
}

type SuccessMessageStatus {
  code: MessageStatusCode!
}

type SuccessResponseStatus implements BaseResponseStatus {
  code: ResponseStatusCode!
}

input TextMessageInput {
  content: String!
  parentMessageId: String
  role: MessageRole!
}

type TextMessageOutput implements BaseMessageOutput {
  content: [String!]!
  createdAt: DateTimeISO!
  id: String!
  parentMessageId: String
  role: MessageRole!
  status: MessageStatus!
}