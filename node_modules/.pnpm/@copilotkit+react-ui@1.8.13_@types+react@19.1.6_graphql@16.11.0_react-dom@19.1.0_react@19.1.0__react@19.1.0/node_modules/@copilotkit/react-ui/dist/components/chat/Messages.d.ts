import * as React from 'react';
import * as react_jsx_runtime from 'react/jsx-runtime';
import { MessagesProps } from './props.js';
import '@copilotkit/runtime-client-gql';
import '../../types/suggestions.js';

declare const Messages: ({ messages, inProgress, children, RenderTextMessage, RenderActionExecutionMessage, RenderAgentStateMessage, RenderResultMessage, RenderImageMessage, AssistantMessage, UserMessage, onRegenerate, onCopy, onThumbsUp, onThumbsDown, markdownTagRenderers, }: MessagesProps) => react_jsx_runtime.JSX.Element;
declare function useScrollToBottom(messages: any[]): {
    messagesEndRef: React.RefObject<HTMLDivElement>;
    messagesContainerRef: React.MutableRefObject<HTMLDivElement | null>;
};

export { Messages, useScrollToBottom };
