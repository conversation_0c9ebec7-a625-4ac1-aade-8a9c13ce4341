"use strict";var ju=Object.create;var to=Object.defineProperty;var Ku=Object.getOwnPropertyDescriptor;var zu=Object.getOwnPropertyNames;var Xu=Object.getPrototypeOf,Yu=Object.prototype.hasOwnProperty;var qu=(e,n,t)=>n in e?to(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var Ju=(e,n)=>{for(var t in n)to(e,t,{get:n[t],enumerable:!0})},ps=(e,n,t,o)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of zu(n))!Yu.call(e,r)&&r!==t&&to(e,r,{get:()=>n[r],enumerable:!(o=Ku(n,r))||o.enumerable});return e};var le=(e,n,t)=>(t=e!=null?ju(<PERSON>(e)):{},ps(n||!e||!e.__esModule?to(t,"default",{value:e,enumerable:!0}):t,e)),Qu=e=>ps(to({},"__esModule",{value:!0}),e);var Oe=(e,n,t)=>(qu(e,typeof n!="symbol"?n+"":n,t),t),ms=(e,n,t)=>{if(!n.has(e))throw TypeError("Cannot "+t)};var ze=(e,n,t)=>(ms(e,n,"read from private field"),t?t.call(e):n.get(e)),Ho=(e,n,t)=>{if(n.has(e))throw TypeError("Cannot add the same private member more than once");n instanceof WeakSet?n.add(e):n.set(e,t)},Nr=(e,n,t,o)=>(ms(e,n,"write to private field"),o?o.call(e,t):n.set(e,t),t);var Vm={};Ju(Vm,{Button:()=>si,Checkbox:()=>Ic,CloseButton:()=>Fc,Combobox:()=>dd,ComboboxButton:()=>Ca,ComboboxInput:()=>Oa,ComboboxLabel:()=>Da,ComboboxOption:()=>Ia,ComboboxOptions:()=>La,DataInteractive:()=>Td,Description:()=>Lt,Dialog:()=>Vd,DialogBackdrop:()=>Gd,DialogDescription:()=>Ud,DialogPanel:()=>ja,DialogTitle:()=>Ka,Disclosure:()=>tp,DisclosureButton:()=>Ja,DisclosurePanel:()=>Qa,Field:()=>rp,Fieldset:()=>sp,FocusTrap:()=>Ui,FocusTrapFeatures:()=>Sr,Input:()=>up,Label:()=>tt,Legend:()=>fp,Listbox:()=>Sp,ListboxButton:()=>uu,ListboxLabel:()=>cu,ListboxOption:()=>du,ListboxOptions:()=>fu,ListboxSelectedOption:()=>pu,Menu:()=>Vp,MenuButton:()=>Tu,MenuHeading:()=>hu,MenuItem:()=>gu,MenuItems:()=>bu,MenuSection:()=>yu,MenuSeparator:()=>vu,Popover:()=>rm,PopoverBackdrop:()=>Au,PopoverButton:()=>Ru,PopoverGroup:()=>Ou,PopoverOverlay:()=>Su,PopoverPanel:()=>Cu,Portal:()=>st,Radio:()=>Lu,RadioGroup:()=>mm,RadioGroupDescription:()=>Mu,RadioGroupLabel:()=>Iu,RadioGroupOption:()=>Du,Select:()=>gm,Switch:()=>Pm,SwitchDescription:()=>$u,SwitchGroup:()=>wu,SwitchLabel:()=>_u,Tab:()=>Nm,TabGroup:()=>Bu,TabList:()=>Gu,TabPanel:()=>Vu,TabPanels:()=>Uu,Textarea:()=>Um,Transition:()=>Wi,TransitionChild:()=>wo,useClose:()=>nr});module.exports=Qu(Vm);var Ts=le(require("react"),1),no=typeof document!="undefined"?Ts.default.useLayoutEffect:()=>{};var No=require("react");function Br(e){let n=(0,No.useRef)(null);return no(()=>{n.current=e},[e]),(0,No.useCallback)((...t)=>{let o=n.current;return o==null?void 0:o(...t)},[])}var Xe=e=>{var n;return(n=e==null?void 0:e.ownerDocument)!==null&&n!==void 0?n:document},mt=e=>e&&"window"in e&&e.window===e?e:Xe(e).defaultView||window;function Zu(e){return e!==null&&typeof e=="object"&&"nodeType"in e&&typeof e.nodeType=="number"}function Gr(e){return Zu(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}var ec=!1;function Bo(){return ec}function oo(e,n){if(!Bo())return n&&e?e.contains(n):!1;if(!e||!n)return!1;let t=n;for(;t!==null;){if(t===e)return!0;t.tagName==="SLOT"&&t.assignedSlot?t=t.assignedSlot.parentNode:Gr(t)?t=t.host:t=t.parentNode}return!1}var yn=(e=document)=>{var n;if(!Bo())return e.activeElement;let t=e.activeElement;for(;t&&"shadowRoot"in t&&(!((n=t.shadowRoot)===null||n===void 0)&&n.activeElement);)t=t.shadowRoot.activeElement;return t};function ro(e){return Bo()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}function Go(e){var n;return typeof window=="undefined"||window.navigator==null?!1:((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent)}function Ur(e){var n;return typeof window!="undefined"&&window.navigator!=null?e.test(((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.platform)||window.navigator.platform):!1}function Ct(e){let n=null;return()=>(n==null&&(n=e()),n)}var io=Ct(function(){return Ur(/^Mac/i)}),bs=Ct(function(){return Ur(/^iPhone/i)}),gs=Ct(function(){return Ur(/^iPad/i)||io()&&navigator.maxTouchPoints>1}),ys=Ct(function(){return bs()||gs()}),tc=Ct(function(){return io()||ys()}),nc=Ct(function(){return Go(/AppleWebKit/i)&&!hs()}),hs=Ct(function(){return Go(/Chrome/i)}),Vr=Ct(function(){return Go(/Android/i)}),oc=Ct(function(){return Go(/Firefox/i)});var Bt=require("react");function so(){let e=(0,Bt.useRef)(new Map),n=(0,Bt.useCallback)((r,i,s,l)=>{let a=l!=null&&l.once?(...c)=>{e.current.delete(s),s(...c)}:s;e.current.set(s,{type:i,eventTarget:r,fn:a,options:l}),r.addEventListener(i,a,l)},[]),t=(0,Bt.useCallback)((r,i,s,l)=>{var a;let c=((a=e.current.get(s))===null||a===void 0?void 0:a.fn)||s;r.removeEventListener(i,c,l),e.current.delete(s)},[]),o=(0,Bt.useCallback)(()=>{e.current.forEach((r,i)=>{t(r.eventTarget,r.type,i,r.options)})},[t]);return(0,Bt.useEffect)(()=>o,[o]),{addGlobalListener:n,removeGlobalListener:t,removeAllGlobalListeners:o}}function Wr(e){return e.mozInputSource===0&&e.isTrusted?!0:Vr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}var Uo=require("react");function jr(e){let n=e;return n.nativeEvent=e,n.isDefaultPrevented=()=>n.defaultPrevented,n.isPropagationStopped=()=>n.cancelBubble,n.persist=()=>{},n}function vs(e,n){Object.defineProperty(e,"target",{value:n}),Object.defineProperty(e,"currentTarget",{value:n})}function Vo(e){let n=(0,Uo.useRef)({isFocused:!1,observer:null});no(()=>{let o=n.current;return()=>{o.observer&&(o.observer.disconnect(),o.observer=null)}},[]);let t=Br(o=>{e==null||e(o)});return(0,Uo.useCallback)(o=>{if(o.target instanceof HTMLButtonElement||o.target instanceof HTMLInputElement||o.target instanceof HTMLTextAreaElement||o.target instanceof HTMLSelectElement){n.current.isFocused=!0;let r=o.target,i=s=>{if(n.current.isFocused=!1,r.disabled){let l=jr(s);t(l)}n.current.observer&&(n.current.observer.disconnect(),n.current.observer=null)};r.addEventListener("focusout",i,{once:!0}),n.current.observer=new MutationObserver(()=>{if(n.current.isFocused&&r.disabled){var s;(s=n.current.observer)===null||s===void 0||s.disconnect();let l=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:l})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:l}))}}),n.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[t])}var Kr=!1;var qr=require("react");var ao=null,zr=new Set,lo=new Map,nn=!1,Xr=!1,rc={Tab:!0,Escape:!0};function Jr(e,n){for(let t of zr)t(e,n)}function ic(e){return!(e.metaKey||!io()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Wo(e){nn=!0,ic(e)&&(ao="keyboard",Jr("keyboard",e))}function hn(e){ao="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(nn=!0,Jr("pointer",e))}function Es(e){Wr(e)&&(nn=!0,ao="virtual")}function xs(e){e.target===window||e.target===document||Kr||!e.isTrusted||(!nn&&!Xr&&(ao="virtual",Jr("virtual",e)),nn=!1,Xr=!1)}function Ps(){Kr||(nn=!1,Xr=!0)}function Yr(e){if(typeof window=="undefined"||lo.get(mt(e)))return;let n=mt(e),t=Xe(e),o=n.HTMLElement.prototype.focus;n.HTMLElement.prototype.focus=function(){nn=!0,o.apply(this,arguments)},t.addEventListener("keydown",Wo,!0),t.addEventListener("keyup",Wo,!0),t.addEventListener("click",Es,!0),n.addEventListener("focus",xs,!0),n.addEventListener("blur",Ps,!1),typeof PointerEvent!="undefined"&&(t.addEventListener("pointerdown",hn,!0),t.addEventListener("pointermove",hn,!0),t.addEventListener("pointerup",hn,!0)),n.addEventListener("beforeunload",()=>{Rs(e)},{once:!0}),lo.set(n,{focus:o})}var Rs=(e,n)=>{let t=mt(e),o=Xe(e);n&&o.removeEventListener("DOMContentLoaded",n),lo.has(t)&&(t.HTMLElement.prototype.focus=lo.get(t).focus,o.removeEventListener("keydown",Wo,!0),o.removeEventListener("keyup",Wo,!0),o.removeEventListener("click",Es,!0),t.removeEventListener("focus",xs,!0),t.removeEventListener("blur",Ps,!1),typeof PointerEvent!="undefined"&&(o.removeEventListener("pointerdown",hn,!0),o.removeEventListener("pointermove",hn,!0),o.removeEventListener("pointerup",hn,!0)),lo.delete(t))};function Ss(e){let n=Xe(e),t;return n.readyState!=="loading"?Yr(e):(t=()=>{Yr(e)},n.addEventListener("DOMContentLoaded",t)),()=>Rs(e,t)}typeof document!="undefined"&&Ss();function jo(){return ao!=="pointer"}var sc=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function lc(e,n,t){let o=Xe(t==null?void 0:t.target),r=typeof window!="undefined"?mt(t==null?void 0:t.target).HTMLInputElement:HTMLInputElement,i=typeof window!="undefined"?mt(t==null?void 0:t.target).HTMLTextAreaElement:HTMLTextAreaElement,s=typeof window!="undefined"?mt(t==null?void 0:t.target).HTMLElement:HTMLElement,l=typeof window!="undefined"?mt(t==null?void 0:t.target).KeyboardEvent:KeyboardEvent;return e=e||o.activeElement instanceof r&&!sc.has(o.activeElement.type)||o.activeElement instanceof i||o.activeElement instanceof s&&o.activeElement.isContentEditable,!(e&&n==="keyboard"&&t instanceof l&&!rc[t.key])}function Qr(e,n,t){Yr(),(0,qr.useEffect)(()=>{let o=(r,i)=>{lc(!!(t!=null&&t.isTextInput),r,i)&&e(jo())};return zr.add(o),()=>{zr.delete(o)}},n)}var Zr=require("react");function ei(e){let{isDisabled:n,onFocus:t,onBlur:o,onFocusChange:r}=e,i=(0,Zr.useCallback)(a=>{if(a.target===a.currentTarget)return o&&o(a),r&&r(!1),!0},[o,r]),s=Vo(i),l=(0,Zr.useCallback)(a=>{let c=Xe(a.target),u=c?yn(c):yn();a.target===a.currentTarget&&u===ro(a.nativeEvent)&&(t&&t(a),r&&r(!0),s(a))},[r,t,s]);return{focusProps:{onFocus:!n&&(t||r||o)?l:void 0,onBlur:!n&&(o||r)?i:void 0}}}var uo=require("react");function ti(e){let{isDisabled:n,onBlurWithin:t,onFocusWithin:o,onFocusWithinChange:r}=e,i=(0,uo.useRef)({isFocusWithin:!1}),{addGlobalListener:s,removeAllGlobalListeners:l}=so(),a=(0,uo.useCallback)(p=>{p.currentTarget.contains(p.target)&&i.current.isFocusWithin&&!p.currentTarget.contains(p.relatedTarget)&&(i.current.isFocusWithin=!1,l(),t&&t(p),r&&r(!1))},[t,r,i,l]),c=Vo(a),u=(0,uo.useCallback)(p=>{if(!p.currentTarget.contains(p.target))return;let d=Xe(p.target),f=yn(d);if(!i.current.isFocusWithin&&f===ro(p.nativeEvent)){o&&o(p),r&&r(!0),i.current.isFocusWithin=!0,c(p);let m=p.currentTarget;s(d,"focus",T=>{if(i.current.isFocusWithin&&!oo(m,T.target)){let b=new d.defaultView.FocusEvent("blur",{relatedTarget:T.target});vs(b,m);let g=jr(b);a(g)}},{capture:!0})}},[o,r,c,s,a]);return n?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:a}}}var Ot=require("react"),oi=!1,ni=0;function ac(){oi=!0,setTimeout(()=>{oi=!1},50)}function As(e){e.pointerType==="touch"&&ac()}function uc(){if(typeof document!="undefined")return typeof PointerEvent!="undefined"&&document.addEventListener("pointerup",As),ni++,()=>{ni--,!(ni>0)&&typeof PointerEvent!="undefined"&&document.removeEventListener("pointerup",As)}}function fe(e){let{onHoverStart:n,onHoverChange:t,onHoverEnd:o,isDisabled:r}=e,[i,s]=(0,Ot.useState)(!1),l=(0,Ot.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,Ot.useEffect)(uc,[]);let{addGlobalListener:a,removeAllGlobalListeners:c}=so(),{hoverProps:u,triggerHoverEnd:p}=(0,Ot.useMemo)(()=>{let d=(T,b)=>{if(l.pointerType=b,r||b==="touch"||l.isHovered||!T.currentTarget.contains(T.target))return;l.isHovered=!0;let g=T.currentTarget;l.target=g,a(Xe(T.target),"pointerover",h=>{l.isHovered&&l.target&&!oo(l.target,h.target)&&f(h,h.pointerType)},{capture:!0}),n&&n({type:"hoverstart",target:g,pointerType:b}),t&&t(!0),s(!0)},f=(T,b)=>{let g=l.target;l.pointerType="",l.target=null,!(b==="touch"||!l.isHovered||!g)&&(l.isHovered=!1,c(),o&&o({type:"hoverend",target:g,pointerType:b}),t&&t(!1),s(!1))},m={};return typeof PointerEvent!="undefined"&&(m.onPointerEnter=T=>{oi&&T.pointerType==="mouse"||d(T,T.pointerType)},m.onPointerLeave=T=>{!r&&T.currentTarget.contains(T.target)&&f(T,T.pointerType)}),{hoverProps:m,triggerHoverEnd:f}},[n,t,o,r,l,a,c]);return(0,Ot.useEffect)(()=>{r&&p({currentTarget:l.target},l.pointerType)},[r]),{hoverProps:u,isHovered:i}}var Gt=require("react");function ce(e={}){let{autoFocus:n=!1,isTextInput:t,within:o}=e,r=(0,Gt.useRef)({isFocused:!1,isFocusVisible:n||jo()}),[i,s]=(0,Gt.useState)(!1),[l,a]=(0,Gt.useState)(()=>r.current.isFocused&&r.current.isFocusVisible),c=(0,Gt.useCallback)(()=>a(r.current.isFocused&&r.current.isFocusVisible),[]),u=(0,Gt.useCallback)(f=>{r.current.isFocused=f,s(f),c()},[c]);Qr(f=>{r.current.isFocusVisible=f,c()},[],{isTextInput:t});let{focusProps:p}=ei({isDisabled:o,onFocusChange:u}),{focusWithinProps:d}=ti({isDisabled:!o,onFocusWithinChange:u});return{isFocused:i,isFocusVisible:l,focusProps:o?d:p}}var Is=require("react");var Xo=require("react");var ri=class{constructor(){Oe(this,"current",this.detect());Oe(this,"handoffState","pending");Oe(this,"currentId",0)}set(n){this.current!==n&&(this.handoffState="pending",this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},at=new ri;function Pe(e){var n,t;return at.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?(t=(n=e.current)==null?void 0:n.ownerDocument)!=null?t:document:null:document}var Ko=require("react");function Dt(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function he(){let e=[],n={addEventListener(t,o,r,i){return t.addEventListener(o,r,i),n.add(()=>t.removeEventListener(o,r,i))},requestAnimationFrame(...t){let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(...t){let o={current:!0};return Dt(()=>{o.current&&t[0]()}),n.add(()=>{o.current=!1})},style(t,o,r){let i=t.style.getPropertyValue(o);return Object.assign(t.style,{[o]:r}),this.add(()=>{Object.assign(t.style,{[o]:i})})},group(t){let o=he();return t(o),this.add(()=>o.dispose())},add(t){return e.includes(t)||e.push(t),()=>{let o=e.indexOf(t);if(o>=0)for(let r of e.splice(o,1))r()}},dispose(){for(let t of e.splice(0))t()}};return n}function Se(){let[e]=(0,Ko.useState)(he);return(0,Ko.useEffect)(()=>()=>e.dispose(),[e]),e}var Os=le(require("react"),1);var Cs=require("react");var zo=require("react");var V=(e,n)=>{at.isServer?(0,zo.useEffect)(e,n):(0,zo.useLayoutEffect)(e,n)};function me(e){let n=(0,Cs.useRef)(e);return V(()=>{n.current=e},[e]),n}var E=function(n){let t=me(n);return Os.default.useCallback((...o)=>t.current(...o),[t])};function cc(e){let n=e.width/2,t=e.height/2;return{top:e.clientY-t,right:e.clientX+n,bottom:e.clientY+t,left:e.clientX-n}}function fc(e,n){return!(!e||!n||e.right<n.left||e.left>n.right||e.bottom<n.top||e.top>n.bottom)}function Ae({disabled:e=!1}={}){let n=(0,Xo.useRef)(null),[t,o]=(0,Xo.useState)(!1),r=Se(),i=E(()=>{n.current=null,o(!1),r.dispose()}),s=E(l=>{if(r.dispose(),n.current===null){n.current=l.currentTarget,o(!0);{let a=Pe(l.currentTarget);r.addEventListener(a,"pointerup",i,!1),r.addEventListener(a,"pointermove",c=>{if(n.current){let u=cc(c);o(fc(u,n.current.getBoundingClientRect()))}},!1),r.addEventListener(a,"pointercancel",i,!1)}}});return{pressed:t,pressProps:e?{}:{onPointerDown:s,onPointerUp:i,onClick:i}}}var vn=le(require("react"),1),Ds=(0,vn.createContext)(void 0);function ge(){return(0,vn.useContext)(Ds)}function Yo({value:e,children:n}){return vn.default.createElement(Ds.Provider,{value:e},n)}var Me=le(require("react"),1);function co(...e){return Array.from(new Set(e.flatMap(n=>typeof n=="string"?n.split(" "):[]))).filter(Boolean).join(" ")}function Y(e,n,...t){if(e in n){let r=n[e];return typeof r=="function"?r(...t):r}let o=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,Y),o}function H(){let e=pc();return(0,Me.useCallback)(n=>dc({mergeRefs:e,...n}),[e])}function dc({ourProps:e,theirProps:n,slot:t,defaultTag:o,features:r,visible:i=!0,name:s,mergeRefs:l}){l=l!=null?l:mc;let a=Ls(n,e);if(i)return qo(a,t,o,s,l);let c=r!=null?r:0;if(c&2){let{static:u=!1,...p}=a;if(u)return qo(p,t,o,s,l)}if(c&1){let{unmount:u=!0,...p}=a;return Y(u?0:1,{[0](){return null},[1](){return qo({...p,hidden:!0,style:{display:"none"}},t,o,s,l)}})}return qo(a,t,o,s,l)}function qo(e,n={},t,o,r){let{as:i=t,children:s,refName:l="ref",...a}=ii(e,["unmount","static"]),c=e.ref!==void 0?{[l]:e.ref}:{},u=typeof s=="function"?s(n):s;"className"in a&&a.className&&typeof a.className=="function"&&(a.className=a.className(n)),a["aria-labelledby"]&&a["aria-labelledby"]===a.id&&(a["aria-labelledby"]=void 0);let p={};if(n){let d=!1,f=[];for(let[m,T]of Object.entries(n))typeof T=="boolean"&&(d=!0),T===!0&&f.push(m.replace(/([A-Z])/g,b=>`-${b.toLowerCase()}`));if(d){p["data-headlessui-state"]=f.join(" ");for(let m of f)p[`data-${m}`]=""}}if(i===Me.Fragment&&(Object.keys(Tt(a)).length>0||Object.keys(Tt(p)).length>0))if(!(0,Me.isValidElement)(u)||Array.isArray(u)&&u.length>1){if(Object.keys(Tt(a)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(Tt(a)).concat(Object.keys(Tt(p))).map(d=>`  - ${d}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(d=>`  - ${d}`).join(`
`)].join(`
`))}else{let d=u.props,f=d==null?void 0:d.className,m=typeof f=="function"?(...g)=>co(f(...g),a.className):co(f,a.className),T=m?{className:m}:{},b=Ls(u.props,Tt(ii(a,["ref"])));for(let g in p)g in b&&delete p[g];return(0,Me.cloneElement)(u,Object.assign({},b,p,c,{ref:r(Tc(u),c.ref)},T))}return(0,Me.createElement)(i,Object.assign({},ii(a,["ref"]),i!==Me.Fragment&&c,i!==Me.Fragment&&p),u)}function pc(){let e=(0,Me.useRef)([]),n=(0,Me.useCallback)(t=>{for(let o of e.current)o!=null&&(typeof o=="function"?o(t):o.current=t)},[]);return(...t)=>{if(!t.every(o=>o==null))return e.current=t,n}}function mc(...e){return e.every(n=>n==null)?void 0:n=>{for(let t of e)t!=null&&(typeof t=="function"?t(n):t.current=n)}}function Ls(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];if(n.disabled||n["aria-disabled"])for(let r in t)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(t[r]=[i=>{var s;return(s=i==null?void 0:i.preventDefault)==null?void 0:s.call(i)}]);for(let r in t)Object.assign(n,{[r](i,...s){let l=t[r];for(let a of l){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;a(i,...s)}}});return n}function ae(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];for(let r in t)Object.assign(n,{[r](...i){let s=t[r];for(let l of s)l==null||l(...i)}});return n}function w(e){var n;return Object.assign((0,Me.forwardRef)(e),{displayName:(n=e.displayName)!=null?n:e.name})}function Tt(e){let n=Object.assign({},e);for(let t in n)n[t]===void 0&&delete n[t];return n}function ii(e,n=[]){let t=Object.assign({},e);for(let o of n)o in t&&delete t[o];return t}function Tc(e){return Me.default.version.split(".")[0]>="19"?e.props.ref:e.ref}var bc="button";function gc(e,n){var T;let t=ge(),{disabled:o=t||!1,autoFocus:r=!1,...i}=e,{isFocusVisible:s,focusProps:l}=ce({autoFocus:r}),{isHovered:a,hoverProps:c}=fe({isDisabled:o}),{pressed:u,pressProps:p}=Ae({disabled:o}),d=ae({ref:n,type:(T=i.type)!=null?T:"button",disabled:o||void 0,autoFocus:r},l,c,p),f=(0,Is.useMemo)(()=>({disabled:o,hover:a,focus:s,active:u,autofocus:r}),[o,a,s,u,r]);return H()({ourProps:d,theirProps:i,slot:f,defaultTag:bc,name:"Button"})}var si=w(gc);var ht=le(require("react"),1);var En=require("react");function bt(e,n,t){let[o,r]=(0,En.useState)(t),i=e!==void 0,s=(0,En.useRef)(i),l=(0,En.useRef)(!1),a=(0,En.useRef)(!1);return i&&!s.current&&!l.current?(l.current=!0,s.current=i,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!i&&s.current&&!a.current&&(a.current=!0,s.current=i,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[i?e:o,E(c=>(i||r(c),n==null?void 0:n(c)))]}var Ms=require("react");function gt(e){let[n]=(0,Ms.useState)(e);return n}var J=require("react");var De=le(require("react"),1),_s=require("react-dom");function li(e={},n=null,t=[]){for(let[o,r]of Object.entries(e))ws(t,Fs(n,o),r);return t}function Fs(e,n){return e?e+"["+n+"]":n}function ws(e,n,t){if(Array.isArray(t))for(let[o,r]of t.entries())ws(e,Fs(n,o.toString()),r);else t instanceof Date?e.push([n,t.toISOString()]):typeof t=="boolean"?e.push([n,t?"1":"0"]):typeof t=="string"?e.push([n,t]):typeof t=="number"?e.push([n,`${t}`]):t==null?e.push([n,""]):li(t,n,e)}function Ut(e){var t,o;let n=(t=e==null?void 0:e.form)!=null?t:e.closest("form");if(n){for(let r of n.elements)if(r!==e&&(r.tagName==="INPUT"&&r.type==="submit"||r.tagName==="BUTTON"&&r.type==="submit"||r.nodeName==="INPUT"&&r.type==="image")){r.click();return}(o=n.requestSubmit)==null||o.call(n)}}var yc="span";function hc(e,n){var s;let{features:t=1,...o}=e,r={ref:n,"aria-hidden":(t&2)===2?!0:(s=o["aria-hidden"])!=null?s:void 0,hidden:(t&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(t&4)===4&&(t&2)!==2&&{display:"none"}}};return H()({ourProps:r,theirProps:o,slot:{},defaultTag:yc,name:"Hidden"})}var ke=w(hc);var $s=(0,De.createContext)(null);function ks(e){let[n,t]=(0,De.useState)(null);return De.default.createElement($s.Provider,{value:{target:n}},e.children,De.default.createElement(ke,{features:4,ref:t}))}function vc({children:e}){let n=(0,De.useContext)($s);if(!n)return De.default.createElement(De.default.Fragment,null,e);let{target:t}=n;return t?(0,_s.createPortal)(De.default.createElement(De.default.Fragment,null,e),t):null}function yt({data:e,form:n,disabled:t,onReset:o,overrides:r}){let[i,s]=(0,De.useState)(null),l=Se();return(0,De.useEffect)(()=>{if(o&&i)return l.addEventListener(i,"reset",o)},[i,n,o]),De.default.createElement(vc,null,De.default.createElement(Ec,{setForm:s,formId:n}),li(e).map(([a,c])=>De.default.createElement(ke,{features:4,...Tt({key:a,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:n,disabled:t,name:a,value:c,...r})})))}function Ec({setForm:e,formId:n}){return(0,De.useEffect)(()=>{if(n){let t=document.getElementById(n);t&&e(t)}},[e,n]),n?null:De.default.createElement(ke,{features:4,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:t=>{if(!t)return;let o=t.closest("form");o&&e(o)}})}var xn=le(require("react"),1),Hs=(0,xn.createContext)(void 0);function we(){return(0,xn.useContext)(Hs)}function Ns({id:e,children:n}){return xn.default.createElement(Hs.Provider,{value:e},n)}function fo(e){return typeof e!="object"||e===null?!1:"nodeType"in e}function ut(e){return fo(e)&&"tagName"in e}function Te(e){return ut(e)&&"accessKey"in e}function Le(e){return ut(e)&&"tabIndex"in e}function Bs(e){return ut(e)&&"style"in e}function Gs(e){return Te(e)&&e.nodeName==="IFRAME"}function on(e){return Te(e)&&e.nodeName==="INPUT"}function po(e){return Te(e)&&e.nodeName==="LABEL"}function Us(e){return Te(e)&&e.nodeName==="FIELDSET"}function ai(e){return Te(e)&&e.nodeName==="LEGEND"}function Vs(e){return ut(e)?e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]'):!1}function _e(e){let n=e.parentElement,t=null;for(;n&&!Us(n);)ai(n)&&(t=n),n=n.parentElement;let o=(n==null?void 0:n.getAttribute("disabled"))==="";return o&&xc(t)?!1:o}function xc(e){if(!e)return!1;let n=e.previousElementSibling;for(;n!==null;){if(ai(n))return!1;n=n.previousElementSibling}return!0}var Ye=le(require("react"),1);var Qo=require("react");var Ws=Symbol();function Pn(e,n=!0){return Object.assign(e,{[Ws]:n})}function K(...e){let n=(0,Qo.useRef)(e);(0,Qo.useEffect)(()=>{n.current=e},[e]);let t=E(o=>{for(let r of n.current)r!=null&&(typeof r=="function"?r(o):r.current=o)});return e.every(o=>o==null||(o==null?void 0:o[Ws]))?void 0:t}var Zo=(0,Ye.createContext)(null);Zo.displayName="DescriptionContext";function js(){let e=(0,Ye.useContext)(Zo);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,js),n}return e}function Ne(){var e,n;return(n=(e=(0,Ye.useContext)(Zo))==null?void 0:e.value)!=null?n:void 0}function ct(){let[e,n]=(0,Ye.useState)([]);return[e.length>0?e.join(" "):void 0,(0,Ye.useMemo)(()=>function(o){let r=E(s=>(n(l=>[...l,s]),()=>n(l=>{let a=l.slice(),c=a.indexOf(s);return c!==-1&&a.splice(c,1),a}))),i=(0,Ye.useMemo)(()=>({register:r,slot:o.slot,name:o.name,props:o.props,value:o.value}),[r,o.slot,o.name,o.props,o.value]);return Ye.default.createElement(Zo.Provider,{value:i},o.children)},[n])]}var Pc="p";function Rc(e,n){let t=(0,J.useId)(),o=ge(),{id:r=`headlessui-description-${t}`,...i}=e,s=js(),l=K(n);V(()=>s.register(r),[r,s.register]);let a=o||!1,c=(0,Ye.useMemo)(()=>({...s.slot,disabled:a}),[s.slot,a]),u={ref:l,...s.props,id:r};return H()({ourProps:u,theirProps:i,slot:c,defaultTag:Pc,name:s.name||"Description"})}var Sc=w(Rc),Lt=Object.assign(Sc,{});var qe=le(require("react"),1);var er=(0,qe.createContext)(null);er.displayName="LabelContext";function tr(){let e=(0,qe.useContext)(er);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,tr),n}return e}function Fe(e){var t,o,r;let n=(o=(t=(0,qe.useContext)(er))==null?void 0:t.value)!=null?o:void 0;return((r=e==null?void 0:e.length)!=null?r:0)>0?[n,...e].filter(Boolean).join(" "):n}function Be({inherit:e=!1}={}){let n=Fe(),[t,o]=(0,qe.useState)([]),r=e?[n,...t].filter(Boolean):t;return[r.length>0?r.join(" "):void 0,(0,qe.useMemo)(()=>function(s){let l=E(c=>(o(u=>[...u,c]),()=>o(u=>{let p=u.slice(),d=p.indexOf(c);return d!==-1&&p.splice(d,1),p}))),a=(0,qe.useMemo)(()=>({register:l,slot:s.slot,name:s.name,props:s.props,value:s.value}),[l,s.slot,s.name,s.props,s.value]);return qe.default.createElement(er.Provider,{value:a},s.children)},[o])]}var Ac="label";function Cc(e,n){var b;let t=(0,J.useId)(),o=tr(),r=we(),i=ge(),{id:s=`headlessui-label-${t}`,htmlFor:l=r!=null?r:(b=o.props)==null?void 0:b.htmlFor,passive:a=!1,...c}=e,u=K(n);V(()=>o.register(s),[s,o.register]);let p=E(g=>{let h=g.currentTarget;if(!(g.target!==g.currentTarget&&Vs(g.target))&&(po(h)&&g.preventDefault(),o.props&&"onClick"in o.props&&typeof o.props.onClick=="function"&&o.props.onClick(g),po(h))){let y=document.getElementById(h.htmlFor);if(y){let v=y.getAttribute("disabled");if(v==="true"||v==="")return;let x=y.getAttribute("aria-disabled");if(x==="true"||x==="")return;(on(y)&&(y.type==="file"||y.type==="radio"||y.type==="checkbox")||y.role==="radio"||y.role==="checkbox"||y.role==="switch")&&y.click(),y.focus({preventScroll:!0})}}}),d=i||!1,f=(0,qe.useMemo)(()=>({...o.slot,disabled:d}),[o.slot,d]),m={ref:u,...o.props,id:s,htmlFor:l,onClick:p};return a&&("onClick"in m&&(delete m.htmlFor,delete m.onClick),"onClick"in c&&delete c.onClick),H()({ourProps:m,theirProps:c,slot:f,defaultTag:l?Ac:"div",name:o.name||"Label"})}var Oc=w(Cc),tt=Object.assign(Oc,{});var Dc="span";function Lc(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-checkbox-${t}`,disabled:s=r||!1,autoFocus:l=!1,checked:a,defaultChecked:c,onChange:u,name:p,value:d,form:f,indeterminate:m=!1,tabIndex:T=0,...b}=e,g=gt(c),[h,y]=bt(a,u,g!=null?g:!1),v=Fe(),x=Ne(),R=Se(),[$,C]=(0,ht.useState)(!1),B=E(()=>{C(!0),y==null||y(!h),R.nextFrame(()=>{C(!1)})}),S=E(X=>{if(_e(X.currentTarget))return X.preventDefault();X.preventDefault(),B()}),P=E(X=>{X.key===" "?(X.preventDefault(),B()):X.key==="Enter"&&Ut(X.currentTarget)}),L=E(X=>X.preventDefault()),{isFocusVisible:O,focusProps:I}=ce({autoFocus:l}),{isHovered:W,hoverProps:A}=fe({isDisabled:s}),{pressed:N,pressProps:ne}=Ae({disabled:s}),q=ae({ref:n,id:i,role:"checkbox","aria-checked":m?"mixed":h?"true":"false","aria-labelledby":v,"aria-describedby":x,"aria-disabled":s?!0:void 0,indeterminate:m?"true":void 0,tabIndex:s?void 0:T,onKeyUp:s?void 0:P,onKeyPress:s?void 0:L,onClick:s?void 0:S},I,A,ne),_=(0,ht.useMemo)(()=>({checked:h,disabled:s,hover:W,focus:O,active:N,indeterminate:m,changing:$,autofocus:l}),[h,m,s,W,O,N,$,l]),G=(0,ht.useCallback)(()=>{if(g!==void 0)return y==null?void 0:y(g)},[y,g]),U=H();return ht.default.createElement(ht.default.Fragment,null,p!=null&&ht.default.createElement(yt,{disabled:s,data:{[p]:d||"on"},overrides:{type:"checkbox",checked:h},form:f,onReset:G}),U({ourProps:q,theirProps:b,slot:_,defaultTag:Dc,name:"Checkbox"}))}var Ic=w(Lc);var zs=le(require("react"),1);var Rn=le(require("react"),1),Ks=(0,Rn.createContext)(()=>{});function nr(){return(0,Rn.useContext)(Ks)}function rn({value:e,children:n}){return Rn.default.createElement(Ks.Provider,{value:e},n)}function Mc(e,n){let t=nr();return zs.default.createElement(si,{ref:n,...ae({onClick:t},e)})}var Fc=w(Mc);var Vt=le(require("react"),1),rl=require("react-dom");function sn(e,n,t){var s;let o=(s=t.initialDeps)!=null?s:[],r;function i(){var l,a,c,u;let p;t.key&&((l=t.debug)!=null&&l.call(t))&&(p=Date.now());let d=e();if(!(d.length!==o.length||d.some((T,b)=>o[b]!==T)))return r;o=d;let m;if(t.key&&((a=t.debug)!=null&&a.call(t))&&(m=Date.now()),r=n(...d),t.key&&((c=t.debug)!=null&&c.call(t))){let T=Math.round((Date.now()-p)*100)/100,b=Math.round((Date.now()-m)*100)/100,g=b/16,h=(y,v)=>{for(y=String(y);y.length<v;)y=" "+y;return y};console.info(`%c\u23F1 ${h(b,5)} /${h(T,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,t==null?void 0:t.key)}return(u=t==null?void 0:t.onChange)==null||u.call(t,r),r}return i.updateDeps=l=>{o=l},i}function ui(e,n){if(e===void 0)throw new Error(`Unexpected undefined${n?`: ${n}`:""}`);return e}var Xs=(e,n)=>Math.abs(e-n)<=1,Ys=(e,n,t)=>{let o;return function(...r){e.clearTimeout(o),o=e.setTimeout(()=>n.apply(this,r),t)}};var qs=e=>{let{offsetWidth:n,offsetHeight:t}=e;return{width:n,height:t}},wc=e=>e,_c=e=>{let n=Math.max(e.startIndex-e.overscan,0),t=Math.min(e.endIndex+e.overscan,e.count-1),o=[];for(let r=n;r<=t;r++)o.push(r);return o},Zs=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=s=>{let{width:l,height:a}=s;n({width:Math.round(l),height:Math.round(a)})};if(r(qs(t)),!o.ResizeObserver)return()=>{};let i=new o.ResizeObserver(s=>{let l=()=>{let a=s[0];if(a!=null&&a.borderBoxSize){let c=a.borderBoxSize[0];if(c){r({width:c.inlineSize,height:c.blockSize});return}}r(qs(t))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(l):l()});return i.observe(t,{box:"border-box"}),()=>{i.unobserve(t)}},Js={passive:!0};var Qs=typeof window=="undefined"?!0:"onscrollend"in window,el=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=0,i=e.options.useScrollendEvent&&Qs?()=>{}:Ys(o,()=>{n(r,!1)},e.options.isScrollingResetDelay),s=u=>()=>{let{horizontal:p,isRtl:d}=e.options;r=p?t.scrollLeft*(d&&-1||1):t.scrollTop,i(),n(r,u)},l=s(!0),a=s(!1);a(),t.addEventListener("scroll",l,Js);let c=e.options.useScrollendEvent&&Qs;return c&&t.addEventListener("scrollend",a,Js),()=>{t.removeEventListener("scroll",l),c&&t.removeEventListener("scrollend",a)}};var $c=(e,n,t)=>{if(n!=null&&n.borderBoxSize){let o=n.borderBoxSize[0];if(o)return Math.round(o[t.options.horizontal?"inlineSize":"blockSize"])}return e[t.options.horizontal?"offsetWidth":"offsetHeight"]};var tl=(e,{adjustments:n=0,behavior:t},o)=>{var r,i;let s=e+n;(i=(r=o.scrollElement)==null?void 0:r.scrollTo)==null||i.call(r,{[o.options.horizontal?"left":"top"]:s,behavior:t})},or=class{constructor(n){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let t=null,o=()=>t||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:t=new this.targetWindow.ResizeObserver(r=>{r.forEach(i=>{let s=()=>{this._measureElement(i.target,i)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(s):s()})}));return{disconnect:()=>{var r;(r=o())==null||r.disconnect(),t=null},observe:r=>{var i;return(i=o())==null?void 0:i.observe(r,{box:"border-box"})},unobserve:r=>{var i;return(i=o())==null?void 0:i.unobserve(r)}}})(),this.range=null,this.setOptions=t=>{Object.entries(t).forEach(([o,r])=>{typeof r=="undefined"&&delete t[o]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:wc,rangeExtractor:_c,onChange:()=>{},measureElement:$c,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...t}},this.notify=t=>{var o,r;(r=(o=this.options).onChange)==null||r.call(o,this,t)},this.maybeNotify=sn(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),t=>{this.notify(t)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(t=>t()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var r;var t;let o=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==o){if(this.cleanup(),!o){this.maybeNotify();return}this.scrollElement=o,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(r=(t=this.scrollElement)==null?void 0:t.window)!=null?r:null,this.elementsCache.forEach(i=>{this.observer.observe(i)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,i=>{this.scrollRect=i,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(i,s)=>{this.scrollAdjustments=0,this.scrollDirection=s?this.getScrollOffset()<i?"forward":"backward":null,this.scrollOffset=i,this.isScrolling=s,this.maybeNotify()}))}},this.getSize=()=>{var t;return this.options.enabled?(this.scrollRect=(t=this.scrollRect)!=null?t:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var t;return this.options.enabled?(this.scrollOffset=(t=this.scrollOffset)!=null?t:typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(t,o)=>{let r=new Map,i=new Map;for(let s=o-1;s>=0;s--){let l=t[s];if(r.has(l.lane))continue;let a=i.get(l.lane);if(a==null||l.end>a.end?i.set(l.lane,l):l.end<a.end&&r.set(l.lane,!0),r.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((s,l)=>s.end===l.end?s.index-l.index:s.end-l.end)[0]:void 0},this.getMeasurementOptions=sn(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(t,o,r,i,s)=>(this.pendingMeasuredCacheIndexes=[],{count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s}),{key:!1}),this.getMeasurements=sn(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s},l)=>{if(!s)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(u=>{this.itemSizeCache.set(u.key,u.size)}));let a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let c=this.measurementsCache.slice(0,a);for(let u=a;u<t;u++){let p=i(u),d=this.options.lanes===1?c[u-1]:this.getFurthestMeasurement(c,u),f=d?d.end+this.options.gap:o+r,m=l.get(p),T=typeof m=="number"?m:this.options.estimateSize(u),b=f+T,g=d?d.lane:u%this.options.lanes;c[u]={index:u,start:f,size:T,end:b,key:p,lane:g}}return this.measurementsCache=c,c},{key:!1,debug:()=>this.options.debug}),this.calculateRange=sn(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(t,o,r,i)=>this.range=t.length>0&&o>0?kc({measurements:t,outerSize:o,scrollOffset:r,lanes:i}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=sn(()=>{let t=null,o=null,r=this.calculateRange();return r&&(t=r.startIndex,o=r.endIndex),this.maybeNotify.updateDeps([this.isScrolling,t,o]),[this.options.rangeExtractor,this.options.overscan,this.options.count,t,o]},(t,o,r,i,s)=>i===null||s===null?[]:t({startIndex:i,endIndex:s,overscan:o,count:r}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=t=>{let o=this.options.indexAttribute,r=t.getAttribute(o);return r?parseInt(r,10):(console.warn(`Missing attribute name '${o}={index}' on measured element.`),-1)},this._measureElement=(t,o)=>{let r=this.indexFromElement(t),i=this.measurementsCache[r];if(!i)return;let s=i.key,l=this.elementsCache.get(s);l!==t&&(l&&this.observer.unobserve(l),this.observer.observe(t),this.elementsCache.set(s,t)),t.isConnected&&this.resizeItem(r,this.options.measureElement(t,o,this))},this.resizeItem=(t,o)=>{var l;let r=this.measurementsCache[t];if(!r)return;let i=(l=this.itemSizeCache.get(r.key))!=null?l:r.size,s=o-i;s!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(r,s,this):r.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=s,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(r.index),this.itemSizeCache=new Map(this.itemSizeCache.set(r.key,o)),this.notify(!1))},this.measureElement=t=>{if(!t){this.elementsCache.forEach((o,r)=>{o.isConnected||(this.observer.unobserve(o),this.elementsCache.delete(r))});return}this._measureElement(t,void 0)},this.getVirtualItems=sn(()=>[this.getVirtualIndexes(),this.getMeasurements()],(t,o)=>{let r=[];for(let i=0,s=t.length;i<s;i++){let l=t[i],a=o[l];r.push(a)}return r},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=t=>{let o=this.getMeasurements();if(o.length!==0)return ui(o[nl(0,o.length-1,r=>ui(o[r]).start,t)])},this.getOffsetForAlignment=(t,o,r=0)=>{let i=this.getSize(),s=this.getScrollOffset();o==="auto"&&(o=t>=s+i?"end":"start"),o==="center"?t+=(r-i)/2:o==="end"&&(t-=i);let l=this.getTotalSize()-i;return Math.max(Math.min(l,t),0)},this.getOffsetForIndex=(t,o="auto")=>{t=Math.max(0,Math.min(t,this.options.count-1));let r=this.measurementsCache[t];if(!r)return;let i=this.getSize(),s=this.getScrollOffset();if(o==="auto")if(r.end>=s+i-this.options.scrollPaddingEnd)o="end";else if(r.start<=s+this.options.scrollPaddingStart)o="start";else return[s,o];let l=o==="end"?r.end+this.options.scrollPaddingEnd:r.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(l,o,r.size),o]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(t,{align:o="start",behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(t,o),{adjustments:void 0,behavior:r})},this.scrollToIndex=(t,{align:o="auto",behavior:r}={})=>{t=Math.max(0,Math.min(t,this.options.count-1)),this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(t,o);if(!i)return;let[s,l]=i;this._scrollToOffset(s,{adjustments:void 0,behavior:r}),r!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(t))){let c=this.getOffsetForIndex(t,l);if(!c)return;let[u]=c,p=this.getScrollOffset();Xs(u,p)||this.scrollToIndex(t,{align:l,behavior:r})}else this.scrollToIndex(t,{align:l,behavior:r})}))},this.scrollBy=(t,{behavior:o}={})=>{this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+t,{adjustments:void 0,behavior:o})},this.getTotalSize=()=>{var i;var t;let o=this.getMeasurements(),r;if(o.length===0)r=this.options.paddingStart;else if(this.options.lanes===1)r=(i=(t=o[o.length-1])==null?void 0:t.end)!=null?i:0;else{let s=Array(this.options.lanes).fill(null),l=o.length-1;for(;l>=0&&s.some(a=>a===null);){let a=o[l];s[a.lane]===null&&(s[a.lane]=a.end),l--}r=Math.max(...s.filter(a=>a!==null))}return Math.max(r-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(t,{adjustments:o,behavior:r})=>{this.options.scrollToFn(t,{behavior:r,adjustments:o},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(n)}},nl=(e,n,t,o)=>{for(;e<=n;){let r=(e+n)/2|0,i=t(r);if(i<o)e=r+1;else if(i>o)n=r-1;else return r}return e>0?e-1:0};function kc({measurements:e,outerSize:n,scrollOffset:t,lanes:o}){let r=e.length-1,i=a=>e[a].start;if(e.length<=o)return{startIndex:0,endIndex:r};let s=nl(0,r,i,t),l=s;if(o===1)for(;l<r&&e[l].end<t+n;)l++;else if(o>1){let a=Array(o).fill(0);for(;l<r&&a.some(u=>u<t+n);){let u=e[l];a[u.lane]=u.end,l++}let c=Array(o).fill(t+n);for(;s>=0&&c.some(u=>u>=t);){let u=e[s];c[u.lane]=u.start,s--}s=Math.max(0,s-s%o),l=Math.min(r,l+(o-1-l%o))}return{startIndex:s,endIndex:l}}var ol=typeof document!="undefined"?Vt.useLayoutEffect:Vt.useEffect;function Hc(e){let n=Vt.useReducer(()=>({}),{})[1],t={...e,onChange:(r,i)=>{var s;i?(0,rl.flushSync)(n):n(),(s=e.onChange)==null||s.call(e,r,i)}},[o]=Vt.useState(()=>new or(t));return o.setOptions(t),ol(()=>o._didMount(),[]),ol(()=>o._willUpdate()),o}function il(e){return Hc({observeElementRect:Zs,observeElementOffset:el,scrollToFn:tl,...e})}var Z=le(require("react"),1),Tn=require("react-dom");var sl=require("react");function Nc(e,n){return e!==null&&n!==null&&typeof e=="object"&&typeof n=="object"&&"id"in e&&"id"in n?e.id===n.id:e===n}function Sn(e=Nc){return(0,sl.useCallback)((n,t)=>{if(typeof e=="string"){let o=e;return(n==null?void 0:n[o])===(t==null?void 0:t[o])}return e(n,t)},[e])}var rr=require("react");function Bc(e){if(e===null)return{width:0,height:0};let{width:n,height:t}=e.getBoundingClientRect();return{width:n,height:t}}function It(e,n=!1){let[t,o]=(0,rr.useReducer)(()=>({}),{}),r=(0,rr.useMemo)(()=>Bc(e),[e,t]);return V(()=>{if(!e)return;let i=new ResizeObserver(o);return i.observe(e),()=>{i.disconnect()}},[e]),n?{width:`${r.width}px`,height:`${r.height}px`}:r}var ir=require("react");var An=class extends Map{constructor(t){super();this.factory=t}get(t){let o=super.get(t);return o===void 0&&(o=this.factory(t),this.set(t,o)),o}};var ft,Cn,On,nt=class{constructor(n){Ho(this,ft,{});Ho(this,Cn,new An(()=>new Set));Ho(this,On,new Set);Oe(this,"disposables",he());Nr(this,ft,n)}dispose(){this.disposables.dispose()}get state(){return ze(this,ft)}subscribe(n,t){let o={selector:n,callback:t,current:n(ze(this,ft))};return ze(this,On).add(o),this.disposables.add(()=>{ze(this,On).delete(o)})}on(n,t){return ze(this,Cn).get(n).add(t),this.disposables.add(()=>{ze(this,Cn).get(n).delete(t)})}send(n){let t=this.reduce(ze(this,ft),n);if(t!==ze(this,ft)){Nr(this,ft,t);for(let o of ze(this,On)){let r=o.selector(ze(this,ft));fi(o.current,r)||(o.current=r,o.callback(r))}for(let o of ze(this,Cn).get(n.type))o(ze(this,ft),n)}}};ft=new WeakMap,Cn=new WeakMap,On=new WeakMap;function fi(e,n){return Object.is(e,n)?!0:typeof e!="object"||e===null||typeof n!="object"||n===null?!1:Array.isArray(e)&&Array.isArray(n)?e.length!==n.length?!1:ci(e[Symbol.iterator](),n[Symbol.iterator]()):e instanceof Map&&n instanceof Map||e instanceof Set&&n instanceof Set?e.size!==n.size?!1:ci(e.entries(),n.entries()):ll(e)&&ll(n)?ci(Object.entries(e)[Symbol.iterator](),Object.entries(n)[Symbol.iterator]()):!1}function ci(e,n){do{let t=e.next(),o=n.next();if(t.done&&o.done)return!0;if(t.done||o.done||!Object.is(t.value,o.value))return!1}while(!0)}function ll(e){if(Object.prototype.toString.call(e)!=="[object Object]")return!1;let n=Object.getPrototypeOf(e);return n===null||Object.getPrototypeOf(n)===null}function ln(e){let[n,t]=e(),o=he();return(...r)=>{n(...r),o.dispose(),o.microTask(t)}}var Gc={[0](e,n){let t=n.id,o=e.stack,r=e.stack.indexOf(t);if(r!==-1){let i=e.stack.slice();return i.splice(r,1),i.push(t),o=i,{...e,stack:o}}return{...e,stack:[...e.stack,t]}},[1](e,n){let t=n.id,o=e.stack.indexOf(t);if(o===-1)return e;let r=e.stack.slice();return r.splice(o,1),{...e,stack:r}}},mo=class extends nt{constructor(){super(...arguments);Oe(this,"actions",{push:t=>this.send({type:0,id:t}),pop:t=>this.send({type:1,id:t})});Oe(this,"selectors",{isTop:(t,o)=>t.stack[t.stack.length-1]===o,inStack:(t,o)=>t.stack.includes(o)})}static new(){return new mo({stack:[]})}reduce(t,o){return Y(o.type,Gc,t,o)}},$e=new An(()=>mo.new());var al=require("use-sync-external-store/with-selector");function ee(e,n,t=fi){return(0,al.useSyncExternalStoreWithSelector)(E(o=>e.subscribe(Uc,o)),E(()=>e.state),E(()=>e.state),E(n),t)}function Uc(e){return e}function Mt(e,n){let t=(0,ir.useId)(),o=$e.get(n),[r,i]=ee(o,(0,ir.useCallback)(s=>[o.selectors.isTop(s,t),o.selectors.inStack(s,t)],[o,t]));return V(()=>{if(e)return o.actions.push(t),()=>o.actions.pop(t)},[o,e,t]),e?i?r:!0:!1}var di=new Map,To=new Map;function ul(e){var t;let n=(t=To.get(e))!=null?t:0;return To.set(e,n+1),n!==0?()=>cl(e):(di.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0,()=>cl(e))}function cl(e){var o;let n=(o=To.get(e))!=null?o:1;if(n===1?To.delete(e):To.set(e,n-1),n!==1)return;let t=di.get(e);t&&(t["aria-hidden"]===null?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",t["aria-hidden"]),e.inert=t.inert,di.delete(e))}function Wt(e,{allowed:n,disallowed:t}={}){let o=Mt(e,"inert-others");V(()=>{var s,l;if(!o)return;let r=he();for(let a of(s=t==null?void 0:t())!=null?s:[])a&&r.add(ul(a));let i=(l=n==null?void 0:n())!=null?l:[];for(let a of i){if(!a)continue;let c=Pe(a);if(!c)continue;let u=a.parentElement;for(;u&&u!==c.body;){for(let p of u.children)i.some(d=>p.contains(d))||r.add(ul(p));u=u.parentElement}}return r.dispose},[o,n,t])}var fl=require("react");function vt(e,n,t){let o=me(r=>{let i=r.getBoundingClientRect();i.x===0&&i.y===0&&i.width===0&&i.height===0&&t()});(0,fl.useEffect)(()=>{if(!e)return;let r=n===null?null:Te(n)?n:n.current;if(!r)return;let i=he();if(typeof ResizeObserver!="undefined"){let s=new ResizeObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}if(typeof IntersectionObserver!="undefined"){let s=new IntersectionObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}return()=>i.dispose()},[n,o,e])}var yo=require("react");var bo=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),Vc=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");function an(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(bo)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function Wc(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Vc)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function Ft(e,n=0){var t;return e===((t=Pe(e))==null?void 0:t.body)?!1:Y(n,{[0](){return e.matches(bo)},[1](){let o=e;for(;o!==null;){if(o.matches(bo))return!0;o=o.parentElement}return!1}})}function pi(e){let n=Pe(e);he().nextFrame(()=>{n&&Le(n.activeElement)&&!Ft(n.activeElement,0)&&dt(e)})}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function dt(e){e==null||e.focus({preventScroll:!0})}var jc=["textarea","input"].join(",");function Kc(e){var n,t;return(t=(n=e==null?void 0:e.matches)==null?void 0:n.call(e,jc))!=null?t:!1}function Ue(e,n=t=>t){return e.slice().sort((t,o)=>{let r=n(t),i=n(o);if(r===null||i===null)return 0;let s=r.compareDocumentPosition(i);return s&Node.DOCUMENT_POSITION_FOLLOWING?-1:s&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function sr(e,n){return ve(an(),n,{relativeTo:e})}function ve(e,n,{sorted:t=!0,relativeTo:o=null,skipElements:r=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?t?Ue(e):e:n&64?Wc(e):an(e);r.length>0&&s.length>1&&(s=s.filter(f=>!r.some(m=>m!=null&&"current"in m?(m==null?void 0:m.current)===f:m===f))),o=o!=null?o:i.activeElement;let l=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),a=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,s.indexOf(o))-1;if(n&4)return Math.max(0,s.indexOf(o))+1;if(n&8)return s.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=n&32?{preventScroll:!0}:{},u=0,p=s.length,d;do{if(u>=p||u+p<=0)return 0;let f=a+u;if(n&16)f=(f+p)%p;else{if(f<0)return 3;if(f>=p)return 1}d=s[f],d==null||d.focus(c),u+=l}while(d!==i.activeElement);return n&6&&Kc(d)&&d.select(),2}function mi(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function zc(){return/Android/gi.test(window.navigator.userAgent)}function go(){return mi()||zc()}var dl=require("react");function jt(e,n,t,o){let r=me(t);(0,dl.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return document.addEventListener(n,i,o),()=>document.removeEventListener(n,i,o)},[e,n,o])}var pl=require("react");function lr(e,n,t,o){let r=me(t);(0,pl.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return window.addEventListener(n,i,o),()=>window.removeEventListener(n,i,o)},[e,n,o])}var ml=30;function Et(e,n,t){let o=me(t),r=(0,yo.useCallback)(function(a,c){if(a.defaultPrevented)return;let u=c(a);if(u===null||!u.getRootNode().contains(u)||!u.isConnected)return;let p=function d(f){return typeof f=="function"?d(f()):Array.isArray(f)||f instanceof Set?f:[f]}(n);for(let d of p)if(d!==null&&(d.contains(u)||a.composed&&a.composedPath().includes(d)))return;return!Ft(u,1)&&u.tabIndex!==-1&&a.preventDefault(),o.current(a,u)},[o,n]),i=(0,yo.useRef)(null);jt(e,"pointerdown",l=>{var a,c;go()||(i.current=((c=(a=l.composedPath)==null?void 0:a.call(l))==null?void 0:c[0])||l.target)},!0),jt(e,"pointerup",l=>{if(go()||!i.current)return;let a=i.current;return i.current=null,r(l,()=>a)},!0);let s=(0,yo.useRef)({x:0,y:0});jt(e,"touchstart",l=>{s.current.x=l.touches[0].clientX,s.current.y=l.touches[0].clientY},!0),jt(e,"touchend",l=>{let a={x:l.changedTouches[0].clientX,y:l.changedTouches[0].clientY};if(!(Math.abs(a.x-s.current.x)>=ml||Math.abs(a.y-s.current.y)>=ml))return r(l,()=>Le(l.target)?l.target:null)},!0),lr(e,"blur",l=>r(l,()=>Gs(window.document.activeElement)?window.document.activeElement:null),!0)}var Tl=require("react");function Re(...e){return(0,Tl.useMemo)(()=>Pe(...e),[...e])}var bl=require("react");var Ge={Ignore:{kind:0},Select:e=>({kind:1,target:e}),Close:{kind:2}},Xc=200;function Dn(e,{trigger:n,action:t,close:o,select:r}){let i=(0,bl.useRef)(null);jt(e&&n!==null,"pointerdown",s=>{fo(s==null?void 0:s.target)&&n!=null&&n.contains(s.target)&&(i.current=new Date)}),jt(e&&n!==null,"pointerup",s=>{if(i.current===null||!Le(s.target))return;let l=t(s),a=new Date().getTime()-i.current.getTime();switch(i.current=null,l.kind){case 0:return;case 1:{a>Xc&&(r(l.target),o());break}case 2:{o();break}}},{capture:!0})}var yl=require("react");var gl=require("react");function Kt(e,n,t,o){let r=me(t);(0,gl.useEffect)(()=>{e=e!=null?e:window;function i(s){r.current(s)}return e.addEventListener(n,i,o),()=>e.removeEventListener(n,i,o)},[e,n,o])}function bi(e){let n=(0,yl.useRef)({value:"",selectionStart:null,selectionEnd:null});return Kt(e,"blur",t=>{let o=t.target;on(o)&&(n.current={value:o.value,selectionStart:o.selectionStart,selectionEnd:o.selectionEnd})}),E(()=>{if(document.activeElement!==e&&on(e)&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==n.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:t,selectionEnd:o}=n.current;t!==null&&o!==null&&e.setSelectionRange(t,o)}n.current={value:"",selectionStart:null,selectionEnd:null}}})}var hl=require("react");function je(e,n){return(0,hl.useMemo)(()=>{var o;if(e.type)return e.type;let t=(o=e.as)!=null?o:"button";if(typeof t=="string"&&t.toLowerCase()==="button"||(n==null?void 0:n.tagName)==="BUTTON"&&!n.hasAttribute("type"))return"button"},[e.type,e.as,n])}var vl=require("react");function El(e){return(0,vl.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}function xl(e,n){let t=e(),o=new Set;return{getSnapshot(){return t},subscribe(r){return o.add(r),()=>o.delete(r)},dispatch(r,...i){let s=n[r].call(t,...i);s&&(t=s,o.forEach(l=>l()))}}}function Pl(){let e;return{before({doc:n}){var r;let t=n.documentElement,o=(r=n.defaultView)!=null?r:window;e=Math.max(0,o.innerWidth-t.clientWidth)},after({doc:n,d:t}){let o=n.documentElement,r=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,e-r);t.style(o,"paddingRight",`${i}px`)}}}function Rl(){return mi()?{before({doc:e,d:n,meta:t}){function o(r){return t.containers.flatMap(i=>i()).some(i=>i.contains(r))}n.microTask(()=>{var s;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let l=he();l.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>l.dispose()))}let r=(s=window.scrollY)!=null?s:window.pageYOffset,i=null;n.addEventListener(e,"click",l=>{if(Le(l.target))try{let a=l.target.closest("a");if(!a)return;let{hash:c}=new URL(a.href),u=e.querySelector(c);Le(u)&&!o(u)&&(i=u)}catch{}},!0),n.addEventListener(e,"touchstart",l=>{if(Le(l.target)&&Bs(l.target))if(o(l.target)){let a=l.target;for(;a.parentElement&&o(a.parentElement);)a=a.parentElement;n.style(a,"overscrollBehavior","contain")}else n.style(l.target,"touchAction","none")}),n.addEventListener(e,"touchmove",l=>{if(Le(l.target)){if(on(l.target))return;if(o(l.target)){let a=l.target;for(;a.parentElement&&a.dataset.headlessuiPortal!==""&&!(a.scrollHeight>a.clientHeight||a.scrollWidth>a.clientWidth);)a=a.parentElement;a.dataset.headlessuiPortal===""&&l.preventDefault()}else l.preventDefault()}},{passive:!1}),n.add(()=>{var a;let l=(a=window.scrollY)!=null?a:window.pageYOffset;r!==l&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function Sl(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function Yc(e){let n={};for(let t of e)Object.assign(n,t(n));return n}var zt=xl(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:he(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:Yc(t)},r=[Rl(),Pl(),Sl()];r.forEach(({before:i})=>i==null?void 0:i(o)),r.forEach(({after:i})=>i==null?void 0:i(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});zt.subscribe(()=>{let e=zt.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)==="hidden",r=t.count!==0;(r&&!o||!r&&o)&&zt.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&zt.dispatch("TEARDOWN",t)}});function Al(e,n,t=()=>({containers:[]})){let o=El(zt),r=n?o.get(n):void 0,i=r?r.count>0:!1;return V(()=>{if(!(!n||!e))return zt.dispatch("PUSH",n,t),()=>zt.dispatch("POP",n,t)},[e,n]),i}function xt(e,n,t=()=>[document.body]){let o=Mt(e,"scroll-lock");Al(o,n,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],t]}})}var Ol=require("react");function Cl(e){return[e.screenX,e.screenY]}function Ln(){let e=(0,Ol.useRef)([-1,-1]);return{wasMoved(n){let t=Cl(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=Cl(n)}}}var ho=require("react");var Xt=require("react");function Dl(e=0){let[n,t]=(0,Xt.useState)(e),o=(0,Xt.useCallback)(a=>t(a),[n]),r=(0,Xt.useCallback)(a=>t(c=>c|a),[n]),i=(0,Xt.useCallback)(a=>(n&a)===a,[n]),s=(0,Xt.useCallback)(a=>t(c=>c&~a),[t]),l=(0,Xt.useCallback)(a=>t(c=>c^a),[t]);return{flags:n,setFlag:o,addFlag:r,hasFlag:i,removeFlag:s,toggleFlag:l}}var Ll,Il;typeof process!="undefined"&&typeof globalThis!="undefined"&&typeof Element!="undefined"&&((Ll=process==null?void 0:process.env)==null?void 0:Ll["NODE_ENV"])==="test"&&typeof((Il=Element==null?void 0:Element.prototype)==null?void 0:Il.getAnimations)=="undefined"&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});function Je(e){let n={};for(let t in e)e[t]===!0&&(n[`data-${t}`]="");return n}function Qe(e,n,t,o){let[r,i]=(0,ho.useState)(t),{hasFlag:s,addFlag:l,removeFlag:a}=Dl(e&&r?3:0),c=(0,ho.useRef)(!1),u=(0,ho.useRef)(!1),p=Se();return V(()=>{var d;if(e){if(t&&i(!0),!n){t&&l(3);return}return(d=o==null?void 0:o.start)==null||d.call(o,t),qc(n,{inFlight:c,prepare(){u.current?u.current=!1:u.current=c.current,c.current=!0,!u.current&&(t?(l(3),a(4)):(l(4),a(2)))},run(){u.current?t?(a(3),l(4)):(a(4),l(3)):t?a(1):l(1)},done(){var f;u.current&&typeof n.getAnimations=="function"&&n.getAnimations().length>0||(c.current=!1,a(7),t||i(!1),(f=o==null?void 0:o.end)==null||f.call(o,t))}})}},[e,t,n,p]),e?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[t,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function qc(e,{prepare:n,run:t,done:o,inFlight:r}){let i=he();return Qc(e,{prepare:n,inFlight:r}),i.nextFrame(()=>{t(),i.requestAnimationFrame(()=>{i.add(Jc(e,o))})}),i.dispose}function Jc(e,n){var i,s;let t=he();if(!e)return t.dispose;let o=!1;t.add(()=>{o=!0});let r=(s=(i=e.getAnimations)==null?void 0:i.call(e).filter(l=>l instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),t.dispose):(Promise.allSettled(r.map(l=>l.finished)).then(()=>{o||n()}),t.dispose)}function Qc(e,{inFlight:n,prepare:t}){if(n!=null&&n.current){t();return}let o=e.style.transition;e.style.transition="none",t(),e.offsetHeight,e.style.transition=o}var vo=require("react");function ar(e,{container:n,accept:t,walk:o}){let r=(0,vo.useRef)(t),i=(0,vo.useRef)(o);(0,vo.useEffect)(()=>{r.current=t,i.current=o},[t,o]),V(()=>{if(!n||!e)return;let s=Pe(n);if(!s)return;let l=r.current,a=i.current,c=Object.assign(p=>l(p),{acceptNode:l}),u=s.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,c,!1);for(;u.nextNode();)a(u.currentNode)},[n,e,r,i])}var ur=require("react");function un(e,n){let t=(0,ur.useRef)([]),o=E(e);(0,ur.useEffect)(()=>{let r=[...t.current];for(let[i,s]of n.entries())if(t.current[i]!==s){let l=o(n,r);return t.current=n,l}},[o,...n])}var te=le(require("react"),1),Oo=require("react");function Ml(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Eo(e){return e instanceof Element||e instanceof Ml(e).Element}function Fl(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(n=>{let{brand:t,version:o}=n;return t+"/"+o}).join(" "):navigator.userAgent}var Yt=Math.min,Ve=Math.max,Po=Math.round,Ro=Math.floor,wt=e=>({x:e,y:e}),Zc={left:"right",right:"left",bottom:"top",top:"bottom"},ef={start:"end",end:"start"};function gi(e,n,t){return Ve(e,Yt(n,t))}function In(e,n){return typeof e=="function"?e(n):e}function _t(e){return e.split("-")[0]}function Mn(e){return e.split("-")[1]}function yi(e){return e==="x"?"y":"x"}function hi(e){return e==="y"?"height":"width"}function Fn(e){return["top","bottom"].includes(_t(e))?"y":"x"}function vi(e){return yi(Fn(e))}function wl(e,n,t){t===void 0&&(t=!1);let o=Mn(e),r=vi(e),i=hi(r),s=r==="x"?o===(t?"end":"start")?"right":"left":o==="start"?"bottom":"top";return n.reference[i]>n.floating[i]&&(s=xo(s)),[s,xo(s)]}function _l(e){let n=xo(e);return[cr(e),n,cr(n)]}function cr(e){return e.replace(/start|end/g,n=>ef[n])}function tf(e,n,t){let o=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return t?n?r:o:n?o:r;case"left":case"right":return n?i:s;default:return[]}}function $l(e,n,t,o){let r=Mn(e),i=tf(_t(e),t==="start",o);return r&&(i=i.map(s=>s+"-"+r),n&&(i=i.concat(i.map(cr)))),i}function xo(e){return e.replace(/left|right|bottom|top/g,n=>Zc[n])}function nf(e){return{top:0,right:0,bottom:0,left:0,...e}}function kl(e){return typeof e!="number"?nf(e):{top:e,right:e,bottom:e,left:e}}function cn(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function Hl(e,n,t){let{reference:o,floating:r}=e,i=Fn(n),s=vi(n),l=hi(s),a=_t(n),c=i==="y",u=o.x+o.width/2-r.width/2,p=o.y+o.height/2-r.height/2,d=o[l]/2-r[l]/2,f;switch(a){case"top":f={x:u,y:o.y-r.height};break;case"bottom":f={x:u,y:o.y+o.height};break;case"right":f={x:o.x+o.width,y:p};break;case"left":f={x:o.x-r.width,y:p};break;default:f={x:o.x,y:o.y}}switch(Mn(n)){case"start":f[s]-=d*(t&&c?-1:1);break;case"end":f[s]+=d*(t&&c?-1:1);break}return f}var Nl=async(e,n,t)=>{let{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:s}=t,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(n)),c=await s.getElementRects({reference:e,floating:n,strategy:r}),{x:u,y:p}=Hl(c,o,a),d=o,f={},m=0;for(let T=0;T<l.length;T++){let{name:b,fn:g}=l[T],{x:h,y,data:v,reset:x}=await g({x:u,y:p,initialPlacement:o,placement:d,strategy:r,middlewareData:f,rects:c,platform:s,elements:{reference:e,floating:n}});if(u=h!=null?h:u,p=y!=null?y:p,f={...f,[b]:{...f[b],...v}},x&&m<=50){m++,typeof x=="object"&&(x.placement&&(d=x.placement),x.rects&&(c=x.rects===!0?await s.getElementRects({reference:e,floating:n,strategy:r}):x.rects),{x:u,y:p}=Hl(c,d,a)),T=-1;continue}}return{x:u,y:p,placement:d,strategy:r,middlewareData:f}};async function Pt(e,n){var t;n===void 0&&(n={});let{x:o,y:r,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:p="floating",altBoundary:d=!1,padding:f=0}=In(n,e),m=kl(f),b=l[d?p==="floating"?"reference":"floating":p],g=cn(await i.getClippingRect({element:(t=await(i.isElement==null?void 0:i.isElement(b)))==null||t?b:b.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:a})),h=p==="floating"?{...s.floating,x:o,y:r}:s.reference,y=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),v=await(i.isElement==null?void 0:i.isElement(y))?await(i.getScale==null?void 0:i.getScale(y))||{x:1,y:1}:{x:1,y:1},x=cn(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:h,offsetParent:y,strategy:a}):h);return{top:(g.top-x.top+m.top)/v.y,bottom:(x.bottom-g.bottom+m.bottom)/v.y,left:(g.left-x.left+m.left)/v.x,right:(x.right-g.right+m.right)/v.x}}var Ei=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,o;let{placement:r,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:c}=n,{mainAxis:u=!0,crossAxis:p=!0,fallbackPlacements:d,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:T=!0,...b}=In(e,n);if((t=i.arrow)!=null&&t.alignmentOffset)return{};let g=_t(r),h=_t(l)===l,y=await(a.isRTL==null?void 0:a.isRTL(c.floating)),v=d||(h||!T?[xo(l)]:_l(l));!d&&m!=="none"&&v.push(...$l(l,T,m,y));let x=[l,...v],R=await Pt(n,b),$=[],C=((o=i.flip)==null?void 0:o.overflows)||[];if(u&&$.push(R[g]),p){let L=wl(r,s,y);$.push(R[L[0]],R[L[1]])}if(C=[...C,{placement:r,overflows:$}],!$.every(L=>L<=0)){var B,S;let L=(((B=i.flip)==null?void 0:B.index)||0)+1,O=x[L];if(O)return{data:{index:L,overflows:C},reset:{placement:O}};let I=(S=C.filter(W=>W.overflows[0]<=0).sort((W,A)=>W.overflows[1]-A.overflows[1])[0])==null?void 0:S.placement;if(!I)switch(f){case"bestFit":{var P;let W=(P=C.map(A=>[A.placement,A.overflows.filter(N=>N>0).reduce((N,ne)=>N+ne,0)]).sort((A,N)=>A[1]-N[1])[0])==null?void 0:P[0];W&&(I=W);break}case"initialPlacement":I=l;break}if(r!==I)return{reset:{placement:I}}}return{}}}};async function of(e,n){let{placement:t,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),s=_t(t),l=Mn(t),a=Fn(t)==="y",c=["left","top"].includes(s)?-1:1,u=i&&a?-1:1,p=In(n,e),{mainAxis:d,crossAxis:f,alignmentAxis:m}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...p};return l&&typeof m=="number"&&(f=l==="end"?m*-1:m),a?{x:f*u,y:d*c}:{x:d*c,y:f*u}}var xi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var t,o;let{x:r,y:i,placement:s,middlewareData:l}=n,a=await of(n,e);return s===((t=l.offset)==null?void 0:t.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:r+a.x,y:i+a.y,data:{...a,placement:s}}}}},Pi=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){let{x:t,y:o,placement:r}=n,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:b=>{let{x:g,y:h}=b;return{x:g,y:h}}},...a}=In(e,n),c={x:t,y:o},u=await Pt(n,a),p=Fn(_t(r)),d=yi(p),f=c[d],m=c[p];if(i){let b=d==="y"?"top":"left",g=d==="y"?"bottom":"right",h=f+u[b],y=f-u[g];f=gi(h,f,y)}if(s){let b=p==="y"?"top":"left",g=p==="y"?"bottom":"right",h=m+u[b],y=m-u[g];m=gi(h,m,y)}let T=l.fn({...n,[d]:f,[p]:m});return{...T,data:{x:T.x-t,y:T.y-o}}}}};var Ri=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){let{placement:t,rects:o,platform:r,elements:i}=n,{apply:s=()=>{},...l}=In(e,n),a=await Pt(n,l),c=_t(t),u=Mn(t),p=Fn(t)==="y",{width:d,height:f}=o.floating,m,T;c==="top"||c==="bottom"?(m=c,T=u===(await(r.isRTL==null?void 0:r.isRTL(i.floating))?"start":"end")?"left":"right"):(T=c,m=u==="end"?"top":"bottom");let b=f-a[m],g=d-a[T],h=!n.middlewareData.shift,y=b,v=g;if(p){let R=d-a.left-a.right;v=u||h?Yt(g,R):R}else{let R=f-a.top-a.bottom;y=u||h?Yt(b,R):R}if(h&&!u){let R=Ve(a.left,0),$=Ve(a.right,0),C=Ve(a.top,0),B=Ve(a.bottom,0);p?v=d-2*(R!==0||$!==0?R+$:Ve(a.left,a.right)):y=f-2*(C!==0||B!==0?C+B:Ve(a.top,a.bottom))}await s({...n,availableWidth:v,availableHeight:y});let x=await r.getDimensions(i.floating);return d!==x.width||f!==x.height?{reset:{rects:!0}}:{}}}};function kt(e){return Gl(e)?(e.nodeName||"").toLowerCase():"#document"}function Ke(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Rt(e){var n;return(n=(Gl(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function Gl(e){return e instanceof Node||e instanceof Ke(e).Node}function St(e){return e instanceof Element||e instanceof Ke(e).Element}function pt(e){return e instanceof HTMLElement||e instanceof Ke(e).HTMLElement}function Bl(e){return typeof ShadowRoot=="undefined"?!1:e instanceof ShadowRoot||e instanceof Ke(e).ShadowRoot}function wn(e){let{overflow:n,overflowX:t,overflowY:o,display:r}=Ze(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(r)}function Ul(e){return["table","td","th"].includes(kt(e))}function fr(e){let n=dr(),t=Ze(e);return t.transform!=="none"||t.perspective!=="none"||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(t.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(t.contain||"").includes(o))}function Vl(e){let n=fn(e);for(;pt(n)&&!So(n);){if(fr(n))return n;n=fn(n)}return null}function dr(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function So(e){return["html","body","#document"].includes(kt(e))}function Ze(e){return Ke(e).getComputedStyle(e)}function Ao(e){return St(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function fn(e){if(kt(e)==="html")return e;let n=e.assignedSlot||e.parentNode||Bl(e)&&e.host||Rt(e);return Bl(n)?n.host:n}function Wl(e){let n=fn(e);return So(n)?e.ownerDocument?e.ownerDocument.body:e.body:pt(n)&&wn(n)?n:Wl(n)}function $t(e,n,t){var o;n===void 0&&(n=[]),t===void 0&&(t=!0);let r=Wl(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),s=Ke(r);return i?n.concat(s,s.visualViewport||[],wn(r)?r:[],s.frameElement&&t?$t(s.frameElement):[]):n.concat(r,$t(r,[],t))}function zl(e){let n=Ze(e),t=parseFloat(n.width)||0,o=parseFloat(n.height)||0,r=pt(e),i=r?e.offsetWidth:t,s=r?e.offsetHeight:o,l=Po(t)!==i||Po(o)!==s;return l&&(t=i,o=s),{width:t,height:o,$:l}}function Si(e){return St(e)?e:e.contextElement}function _n(e){let n=Si(e);if(!pt(n))return wt(1);let t=n.getBoundingClientRect(),{width:o,height:r,$:i}=zl(n),s=(i?Po(t.width):t.width)/o,l=(i?Po(t.height):t.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}var cf=wt(0);function Xl(e){let n=Ke(e);return!dr()||!n.visualViewport?cf:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function ff(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==Ke(e)?!1:n}function dn(e,n,t,o){n===void 0&&(n=!1),t===void 0&&(t=!1);let r=e.getBoundingClientRect(),i=Si(e),s=wt(1);n&&(o?St(o)&&(s=_n(o)):s=_n(e));let l=ff(i,t,o)?Xl(i):wt(0),a=(r.left+l.x)/s.x,c=(r.top+l.y)/s.y,u=r.width/s.x,p=r.height/s.y;if(i){let d=Ke(i),f=o&&St(o)?Ke(o):o,m=d.frameElement;for(;m&&o&&f!==d;){let T=_n(m),b=m.getBoundingClientRect(),g=Ze(m),h=b.left+(m.clientLeft+parseFloat(g.paddingLeft))*T.x,y=b.top+(m.clientTop+parseFloat(g.paddingTop))*T.y;a*=T.x,c*=T.y,u*=T.x,p*=T.y,a+=h,c+=y,m=Ke(m).frameElement}}return cn({width:u,height:p,x:a,y:c})}function df(e){let{rect:n,offsetParent:t,strategy:o}=e,r=pt(t),i=Rt(t);if(t===i)return n;let s={scrollLeft:0,scrollTop:0},l=wt(1),a=wt(0);if((r||!r&&o!=="fixed")&&((kt(t)!=="body"||wn(i))&&(s=Ao(t)),pt(t))){let c=dn(t);l=_n(t),a.x=c.x+t.clientLeft,a.y=c.y+t.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-s.scrollLeft*l.x+a.x,y:n.y*l.y-s.scrollTop*l.y+a.y}}function pf(e){return Array.from(e.getClientRects())}function Yl(e){return dn(Rt(e)).left+Ao(e).scrollLeft}function mf(e){let n=Rt(e),t=Ao(e),o=e.ownerDocument.body,r=Ve(n.scrollWidth,n.clientWidth,o.scrollWidth,o.clientWidth),i=Ve(n.scrollHeight,n.clientHeight,o.scrollHeight,o.clientHeight),s=-t.scrollLeft+Yl(e),l=-t.scrollTop;return Ze(o).direction==="rtl"&&(s+=Ve(n.clientWidth,o.clientWidth)-r),{width:r,height:i,x:s,y:l}}function Tf(e,n){let t=Ke(e),o=Rt(e),r=t.visualViewport,i=o.clientWidth,s=o.clientHeight,l=0,a=0;if(r){i=r.width,s=r.height;let c=dr();(!c||c&&n==="fixed")&&(l=r.offsetLeft,a=r.offsetTop)}return{width:i,height:s,x:l,y:a}}function bf(e,n){let t=dn(e,!0,n==="fixed"),o=t.top+e.clientTop,r=t.left+e.clientLeft,i=pt(e)?_n(e):wt(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=r*i.x,c=o*i.y;return{width:s,height:l,x:a,y:c}}function jl(e,n,t){let o;if(n==="viewport")o=Tf(e,t);else if(n==="document")o=mf(Rt(e));else if(St(n))o=bf(n,t);else{let r=Xl(e);o={...n,x:n.x-r.x,y:n.y-r.y}}return cn(o)}function ql(e,n){let t=fn(e);return t===n||!St(t)||So(t)?!1:Ze(t).position==="fixed"||ql(t,n)}function gf(e,n){let t=n.get(e);if(t)return t;let o=$t(e,[],!1).filter(l=>St(l)&&kt(l)!=="body"),r=null,i=Ze(e).position==="fixed",s=i?fn(e):e;for(;St(s)&&!So(s);){let l=Ze(s),a=fr(s);!a&&l.position==="fixed"&&(r=null),(i?!a&&!r:!a&&l.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||wn(s)&&!a&&ql(e,s))?o=o.filter(u=>u!==s):r=l,s=fn(s)}return n.set(e,o),o}function yf(e){let{element:n,boundary:t,rootBoundary:o,strategy:r}=e,s=[...t==="clippingAncestors"?gf(n,this._c):[].concat(t),o],l=s[0],a=s.reduce((c,u)=>{let p=jl(n,u,r);return c.top=Ve(p.top,c.top),c.right=Yt(p.right,c.right),c.bottom=Yt(p.bottom,c.bottom),c.left=Ve(p.left,c.left),c},jl(n,l,r));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function hf(e){return zl(e)}function vf(e,n,t){let o=pt(n),r=Rt(n),i=t==="fixed",s=dn(e,!0,i,n),l={scrollLeft:0,scrollTop:0},a=wt(0);if(o||!o&&!i)if((kt(n)!=="body"||wn(r))&&(l=Ao(n)),o){let c=dn(n,!0,i,n);a.x=c.x+n.clientLeft,a.y=c.y+n.clientTop}else r&&(a.x=Yl(r));return{x:s.left+l.scrollLeft-a.x,y:s.top+l.scrollTop-a.y,width:s.width,height:s.height}}function Kl(e,n){return!pt(e)||Ze(e).position==="fixed"?null:n?n(e):e.offsetParent}function Jl(e,n){let t=Ke(e);if(!pt(e))return t;let o=Kl(e,n);for(;o&&Ul(o)&&Ze(o).position==="static";)o=Kl(o,n);return o&&(kt(o)==="html"||kt(o)==="body"&&Ze(o).position==="static"&&!fr(o))?t:o||Vl(e)||t}var Ef=async function(e){let{reference:n,floating:t,strategy:o}=e,r=this.getOffsetParent||Jl,i=this.getDimensions;return{reference:vf(n,await r(t),o),floating:{x:0,y:0,...await i(t)}}};function xf(e){return Ze(e).direction==="rtl"}var pr={convertOffsetParentRelativeRectToViewportRelativeRect:df,getDocumentElement:Rt,getClippingRect:yf,getOffsetParent:Jl,getElementRects:Ef,getClientRects:pf,getDimensions:hf,getScale:_n,isElement:St,isRTL:xf};function Pf(e,n){let t=null,o,r=Rt(e);function i(){clearTimeout(o),t&&t.disconnect(),t=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();let{left:c,top:u,width:p,height:d}=e.getBoundingClientRect();if(l||n(),!p||!d)return;let f=Ro(u),m=Ro(r.clientWidth-(c+p)),T=Ro(r.clientHeight-(u+d)),b=Ro(c),h={rootMargin:-f+"px "+-m+"px "+-T+"px "+-b+"px",threshold:Ve(0,Yt(1,a))||1},y=!0;function v(x){let R=x[0].intersectionRatio;if(R!==a){if(!y)return s();R?s(!1,R):o=setTimeout(()=>{s(!1,1e-7)},100)}y=!1}try{t=new IntersectionObserver(v,{...h,root:r.ownerDocument})}catch{t=new IntersectionObserver(v,h)}t.observe(e)}return s(!0),i}function mr(e,n,t,o){o===void 0&&(o={});let{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,c=Si(e),u=r||i?[...c?$t(c):[],...$t(n)]:[];u.forEach(g=>{r&&g.addEventListener("scroll",t,{passive:!0}),i&&g.addEventListener("resize",t)});let p=c&&l?Pf(c,t):null,d=-1,f=null;s&&(f=new ResizeObserver(g=>{let[h]=g;h&&h.target===c&&f&&(f.unobserve(n),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{f&&f.observe(n)})),t()}),c&&!a&&f.observe(c),f.observe(n));let m,T=a?dn(e):null;a&&b();function b(){let g=dn(e);T&&(g.x!==T.x||g.y!==T.y||g.width!==T.width||g.height!==T.height)&&t(),T=g,m=requestAnimationFrame(b)}return t(),()=>{u.forEach(g=>{r&&g.removeEventListener("scroll",t),i&&g.removeEventListener("resize",t)}),p&&p(),f&&f.disconnect(),f=null,a&&cancelAnimationFrame(m)}}var Tr=(e,n,t)=>{let o=new Map,r={platform:pr,...t},i={...r.platform,_c:o};return Nl(e,n,{...r,platform:i})};var Ie=le(require("react"),1),yr=require("react"),ea=le(require("react-dom"),1),br=typeof document!="undefined"?yr.useLayoutEffect:yr.useEffect;function gr(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(typeof e=="function"&&e.toString()===n.toString())return!0;let t,o,r;if(e&&n&&typeof e=="object"){if(Array.isArray(e)){if(t=e.length,t!==n.length)return!1;for(o=t;o--!==0;)if(!gr(e[o],n[o]))return!1;return!0}if(r=Object.keys(e),t=r.length,t!==Object.keys(n).length)return!1;for(o=t;o--!==0;)if(!{}.hasOwnProperty.call(n,r[o]))return!1;for(o=t;o--!==0;){let i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!gr(e[i],n[i]))return!1}return!0}return e!==e&&n!==n}function ta(e){return typeof window=="undefined"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ql(e,n){let t=ta(e);return Math.round(n*t)/t}function Zl(e){let n=Ie.useRef(e);return br(()=>{n.current=e}),n}function na(e){e===void 0&&(e={});let{placement:n="bottom",strategy:t="absolute",middleware:o=[],platform:r,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:c}=e,[u,p]=Ie.useState({x:0,y:0,strategy:t,placement:n,middlewareData:{},isPositioned:!1}),[d,f]=Ie.useState(o);gr(d,o)||f(o);let[m,T]=Ie.useState(null),[b,g]=Ie.useState(null),h=Ie.useCallback(N=>{N!==R.current&&(R.current=N,T(N))},[]),y=Ie.useCallback(N=>{N!==$.current&&($.current=N,g(N))},[]),v=i||m,x=s||b,R=Ie.useRef(null),$=Ie.useRef(null),C=Ie.useRef(u),B=a!=null,S=Zl(a),P=Zl(r),L=Ie.useCallback(()=>{if(!R.current||!$.current)return;let N={placement:n,strategy:t,middleware:d};P.current&&(N.platform=P.current),Tr(R.current,$.current,N).then(ne=>{let q={...ne,isPositioned:!0};O.current&&!gr(C.current,q)&&(C.current=q,ea.flushSync(()=>{p(q)}))})},[d,n,t,P]);br(()=>{c===!1&&C.current.isPositioned&&(C.current.isPositioned=!1,p(N=>({...N,isPositioned:!1})))},[c]);let O=Ie.useRef(!1);br(()=>(O.current=!0,()=>{O.current=!1}),[]),br(()=>{if(v&&(R.current=v),x&&($.current=x),v&&x){if(S.current)return S.current(v,x,L);L()}},[v,x,L,S,B]);let I=Ie.useMemo(()=>({reference:R,floating:$,setReference:h,setFloating:y}),[h,y]),W=Ie.useMemo(()=>({reference:v,floating:x}),[v,x]),A=Ie.useMemo(()=>{let N={position:t,left:0,top:0};if(!W.floating)return N;let ne=Ql(W.floating,u.x),q=Ql(W.floating,u.y);return l?{...N,transform:"translate("+ne+"px, "+q+"px)",...ta(W.floating)>=1.5&&{willChange:"transform"}}:{position:t,left:ne,top:q}},[t,l,W.floating,u.x,u.y]);return Ie.useMemo(()=>({...u,update:L,refs:I,elements:W,floatingStyles:A}),[u,L,I,W,A])}var Co=(e,n)=>({...xi(e),options:[e,n]}),Ai=(e,n)=>({...Pi(e),options:[e,n]});var Ci=(e,n)=>({...Ei(e),options:[e,n]}),Oi=(e,n)=>({...Ri(e),options:[e,n]});var $n=require("react-dom");var aa={...te},Rf=aa.useInsertionEffect,Sf=Rf||(e=>e());function ua(e){let n=te.useRef(()=>{});return Sf(()=>{n.current=e}),te.useCallback(function(){for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return n.current==null?void 0:n.current(...o)},[])}var Af="ArrowUp",Cf="ArrowDown",Of="ArrowLeft",Df="ArrowRight";var Li=typeof document!="undefined"?Oo.useLayoutEffect:Oo.useEffect;var Lf=[Of,Df],If=[Af,Cf],Xh=[...Lf,...If];var oa=!1,Mf=0,ra=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Mf++;function Ff(){let[e,n]=te.useState(()=>oa?ra():void 0);return Li(()=>{e==null&&n(ra())},[]),te.useEffect(()=>{oa=!0},[]),e}var wf=aa.useId,_f=wf||Ff;function $f(){let e=new Map;return{emit(n,t){var o;(o=e.get(n))==null||o.forEach(r=>r(t))},on(n,t){e.set(n,[...e.get(n)||[],t])},off(n,t){var o;e.set(n,((o=e.get(n))==null?void 0:o.filter(r=>r!==t))||[])}}}var kf=te.createContext(null),Hf=te.createContext(null),Nf=()=>{var e;return((e=te.useContext(kf))==null?void 0:e.id)||null},Bf=()=>te.useContext(Hf);function Gf(e){let{open:n=!1,onOpenChange:t,elements:o}=e,r=_f(),i=te.useRef({}),[s]=te.useState(()=>$f()),l=Nf()!=null,[a,c]=te.useState(o.reference),u=ua((f,m,T)=>{i.current.openEvent=f?m:void 0,s.emit("openchange",{open:f,event:m,reason:T,nested:l}),t==null||t(f,m,T)}),p=te.useMemo(()=>({setPositionReference:c}),[]),d=te.useMemo(()=>({reference:a||o.reference||null,floating:o.floating||null,domReference:o.reference}),[a,o.reference,o.floating]);return te.useMemo(()=>({dataRef:i,open:n,onOpenChange:u,elements:d,events:s,floatingId:r,refs:p}),[n,u,d,s,r,p])}function ca(e){e===void 0&&(e={});let{nodeId:n}=e,t=Gf({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||t,r=o.elements,[i,s]=te.useState(null),[l,a]=te.useState(null),u=(r==null?void 0:r.reference)||i,p=te.useRef(null),d=Bf();Li(()=>{u&&(p.current=u)},[u]);let f=na({...e,elements:{...r,...l&&{reference:l}}}),m=te.useCallback(y=>{let v=Eo(y)?{getBoundingClientRect:()=>y.getBoundingClientRect(),contextElement:y}:y;a(v),f.refs.setReference(v)},[f.refs]),T=te.useCallback(y=>{(Eo(y)||y===null)&&(p.current=y,s(y)),(Eo(f.refs.reference.current)||f.refs.reference.current===null||y!==null&&!Eo(y))&&f.refs.setReference(y)},[f.refs]),b=te.useMemo(()=>({...f.refs,setReference:T,setPositionReference:m,domReference:p}),[f.refs,T,m]),g=te.useMemo(()=>({...f.elements,domReference:u}),[f.elements,u]),h=te.useMemo(()=>({...f,...o,refs:b,elements:g,nodeId:n}),[f,b,g,n,o]);return Li(()=>{o.dataRef.current.floatingContext=h;let y=d==null?void 0:d.nodesRef.current.find(v=>v.id===n);y&&(y.context=h)}),te.useMemo(()=>({...f,context:h,refs:b,elements:g}),[f,b,g,h])}var ia="active",sa="selected";function Di(e,n,t){let o=new Map,r=t==="item",i=e;if(r&&e){let{[ia]:s,[sa]:l,...a}=e;i=a}return{...t==="floating"&&{tabIndex:-1},...i,...n.map(s=>{let l=s?s[t]:null;return typeof l=="function"?e?l(e):null:l}).concat(e).reduce((s,l)=>(l&&Object.entries(l).forEach(a=>{let[c,u]=a;if(!(r&&[ia,sa].includes(c)))if(c.indexOf("on")===0){if(o.has(c)||o.set(c,[]),typeof u=="function"){var p;(p=o.get(c))==null||p.push(u),s[c]=function(){for(var d,f=arguments.length,m=new Array(f),T=0;T<f;T++)m[T]=arguments[T];return(d=o.get(c))==null?void 0:d.map(b=>b(...m)).find(b=>b!==void 0)}}}else s[c]=u}),s),{})}}function fa(e){e===void 0&&(e=[]);let n=e,t=te.useCallback(i=>Di(i,e,"reference"),n),o=te.useCallback(i=>Di(i,e,"floating"),n),r=te.useCallback(i=>Di(i,e,"item"),e.map(i=>i==null?void 0:i.item));return te.useMemo(()=>({getReferenceProps:t,getFloatingProps:o,getItemProps:r}),[t,o,r])}function la(e,n){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:n}}}}var da=e=>({name:"inner",options:e,async fn(n){let{listRef:t,overflowRef:o,onFallbackChange:r,offset:i=0,index:s=0,minItemsVisible:l=4,referenceOverflowThreshold:a=0,scrollRef:c,...u}=e,{rects:p,elements:{floating:d}}=n,f=t.current[s];if(!f)return{};let m={...n,...await Co(-f.offsetTop-d.clientTop-p.reference.height/2-f.offsetHeight/2-i).fn(n)},T=(c==null?void 0:c.current)||d,b=await Pt(la(m,T.scrollHeight),u),g=await Pt(m,{...u,elementContext:"reference"}),h=Math.max(0,b.top),y=m.y+h,v=Math.max(0,T.scrollHeight-h-Math.max(0,b.bottom));return T.style.maxHeight=v+"px",T.scrollTop=h,r&&(T.offsetHeight<f.offsetHeight*Math.min(l,t.current.length-1)-1||g.top>=-a||g.bottom>=-a?(0,$n.flushSync)(()=>r(!0)):(0,$n.flushSync)(()=>r(!1))),o&&(o.current=await Pt(la({...m,y},T.offsetHeight),u)),{y}}});function pa(e,n){let{open:t,elements:o}=e,{enabled:r=!0,overflowRef:i,scrollRef:s,onChange:l}=n,a=ua(l),c=te.useRef(!1),u=te.useRef(null),p=te.useRef(null);return te.useEffect(()=>{if(!r)return;function d(m){if(m.ctrlKey||!f||i.current==null)return;let T=m.deltaY,b=i.current.top>=-.5,g=i.current.bottom>=-.5,h=f.scrollHeight-f.clientHeight,y=T<0?-1:1,v=T<0?"max":"min";f.scrollHeight<=f.clientHeight||(!b&&T>0||!g&&T<0?(m.preventDefault(),(0,$n.flushSync)(()=>{a(x=>x+Math[v](T,h*y))})):/firefox/i.test(Fl())&&(f.scrollTop+=T))}let f=(s==null?void 0:s.current)||o.floating;if(t&&f)return f.addEventListener("wheel",d),requestAnimationFrame(()=>{u.current=f.scrollTop,i.current!=null&&(p.current={...i.current})}),()=>{u.current=null,p.current=null,f.removeEventListener("wheel",d)}},[r,t,o.floating,i,s,a]),te.useMemo(()=>r?{floating:{onKeyDown(){c.current=!0},onWheel(){c.current=!1},onPointerMove(){c.current=!1},onScroll(){let d=(s==null?void 0:s.current)||o.floating;if(!(!i.current||!d||!c.current)){if(u.current!==null){let f=d.scrollTop-u.current;(i.current.bottom<-.5&&f<-1||i.current.top<-.5&&f>1)&&(0,$n.flushSync)(()=>a(m=>m+f))}requestAnimationFrame(()=>{u.current=d.scrollTop})}}}}:{},[r,i,o.floating,s,a])}var Mi=le(require("react"),1),Ee=require("react");var kn=(0,Ee.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});kn.displayName="FloatingContext";var Fi=(0,Ee.createContext)(null);Fi.displayName="PlacementContext";function qt(e){return(0,Ee.useMemo)(()=>e?typeof e=="string"?{to:e}:e:null,[e])}function Jt(){return(0,Ee.useContext)(kn).setReference}function hr(){return(0,Ee.useContext)(kn).getReferenceProps}function Qt(){let{getFloatingProps:e,slot:n}=(0,Ee.useContext)(kn);return(0,Ee.useCallback)((...t)=>Object.assign({},e(...t),{"data-anchor":n.anchor}),[e,n])}function Zt(e=null){e===!1&&(e=null),typeof e=="string"&&(e={to:e});let n=(0,Ee.useContext)(Fi),t=(0,Ee.useMemo)(()=>e,[JSON.stringify(e,(r,i)=>{var s;return(s=i==null?void 0:i.outerHTML)!=null?s:i})]);V(()=>{n==null||n(t!=null?t:null)},[n,t]);let o=(0,Ee.useContext)(kn);return(0,Ee.useMemo)(()=>[o.setFloating,e?o.styles:{}],[o.setFloating,e,o.styles])}var ma=4;function en({children:e,enabled:n=!0}){let[t,o]=(0,Ee.useState)(null),[r,i]=(0,Ee.useState)(0),s=(0,Ee.useRef)(null),[l,a]=(0,Ee.useState)(null);Uf(l);let c=n&&t!==null&&l!==null,{to:u="bottom",gap:p=0,offset:d=0,padding:f=0,inner:m}=Vf(t,l),[T,b="center"]=u.split(" ");V(()=>{c&&i(0)},[c]);let{refs:g,floatingStyles:h,context:y}=ca({open:c,placement:T==="selection"?b==="center"?"bottom":`bottom-${b}`:b==="center"?`${T}`:`${T}-${b}`,strategy:"absolute",transform:!1,middleware:[Co({mainAxis:T==="selection"?0:p,crossAxis:d}),Ai({padding:f}),T!=="selection"&&Ci({padding:f}),T==="selection"&&m?da({...m,padding:f,overflowRef:s,offset:r,minItemsVisible:ma,referenceOverflowThreshold:f,onFallbackChange(P){var N,ne;if(!P)return;let L=y.elements.floating;if(!L)return;let O=parseFloat(getComputedStyle(L).scrollPaddingBottom)||0,I=Math.min(ma,L.childElementCount),W=0,A=0;for(let q of(ne=(N=y.elements.floating)==null?void 0:N.childNodes)!=null?ne:[])if(Te(q)){let _=q.offsetTop,G=_+q.clientHeight+O,U=L.scrollTop,X=U+L.clientHeight;if(_>=U&&G<=X)I--;else{A=Math.max(0,Math.min(G,X)-Math.max(_,U)),W=q.clientHeight;break}}I>=1&&i(q=>{let _=W*I-A+O;return q>=_?q:_})}}):null,Oi({padding:f,apply({availableWidth:P,availableHeight:L,elements:O}){Object.assign(O.floating.style,{overflow:"auto",maxWidth:`${P}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${L}px)`})}})].filter(Boolean),whileElementsMounted:mr}),[v=T,x=b]=y.placement.split("-");T==="selection"&&(v="selection");let R=(0,Ee.useMemo)(()=>({anchor:[v,x].filter(Boolean).join(" ")}),[v,x]),$=pa(y,{overflowRef:s,onChange:i}),{getReferenceProps:C,getFloatingProps:B}=fa([$]),S=E(P=>{a(P),g.setFloating(P)});return Mi.createElement(Fi.Provider,{value:o},Mi.createElement(kn.Provider,{value:{setFloating:S,setReference:g.setReference,styles:h,getReferenceProps:C,getFloatingProps:B,slot:R}},e))}function Uf(e){V(()=>{if(!e)return;let n=new MutationObserver(()=>{let t=window.getComputedStyle(e).maxHeight,o=parseFloat(t);if(isNaN(o))return;let r=parseInt(t);isNaN(r)||o!==r&&(e.style.maxHeight=`${Math.ceil(o)}px`)});return n.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{n.disconnect()}},[e])}function Vf(e,n){var i,s,l;let t=Ii((i=e==null?void 0:e.gap)!=null?i:"var(--anchor-gap, 0)",n),o=Ii((s=e==null?void 0:e.offset)!=null?s:"var(--anchor-offset, 0)",n),r=Ii((l=e==null?void 0:e.padding)!=null?l:"var(--anchor-padding, 0)",n);return{...e,gap:t,offset:o,padding:r}}function Ii(e,n,t=void 0){let o=Se(),r=E((a,c)=>{if(a==null)return[t,null];if(typeof a=="number")return[a,null];if(typeof a=="string"){if(!c)return[t,null];let u=Ta(a,c);return[u,p=>{let d=ba(a);{let f=d.map(m=>window.getComputedStyle(c).getPropertyValue(m));o.requestAnimationFrame(function m(){o.nextFrame(m);let T=!1;for(let[g,h]of d.entries()){let y=window.getComputedStyle(c).getPropertyValue(h);if(f[g]!==y){f[g]=y,T=!0;break}}if(!T)return;let b=Ta(a,c);u!==b&&(p(b),u=b)})}return o.dispose}]}return[t,null]}),i=(0,Ee.useMemo)(()=>r(e,n)[0],[e,n]),[s=i,l]=(0,Ee.useState)();return V(()=>{let[a,c]=r(e,n);if(l(a),!!c)return c(l)},[e,n]),s}function ba(e){let n=/var\((.*)\)/.exec(e);if(n){let t=n[1].indexOf(",");if(t===-1)return[n[1]];let o=n[1].slice(0,t).trim(),r=n[1].slice(t+1).trim();return r?[o,...ba(r)]:[o]}return[]}function Ta(e,n){let t=document.createElement("div");n.appendChild(t),t.style.setProperty("margin-top","0px","important"),t.style.setProperty("margin-top",e,"important");let o=parseFloat(window.getComputedStyle(t).marginTop)||0;return n.removeChild(t),o}var Do=le(require("react"),1);function ga({children:e,freeze:n}){let t=Hn(n,e);return Do.default.createElement(Do.default.Fragment,null,t)}function Hn(e,n){let[t,o]=(0,Do.useState)(n);return!e&&t!==n&&o(n),e?t:n}var pn=le(require("react"),1),vr=(0,pn.createContext)(null);vr.displayName="OpenClosedContext";function He(){return(0,pn.useContext)(vr)}function ot({value:e,children:n}){return pn.default.createElement(vr.Provider,{value:e},n)}function Nn({children:e}){return pn.default.createElement(vr.Provider,{value:null},e)}function ya(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",n),n())}var rt=[];ya(()=>{function e(n){if(!Le(n.target)||n.target===document.body||rt[0]===n.target)return;let t=n.target;t=t.closest(bo),rt.unshift(t!=null?t:n.target),rt=rt.filter(o=>o!=null&&o.isConnected),rt.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function Wf(e){throw new Error("Unexpected object: "+e)}function et(e,n){let t=n.resolveItems();if(t.length<=0)return null;let o=n.resolveActiveIndex(),r=o!=null?o:-1;switch(e.focus){case 0:{for(let i=0;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 1:{r===-1&&(r=t.length);for(let i=r-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 2:{for(let i=r+1;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 3:{for(let i=t.length-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 4:{for(let i=0;i<t.length;++i)if(n.resolveId(t[i],i,t)===e.id)return i;return o}case 5:return null;default:Wf(e)}}var ye=le(require("react"),1),Ea=require("react-dom");var Er=require("react");function it(e){let n=E(e),t=(0,Er.useRef)(!1);(0,Er.useEffect)(()=>(t.current=!1,()=>{t.current=!0,Dt(()=>{t.current&&n()})}),[n])}var mn=le(require("react"),1);function jf(){let e=typeof document=="undefined";return"useSyncExternalStore"in mn?(o=>o.useSyncExternalStore)(mn)(()=>()=>{},()=>!1,()=>!e):!1}function Ht(){let e=jf(),[n,t]=mn.useState(at.isHandoffComplete);return n&&at.isHandoffComplete===!1&&t(!1),mn.useEffect(()=>{n!==!0&&t(!0)},[n]),mn.useEffect(()=>at.handoff(),[]),e?!1:n}var Bn=le(require("react"),1),ha=(0,Bn.createContext)(!1);function va(){return(0,Bn.useContext)(ha)}function wi(e){return Bn.default.createElement(ha.Provider,{value:e.force},e.children)}function Kf(e){let n=va(),t=(0,ye.useContext)(Pa),[o,r]=(0,ye.useState)(()=>{var l;if(!n&&t!==null)return(l=t.current)!=null?l:null;if(at.isServer)return null;let i=e==null?void 0:e.getElementById("headlessui-portal-root");if(i)return i;if(e===null)return null;let s=e.createElement("div");return s.setAttribute("id","headlessui-portal-root"),e.body.appendChild(s)});return(0,ye.useEffect)(()=>{o!==null&&(e!=null&&e.body.contains(o)||e==null||e.body.appendChild(o))},[o,e]),(0,ye.useEffect)(()=>{n||t!==null&&r(t.current)},[t,r,n]),o}var xa=ye.Fragment,zf=w(function(n,t){let{ownerDocument:o=null,...r}=n,i=(0,ye.useRef)(null),s=K(Pn(T=>{i.current=T}),t),l=Re(i),a=o!=null?o:l,c=Kf(a),[u]=(0,ye.useState)(()=>{var T;return at.isServer?null:(T=a==null?void 0:a.createElement("div"))!=null?T:null}),p=(0,ye.useContext)(_i),d=Ht();V(()=>{!c||!u||c.contains(u)||(u.setAttribute("data-headlessui-portal",""),c.appendChild(u))},[c,u]),V(()=>{if(u&&p)return p.register(u)},[p,u]),it(()=>{var T;!c||!u||(fo(u)&&c.contains(u)&&c.removeChild(u),c.childNodes.length<=0&&((T=c.parentElement)==null||T.removeChild(c)))});let f=H();return d?!c||!u?null:(0,Ea.createPortal)(f({ourProps:{ref:s},theirProps:r,slot:{},defaultTag:xa,name:"Portal"}),u):null});function Xf(e,n){let t=K(n),{enabled:o=!0,ownerDocument:r,...i}=e,s=H();return o?ye.default.createElement(zf,{...i,ownerDocument:r,ref:t}):s({ourProps:{ref:t},theirProps:i,slot:{},defaultTag:xa,name:"Portal"})}var Yf=ye.Fragment,Pa=(0,ye.createContext)(null);function qf(e,n){let{target:t,...o}=e,i={ref:K(n)},s=H();return ye.default.createElement(Pa.Provider,{value:t},s({ourProps:i,theirProps:o,defaultTag:Yf,name:"Popover.Group"}))}var _i=(0,ye.createContext)(null);function xr(){let e=(0,ye.useContext)(_i),n=(0,ye.useRef)([]),t=E(i=>(n.current.push(i),e&&e.register(i),()=>o(i))),o=E(i=>{let s=n.current.indexOf(i);s!==-1&&n.current.splice(s,1),e&&e.unregister(i)}),r=(0,ye.useMemo)(()=>({register:t,unregister:o,portals:n}),[t,o,n]);return[n,(0,ye.useMemo)(()=>function({children:s}){return ye.default.createElement(_i.Provider,{value:r},s)},[r])]}var Jf=w(Xf),$i=w(qf),st=Object.assign(Jf,{Group:$i});function ki(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=n(e.options.slice()),r=o.length>0&&o[0].dataRef.current.order!==null?o.sort((s,l)=>s.dataRef.current.order-l.dataRef.current.order):Ue(o,s=>s.dataRef.current.domRef.current),i=t?r.indexOf(t):null;return i===-1&&(i=null),{options:r,activeOptionIndex:i}}var Qf={[1](e){var n;return(n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](e){var n,t;if((n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===0)return e;if((t=e.dataRef.current)!=null&&t.value){let o=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(o!==-1)return{...e,activeOptionIndex:o,comboboxState:0,__demoMode:!1}}return{...e,comboboxState:0,__demoMode:!1}},[3](e,n){return e.isTyping===n.isTyping?e:{...e,isTyping:n.isTyping}},[2](e,n){var i,s,l,a;if((i=e.dataRef.current)!=null&&i.disabled||e.optionsElement&&!((s=e.dataRef.current)!=null&&s.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let{options:c,disabled:u}=e.virtual,p=n.focus===4?n.idx:et(n,{resolveItems:()=>c,resolveActiveIndex:()=>{var f,m;return(m=(f=e.activeOptionIndex)!=null?f:c.findIndex(T=>!u(T)))!=null?m:null},resolveDisabled:u,resolveId(){throw new Error("Function not implemented.")}}),d=(l=n.trigger)!=null?l:2;return e.activeOptionIndex===p&&e.activationTrigger===d?e:{...e,activeOptionIndex:p,activationTrigger:d,isTyping:!1,__demoMode:!1}}let t=ki(e);if(t.activeOptionIndex===null){let c=t.options.findIndex(u=>!u.dataRef.current.disabled);c!==-1&&(t.activeOptionIndex=c)}let o=n.focus===4?n.idx:et(n,{resolveItems:()=>t.options,resolveActiveIndex:()=>t.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled}),r=(a=n.trigger)!=null?a:2;return e.activeOptionIndex===o&&e.activationTrigger===r?e:{...e,...t,isTyping:!1,activeOptionIndex:o,activationTrigger:r,__demoMode:!1}},[4]:(e,n)=>{var i,s,l,a;if((i=e.dataRef.current)!=null&&i.virtual)return{...e,options:[...e.options,n.payload]};let t=n.payload,o=ki(e,c=>(c.push(t),c));e.activeOptionIndex===null&&(l=(s=e.dataRef.current).isSelected)!=null&&l.call(s,n.payload.dataRef.current.value)&&(o.activeOptionIndex=o.options.indexOf(t));let r={...e,...o,activationTrigger:2};return(a=e.dataRef.current)!=null&&a.__demoMode&&e.dataRef.current.value===void 0&&(r.activeOptionIndex=0),r},[5]:(e,n)=>{var o;if((o=e.dataRef.current)!=null&&o.virtual)return{...e,options:e.options.filter(r=>r.id!==n.id)};let t=ki(e,r=>{let i=r.findIndex(s=>s.id===n.id);return i!==-1&&r.splice(i,1),r});return{...e,...t,activationTrigger:2}},[6]:(e,n)=>e.defaultToFirstOption===n.value?e:{...e,defaultToFirstOption:n.value},[7]:(e,n)=>e.activationTrigger===n.trigger?e:{...e,activationTrigger:n.trigger},[8]:(e,n)=>{var o,r;if(e.virtual===null)return{...e,virtual:{options:n.options,disabled:(o=n.disabled)!=null?o:()=>!1}};if(e.virtual.options===n.options&&e.virtual.disabled===n.disabled)return e;let t=e.activeOptionIndex;if(e.activeOptionIndex!==null){let i=n.options.indexOf(e.virtual.options[e.activeOptionIndex]);i!==-1?t=i:t=null}return{...e,activeOptionIndex:t,virtual:{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}}},[9]:(e,n)=>e.inputElement===n.element?e:{...e,inputElement:n.element},[10]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[11]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element}},Gn=class extends nt{constructor(t){super(t);Oe(this,"actions",{onChange:t=>{let{onChange:o,compare:r,mode:i,value:s}=this.state.dataRef.current;return Y(i,{[0]:()=>o==null?void 0:o(t),[1]:()=>{let l=s.slice(),a=l.findIndex(c=>r(c,t));return a===-1?l.push(t):l.splice(a,1),o==null?void 0:o(l)}})},registerOption:(t,o)=>(this.send({type:4,payload:{id:t,dataRef:o}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(o.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:t})}),goToOption:(t,o)=>(this.send({type:6,value:!1}),this.send({type:2,...t,trigger:o})),setIsTyping:t=>{this.send({type:3,isTyping:t})},closeCombobox:()=>{var t,o;this.send({type:1}),this.send({type:6,value:!1}),(o=(t=this.state.dataRef.current).onClose)==null||o.call(t)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:t=>{this.send({type:7,trigger:t})},selectActiveOption:()=>{let t=this.selectors.activeOptionIndex(this.state);if(t!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[t]);else{let{dataRef:o}=this.state.options[t];this.actions.onChange(o.current.value)}this.actions.goToOption({focus:4,idx:t})}},setInputElement:t=>{this.send({type:9,element:t})},setButtonElement:t=>{this.send({type:10,element:t})},setOptionsElement:t=>{this.send({type:11,element:t})}});Oe(this,"selectors",{activeDescendantId:t=>{var r,i;let o=this.selectors.activeOptionIndex(t);if(o!==null)return t.virtual?(i=t.options.find(s=>!s.dataRef.current.disabled&&t.dataRef.current.compare(s.dataRef.current.value,t.virtual.options[o])))==null?void 0:i.id:(r=t.options[o])==null?void 0:r.id},activeOptionIndex:t=>{if(t.defaultToFirstOption&&t.activeOptionIndex===null&&(t.virtual?t.virtual.options.length>0:t.options.length>0)){if(t.virtual){let{options:r,disabled:i}=t.virtual,s=r.findIndex(l=>{var a;return!((a=i==null?void 0:i(l))!=null&&a)});if(s!==-1)return s}let o=t.options.findIndex(r=>!r.dataRef.current.disabled);if(o!==-1)return o}return t.activeOptionIndex},activeOption:t=>{var r,i;let o=this.selectors.activeOptionIndex(t);return o===null?null:t.virtual?t.virtual.options[o!=null?o:0]:(i=(r=t.options[o])==null?void 0:r.dataRef.current.value)!=null?i:null},isActive:(t,o,r)=>{var s;let i=this.selectors.activeOptionIndex(t);return i===null?!1:t.virtual?i===t.dataRef.current.calculateIndex(o):((s=t.options[i])==null?void 0:s.id)===r},shouldScrollIntoView:(t,o,r)=>!(t.virtual||t.__demoMode||t.comboboxState!==0||t.activationTrigger===0||!this.selectors.isActive(t,o,r))});{let o=this.state.id,r=$e.get(null);this.disposables.add(r.on(0,i=>{!r.selectors.isTop(i,o)&&this.state.comboboxState===0&&this.actions.closeCombobox()})),this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,virtual:o=null,__demoMode:r=!1}){var i;return new Gn({id:t,dataRef:{current:{}},comboboxState:r?0:1,isTyping:!1,options:[],virtual:o?{options:o.options,disabled:(i=o.disabled)!=null?i:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:r})}reduce(t,o){return Y(o.type,Qf,t,o)}};var Un=require("react");var Hi=(0,Un.createContext)(null);function Vn(e){let n=(0,Un.useContext)(Hi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ni),t}return n}function Ni({id:e,virtual:n=null,__demoMode:t=!1}){let o=(0,Un.useMemo)(()=>Gn.new({id:e,virtual:n,__demoMode:t}),[]);return it(()=>o.dispose()),o}var Lo=(0,Z.createContext)(null);Lo.displayName="ComboboxDataContext";function jn(e){let n=(0,Z.useContext)(Lo);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,jn),t}return n}var Aa=(0,Z.createContext)(null);function Zf(e){let n=Vn("VirtualProvider"),t=jn("VirtualProvider"),{options:o}=t.virtual,r=ee(n,f=>f.optionsElement),[i,s]=(0,Z.useMemo)(()=>{let f=r;if(!f)return[0,0];let m=window.getComputedStyle(f);return[parseFloat(m.paddingBlockStart||m.paddingTop),parseFloat(m.paddingBlockEnd||m.paddingBottom)]},[r]),l=il({enabled:o.length!==0,scrollPaddingStart:i,scrollPaddingEnd:s,count:o.length,estimateSize(){return 40},getScrollElement(){return n.state.optionsElement},overscan:12}),[a,c]=(0,Z.useState)(0);V(()=>{c(f=>f+1)},[o]);let u=l.getVirtualItems(),p=ee(n,f=>f.activationTrigger===0),d=ee(n,n.selectors.activeOptionIndex);return u.length===0?null:Z.default.createElement(Aa.Provider,{value:l},Z.default.createElement("div",{style:{position:"relative",width:"100%",height:`${l.getTotalSize()}px`},ref:f=>{f&&(p||d!==null&&o.length>d&&l.scrollToIndex(d))}},u.map(f=>{var m;return Z.default.createElement(Z.Fragment,{key:f.key},Z.default.cloneElement((m=e.children)==null?void 0:m.call(e,{...e.slot,option:o[f.index]}),{key:`${a}-${f.key}`,"data-index":f.index,"aria-setsize":o.length,"aria-posinset":f.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${f.start}px)`,overflowAnchor:"none"}}))})))}var ed=Z.Fragment;function td(e,n){let t=(0,J.useId)(),o=ge(),{value:r,defaultValue:i,onChange:s,form:l,name:a,by:c,invalid:u=!1,disabled:p=o||!1,onClose:d,__demoMode:f=!1,multiple:m=!1,immediate:T=!1,virtual:b=null,nullable:g,...h}=e,y=gt(i),[v=m?[]:void 0,x]=bt(r,s,y),R=Ni({id:t,virtual:b,__demoMode:f}),$=(0,Z.useRef)({static:!1,hold:!1}),C=Sn(c),B=E(D=>b?c===null?b.options.indexOf(D):b.options.findIndex(z=>C(z,D)):R.state.options.findIndex(z=>C(z.dataRef.current.value,D))),S=(0,Z.useCallback)(D=>Y(O.mode,{[1]:()=>v.some(z=>C(z,D)),[0]:()=>C(v,D)}),[v]),P=ee(R,D=>D.virtual),L=E(()=>d==null?void 0:d()),O=(0,Z.useMemo)(()=>({__demoMode:f,immediate:T,optionsPropsRef:$,value:v,defaultValue:y,disabled:p,invalid:u,mode:m?1:0,virtual:b?P:null,onChange:x,isSelected:S,calculateIndex:B,compare:C,onClose:L}),[v,y,p,u,m,x,S,f,R,b,P,L]);V(()=>{var D;b&&R.send({type:8,options:b.options,disabled:(D=b.disabled)!=null?D:null})},[b,b==null?void 0:b.options,b==null?void 0:b.disabled]),V(()=>{R.state.dataRef.current=O},[O]);let[I,W,A,N]=ee(R,D=>[D.comboboxState,D.buttonElement,D.inputElement,D.optionsElement]),ne=$e.get(null),q=ee(ne,(0,Z.useCallback)(D=>ne.selectors.isTop(D,t),[ne,t]));Et(q,[W,A,N],()=>R.actions.closeCombobox());let _=ee(R,R.selectors.activeOptionIndex),G=ee(R,R.selectors.activeOption),U=(0,Z.useMemo)(()=>({open:I===0,disabled:p,invalid:u,activeIndex:_,activeOption:G,value:v}),[O,p,v,u,G,I]),[X,j]=Be(),F=n===null?{}:{ref:n},k=(0,Z.useCallback)(()=>{if(y!==void 0)return x==null?void 0:x(y)},[x,y]),M=H();return Z.default.createElement(j,{value:X,props:{htmlFor:A==null?void 0:A.id},slot:{open:I===0,disabled:p}},Z.default.createElement(en,null,Z.default.createElement(Lo.Provider,{value:O},Z.default.createElement(Hi.Provider,{value:R},Z.default.createElement(ot,{value:Y(I,{[0]:1,[1]:2})},a!=null&&Z.default.createElement(yt,{disabled:p,data:v!=null?{[a]:v}:{},form:l,onReset:k}),M({ourProps:F,theirProps:h,slot:U,defaultTag:ed,name:"Combobox"}))))))}var nd="input";function od(e,n){var X,j;let t=Vn("Combobox.Input"),o=jn("Combobox.Input"),r=(0,J.useId)(),i=we(),{id:s=i||`headlessui-combobox-input-${r}`,onChange:l,displayValue:a,disabled:c=o.disabled||!1,autoFocus:u=!1,type:p="text",...d}=e,[f]=ee(t,F=>[F.inputElement]),m=(0,Z.useRef)(null),T=K(m,n,Jt(),t.actions.setInputElement),b=Re(f),[g,h]=ee(t,F=>[F.comboboxState,F.isTyping]),y=Se(),v=E(()=>{t.actions.onChange(null),t.state.optionsElement&&(t.state.optionsElement.scrollTop=0),t.actions.goToOption({focus:5})}),x=(0,Z.useMemo)(()=>{var F;return typeof a=="function"&&o.value!==void 0?(F=a(o.value))!=null?F:"":typeof o.value=="string"?o.value:""},[o.value,a]);un(([F,k],[M,D])=>{if(t.state.isTyping)return;let z=m.current;z&&((D===0&&k===1||F!==M)&&(z.value=F),requestAnimationFrame(()=>{if(t.state.isTyping||!z||(b==null?void 0:b.activeElement)!==z)return;let{selectionStart:oe,selectionEnd:We}=z;Math.abs((We!=null?We:0)-(oe!=null?oe:0))===0&&oe===0&&z.setSelectionRange(z.value.length,z.value.length)}))},[x,g,b,h]),un(([F],[k])=>{if(F===0&&k===1){if(t.state.isTyping)return;let M=m.current;if(!M)return;let D=M.value,{selectionStart:z,selectionEnd:oe,selectionDirection:We}=M;M.value="",M.value=D,We!==null?M.setSelectionRange(z,oe,We):M.setSelectionRange(z,oe)}},[g]);let R=(0,Z.useRef)(!1),$=E(()=>{R.current=!0}),C=E(()=>{y.nextFrame(()=>{R.current=!1})}),B=E(F=>{switch(t.actions.setIsTyping(!0),F.key){case"Enter":if(t.state.comboboxState!==0||R.current)return;if(F.preventDefault(),F.stopPropagation(),t.selectors.activeOptionIndex(t.state)===null){t.actions.closeCombobox();return}t.actions.selectActiveOption(),o.mode===0&&t.actions.closeCombobox();break;case"ArrowDown":return F.preventDefault(),F.stopPropagation(),Y(t.state.comboboxState,{[0]:()=>t.actions.goToOption({focus:2}),[1]:()=>t.actions.openCombobox()});case"ArrowUp":return F.preventDefault(),F.stopPropagation(),Y(t.state.comboboxState,{[0]:()=>t.actions.goToOption({focus:1}),[1]:()=>{(0,Tn.flushSync)(()=>t.actions.openCombobox()),o.value||t.actions.goToOption({focus:3})}});case"Home":if(F.shiftKey)break;return F.preventDefault(),F.stopPropagation(),t.actions.goToOption({focus:0});case"PageUp":return F.preventDefault(),F.stopPropagation(),t.actions.goToOption({focus:0});case"End":if(F.shiftKey)break;return F.preventDefault(),F.stopPropagation(),t.actions.goToOption({focus:3});case"PageDown":return F.preventDefault(),F.stopPropagation(),t.actions.goToOption({focus:3});case"Escape":return t.state.comboboxState!==0?void 0:(F.preventDefault(),t.state.optionsElement&&!o.optionsPropsRef.current.static&&F.stopPropagation(),o.mode===0&&o.value===null&&v(),t.actions.closeCombobox());case"Tab":if(t.state.comboboxState!==0)return;o.mode===0&&t.state.activationTrigger!==1&&t.actions.selectActiveOption(),t.actions.closeCombobox();break}}),S=E(F=>{l==null||l(F),o.mode===0&&F.target.value===""&&v(),t.actions.openCombobox()}),P=E(F=>{var M,D,z;let k=(M=F.relatedTarget)!=null?M:rt.find(oe=>oe!==F.currentTarget);if(!((D=t.state.optionsElement)!=null&&D.contains(k))&&!((z=t.state.buttonElement)!=null&&z.contains(k))&&t.state.comboboxState===0)return F.preventDefault(),o.mode===0&&o.value===null&&v(),t.actions.closeCombobox()}),L=E(F=>{var M,D,z;let k=(M=F.relatedTarget)!=null?M:rt.find(oe=>oe!==F.currentTarget);(D=t.state.buttonElement)!=null&&D.contains(k)||(z=t.state.optionsElement)!=null&&z.contains(k)||o.disabled||o.immediate&&t.state.comboboxState!==0&&y.microTask(()=>{(0,Tn.flushSync)(()=>t.actions.openCombobox()),t.actions.setActivationTrigger(1)})}),O=Fe(),I=Ne(),{isFocused:W,focusProps:A}=ce({autoFocus:u}),{isHovered:N,hoverProps:ne}=fe({isDisabled:c}),q=ee(t,F=>F.optionsElement),_=(0,Z.useMemo)(()=>({open:g===0,disabled:c,invalid:o.invalid,hover:N,focus:W,autofocus:u}),[o,N,W,u,c,o.invalid]),G=ae({ref:T,id:s,role:"combobox",type:p,"aria-controls":q==null?void 0:q.id,"aria-expanded":g===0,"aria-activedescendant":ee(t,t.selectors.activeDescendantId),"aria-labelledby":O,"aria-describedby":I,"aria-autocomplete":"list",defaultValue:(j=(X=e.defaultValue)!=null?X:o.defaultValue!==void 0?a==null?void 0:a(o.defaultValue):null)!=null?j:o.defaultValue,disabled:c||void 0,autoFocus:u,onCompositionStart:$,onCompositionEnd:C,onKeyDown:B,onChange:S,onFocus:L,onBlur:P},A,ne);return H()({ourProps:G,theirProps:d,slot:_,defaultTag:nd,name:"Combobox.Input"})}var rd="button";function id(e,n){let t=Vn("Combobox.Button"),o=jn("Combobox.Button"),[r,i]=(0,Z.useState)(null),s=K(n,i,t.actions.setButtonElement),l=(0,J.useId)(),{id:a=`headlessui-combobox-button-${l}`,disabled:c=o.disabled||!1,autoFocus:u=!1,...p}=e,[d,f,m]=ee(t,O=>[O.comboboxState,O.inputElement,O.optionsElement]),T=bi(f),b=d===0;Dn(b,{trigger:r,action:(0,Z.useCallback)(O=>{if(r!=null&&r.contains(O.target))return Ge.Ignore;if(f!=null&&f.contains(O.target))return Ge.Ignore;let I=O.target.closest('[role="option"]:not([data-disabled])');return Te(I)?Ge.Select(I):m!=null&&m.contains(O.target)?Ge.Ignore:Ge.Close},[r,f,m]),close:t.actions.closeCombobox,select:t.actions.selectActiveOption});let g=E(O=>{switch(O.key){case" ":case"Enter":O.preventDefault(),O.stopPropagation(),t.state.comboboxState===1&&(0,Tn.flushSync)(()=>t.actions.openCombobox()),T();return;case"ArrowDown":O.preventDefault(),O.stopPropagation(),t.state.comboboxState===1&&((0,Tn.flushSync)(()=>t.actions.openCombobox()),t.state.dataRef.current.value||t.actions.goToOption({focus:0})),T();return;case"ArrowUp":O.preventDefault(),O.stopPropagation(),t.state.comboboxState===1&&((0,Tn.flushSync)(()=>t.actions.openCombobox()),t.state.dataRef.current.value||t.actions.goToOption({focus:3})),T();return;case"Escape":if(t.state.comboboxState!==0)return;O.preventDefault(),t.state.optionsElement&&!o.optionsPropsRef.current.static&&O.stopPropagation(),(0,Tn.flushSync)(()=>t.actions.closeCombobox()),T();return;default:return}}),h=E(O=>{O.preventDefault(),!_e(O.currentTarget)&&(O.button===0&&(t.state.comboboxState===0?t.actions.closeCombobox():t.actions.openCombobox()),T())}),y=Fe([a]),{isFocusVisible:v,focusProps:x}=ce({autoFocus:u}),{isHovered:R,hoverProps:$}=fe({isDisabled:c}),{pressed:C,pressProps:B}=Ae({disabled:c}),S=(0,Z.useMemo)(()=>({open:d===0,active:C||d===0,disabled:c,invalid:o.invalid,value:o.value,hover:R,focus:v}),[o,R,v,C,c,d]),P=ae({ref:s,id:a,type:je(e,r),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":m==null?void 0:m.id,"aria-expanded":d===0,"aria-labelledby":y,disabled:c||void 0,autoFocus:u,onPointerDown:h,onKeyDown:g},x,$,B);return H()({ourProps:P,theirProps:p,slot:S,defaultTag:rd,name:"Combobox.Button"})}var sd="div",ld=3;function ad(e,n){var M,D,z;let t=(0,J.useId)(),{id:o=`headlessui-combobox-options-${t}`,hold:r=!1,anchor:i,portal:s=!1,modal:l=!0,transition:a=!1,...c}=e,u=Vn("Combobox.Options"),p=jn("Combobox.Options"),d=qt(i);d&&(s=!0);let[f,m]=Zt(d),[T,b]=(0,Z.useState)(null),g=Qt(),h=K(n,d?f:null,u.actions.setOptionsElement,b),[y,v,x,R,$]=ee(u,oe=>[oe.comboboxState,oe.inputElement,oe.buttonElement,oe.optionsElement,oe.activationTrigger]),C=Re(v||x),B=Re(R),S=He(),[P,L]=Qe(a,T,S!==null?(S&1)===1:y===0);vt(P,v,u.actions.closeCombobox);let O=p.__demoMode?!1:l&&y===0;xt(O,B);let I=p.__demoMode?!1:l&&y===0;Wt(I,{allowed:(0,Z.useCallback)(()=>[v,x,R],[v,x,R])}),V(()=>{var oe;p.optionsPropsRef.current.static=(oe=e.static)!=null?oe:!1},[p.optionsPropsRef,e.static]),V(()=>{p.optionsPropsRef.current.hold=r},[p.optionsPropsRef,r]),ar(y===0,{container:R,accept(oe){return oe.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:oe.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(oe){oe.setAttribute("role","none")}});let W=Fe([x==null?void 0:x.id]),A=(0,Z.useMemo)(()=>({open:y===0,option:void 0}),[y]),N=E(()=>{u.actions.setActivationTrigger(0)}),ne=E(oe=>{oe.preventDefault(),u.actions.setActivationTrigger(0)}),q=ae(d?g():{},{"aria-labelledby":W,role:"listbox","aria-multiselectable":p.mode===1?!0:void 0,id:o,ref:h,style:{...c.style,...m,"--input-width":It(v,!0).width,"--button-width":It(x,!0).width},onWheel:$===0?void 0:N,onMouseDown:ne,...Je(L)}),_=P&&y===1,G=Hn(_,(M=p.virtual)==null?void 0:M.options),U=Hn(_,p.value),X=E(oe=>p.compare(U,oe)),j=(0,Z.useMemo)(()=>{if(!p.virtual)return p;if(G===void 0)throw new Error("Missing `options` in virtual mode");return G!==p.virtual.options?{...p,virtual:{...p.virtual,options:G}}:p},[p,G,(D=p.virtual)==null?void 0:D.options]);p.virtual&&Object.assign(c,{children:Z.default.createElement(Lo.Provider,{value:j},Z.default.createElement(Zf,{slot:A},c.children))});let F=H(),k=(0,Z.useMemo)(()=>p.mode===1?p:{...p,isSelected:X},[p,X]);return Z.default.createElement(st,{enabled:s?e.static||P:!1,ownerDocument:C},Z.default.createElement(Lo.Provider,{value:k},F({ourProps:q,theirProps:{...c,children:Z.default.createElement(ga,{freeze:_},typeof c.children=="function"?(z=c.children)==null?void 0:z.call(c,A):c.children)},slot:A,defaultTag:sd,features:ld,visible:P,name:"Combobox.Options"})))}var ud="div";function cd(e,n){var O,I,W;let t=jn("Combobox.Option"),o=Vn("Combobox.Option"),r=(0,J.useId)(),{id:i=`headlessui-combobox-option-${r}`,value:s,disabled:l=(W=(I=(O=t.virtual)==null?void 0:O.disabled)==null?void 0:I.call(O,s))!=null?W:!1,order:a=null,...c}=e,[u]=ee(o,A=>[A.inputElement]),p=bi(u),d=ee(o,(0,Z.useCallback)(A=>o.selectors.isActive(A,s,i),[s,i])),f=t.isSelected(s),m=(0,Z.useRef)(null),T=me({disabled:l,value:s,domRef:m,order:a}),b=(0,Z.useContext)(Aa),g=K(n,m,b?b.measureElement:null),h=E(()=>{o.actions.setIsTyping(!1),o.actions.onChange(s)});V(()=>o.actions.registerOption(i,T),[T,i]);let y=ee(o,(0,Z.useCallback)(A=>o.selectors.shouldScrollIntoView(A,s,i),[s,i]));V(()=>{if(y)return he().requestAnimationFrame(()=>{var A,N;(N=(A=m.current)==null?void 0:A.scrollIntoView)==null||N.call(A,{block:"nearest"})})},[y,m]);let v=E(A=>{A.preventDefault(),A.button===0&&(l||(h(),go()||requestAnimationFrame(()=>p()),t.mode===0&&o.actions.closeCombobox()))}),x=E(()=>{if(l)return o.actions.goToOption({focus:5});let A=t.calculateIndex(s);o.actions.goToOption({focus:4,idx:A})}),R=Ln(),$=E(A=>R.update(A)),C=E(A=>{if(!R.wasMoved(A)||l||d)return;let N=t.calculateIndex(s);o.actions.goToOption({focus:4,idx:N},0)}),B=E(A=>{R.wasMoved(A)&&(l||d&&(t.optionsPropsRef.current.hold||o.actions.goToOption({focus:5})))}),S=(0,Z.useMemo)(()=>({active:d,focus:d,selected:f,disabled:l}),[d,f,l]),P={id:i,ref:g,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":f,disabled:void 0,onMouseDown:v,onFocus:x,onPointerEnter:$,onMouseEnter:$,onPointerMove:C,onMouseMove:C,onPointerLeave:B,onMouseLeave:B};return H()({ourProps:P,theirProps:c,slot:S,defaultTag:ud,name:"Combobox.Option"})}var fd=w(td),Ca=w(id),Oa=w(od),Da=tt,La=w(ad),Ia=w(cd),dd=Object.assign(fd,{Input:Oa,Button:Ca,Label:Da,Options:La,Option:Ia});var Pr=require("react");var pd=Pr.Fragment;function md(e,n){let{...t}=e,o=!1,{isFocusVisible:r,focusProps:i}=ce(),{isHovered:s,hoverProps:l}=fe({isDisabled:o}),{pressed:a,pressProps:c}=Ae({disabled:o}),u=ae({ref:n},i,l,c),p=(0,Pr.useMemo)(()=>({hover:s,focus:r,active:a}),[s,r,a]);return H()({ourProps:u,theirProps:t,slot:p,defaultTag:pd,name:"DataInteractive"})}var Td=w(md);var se=le(require("react"),1);function Ma(e,n=typeof document!="undefined"?document.defaultView:null,t){let o=Mt(e,"escape");Kt(n,"keydown",r=>{o&&(r.defaultPrevented||r.key==="Escape"&&t(r))})}var Bi=require("react");function Fa(){var o;let[e]=(0,Bi.useState)(()=>typeof window!="undefined"&&typeof window.matchMedia=="function"?window.matchMedia("(pointer: coarse)"):null),[n,t]=(0,Bi.useState)((o=e==null?void 0:e.matches)!=null?o:!1);return V(()=>{if(!e)return;function r(i){t(i.matches)}return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[e]),n}var Nt=le(require("react"),1);function Rr({defaultContainers:e=[],portals:n,mainTreeNode:t}={}){let o=Re(t),r=E(()=>{var s,l;let i=[];for(let a of e)a!==null&&(ut(a)?i.push(a):"current"in a&&ut(a.current)&&i.push(a.current));if(n!=null&&n.current)for(let a of n.current)i.push(a);for(let a of(s=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?s:[])a!==document.body&&a!==document.head&&ut(a)&&a.id!=="headlessui-portal-root"&&(t&&(a.contains(t)||a.contains((l=t==null?void 0:t.getRootNode())==null?void 0:l.host))||i.some(c=>a.contains(c))||i.push(a));return i});return{resolveContainers:r,contains:E(i=>r().some(s=>s.contains(i)))}}var wa=(0,Nt.createContext)(null);function Kn({children:e,node:n}){let[t,o]=(0,Nt.useState)(null),r=Io(n!=null?n:t);return Nt.default.createElement(wa.Provider,{value:r},e,r===null&&Nt.default.createElement(ke,{features:4,ref:i=>{var s,l;if(i){for(let a of(l=(s=Pe(i))==null?void 0:s.querySelectorAll("html > *, body > *"))!=null?l:[])if(a!==document.body&&a!==document.head&&ut(a)&&a!=null&&a.contains(i)){o(a);break}}}}))}function Io(e=null){var n;return(n=(0,Nt.useContext)(wa))!=null?n:e}var At=le(require("react"),1);var _a=require("react");function gn(){let e=(0,_a.useRef)(!1);return V(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var $a=require("react");function Mo(){let e=(0,$a.useRef)(0);return lr(!0,"keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function ka(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.current)ut(t.current)&&n.add(t.current);return n}var gd="div",Sr=(s=>(s[s.None=0]="None",s[s.InitialFocus=1]="InitialFocus",s[s.TabLock=2]="TabLock",s[s.FocusLock=4]="FocusLock",s[s.RestoreFocus=8]="RestoreFocus",s[s.AutoFocus=16]="AutoFocus",s))(Sr||{});function yd(e,n){let t=(0,At.useRef)(null),o=K(t,n),{initialFocus:r,initialFocusFallback:i,containers:s,features:l=15,...a}=e;Ht()||(l=0);let c=Re(t);Ed(l,{ownerDocument:c});let u=xd(l,{ownerDocument:c,container:t,initialFocus:r,initialFocusFallback:i});Pd(l,{ownerDocument:c,container:t,containers:s,previousActiveElement:u});let p=Mo(),d=E(h=>{if(!Te(t.current))return;let y=t.current;(x=>x())(()=>{Y(p.current,{[0]:()=>{ve(y,1,{skipElements:[h.relatedTarget,i]})},[1]:()=>{ve(y,8,{skipElements:[h.relatedTarget,i]})}})})}),f=Mt(!!(l&2),"focus-trap#tab-lock"),m=Se(),T=(0,At.useRef)(!1),b={ref:o,onKeyDown(h){h.key=="Tab"&&(T.current=!0,m.requestAnimationFrame(()=>{T.current=!1}))},onBlur(h){if(!(l&4))return;let y=ka(s);Te(t.current)&&y.add(t.current);let v=h.relatedTarget;Le(v)&&v.dataset.headlessuiFocusGuard!=="true"&&(Ha(y,v)||(T.current?ve(t.current,Y(p.current,{[0]:()=>4,[1]:()=>2})|16,{relativeTo:h.target}):Le(h.target)&&dt(h.target)))}},g=H();return At.default.createElement(At.default.Fragment,null,f&&At.default.createElement(ke,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:2}),g({ourProps:b,theirProps:a,defaultTag:gd,name:"FocusTrap"}),f&&At.default.createElement(ke,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:2}))}var hd=w(yd),Ui=Object.assign(hd,{features:Sr});function vd(e=!0){let n=(0,At.useRef)(rt.slice());return un(([t],[o])=>{o===!0&&t===!1&&Dt(()=>{n.current.splice(0)}),o===!1&&t===!0&&(n.current=rt.slice())},[e,rt,n]),E(()=>{var t;return(t=n.current.find(o=>o!=null&&o.isConnected))!=null?t:null})}function Ed(e,{ownerDocument:n}){let t=!!(e&8),o=vd(t);un(()=>{t||(n==null?void 0:n.activeElement)===(n==null?void 0:n.body)&&dt(o())},[t]),it(()=>{t&&dt(o())})}function xd(e,{ownerDocument:n,container:t,initialFocus:o,initialFocusFallback:r}){let i=(0,At.useRef)(null),s=Mt(!!(e&1),"focus-trap#initial-focus"),l=gn();return un(()=>{if(e===0)return;if(!s){r!=null&&r.current&&dt(r.current);return}let a=t.current;a&&Dt(()=>{if(!l.current)return;let c=n==null?void 0:n.activeElement;if(o!=null&&o.current){if((o==null?void 0:o.current)===c){i.current=c;return}}else if(a.contains(c)){i.current=c;return}if(o!=null&&o.current)dt(o.current);else{if(e&16){if(ve(a,65)!==0)return}else if(ve(a,1)!==0)return;if(r!=null&&r.current&&(dt(r.current),(n==null?void 0:n.activeElement)===r.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}i.current=n==null?void 0:n.activeElement})},[r,s,e]),i}function Pd(e,{ownerDocument:n,container:t,containers:o,previousActiveElement:r}){let i=gn(),s=!!(e&4);Kt(n==null?void 0:n.defaultView,"focus",l=>{if(!s||!i.current)return;let a=ka(o);Te(t.current)&&a.add(t.current);let c=r.current;if(!c)return;let u=l.target;Te(u)?Ha(a,u)?(r.current=u,dt(u)):(l.preventDefault(),l.stopPropagation(),dt(c)):dt(r.current)},!0)}function Ha(e,n){for(let t of e)if(t.contains(n))return!0;return!1}var ie=le(require("react"),1);function Na(e){var n;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((n=e.as)!=null?n:Ga)!==ie.Fragment||ie.default.Children.count(e.children)===1}var Ar=(0,ie.createContext)(null);Ar.displayName="TransitionContext";function Rd(){let e=(0,ie.useContext)(Ar);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function Sd(){let e=(0,ie.useContext)(Cr);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}var Cr=(0,ie.createContext)(null);Cr.displayName="NestingContext";function Or(e){return"children"in e?Or(e.children):e.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function Ba(e,n){let t=me(e),o=(0,ie.useRef)([]),r=gn(),i=Se(),s=E((f,m=1)=>{let T=o.current.findIndex(({el:b})=>b===f);T!==-1&&(Y(m,{[0](){o.current.splice(T,1)},[1](){o.current[T].state="hidden"}}),i.microTask(()=>{var b;!Or(o)&&r.current&&((b=t.current)==null||b.call(t))}))}),l=E(f=>{let m=o.current.find(({el:T})=>T===f);return m?m.state!=="visible"&&(m.state="visible"):o.current.push({el:f,state:"visible"}),()=>s(f,0)}),a=(0,ie.useRef)([]),c=(0,ie.useRef)(Promise.resolve()),u=(0,ie.useRef)({enter:[],leave:[]}),p=E((f,m,T)=>{a.current.splice(0),n&&(n.chains.current[m]=n.chains.current[m].filter(([b])=>b!==f)),n==null||n.chains.current[m].push([f,new Promise(b=>{a.current.push(b)})]),n==null||n.chains.current[m].push([f,new Promise(b=>{Promise.all(u.current[m].map(([g,h])=>h)).then(()=>b())})]),m==="enter"?c.current=c.current.then(()=>n==null?void 0:n.wait.current).then(()=>T(m)):T(m)}),d=E((f,m,T)=>{Promise.all(u.current[m].splice(0).map(([b,g])=>g)).then(()=>{var b;(b=a.current.shift())==null||b()}).then(()=>T(m))});return(0,ie.useMemo)(()=>({children:o,register:l,unregister:s,onStart:p,onStop:d,wait:c,chains:u}),[l,s,o,p,d,u,c])}var Ga=ie.Fragment,Ua=1;function Ad(e,n){var F,k;let{transition:t=!0,beforeEnter:o,afterEnter:r,beforeLeave:i,afterLeave:s,enter:l,enterFrom:a,enterTo:c,entered:u,leave:p,leaveFrom:d,leaveTo:f,...m}=e,[T,b]=(0,ie.useState)(null),g=(0,ie.useRef)(null),h=Na(e),y=K(...h?[g,n,b]:n===null?[]:[n]),v=(F=m.unmount)==null||F?0:1,{show:x,appear:R,initial:$}=Rd(),[C,B]=(0,ie.useState)(x?"visible":"hidden"),S=Sd(),{register:P,unregister:L}=S;V(()=>P(g),[P,g]),V(()=>{if(v===1&&g.current){if(x&&C!=="visible"){B("visible");return}return Y(C,{["hidden"]:()=>L(g),["visible"]:()=>P(g)})}},[C,g,P,L,x,v]);let O=Ht();V(()=>{if(h&&O&&C==="visible"&&g.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[g,C,O,h]);let I=$&&!R,W=R&&x&&$,A=(0,ie.useRef)(!1),N=Ba(()=>{A.current||(B("hidden"),L(g))},S),ne=E(M=>{A.current=!0;let D=M?"enter":"leave";N.onStart(g,D,z=>{z==="enter"?o==null||o():z==="leave"&&(i==null||i())})}),q=E(M=>{let D=M?"enter":"leave";A.current=!1,N.onStop(g,D,z=>{z==="enter"?r==null||r():z==="leave"&&(s==null||s())}),D==="leave"&&!Or(N)&&(B("hidden"),L(g))});(0,ie.useEffect)(()=>{h&&t||(ne(x),q(x))},[x,h,t]);let _=(()=>!(!t||!h||!O||I))(),[,G]=Qe(_,T,x,{start:ne,end:q}),U=Tt({ref:y,className:((k=co(m.className,W&&l,W&&a,G.enter&&l,G.enter&&G.closed&&a,G.enter&&!G.closed&&c,G.leave&&p,G.leave&&!G.closed&&d,G.leave&&G.closed&&f,!G.transition&&x&&u))==null?void 0:k.trim())||void 0,...Je(G)}),X=0;C==="visible"&&(X|=1),C==="hidden"&&(X|=2),x&&C==="hidden"&&(X|=8),!x&&C==="visible"&&(X|=4);let j=H();return ie.default.createElement(Cr.Provider,{value:N},ie.default.createElement(ot,{value:X},j({ourProps:U,theirProps:m,defaultTag:Ga,features:Ua,visible:C==="visible",name:"Transition.Child"})))}function Cd(e,n){let{show:t,appear:o=!1,unmount:r=!0,...i}=e,s=(0,ie.useRef)(null),l=Na(e),a=K(...l?[s,n]:n===null?[]:[n]);Ht();let c=He();if(t===void 0&&c!==null&&(t=(c&1)===1),t===void 0)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[u,p]=(0,ie.useState)(t?"visible":"hidden"),d=Ba(()=>{t||p("hidden")}),[f,m]=(0,ie.useState)(!0),T=(0,ie.useRef)([t]);V(()=>{f!==!1&&T.current[T.current.length-1]!==t&&(T.current.push(t),m(!1))},[T,t]);let b=(0,ie.useMemo)(()=>({show:t,appear:o,initial:f}),[t,o,f]);V(()=>{t?p("visible"):!Or(d)&&s.current!==null&&p("hidden")},[t,d]);let g={unmount:r},h=E(()=>{var x;f&&m(!1),(x=e.beforeEnter)==null||x.call(e)}),y=E(()=>{var x;f&&m(!1),(x=e.beforeLeave)==null||x.call(e)}),v=H();return ie.default.createElement(Cr.Provider,{value:d},ie.default.createElement(Ar.Provider,{value:b},v({ourProps:{...g,as:ie.Fragment,children:ie.default.createElement(Va,{ref:a,...g,...i,beforeEnter:h,beforeLeave:y})},theirProps:{},defaultTag:ie.Fragment,features:Ua,visible:u==="visible",name:"Transition"})))}function Od(e,n){let t=(0,ie.useContext)(Ar)!==null,o=He()!==null;return ie.default.createElement(ie.default.Fragment,null,!t&&o?ie.default.createElement(Vi,{ref:n,...e}):ie.default.createElement(Va,{ref:n,...e}))}var Vi=w(Cd),Va=w(Ad),wo=w(Od),Wi=Object.assign(Vi,{Child:wo,Root:Vi});var Dd={[0](e,n){return e.titleId===n.id?e:{...e,titleId:n.id}}},ji=(0,se.createContext)(null);ji.displayName="DialogContext";function Dr(e){let n=(0,se.useContext)(ji);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Dr),t}return n}function Ld(e,n){return Y(n.type,Dd,e,n)}var Wa=w(function(n,t){let o=(0,J.useId)(),{id:r=`headlessui-dialog-${o}`,open:i,onClose:s,initialFocus:l,role:a="dialog",autoFocus:c=!0,__demoMode:u=!1,unmount:p=!1,...d}=n,f=(0,se.useRef)(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(f.current||(f.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=He();i===void 0&&m!==null&&(i=(m&1)===1);let T=(0,se.useRef)(null),b=K(T,t),g=Re(T),h=i?0:1,[y,v]=(0,se.useReducer)(Ld,{titleId:null,descriptionId:null,panelRef:(0,se.createRef)()}),x=E(()=>s(!1)),R=E(M=>v({type:0,id:M})),C=Ht()?h===0:!1,[B,S]=xr(),P={get current(){var M;return(M=y.panelRef.current)!=null?M:T.current}},L=Io(),{resolveContainers:O}=Rr({mainTreeNode:L,portals:B,defaultContainers:[P]}),I=m!==null?(m&4)===4:!1;Wt(u||I?!1:C,{allowed:E(()=>{var M,D;return[(D=(M=T.current)==null?void 0:M.closest("[data-headlessui-portal]"))!=null?D:null]}),disallowed:E(()=>{var M;return[(M=L==null?void 0:L.closest("body > *:not(#headlessui-portal-root)"))!=null?M:null]})});let A=$e.get(null);V(()=>{if(C)return A.actions.push(r),()=>A.actions.pop(r)},[A,r,C]);let N=ee(A,(0,se.useCallback)(M=>A.selectors.isTop(M,r),[A,r]));Et(N,O,M=>{M.preventDefault(),x()}),Ma(N,g==null?void 0:g.defaultView,M=>{M.preventDefault(),M.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),x()}),xt(u||I?!1:C,g,O),vt(C,T,x);let[q,_]=ct(),G=(0,se.useMemo)(()=>[{dialogState:h,close:x,setTitleId:R,unmount:p},y],[h,y,x,R,p]),U=(0,se.useMemo)(()=>({open:h===0}),[h]),X={ref:b,id:r,role:a,tabIndex:-1,"aria-modal":u?void 0:h===0?!0:void 0,"aria-labelledby":y.titleId,"aria-describedby":q,unmount:p},j=!Fa(),F=0;C&&!u&&(F|=8,F|=2,c&&(F|=16),j&&(F|=1));let k=H();return se.default.createElement(Nn,null,se.default.createElement(wi,{force:!0},se.default.createElement(st,null,se.default.createElement(ji.Provider,{value:G},se.default.createElement($i,{target:T},se.default.createElement(wi,{force:!1},se.default.createElement(_,{slot:U},se.default.createElement(S,null,se.default.createElement(Ui,{initialFocus:l,initialFocusFallback:T,containers:O,features:F},se.default.createElement(rn,{value:x},k({ourProps:X,theirProps:d,slot:U,defaultTag:Id,features:Md,visible:h===0,name:"Dialog"})))))))))))}),Id="div",Md=3;function Fd(e,n){let{transition:t=!1,open:o,...r}=e,i=He(),s=e.hasOwnProperty("open")||i!==null,l=e.hasOwnProperty("onClose");if(!s&&!l)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!l)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(o!==void 0||t)&&!r.static?se.default.createElement(Kn,null,se.default.createElement(Wi,{show:o,transition:t,unmount:r.unmount},se.default.createElement(Wa,{ref:n,...r}))):se.default.createElement(Kn,null,se.default.createElement(Wa,{ref:n,open:o,...r}))}var wd="div";function _d(e,n){let t=(0,J.useId)(),{id:o=`headlessui-dialog-panel-${t}`,transition:r=!1,...i}=e,[{dialogState:s,unmount:l},a]=Dr("Dialog.Panel"),c=K(n,a.panelRef),u=(0,se.useMemo)(()=>({open:s===0}),[s]),p=E(b=>{b.stopPropagation()}),d={ref:c,id:o,onClick:p},f=r?wo:se.Fragment,m=r?{unmount:l}:{},T=H();return se.default.createElement(f,{...m},T({ourProps:d,theirProps:i,slot:u,defaultTag:wd,name:"Dialog.Panel"}))}var $d="div";function kd(e,n){let{transition:t=!1,...o}=e,[{dialogState:r,unmount:i}]=Dr("Dialog.Backdrop"),s=(0,se.useMemo)(()=>({open:r===0}),[r]),l={ref:n,"aria-hidden":!0},a=t?wo:se.Fragment,c=t?{unmount:i}:{},u=H();return se.default.createElement(a,{...c},u({ourProps:l,theirProps:o,slot:s,defaultTag:$d,name:"Dialog.Backdrop"}))}var Hd="h2";function Nd(e,n){let t=(0,J.useId)(),{id:o=`headlessui-dialog-title-${t}`,...r}=e,[{dialogState:i,setTitleId:s}]=Dr("Dialog.Title"),l=K(n);(0,se.useEffect)(()=>(s(o),()=>s(null)),[o,s]);let a=(0,se.useMemo)(()=>({open:i===0}),[i]),c={ref:l,id:o};return H()({ourProps:c,theirProps:r,slot:a,defaultTag:Hd,name:"Dialog.Title"})}var Bd=w(Fd),ja=w(_d),Gd=w(kd),Ka=w(Nd),Ud=Lt,Vd=Object.assign(Bd,{Panel:ja,Title:Ka,Description:Lt});var ue=le(require("react"),1);var Xa=le(require("react"),1),za,Ya=(za=Xa.default.startTransition)!=null?za:function(n){n()};var Wd={[0]:e=>({...e,disclosureState:Y(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}},[4](e,n){return e.buttonElement===n.element?e:{...e,buttonElement:n.element}},[5](e,n){return e.panelElement===n.element?e:{...e,panelElement:n.element}}},Ki=(0,ue.createContext)(null);Ki.displayName="DisclosureContext";function zi(e){let n=(0,ue.useContext)(Ki);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,zi),t}return n}var Xi=(0,ue.createContext)(null);Xi.displayName="DisclosureAPIContext";function qa(e){let n=(0,ue.useContext)(Xi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,qa),t}return n}var Yi=(0,ue.createContext)(null);Yi.displayName="DisclosurePanelContext";function jd(){return(0,ue.useContext)(Yi)}function Kd(e,n){return Y(n.type,Wd,e,n)}var zd=ue.Fragment;function Xd(e,n){let{defaultOpen:t=!1,...o}=e,r=(0,ue.useRef)(null),i=K(n,Pn(T=>{r.current=T},e.as===void 0||e.as===ue.Fragment)),s=(0,ue.useReducer)(Kd,{disclosureState:t?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:l,buttonId:a},c]=s,u=E(T=>{c({type:1});let b=Pe(r);if(!b||!a)return;let g=(()=>T?Le(T)?T:"current"in T&&Le(T.current)?T.current:b.getElementById(a):b.getElementById(a))();g==null||g.focus()}),p=(0,ue.useMemo)(()=>({close:u}),[u]),d=(0,ue.useMemo)(()=>({open:l===0,close:u}),[l,u]),f={ref:i},m=H();return ue.default.createElement(Ki.Provider,{value:s},ue.default.createElement(Xi.Provider,{value:p},ue.default.createElement(rn,{value:u},ue.default.createElement(ot,{value:Y(l,{[0]:1,[1]:2})},m({ourProps:f,theirProps:o,slot:d,defaultTag:zd,name:"Disclosure"})))))}var Yd="button";function qd(e,n){let t=(0,J.useId)(),{id:o=`headlessui-disclosure-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[l,a]=zi("Disclosure.Button"),c=jd(),u=c===null?!1:c===l.panelId,p=(0,ue.useRef)(null),d=K(p,n,E(S=>{if(!u)return a({type:4,element:S})}));(0,ue.useEffect)(()=>{if(!u)return a({type:2,buttonId:o}),()=>{a({type:2,buttonId:null})}},[o,a,u]);let f=E(S=>{var P;if(u){if(l.disclosureState===1)return;switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),a({type:0}),(P=l.buttonElement)==null||P.focus();break}}else switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),a({type:0});break}}),m=E(S=>{switch(S.key){case" ":S.preventDefault();break}}),T=E(S=>{var P;_e(S.currentTarget)||r||(u?(a({type:0}),(P=l.buttonElement)==null||P.focus()):a({type:0}))}),{isFocusVisible:b,focusProps:g}=ce({autoFocus:i}),{isHovered:h,hoverProps:y}=fe({isDisabled:r}),{pressed:v,pressProps:x}=Ae({disabled:r}),R=(0,ue.useMemo)(()=>({open:l.disclosureState===0,hover:h,active:v,disabled:r,focus:b,autofocus:i}),[l,h,v,b,r,i]),$=je(e,l.buttonElement),C=u?ae({ref:d,type:$,disabled:r||void 0,autoFocus:i,onKeyDown:f,onClick:T},g,y,x):ae({ref:d,id:o,type:$,"aria-expanded":l.disclosureState===0,"aria-controls":l.panelElement?l.panelId:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:f,onKeyUp:m,onClick:T},g,y,x);return H()({ourProps:C,theirProps:s,slot:R,defaultTag:Yd,name:"Disclosure.Button"})}var Jd="div",Qd=3;function Zd(e,n){let t=(0,J.useId)(),{id:o=`headlessui-disclosure-panel-${t}`,transition:r=!1,...i}=e,[s,l]=zi("Disclosure.Panel"),{close:a}=qa("Disclosure.Panel"),[c,u]=(0,ue.useState)(null),p=K(n,E(h=>{Ya(()=>l({type:5,element:h}))}),u);(0,ue.useEffect)(()=>(l({type:3,panelId:o}),()=>{l({type:3,panelId:null})}),[o,l]);let d=He(),[f,m]=Qe(r,c,d!==null?(d&1)===1:s.disclosureState===0),T=(0,ue.useMemo)(()=>({open:s.disclosureState===0,close:a}),[s.disclosureState,a]),b={ref:p,id:o,...Je(m)},g=H();return ue.default.createElement(Nn,null,ue.default.createElement(Yi.Provider,{value:s.panelId},g({ourProps:b,theirProps:i,slot:T,defaultTag:Jd,features:Qd,visible:f,name:"Disclosure.Panel"})))}var ep=w(Xd),Ja=w(qd),Qa=w(Zd),tp=Object.assign(ep,{Button:Ja,Panel:Qa});var tn=le(require("react"),1);var np="div";function op(e,n){let t=`headlessui-control-${(0,J.useId)()}`,[o,r]=Be(),[i,s]=ct(),l=ge(),{disabled:a=l||!1,...c}=e,u=(0,tn.useMemo)(()=>({disabled:a}),[a]),p={ref:n,disabled:a||void 0,"aria-disabled":a||void 0},d=H();return tn.default.createElement(Yo,{value:a},tn.default.createElement(r,{value:o},tn.default.createElement(s,{value:i},tn.default.createElement(Ns,{id:t},d({ourProps:p,theirProps:{...c,children:tn.default.createElement(ks,null,typeof c.children=="function"?c.children(u):c.children)},slot:u,defaultTag:np,name:"Field"})))))}var rp=w(op);var _o=le(require("react"),1);var Lr=require("react");function Za(e){let n=typeof e=="string"?e:void 0,[t,o]=(0,Lr.useState)(n);return[n!=null?n:t,(0,Lr.useCallback)(r=>{n||Te(r)&&o(r.tagName.toLowerCase())},[n])]}var eu="fieldset";function ip(e,n){var f;let t=ge(),{disabled:o=t||!1,...r}=e,[i,s]=Za((f=e.as)!=null?f:eu),l=K(n,s),[a,c]=Be(),u=(0,_o.useMemo)(()=>({disabled:o}),[o]),p=i==="fieldset"?{ref:l,"aria-labelledby":a,disabled:o||void 0}:{ref:l,role:"group","aria-labelledby":a,"aria-disabled":o||void 0},d=H();return _o.default.createElement(Yo,{value:o},_o.default.createElement(c,null,d({ourProps:p,theirProps:r,slot:u,defaultTag:eu,name:"Fieldset"})))}var sp=w(ip);var tu=require("react");var lp="input";function ap(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-input-${t}`,disabled:s=r||!1,autoFocus:l=!1,invalid:a=!1,...c}=e,u=Fe(),p=Ne(),{isFocused:d,focusProps:f}=ce({autoFocus:l}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),b=ae({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":a?"true":void 0,disabled:s||void 0,autoFocus:l},f,T),g=(0,tu.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:d,autofocus:l}),[s,a,m,d,l]);return H()({ourProps:b,theirProps:c,slot:g,defaultTag:lp,name:"Input"})}var up=w(ap);var nu=le(require("react"),1);function cp(e,n){return nu.default.createElement(tt,{as:"div",ref:n,...e})}var fp=w(cp);var re=le(require("react"),1),Yn=require("react-dom");var ou=require("react");function Ir(e,n){let t=(0,ou.useRef)({left:0,top:0});if(V(()=>{if(!n)return;let i=n.getBoundingClientRect();i&&(t.current=i)},[e,n]),n==null||!e||n===document.activeElement)return!1;let o=n.getBoundingClientRect();return o.top!==t.current.top||o.left!==t.current.left}var qi=require("react");var ru=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function iu(e){var i,s;let n=(i=e.innerText)!=null?i:"",t=e.cloneNode(!0);if(!Te(t))return n;let o=!1;for(let l of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))l.remove(),o=!0;let r=o?(s=t.innerText)!=null?s:"":n;return ru.test(r)&&(r=r.replace(ru,"")),r}function su(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let o=t.split(" ").map(r=>{let i=document.getElementById(r);if(i){let s=i.getAttribute("aria-label");return typeof s=="string"?s.trim():iu(i).trim()}return null}).filter(Boolean);if(o.length>0)return o.join(", ")}return iu(e).trim()}function Mr(e){let n=(0,qi.useRef)(""),t=(0,qi.useRef)("");return E(()=>{let o=e.current;if(!o)return"";let r=o.innerText;if(n.current===r)return t.current;let i=su(o).trim().toLowerCase();return n.current=r,t.current=i,i})}function lu(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=Ue(n(e.options.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}var dp={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,pendingFocus:{focus:5},listboxState:1,__demoMode:!1}},[0](e,n){if(e.dataRef.current.disabled||e.listboxState===0)return e;let t=e.activeOptionIndex,{isSelected:o}=e.dataRef.current,r=e.options.findIndex(i=>o(i.dataRef.current.value));return r!==-1&&(t=r),{...e,pendingFocus:n.focus,listboxState:0,activeOptionIndex:t,__demoMode:!1}},[2](e,n){var i,s,l,a,c;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeOptionIndex:null};if(n.focus===4)return{...t,activeOptionIndex:e.options.findIndex(u=>u.id===n.id)};if(n.focus===1){let u=e.activeOptionIndex;if(u!==null){let p=e.options[u].dataRef.current.domRef,d=et(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.options[d].dataRef.current.domRef;if(((s=p.current)==null?void 0:s.previousElementSibling)===f.current||((l=f.current)==null?void 0:l.previousElementSibling)===null)return{...t,activeOptionIndex:d}}}}else if(n.focus===2){let u=e.activeOptionIndex;if(u!==null){let p=e.options[u].dataRef.current.domRef,d=et(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.options[d].dataRef.current.domRef;if(((a=p.current)==null?void 0:a.nextElementSibling)===f.current||((c=f.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeOptionIndex:d}}}}let o=lu(e),r=et(n,{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...t,...o,activeOptionIndex:r}},[3]:(e,n)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+o).concat(e.options.slice(0,e.activeOptionIndex+o)):e.options).find(a=>{var c;return!a.dataRef.current.disabled&&((c=a.dataRef.current.textValue)==null?void 0:c.startsWith(r))}),l=s?e.options.indexOf(s):-1;return l===-1||l===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:l,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,n)=>{let t=e.options.concat(n.options),o=e.activeOptionIndex;if(e.pendingFocus.focus!==5&&(o=et(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled})),e.activeOptionIndex===null){let{isSelected:r}=e.dataRef.current;if(r){let i=t.findIndex(s=>r==null?void 0:r(s.dataRef.current.value));i!==-1&&(o=i)}}return{...e,options:t,activeOptionIndex:o,pendingFocus:{focus:5},pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.options,o=[],r=new Set(n.options);for(let[i,s]of t.entries())if(r.has(s.id)&&(o.push(i),r.delete(s.id),r.size===0))break;if(o.length>0){t=t.slice();for(let i of o.reverse())t.splice(i,1)}return{...e,options:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...lu(e),pendingShouldSort:!1}:e},zn=class extends nt{constructor(t){super(t);Oe(this,"actions",{onChange:t=>{let{onChange:o,compare:r,mode:i,value:s}=this.state.dataRef.current;return Y(i,{[0]:()=>o==null?void 0:o(t),[1]:()=>{let l=s.slice(),a=l.findIndex(c=>r(c,t));return a===-1?l.push(t):l.splice(a,1),o==null?void 0:o(l)}})},registerOption:ln(()=>{let t=[],o=new Set;return[(r,i)=>{o.has(i)||(o.add(i),t.push({id:r,dataRef:i}))},()=>(o.clear(),this.send({type:5,options:t.splice(0)}))]}),unregisterOption:ln(()=>{let t=[];return[o=>t.push(o),()=>{this.send({type:6,options:t.splice(0)})}]}),goToOption:ln(()=>{let t=null;return[(o,r)=>{t={type:2,...o,trigger:r}},()=>t&&this.send(t)]}),closeListbox:()=>{this.send({type:1})},openListbox:t=>{this.send({type:0,focus:t})},selectActiveOption:()=>{if(this.state.activeOptionIndex!==null){let{dataRef:t,id:o}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(t.current.value),this.send({type:2,focus:4,id:o})}},selectOption:t=>{let o=this.state.options.find(r=>r.id===t);o&&this.actions.onChange(o.dataRef.current.value)},search:t=>{this.send({type:3,value:t})},clearSearch:()=>{this.send({type:4})},setButtonElement:t=>{this.send({type:7,element:t})},setOptionsElement:t=>{this.send({type:8,element:t})}});Oe(this,"selectors",{activeDescendantId(t){var i;let o=t.activeOptionIndex,r=t.options;return o===null||(i=r[o])==null?void 0:i.id},isActive(t,o){var s;let r=t.activeOptionIndex,i=t.options;return r!==null?((s=i[r])==null?void 0:s.id)===o:!1},shouldScrollIntoView(t,o){return t.__demoMode||t.listboxState!==0||t.activationTrigger===0?!1:this.isActive(t,o)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})});{let o=this.state.id,r=$e.get(null);this.disposables.add(r.on(0,i=>{!r.selectors.isTop(i,o)&&this.state.listboxState===0&&this.actions.closeListbox()})),this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,__demoMode:o=!1}){return new zn({id:t,dataRef:{current:{}},listboxState:o?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,pendingShouldSort:!1,pendingFocus:{focus:5},__demoMode:o})}reduce(t,o){return Y(o.type,dp,t,o)}};var Xn=require("react");var Ji=(0,Xn.createContext)(null);function Fr(e){let n=(0,Xn.useContext)(Ji);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Qi),t}return n}function Qi({id:e,__demoMode:n=!1}){let t=(0,Xn.useMemo)(()=>zn.new({id:e,__demoMode:n}),[]);return it(()=>t.dispose()),t}var wr=(0,re.createContext)(null);wr.displayName="ListboxDataContext";function $o(e){let n=(0,re.useContext)(wr);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,$o),t}return n}var pp=re.Fragment;function mp(e,n){let t=(0,J.useId)(),o=ge(),{value:r,defaultValue:i,form:s,name:l,onChange:a,by:c,invalid:u=!1,disabled:p=o||!1,horizontal:d=!1,multiple:f=!1,__demoMode:m=!1,...T}=e,b=d?"horizontal":"vertical",g=K(n),h=gt(i),[y=f?[]:void 0,v]=bt(r,a,h),x=Qi({id:t,__demoMode:m}),R=(0,re.useRef)({static:!1,hold:!1}),$=(0,re.useRef)(new Map),C=Sn(c),B=(0,re.useCallback)(U=>Y(S.mode,{[1]:()=>y.some(X=>C(X,U)),[0]:()=>C(y,U)}),[y]),S=(0,re.useMemo)(()=>({value:y,disabled:p,invalid:u,mode:f?1:0,orientation:b,onChange:v,compare:C,isSelected:B,optionsPropsRef:R,listRef:$}),[y,p,u,f,b,v,C,B,R,$]);V(()=>{x.state.dataRef.current=S},[S]);let P=ee(x,U=>U.listboxState),L=$e.get(null),O=ee(L,(0,re.useCallback)(U=>L.selectors.isTop(U,t),[L,t])),[I,W]=ee(x,U=>[U.buttonElement,U.optionsElement]);Et(O,[I,W],(U,X)=>{x.send({type:1}),Ft(X,1)||(U.preventDefault(),I==null||I.focus())});let A=(0,re.useMemo)(()=>({open:P===0,disabled:p,invalid:u,value:y}),[P,p,u,y]),[N,ne]=Be({inherit:!0}),q={ref:g},_=(0,re.useCallback)(()=>{if(h!==void 0)return v==null?void 0:v(h)},[v,h]),G=H();return re.default.createElement(ne,{value:N,props:{htmlFor:I==null?void 0:I.id},slot:{open:P===0,disabled:p}},re.default.createElement(en,null,re.default.createElement(Ji.Provider,{value:x},re.default.createElement(wr.Provider,{value:S},re.default.createElement(ot,{value:Y(P,{[0]:1,[1]:2})},l!=null&&y!=null&&re.default.createElement(yt,{disabled:p,data:{[l]:y},form:s,onReset:_}),G({ourProps:q,theirProps:T,slot:A,defaultTag:pp,name:"Listbox"}))))))}var Tp="button";function bp(e,n){let t=(0,J.useId)(),o=we(),r=$o("Listbox.Button"),i=Fr("Listbox.Button"),{id:s=o||`headlessui-listbox-button-${t}`,disabled:l=r.disabled||!1,autoFocus:a=!1,...c}=e,u=K(n,Jt(),i.actions.setButtonElement),p=hr(),[d,f,m]=ee(i,A=>[A.listboxState,A.buttonElement,A.optionsElement]),T=d===0;Dn(T,{trigger:f,action:(0,re.useCallback)(A=>{if(f!=null&&f.contains(A.target))return Ge.Ignore;let N=A.target.closest('[role="option"]:not([data-disabled])');return Te(N)?Ge.Select(N):m!=null&&m.contains(A.target)?Ge.Ignore:Ge.Close},[f,m]),close:i.actions.closeListbox,select:i.actions.selectActiveOption});let b=E(A=>{switch(A.key){case"Enter":Ut(A.currentTarget);break;case" ":case"ArrowDown":A.preventDefault(),i.actions.openListbox({focus:r.value?5:0});break;case"ArrowUp":A.preventDefault(),i.actions.openListbox({focus:r.value?5:3});break}}),g=E(A=>{switch(A.key){case" ":A.preventDefault();break}}),h=E(A=>{var N;if(A.button===0){if(_e(A.currentTarget))return A.preventDefault();i.state.listboxState===0?((0,Yn.flushSync)(()=>i.actions.closeListbox()),(N=i.state.buttonElement)==null||N.focus({preventScroll:!0})):(A.preventDefault(),i.actions.openListbox({focus:5}))}}),y=E(A=>A.preventDefault()),v=Fe([s]),x=Ne(),{isFocusVisible:R,focusProps:$}=ce({autoFocus:a}),{isHovered:C,hoverProps:B}=fe({isDisabled:l}),{pressed:S,pressProps:P}=Ae({disabled:l}),L=(0,re.useMemo)(()=>({open:d===0,active:S||d===0,disabled:l,invalid:r.invalid,value:r.value,hover:C,focus:R,autofocus:a}),[d,r.value,l,C,R,S,r.invalid,a]),O=ee(i,A=>A.listboxState===0),I=ae(p(),{ref:u,id:s,type:je(e,f),"aria-haspopup":"listbox","aria-controls":m==null?void 0:m.id,"aria-expanded":O,"aria-labelledby":v,"aria-describedby":x,disabled:l||void 0,autoFocus:a,onKeyDown:b,onKeyUp:g,onKeyPress:y,onPointerDown:h},$,B,P);return H()({ourProps:I,theirProps:c,slot:L,defaultTag:Tp,name:"Listbox.Button"})}var au=(0,re.createContext)(!1),gp="div",yp=3;function hp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-listbox-options-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:l=!1,...a}=e,c=qt(r),[u,p]=(0,re.useState)(null);c&&(i=!0);let d=$o("Listbox.Options"),f=Fr("Listbox.Options"),[m,T,b,g]=ee(f,D=>[D.listboxState,D.buttonElement,D.optionsElement,D.__demoMode]),h=Re(T),y=Re(b),v=He(),[x,R]=Qe(l,u,v!==null?(v&1)===1:m===0);vt(x,T,f.actions.closeListbox);let $=g?!1:s&&m===0;xt($,y);let C=g?!1:s&&m===0;Wt(C,{allowed:(0,re.useCallback)(()=>[T,b],[T,b])});let B=m!==0,P=Ir(B,T)?!1:x,L=x&&m===1,O=Hn(L,d.value),I=E(D=>d.compare(O,D)),W=ee(f,D=>{var oe;if(c==null||!((oe=c==null?void 0:c.to)!=null&&oe.includes("selection")))return null;let z=D.options.findIndex(We=>I(We.dataRef.current.value));return z===-1&&(z=0),z}),A=(()=>{if(c==null)return;if(W===null)return{...c,inner:void 0};let D=Array.from(d.listRef.current.values());return{...c,inner:{listRef:{current:D},index:W}}})(),[N,ne]=Zt(A),q=Qt(),_=K(n,c?N:null,f.actions.setOptionsElement,p),G=Se();(0,re.useEffect)(()=>{var z;let D=b;D&&m===0&&D!==((z=Pe(D))==null?void 0:z.activeElement)&&(D==null||D.focus({preventScroll:!0}))},[m,b]);let U=E(D=>{var z,oe;switch(G.dispose(),D.key){case" ":if(f.state.searchQuery!=="")return D.preventDefault(),D.stopPropagation(),f.actions.search(D.key);case"Enter":if(D.preventDefault(),D.stopPropagation(),f.state.activeOptionIndex!==null){let{dataRef:We}=f.state.options[f.state.activeOptionIndex];f.actions.onChange(We.current.value)}d.mode===0&&((0,Yn.flushSync)(()=>f.actions.closeListbox()),(z=f.state.buttonElement)==null||z.focus({preventScroll:!0}));break;case Y(d.orientation,{vertical:"ArrowDown",horizontal:"ArrowRight"}):return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:2});case Y(d.orientation,{vertical:"ArrowUp",horizontal:"ArrowLeft"}):return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:1});case"Home":case"PageUp":return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:0});case"End":case"PageDown":return D.preventDefault(),D.stopPropagation(),f.actions.goToOption({focus:3});case"Escape":D.preventDefault(),D.stopPropagation(),(0,Yn.flushSync)(()=>f.actions.closeListbox()),(oe=f.state.buttonElement)==null||oe.focus({preventScroll:!0});return;case"Tab":D.preventDefault(),D.stopPropagation(),(0,Yn.flushSync)(()=>f.actions.closeListbox()),sr(f.state.buttonElement,D.shiftKey?2:4);break;default:D.key.length===1&&(f.actions.search(D.key),G.setTimeout(()=>f.actions.clearSearch(),350));break}}),X=ee(f,D=>{var z;return(z=D.buttonElement)==null?void 0:z.id}),j=(0,re.useMemo)(()=>({open:m===0}),[m]),F=ae(c?q():{},{id:o,ref:_,"aria-activedescendant":ee(f,f.selectors.activeDescendantId),"aria-multiselectable":d.mode===1?!0:void 0,"aria-labelledby":X,"aria-orientation":d.orientation,onKeyDown:U,role:"listbox",tabIndex:m===0?0:void 0,style:{...a.style,...ne,"--button-width":It(T,!0).width},...Je(R)}),k=H(),M=(0,re.useMemo)(()=>d.mode===1?d:{...d,isSelected:I},[d,I]);return re.default.createElement(st,{enabled:i?e.static||x:!1,ownerDocument:h},re.default.createElement(wr.Provider,{value:M},k({ourProps:F,theirProps:a,slot:j,defaultTag:gp,features:yp,visible:P,name:"Listbox.Options"})))}var vp="div";function Ep(e,n){let t=(0,J.useId)(),{id:o=`headlessui-listbox-option-${t}`,disabled:r=!1,value:i,...s}=e,l=(0,re.useContext)(au)===!0,a=$o("Listbox.Option"),c=Fr("Listbox.Option"),u=ee(c,S=>c.selectors.isActive(S,o)),p=a.isSelected(i),d=(0,re.useRef)(null),f=Mr(d),m=me({disabled:r,value:i,domRef:d,get textValue(){return f()}}),T=K(n,d,S=>{S?a.listRef.current.set(o,S):a.listRef.current.delete(o)}),b=ee(c,S=>c.selectors.shouldScrollIntoView(S,o));V(()=>{if(b)return he().requestAnimationFrame(()=>{var S,P;(P=(S=d.current)==null?void 0:S.scrollIntoView)==null||P.call(S,{block:"nearest"})})},[b,d]),V(()=>{if(!l)return c.actions.registerOption(o,m),()=>c.actions.unregisterOption(o)},[m,o,l]);let g=E(S=>{var P;if(r)return S.preventDefault();c.actions.onChange(i),a.mode===0&&((0,Yn.flushSync)(()=>c.actions.closeListbox()),(P=c.state.buttonElement)==null||P.focus({preventScroll:!0}))}),h=E(()=>{if(r)return c.actions.goToOption({focus:5});c.actions.goToOption({focus:4,id:o})}),y=Ln(),v=E(S=>{y.update(S),!r&&(u||c.actions.goToOption({focus:4,id:o},0))}),x=E(S=>{y.wasMoved(S)&&(r||u||c.actions.goToOption({focus:4,id:o},0))}),R=E(S=>{y.wasMoved(S)&&(r||u&&c.actions.goToOption({focus:5}))}),$=(0,re.useMemo)(()=>({active:u,focus:u,selected:p,disabled:r,selectedOption:p&&l}),[u,p,r,l]),C=l?{}:{id:o,ref:T,role:"option",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-selected":p,disabled:void 0,onClick:g,onFocus:h,onPointerEnter:v,onMouseEnter:v,onPointerMove:x,onMouseMove:x,onPointerLeave:R,onMouseLeave:R},B=H();return!p&&l?null:B({ourProps:C,theirProps:s,slot:$,defaultTag:vp,name:"Listbox.Option"})}var xp=re.Fragment;function Pp(e,n){let{options:t,placeholder:o,...r}=e,s={ref:K(n)},l=$o("ListboxSelectedOption"),a=(0,re.useMemo)(()=>({}),[]),c=l.value===void 0||l.value===null||l.mode===1&&Array.isArray(l.value)&&l.value.length===0,u=H();return re.default.createElement(au.Provider,{value:!0},u({ourProps:s,theirProps:{...r,children:re.default.createElement(re.default.Fragment,null,o&&c?o:t)},slot:a,defaultTag:xp,name:"ListboxSelectedOption"}))}var Rp=w(mp),uu=w(bp),cu=tt,fu=w(hp),du=w(Ep),pu=w(Pp),Sp=Object.assign(Rp,{Button:uu,Label:cu,Options:fu,Option:du,SelectedOption:pu});var de=le(require("react"),1),$r=require("react-dom");function mu(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,o=Ue(n(e.items.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{items:o,activeItemIndex:r}}var Ap={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,pendingFocus:{focus:5},menuState:1}},[0](e,n){return e.menuState===0?e:{...e,__demoMode:!1,pendingFocus:n.focus,menuState:0}},[2]:(e,n)=>{var i,s,l,a,c;if(e.menuState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeItemIndex:null};if(n.focus===4)return{...t,activeItemIndex:e.items.findIndex(u=>u.id===n.id)};if(n.focus===1){let u=e.activeItemIndex;if(u!==null){let p=e.items[u].dataRef.current.domRef,d=et(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.items[d].dataRef.current.domRef;if(((s=p.current)==null?void 0:s.previousElementSibling)===f.current||((l=f.current)==null?void 0:l.previousElementSibling)===null)return{...t,activeItemIndex:d}}}}else if(n.focus===2){let u=e.activeItemIndex;if(u!==null){let p=e.items[u].dataRef.current.domRef,d=et(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(d!==null){let f=e.items[d].dataRef.current.domRef;if(((a=p.current)==null?void 0:a.nextElementSibling)===f.current||((c=f.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeItemIndex:d}}}}let o=mu(e),r=et(n,{resolveItems:()=>o.items,resolveActiveIndex:()=>o.activeItemIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled});return{...t,...o,activeItemIndex:r}},[3]:(e,n)=>{let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+o).concat(e.items.slice(0,e.activeItemIndex+o)):e.items).find(a=>{var c;return((c=a.dataRef.current.textValue)==null?void 0:c.startsWith(r))&&!a.dataRef.current.disabled}),l=s?e.items.indexOf(s):-1;return l===-1||l===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:l,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let t=e.items.concat(n.items.map(r=>r)),o=e.activeItemIndex;return e.pendingFocus.focus!==5&&(o=et(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeItemIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled})),{...e,items:t,activeItemIndex:o,pendingFocus:{focus:5},pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.items,o=[],r=new Set(n.items);for(let[i,s]of t.entries())if(r.has(s.id)&&(o.push(i),r.delete(s.id),r.size===0))break;if(o.length>0){t=t.slice();for(let i of o.reverse())t.splice(i,1)}return{...e,items:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...mu(e),pendingShouldSort:!1}:e},qn=class extends nt{constructor(t){super(t);Oe(this,"actions",{registerItem:ln(()=>{let t=[],o=new Set;return[(r,i)=>{o.has(i)||(o.add(i),t.push({id:r,dataRef:i}))},()=>(o.clear(),this.send({type:5,items:t.splice(0)}))]}),unregisterItem:ln(()=>{let t=[];return[o=>t.push(o),()=>this.send({type:6,items:t.splice(0)})]})});Oe(this,"selectors",{activeDescendantId(t){var i;let o=t.activeItemIndex,r=t.items;return o===null||(i=r[o])==null?void 0:i.id},isActive(t,o){var s;let r=t.activeItemIndex,i=t.items;return r!==null?((s=i[r])==null?void 0:s.id)===o:!1},shouldScrollIntoView(t,o){return t.__demoMode||t.menuState!==0||t.activationTrigger===0?!1:this.isActive(t,o)}});this.on(5,()=>{this.disposables.requestAnimationFrame(()=>{this.send({type:9})})});{let o=this.state.id,r=$e.get(null);this.disposables.add(r.on(0,i=>{!r.selectors.isTop(i,o)&&this.state.menuState===0&&this.send({type:1})})),this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,__demoMode:o=!1}){return new qn({id:t,__demoMode:o,menuState:o?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:5}})}reduce(t,o){return Y(o.type,Ap,t,o)}};var Jn=require("react");var Zi=(0,Jn.createContext)(null);function _r(e){let n=(0,Jn.useContext)(Zi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,es),t}return n}function es({id:e,__demoMode:n=!1}){let t=(0,Jn.useMemo)(()=>qn.new({id:e,__demoMode:n}),[]);return it(()=>t.dispose()),t}var Cp=de.Fragment;function Op(e,n){let t=(0,J.useId)(),{__demoMode:o=!1,...r}=e,i=es({id:t,__demoMode:o}),[s,l,a]=ee(i,b=>[b.menuState,b.itemsElement,b.buttonElement]),c=K(n),u=$e.get(null),p=ee(u,(0,de.useCallback)(b=>u.selectors.isTop(b,t),[u,t]));Et(p,[a,l],(b,g)=>{var h;i.send({type:1}),Ft(g,1)||(b.preventDefault(),(h=i.state.buttonElement)==null||h.focus())});let d=E(()=>{i.send({type:1})}),f=(0,de.useMemo)(()=>({open:s===0,close:d}),[s,d]),m={ref:c},T=H();return de.default.createElement(en,null,de.default.createElement(Zi.Provider,{value:i},de.default.createElement(ot,{value:Y(s,{[0]:1,[1]:2})},T({ourProps:m,theirProps:r,slot:f,defaultTag:Cp,name:"Menu"}))))}var Dp="button";function Lp(e,n){let t=_r("Menu.Button"),o=(0,J.useId)(),{id:r=`headlessui-menu-button-${o}`,disabled:i=!1,autoFocus:s=!1,...l}=e,a=(0,de.useRef)(null),c=hr(),u=K(n,a,Jt(),E(P=>t.send({type:7,element:P}))),p=E(P=>{switch(P.key){case" ":case"Enter":case"ArrowDown":P.preventDefault(),P.stopPropagation(),t.send({type:0,focus:{focus:0}});break;case"ArrowUp":P.preventDefault(),P.stopPropagation(),t.send({type:0,focus:{focus:3}});break}}),d=E(P=>{switch(P.key){case" ":P.preventDefault();break}}),[f,m,T]=ee(t,P=>[P.menuState,P.buttonElement,P.itemsElement]),b=f===0;Dn(b,{trigger:m,action:(0,de.useCallback)(P=>{if(m!=null&&m.contains(P.target))return Ge.Ignore;let L=P.target.closest('[role="menuitem"]:not([data-disabled])');return Te(L)?Ge.Select(L):T!=null&&T.contains(P.target)?Ge.Ignore:Ge.Close},[m,T]),close:(0,de.useCallback)(()=>t.send({type:1}),[]),select:(0,de.useCallback)(P=>P.click(),[])});let g=E(P=>{var L;if(P.button===0){if(_e(P.currentTarget))return P.preventDefault();i||(f===0?((0,$r.flushSync)(()=>t.send({type:1})),(L=a.current)==null||L.focus({preventScroll:!0})):(P.preventDefault(),t.send({type:0,focus:{focus:5},trigger:0})))}}),{isFocusVisible:h,focusProps:y}=ce({autoFocus:s}),{isHovered:v,hoverProps:x}=fe({isDisabled:i}),{pressed:R,pressProps:$}=Ae({disabled:i}),C=(0,de.useMemo)(()=>({open:f===0,active:R||f===0,disabled:i,hover:v,focus:h,autofocus:s}),[f,v,h,R,i,s]),B=ae(c(),{ref:u,id:r,type:je(e,a.current),"aria-haspopup":"menu","aria-controls":T==null?void 0:T.id,"aria-expanded":f===0,disabled:i||void 0,autoFocus:s,onKeyDown:p,onKeyUp:d,onPointerDown:g},y,x,$);return H()({ourProps:B,theirProps:l,slot:C,defaultTag:Dp,name:"Menu.Button"})}var Ip="div",Mp=3;function Fp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-menu-items-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:l=!1,...a}=e,c=qt(r),u=_r("Menu.Items"),[p,d]=Zt(c),f=Qt(),[m,T]=(0,de.useState)(null),b=K(n,c?p:null,E(_=>u.send({type:8,element:_})),T),[g,h]=ee(u,_=>[_.menuState,_.buttonElement]),y=Re(h),v=Re(m);c&&(i=!0);let x=He(),[R,$]=Qe(l,m,x!==null?(x&1)===1:g===0);vt(R,h,()=>{u.send({type:1})});let C=ee(u,_=>_.__demoMode),B=C?!1:s&&g===0;xt(B,v);let S=C?!1:s&&g===0;Wt(S,{allowed:(0,de.useCallback)(()=>[h,m],[h,m])});let P=g!==0,O=Ir(P,h)?!1:R;(0,de.useEffect)(()=>{let _=m;_&&g===0&&_!==(v==null?void 0:v.activeElement)&&_.focus({preventScroll:!0})},[g,m,v]),ar(g===0,{container:m,accept(_){return _.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:_.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(_){_.setAttribute("role","none")}});let I=Se(),W=E(_=>{var G,U,X;switch(I.dispose(),_.key){case" ":if(u.state.searchQuery!=="")return _.preventDefault(),_.stopPropagation(),u.send({type:3,value:_.key});case"Enter":if(_.preventDefault(),_.stopPropagation(),u.state.activeItemIndex!==null){let{dataRef:j}=u.state.items[u.state.activeItemIndex];(U=(G=j.current)==null?void 0:G.domRef.current)==null||U.click()}u.send({type:1}),pi(u.state.buttonElement);break;case"ArrowDown":return _.preventDefault(),_.stopPropagation(),u.send({type:2,focus:2});case"ArrowUp":return _.preventDefault(),_.stopPropagation(),u.send({type:2,focus:1});case"Home":case"PageUp":return _.preventDefault(),_.stopPropagation(),u.send({type:2,focus:0});case"End":case"PageDown":return _.preventDefault(),_.stopPropagation(),u.send({type:2,focus:3});case"Escape":_.preventDefault(),_.stopPropagation(),(0,$r.flushSync)(()=>u.send({type:1})),(X=u.state.buttonElement)==null||X.focus({preventScroll:!0});break;case"Tab":_.preventDefault(),_.stopPropagation(),(0,$r.flushSync)(()=>u.send({type:1})),sr(u.state.buttonElement,_.shiftKey?2:4);break;default:_.key.length===1&&(u.send({type:3,value:_.key}),I.setTimeout(()=>u.send({type:4}),350));break}}),A=E(_=>{switch(_.key){case" ":_.preventDefault();break}}),N=(0,de.useMemo)(()=>({open:g===0}),[g]),ne=ae(c?f():{},{"aria-activedescendant":ee(u,u.selectors.activeDescendantId),"aria-labelledby":ee(u,_=>{var G;return(G=_.buttonElement)==null?void 0:G.id}),id:o,onKeyDown:W,onKeyUp:A,role:"menu",tabIndex:g===0?0:void 0,ref:b,style:{...a.style,...d,"--button-width":It(h,!0).width},...Je($)}),q=H();return de.default.createElement(st,{enabled:i?e.static||R:!1,ownerDocument:y},q({ourProps:ne,theirProps:a,slot:N,defaultTag:Ip,features:Mp,visible:O,name:"Menu.Items"}))}var wp=de.Fragment;function _p(e,n){let t=(0,J.useId)(),{id:o=`headlessui-menu-item-${t}`,disabled:r=!1,...i}=e,s=_r("Menu.Item"),l=ee(s,P=>s.selectors.isActive(P,o)),a=(0,de.useRef)(null),c=K(n,a),u=ee(s,P=>s.selectors.shouldScrollIntoView(P,o));V(()=>{if(u)return he().requestAnimationFrame(()=>{var P,L;(L=(P=a.current)==null?void 0:P.scrollIntoView)==null||L.call(P,{block:"nearest"})})},[u,a]);let p=Mr(a),d=(0,de.useRef)({disabled:r,domRef:a,get textValue(){return p()}});V(()=>{d.current.disabled=r},[d,r]),V(()=>(s.actions.registerItem(o,d),()=>s.actions.unregisterItem(o)),[d,o]);let f=E(()=>{s.send({type:1})}),m=E(P=>{if(r)return P.preventDefault();s.send({type:1}),pi(s.state.buttonElement)}),T=E(()=>{if(r)return s.send({type:2,focus:5});s.send({type:2,focus:4,id:o})}),b=Ln(),g=E(P=>{b.update(P),!r&&(l||s.send({type:2,focus:4,id:o,trigger:0}))}),h=E(P=>{b.wasMoved(P)&&(r||l||s.send({type:2,focus:4,id:o,trigger:0}))}),y=E(P=>{b.wasMoved(P)&&(r||l&&s.send({type:2,focus:5}))}),[v,x]=Be(),[R,$]=ct(),C=(0,de.useMemo)(()=>({active:l,focus:l,disabled:r,close:f}),[l,r,f]),B={id:o,ref:c,role:"menuitem",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-labelledby":v,"aria-describedby":R,disabled:void 0,onClick:m,onFocus:T,onPointerEnter:g,onMouseEnter:g,onPointerMove:h,onMouseMove:h,onPointerLeave:y,onMouseLeave:y},S=H();return de.default.createElement(x,null,de.default.createElement($,null,S({ourProps:B,theirProps:i,slot:C,defaultTag:wp,name:"Menu.Item"})))}var $p="div";function kp(e,n){let[t,o]=Be(),r=e,i={ref:n,"aria-labelledby":t,role:"group"},s=H();return de.default.createElement(o,null,s({ourProps:i,theirProps:r,slot:{},defaultTag:$p,name:"Menu.Section"}))}var Hp="header";function Np(e,n){let t=(0,J.useId)(),{id:o=`headlessui-menu-heading-${t}`,...r}=e,i=tr();V(()=>i.register(o),[o,i.register]);let s={id:o,ref:n,role:"presentation",...i.props};return H()({ourProps:s,theirProps:r,slot:{},defaultTag:Hp,name:"Menu.Heading"})}var Bp="div";function Gp(e,n){let t=e,o={ref:n,role:"separator"};return H()({ourProps:o,theirProps:t,slot:{},defaultTag:Bp,name:"Menu.Separator"})}var Up=w(Op),Tu=w(Lp),bu=w(Fp),gu=w(_p),yu=w(kp),hu=w(Np),vu=w(Gp),Vp=Object.assign(Up,{Button:Tu,Items:bu,Item:gu,Section:yu,Heading:hu,Separator:vu});var Q=le(require("react"),1);var Wp={[0]:e=>e.popoverState===0?e:{...e,popoverState:0,__demoMode:!1},[1](e){return e.popoverState===1?e:{...e,popoverState:1,__demoMode:!1}},[2](e,n){return e.button===n.button?e:{...e,button:n.button}},[3](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[4](e,n){return e.panel===n.panel?e:{...e,panel:n.panel}},[5](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},Qn=class extends nt{constructor(t){super(t);Oe(this,"actions",{close:()=>this.send({type:1}),refocusableClose:t=>{this.actions.close();let o=(()=>t?Te(t)?t:"current"in t&&Te(t.current)?t.current:this.state.button:this.state.button)();o==null||o.focus()},open:()=>this.send({type:0}),setButtonId:t=>this.send({type:3,buttonId:t}),setButton:t=>this.send({type:2,button:t}),setPanelId:t=>this.send({type:5,panelId:t}),setPanel:t=>this.send({type:4,panel:t})});Oe(this,"selectors",{isPortalled:t=>{if(!t.button||!t.panel)return!1;for(let c of document.querySelectorAll("body > *"))if(Number(c==null?void 0:c.contains(t.button))^Number(c==null?void 0:c.contains(t.panel)))return!0;let o=an(),r=o.indexOf(t.button),i=(r+o.length-1)%o.length,s=(r+1)%o.length,l=o[i],a=o[s];return!t.panel.contains(l)&&!t.panel.contains(a)}});{let o=this.state.id,r=$e.get(null);this.on(0,()=>r.actions.push(o)),this.on(1,()=>r.actions.pop(o))}}static new({id:t,__demoMode:o=!1}){return new Qn({id:t,__demoMode:o,popoverState:o?0:1,buttons:{current:[]},button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:{current:null},afterPanelSentinel:{current:null},afterButtonSentinel:{current:null}})}reduce(t,o){return Y(o.type,Wp,t,o)}};var Zn=require("react");var ts=(0,Zn.createContext)(null);function ko(e){let n=(0,Zn.useContext)(ts);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ko),t}return n}function Eu({id:e,__demoMode:n=!1}){let t=(0,Zn.useMemo)(()=>Qn.new({id:e,__demoMode:n}),[]);return it(()=>t.dispose()),t}var ns=(0,Q.createContext)(null);ns.displayName="PopoverGroupContext";function xu(){return(0,Q.useContext)(ns)}var kr=(0,Q.createContext)(null);kr.displayName="PopoverPanelContext";function jp(){return(0,Q.useContext)(kr)}var Kp="div";function zp(e,n){var O;let t=(0,J.useId)(),{__demoMode:o=!1,...r}=e,i=Eu({id:t,__demoMode:o}),s=(0,Q.useRef)(null),l=K(n,Pn(I=>{s.current=I})),[a,c,u,p,d]=ee(i,(0,Q.useCallback)(I=>[I.popoverState,I.button,I.panel,I.buttonId,I.panelId],[])),f=Re((O=s.current)!=null?O:c),m=me(p),T=me(d),b=(0,Q.useMemo)(()=>({buttonId:m,panelId:T,close:i.actions.close}),[m,T,i]),g=xu(),h=g==null?void 0:g.registerPopover,y=E(()=>{var I;return(I=g==null?void 0:g.isFocusWithinPopoverGroup())!=null?I:(f==null?void 0:f.activeElement)&&((c==null?void 0:c.contains(f.activeElement))||(u==null?void 0:u.contains(f.activeElement)))});(0,Q.useEffect)(()=>h==null?void 0:h(b),[h,b]);let[v,x]=xr(),R=Io(c),$=Rr({mainTreeNode:R,portals:v,defaultContainers:[{get current(){return i.state.button}},{get current(){return i.state.panel}}]});Kt(f==null?void 0:f.defaultView,"focus",I=>{var W,A,N,ne,q,_;I.target!==window&&Le(I.target)&&i.state.popoverState===0&&(y()||i.state.button&&i.state.panel&&($.contains(I.target)||(A=(W=i.state.beforePanelSentinel.current)==null?void 0:W.contains)!=null&&A.call(W,I.target)||(ne=(N=i.state.afterPanelSentinel.current)==null?void 0:N.contains)!=null&&ne.call(N,I.target)||(_=(q=i.state.afterButtonSentinel.current)==null?void 0:q.contains)!=null&&_.call(q,I.target)||i.actions.close()))},!0);let C=a===0;Et(C,$.resolveContainers,(I,W)=>{i.actions.close(),Ft(W,1)||(I.preventDefault(),c==null||c.focus())});let B=(0,Q.useMemo)(()=>({open:a===0,close:i.actions.refocusableClose}),[a,i]),S=ee(i,(0,Q.useCallback)(I=>Y(I.popoverState,{[0]:1,[1]:2}),[])),P={ref:l},L=H();return Q.default.createElement(Kn,{node:R},Q.default.createElement(en,null,Q.default.createElement(kr.Provider,{value:null},Q.default.createElement(ts.Provider,{value:i},Q.default.createElement(rn,{value:i.actions.refocusableClose},Q.default.createElement(ot,{value:S},Q.default.createElement(x,null,L({ourProps:P,theirProps:r,slot:B,defaultTag:Kp,name:"Popover"}))))))))}var Xp="button";function Yp(e,n){let t=(0,J.useId)(),{id:o=`headlessui-popover-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,l=ko("Popover.Button"),[a,c,u,p,d,f,m]=ee(l,(0,Q.useCallback)(k=>[k.popoverState,l.selectors.isPortalled(k),k.button,k.buttonId,k.panel,k.panelId,k.afterButtonSentinel],[])),T=(0,Q.useRef)(null),b=`headlessui-focus-sentinel-${(0,J.useId)()}`,g=xu(),h=g==null?void 0:g.closeOthers,v=jp()!==null;(0,Q.useEffect)(()=>{if(!v)return l.actions.setButtonId(o),()=>l.actions.setButtonId(null)},[v,o,l]);let[x]=(0,Q.useState)(()=>Symbol()),R=K(T,n,Jt(),E(k=>{if(!v){if(k)l.state.buttons.current.push(x);else{let M=l.state.buttons.current.indexOf(x);M!==-1&&l.state.buttons.current.splice(M,1)}l.state.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),k&&l.actions.setButton(k)}})),$=K(T,n),C=Re(T),B=E(k=>{var M,D,z;if(v){if(l.state.popoverState===1)return;switch(k.key){case" ":case"Enter":k.preventDefault(),(D=(M=k.target).click)==null||D.call(M),l.actions.close(),(z=l.state.button)==null||z.focus();break}}else switch(k.key){case" ":case"Enter":k.preventDefault(),k.stopPropagation(),l.state.popoverState===1?(h==null||h(l.state.buttonId),l.actions.open()):l.actions.close();break;case"Escape":if(l.state.popoverState!==0)return h==null?void 0:h(l.state.buttonId);if(!T.current||C!=null&&C.activeElement&&!T.current.contains(C.activeElement))return;k.preventDefault(),k.stopPropagation(),l.actions.close();break}}),S=E(k=>{v||k.key===" "&&k.preventDefault()}),P=E(k=>{var M,D;_e(k.currentTarget)||r||(v?(l.actions.close(),(M=l.state.button)==null||M.focus()):(k.preventDefault(),k.stopPropagation(),l.state.popoverState===1?(h==null||h(l.state.buttonId),l.actions.open()):l.actions.close(),(D=l.state.button)==null||D.focus()))}),L=E(k=>{k.preventDefault(),k.stopPropagation()}),{isFocusVisible:O,focusProps:I}=ce({autoFocus:i}),{isHovered:W,hoverProps:A}=fe({isDisabled:r}),{pressed:N,pressProps:ne}=Ae({disabled:r}),q=a===0,_=(0,Q.useMemo)(()=>({open:q,active:N||q,disabled:r,hover:W,focus:O,autofocus:i}),[q,W,O,N,r,i]),G=je(e,u),U=v?ae({ref:$,type:G,onKeyDown:B,onClick:P,disabled:r||void 0,autoFocus:i},I,A,ne):ae({ref:R,id:p,type:G,"aria-expanded":a===0,"aria-controls":d?f:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:B,onKeyUp:S,onClick:P,onMouseDown:L},I,A,ne),X=Mo(),j=E(()=>{if(!Te(l.state.panel))return;let k=l.state.panel;function M(){Y(X.current,{[0]:()=>ve(k,1),[1]:()=>ve(k,8)})===0&&ve(an().filter(z=>z.dataset.headlessuiFocusGuard!=="true"),Y(X.current,{[0]:4,[1]:2}),{relativeTo:l.state.button})}M()}),F=H();return Q.default.createElement(Q.default.Fragment,null,F({ourProps:U,theirProps:s,slot:_,defaultTag:Xp,name:"Popover.Button"}),q&&!v&&c&&Q.default.createElement(ke,{id:b,ref:m,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:j}))}var qp="div",Jp=3;function Pu(e,n){let t=(0,J.useId)(),{id:o=`headlessui-popover-backdrop-${t}`,transition:r=!1,...i}=e,s=ko("Popover.Backdrop"),l=ee(s,(0,Q.useCallback)(h=>h.popoverState,[])),[a,c]=(0,Q.useState)(null),u=K(n,c),p=He(),[d,f]=Qe(r,a,p!==null?(p&1)===1:l===0),m=E(h=>{if(_e(h.currentTarget))return h.preventDefault();s.actions.close()}),T=(0,Q.useMemo)(()=>({open:l===0}),[l]),b={ref:u,id:o,"aria-hidden":!0,onClick:m,...Je(f)};return H()({ourProps:b,theirProps:i,slot:T,defaultTag:qp,features:Jp,visible:d,name:"Popover.Backdrop"})}var Qp="div",Zp=3;function em(e,n){let t=(0,J.useId)(),{id:o=`headlessui-popover-panel-${t}`,focus:r=!1,anchor:i,portal:s=!1,modal:l=!1,transition:a=!1,...c}=e,u=ko("Popover.Panel"),p=ee(u,u.selectors.isPortalled),[d,f,m,T,b]=ee(u,(0,Q.useCallback)(j=>[j.popoverState,j.button,j.__demoMode,j.beforePanelSentinel,j.afterPanelSentinel],[])),g=`headlessui-focus-sentinel-before-${t}`,h=`headlessui-focus-sentinel-after-${t}`,y=(0,Q.useRef)(null),v=qt(i),[x,R]=Zt(v),$=Qt();v&&(s=!0);let[C,B]=(0,Q.useState)(null),S=K(y,n,v?x:null,u.actions.setPanel,B),P=Re(f),L=Re(y);V(()=>(u.actions.setPanelId(o),()=>u.actions.setPanelId(null)),[o,u]);let O=He(),[I,W]=Qe(a,C,O!==null?(O&1)===1:d===0);vt(I,f,u.actions.close),xt(m?!1:l&&I,L);let N=E(j=>{var F;switch(j.key){case"Escape":if(u.state.popoverState!==0||!y.current||L!=null&&L.activeElement&&!y.current.contains(L.activeElement))return;j.preventDefault(),j.stopPropagation(),u.actions.close(),(F=u.state.button)==null||F.focus();break}});(0,Q.useEffect)(()=>{var j;e.static||d===1&&((j=e.unmount)==null||j)&&u.actions.setPanel(null)},[d,e.unmount,e.static,u]),(0,Q.useEffect)(()=>{if(m||!r||d!==0||!y.current)return;let j=L==null?void 0:L.activeElement;y.current.contains(j)||ve(y.current,1)},[m,r,y.current,d]);let ne=(0,Q.useMemo)(()=>({open:d===0,close:u.actions.refocusableClose}),[d,u]),q=ae(v?$():{},{ref:S,id:o,onKeyDown:N,onBlur:r&&d===0?j=>{var k,M,D,z,oe;let F=j.relatedTarget;F&&y.current&&((k=y.current)!=null&&k.contains(F)||(u.actions.close(),((D=(M=T.current)==null?void 0:M.contains)!=null&&D.call(M,F)||(oe=(z=b.current)==null?void 0:z.contains)!=null&&oe.call(z,F))&&F.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...c.style,...R,"--button-width":It(f,!0).width},...Je(W)}),_=Mo(),G=E(()=>{let j=y.current;if(!j)return;function F(){Y(_.current,{[0]:()=>{var M;ve(j,1)===0&&((M=u.state.afterPanelSentinel.current)==null||M.focus())},[1]:()=>{var k;(k=u.state.button)==null||k.focus({preventScroll:!0})}})}F()}),U=E(()=>{let j=y.current;if(!j)return;function F(){Y(_.current,{[0]:()=>{if(!u.state.button)return;let k=an(),M=k.indexOf(u.state.button),D=k.slice(0,M+1),oe=[...k.slice(M+1),...D];for(let We of oe.slice())if(We.dataset.headlessuiFocusGuard==="true"||C!=null&&C.contains(We)){let ds=oe.indexOf(We);ds!==-1&&oe.splice(ds,1)}ve(oe,1,{sorted:!1})},[1]:()=>{var M;ve(j,2)===0&&((M=u.state.button)==null||M.focus())}})}F()}),X=H();return Q.default.createElement(Nn,null,Q.default.createElement(kr.Provider,{value:o},Q.default.createElement(rn,{value:u.actions.refocusableClose},Q.default.createElement(st,{enabled:s?e.static||I:!1,ownerDocument:P},I&&p&&Q.default.createElement(ke,{id:g,ref:T,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:G}),X({ourProps:q,theirProps:c,slot:ne,defaultTag:Qp,features:Zp,visible:I,name:"Popover.Panel"}),I&&p&&Q.default.createElement(ke,{id:h,ref:b,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:U})))))}var tm="div";function nm(e,n){let t=(0,Q.useRef)(null),o=K(t,n),[r,i]=(0,Q.useState)([]),s=E(T=>{i(b=>{let g=b.indexOf(T);if(g!==-1){let h=b.slice();return h.splice(g,1),h}return b})}),l=E(T=>(i(b=>[...b,T]),()=>s(T))),a=E(()=>{var g;let T=Pe(t);if(!T)return!1;let b=T.activeElement;return(g=t.current)!=null&&g.contains(b)?!0:r.some(h=>{var y,v;return((y=T.getElementById(h.buttonId.current))==null?void 0:y.contains(b))||((v=T.getElementById(h.panelId.current))==null?void 0:v.contains(b))})}),c=E(T=>{for(let b of r)b.buttonId.current!==T&&b.close()}),u=(0,Q.useMemo)(()=>({registerPopover:l,unregisterPopover:s,isFocusWithinPopoverGroup:a,closeOthers:c}),[l,s,a,c]),p=(0,Q.useMemo)(()=>({}),[]),d=e,f={ref:o},m=H();return Q.default.createElement(Kn,null,Q.default.createElement(ns.Provider,{value:u},m({ourProps:f,theirProps:d,slot:p,defaultTag:tm,name:"Popover.Group"})))}var om=w(zp),Ru=w(Yp),Su=w(Pu),Au=w(Pu),Cu=w(em),Ou=w(nm),rm=Object.assign(om,{Button:Ru,Backdrop:Au,Overlay:Su,Panel:Cu,Group:Ou});var pe=le(require("react"),1);var im={[0](e,n){let t=[...e.options,{id:n.id,element:n.element,propsRef:n.propsRef}];return{...e,options:Ue(t,o=>o.element.current)}},[1](e,n){let t=e.options.slice(),o=e.options.findIndex(r=>r.id===n.id);return o===-1?e:(t.splice(o,1),{...e,options:t})}},os=(0,pe.createContext)(null);os.displayName="RadioGroupDataContext";function rs(e){let n=(0,pe.useContext)(os);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,rs),t}return n}var is=(0,pe.createContext)(null);is.displayName="RadioGroupActionsContext";function ss(e){let n=(0,pe.useContext)(is);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ss),t}return n}function sm(e,n){return Y(n.type,im,e,n)}var lm="div";function am(e,n){let t=(0,J.useId)(),o=ge(),{id:r=`headlessui-radiogroup-${t}`,value:i,form:s,name:l,onChange:a,by:c,disabled:u=o||!1,defaultValue:p,tabIndex:d=0,...f}=e,m=Sn(c),[T,b]=(0,pe.useReducer)(sm,{options:[]}),g=T.options,[h,y]=Be(),[v,x]=ct(),R=(0,pe.useRef)(null),$=K(R,n),C=gt(p),[B,S]=bt(i,a,C),P=(0,pe.useMemo)(()=>g.find(U=>!U.propsRef.current.disabled),[g]),L=(0,pe.useMemo)(()=>g.some(U=>m(U.propsRef.current.value,B)),[g,B]),O=E(U=>{var j;if(u||m(U,B))return!1;let X=(j=g.find(F=>m(F.propsRef.current.value,U)))==null?void 0:j.propsRef.current;return X!=null&&X.disabled?!1:(S==null||S(U),!0)}),I=E(U=>{let X=R.current;if(!X)return;let j=Pe(X),F=g.filter(k=>k.propsRef.current.disabled===!1).map(k=>k.element.current);switch(U.key){case"Enter":Ut(U.currentTarget);break;case"ArrowLeft":case"ArrowUp":if(U.preventDefault(),U.stopPropagation(),ve(F,18)===2){let M=g.find(D=>D.element.current===(j==null?void 0:j.activeElement));M&&O(M.propsRef.current.value)}break;case"ArrowRight":case"ArrowDown":if(U.preventDefault(),U.stopPropagation(),ve(F,20)===2){let M=g.find(D=>D.element.current===(j==null?void 0:j.activeElement));M&&O(M.propsRef.current.value)}break;case" ":{U.preventDefault(),U.stopPropagation();let k=g.find(M=>M.element.current===(j==null?void 0:j.activeElement));k&&O(k.propsRef.current.value)}break}}),W=E(U=>(b({type:0,...U}),()=>b({type:1,id:U.id}))),A=(0,pe.useMemo)(()=>({value:B,firstOption:P,containsCheckedOption:L,disabled:u,compare:m,tabIndex:d,...T}),[B,P,L,u,m,d,T]),N=(0,pe.useMemo)(()=>({registerOption:W,change:O}),[W,O]),ne={ref:$,id:r,role:"radiogroup","aria-labelledby":h,"aria-describedby":v,onKeyDown:I},q=(0,pe.useMemo)(()=>({value:B}),[B]),_=(0,pe.useCallback)(()=>{if(C!==void 0)return O(C)},[O,C]),G=H();return pe.default.createElement(x,{name:"RadioGroup.Description"},pe.default.createElement(y,{name:"RadioGroup.Label"},pe.default.createElement(is.Provider,{value:N},pe.default.createElement(os.Provider,{value:A},l!=null&&pe.default.createElement(yt,{disabled:u,data:{[l]:B||"on"},overrides:{type:"radio",checked:B!=null},form:s,onReset:_}),G({ourProps:ne,theirProps:f,slot:q,defaultTag:lm,name:"RadioGroup"})))))}var um="div";function cm(e,n){var P;let t=rs("RadioGroup.Option"),o=ss("RadioGroup.Option"),r=(0,J.useId)(),{id:i=`headlessui-radiogroup-option-${r}`,value:s,disabled:l=t.disabled||!1,autoFocus:a=!1,...c}=e,u=(0,pe.useRef)(null),p=K(u,n),[d,f]=Be(),[m,T]=ct(),b=me({value:s,disabled:l});V(()=>o.registerOption({id:i,element:u,propsRef:b}),[i,o,u,b]);let g=E(L=>{var O;if(_e(L.currentTarget))return L.preventDefault();o.change(s)&&((O=u.current)==null||O.focus())}),h=((P=t.firstOption)==null?void 0:P.id)===i,{isFocusVisible:y,focusProps:v}=ce({autoFocus:a}),{isHovered:x,hoverProps:R}=fe({isDisabled:l}),$=t.compare(t.value,s),C=ae({ref:p,id:i,role:"radio","aria-checked":$?"true":"false","aria-labelledby":d,"aria-describedby":m,"aria-disabled":l?!0:void 0,tabIndex:(()=>l?-1:$||!t.containsCheckedOption&&h?t.tabIndex:-1)(),onClick:l?void 0:g,autoFocus:a},v,R),B=(0,pe.useMemo)(()=>({checked:$,disabled:l,active:y,hover:x,focus:y,autofocus:a}),[$,l,x,y,a]),S=H();return pe.default.createElement(T,{name:"RadioGroup.Description"},pe.default.createElement(f,{name:"RadioGroup.Label"},S({ourProps:C,theirProps:c,slot:B,defaultTag:um,name:"RadioGroup.Option"})))}var fm="span";function dm(e,n){var P;let t=rs("Radio"),o=ss("Radio"),r=(0,J.useId)(),i=we(),s=ge(),{id:l=i||`headlessui-radio-${r}`,value:a,disabled:c=t.disabled||s||!1,autoFocus:u=!1,...p}=e,d=(0,pe.useRef)(null),f=K(d,n),m=Fe(),T=Ne(),b=me({value:a,disabled:c});V(()=>o.registerOption({id:l,element:d,propsRef:b}),[l,o,d,b]);let g=E(L=>{var O;if(_e(L.currentTarget))return L.preventDefault();o.change(a)&&((O=d.current)==null||O.focus())}),{isFocusVisible:h,focusProps:y}=ce({autoFocus:u}),{isHovered:v,hoverProps:x}=fe({isDisabled:c}),R=((P=t.firstOption)==null?void 0:P.id)===l,$=t.compare(t.value,a),C=ae({ref:f,id:l,role:"radio","aria-checked":$?"true":"false","aria-labelledby":m,"aria-describedby":T,"aria-disabled":c?!0:void 0,tabIndex:(()=>c?-1:$||!t.containsCheckedOption&&R?t.tabIndex:-1)(),autoFocus:u,onClick:c?void 0:g},y,x),B=(0,pe.useMemo)(()=>({checked:$,disabled:c,hover:v,focus:h,autofocus:u}),[$,c,v,h,u]);return H()({ourProps:C,theirProps:p,slot:B,defaultTag:fm,name:"Radio"})}var pm=w(am),Du=w(cm),Lu=w(dm),Iu=tt,Mu=Lt,mm=Object.assign(pm,{Option:Du,Radio:Lu,Label:Iu,Description:Mu});var Fu=require("react");var Tm="select";function bm(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-select-${t}`,disabled:s=r||!1,invalid:l=!1,autoFocus:a=!1,...c}=e,u=Fe(),p=Ne(),{isFocusVisible:d,focusProps:f}=ce({autoFocus:a}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),{pressed:b,pressProps:g}=Ae({disabled:s}),h=ae({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":l?"true":void 0,disabled:s||void 0,autoFocus:a},f,T,g),y=(0,Fu.useMemo)(()=>({disabled:s,invalid:l,hover:m,focus:d,active:b,autofocus:a}),[s,l,m,d,b,a]);return H()({ourProps:h,theirProps:c,slot:y,defaultTag:Tm,name:"Select"})}var gm=w(bm);var xe=le(require("react"),1);var ls=(0,xe.createContext)(null);ls.displayName="GroupContext";var ym=xe.Fragment;function hm(e){var p;let[n,t]=(0,xe.useState)(null),[o,r]=Be(),[i,s]=ct(),l=(0,xe.useMemo)(()=>({switch:n,setSwitch:t}),[n,t]),a={},c=e,u=H();return xe.default.createElement(s,{name:"Switch.Description",value:i},xe.default.createElement(r,{name:"Switch.Label",value:o,props:{htmlFor:(p=l.switch)==null?void 0:p.id,onClick(d){n&&(po(d.currentTarget)&&d.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},xe.default.createElement(ls.Provider,{value:l},u({ourProps:a,theirProps:c,slot:{},defaultTag:ym,name:"Switch.Group"}))))}var vm="button";function Em(e,n){var k;let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-switch-${t}`,disabled:s=r||!1,checked:l,defaultChecked:a,onChange:c,name:u,value:p,form:d,autoFocus:f=!1,...m}=e,T=(0,xe.useContext)(ls),[b,g]=(0,xe.useState)(null),h=(0,xe.useRef)(null),y=K(h,n,T===null?null:T.setSwitch,g),v=gt(a),[x,R]=bt(l,c,v!=null?v:!1),$=Se(),[C,B]=(0,xe.useState)(!1),S=E(()=>{B(!0),R==null||R(!x),$.nextFrame(()=>{B(!1)})}),P=E(M=>{if(_e(M.currentTarget))return M.preventDefault();M.preventDefault(),S()}),L=E(M=>{M.key===" "?(M.preventDefault(),S()):M.key==="Enter"&&Ut(M.currentTarget)}),O=E(M=>M.preventDefault()),I=Fe(),W=Ne(),{isFocusVisible:A,focusProps:N}=ce({autoFocus:f}),{isHovered:ne,hoverProps:q}=fe({isDisabled:s}),{pressed:_,pressProps:G}=Ae({disabled:s}),U=(0,xe.useMemo)(()=>({checked:x,disabled:s,hover:ne,focus:A,active:_,autofocus:f,changing:C}),[x,ne,A,_,s,C,f]),X=ae({id:i,ref:y,role:"switch",type:je(e,b),tabIndex:e.tabIndex===-1?0:(k=e.tabIndex)!=null?k:0,"aria-checked":x,"aria-labelledby":I,"aria-describedby":W,disabled:s||void 0,autoFocus:f,onClick:P,onKeyUp:L,onKeyPress:O},N,q,G),j=(0,xe.useCallback)(()=>{if(v!==void 0)return R==null?void 0:R(v)},[R,v]),F=H();return xe.default.createElement(xe.default.Fragment,null,u!=null&&xe.default.createElement(yt,{disabled:s,data:{[u]:p||"on"},overrides:{type:"checkbox",checked:x},form:d,onReset:j}),F({ourProps:X,theirProps:m,slot:U,defaultTag:vm,name:"Switch"}))}var xm=w(Em),wu=hm,_u=tt,$u=Lt,Pm=Object.assign(xm,{Group:wu,Label:_u,Description:$u});var be=le(require("react"),1);var Hr=le(require("react"),1);function ku({onFocus:e}){let[n,t]=(0,Hr.useState)(!0),o=gn();return n?Hr.default.createElement(ke,{as:"button",type:"button",features:2,onFocus:r=>{r.preventDefault();let i,s=50;function l(){if(s--<=0){i&&cancelAnimationFrame(i);return}if(e()){if(cancelAnimationFrame(i),!o.current)return;t(!1);return}i=requestAnimationFrame(l)}i=requestAnimationFrame(l)}}):null}var lt=le(require("react"),1),Hu=lt.createContext(null);function Rm(){return{groups:new Map,get(e,n){var s;let t=this.groups.get(e);t||(t=new Map,this.groups.set(e,t));let o=(s=t.get(n))!=null?s:0;t.set(n,o+1);let r=Array.from(t.keys()).indexOf(n);function i(){let l=t.get(n);l>1?t.set(n,l-1):t.delete(n)}return[r,i]}}}function Nu({children:e}){let n=lt.useRef(Rm());return lt.createElement(Hu.Provider,{value:n},e)}function as(e){let n=lt.useContext(Hu);if(!n)throw new Error("You must wrap your component in a <StableCollection>");let t=lt.useId(),[o,r]=n.current.get(e,t);return lt.useEffect(()=>r,[]),o}var Sm={[0](e,n){var u;let t=Ue(e.tabs,p=>p.current),o=Ue(e.panels,p=>p.current),r=t.filter(p=>{var d;return!((d=p.current)!=null&&d.hasAttribute("disabled"))}),i={...e,tabs:t,panels:o};if(n.index<0||n.index>t.length-1){let p=Y(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>Y(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(r.length===0)return i;let d=Y(p,{[0]:()=>t.indexOf(r[0]),[1]:()=>t.indexOf(r[r.length-1])});return{...i,selectedIndex:d===-1?e.selectedIndex:d}}let s=t.slice(0,n.index),a=[...t.slice(n.index),...s].find(p=>r.includes(p));if(!a)return i;let c=(u=t.indexOf(a))!=null?u:e.selectedIndex;return c===-1&&(c=e.selectedIndex),{...i,selectedIndex:c}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],o=Ue([...e.tabs,n.tab],i=>i.current),r=e.selectedIndex;return e.info.current.isControlled||(r=o.indexOf(t),r===-1&&(r=e.selectedIndex)),{...e,tabs:o,selectedIndex:r}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:Ue([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},us=(0,be.createContext)(null);us.displayName="TabsDataContext";function eo(e){let n=(0,be.useContext)(us);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eo),t}return n}var cs=(0,be.createContext)(null);cs.displayName="TabsActionsContext";function fs(e){let n=(0,be.useContext)(cs);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,fs),t}return n}function Am(e,n){return Y(n.type,Sm,e,n)}var Cm="div";function Om(e,n){let{defaultIndex:t=0,vertical:o=!1,manual:r=!1,onChange:i,selectedIndex:s=null,...l}=e,a=o?"vertical":"horizontal",c=r?"manual":"auto",u=s!==null,p=me({isControlled:u}),d=K(n),[f,m]=(0,be.useReducer)(Am,{info:p,selectedIndex:s!=null?s:t,tabs:[],panels:[]}),T=(0,be.useMemo)(()=>({selectedIndex:f.selectedIndex}),[f.selectedIndex]),b=me(i||(()=>{})),g=me(f.tabs),h=(0,be.useMemo)(()=>({orientation:a,activation:c,...f}),[a,c,f]),y=E(S=>(m({type:1,tab:S}),()=>m({type:2,tab:S}))),v=E(S=>(m({type:3,panel:S}),()=>m({type:4,panel:S}))),x=E(S=>{R.current!==S&&b.current(S),u||m({type:0,index:S})}),R=me(u?e.selectedIndex:f.selectedIndex),$=(0,be.useMemo)(()=>({registerTab:y,registerPanel:v,change:x}),[]);V(()=>{m({type:0,index:s!=null?s:t})},[s]),V(()=>{if(R.current===void 0||f.tabs.length<=0)return;let S=Ue(f.tabs,L=>L.current);S.some((L,O)=>f.tabs[O]!==L)&&x(S.indexOf(f.tabs[R.current]))});let C={ref:d},B=H();return be.default.createElement(Nu,null,be.default.createElement(cs.Provider,{value:$},be.default.createElement(us.Provider,{value:h},h.tabs.length<=0&&be.default.createElement(ku,{onFocus:()=>{var S,P;for(let L of g.current)if(((S=L.current)==null?void 0:S.tabIndex)===0)return(P=L.current)==null||P.focus(),!0;return!1}}),B({ourProps:C,theirProps:l,slot:T,defaultTag:Cm,name:"Tabs"}))))}var Dm="div";function Lm(e,n){let{orientation:t,selectedIndex:o}=eo("Tab.List"),r=K(n),i=(0,be.useMemo)(()=>({selectedIndex:o}),[o]),s=e,l={ref:r,role:"tablist","aria-orientation":t};return H()({ourProps:l,theirProps:s,slot:i,defaultTag:Dm,name:"Tabs.List"})}var Im="button";function Mm(e,n){var q,_;let t=(0,J.useId)(),{id:o=`headlessui-tabs-tab-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,{orientation:l,activation:a,selectedIndex:c,tabs:u,panels:p}=eo("Tab"),d=fs("Tab"),f=eo("Tab"),[m,T]=(0,be.useState)(null),b=(0,be.useRef)(null),g=K(b,n,T);V(()=>d.registerTab(b),[d,b]);let h=as("tabs"),y=u.indexOf(b);y===-1&&(y=h);let v=y===c,x=E(G=>{var X;let U=G();if(U===2&&a==="auto"){let j=(X=Pe(b))==null?void 0:X.activeElement,F=f.tabs.findIndex(k=>k.current===j);F!==-1&&d.change(F)}return U}),R=E(G=>{let U=u.map(j=>j.current).filter(Boolean);if(G.key===" "||G.key==="Enter"){G.preventDefault(),G.stopPropagation(),d.change(y);return}switch(G.key){case"Home":case"PageUp":return G.preventDefault(),G.stopPropagation(),x(()=>ve(U,1));case"End":case"PageDown":return G.preventDefault(),G.stopPropagation(),x(()=>ve(U,8))}if(x(()=>Y(l,{vertical(){return G.key==="ArrowUp"?ve(U,18):G.key==="ArrowDown"?ve(U,20):0},horizontal(){return G.key==="ArrowLeft"?ve(U,18):G.key==="ArrowRight"?ve(U,20):0}}))===2)return G.preventDefault()}),$=(0,be.useRef)(!1),C=E(()=>{var G;$.current||($.current=!0,(G=b.current)==null||G.focus({preventScroll:!0}),d.change(y),Dt(()=>{$.current=!1}))}),B=E(G=>{G.preventDefault()}),{isFocusVisible:S,focusProps:P}=ce({autoFocus:i}),{isHovered:L,hoverProps:O}=fe({isDisabled:r}),{pressed:I,pressProps:W}=Ae({disabled:r}),A=(0,be.useMemo)(()=>({selected:v,hover:L,active:I,focus:S,autofocus:i,disabled:r}),[v,L,S,I,i,r]),N=ae({ref:g,onKeyDown:R,onMouseDown:B,onClick:C,id:o,role:"tab",type:je(e,m),"aria-controls":(_=(q=p[y])==null?void 0:q.current)==null?void 0:_.id,"aria-selected":v,tabIndex:v?0:-1,disabled:r||void 0,autoFocus:i},P,O,W);return H()({ourProps:N,theirProps:s,slot:A,defaultTag:Im,name:"Tabs.Tab"})}var Fm="div";function wm(e,n){let{selectedIndex:t}=eo("Tab.Panels"),o=K(n),r=(0,be.useMemo)(()=>({selectedIndex:t}),[t]),i=e,s={ref:o};return H()({ourProps:s,theirProps:i,slot:r,defaultTag:Fm,name:"Tabs.Panels"})}var _m="div",$m=3;function km(e,n){var v,x,R,$;let t=(0,J.useId)(),{id:o=`headlessui-tabs-panel-${t}`,tabIndex:r=0,...i}=e,{selectedIndex:s,tabs:l,panels:a}=eo("Tab.Panel"),c=fs("Tab.Panel"),u=(0,be.useRef)(null),p=K(u,n);V(()=>c.registerPanel(u),[c,u]);let d=as("panels"),f=a.indexOf(u);f===-1&&(f=d);let m=f===s,{isFocusVisible:T,focusProps:b}=ce(),g=(0,be.useMemo)(()=>({selected:m,focus:T}),[m,T]),h=ae({ref:p,id:o,role:"tabpanel","aria-labelledby":(x=(v=l[f])==null?void 0:v.current)==null?void 0:x.id,tabIndex:m?r:-1},b),y=H();return!m&&((R=i.unmount)==null||R)&&!(($=i.static)!=null&&$)?be.default.createElement(ke,{"aria-hidden":"true",...h}):y({ourProps:h,theirProps:i,slot:g,defaultTag:_m,features:$m,visible:m,name:"Tabs.Panel"})}var Hm=w(Mm),Bu=w(Om),Gu=w(Lm),Uu=w(wm),Vu=w(km),Nm=Object.assign(Hm,{Group:Bu,List:Gu,Panels:Uu,Panel:Vu});var Wu=require("react");var Bm="textarea";function Gm(e,n){let t=(0,J.useId)(),o=we(),r=ge(),{id:i=o||`headlessui-textarea-${t}`,disabled:s=r||!1,autoFocus:l=!1,invalid:a=!1,...c}=e,u=Fe(),p=Ne(),{isFocused:d,focusProps:f}=ce({autoFocus:l}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),b=ae({ref:n,id:i,"aria-labelledby":u,"aria-describedby":p,"aria-invalid":a?"true":void 0,disabled:s||void 0,autoFocus:l},f,T),g=(0,Wu.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:d,autofocus:l}),[s,a,m,d,l]);return H()({ourProps:b,theirProps:c,slot:g,defaultTag:Bm,name:"Textarea"})}var Um=w(Gm);
