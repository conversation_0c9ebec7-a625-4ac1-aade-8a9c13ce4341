import { Analytics } from '@segment/analytics-node';
import { AnalyticsEvents } from './events.js';
import '../utils/detect-endpoint-type.utils.js';

declare class AnalyticsService {
    private readonly authData?;
    private segment;
    private globalProperties;
    private userId;
    private email;
    private organizationId;
    private config;
    constructor(authData?: {
        userId: string;
        email: string;
        organizationId: string;
    } | undefined);
    private getAnonymousId;
    track<K extends keyof AnalyticsEvents>(event: Omit<Parameters<Analytics['track']>[0], 'userId'> & {
        event: K;
        properties: AnalyticsEvents[K];
    }): void;
}

export { AnalyticsService };
