// src/utils/detect-endpoint-type.utils.ts
var RemoteEndpointType = /* @__PURE__ */ ((RemoteEndpointType2) => {
  RemoteEndpointType2["LangGraphPlatform"] = "LangGraphPlatform";
  RemoteEndpointType2["CopilotKit"] = "CopilotKit";
  RemoteEndpointType2["Invalid"] = "Invalid";
  return RemoteEndpointType2;
})(RemoteEndpointType || {});
var removeTrailingSlash = (url) => url.replace(/\/$/, "");
var getHumanReadableEndpointType = (type) => {
  switch (type) {
    case "LangGraphPlatform" /* LangGraphPlatform */:
      return "LangGraph Platform";
    case "CopilotKit" /* CopilotKit */:
      return "CopilotKit";
    default:
      return "Invalid";
  }
};
async function detectRemoteEndpointType(url) {
  const promises = [
    isLangGraphPlatformEndpoint(url),
    isCopilotKitEndpoint(url)
  ];
  if (!url.endsWith("/copilotkit")) {
    promises.push(isCopilotKitEndpoint(`${removeTrailingSlash(url)}/copilotkit`));
  }
  const results = await Promise.all(promises);
  if (results[0]) {
    return {
      url,
      type: "LangGraphPlatform" /* LangGraphPlatform */,
      humanReadableType: "LangGraph Platform"
    };
  }
  if (results[1]) {
    return {
      url,
      type: "CopilotKit" /* CopilotKit */,
      humanReadableType: "CopilotKit"
    };
  }
  if (results[2]) {
    return {
      url: `${removeTrailingSlash(url)}/copilotkit`,
      type: "CopilotKit" /* CopilotKit */,
      humanReadableType: "CopilotKit"
    };
  }
  return {
    url,
    type: "Invalid" /* Invalid */,
    humanReadableType: "Invalid"
  };
}
async function isLangGraphPlatformEndpoint(url, retries = 0) {
  let response;
  try {
    response = await fetch(`${url}/assistants/search`, {
      method: "POST",
      body: JSON.stringify({
        metadata: {},
        limit: 99,
        offset: 0
      })
    });
  } catch (error) {
    return false;
  }
  if (!response.ok) {
    if (response.status === 502) {
      if (retries < 3) {
        console.log("RETRYING LGC", retries + 1);
        return isLangGraphPlatformEndpoint(url, retries + 1);
      }
    }
    if (response.status === 403) {
      return true;
    }
    return false;
  }
  const data = await response.json();
  if (data[0].assistant_id) {
    return true;
  }
  return false;
}
async function isCopilotKitEndpoint(url, retries = 0) {
  let response;
  try {
    response = await fetch(`${url}/info`, {
      method: "POST",
      body: JSON.stringify({})
    });
  } catch (error) {
    return false;
  }
  if (!response.ok) {
    if (response.status === 502) {
      if (retries < 3) {
        console.log("RETRYING CK", retries + 1);
        return isCopilotKitEndpoint(url, retries + 1);
      }
    }
    return false;
  }
  const data = await response.json();
  if (data.agents && data.actions) {
    return true;
  }
  return false;
}
export {
  RemoteEndpointType,
  detectRemoteEndpointType,
  getHumanReadableEndpointType
};
//# sourceMappingURL=detect-endpoint-type.utils.js.map