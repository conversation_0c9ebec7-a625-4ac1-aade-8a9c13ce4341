{"version": 3, "sources": ["../src/client/types.ts"], "sourcesContent": ["import { randomId } from \"@copilotkit/shared\";\nimport {\n  ActionExecutionMessageInput,\n  MessageRole,\n  MessageStatus,\n  ResultMessageInput,\n  TextMessageInput,\n  BaseMessageOutput,\n  AgentStateMessageInput,\n  MessageStatusCode,\n  LangGraphInterruptEvent as GqlLangGraphInterruptEvent,\n  MetaEventName,\n  CopilotKitLangGraphInterruptEvent as GqlCopilotKitLangGraphInterruptEvent,\n  ImageMessageInput,\n} from \"../graphql/@generated/graphql\";\nimport { parseJson } from \"@copilotkit/shared\";\n\ntype MessageType =\n  | \"TextMessage\"\n  | \"ActionExecutionMessage\"\n  | \"ResultMessage\"\n  | \"AgentStateMessage\"\n  | \"ImageMessage\";\n\nexport class Message {\n  type: MessageType;\n  id: BaseMessageOutput[\"id\"];\n  createdAt: BaseMessageOutput[\"createdAt\"];\n  status: MessageStatus;\n\n  constructor(props: any) {\n    props.id ??= randomId();\n    props.status ??= { code: MessageStatusCode.Success };\n    props.createdAt ??= new Date();\n    Object.assign(this, props);\n  }\n\n  isTextMessage(): this is TextMessage {\n    return this.type === \"TextMessage\";\n  }\n\n  isActionExecutionMessage(): this is ActionExecutionMessage {\n    return this.type === \"ActionExecutionMessage\";\n  }\n\n  isResultMessage(): this is ResultMessage {\n    return this.type === \"ResultMessage\";\n  }\n\n  isAgentStateMessage(): this is AgentStateMessage {\n    return this.type === \"AgentStateMessage\";\n  }\n\n  isImageMessage(): this is ImageMessage {\n    return this.type === \"ImageMessage\";\n  }\n}\n\n// alias Role to MessageRole\nexport const Role = MessageRole;\n\n// when constructing any message, the base fields are optional\ntype MessageConstructorOptions = Partial<Message>;\n\ntype TextMessageConstructorOptions = MessageConstructorOptions & TextMessageInput;\n\nexport class TextMessage extends Message implements TextMessageConstructorOptions {\n  role: TextMessageInput[\"role\"];\n  content: TextMessageInput[\"content\"];\n  parentMessageId: TextMessageInput[\"parentMessageId\"];\n\n  constructor(props: TextMessageConstructorOptions) {\n    super(props);\n    this.type = \"TextMessage\";\n  }\n}\n\ntype ActionExecutionMessageConstructorOptions = MessageConstructorOptions &\n  Omit<ActionExecutionMessageInput, \"arguments\"> & {\n    arguments: Record<string, any>;\n  };\n\nexport class ActionExecutionMessage\n  extends Message\n  implements Omit<ActionExecutionMessageInput, \"arguments\" | \"scope\">\n{\n  name: ActionExecutionMessageInput[\"name\"];\n  arguments: Record<string, any>;\n  parentMessageId: ActionExecutionMessageInput[\"parentMessageId\"];\n  constructor(props: ActionExecutionMessageConstructorOptions) {\n    super(props);\n    this.type = \"ActionExecutionMessage\";\n  }\n}\n\ntype ResultMessageConstructorOptions = MessageConstructorOptions & ResultMessageInput;\n\nexport class ResultMessage extends Message implements ResultMessageConstructorOptions {\n  actionExecutionId: ResultMessageInput[\"actionExecutionId\"];\n  actionName: ResultMessageInput[\"actionName\"];\n  result: ResultMessageInput[\"result\"];\n\n  constructor(props: ResultMessageConstructorOptions) {\n    super(props);\n    this.type = \"ResultMessage\";\n  }\n\n  static decodeResult(result: string): any {\n    return parseJson(result, result);\n  }\n\n  static encodeResult(result: any): string {\n    if (result === undefined) {\n      return \"\";\n    } else if (typeof result === \"string\") {\n      return result;\n    } else {\n      return JSON.stringify(result);\n    }\n  }\n}\n\nexport class AgentStateMessage extends Message implements Omit<AgentStateMessageInput, \"state\"> {\n  agentName: AgentStateMessageInput[\"agentName\"];\n  state: any;\n  running: AgentStateMessageInput[\"running\"];\n  threadId: AgentStateMessageInput[\"threadId\"];\n  role: AgentStateMessageInput[\"role\"];\n  nodeName: AgentStateMessageInput[\"nodeName\"];\n  runId: AgentStateMessageInput[\"runId\"];\n  active: AgentStateMessageInput[\"active\"];\n\n  constructor(props: any) {\n    super(props);\n    this.type = \"AgentStateMessage\";\n  }\n}\n\ntype ImageMessageConstructorOptions = MessageConstructorOptions & ImageMessageInput;\n\nexport class ImageMessage extends Message implements ImageMessageConstructorOptions {\n  format: ImageMessageInput[\"format\"];\n  bytes: ImageMessageInput[\"bytes\"];\n  role: ImageMessageInput[\"role\"];\n  parentMessageId: ImageMessageInput[\"parentMessageId\"];\n\n  constructor(props: ImageMessageConstructorOptions) {\n    super(props);\n    this.type = \"ImageMessage\";\n  }\n}\n\nexport function langGraphInterruptEvent(\n  eventProps: Omit<LangGraphInterruptEvent, \"name\" | \"type\" | \"__typename\">,\n): LangGraphInterruptEvent {\n  return { ...eventProps, name: MetaEventName.LangGraphInterruptEvent, type: \"MetaEvent\" };\n}\n\nexport type LangGraphInterruptEvent<TValue extends any = any> = GqlLangGraphInterruptEvent & {\n  value: TValue;\n};\n\ntype CopilotKitLangGraphInterruptEvent<TValue extends any = any> =\n  GqlCopilotKitLangGraphInterruptEvent & {\n    data: GqlCopilotKitLangGraphInterruptEvent[\"data\"] & { value: TValue };\n  };\n\nexport type MetaEvent = LangGraphInterruptEvent | CopilotKitLangGraphInterruptEvent;\n"], "mappings": ";;;;;AAAA,SAAS,gBAAgB;AAezB,SAAS,iBAAiB;AASnB,IAAM,UAAN,MAAc;AAAA,EAMnB,YAAY,OAAY;AACtB,UAAM,OAAN,MAAM,KAAO,SAAS;AACtB,UAAM,WAAN,MAAM,SAAW,EAAE,8BAAgC;AACnD,UAAM,cAAN,MAAM,YAAc,oBAAI,KAAK;AAC7B,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AAAA,EAEA,gBAAqC;AACnC,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,2BAA2D;AACzD,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,kBAAyC;AACvC,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,sBAAiD;AAC/C,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,iBAAuC;AACrC,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AAGO,IAAM,OAAO;AAOb,IAAM,cAAN,cAA0B,QAAiD;AAAA,EAKhF,YAAY,OAAsC;AAChD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAOO,IAAM,yBAAN,cACG,QAEV;AAAA,EAIE,YAAY,OAAiD;AAC3D,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAIO,IAAM,gBAAN,cAA4B,QAAmD;AAAA,EAKpF,YAAY,OAAwC;AAClD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,aAAa,QAAqB;AACvC,WAAO,UAAU,QAAQ,MAAM;AAAA,EACjC;AAAA,EAEA,OAAO,aAAa,QAAqB;AACvC,QAAI,WAAW,QAAW;AACxB,aAAO;AAAA,IACT,WAAW,OAAO,WAAW,UAAU;AACrC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,UAAU,MAAM;AAAA,IAC9B;AAAA,EACF;AACF;AAEO,IAAM,oBAAN,cAAgC,QAAyD;AAAA,EAU9F,YAAY,OAAY;AACtB,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAIO,IAAM,eAAN,cAA2B,QAAkD;AAAA,EAMlF,YAAY,OAAuC;AACjD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAEO,SAAS,wBACd,YACyB;AACzB,SAAO,EAAE,GAAG,YAAY,+DAA6C,MAAM,YAAY;AACzF;", "names": []}