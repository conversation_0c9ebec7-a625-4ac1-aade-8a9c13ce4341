"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/lib/copilot-task.ts
var copilot_task_exports = {};
__export(copilot_task_exports, {
  CopilotTask: () => CopilotTask
});
module.exports = __toCommonJS(copilot_task_exports);
var import_runtime_client_gql2 = require("@copilotkit/runtime-client-gql");

// src/types/frontend-action.ts
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_shared = require("@copilotkit/shared");
function processActionsForRuntimeRequest(actions) {
  const filteredActions = actions.filter(
    (action) => action.available !== import_runtime_client_gql.ActionInputAvailability.Disabled && action.disabled !== true && action.name !== "*" && action.available != "frontend" && !action.pairedAction
  ).map((action) => {
    let available = import_runtime_client_gql.ActionInputAvailability.Enabled;
    if (action.disabled) {
      available = import_runtime_client_gql.ActionInputAvailability.Disabled;
    } else if (action.available === "disabled") {
      available = import_runtime_client_gql.ActionInputAvailability.Disabled;
    } else if (action.available === "remote") {
      available = import_runtime_client_gql.ActionInputAvailability.Remote;
    }
    return {
      name: action.name,
      description: action.description || "",
      jsonSchema: JSON.stringify((0, import_shared.actionParametersToJsonSchema)(action.parameters || [])),
      available
    };
  });
  return filteredActions;
}

// src/components/copilot-provider/copilotkit.tsx
var import_react = require("react");
var import_react_dom = require("react-dom");
var import_shared2 = require("@copilotkit/shared");
var import_jsx_runtime = require("react/jsx-runtime");
var defaultCopilotContextCategories = ["global"];

// src/lib/copilot-task.ts
var CopilotTask = class {
  constructor(config) {
    this.instructions = config.instructions;
    this.actions = config.actions || [];
    this.includeCopilotReadable = config.includeCopilotReadable !== false;
    this.includeCopilotActions = config.includeCopilotActions !== false;
    this.forwardedParameters = config.forwardedParameters;
  }
  /**
   * Run the task.
   * @param context The CopilotContext to use for the task. Use `useCopilotContext` to obtain the current context.
   * @param data The data to use for the task.
   */
  run(context, data) {
    return __async(this, null, function* () {
      var _a, _b, _c;
      const actions = this.includeCopilotActions ? Object.assign({}, context.actions) : {};
      for (const fn of this.actions) {
        actions[fn.name] = fn;
      }
      let contextString = "";
      if (data) {
        contextString = (typeof data === "string" ? data : JSON.stringify(data)) + "\n\n";
      }
      if (this.includeCopilotReadable) {
        contextString += context.getContextString([], defaultCopilotContextCategories);
      }
      const systemMessage = new import_runtime_client_gql2.TextMessage({
        content: taskSystemMessage(contextString, this.instructions),
        role: import_runtime_client_gql2.Role.System
      });
      const messages = [systemMessage];
      const runtimeClient = new import_runtime_client_gql2.CopilotRuntimeClient({
        url: context.copilotApiConfig.chatApiEndpoint,
        publicApiKey: context.copilotApiConfig.publicApiKey,
        headers: context.copilotApiConfig.headers,
        credentials: context.copilotApiConfig.credentials
      });
      const response = yield runtimeClient.generateCopilotResponse({
        data: {
          frontend: {
            actions: processActionsForRuntimeRequest(Object.values(actions)),
            url: window.location.href
          },
          messages: (0, import_runtime_client_gql2.convertMessagesToGqlInput)((0, import_runtime_client_gql2.filterAgentStateMessages)(messages)),
          metadata: {
            requestType: import_runtime_client_gql2.CopilotRequestType.Task
          },
          forwardedParameters: __spreadProps(__spreadValues({}, (_a = this.forwardedParameters) != null ? _a : {}), {
            toolChoice: "required"
          })
        },
        properties: context.copilotApiConfig.properties
      }).toPromise();
      const functionCallHandler = context.getFunctionCallHandler(actions);
      const functionCalls = (0, import_runtime_client_gql2.convertGqlOutputToMessages)(
        ((_c = (_b = response.data) == null ? void 0 : _b.generateCopilotResponse) == null ? void 0 : _c.messages) || []
      ).filter((m) => m.isActionExecutionMessage());
      for (const functionCall of functionCalls) {
        yield functionCallHandler({
          messages,
          name: functionCall.name,
          args: functionCall.arguments
        });
      }
    });
  }
};
function taskSystemMessage(contextString, instructions) {
  return `
Please act as an efficient, competent, conscientious, and industrious professional assistant.

Help the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.
Always be polite and respectful, and prefer brevity over verbosity.

The user has provided you with the following context:
\`\`\`
${contextString}
\`\`\`

They have also provided you with functions you can call to initiate actions on their behalf.

Please assist them as best you can.

This is not a conversation, so please do not ask questions. Just call a function without saying anything else.

The user has given you the following task to complete:

\`\`\`
${instructions}
\`\`\`
`;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CopilotTask
});
//# sourceMappingURL=copilot-task.js.map