import {
  MessageRole
} from "./chunk-WM3ARNBD.mjs";

// src/client/types.ts
import { randomId } from "@copilotkit/shared";
import { parseJson } from "@copilotkit/shared";
var Message = class {
  constructor(props) {
    props.id ?? (props.id = randomId());
    props.status ?? (props.status = { code: "Success" /* Success */ });
    props.createdAt ?? (props.createdAt = /* @__PURE__ */ new Date());
    Object.assign(this, props);
  }
  isTextMessage() {
    return this.type === "TextMessage";
  }
  isActionExecutionMessage() {
    return this.type === "ActionExecutionMessage";
  }
  isResultMessage() {
    return this.type === "ResultMessage";
  }
  isAgentStateMessage() {
    return this.type === "AgentStateMessage";
  }
  isImageMessage() {
    return this.type === "ImageMessage";
  }
};
var Role = MessageRole;
var TextMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "TextMessage";
  }
};
var ActionExecutionMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "ActionExecutionMessage";
  }
};
var ResultMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "ResultMessage";
  }
  static decodeResult(result) {
    return parseJson(result, result);
  }
  static encodeResult(result) {
    if (result === void 0) {
      return "";
    } else if (typeof result === "string") {
      return result;
    } else {
      return JSON.stringify(result);
    }
  }
};
var AgentStateMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "AgentStateMessage";
  }
};
var ImageMessage = class extends Message {
  constructor(props) {
    super(props);
    this.type = "ImageMessage";
  }
};
function langGraphInterruptEvent(eventProps) {
  return { ...eventProps, name: "LangGraphInterruptEvent" /* LangGraphInterruptEvent */, type: "MetaEvent" };
}

export {
  Message,
  Role,
  TextMessage,
  ActionExecutionMessage,
  ResultMessage,
  AgentStateMessage,
  ImageMessage,
  langGraphInterruptEvent
};
//# sourceMappingURL=chunk-ROUIRR4B.mjs.map