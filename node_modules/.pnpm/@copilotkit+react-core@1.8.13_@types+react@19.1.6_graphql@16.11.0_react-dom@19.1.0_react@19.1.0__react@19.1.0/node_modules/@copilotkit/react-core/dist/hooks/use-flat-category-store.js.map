{"version": 3, "sources": ["../../src/hooks/use-flat-category-store.ts"], "sourcesContent": ["import { useCallback, useReducer } from \"react\";\nimport { randomId } from \"@copilotkit/shared\";\n\nexport type FlatCategoryStoreId = string;\n\nexport interface UseFlatCategoryStoreReturn<T> {\n  addElement: (value: T, categories: string[]) => FlatCategoryStoreId;\n  removeElement: (id: FlatCategoryStoreId) => void;\n  allElements: (categories: string[]) => T[];\n}\n\ninterface FlatCategoryStoreElement<T> {\n  id: FlatCategoryStoreId;\n  value: T;\n  categories: Set<string>;\n}\n\nconst useFlatCategoryStore = <T>(): UseFlatCategoryStoreReturn<T> => {\n  const [elements, dispatch] = useReducer<\n    React.Reducer<Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>>, Action<T>>\n  >(flatCategoryStoreReducer, new Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>>());\n\n  const addElement = useCallback((value: T, categories: string[]): FlatCategoryStoreId => {\n    const newId = randomId();\n    dispatch({\n      type: \"ADD_ELEMENT\",\n      value,\n      id: newId,\n      categories,\n    });\n    return newId;\n  }, []);\n\n  const removeElement = useCallback((id: FlatCategoryStoreId): void => {\n    dispatch({ type: \"REMOVE_ELEMENT\", id });\n  }, []);\n\n  const allElements = useCallback(\n    (categories: string[]): T[] => {\n      const categoriesSet = new Set(categories);\n      const result: T[] = [];\n      elements.forEach((element) => {\n        if (setsHaveIntersection(categoriesSet, element.categories)) {\n          result.push(element.value);\n        }\n      });\n      return result;\n    },\n    [elements],\n  );\n\n  return { addElement, removeElement, allElements };\n};\n\nexport default useFlatCategoryStore;\n\n// Action types\ntype Action<T> =\n  | {\n      type: \"ADD_ELEMENT\";\n      value: T;\n      id: FlatCategoryStoreId;\n      categories: string[];\n    }\n  | { type: \"REMOVE_ELEMENT\"; id: FlatCategoryStoreId };\n\n// Reducer\nfunction flatCategoryStoreReducer<T>(\n  state: Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>>,\n  action: Action<T>,\n): Map<FlatCategoryStoreId, FlatCategoryStoreElement<T>> {\n  switch (action.type) {\n    case \"ADD_ELEMENT\": {\n      const { value, id, categories } = action;\n      const newElement: FlatCategoryStoreElement<T> = {\n        id,\n        value,\n        categories: new Set(categories),\n      };\n      const newState = new Map(state);\n      newState.set(id, newElement);\n      return newState;\n    }\n    case \"REMOVE_ELEMENT\": {\n      const newState = new Map(state);\n      newState.delete(action.id);\n      return newState;\n    }\n    default:\n      return state;\n  }\n}\n\nfunction setsHaveIntersection<T>(setA: Set<T>, setB: Set<T>): boolean {\n  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];\n\n  for (let item of smallerSet) {\n    if (largerSet.has(item)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAwC;AACxC,oBAAyB;AAgBzB,IAAM,uBAAuB,MAAwC;AACnE,QAAM,CAAC,UAAU,QAAQ,QAAI,yBAE3B,0BAA0B,oBAAI,IAAsD,CAAC;AAEvF,QAAM,iBAAa,0BAAY,CAAC,OAAU,eAA8C;AACtF,UAAM,YAAQ,wBAAS;AACvB,aAAS;AAAA,MACP,MAAM;AAAA,MACN;AAAA,MACA,IAAI;AAAA,MACJ;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,QAAM,oBAAgB,0BAAY,CAAC,OAAkC;AACnE,aAAS,EAAE,MAAM,kBAAkB,GAAG,CAAC;AAAA,EACzC,GAAG,CAAC,CAAC;AAEL,QAAM,kBAAc;AAAA,IAClB,CAAC,eAA8B;AAC7B,YAAM,gBAAgB,IAAI,IAAI,UAAU;AACxC,YAAM,SAAc,CAAC;AACrB,eAAS,QAAQ,CAAC,YAAY;AAC5B,YAAI,qBAAqB,eAAe,QAAQ,UAAU,GAAG;AAC3D,iBAAO,KAAK,QAAQ,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AAEA,SAAO,EAAE,YAAY,eAAe,YAAY;AAClD;AAEA,IAAO,kCAAQ;AAaf,SAAS,yBACP,OACA,QACuD;AACvD,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK,eAAe;AAClB,YAAM,EAAE,OAAO,IAAI,WAAW,IAAI;AAClC,YAAM,aAA0C;AAAA,QAC9C;AAAA,QACA;AAAA,QACA,YAAY,IAAI,IAAI,UAAU;AAAA,MAChC;AACA,YAAM,WAAW,IAAI,IAAI,KAAK;AAC9B,eAAS,IAAI,IAAI,UAAU;AAC3B,aAAO;AAAA,IACT;AAAA,IACA,KAAK,kBAAkB;AACrB,YAAM,WAAW,IAAI,IAAI,KAAK;AAC9B,eAAS,OAAO,OAAO,EAAE;AACzB,aAAO;AAAA,IACT;AAAA,IACA;AACE,aAAO;AAAA,EACX;AACF;AAEA,SAAS,qBAAwB,MAAc,MAAuB;AACpE,QAAM,CAAC,YAAY,SAAS,IAAI,KAAK,QAAQ,KAAK,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI;AAEnF,WAAS,QAAQ,YAAY;AAC3B,QAAI,UAAU,IAAI,IAAI,GAAG;AACvB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;", "names": []}