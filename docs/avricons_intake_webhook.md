# Avricons Intake Webhook

This document describes the webhook endpoint that receives finished-call JSON from the Voice service.

## Overview

The avricons intake webhook endpoint allows the Voice service to push completed call data to Core AiLex for processing. The endpoint provides secure HMAC-based authentication and stores events for background processing.

## Endpoint

```
POST /api/v1/avricons/intake
```

## Authentication

The endpoint uses HMAC-SHA256 signature verification for security:

- **Header**: `X-Avr-Signature`
- **Format**: `sha256=<signature>`
- **Secret**: Configured via `CORE_INTAKE_SECRET` environment variable

## Request Schema

```json
{
  "tenant_id": "uuid",
  "call_id": "string",
  "call_status": "string",
  "call_duration": "integer",
  "transcript": "string",
  "summary": "string",
  "client_info": {
    "name": "string",
    "phone": "string",
    "email": "string"
  },
  "case_info": {
    "type": "string",
    "incident_date": "string",
    "description": "string"
  },
  "metadata": {
    "call_timestamp": "string",
    "agent_version": "string"
  }
}
```

### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `tenant_id` | UUID | Yes | The tenant ID associated with this intake event |
| `call_id` | String | Yes | Unique identifier for the voice call |
| `call_status` | String | Yes | Status of the call (completed, failed, etc.) |
| `call_duration` | Integer | Yes | Duration of the call in seconds |
| `transcript` | String | Yes | Full transcript of the call |
| `summary` | String | Yes | AI-generated summary of the call |
| `client_info` | Object | No | Extracted client information from the call |
| `case_info` | Object | No | Extracted case information from the call |
| `metadata` | Object | No | Additional metadata about the call |

## Response Schema

### Success Response (202 Accepted)

```json
{
  "status": "queued",
  "event_id": "uuid",
  "message": "Event queued for processing"
}
```

### Error Responses

#### 401 Unauthorized - Invalid Signature
```json
{
  "detail": "Invalid signature"
}
```

#### 422 Unprocessable Entity - Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "field_name"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

#### 500 Internal Server Error
```json
{
  "detail": "Internal server error while processing webhook"
}
```

## Example Usage

### cURL Example

```bash
# Generate HMAC signature (example using openssl)
PAYLOAD='{"tenant_id":"123e4567-e89b-12d3-a456-************","call_id":"call_123456","call_status":"completed","call_duration":300,"transcript":"Hello, I was in a car accident...","summary":"Client reports car accident","client_info":{"name":"John Doe","phone":"+1234567890"},"case_info":{"type":"personal_injury"},"metadata":{}}'
SECRET="your_secret_key"
SIGNATURE=$(echo -n "$PAYLOAD" | openssl dgst -sha256 -hmac "$SECRET" | cut -d' ' -f2)

# Make the request
curl -X POST "https://api.ailex.com/api/v1/avricons/intake" \
  -H "Content-Type: application/json" \
  -H "X-Avr-Signature: sha256=$SIGNATURE" \
  -d "$PAYLOAD"
```

### Python Example

```python
import hashlib
import hmac
import json
import requests

# Payload
payload = {
    "tenant_id": "123e4567-e89b-12d3-a456-************",
    "call_id": "call_123456",
    "call_status": "completed",
    "call_duration": 300,
    "transcript": "Hello, I was in a car accident last week...",
    "summary": "Client reports car accident, seeking legal representation",
    "client_info": {
        "name": "John Doe",
        "phone": "+1234567890",
        "email": "<EMAIL>"
    },
    "case_info": {
        "type": "personal_injury",
        "incident_date": "2025-01-01",
        "description": "Rear-end collision at intersection"
    },
    "metadata": {
        "call_timestamp": "2025-01-03T10:00:00Z",
        "agent_version": "v1.2.3"
    }
}

# Generate HMAC signature
secret = "your_secret_key"
payload_json = json.dumps(payload)
signature = hmac.new(
    key=secret.encode('utf-8'),
    msg=payload_json.encode('utf-8'),
    digestmod=hashlib.sha256
).hexdigest()

# Make request
response = requests.post(
    "https://api.ailex.com/api/v1/avricons/intake",
    json=payload,
    headers={
        "X-Avr-Signature": f"sha256={signature}"
    }
)

print(f"Status: {response.status_code}")
print(f"Response: {response.json()}")
```

## Configuration

### Environment Variables

| Variable | Required | Description | Default |
|----------|----------|-------------|---------|
| `CORE_INTAKE_SECRET` | Yes | Secret key for HMAC signature verification | None |
| `USE_REDIS_QUEUE` | No | Enable Redis queue for event processing | `false` |

### Database Storage

Events are stored in the `tenants.intake_events` table with the following structure:

```sql
CREATE TABLE tenants.intake_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    payload JSONB NOT NULL,
    received_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    processed BOOLEAN NOT NULL DEFAULT false
);
```

## Security Considerations

1. **HMAC Verification**: All requests must include a valid HMAC signature
2. **Tenant Isolation**: Events are stored with tenant_id for proper isolation
3. **Logging**: All webhook attempts are logged for audit purposes
4. **Rate Limiting**: Consider implementing rate limiting for production use

## Monitoring

The endpoint logs the following events:

- Successful webhook receipt
- HMAC verification failures
- Database storage errors
- Processing status updates

Monitor these logs for operational insights and security alerts.

## Future Enhancements

1. **Redis Queue Integration**: Complete Redis queue implementation for high-throughput scenarios
2. **Feature Flag Validation**: Check tenant subscription for voice_intake feature
3. **Retry Logic**: Implement automatic retry for failed processing
4. **Rate Limiting**: Add rate limiting per tenant
5. **Webhook Delivery Confirmation**: Add delivery confirmation back to Voice service
