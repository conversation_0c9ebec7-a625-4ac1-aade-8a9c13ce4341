{"name": "@graphql-tools/utils", "version": "10.8.6", "description": "Common package containing utils and types for GraphQL tools", "sideEffects": false, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}, "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "@whatwg-node/promise-helpers": "^1.0.0", "cross-inspect": "1.0.1", "dset": "^3.1.4", "tslib": "^2.4.0"}, "repository": {"type": "git", "url": "ardatan/graphql-tools", "directory": "packages/utils"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=16.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}