{"name": "@graphql-yoga/plugin-defer-stream", "version": "3.13.5", "description": "Defer/Stream plugin for GraphQL Yoga.", "peerDependencies": {"graphql": "^15.2.0 || ^16.0.0", "graphql-yoga": "^5.13.5"}, "dependencies": {"@graphql-tools/utils": "^10.6.1"}, "repository": {"type": "git", "url": "https://github.com/graphql-hive/graphql-yoga.git", "directory": "packages/plugins/defer"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON> <saihaj<PERSON><PERSON>.<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./package.json": "./package.json"}}