import { CopilotCloudConfig, FunctionCallHandler } from '@copilotkit/shared';
import { ActionRenderProps, CatchAllActionRenderProps, FrontendAction } from './types/frontend-action.js';
import React$1 from 'react';
import { TreeNodeId } from './hooks/use-tree.js';
import { DocumentPointer } from './types/document-pointer.js';
import { CopilotChatSuggestionConfiguration } from './types/chat-suggestion-configuration.js';
import { CoAgentStateRenderProps, CoAgentStateRender } from './types/coagent-action.js';
import { CoagentState } from './types/coagent-state.js';
import { LangGraphInterruptEvent, CopilotRuntimeClient, ForwardedParametersInput, Agent, ExtensionsInput } from '@copilotkit/runtime-client-gql';

interface LangGraphInterruptRenderHandlerProps<TEventValue = any> {
    event: LangGraphInterruptEvent<TEventValue>;
    resolve: (resolution: string) => void;
}
interface LangGraphInterruptRenderProps<TEventValue = any> {
    result: unknown;
    event: LangGraphInterruptEvent<TEventValue>;
    resolve: (resolution: string) => void;
}
interface LangGraphInterruptRender<TEventValue = any> {
    id: string;
    /**
     * The handler function to handle the event.
     */
    handler?: (props: LangGraphInterruptRenderHandlerProps<TEventValue>) => any | Promise<any>;
    /**
     * The render function to handle the event.
     */
    render?: (props: LangGraphInterruptRenderProps<TEventValue>) => string | React.ReactElement;
    /**
     * Method that returns a boolean, indicating if the interrupt action should run
     * Useful when using multiple interrupts
     */
    enabled?: (args: {
        eventValue: TEventValue;
        agentMetadata: AgentSession;
    }) => boolean;
}
type LangGraphInterruptAction = LangGraphInterruptRender & {
    event?: LangGraphInterruptEvent;
};
type LangGraphInterruptActionSetterArgs = (Partial<LangGraphInterruptRender> & {
    event?: Partial<LangGraphInterruptEvent>;
}) | null;
type LangGraphInterruptActionSetter = (action: LangGraphInterruptActionSetterArgs) => void;

/**
 * Interface for the configuration of the Copilot API.
 */
interface CopilotApiConfig {
    /**
     * The public API key for Copilot Cloud.
     */
    publicApiKey?: string;
    /**
     * The configuration for Copilot Cloud.
     */
    cloud?: CopilotCloudConfig;
    /**
     * The endpoint for the chat API.
     */
    chatApiEndpoint: string;
    /**
     * The endpoint for the Copilot transcribe audio service.
     */
    transcribeAudioUrl?: string;
    /**
     * The endpoint for the Copilot text to speech service.
     */
    textToSpeechUrl?: string;
    /**
     * additional headers to be sent with the request
     * @default {}
     * @example
     * ```
     * {
     *   'Authorization': 'Bearer your_token_here'
     * }
     * ```
     */
    headers: Record<string, string>;
    /**
     * Custom properties to be sent with the request
     * @default {}
     * @example
     * ```
     * {
     *   'user_id': 'user_id'
     * }
     * ```
     */
    properties?: Record<string, any>;
    /**
     * Indicates whether the user agent should send or receive cookies from the other domain
     * in the case of cross-origin requests.
     */
    credentials?: RequestCredentials;
    /**
     * Optional configuration for connecting to Model Context Protocol (MCP) servers.
     * This is typically derived from the CopilotKitProps and used internally.
     * @experimental
     */
    mcpServers?: Array<{
        endpoint: string;
        apiKey?: string;
    }>;
}
type InChatRenderFunction<TProps = ActionRenderProps<any> | CatchAllActionRenderProps<any>> = (props: TProps) => string | JSX.Element;
type CoagentInChatRenderFunction = (props: CoAgentStateRenderProps<any>) => string | JSX.Element | undefined | null;
interface ChatComponentsCache {
    actions: Record<string, InChatRenderFunction | string>;
    coAgentStateRenders: Record<string, CoagentInChatRenderFunction | string>;
}
interface AgentSession {
    agentName: string;
    threadId?: string;
    nodeName?: string;
}
interface AuthState {
    status: "authenticated" | "unauthenticated";
    authHeaders: Record<string, string>;
    userId?: string;
    metadata?: Record<string, any>;
}
type ActionName = string;
interface CopilotContextParams {
    actions: Record<string, FrontendAction<any>>;
    setAction: (id: string, action: FrontendAction<any>) => void;
    removeAction: (id: string) => void;
    coAgentStateRenders: Record<string, CoAgentStateRender<any>>;
    setCoAgentStateRender: (id: string, stateRender: CoAgentStateRender<any>) => void;
    removeCoAgentStateRender: (id: string) => void;
    chatComponentsCache: React$1.RefObject<ChatComponentsCache>;
    getFunctionCallHandler: (customEntryPoints?: Record<string, FrontendAction<any>>) => FunctionCallHandler;
    addContext: (context: string, parentId?: string, categories?: string[]) => TreeNodeId;
    removeContext: (id: TreeNodeId) => void;
    getContextString: (documents: DocumentPointer[], categories: string[]) => string;
    addDocumentContext: (documentPointer: DocumentPointer, categories?: string[]) => TreeNodeId;
    removeDocumentContext: (documentId: string) => void;
    getDocumentsContext: (categories: string[]) => DocumentPointer[];
    isLoading: boolean;
    setIsLoading: React$1.Dispatch<React$1.SetStateAction<boolean>>;
    chatSuggestionConfiguration: {
        [key: string]: CopilotChatSuggestionConfiguration;
    };
    addChatSuggestionConfiguration: (id: string, suggestion: CopilotChatSuggestionConfiguration) => void;
    removeChatSuggestionConfiguration: (id: string) => void;
    chatInstructions: string;
    setChatInstructions: React$1.Dispatch<React$1.SetStateAction<string>>;
    additionalInstructions?: string[];
    setAdditionalInstructions: React$1.Dispatch<React$1.SetStateAction<string[]>>;
    copilotApiConfig: CopilotApiConfig;
    showDevConsole: boolean | "auto";
    coagentStates: Record<string, CoagentState>;
    setCoagentStates: React$1.Dispatch<React$1.SetStateAction<Record<string, CoagentState>>>;
    coagentStatesRef: React$1.RefObject<Record<string, CoagentState>>;
    setCoagentStatesWithRef: (value: Record<string, CoagentState> | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>)) => void;
    agentSession: AgentSession | null;
    setAgentSession: React$1.Dispatch<React$1.SetStateAction<AgentSession | null>>;
    agentLock: string | null;
    threadId: string;
    setThreadId: React$1.Dispatch<React$1.SetStateAction<string>>;
    runId: string | null;
    setRunId: React$1.Dispatch<React$1.SetStateAction<string | null>>;
    chatAbortControllerRef: React$1.MutableRefObject<AbortController | null>;
    runtimeClient: CopilotRuntimeClient;
    /**
     * The forwarded parameters to use for the task.
     */
    forwardedParameters?: Pick<ForwardedParametersInput, "temperature">;
    availableAgents: Agent[];
    /**
     * The auth states for the CopilotKit.
     */
    authStates_c?: Record<ActionName, AuthState>;
    setAuthStates_c?: React$1.Dispatch<React$1.SetStateAction<Record<ActionName, AuthState>>>;
    /**
     * The auth config for the CopilotKit.
     */
    authConfig_c?: {
        SignInComponent: React$1.ComponentType<{
            onSignInComplete: (authState: AuthState) => void;
        }>;
    };
    extensions: ExtensionsInput;
    setExtensions: React$1.Dispatch<React$1.SetStateAction<ExtensionsInput>>;
    langGraphInterruptAction: LangGraphInterruptAction | null;
    setLangGraphInterruptAction: LangGraphInterruptActionSetter;
    removeLangGraphInterruptAction: () => void;
}
declare const CopilotContext: React$1.Context<CopilotContextParams>;
declare function useCopilotContext(): CopilotContextParams;

export { AgentSession as A, CopilotContext as C, InChatRenderFunction as I, LangGraphInterruptAction as L, CopilotContextParams as a, CoagentInChatRenderFunction as b, CopilotApiConfig as c, LangGraphInterruptActionSetter as d, LangGraphInterruptRender as e, AuthState as f, LangGraphInterruptRenderHandlerProps as g, LangGraphInterruptRenderProps as h, LangGraphInterruptActionSetterArgs as i, ChatComponentsCache as j, ActionName as k, useCopilotContext as u };
