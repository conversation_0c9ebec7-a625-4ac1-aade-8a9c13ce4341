{"version": 3, "sources": ["../src/components/help-modal/icons.tsx"], "sourcesContent": ["import React from \"react\";\n\nexport const LifeBuoyIcon = () => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    className=\"icon icon-tabler icons-tabler-outline icon-tabler-lifebuoy\"\n  >\n    <g transform=\"translate(0, -1)\">\n      <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n      <path d=\"M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0\" />\n      <path d=\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\" />\n      <path d=\"M15 15l3.35 3.35\" />\n      <path d=\"M9 15l-3.35 3.35\" />\n      <path d=\"M5.65 5.65l3.35 3.35\" />\n      <path d=\"M18.35 5.65l-3.35 3.35\" />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = () => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"20\"\n    height=\"20\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const LoadingSpinnerIcon = ({ color = \"rgb(107 114 128)\" }: { color?: string }) => (\n  <svg\n    style={{\n      animation: \"copilotKitSpinAnimation 1s linear infinite\",\n      color,\n    }}\n    width=\"24\"\n    height=\"24\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n  >\n    <circle\n      style={{ opacity: 0.25 }}\n      cx=\"12\"\n      cy=\"12\"\n      r=\"10\"\n      stroke=\"currentColor\"\n      strokeWidth=\"4\"\n    ></circle>\n    <path\n      style={{ opacity: 0.75 }}\n      fill=\"currentColor\"\n      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n    ></path>\n  </svg>\n);\n"], "mappings": ";AAeI,SACE,KADF;AAbG,IAAM,eAAe,MAC1B;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,QAAO;AAAA,IACP,aAAY;AAAA,IACZ,eAAc;AAAA,IACd,gBAAe;AAAA,IACf,WAAU;AAAA,IAEV,+BAAC,OAAE,WAAU,oBACX;AAAA,0BAAC,UAAK,QAAO,QAAO,GAAE,iBAAgB,MAAK,QAAO;AAAA,MAClD,oBAAC,UAAK,GAAE,4CAA2C;AAAA,MACnD,oBAAC,UAAK,GAAE,8CAA6C;AAAA,MACrD,oBAAC,UAAK,GAAE,oBAAmB;AAAA,MAC3B,oBAAC,UAAK,GAAE,oBAAmB;AAAA,MAC3B,oBAAC,UAAK,GAAE,wBAAuB;AAAA,MAC/B,oBAAC,UAAK,GAAE,0BAAyB;AAAA,OACnC;AAAA;AACF;AAGK,IAAM,YAAY,MACvB;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,8BAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,wBAAuB;AAAA;AAC9E;AAGK,IAAM,qBAAqB,CAAC,EAAE,QAAQ,mBAAmB,MAC9D;AAAA,EAAC;AAAA;AAAA,IACC,OAAO;AAAA,MACL,WAAW;AAAA,MACX;AAAA,IACF;AAAA,IACA,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IAER;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,OAAO,EAAE,SAAS,KAAK;AAAA,UACvB,IAAG;AAAA,UACH,IAAG;AAAA,UACH,GAAE;AAAA,UACF,QAAO;AAAA,UACP,aAAY;AAAA;AAAA,MACb;AAAA,MACD;AAAA,QAAC;AAAA;AAAA,UACC,OAAO,EAAE,SAAS,KAAK;AAAA,UACvB,MAAK;AAAA,UACL,GAAE;AAAA;AAAA,MACH;AAAA;AAAA;AACH;", "names": []}