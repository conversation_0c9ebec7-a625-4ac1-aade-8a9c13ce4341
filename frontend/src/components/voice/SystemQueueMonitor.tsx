'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Activity, 
  Trash2, 
  RefreshCw,
  AlertTriangle,
  Play,
  Square,
  Users,
  TrendingUp
} from 'lucide-react';
import { useSystemQueueStats } from '@/hooks/useSystemQueueStats';
import { formatDistanceToNow } from 'date-fns';
import { MultiTenantTable, TableColumn } from '@/components/ui/MultiTenantTable';
import { QueueStats } from '@/hooks/useQueueStats';

export default function SystemQueueMonitor() {
  const { 
    data: systemStats, 
    isLoading, 
    error, 
    mutate, 
    clearTenantQueue,
    startWorker,
    stopWorker 
  } = useSystemQueueStats();

  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const handleClearTenantQueue = async (tenantId: string) => {
    setActionLoading(`clear-${tenantId}`);
    try {
      await clearTenantQueue(tenantId);
    } catch (err) {
      console.error('Failed to clear tenant queue:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const handleStartWorker = async () => {
    setActionLoading('start-worker');
    try {
      await startWorker();
    } catch (err) {
      console.error('Failed to start worker:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const handleStopWorker = async () => {
    setActionLoading('stop-worker');
    try {
      await stopWorker();
    } catch (err) {
      console.error('Failed to stop worker:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const handleRefresh = async () => {
    setActionLoading('refresh');
    try {
      await mutate();
    } catch (err) {
      console.error('Failed to refresh system stats:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const getWorkerStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-500 text-white">Healthy</Badge>;
      case 'degraded':
        return <Badge className="bg-yellow-500 text-white">Degraded</Badge>;
      case 'down':
        return <Badge className="bg-red-500 text-white">Down</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatProcessingTime = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    }
    return `${(seconds / 60).toFixed(1)}m`;
  };

  // Table columns for tenant queue data
  const columns: TableColumn<QueueStats>[] = [
    {
      key: 'tenant_id',
      header: 'Tenant ID',
      className: 'font-mono text-xs',
    },
    {
      key: 'queued',
      header: 'Queued',
      render: (value) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-blue-500" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'processing',
      header: 'Processing',
      render: (value) => (
        <div className="flex items-center gap-1">
          <Activity className="h-3 w-3 text-orange-500" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'completed',
      header: 'Completed',
      render: (value) => (
        <div className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3 text-green-500" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'failed',
      header: 'Failed',
      render: (value) => (
        <div className="flex items-center gap-1">
          <XCircle className="h-3 w-3 text-red-500" />
          <span className="font-medium">{value}</span>
        </div>
      ),
    },
    {
      key: 'total_today',
      header: 'Today Total',
      render: (value) => <span className="font-medium">{value}</span>,
    },
    {
      key: 'avg_processing_time_seconds',
      header: 'Avg Time',
      render: (value) => (
        <span className="text-sm">{value > 0 ? formatProcessingTime(value) : '-'}</span>
      ),
    },
    {
      key: 'worker_status',
      header: 'Status',
      render: (value) => getWorkerStatusBadge(value),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <Button
          onClick={() => handleClearTenantQueue(row.tenant_id)}
          disabled={row.queued === 0 || actionLoading === `clear-${row.tenant_id}`}
          size="sm"
          variant="outline"
        >
          {actionLoading === `clear-${row.tenant_id}` ? (
            <RefreshCw className="h-3 w-3 animate-spin" />
          ) : (
            <Trash2 className="h-3 w-3" />
          )}
        </Button>
      ),
    },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading system queue stats...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground mb-4">Failed to load system queue stats</p>
              <Button onClick={handleRefresh} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!systemStats) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <p className="text-sm text-muted-foreground">No system queue data available</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { totals, worker_health, tenants } = systemStats;
  const totalProcessed = totals.completed + totals.failed;
  const successRate = totalProcessed > 0 ? (totals.completed / totalProcessed) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Queue Monitor</h1>
          <p className="text-muted-foreground">
            Monitor voice queue performance across all tenants
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} disabled={actionLoading === 'refresh'} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${actionLoading === 'refresh' ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Queued</CardTitle>
            <Clock className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{totals.queued}</div>
            <p className="text-xs text-muted-foreground">
              Across {totals.active_tenants} active tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{totals.processing}</div>
            <p className="text-xs text-muted-foreground">
              Currently being processed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{successRate.toFixed(1)}%</div>
            <Progress value={successRate} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Worker Health</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-1">
              {getWorkerStatusBadge(worker_health.status)}
            </div>
            <p className="text-xs text-muted-foreground">
              {worker_health.workers_active}/{worker_health.workers_total} workers active
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Worker Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Worker Management</CardTitle>
          <CardDescription>
            Control queue workers and monitor system health
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex gap-2">
              <Button
                onClick={handleStartWorker}
                disabled={actionLoading === 'start-worker' || worker_health.status === 'healthy'}
                size="sm"
              >
                {actionLoading === 'start-worker' ? (
                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Play className="h-3 w-3 mr-1" />
                )}
                Start Worker
              </Button>
              <Button
                onClick={handleStopWorker}
                disabled={actionLoading === 'stop-worker' || worker_health.status === 'down'}
                size="sm"
                variant="outline"
              >
                {actionLoading === 'stop-worker' ? (
                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                ) : (
                  <Square className="h-3 w-3 mr-1" />
                )}
                Stop Worker
              </Button>
            </div>
            <div className="text-sm text-muted-foreground">
              Last heartbeat: {worker_health.last_heartbeat ? 
                formatDistanceToNow(new Date(worker_health.last_heartbeat), { addSuffix: true }) : 
                'Never'
              }
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tenant Queue Table */}
      <Card>
        <CardHeader>
          <CardTitle>Tenant Queue Status</CardTitle>
          <CardDescription>
            Detailed queue statistics for each tenant
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MultiTenantTable
            rows={tenants}
            columns={columns}
            showTenantColumn={false}
            isLoading={false}
            emptyMessage="No tenant queue data available"
          />
        </CardContent>
      </Card>
    </div>
  );
}
