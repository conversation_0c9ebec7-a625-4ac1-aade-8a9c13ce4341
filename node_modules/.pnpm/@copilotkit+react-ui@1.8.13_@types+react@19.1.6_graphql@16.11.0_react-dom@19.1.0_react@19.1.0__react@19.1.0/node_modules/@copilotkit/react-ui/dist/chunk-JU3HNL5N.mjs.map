{"version": 3, "sources": ["../src/components/chat/Sidebar.tsx"], "sourcesContent": ["/**\n * <br/>\n * <img src=\"/images/CopilotSidebar.gif\" width=\"500\" />\n *\n * A chatbot sidebar component for the CopilotKit framework. Highly customizable through various props and custom CSS.\n *\n * See [CopilotPopup](/reference/components/chat/CopilotPopup) for a popup version of this component.\n *\n * ## Install Dependencies\n *\n * This component is part of the [@copilotkit/react-ui](https://npmjs.com/package/@copilotkit/react-ui) package.\n *\n * ```shell npm2yarn \\\"@copilotkit/react-ui\"\\\n * npm install @copilotkit/react-core @copilotkit/react-ui\n * ```\n *\n * ## Usage\n *\n * ```tsx\n * import { CopilotSidebar } from \"@copilotkit/react-ui\";\n * import \"@copilotkit/react-ui/styles.css\";\n *\n * <CopilotSidebar\n *   labels={{\n *     title: \"Your Assistant\",\n *     initial: \"Hi! 👋 How can I assist you today?\",\n *   }}\n * >\n *   <YourApp/>\n * </CopilotSidebar>\n * ```\n *\n * ### Look & Feel\n *\n * By default, CopilotKit components do not have any styles. You can import CopilotKit's stylesheet at the root of your project:\n * ```tsx title=\"YourRootComponent.tsx\"\n * ...\n * import \"@copilotkit/react-ui/styles.css\"; // [!code highlight]\n *\n * export function YourRootComponent() {\n *   return (\n *     <CopilotKit>\n *       ...\n *     </CopilotKit>\n *   );\n * }\n * ```\n * For more information about how to customize the styles, check out the [Customize Look & Feel](/guides/custom-look-and-feel/customize-built-in-ui-components) guide.\n */\nimport React, { useState } from \"react\";\nimport { CopilotModal, CopilotModalProps } from \"./Modal\";\n\nexport function CopilotSidebar(props: CopilotModalProps) {\n  props = {\n    ...props,\n    className: props.className ? props.className + \" copilotKitSidebar\" : \"copilotKitSidebar\",\n  };\n  const [expandedClassName, setExpandedClassName] = useState(\n    props.defaultOpen ? \"sidebarExpanded\" : \"\",\n  );\n\n  const onSetOpen = (open: boolean) => {\n    props.onSetOpen?.(open);\n    setExpandedClassName(open ? \"sidebarExpanded\" : \"\");\n  };\n\n  return (\n    <div className={`copilotKitSidebarContentWrapper ${expandedClassName}`}>\n      <CopilotModal {...props} {...{ onSetOpen }}>\n        {props.children}\n      </CopilotModal>\n    </div>\n  );\n}\n"], "mappings": ";;;;;;;;;AAiDA,SAAgB,gBAAgB;AAmB1B;AAhBC,SAAS,eAAe,OAA0B;AACvD,UAAQ,iCACH,QADG;AAAA,IAEN,WAAW,MAAM,YAAY,MAAM,YAAY,uBAAuB;AAAA,EACxE;AACA,QAAM,CAAC,mBAAmB,oBAAoB,IAAI;AAAA,IAChD,MAAM,cAAc,oBAAoB;AAAA,EAC1C;AAEA,QAAM,YAAY,CAAC,SAAkB;AA7DvC;AA8DI,gBAAM,cAAN,+BAAkB;AAClB,yBAAqB,OAAO,oBAAoB,EAAE;AAAA,EACpD;AAEA,SACE,oBAAC,SAAI,WAAW,mCAAmC,qBACjD,8BAAC,8DAAiB,QAAW,EAAE,UAAU,IAAxC,EACE,gBAAM,WACT,GACF;AAEJ;", "names": []}