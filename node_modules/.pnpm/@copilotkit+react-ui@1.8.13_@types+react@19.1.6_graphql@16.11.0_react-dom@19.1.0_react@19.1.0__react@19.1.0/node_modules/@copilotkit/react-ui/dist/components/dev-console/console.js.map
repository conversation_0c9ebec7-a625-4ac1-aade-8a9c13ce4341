{"version": 3, "sources": ["../../../src/components/dev-console/console.tsx", "../../../src/components/dev-console/utils.ts", "../../../src/components/dev-console/icons.tsx", "../../../src/components/chat/Icons.tsx", "../../../src/components/help-modal/modal.tsx", "../../../src/components/help-modal/icons.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useCopilotContext, useCopilotMessagesContext } from \"@copilotkit/react-core\";\nimport {\n  getPublishedCopilotKitVersion,\n  logActions,\n  logMessages,\n  logReadables,\n  shouldShowDevConsole,\n} from \"./utils\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport {\n  CheckIcon,\n  ChevronDownIcon,\n  CopilotKitIcon,\n  ExclamationMarkIcon,\n  ExclamationMarkTriangleIcon,\n} from \"./icons\";\nimport { Menu, MenuButton, MenuItem, MenuItems } from \"@headlessui/react\";\nimport { COPILOTKIT_VERSION } from \"@copilotkit/shared\";\nimport { SmallSpinnerIcon } from \"../chat/Icons\";\nimport { CopilotKitHelpModal } from \"../help-modal\";\n\ntype VersionStatus = \"unknown\" | \"checking\" | \"latest\" | \"update-available\" | \"outdated\";\n\nexport function CopilotDevConsole() {\n  const currentVersion = COPILOTKIT_VERSION;\n  const context = useCopilotContext();\n\n  // to prevent hydration errors, ensure that the component renders the same content\n  // server-side as it does during the initial client-side render to prevent a hydration\n  // mismatch\n  // see: https://nextjs.org/docs/messages/react-hydration-error#solution-1-using-useeffect-to-run-on-the-client-only\n\n  const [showDevConsole, setShowDevConsole] = useState(false);\n\n  useEffect(() => {\n    setShowDevConsole(shouldShowDevConsole(context.showDevConsole));\n  }, [context.showDevConsole]);\n\n  const dontRunTwiceInDevMode = useRef(false);\n  const [versionStatus, setVersionStatus] = useState<VersionStatus>(\"unknown\");\n  const [latestVersion, setLatestVersion] = useState<string>(\"\");\n  const consoleRef = useRef<HTMLDivElement>(null);\n  const [debugButtonMode, setDebugButtonMode] = useState<\"full\" | \"compact\">(\"full\");\n\n  const checkForUpdates = (force: boolean = false) => {\n    setVersionStatus(\"checking\");\n\n    getPublishedCopilotKitVersion(currentVersion, force)\n      .then((v) => {\n        setLatestVersion(v.latest);\n        let versionOk = false;\n\n        // match exact version or a version with a letter (e.g. 1.0.0-alpha.1)\n        if (v.current === v.latest) {\n          versionOk = true;\n        } else if (/[a-zA-Z]/.test(v.current)) {\n          versionOk = true;\n        }\n\n        if (versionOk) {\n          setVersionStatus(\"latest\");\n        } else if (v.severity !== \"low\") {\n          setVersionStatus(\"outdated\");\n        } else {\n          setVersionStatus(\"update-available\");\n        }\n      })\n      .catch((e) => {\n        console.error(e);\n        setVersionStatus(\"unknown\");\n      });\n  };\n\n  useEffect(() => {\n    if (dontRunTwiceInDevMode.current === true) {\n      return;\n    }\n    dontRunTwiceInDevMode.current = true;\n\n    checkForUpdates();\n  }, []);\n\n  if (!showDevConsole) {\n    return null;\n  }\n  return (\n    <div\n      ref={consoleRef}\n      className={\n        \"copilotKitDevConsole \" +\n        (versionStatus === \"update-available\" ? \"copilotKitDevConsoleUpgrade\" : \"\") +\n        (versionStatus === \"outdated\" ? \"copilotKitDevConsoleWarnOutdated\" : \"\")\n      }\n    >\n      <VersionInfo\n        showDevConsole={context.showDevConsole}\n        versionStatus={versionStatus}\n        currentVersion={currentVersion}\n        latestVersion={latestVersion}\n      />\n\n      <CopilotKitHelpModal />\n\n      <DebugMenuButton\n        setShowDevConsole={setShowDevConsole}\n        checkForUpdates={checkForUpdates}\n        mode={debugButtonMode}\n      />\n    </div>\n  );\n}\n\nfunction VersionInfo({\n  showDevConsole,\n  versionStatus,\n  currentVersion,\n  latestVersion,\n}: {\n  showDevConsole: boolean | \"auto\";\n  versionStatus: VersionStatus;\n  currentVersion: string;\n  latestVersion: string;\n}) {\n  const [copyStatus, setCopyStatus] = useState<string>(\"\");\n\n  let versionLabel = \"\";\n  let versionIcon: any = \"\";\n  let currentVersionLabel = currentVersion;\n\n  if (versionStatus === \"latest\") {\n    versionLabel = \"latest\";\n    versionIcon = CheckIcon;\n  } else if (versionStatus === \"checking\") {\n    versionLabel = \"checking\";\n    versionIcon = SmallSpinnerIcon;\n  } else if (versionStatus === \"update-available\") {\n    versionLabel = \"update available\";\n    versionIcon = ExclamationMarkIcon;\n    currentVersionLabel = `${currentVersion} → ${latestVersion}`;\n  } else if (versionStatus === \"outdated\") {\n    versionLabel = \"outdated\";\n    versionIcon = ExclamationMarkTriangleIcon;\n    currentVersionLabel = `${currentVersion} → ${latestVersion}`;\n  }\n\n  let asideLabel = \"\";\n  if (showDevConsole === \"auto\") {\n    asideLabel = \"(localhost only)\";\n  } else if (showDevConsole === true) {\n    asideLabel = \"(always on)\";\n  }\n\n  const installCommand = [\n    `npm install`,\n    `@copilotkit/react-core@${latestVersion}`,\n    `@copilotkit/react-ui@${latestVersion}`,\n    `@copilotkit/react-textarea@${latestVersion}`,\n    `&& npm install @copilotkit/runtime@${latestVersion}`,\n  ].join(\" \");\n\n  const handleCopyClick = () => {\n    navigator.clipboard.writeText(installCommand.trim()).then(() => {\n      setCopyStatus(\"Command copied to clipboard!\");\n      setTimeout(() => setCopyStatus(\"\"), 1000);\n    });\n  };\n\n  if (versionStatus === \"update-available\" || versionStatus === \"outdated\") {\n    return (\n      <div className=\"copilotKitVersionInfo\">\n        <p>\n          {currentVersionLabel} {versionIcon}\n        </p>\n        <button onClick={handleCopyClick}>{copyStatus || installCommand}</button>\n      </div>\n    );\n  }\n\n  return null;\n}\n\nexport default function DebugMenuButton({\n  setShowDevConsole,\n  checkForUpdates,\n  mode,\n}: {\n  setShowDevConsole: (show: boolean) => void;\n  checkForUpdates: (force: boolean) => void;\n  mode: \"full\" | \"compact\";\n}) {\n  const context = useCopilotContext();\n  const messagesContext = useCopilotMessagesContext();\n\n  return (\n    <>\n      <Menu>\n        <MenuButton\n          className={`copilotKitDebugMenuTriggerButton ${mode === \"compact\" ? \"compact\" : \"\"}`}\n        >\n          {mode == \"compact\" ? \"Debug\" : <>Debug {ChevronDownIcon}</>}\n        </MenuButton>\n\n        <MenuItems\n          transition\n          anchor=\"bottom end\"\n          className=\"copilotKitDebugMenu\"\n          style={{ zIndex: 40 }}\n        >\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => logReadables(context)}>\n              Log Readables\n            </button>\n          </MenuItem>\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => logActions(context)}>\n              Log Actions\n            </button>\n          </MenuItem>\n          <MenuItem>\n            <button\n              className=\"copilotKitDebugMenuItem\"\n              onClick={() => logMessages(messagesContext)}\n            >\n              Log Messages\n            </button>\n          </MenuItem>\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => checkForUpdates(true)}>\n              Check for Updates\n            </button>\n          </MenuItem>\n          <hr />\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => setShowDevConsole(false)}>\n              Hide Dev Console\n            </button>\n          </MenuItem>\n        </MenuItems>\n      </Menu>\n    </>\n  );\n}\n", "import {\n  CopilotContextParams,\n  CopilotMessagesContextParams,\n  defaultCopilotContextCategories,\n} from \"@copilotkit/react-core\";\nimport { CopilotKitVersion } from \"./types\";\nimport { ActionExecutionMessage, ResultMessage, TextMessage } from \"@copilotkit/runtime-client-gql\";\nimport { AgentStateMessage } from \"@copilotkit/runtime-client-gql\";\n\nexport function shouldShowDevConsole(showDevConsole: boolean | \"auto\"): boolean {\n  if (typeof showDevConsole === \"boolean\") {\n    return showDevConsole;\n  }\n  return (\n    getHostname() === \"localhost\" ||\n    getHostname() === \"127.0.0.1\" ||\n    getHostname() === \"0.0.0.0\" ||\n    getHostname() === \"::1\"\n  );\n}\n\nfunction getHostname(): string {\n  if (typeof window !== \"undefined\" && window.location) {\n    return window.location.hostname;\n  }\n  return \"\";\n}\n\nexport async function getPublishedCopilotKitVersion(\n  current: string,\n  forceCheck: boolean = false,\n): Promise<CopilotKitVersion> {\n  const LOCAL_STORAGE_KEY = \"__copilotkit_version_check__\";\n  const serializedVersion = localStorage.getItem(LOCAL_STORAGE_KEY);\n  if (serializedVersion && !forceCheck) {\n    try {\n      const parsedVersion: CopilotKitVersion = JSON.parse(serializedVersion);\n      const oneHour = 60 * 60 * 1000;\n      const now = new Date().getTime();\n\n      if (\n        parsedVersion.current === current &&\n        now - new Date(parsedVersion.lastChecked).getTime() < oneHour\n      ) {\n        return parsedVersion;\n      }\n    } catch (error) {\n      console.error(\"Failed to parse CopilotKitVersion from localStorage\", error);\n    }\n  }\n\n  try {\n    const response = await fetch(\"https://api.cloud.copilotkit.ai/check-for-updates\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        packages: [\n          {\n            packageName: \"@copilotkit/shared\",\n            packageVersion: current,\n          },\n        ],\n      }),\n    });\n\n    const data = await response.json();\n\n    const version: CopilotKitVersion = {\n      current,\n      lastChecked: new Date().getTime(),\n      latest: data.packages[0].latestVersion,\n      severity: data.packages[0].severity,\n      advisory: data.packages[0].advisory || null,\n    };\n\n    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(version));\n    return version;\n  } catch (error) {\n    console.error(\"Failed to check for updates\", error);\n    throw error;\n  }\n}\n\nexport function logReadables(context: CopilotContextParams) {\n  console.log(\"%cCurrent Readables:\", \"font-size: 16px; font-weight: bold;\");\n\n  const readables = context.getContextString([], defaultCopilotContextCategories).trim();\n  if (readables.length === 0) {\n    console.log(\"No readables found\");\n    return;\n  }\n  console.log(readables);\n}\n\nexport function logActions(context: CopilotContextParams) {\n  console.log(\"%cCurrent Actions:\", \"font-size: 16px; font-weight: bold;\");\n\n  if (Object.values(context.actions).length === 0) {\n    console.log(\"No actions found\");\n    return;\n  }\n  for (const action of Object.values(context.actions)) {\n    console.group(action.name);\n    console.log(\"name\", action.name);\n    console.log(\"description\", action.description);\n    console.log(\"parameters\", action.parameters);\n\n    console.groupEnd();\n  }\n}\n\nexport function logMessages(context: CopilotMessagesContextParams) {\n  console.log(\"%cCurrent Messages:\", \"font-size: 16px; font-weight: bold;\");\n\n  if (context.messages.length === 0) {\n    console.log(\"No messages found\");\n    return;\n  }\n\n  const tableData = context.messages.map((message) => {\n    if (message.isTextMessage()) {\n      return {\n        id: message.id,\n        type: \"TextMessage\",\n        role: message.role,\n        name: undefined,\n        scope: undefined,\n        content: message.content,\n      };\n    } else if (message.isActionExecutionMessage()) {\n      return {\n        id: message.id,\n        type: \"ActionExecutionMessage\",\n        role: undefined,\n        name: message.name,\n        scope: message.parentMessageId,\n        content: message.arguments,\n      };\n    } else if (message.isResultMessage()) {\n      return {\n        id: message.id,\n        type: \"ResultMessage\",\n        role: undefined,\n        name: message.actionName,\n        scope: message.actionExecutionId,\n        content: message.result,\n      };\n    } else if (message.isAgentStateMessage()) {\n      return {\n        id: message.id,\n        type: `AgentStateMessage (running: ${message.running})`,\n        role: message.role,\n        name: undefined,\n        scope: message.threadId,\n        content: message.state,\n      };\n    }\n  });\n  console.table(tableData);\n}\n", "export const ExclamationMarkTriangleIcon = (\n  <svg\n    width=\"13.3967723px\"\n    height=\"12px\"\n    viewBox=\"0 0 13.3967723 12\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"exclamation-triangle\" fill=\"#CD2121\">\n        <path\n          d=\"M5.39935802,0.75 C5.97670802,-0.25 7.42007802,-0.25 7.99742802,0.75 L13.193588,9.75 C13.770888,10.75 13.049288,12 11.894588,12 L1.50223802,12 C0.34753802,12 -0.37414898,10.75 0.20319802,9.75 L5.39935802,0.75 Z M6.69838802,2.5 C7.11260802,2.5 7.44838802,2.83579 7.44838802,3.25 L7.44838802,6.25 C7.44838802,6.66421 7.11260802,7 6.69838802,7 C6.28417802,7 5.94838802,6.66421 5.94838802,6.25 L5.94838802,3.25 C5.94838802,2.83579 6.28417802,2.5 6.69838802,2.5 Z M6.69838802,10.5 C7.25067802,10.5 7.69838802,10.0523 7.69838802,9.5 C7.69838802,8.9477 7.25067802,8.5 6.69838802,8.5 C6.14610802,8.5 5.69838802,8.9477 5.69838802,9.5 C5.69838802,10.0523 6.14610802,10.5 6.69838802,10.5 Z\"\n          id=\"Shape\"\n        ></path>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const ExclamationMarkIcon = (\n  <svg\n    width=\"14px\"\n    height=\"14px\"\n    viewBox=\"0 0 14 14\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"exclamation-circle\" fill=\"#EC662C\">\n        <path\n          d=\"M7,14 C10.866,14 14,10.866 14,7 C14,3.13401 10.866,0 7,0 C3.13401,0 0,3.13401 0,7 C0,10.866 3.13401,14 7,14 Z M7,3 C7.41421,3 7.75,3.33579 7.75,3.75 L7.75,6.75 C7.75,7.16421 7.41421,7.5 7,7.5 C6.58579,7.5 6.25,7.16421 6.25,6.75 L6.25,3.75 C6.25,3.33579 6.58579,3 7,3 Z M7,11 C7.55228,11 8,10.5523 8,10 C8,9.4477 7.55228,9 7,9 C6.44772,9 6,9.4477 6,10 C6,10.5523 6.44772,11 7,11 Z\"\n          id=\"Shape\"\n        ></path>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const ChevronDownIcon = (\n  <svg\n    width=\"7px\"\n    height=\"4px\"\n    viewBox=\"0 0 7 4\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"currentColor\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"Group\" fill=\"currentColor\" fillRule=\"nonzero\">\n        <path\n          d=\"M3.71690723,3.90271086 C3.59268176,4.03242971 3.39143629,4.03242971 3.26721082,3.90271086 L0.0853966595,0.57605615 C-0.0314221035,0.444981627 -0.0279751448,0.240725043 0.0931934622,0.114040675 C0.214362069,-0.0126436935 0.409725445,-0.0162475626 0.535093061,0.105888951 L3.49205902,3.19746006 L6.44902499,0.105888951 C6.52834574,0.0168884389 6.64780588,-0.0197473458 6.7605411,0.0103538404 C6.87327633,0.0404550266 6.96130636,0.132492308 6.99009696,0.250359396 C7.01888756,0.368226483 6.98384687,0.493124608 6.89872139,0.57605615 L3.71690723,3.90271086 Z\"\n          id=\"Path\"\n        ></path>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    width=\"14px\"\n    height=\"14px\"\n    viewBox=\"0 0 14 14\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"Group-2\" transform=\"translate(-118, 0)\" fill=\"#1BC030\" fillRule=\"nonzero\">\n        <g id=\"Group\" transform=\"translate(118, 0)\">\n          <path\n            d=\"M0,7 C0,3.13384615 3.13384615,0 7,0 C10.8661538,0 14,3.13384615 14,7 C14,10.8661538 10.8661538,14 7,14 C3.13384615,14 0,10.8661538 0,7 Z M9.59179487,5.69764103 C9.70905818,5.54139023 9.73249341,5.33388318 9.65303227,5.15541491 C9.57357113,4.97694665 9.40367989,4.85551619 9.20909814,4.83811118 C9.01451638,4.82070616 8.82577109,4.91005717 8.71589744,5.07158974 L6.39261538,8.32389744 L5.22666667,7.15794872 C5.01450582,6.96025518 4.68389046,6.9660885 4.47883563,7.17114332 C4.27378081,7.37619815 4.26794748,7.70681351 4.46564103,7.91897436 L6.08102564,9.53435897 C6.19289944,9.64614839 6.3482622,9.70310251 6.50588106,9.69010587 C6.66349993,9.67710922 6.80743532,9.59547613 6.89948718,9.46687179 L9.59179487,5.69764103 L9.59179487,5.69764103 Z\"\n            id=\"Shape\"\n          ></path>\n        </g>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const CopilotKitIcon = (\n  <svg\n    width=\"33px\"\n    height=\"35px\"\n    viewBox=\"0 0 33 35\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <title>bd5c9079-929b-4d55-bdc9-16d1c8181b71</title>\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <image\n        x=\"0\"\n        y=\"0\"\n        width=\"33\"\n        height=\"35\"\n        xlinkHref=\"data:image/png;base64,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\"\n      ></image>\n    </g>\n  </svg>\n);\n", "import React from \"react\";\n\nexport const OpenIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <g transform=\"translate(24, 0) scale(-1, 1)\">\n      <path\n        fillRule=\"evenodd\"\n        d=\"M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z\"\n        clipRule=\"evenodd\"\n      />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\n  </svg>\n);\n\nexport const HeaderCloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const SendIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 19V5m0 0l-7 7m7-7l7 7\" />\n  </svg>\n);\n\nexport const MicrophoneIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z\"\n    />\n  </svg>\n);\n\nexport const StopIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z\"\n    />\n  </svg>\n);\n\nexport const RegenerateIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n    />\n  </svg>\n);\n\nexport const CopyIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\n    />\n  </svg>\n);\n\nexport const SmallSpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"13px\", height: \"13px\" }}></span>\n);\n\nexport const SpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"24px\", height: \"24px\" }}></span>\n);\n\nexport const ActivityIcon = (\n  <div style={{ display: \"flex\", alignItems: \"center\", gap: \"4px\" }}>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.2s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.4s\" }}></span>\n  </div>\n);\n\nexport const ThumbsUpIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z\"\n    />\n  </svg>\n);\n\nexport const ThumbsDownIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384\"\n    />\n  </svg>\n);\n\nexport const DownloadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\n    />\n  </svg>\n);\n\nexport const UploadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n  </svg>\n);\n", "import React, { useMemo, useState, useRef, useEffect } from \"react\";\nimport { CloseIcon } from \"./icons\";\n\nexport function CopilotKitHelpModal() {\n  const [showHelpModal, setShowHelpModal] = useState(false);\n  const buttonRef = useRef<HTMLButtonElement>(null);\n  const popoverRef = useRef<HTMLDivElement>(null);\n\n  // Close popover when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        popoverRef.current &&\n        !popoverRef.current.contains(event.target as Node) &&\n        buttonRef.current &&\n        !buttonRef.current.contains(event.target as Node)\n      ) {\n        setShowHelpModal(false);\n      }\n    };\n\n    if (showHelpModal) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [showHelpModal]);\n\n  const HelpButton = () => (\n    <button\n      ref={buttonRef}\n      onClick={() => setShowHelpModal(!showHelpModal)}\n      className=\"copilotKitDebugMenuTriggerButton relative\"\n      aria-label=\"Open Help\"\n    >\n      Help\n    </button>\n  );\n\n  return (\n    <div className=\"relative\">\n      <HelpButton />\n      {showHelpModal && (\n        <div\n          ref={popoverRef}\n          className=\"absolute mt-2 z-50\"\n          style={{\n            top: \"100%\",\n            right: \"-120px\",\n            width: \"380px\",\n          }}\n        >\n          <div className=\"copilotKitHelpModal rounded-lg shadow-xl w-full p-4 flex-col relative\">\n            <button\n              className=\"copilotKitHelpModalCloseButton absolute text-gray-400 hover:text-gray-600 focus:outline-none\"\n              style={{ top: \"10px\", right: \"10px\" }}\n              onClick={() => setShowHelpModal(false)}\n              aria-label=\"Close\"\n            >\n              <CloseIcon />\n            </button>\n            <div className=\"w-full flex mb-6 justify-center\">\n              <h2 className=\"text-2xl font-bold\">Help Options</h2>\n            </div>\n            <div className=\"space-y-4 mb-4\">\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://docs.copilotkit.ai/coagents/troubleshooting/common-issues\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Visit the Troubleshooting and FAQ section in the docs\n                </a>\n              </div>\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://go.copilotkit.ai/dev-console-support-discord\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Go to Discord Support Channel (Community Support)\n                </a>\n              </div>\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://go.copilotkit.ai/dev-console-support-slack\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Apply for Priority Direct Slack Support\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n", "import React from \"react\";\n\nexport const LifeBuoyIcon = () => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    className=\"icon icon-tabler icons-tabler-outline icon-tabler-lifebuoy\"\n  >\n    <g transform=\"translate(0, -1)\">\n      <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n      <path d=\"M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0\" />\n      <path d=\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\" />\n      <path d=\"M15 15l3.35 3.35\" />\n      <path d=\"M9 15l-3.35 3.35\" />\n      <path d=\"M5.65 5.65l3.35 3.35\" />\n      <path d=\"M18.35 5.65l-3.35 3.35\" />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = () => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"20\"\n    height=\"20\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const LoadingSpinnerIcon = ({ color = \"rgb(107 114 128)\" }: { color?: string }) => (\n  <svg\n    style={{\n      animation: \"copilotKitSpinAnimation 1s linear infinite\",\n      color,\n    }}\n    width=\"24\"\n    height=\"24\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n  >\n    <circle\n      style={{ opacity: 0.25 }}\n      cx=\"12\"\n      cy=\"12\"\n      r=\"10\"\n      stroke=\"currentColor\"\n      strokeWidth=\"4\"\n    ></circle>\n    <path\n      style={{ opacity: 0.75 }}\n      fill=\"currentColor\"\n      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n    ></path>\n  </svg>\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,IAAAA,qBAA6D;;;ACF7D,wBAIO;AAKA,SAAS,qBAAqB,gBAA2C;AAC9E,MAAI,OAAO,mBAAmB,WAAW;AACvC,WAAO;AAAA,EACT;AACA,SACE,YAAY,MAAM,eAClB,YAAY,MAAM,eAClB,YAAY,MAAM,aAClB,YAAY,MAAM;AAEtB;AAEA,SAAS,cAAsB;AAC7B,MAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,WAAO,OAAO,SAAS;AAAA,EACzB;AACA,SAAO;AACT;AAEA,SAAsB,8BACpB,SACA,aAAsB,OACM;AAAA;AAC5B,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB,aAAa,QAAQ,iBAAiB;AAChE,QAAI,qBAAqB,CAAC,YAAY;AACpC,UAAI;AACF,cAAM,gBAAmC,KAAK,MAAM,iBAAiB;AACrE,cAAM,UAAU,KAAK,KAAK;AAC1B,cAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAE/B,YACE,cAAc,YAAY,WAC1B,MAAM,IAAI,KAAK,cAAc,WAAW,EAAE,QAAQ,IAAI,SACtD;AACA,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,uDAAuD,KAAK;AAAA,MAC5E;AAAA,IACF;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,qDAAqD;AAAA,QAChF,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,UAAU;AAAA,YACR;AAAA,cACE,aAAa;AAAA,cACb,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,YAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,YAAM,UAA6B;AAAA,QACjC;AAAA,QACA,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAAA,QAChC,QAAQ,KAAK,SAAS,CAAC,EAAE;AAAA,QACzB,UAAU,KAAK,SAAS,CAAC,EAAE;AAAA,QAC3B,UAAU,KAAK,SAAS,CAAC,EAAE,YAAY;AAAA,MACzC;AAEA,mBAAa,QAAQ,mBAAmB,KAAK,UAAU,OAAO,CAAC;AAC/D,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA+B,KAAK;AAClD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAEO,SAAS,aAAa,SAA+B;AAC1D,UAAQ,IAAI,wBAAwB,qCAAqC;AAEzE,QAAM,YAAY,QAAQ,iBAAiB,CAAC,GAAG,iDAA+B,EAAE,KAAK;AACrF,MAAI,UAAU,WAAW,GAAG;AAC1B,YAAQ,IAAI,oBAAoB;AAChC;AAAA,EACF;AACA,UAAQ,IAAI,SAAS;AACvB;AAEO,SAAS,WAAW,SAA+B;AACxD,UAAQ,IAAI,sBAAsB,qCAAqC;AAEvE,MAAI,OAAO,OAAO,QAAQ,OAAO,EAAE,WAAW,GAAG;AAC/C,YAAQ,IAAI,kBAAkB;AAC9B;AAAA,EACF;AACA,aAAW,UAAU,OAAO,OAAO,QAAQ,OAAO,GAAG;AACnD,YAAQ,MAAM,OAAO,IAAI;AACzB,YAAQ,IAAI,QAAQ,OAAO,IAAI;AAC/B,YAAQ,IAAI,eAAe,OAAO,WAAW;AAC7C,YAAQ,IAAI,cAAc,OAAO,UAAU;AAE3C,YAAQ,SAAS;AAAA,EACnB;AACF;AAEO,SAAS,YAAY,SAAuC;AACjE,UAAQ,IAAI,uBAAuB,qCAAqC;AAExE,MAAI,QAAQ,SAAS,WAAW,GAAG;AACjC,YAAQ,IAAI,mBAAmB;AAC/B;AAAA,EACF;AAEA,QAAM,YAAY,QAAQ,SAAS,IAAI,CAAC,YAAY;AAClD,QAAI,QAAQ,cAAc,GAAG;AAC3B,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,gBAAgB,GAAG;AACpC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,oBAAoB,GAAG;AACxC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM,+BAA+B,QAAQ;AAAA,QAC7C,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,QACN,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,UAAQ,MAAM,SAAS;AACzB;;;ADvJA,IAAAC,gBAAmD;;;AEA3C;AAVD,IAAM,8BACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IAEN,sDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,sDAAC,OAAE,IAAG,wBAAuB,MAAK,WAChC;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF;AAAA;AACF;AAGK,IAAM,sBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IAEN,sDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,sDAAC,OAAE,IAAG,sBAAqB,MAAK,WAC9B;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF;AAAA;AACF;AAGK,IAAM,kBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,MAAK;AAAA,IAEL,sDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,sDAAC,OAAE,IAAG,SAAQ,MAAK,gBAAe,UAAS,WACzC;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF;AAAA;AACF;AAGK,IAAM,YACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IAEN,sDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,sDAAC,OAAE,IAAG,WAAU,WAAU,sBAAqB,MAAK,WAAU,UAAS,WACrE,sDAAC,OAAE,IAAG,SAAQ,WAAU,qBACtB;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF,GACF;AAAA;AACF;;;AF1DF,IAAAC,gBAAsD;AACtD,oBAAmC;;;AGR7B,IAAAC,sBAAA;AA6HC,IAAM,mBACX,6CAAC,UAAK,WAAU,qBAAoB,OAAO,EAAE,OAAO,QAAQ,QAAQ,OAAO,GAAG;;;ACzIhF,mBAA4D;;;ACexD,IAAAC,sBAAA;AAYG,IAAM,YAAY,MACvB;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,uDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,wBAAuB;AAAA;AAC9E;;;ADPE,IAAAC,sBAAA;AA5BG,SAAS,sBAAsB;AACpC,QAAM,CAAC,eAAe,gBAAgB,QAAI,uBAAS,KAAK;AACxD,QAAM,gBAAY,qBAA0B,IAAI;AAChD,QAAM,iBAAa,qBAAuB,IAAI;AAG9C,8BAAU,MAAM;AACd,UAAM,qBAAqB,CAAC,UAAsB;AAChD,UACE,WAAW,WACX,CAAC,WAAW,QAAQ,SAAS,MAAM,MAAc,KACjD,UAAU,WACV,CAAC,UAAU,QAAQ,SAAS,MAAM,MAAc,GAChD;AACA,yBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,eAAe;AACjB,eAAS,iBAAiB,aAAa,kBAAkB;AAAA,IAC3D;AAEA,WAAO,MAAM;AACX,eAAS,oBAAoB,aAAa,kBAAkB;AAAA,IAC9D;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,aAAa,MACjB;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,SAAS,MAAM,iBAAiB,CAAC,aAAa;AAAA,MAC9C,WAAU;AAAA,MACV,cAAW;AAAA,MACZ;AAAA;AAAA,EAED;AAGF,SACE,8CAAC,SAAI,WAAU,YACb;AAAA,iDAAC,cAAW;AAAA,IACX,iBACC;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,WAAU;AAAA,QACV,OAAO;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QAEA,wDAAC,SAAI,WAAU,yEACb;AAAA;AAAA,YAAC;AAAA;AAAA,cACC,WAAU;AAAA,cACV,OAAO,EAAE,KAAK,QAAQ,OAAO,OAAO;AAAA,cACpC,SAAS,MAAM,iBAAiB,KAAK;AAAA,cACrC,cAAW;AAAA,cAEX,uDAAC,aAAU;AAAA;AAAA,UACb;AAAA,UACA,6CAAC,SAAI,WAAU,mCACb,uDAAC,QAAG,WAAU,sBAAqB,0BAAY,GACjD;AAAA,UACA,8CAAC,SAAI,WAAU,kBACb;AAAA,yDAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,YACA,6CAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,YACA,6CAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,aACF;AAAA,WACF;AAAA;AAAA,IACF;AAAA,KAEJ;AAEJ;;;AJZI,IAAAC,sBAAA;AA/DG,SAAS,oBAAoB;AAClC,QAAM,iBAAiB;AACvB,QAAM,cAAU,sCAAkB;AAOlC,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,wBAAS,KAAK;AAE1D,+BAAU,MAAM;AACd,sBAAkB,qBAAqB,QAAQ,cAAc,CAAC;AAAA,EAChE,GAAG,CAAC,QAAQ,cAAc,CAAC;AAE3B,QAAM,4BAAwB,sBAAO,KAAK;AAC1C,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAwB,SAAS;AAC3E,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAiB,EAAE;AAC7D,QAAM,iBAAa,sBAAuB,IAAI;AAC9C,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,wBAA6B,MAAM;AAEjF,QAAM,kBAAkB,CAAC,QAAiB,UAAU;AAClD,qBAAiB,UAAU;AAE3B,kCAA8B,gBAAgB,KAAK,EAChD,KAAK,CAAC,MAAM;AACX,uBAAiB,EAAE,MAAM;AACzB,UAAI,YAAY;AAGhB,UAAI,EAAE,YAAY,EAAE,QAAQ;AAC1B,oBAAY;AAAA,MACd,WAAW,WAAW,KAAK,EAAE,OAAO,GAAG;AACrC,oBAAY;AAAA,MACd;AAEA,UAAI,WAAW;AACb,yBAAiB,QAAQ;AAAA,MAC3B,WAAW,EAAE,aAAa,OAAO;AAC/B,yBAAiB,UAAU;AAAA,MAC7B,OAAO;AACL,yBAAiB,kBAAkB;AAAA,MACrC;AAAA,IACF,CAAC,EACA,MAAM,CAAC,MAAM;AACZ,cAAQ,MAAM,CAAC;AACf,uBAAiB,SAAS;AAAA,IAC5B,CAAC;AAAA,EACL;AAEA,+BAAU,MAAM;AACd,QAAI,sBAAsB,YAAY,MAAM;AAC1C;AAAA,IACF;AACA,0BAAsB,UAAU;AAEhC,oBAAgB;AAAA,EAClB,GAAG,CAAC,CAAC;AAEL,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,WACE,2BACC,kBAAkB,qBAAqB,gCAAgC,OACvE,kBAAkB,aAAa,qCAAqC;AAAA,MAGvE;AAAA;AAAA,UAAC;AAAA;AAAA,YACC,gBAAgB,QAAQ;AAAA,YACxB;AAAA,YACA;AAAA,YACA;AAAA;AAAA,QACF;AAAA,QAEA,6CAAC,uBAAoB;AAAA,QAErB;AAAA,UAAC;AAAA;AAAA,YACC;AAAA,YACA;AAAA,YACA,MAAM;AAAA;AAAA,QACR;AAAA;AAAA;AAAA,EACF;AAEJ;AAEA,SAAS,YAAY;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,QAAM,CAAC,YAAY,aAAa,QAAI,wBAAiB,EAAE;AAEvD,MAAI,eAAe;AACnB,MAAI,cAAmB;AACvB,MAAI,sBAAsB;AAE1B,MAAI,kBAAkB,UAAU;AAC9B,mBAAe;AACf,kBAAc;AAAA,EAChB,WAAW,kBAAkB,YAAY;AACvC,mBAAe;AACf,kBAAc;AAAA,EAChB,WAAW,kBAAkB,oBAAoB;AAC/C,mBAAe;AACf,kBAAc;AACd,0BAAsB,GAAG,yBAAoB;AAAA,EAC/C,WAAW,kBAAkB,YAAY;AACvC,mBAAe;AACf,kBAAc;AACd,0BAAsB,GAAG,yBAAoB;AAAA,EAC/C;AAEA,MAAI,aAAa;AACjB,MAAI,mBAAmB,QAAQ;AAC7B,iBAAa;AAAA,EACf,WAAW,mBAAmB,MAAM;AAClC,iBAAa;AAAA,EACf;AAEA,QAAM,iBAAiB;AAAA,IACrB;AAAA,IACA,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,8BAA8B;AAAA,IAC9B,sCAAsC;AAAA,EACxC,EAAE,KAAK,GAAG;AAEV,QAAM,kBAAkB,MAAM;AAC5B,cAAU,UAAU,UAAU,eAAe,KAAK,CAAC,EAAE,KAAK,MAAM;AAC9D,oBAAc,8BAA8B;AAC5C,iBAAW,MAAM,cAAc,EAAE,GAAG,GAAI;AAAA,IAC1C,CAAC;AAAA,EACH;AAEA,MAAI,kBAAkB,sBAAsB,kBAAkB,YAAY;AACxE,WACE,8CAAC,SAAI,WAAU,yBACb;AAAA,oDAAC,OACE;AAAA;AAAA,QAAoB;AAAA,QAAE;AAAA,SACzB;AAAA,MACA,6CAAC,YAAO,SAAS,iBAAkB,wBAAc,gBAAe;AAAA,OAClE;AAAA,EAEJ;AAEA,SAAO;AACT;AAEe,SAAR,gBAAiC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,cAAU,sCAAkB;AAClC,QAAM,sBAAkB,8CAA0B;AAElD,SACE,6EACE,wDAAC,sBACC;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,WAAW,oCAAoC,SAAS,YAAY,YAAY;AAAA,QAE/E,kBAAQ,YAAY,UAAU,8EAAE;AAAA;AAAA,UAAO;AAAA,WAAgB;AAAA;AAAA,IAC1D;AAAA,IAEA;AAAA,MAAC;AAAA;AAAA,QACC,YAAU;AAAA,QACV,QAAO;AAAA,QACP,WAAU;AAAA,QACV,OAAO,EAAE,QAAQ,GAAG;AAAA,QAEpB;AAAA,uDAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,aAAa,OAAO,GAAG,2BAElF,GACF;AAAA,UACA,6CAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,WAAW,OAAO,GAAG,yBAEhF,GACF;AAAA,UACA,6CAAC,0BACC;AAAA,YAAC;AAAA;AAAA,cACC,WAAU;AAAA,cACV,SAAS,MAAM,YAAY,eAAe;AAAA,cAC3C;AAAA;AAAA,UAED,GACF;AAAA,UACA,6CAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,gBAAgB,IAAI,GAAG,+BAElF,GACF;AAAA,UACA,6CAAC,QAAG;AAAA,UACJ,6CAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,kBAAkB,KAAK,GAAG,8BAErF,GACF;AAAA;AAAA;AAAA,IACF;AAAA,KACF,GACF;AAEJ;", "names": ["import_react_core", "import_react", "import_react", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime"]}