import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import QueueStatusWidget from '../QueueStatusWidget';
import { useQueueStats } from '@/hooks/useQueueStats';

// Mock the hooks and dependencies
vi.mock('@/hooks/useQueueStats');
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock Supabase
vi.mock('@/lib/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
    },
  },
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '5 minutes ago'),
}));

const mockUseQueueStats = vi.mocked(useQueueStats);

const mockQueueStats = {
  tenant_id: 'test-tenant',
  queued: 5,
  processing: 2,
  completed: 100,
  failed: 3,
  total_today: 103,
  avg_processing_time_seconds: 45.5,
  last_updated: '2024-01-15T10:30:00Z',
  worker_status: 'healthy' as const,
};

describe('QueueStatusWidget', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state correctly', () => {
    mockUseQueueStats.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);
    
    expect(screen.getByText('Call Queue Status')).toBeInTheDocument();
    expect(screen.getByRole('generic', { name: /loading/i })).toBeInTheDocument();
  });

  it('renders error state correctly', () => {
    const mockMutate = vi.fn();
    mockUseQueueStats.mockReturnValue({
      data: null,
      isLoading: false,
      error: new Error('Failed to fetch'),
      mutate: mockMutate,
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);
    
    expect(screen.getByText('Failed to load queue status')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
    
    fireEvent.click(screen.getByText('Retry'));
    expect(mockMutate).toHaveBeenCalled();
  });

  it('renders queue stats correctly', () => {
    mockUseQueueStats.mockReturnValue({
      data: mockQueueStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);
    
    // Check main stats
    expect(screen.getByText('5')).toBeInTheDocument(); // queued
    expect(screen.getByText('2')).toBeInTheDocument(); // processing
    expect(screen.getByText('100 completed')).toBeInTheDocument();
    expect(screen.getByText('3 failed')).toBeInTheDocument();
    expect(screen.getByText('103 total')).toBeInTheDocument();
    
    // Check worker status
    expect(screen.getByText('Healthy')).toBeInTheDocument();
    
    // Check processing time
    expect(screen.getByText('Avg processing time: 45.5s')).toBeInTheDocument();
  });

  it('disables clear queue button when queue is empty', () => {
    const emptyQueueStats = { ...mockQueueStats, queued: 0 };
    mockUseQueueStats.mockReturnValue({
      data: emptyQueueStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);

    const clearButton = screen.getByText(/Clear Queue \(0\)/);
    expect(clearButton).toBeDisabled();
  });

  it('enables clear queue button when queue has items', () => {
    mockUseQueueStats.mockReturnValue({
      data: mockQueueStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);
    
    const clearButton = screen.getByText(/Clear Queue \(5\)/);
    expect(clearButton).not.toBeDisabled();
  });

  it('calls clearQueue when clear button is clicked', async () => {
    const mockClearQueue = vi.fn().mockResolvedValue(undefined);
    mockUseQueueStats.mockReturnValue({
      data: mockQueueStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: mockClearQueue,
    });

    render(<QueueStatusWidget />);
    
    const clearButton = screen.getByText(/Clear Queue \(5\)/);
    fireEvent.click(clearButton);
    
    await waitFor(() => {
      expect(mockClearQueue).toHaveBeenCalled();
    });
  });

  it('calculates success rate correctly', () => {
    mockUseQueueStats.mockReturnValue({
      data: mockQueueStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);

    // Success rate should be 100/(100+3) = 97.1%
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toHaveAttribute('aria-valuenow', '97.08737864077669');
  });

  it('handles different worker statuses', () => {
    const degradedStats = { ...mockQueueStats, worker_status: 'degraded' as const };
    mockUseQueueStats.mockReturnValue({
      data: degradedStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);
    
    expect(screen.getByText('Degraded')).toBeInTheDocument();
  });

  it('formats processing time correctly for minutes', () => {
    const longProcessingStats = { ...mockQueueStats, avg_processing_time_seconds: 125.5 };
    mockUseQueueStats.mockReturnValue({
      data: longProcessingStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);

    expect(screen.getByText('Avg processing time: 2.1m')).toBeInTheDocument();
  });

  it('handles missing last_updated gracefully', () => {
    const noUpdateStats = { ...mockQueueStats, last_updated: '' };
    mockUseQueueStats.mockReturnValue({
      data: noUpdateStats,
      isLoading: false,
      error: null,
      mutate: vi.fn(),
      clearQueue: vi.fn(),
    });

    render(<QueueStatusWidget />);
    
    expect(screen.getByText(/Last updated never/)).toBeInTheDocument();
  });
});
