{"version": 3, "file": "sessions.d.ts", "sourceRoot": "", "sources": ["../../src/resources/sessions/sessions.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AACnC,OAAO,KAAK,YAAY,MAAM,aAAa,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,KAAK,OAAO,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAC3D,OAAO,KAAK,YAAY,MAAM,aAAa,CAAC;AAC5C,OAAO,EAAE,SAAS,EAAE,yBAAyB,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AACrF,OAAO,KAAK,UAAU,MAAM,WAAW,CAAC;AACxC,OAAO,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAE9E,qBAAa,QAAS,SAAQ,WAAW;IACvC,SAAS,EAAE,YAAY,CAAC,SAAS,CAA4C;IAC7E,IAAI,EAAE,OAAO,CAAC,IAAI,CAAkC;IACpD,SAAS,EAAE,YAAY,CAAC,SAAS,CAA4C;IAC7E,OAAO,EAAE,UAAU,CAAC,OAAO,CAAwC;IAEnE;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC;IAIxG;;OAEG;IACH,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;IAI7F;;OAEG;IACH,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IAItG;;OAEG;IACH,IAAI,CAAC,KAAK,CAAC,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IACpG,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;IAWzE;;OAEG;IACH,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;CAGnF;AAED,MAAM,WAAW,OAAO;IACtB,EAAE,EAAE,MAAM,CAAC;IAEX,SAAS,EAAE,MAAM,CAAC;IAElB,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,WAAW,GAAG,WAAW,GAAG,cAAc,GAAG,gBAAgB,CAAC;IAEtE,SAAS,EAAE,MAAM,CAAC;IAElB,MAAM,EAAE,SAAS,GAAG,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAExD,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACxC;AAED,MAAM,WAAW,eAAe;IAC9B,qBAAqB,EAAE,MAAM,CAAC;IAE9B,WAAW,EAAE,MAAM,CAAC;IAEpB,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAEnC,KAAK,EAAE,MAAM,CAAC;CACf;AAED,yBAAiB,eAAe,CAAC;IAC/B,UAAiB,IAAI;QACnB,EAAE,EAAE,MAAM,CAAC;QAEX,qBAAqB,EAAE,MAAM,CAAC;QAE9B,WAAW,EAAE,MAAM,CAAC;QAEpB,UAAU,EAAE,MAAM,CAAC;QAEnB,KAAK,EAAE,MAAM,CAAC;QAEd,GAAG,EAAE,MAAM,CAAC;KACb;CACF;AAED,MAAM,WAAW,qBAAqB;IACpC,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB,SAAS,EAAE,MAAM,CAAC;IAElB,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,WAAW,GAAG,WAAW,GAAG,cAAc,GAAG,gBAAgB,CAAC;IAEtE;;OAEG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB,SAAS,EAAE,MAAM,CAAC;IAElB,MAAM,EAAE,SAAS,GAAG,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAExD,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACxC;AAED,MAAM,WAAW,uBAAuB;IACtC,EAAE,EAAE,MAAM,CAAC;IAEX,SAAS,EAAE,MAAM,CAAC;IAElB,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAE,WAAW,GAAG,WAAW,GAAG,cAAc,GAAG,gBAAgB,CAAC;IAEtE,SAAS,EAAE,MAAM,CAAC;IAElB,MAAM,EAAE,SAAS,GAAG,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAExD,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAE3B;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACxC;AAED,MAAM,MAAM,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AAEjD,MAAM,WAAW,mBAAmB;IAClC;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB,eAAe,CAAC,EAAE,mBAAmB,CAAC,eAAe,CAAC;IAEtD;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IAEpB;;;OAGG;IACH,OAAO,CAAC,EACJ,OAAO,GACP,KAAK,CAAC,mBAAmB,CAAC,sBAAsB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;IAEhG;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,GAAG,WAAW,GAAG,cAAc,GAAG,gBAAgB,CAAC;IAEvE;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IAEjB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACxC;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,eAAe;QAC9B;;WAEG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;WAEG;QACH,QAAQ,CAAC,EAAE,OAAO,CAAC;QAEnB;;;WAGG;QACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAE9B;;;WAGG;QACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAE9B,OAAO,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC;QAElC;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;;WAGG;QACH,WAAW,CAAC,EAAE,eAAe,CAAC,WAAW,CAAC;QAE1C;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,aAAa,CAAC,EAAE,OAAO,CAAC;QAExB;;WAEG;QACH,aAAa,CAAC,EAAE,OAAO,CAAC;QAExB,QAAQ,CAAC,EAAE,eAAe,CAAC,QAAQ,CAAC;KACrC;IAED,UAAiB,eAAe,CAAC;QAC/B,UAAiB,OAAO;YACtB;;eAEG;YACH,EAAE,EAAE,MAAM,CAAC;YAEX;;eAEG;YACH,OAAO,CAAC,EAAE,OAAO,CAAC;SACnB;QAED;;;WAGG;QACH,UAAiB,WAAW;YAC1B,QAAQ,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,CAAC;YAE3D,OAAO,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;YAEtC,WAAW,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC;YAExB;;;eAGG;YACH,OAAO,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAExB;;;eAGG;YACH,gBAAgB,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,CAAC,CAAC;YAE5E,MAAM,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC;SAC7B;QAED,UAAiB,WAAW,CAAC;YAC3B,UAAiB,MAAM;gBACrB,SAAS,CAAC,EAAE,MAAM,CAAC;gBAEnB,QAAQ,CAAC,EAAE,MAAM,CAAC;gBAElB,SAAS,CAAC,EAAE,MAAM,CAAC;gBAEnB,QAAQ,CAAC,EAAE,MAAM,CAAC;aACnB;SACF;QAED,UAAiB,QAAQ;YACvB,MAAM,CAAC,EAAE,MAAM,CAAC;YAEhB,KAAK,CAAC,EAAE,MAAM,CAAC;SAChB;KACF;IAED,UAAiB,sBAAsB;QACrC;;;WAGG;QACH,IAAI,EAAE,aAAa,CAAC;QAEpB;;;WAGG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;QAEvB;;WAEG;QACH,WAAW,CAAC,EAAE,sBAAsB,CAAC,WAAW,CAAC;KAClD;IAED,UAAiB,sBAAsB,CAAC;QACtC;;WAEG;QACH,UAAiB,WAAW;YAC1B;;eAEG;YACH,OAAO,EAAE,MAAM,CAAC;YAEhB;;eAEG;YACH,IAAI,CAAC,EAAE,MAAM,CAAC;YAEd;;eAEG;YACH,KAAK,CAAC,EAAE,MAAM,CAAC;SAChB;KACF;IAED,UAAiB,mBAAmB;QAClC;;WAEG;QACH,MAAM,EAAE,MAAM,CAAC;QAEf;;WAEG;QACH,IAAI,EAAE,UAAU,CAAC;QAEjB;;;WAGG;QACH,aAAa,CAAC,EAAE,MAAM,CAAC;QAEvB;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;WAEG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;OAGG;IACH,MAAM,EAAE,iBAAiB,CAAC;CAC3B;AAED,MAAM,WAAW,iBAAiB;IAChC;;;;OAIG;IACH,CAAC,CAAC,EAAE,MAAM,CAAC;IAEX,MAAM,CAAC,EAAE,SAAS,GAAG,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;CAC1D;AAOD,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,iBAAiB,IAAI,iBAAiB,GAC5C,CAAC;IAEF,OAAO,EAAE,SAAS,IAAI,SAAS,EAAE,CAAC;IAElC,OAAO,EAAE,IAAI,IAAI,IAAI,EAAE,KAAK,UAAU,IAAI,UAAU,EAAE,KAAK,eAAe,IAAI,eAAe,EAAE,CAAC;IAEhG,OAAO,EACL,SAAS,IAAI,SAAS,EACtB,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,yBAAyB,IAAI,yBAAyB,GAC5D,CAAC;IAEF,OAAO,EACL,OAAO,IAAI,OAAO,EAClB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;CACH"}