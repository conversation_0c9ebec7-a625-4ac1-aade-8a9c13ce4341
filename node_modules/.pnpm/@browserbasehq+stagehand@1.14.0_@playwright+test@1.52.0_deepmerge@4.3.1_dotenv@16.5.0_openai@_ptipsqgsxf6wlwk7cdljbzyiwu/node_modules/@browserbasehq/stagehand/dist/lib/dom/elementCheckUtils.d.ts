export declare function isElementNode(node: Node): node is Element;
export declare function isTextNode(node: Node): node is Text;
export declare const isVisible: (element: Element) => boolean;
export declare const isTextVisible: (element: ChildNode) => boolean;
export declare function isTopElement(elem: ChildNode, rect: DOMRect): boolean;
export declare const isActive: (element: Element) => boolean;
export declare const isInteractiveElement: (element: Element) => boolean;
export declare const isLeafElement: (element: Element) => boolean;
