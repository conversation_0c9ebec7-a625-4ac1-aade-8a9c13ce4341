import { ASTVisitor, ValidationContext } from 'graphql';
/**
 * Returns a number indicating whether a reference string comes before, or after,
 * or is the same as the given string in natural sort order.
 *
 * See: https://en.wikipedia.org/wiki/Natural_sort_order
 *
 */
export declare function naturalCompare(aStr: string, bStr: string): number;
/**
 * Overlapping fields can be merged
 *
 * A selection set is only valid if all fields (including spreading any
 * fragments) either correspond to distinct response names or can be merged
 * without ambiguity.
 *
 * See https://spec.graphql.org/draft/#sec-Field-Selection-Merging
 */
export declare function OverlappingFieldsCanBeMergedRule(context: ValidationContext): ASTVisitor;
