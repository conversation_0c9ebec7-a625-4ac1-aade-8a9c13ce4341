"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/components/chat/Modal.tsx
var Modal_exports = {};
__export(Modal_exports, {
  CopilotModal: () => CopilotModal
});
module.exports = __toCommonJS(Modal_exports);
var import_react14 = __toESM(require("react"));

// src/components/chat/ChatContext.tsx
var import_react = __toESM(require("react"));

// src/components/chat/Icons.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var OpenIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("g", { transform: "translate(24, 0) scale(-1, 1)", children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        fillRule: "evenodd",
        d: "M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z",
        clipRule: "evenodd"
      }
    ) })
  }
);
var CloseIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M19.5 8.25l-7.5 7.5-7.5-7.5" })
  }
);
var HeaderCloseIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M6 18L18 6M6 6l12 12" })
  }
);
var SendIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M12 19V5m0 0l-7 7m7-7l7 7" })
  }
);
var MicrophoneIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"
      }
    )
  }
);
var StopIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z"
      }
    )
  }
);
var RegenerateIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
      }
    )
  }
);
var CopyIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
      }
    )
  }
);
var SmallSpinnerIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitSpinner", style: { width: "13px", height: "13px" } });
var SpinnerIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitSpinner", style: { width: "24px", height: "24px" } });
var ActivityIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsxs)("div", { style: { display: "flex", alignItems: "center", gap: "4px" }, children: [
  /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitActivityDot", style: { animationDelay: "0s" } }),
  /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitActivityDot", style: { animationDelay: "0.2s" } }),
  /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitActivityDot", style: { animationDelay: "0.4s" } })
] });
var ThumbsUpIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z"
      }
    )
  }
);
var ThumbsDownIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384"
      }
    )
  }
);
var DownloadIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
      "path",
      {
        strokeLinecap: "round",
        strokeLinejoin: "round",
        d: "M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"
      }
    )
  }
);
var UploadIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "24",
    height: "24",
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M12 4.5v15m7.5-7.5h-15" })
  }
);
var CheckIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "2",
    stroke: "currentColor",
    width: "16",
    height: "16",
    style: { minWidth: "16px", minHeight: "16px" },
    children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M4.5 12.75l6 6 9-13.5" })
  }
);

// src/components/chat/ChatContext.tsx
var import_jsx_runtime2 = require("react/jsx-runtime");
var ChatContext = import_react.default.createContext(void 0);
function useChatContext() {
  const context = import_react.default.useContext(ChatContext);
  if (context === void 0) {
    throw new Error(
      "Context not found. Did you forget to wrap your app in a <ChatContextProvider> component?"
    );
  }
  return context;
}
var ChatContextProvider = ({
  // temperature,
  // instructions,
  // maxFeedback,
  labels,
  icons,
  children,
  open,
  setOpen
}) => {
  const memoizedLabels = (0, import_react.useMemo)(
    () => __spreadValues(__spreadValues({}, {
      initial: "",
      title: "CopilotKit",
      placeholder: "Type a message...",
      error: "\u274C An error occurred. Please try again.",
      stopGenerating: "Stop generating",
      regenerateResponse: "Regenerate response",
      copyToClipboard: "Copy to clipboard",
      thumbsUp: "Thumbs up",
      thumbsDown: "Thumbs down",
      copied: "Copied!"
    }), labels),
    [labels]
  );
  const memoizedIcons = (0, import_react.useMemo)(
    () => __spreadValues(__spreadValues({}, {
      openIcon: OpenIcon,
      closeIcon: CloseIcon,
      headerCloseIcon: HeaderCloseIcon,
      sendIcon: SendIcon,
      activityIcon: ActivityIcon,
      spinnerIcon: SpinnerIcon,
      stopIcon: StopIcon,
      regenerateIcon: RegenerateIcon,
      pushToTalkIcon: MicrophoneIcon,
      copyIcon: CopyIcon,
      thumbsUpIcon: ThumbsUpIcon,
      thumbsDownIcon: ThumbsDownIcon,
      uploadIcon: UploadIcon
    }), icons),
    [icons]
  );
  const context = (0, import_react.useMemo)(
    () => ({
      labels: memoizedLabels,
      icons: memoizedIcons,
      open,
      setOpen
    }),
    [memoizedLabels, memoizedIcons, open, setOpen]
  );
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(ChatContext.Provider, { value: context, children });
};

// src/components/chat/Window.tsx
var import_react2 = __toESM(require("react"));
var import_react_core = require("@copilotkit/react-core");
var import_jsx_runtime3 = require("react/jsx-runtime");
var Window = ({
  children,
  clickOutsideToClose,
  shortcut,
  hitEscapeToClose
}) => {
  const windowRef = import_react2.default.useRef(null);
  const context = (0, import_react_core.useCopilotContext)();
  const { open, setOpen } = useChatContext();
  const handleClickOutside = (0, import_react2.useCallback)(
    (event) => {
      var _a;
      if (!clickOutsideToClose) {
        return;
      }
      const parentElement = (_a = windowRef.current) == null ? void 0 : _a.parentElement;
      let className = "";
      if (event.target instanceof HTMLElement) {
        className = event.target.className;
      }
      if (open && parentElement && !parentElement.contains(event.target) && // prevent closing the window when clicking on the debug menu
      !className.includes("copilotKitDebugMenu")) {
        setOpen(false);
      }
    },
    [clickOutsideToClose, open, setOpen]
  );
  const handleKeyDown = (0, import_react2.useCallback)(
    (event) => {
      var _a;
      const target = event.target;
      const isInput = target.tagName === "INPUT" || target.tagName === "SELECT" || target.tagName === "TEXTAREA" || target.isContentEditable;
      const isDescendantOfWrapper = (_a = windowRef.current) == null ? void 0 : _a.contains(target);
      if (open && event.key === "Escape" && (!isInput || isDescendantOfWrapper) && hitEscapeToClose) {
        setOpen(false);
      } else if (event.key === shortcut && (isMacOS() && event.metaKey || !isMacOS() && event.ctrlKey) && (!isInput || isDescendantOfWrapper)) {
        setOpen(!open);
      }
    },
    [hitEscapeToClose, shortcut, open, setOpen]
  );
  const adjustForMobile = (0, import_react2.useCallback)(() => {
    const copilotKitWindow = windowRef.current;
    const vv = window.visualViewport;
    if (!copilotKitWindow || !vv) {
      return;
    }
    if (window.innerWidth < 640 && open) {
      copilotKitWindow.style.height = `${vv.height}px`;
      copilotKitWindow.style.left = `${vv.offsetLeft}px`;
      copilotKitWindow.style.top = `${vv.offsetTop}px`;
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
      document.body.style.height = `${window.innerHeight}px`;
      document.body.style.overflow = "hidden";
      document.body.style.touchAction = "none";
      document.body.addEventListener("touchmove", preventScroll, {
        passive: false
      });
    } else {
      copilotKitWindow.style.height = "";
      copilotKitWindow.style.left = "";
      copilotKitWindow.style.top = "";
      document.body.style.position = "";
      document.body.style.height = "";
      document.body.style.width = "";
      document.body.style.overflow = "";
      document.body.style.top = "";
      document.body.style.touchAction = "";
      document.body.removeEventListener("touchmove", preventScroll);
    }
  }, [open]);
  (0, import_react2.useEffect)(() => {
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);
    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", adjustForMobile);
      adjustForMobile();
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener("resize", adjustForMobile);
      }
    };
  }, [adjustForMobile, handleClickOutside, handleKeyDown]);
  return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("div", { className: `copilotKitWindow ${open ? " open" : ""}`, ref: windowRef, children });
};
var preventScroll = (event) => {
  let targetElement = event.target;
  const hasParentWithClass = (element, className) => {
    while (element && element !== document.body) {
      if (element.classList.contains(className)) {
        return true;
      }
      element = element.parentElement;
    }
    return false;
  };
  if (!hasParentWithClass(targetElement, "copilotKitMessages")) {
    event.preventDefault();
  }
};
function isMacOS() {
  return /Mac|iMac|Macintosh/i.test(navigator.userAgent);
}

// src/components/chat/Button.tsx
var import_jsx_runtime4 = require("react/jsx-runtime");
var Button = ({}) => {
  const { open, setOpen, icons } = useChatContext();
  return /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { onClick: () => setOpen(!open), children: /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(
    "button",
    {
      className: `copilotKitButton ${open ? "open" : ""}`,
      "aria-label": open ? "Close Chat" : "Open Chat",
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { className: "copilotKitButtonIcon copilotKitButtonIconOpen", children: icons.openIcon }),
        /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { className: "copilotKitButtonIcon copilotKitButtonIconClose", children: icons.closeIcon })
      ]
    }
  ) });
};

// src/components/dev-console/utils.ts
var import_react_core2 = require("@copilotkit/react-core");
function shouldShowDevConsole(showDevConsole) {
  if (typeof showDevConsole === "boolean") {
    return showDevConsole;
  }
  return getHostname() === "localhost" || getHostname() === "127.0.0.1" || getHostname() === "0.0.0.0" || getHostname() === "::1";
}
function getHostname() {
  if (typeof window !== "undefined" && window.location) {
    return window.location.hostname;
  }
  return "";
}
function getPublishedCopilotKitVersion(current, forceCheck = false) {
  return __async(this, null, function* () {
    const LOCAL_STORAGE_KEY = "__copilotkit_version_check__";
    const serializedVersion = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (serializedVersion && !forceCheck) {
      try {
        const parsedVersion = JSON.parse(serializedVersion);
        const oneHour = 60 * 60 * 1e3;
        const now = (/* @__PURE__ */ new Date()).getTime();
        if (parsedVersion.current === current && now - new Date(parsedVersion.lastChecked).getTime() < oneHour) {
          return parsedVersion;
        }
      } catch (error) {
        console.error("Failed to parse CopilotKitVersion from localStorage", error);
      }
    }
    try {
      const response = yield fetch("https://api.cloud.copilotkit.ai/check-for-updates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          packages: [
            {
              packageName: "@copilotkit/shared",
              packageVersion: current
            }
          ]
        })
      });
      const data = yield response.json();
      const version = {
        current,
        lastChecked: (/* @__PURE__ */ new Date()).getTime(),
        latest: data.packages[0].latestVersion,
        severity: data.packages[0].severity,
        advisory: data.packages[0].advisory || null
      };
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(version));
      return version;
    } catch (error) {
      console.error("Failed to check for updates", error);
      throw error;
    }
  });
}
function logReadables(context) {
  console.log("%cCurrent Readables:", "font-size: 16px; font-weight: bold;");
  const readables = context.getContextString([], import_react_core2.defaultCopilotContextCategories).trim();
  if (readables.length === 0) {
    console.log("No readables found");
    return;
  }
  console.log(readables);
}
function logActions(context) {
  console.log("%cCurrent Actions:", "font-size: 16px; font-weight: bold;");
  if (Object.values(context.actions).length === 0) {
    console.log("No actions found");
    return;
  }
  for (const action of Object.values(context.actions)) {
    console.group(action.name);
    console.log("name", action.name);
    console.log("description", action.description);
    console.log("parameters", action.parameters);
    console.groupEnd();
  }
}
function logMessages(context) {
  console.log("%cCurrent Messages:", "font-size: 16px; font-weight: bold;");
  if (context.messages.length === 0) {
    console.log("No messages found");
    return;
  }
  const tableData = context.messages.map((message) => {
    if (message.isTextMessage()) {
      return {
        id: message.id,
        type: "TextMessage",
        role: message.role,
        name: void 0,
        scope: void 0,
        content: message.content
      };
    } else if (message.isActionExecutionMessage()) {
      return {
        id: message.id,
        type: "ActionExecutionMessage",
        role: void 0,
        name: message.name,
        scope: message.parentMessageId,
        content: message.arguments
      };
    } else if (message.isResultMessage()) {
      return {
        id: message.id,
        type: "ResultMessage",
        role: void 0,
        name: message.actionName,
        scope: message.actionExecutionId,
        content: message.result
      };
    } else if (message.isAgentStateMessage()) {
      return {
        id: message.id,
        type: `AgentStateMessage (running: ${message.running})`,
        role: message.role,
        name: void 0,
        scope: message.threadId,
        content: message.state
      };
    }
  });
  console.table(tableData);
}

// src/components/dev-console/console.tsx
var import_react_core3 = require("@copilotkit/react-core");
var import_react4 = require("react");

// src/components/dev-console/icons.tsx
var import_jsx_runtime5 = require("react/jsx-runtime");
var ExclamationMarkTriangleIcon = /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
  "svg",
  {
    width: "13.3967723px",
    height: "12px",
    viewBox: "0 0 13.3967723 12",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "exclamation-triangle", fill: "#CD2121", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
      "path",
      {
        d: "M5.39935802,0.75 C5.97670802,-0.25 7.42007802,-0.25 7.99742802,0.75 L13.193588,9.75 C13.770888,10.75 13.049288,12 11.894588,12 L1.50223802,12 C0.34753802,12 -0.37414898,10.75 0.20319802,9.75 L5.39935802,0.75 Z M6.69838802,2.5 C7.11260802,2.5 7.44838802,2.83579 7.44838802,3.25 L7.44838802,6.25 C7.44838802,6.66421 7.11260802,7 6.69838802,7 C6.28417802,7 5.94838802,6.66421 5.94838802,6.25 L5.94838802,3.25 C5.94838802,2.83579 6.28417802,2.5 6.69838802,2.5 Z M6.69838802,10.5 C7.25067802,10.5 7.69838802,10.0523 7.69838802,9.5 C7.69838802,8.9477 7.25067802,8.5 6.69838802,8.5 C6.14610802,8.5 5.69838802,8.9477 5.69838802,9.5 C5.69838802,10.0523 6.14610802,10.5 6.69838802,10.5 Z",
        id: "Shape"
      }
    ) }) })
  }
);
var ExclamationMarkIcon = /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
  "svg",
  {
    width: "14px",
    height: "14px",
    viewBox: "0 0 14 14",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "exclamation-circle", fill: "#EC662C", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
      "path",
      {
        d: "M7,14 C10.866,14 14,10.866 14,7 C14,3.13401 10.866,0 7,0 C3.13401,0 0,3.13401 0,7 C0,10.866 3.13401,14 7,14 Z M7,3 C7.41421,3 7.75,3.33579 7.75,3.75 L7.75,6.75 C7.75,7.16421 7.41421,7.5 7,7.5 C6.58579,7.5 6.25,7.16421 6.25,6.75 L6.25,3.75 C6.25,3.33579 6.58579,3 7,3 Z M7,11 C7.55228,11 8,10.5523 8,10 C8,9.4477 7.55228,9 7,9 C6.44772,9 6,9.4477 6,10 C6,10.5523 6.44772,11 7,11 Z",
        id: "Shape"
      }
    ) }) })
  }
);
var ChevronDownIcon = /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
  "svg",
  {
    width: "7px",
    height: "4px",
    viewBox: "0 0 7 4",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    fill: "currentColor",
    children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Group", fill: "currentColor", fillRule: "nonzero", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
      "path",
      {
        d: "M3.71690723,3.90271086 C3.59268176,4.03242971 3.39143629,4.03242971 3.26721082,3.90271086 L0.0853966595,0.57605615 C-0.0314221035,0.444981627 -0.0279751448,0.240725043 0.0931934622,0.114040675 C0.214362069,-0.0126436935 0.409725445,-0.0162475626 0.535093061,0.105888951 L3.49205902,3.19746006 L6.44902499,0.105888951 C6.52834574,0.0168884389 6.64780588,-0.0197473458 6.7605411,0.0103538404 C6.87327633,0.0404550266 6.96130636,0.132492308 6.99009696,0.250359396 C7.01888756,0.368226483 6.98384687,0.493124608 6.89872139,0.57605615 L3.71690723,3.90271086 Z",
        id: "Path"
      }
    ) }) })
  }
);
var CheckIcon2 = /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
  "svg",
  {
    width: "14px",
    height: "14px",
    viewBox: "0 0 14 14",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Group-2", transform: "translate(-118, 0)", fill: "#1BC030", fillRule: "nonzero", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("g", { id: "Group", transform: "translate(118, 0)", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
      "path",
      {
        d: "M0,7 C0,3.13384615 3.13384615,0 7,0 C10.8661538,0 14,3.13384615 14,7 C14,10.8661538 10.8661538,14 7,14 C3.13384615,14 0,10.8661538 0,7 Z M9.59179487,5.69764103 C9.70905818,5.54139023 9.73249341,5.33388318 9.65303227,5.15541491 C9.57357113,4.97694665 9.40367989,4.85551619 9.20909814,4.83811118 C9.01451638,4.82070616 8.82577109,4.91005717 8.71589744,5.07158974 L6.39261538,8.32389744 L5.22666667,7.15794872 C5.01450582,6.96025518 4.68389046,6.9660885 4.47883563,7.17114332 C4.27378081,7.37619815 4.26794748,7.70681351 4.46564103,7.91897436 L6.08102564,9.53435897 C6.19289944,9.64614839 6.3482622,9.70310251 6.50588106,9.69010587 C6.66349993,9.67710922 6.80743532,9.59547613 6.89948718,9.46687179 L9.59179487,5.69764103 L9.59179487,5.69764103 Z",
        id: "Shape"
      }
    ) }) }) })
  }
);

// src/components/dev-console/console.tsx
var import_react5 = require("@headlessui/react");
var import_shared = require("@copilotkit/shared");

// src/components/help-modal/modal.tsx
var import_react3 = require("react");

// src/components/help-modal/icons.tsx
var import_jsx_runtime6 = require("react/jsx-runtime");
var CloseIcon2 = () => /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "20",
    height: "20",
    children: /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M6 18L18 6M6 6l12 12" })
  }
);

// src/components/help-modal/modal.tsx
var import_jsx_runtime7 = require("react/jsx-runtime");
function CopilotKitHelpModal() {
  const [showHelpModal, setShowHelpModal] = (0, import_react3.useState)(false);
  const buttonRef = (0, import_react3.useRef)(null);
  const popoverRef = (0, import_react3.useRef)(null);
  (0, import_react3.useEffect)(() => {
    const handleClickOutside = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target) && buttonRef.current && !buttonRef.current.contains(event.target)) {
        setShowHelpModal(false);
      }
    };
    if (showHelpModal) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showHelpModal]);
  const HelpButton = () => /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
    "button",
    {
      ref: buttonRef,
      onClick: () => setShowHelpModal(!showHelpModal),
      className: "copilotKitDebugMenuTriggerButton relative",
      "aria-label": "Open Help",
      children: "Help"
    }
  );
  return /* @__PURE__ */ (0, import_jsx_runtime7.jsxs)("div", { className: "relative", children: [
    /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(HelpButton, {}),
    showHelpModal && /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
      "div",
      {
        ref: popoverRef,
        className: "absolute mt-2 z-50",
        style: {
          top: "100%",
          right: "-120px",
          width: "380px"
        },
        children: /* @__PURE__ */ (0, import_jsx_runtime7.jsxs)("div", { className: "copilotKitHelpModal rounded-lg shadow-xl w-full p-4 flex-col relative", children: [
          /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
            "button",
            {
              className: "copilotKitHelpModalCloseButton absolute text-gray-400 hover:text-gray-600 focus:outline-none",
              style: { top: "10px", right: "10px" },
              onClick: () => setShowHelpModal(false),
              "aria-label": "Close",
              children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(CloseIcon2, {})
            }
          ),
          /* @__PURE__ */ (0, import_jsx_runtime7.jsx)("div", { className: "w-full flex mb-6 justify-center", children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)("h2", { className: "text-2xl font-bold", children: "Help Options" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime7.jsxs)("div", { className: "space-y-4 mb-4", children: [
            /* @__PURE__ */ (0, import_jsx_runtime7.jsx)("div", { className: "copilotKitHelpItemButton", children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
              "a",
              {
                href: "https://docs.copilotkit.ai/coagents/troubleshooting/common-issues",
                target: "_blank",
                rel: "noopener noreferrer",
                children: "Visit the Troubleshooting and FAQ section in the docs"
              }
            ) }),
            /* @__PURE__ */ (0, import_jsx_runtime7.jsx)("div", { className: "copilotKitHelpItemButton", children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
              "a",
              {
                href: "https://go.copilotkit.ai/dev-console-support-discord",
                target: "_blank",
                rel: "noopener noreferrer",
                children: "Go to Discord Support Channel (Community Support)"
              }
            ) }),
            /* @__PURE__ */ (0, import_jsx_runtime7.jsx)("div", { className: "copilotKitHelpItemButton", children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
              "a",
              {
                href: "https://go.copilotkit.ai/dev-console-support-slack",
                target: "_blank",
                rel: "noopener noreferrer",
                children: "Apply for Priority Direct Slack Support"
              }
            ) })
          ] })
        ] })
      }
    )
  ] });
}

// src/components/dev-console/console.tsx
var import_jsx_runtime8 = require("react/jsx-runtime");
function CopilotDevConsole() {
  const currentVersion = import_shared.COPILOTKIT_VERSION;
  const context = (0, import_react_core3.useCopilotContext)();
  const [showDevConsole, setShowDevConsole] = (0, import_react4.useState)(false);
  (0, import_react4.useEffect)(() => {
    setShowDevConsole(shouldShowDevConsole(context.showDevConsole));
  }, [context.showDevConsole]);
  const dontRunTwiceInDevMode = (0, import_react4.useRef)(false);
  const [versionStatus, setVersionStatus] = (0, import_react4.useState)("unknown");
  const [latestVersion, setLatestVersion] = (0, import_react4.useState)("");
  const consoleRef = (0, import_react4.useRef)(null);
  const [debugButtonMode, setDebugButtonMode] = (0, import_react4.useState)("full");
  const checkForUpdates = (force = false) => {
    setVersionStatus("checking");
    getPublishedCopilotKitVersion(currentVersion, force).then((v) => {
      setLatestVersion(v.latest);
      let versionOk = false;
      if (v.current === v.latest) {
        versionOk = true;
      } else if (/[a-zA-Z]/.test(v.current)) {
        versionOk = true;
      }
      if (versionOk) {
        setVersionStatus("latest");
      } else if (v.severity !== "low") {
        setVersionStatus("outdated");
      } else {
        setVersionStatus("update-available");
      }
    }).catch((e) => {
      console.error(e);
      setVersionStatus("unknown");
    });
  };
  (0, import_react4.useEffect)(() => {
    if (dontRunTwiceInDevMode.current === true) {
      return;
    }
    dontRunTwiceInDevMode.current = true;
    checkForUpdates();
  }, []);
  if (!showDevConsole) {
    return null;
  }
  return /* @__PURE__ */ (0, import_jsx_runtime8.jsxs)(
    "div",
    {
      ref: consoleRef,
      className: "copilotKitDevConsole " + (versionStatus === "update-available" ? "copilotKitDevConsoleUpgrade" : "") + (versionStatus === "outdated" ? "copilotKitDevConsoleWarnOutdated" : ""),
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(
          VersionInfo,
          {
            showDevConsole: context.showDevConsole,
            versionStatus,
            currentVersion,
            latestVersion
          }
        ),
        /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(CopilotKitHelpModal, {}),
        /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(
          DebugMenuButton,
          {
            setShowDevConsole,
            checkForUpdates,
            mode: debugButtonMode
          }
        )
      ]
    }
  );
}
function VersionInfo({
  showDevConsole,
  versionStatus,
  currentVersion,
  latestVersion
}) {
  const [copyStatus, setCopyStatus] = (0, import_react4.useState)("");
  let versionLabel = "";
  let versionIcon = "";
  let currentVersionLabel = currentVersion;
  if (versionStatus === "latest") {
    versionLabel = "latest";
    versionIcon = CheckIcon2;
  } else if (versionStatus === "checking") {
    versionLabel = "checking";
    versionIcon = SmallSpinnerIcon;
  } else if (versionStatus === "update-available") {
    versionLabel = "update available";
    versionIcon = ExclamationMarkIcon;
    currentVersionLabel = `${currentVersion} \u2192 ${latestVersion}`;
  } else if (versionStatus === "outdated") {
    versionLabel = "outdated";
    versionIcon = ExclamationMarkTriangleIcon;
    currentVersionLabel = `${currentVersion} \u2192 ${latestVersion}`;
  }
  let asideLabel = "";
  if (showDevConsole === "auto") {
    asideLabel = "(localhost only)";
  } else if (showDevConsole === true) {
    asideLabel = "(always on)";
  }
  const installCommand = [
    `npm install`,
    `@copilotkit/react-core@${latestVersion}`,
    `@copilotkit/react-ui@${latestVersion}`,
    `@copilotkit/react-textarea@${latestVersion}`,
    `&& npm install @copilotkit/runtime@${latestVersion}`
  ].join(" ");
  const handleCopyClick = () => {
    navigator.clipboard.writeText(installCommand.trim()).then(() => {
      setCopyStatus("Command copied to clipboard!");
      setTimeout(() => setCopyStatus(""), 1e3);
    });
  };
  if (versionStatus === "update-available" || versionStatus === "outdated") {
    return /* @__PURE__ */ (0, import_jsx_runtime8.jsxs)("div", { className: "copilotKitVersionInfo", children: [
      /* @__PURE__ */ (0, import_jsx_runtime8.jsxs)("p", { children: [
        currentVersionLabel,
        " ",
        versionIcon
      ] }),
      /* @__PURE__ */ (0, import_jsx_runtime8.jsx)("button", { onClick: handleCopyClick, children: copyStatus || installCommand })
    ] });
  }
  return null;
}
function DebugMenuButton({
  setShowDevConsole,
  checkForUpdates,
  mode
}) {
  const context = (0, import_react_core3.useCopilotContext)();
  const messagesContext = (0, import_react_core3.useCopilotMessagesContext)();
  return /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(import_jsx_runtime8.Fragment, { children: /* @__PURE__ */ (0, import_jsx_runtime8.jsxs)(import_react5.Menu, { children: [
    /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(
      import_react5.MenuButton,
      {
        className: `copilotKitDebugMenuTriggerButton ${mode === "compact" ? "compact" : ""}`,
        children: mode == "compact" ? "Debug" : /* @__PURE__ */ (0, import_jsx_runtime8.jsxs)(import_jsx_runtime8.Fragment, { children: [
          "Debug ",
          ChevronDownIcon
        ] })
      }
    ),
    /* @__PURE__ */ (0, import_jsx_runtime8.jsxs)(
      import_react5.MenuItems,
      {
        transition: true,
        anchor: "bottom end",
        className: "copilotKitDebugMenu",
        style: { zIndex: 40 },
        children: [
          /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(import_react5.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime8.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => logReadables(context), children: "Log Readables" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(import_react5.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime8.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => logActions(context), children: "Log Actions" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(import_react5.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(
            "button",
            {
              className: "copilotKitDebugMenuItem",
              onClick: () => logMessages(messagesContext),
              children: "Log Messages"
            }
          ) }),
          /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(import_react5.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime8.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => checkForUpdates(true), children: "Check for Updates" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime8.jsx)("hr", {}),
          /* @__PURE__ */ (0, import_jsx_runtime8.jsx)(import_react5.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime8.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => setShowDevConsole(false), children: "Hide Dev Console" }) })
        ]
      }
    )
  ] }) });
}

// src/components/chat/Header.tsx
var import_jsx_runtime9 = require("react/jsx-runtime");
var Header = ({}) => {
  const { setOpen, icons, labels } = useChatContext();
  return /* @__PURE__ */ (0, import_jsx_runtime9.jsxs)("div", { className: "copilotKitHeader", children: [
    /* @__PURE__ */ (0, import_jsx_runtime9.jsx)("div", { children: labels.title }),
    /* @__PURE__ */ (0, import_jsx_runtime9.jsxs)("div", { className: "copilotKitHeaderControls", children: [
      /* @__PURE__ */ (0, import_jsx_runtime9.jsx)(CopilotDevConsole, {}),
      /* @__PURE__ */ (0, import_jsx_runtime9.jsx)(
        "button",
        {
          onClick: () => setOpen(false),
          "aria-label": "Close",
          className: "copilotKitHeaderCloseButton",
          children: icons.headerCloseIcon
        }
      )
    ] })
  ] });
};

// src/components/chat/Messages.tsx
var import_react6 = require("react");
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_react_core4 = require("@copilotkit/react-core");
var import_jsx_runtime10 = require("react/jsx-runtime");
var Messages = ({
  messages,
  inProgress,
  children,
  RenderTextMessage: RenderTextMessage2,
  RenderActionExecutionMessage: RenderActionExecutionMessage2,
  RenderAgentStateMessage: RenderAgentStateMessage2,
  RenderResultMessage: RenderResultMessage2,
  RenderImageMessage: RenderImageMessage2,
  AssistantMessage: AssistantMessage2,
  UserMessage: UserMessage2,
  onRegenerate,
  onCopy,
  onThumbsUp,
  onThumbsDown,
  markdownTagRenderers
}) => {
  const context = useChatContext();
  const initialMessages = (0, import_react6.useMemo)(
    () => makeInitialMessages(context.labels.initial),
    [context.labels.initial]
  );
  messages = [...initialMessages, ...messages];
  const actionResults = {};
  for (let i = 0; i < messages.length; i++) {
    if (messages[i].isActionExecutionMessage()) {
      const id = messages[i].id;
      const resultMessage = messages.find(
        (message) => message.isResultMessage() && message.actionExecutionId === id
      );
      if (resultMessage) {
        actionResults[id] = import_runtime_client_gql.ResultMessage.decodeResult(resultMessage.result || "");
      }
    }
  }
  const { messagesContainerRef, messagesEndRef } = useScrollToBottom(messages);
  const interrupt = (0, import_react_core4.useLangGraphInterruptRender)();
  return /* @__PURE__ */ (0, import_jsx_runtime10.jsxs)("div", { className: "copilotKitMessages", ref: messagesContainerRef, children: [
    /* @__PURE__ */ (0, import_jsx_runtime10.jsxs)("div", { className: "copilotKitMessagesContainer", children: [
      messages.map((message, index) => {
        const isCurrentMessage = index === messages.length - 1;
        if (message.isTextMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime10.jsx)(
            RenderTextMessage2,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage: AssistantMessage2,
              UserMessage: UserMessage2,
              onRegenerate,
              onCopy,
              onThumbsUp,
              onThumbsDown,
              markdownTagRenderers
            },
            index
          );
        } else if (message.isActionExecutionMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime10.jsx)(
            RenderActionExecutionMessage2,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              actionResult: actionResults[message.id],
              AssistantMessage: AssistantMessage2,
              UserMessage: UserMessage2
            },
            index
          );
        } else if (message.isAgentStateMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime10.jsx)(
            RenderAgentStateMessage2,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage: AssistantMessage2,
              UserMessage: UserMessage2
            },
            index
          );
        } else if (message.isResultMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime10.jsx)(
            RenderResultMessage2,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage: AssistantMessage2,
              UserMessage: UserMessage2
            },
            index
          );
        } else if (message.isImageMessage && message.isImageMessage()) {
          return /* @__PURE__ */ (0, import_jsx_runtime10.jsx)(
            RenderImageMessage2,
            {
              message,
              inProgress,
              index,
              isCurrentMessage,
              AssistantMessage: AssistantMessage2,
              UserMessage: UserMessage2,
              onRegenerate,
              onCopy,
              onThumbsUp,
              onThumbsDown
            },
            index
          );
        }
      }),
      interrupt
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime10.jsx)("footer", { className: "copilotKitMessagesFooter", ref: messagesEndRef, children })
  ] });
};
function makeInitialMessages(initial) {
  let initialArray = [];
  if (initial) {
    if (Array.isArray(initial)) {
      initialArray.push(...initial);
    } else {
      initialArray.push(initial);
    }
  }
  return initialArray.map(
    (message) => new import_runtime_client_gql.TextMessage({
      role: import_runtime_client_gql.Role.Assistant,
      content: message
    })
  );
}
function useScrollToBottom(messages) {
  const messagesEndRef = (0, import_react6.useRef)(null);
  const messagesContainerRef = (0, import_react6.useRef)(null);
  const isProgrammaticScrollRef = (0, import_react6.useRef)(false);
  const isUserScrollUpRef = (0, import_react6.useRef)(false);
  const scrollToBottom = () => {
    if (messagesContainerRef.current && messagesEndRef.current) {
      isProgrammaticScrollRef.current = true;
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  };
  const handleScroll = () => {
    if (isProgrammaticScrollRef.current) {
      isProgrammaticScrollRef.current = false;
      return;
    }
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      isUserScrollUpRef.current = scrollTop + clientHeight < scrollHeight;
    }
  };
  (0, import_react6.useEffect)(() => {
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (container) {
        container.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);
  (0, import_react6.useEffect)(() => {
    const container = messagesContainerRef.current;
    if (!container) {
      return;
    }
    const mutationObserver = new MutationObserver(() => {
      if (!isUserScrollUpRef.current) {
        scrollToBottom();
      }
    });
    mutationObserver.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    });
    return () => {
      mutationObserver.disconnect();
    };
  }, []);
  (0, import_react6.useEffect)(() => {
    isUserScrollUpRef.current = false;
    scrollToBottom();
  }, [messages.filter((m) => m.isTextMessage() && m.role === import_runtime_client_gql.Role.User).length]);
  return { messagesEndRef, messagesContainerRef };
}

// src/components/chat/Input.tsx
var import_react9 = require("react");

// src/components/chat/Textarea.tsx
var import_react7 = require("react");
var import_jsx_runtime11 = require("react/jsx-runtime");
var AutoResizingTextarea = (0, import_react7.forwardRef)(
  ({ maxRows = 1, placeholder, value, onChange, onKeyDown, autoFocus }, ref) => {
    const internalTextareaRef = (0, import_react7.useRef)(null);
    const [maxHeight, setMaxHeight] = (0, import_react7.useState)(0);
    (0, import_react7.useImperativeHandle)(ref, () => internalTextareaRef.current);
    (0, import_react7.useEffect)(() => {
      const calculateMaxHeight = () => {
        const textarea = internalTextareaRef.current;
        if (textarea) {
          textarea.style.height = "auto";
          const singleRowHeight = textarea.scrollHeight;
          setMaxHeight(singleRowHeight * maxRows);
          if (autoFocus) {
            textarea.focus();
          }
        }
      };
      calculateMaxHeight();
    }, [maxRows]);
    (0, import_react7.useEffect)(() => {
      const textarea = internalTextareaRef.current;
      if (textarea) {
        textarea.style.height = "auto";
        textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`;
      }
    }, [value, maxHeight]);
    return /* @__PURE__ */ (0, import_jsx_runtime11.jsx)(
      "textarea",
      {
        ref: internalTextareaRef,
        value,
        onChange,
        onKeyDown,
        placeholder,
        style: {
          overflow: "auto",
          resize: "none",
          maxHeight: `${maxHeight}px`
        },
        rows: 1
      }
    );
  }
);
var Textarea_default = AutoResizingTextarea;

// src/hooks/use-push-to-talk.tsx
var import_react_core5 = require("@copilotkit/react-core");
var import_react8 = require("react");
var startRecording = (mediaStreamRef, mediaRecorderRef, audioContextRef, recordedChunks, onStop) => __async(void 0, null, function* () {
  if (!mediaStreamRef.current || !audioContextRef.current) {
    mediaStreamRef.current = yield navigator.mediaDevices.getUserMedia({ audio: true });
    audioContextRef.current = new window.AudioContext();
    yield audioContextRef.current.resume();
  }
  mediaRecorderRef.current = new MediaRecorder(mediaStreamRef.current);
  mediaRecorderRef.current.start(1e3);
  mediaRecorderRef.current.ondataavailable = (event) => {
    recordedChunks.push(event.data);
  };
  mediaRecorderRef.current.onstop = onStop;
});
var stopRecording = (mediaRecorderRef) => {
  if (mediaRecorderRef.current && mediaRecorderRef.current.state !== "inactive") {
    mediaRecorderRef.current.stop();
  }
};
var transcribeAudio = (recordedChunks, transcribeAudioUrl) => __async(void 0, null, function* () {
  const completeBlob = new Blob(recordedChunks, { type: "audio/mp4" });
  const formData = new FormData();
  formData.append("file", completeBlob, "recording.mp4");
  const response = yield fetch(transcribeAudioUrl, {
    method: "POST",
    body: formData
  });
  if (!response.ok) {
    throw new Error(`Error: ${response.statusText}`);
  }
  const transcription = yield response.json();
  return transcription.text;
});
var playAudioResponse = (text, textToSpeechUrl, audioContext) => {
  const encodedText = encodeURIComponent(text);
  const url = `${textToSpeechUrl}?text=${encodedText}`;
  fetch(url).then((response) => response.arrayBuffer()).then((arrayBuffer) => audioContext.decodeAudioData(arrayBuffer)).then((audioBuffer) => {
    const source = audioContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(audioContext.destination);
    source.start(0);
  }).catch((error) => {
    console.error("Error with decoding audio data", error);
  });
};
var usePushToTalk = ({
  sendFunction,
  inProgress
}) => {
  const [pushToTalkState, setPushToTalkState] = (0, import_react8.useState)("idle");
  const mediaStreamRef = (0, import_react8.useRef)(null);
  const audioContextRef = (0, import_react8.useRef)(null);
  const mediaRecorderRef = (0, import_react8.useRef)(null);
  const recordedChunks = (0, import_react8.useRef)([]);
  const generalContext = (0, import_react_core5.useCopilotContext)();
  const messagesContext = (0, import_react_core5.useCopilotMessagesContext)();
  const context = __spreadValues(__spreadValues({}, generalContext), messagesContext);
  const [startReadingFromMessageId, setStartReadingFromMessageId] = (0, import_react8.useState)(null);
  (0, import_react8.useEffect)(() => {
    if (pushToTalkState === "recording") {
      startRecording(
        mediaStreamRef,
        mediaRecorderRef,
        audioContextRef,
        recordedChunks.current,
        () => {
          setPushToTalkState("transcribing");
        }
      );
    } else {
      stopRecording(mediaRecorderRef);
      if (pushToTalkState === "transcribing") {
        transcribeAudio(recordedChunks.current, context.copilotApiConfig.transcribeAudioUrl).then(
          (transcription) => __async(void 0, null, function* () {
            recordedChunks.current = [];
            setPushToTalkState("idle");
            const message = yield sendFunction(transcription);
            setStartReadingFromMessageId(message.id);
          })
        );
      }
    }
    return () => {
      stopRecording(mediaRecorderRef);
    };
  }, [pushToTalkState]);
  (0, import_react8.useEffect)(() => {
    if (inProgress === false && startReadingFromMessageId) {
      const lastMessageIndex = context.messages.findIndex(
        (message) => message.id === startReadingFromMessageId
      );
      const messagesAfterLast = context.messages.slice(lastMessageIndex + 1).filter(
        (message) => message.isTextMessage() && message.role === "assistant"
      );
      const text = messagesAfterLast.map((message) => message.content).join("\n");
      playAudioResponse(text, context.copilotApiConfig.textToSpeechUrl, audioContextRef.current);
      setStartReadingFromMessageId(null);
    }
  }, [startReadingFromMessageId, inProgress]);
  return { pushToTalkState, setPushToTalkState };
};

// src/components/chat/Input.tsx
var import_react_core6 = require("@copilotkit/react-core");

// src/hooks/use-dark-mode.ts
var useDarkMode = () => {
  if (typeof window === "undefined")
    return false;
  return document.documentElement.classList.contains("dark") || document.body.classList.contains("dark") || document.documentElement.getAttribute("data-theme") === "dark" || document.body.getAttribute("data-theme") === "dark" || window.matchMedia("(prefers-color-scheme: dark)").matches;
};

// src/components/chat/PoweredByTag.tsx
var import_jsx_runtime12 = require("react/jsx-runtime");
function PoweredByTag({ showPoweredBy = true }) {
  const isDark = useDarkMode();
  if (!showPoweredBy) {
    return null;
  }
  const poweredByStyle = {
    visibility: "visible",
    display: "block",
    position: "static",
    textAlign: "center",
    fontSize: "12px",
    padding: "3px 0",
    color: isDark ? "rgb(69, 69, 69)" : "rgb(214, 214, 214)"
  };
  return /* @__PURE__ */ (0, import_jsx_runtime12.jsx)("div", { children: /* @__PURE__ */ (0, import_jsx_runtime12.jsx)("p", { className: "poweredBy", style: poweredByStyle, children: "Powered by CopilotKit" }) });
}

// src/components/chat/Input.tsx
var import_jsx_runtime13 = require("react/jsx-runtime");
var MAX_NEWLINES = 6;
var Input = ({ inProgress, onSend, isVisible = false, onStop, onUpload }) => {
  var _a;
  const context = useChatContext();
  const copilotContext = (0, import_react_core6.useCopilotContext)();
  const showPoweredBy = !((_a = copilotContext.copilotApiConfig) == null ? void 0 : _a.publicApiKey);
  const pushToTalkConfigured = copilotContext.copilotApiConfig.textToSpeechUrl !== void 0 && copilotContext.copilotApiConfig.transcribeAudioUrl !== void 0;
  const textareaRef = (0, import_react9.useRef)(null);
  const handleDivClick = (event) => {
    var _a2;
    const target = event.target;
    if (target.closest("button"))
      return;
    if (target.tagName === "TEXTAREA")
      return;
    (_a2 = textareaRef.current) == null ? void 0 : _a2.focus();
  };
  const [text, setText] = (0, import_react9.useState)("");
  const send = () => {
    var _a2;
    if (inProgress)
      return;
    onSend(text);
    setText("");
    (_a2 = textareaRef.current) == null ? void 0 : _a2.focus();
  };
  const { pushToTalkState, setPushToTalkState } = usePushToTalk({
    sendFunction: onSend,
    inProgress
  });
  const isInProgress = inProgress || pushToTalkState === "transcribing";
  const buttonIcon = isInProgress ? context.icons.stopIcon : context.icons.sendIcon;
  const showPushToTalk = pushToTalkConfigured && (pushToTalkState === "idle" || pushToTalkState === "recording") && !inProgress;
  const canSend = () => {
    var _a2;
    const interruptEvent = (_a2 = copilotContext.langGraphInterruptAction) == null ? void 0 : _a2.event;
    const interruptInProgress = (interruptEvent == null ? void 0 : interruptEvent.name) === "LangGraphInterruptEvent" && !(interruptEvent == null ? void 0 : interruptEvent.response);
    return (isInProgress || !isInProgress && text.trim().length > 0) && pushToTalkState === "idle" && !interruptInProgress;
  };
  const sendDisabled = !canSend();
  return /* @__PURE__ */ (0, import_jsx_runtime13.jsxs)("div", { className: `copilotKitInputContainer ${showPoweredBy ? "poweredByContainer" : ""}`, children: [
    /* @__PURE__ */ (0, import_jsx_runtime13.jsxs)("div", { className: "copilotKitInput", onClick: handleDivClick, children: [
      /* @__PURE__ */ (0, import_jsx_runtime13.jsx)(
        Textarea_default,
        {
          ref: textareaRef,
          placeholder: context.labels.placeholder,
          autoFocus: false,
          maxRows: MAX_NEWLINES,
          value: text,
          onChange: (event) => setText(event.target.value),
          onKeyDown: (event) => {
            if (event.key === "Enter" && !event.shiftKey) {
              event.preventDefault();
              if (canSend()) {
                send();
              }
            }
          }
        }
      ),
      /* @__PURE__ */ (0, import_jsx_runtime13.jsxs)("div", { className: "copilotKitInputControls", children: [
        onUpload && /* @__PURE__ */ (0, import_jsx_runtime13.jsx)("button", { onClick: onUpload, className: "copilotKitInputControlButton", children: context.icons.uploadIcon }),
        /* @__PURE__ */ (0, import_jsx_runtime13.jsx)("div", { style: { flexGrow: 1 } }),
        showPushToTalk && /* @__PURE__ */ (0, import_jsx_runtime13.jsx)(
          "button",
          {
            onClick: () => setPushToTalkState(pushToTalkState === "idle" ? "recording" : "transcribing"),
            className: pushToTalkState === "recording" ? "copilotKitInputControlButton copilotKitPushToTalkRecording" : "copilotKitInputControlButton",
            children: context.icons.pushToTalkIcon
          }
        ),
        /* @__PURE__ */ (0, import_jsx_runtime13.jsx)(
          "button",
          {
            disabled: sendDisabled,
            onClick: isInProgress ? onStop : send,
            "data-copilotkit-in-progress": inProgress,
            "data-test-id": inProgress ? "copilot-chat-request-in-progress" : "copilot-chat-ready",
            className: "copilotKitInputControlButton",
            children: buttonIcon
          }
        )
      ] })
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime13.jsx)(PoweredByTag, { showPoweredBy })
  ] });
};

// src/components/chat/messages/UserMessage.tsx
var import_jsx_runtime14 = require("react/jsx-runtime");
var UserMessage = (props) => {
  return /* @__PURE__ */ (0, import_jsx_runtime14.jsx)("div", { className: "copilotKitMessage copilotKitUserMessage", children: props.subComponent || props.message });
};

// src/components/chat/Markdown.tsx
var import_react11 = require("react");
var import_react_markdown = __toESM(require("react-markdown"));

// src/components/chat/CodeBlock.tsx
var import_react10 = require("react");
var import_react_syntax_highlighter = require("react-syntax-highlighter");

// src/hooks/use-copy-to-clipboard.tsx
var React7 = __toESM(require("react"));
function useCopyToClipboard({ timeout = 2e3 }) {
  const [isCopied, setIsCopied] = React7.useState(false);
  const copyToClipboard = (value) => {
    var _a;
    if (typeof window === "undefined" || !((_a = navigator.clipboard) == null ? void 0 : _a.writeText)) {
      return;
    }
    if (!value) {
      return;
    }
    navigator.clipboard.writeText(value).then(() => {
      setIsCopied(true);
      setTimeout(() => {
        setIsCopied(false);
      }, timeout);
    });
  };
  return { isCopied, copyToClipboard };
}

// src/components/chat/CodeBlock.tsx
var import_jsx_runtime15 = require("react/jsx-runtime");
var programmingLanguages = {
  javascript: ".js",
  python: ".py",
  java: ".java",
  c: ".c",
  cpp: ".cpp",
  "c++": ".cpp",
  "c#": ".cs",
  ruby: ".rb",
  php: ".php",
  swift: ".swift",
  "objective-c": ".m",
  kotlin: ".kt",
  typescript: ".ts",
  go: ".go",
  perl: ".pl",
  rust: ".rs",
  scala: ".scala",
  haskell: ".hs",
  lua: ".lua",
  shell: ".sh",
  sql: ".sql",
  html: ".html",
  css: ".css"
  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component
};
var generateRandomString = (length, lowercase = false) => {
  const chars = "ABCDEFGHJKLMNPQRSTUVWXY3456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return lowercase ? result.toLowerCase() : result;
};
var CodeBlock = (0, import_react10.memo)(({ language, value }) => {
  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 2e3 });
  const downloadAsFile = () => {
    if (typeof window === "undefined") {
      return;
    }
    const fileExtension = programmingLanguages[language] || ".file";
    const suggestedFileName = `file-${generateRandomString(3, true)}${fileExtension}`;
    const fileName = window.prompt("Enter file name", suggestedFileName);
    if (!fileName) {
      return;
    }
    const blob = new Blob([value], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.download = fileName;
    link.href = url;
    link.style.display = "none";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };
  const onCopy = () => {
    if (isCopied)
      return;
    copyToClipboard(value);
  };
  return /* @__PURE__ */ (0, import_jsx_runtime15.jsxs)("div", { className: "copilotKitCodeBlock", children: [
    /* @__PURE__ */ (0, import_jsx_runtime15.jsxs)("div", { className: "copilotKitCodeBlockToolbar", children: [
      /* @__PURE__ */ (0, import_jsx_runtime15.jsx)("span", { className: "copilotKitCodeBlockToolbarLanguage", children: language }),
      /* @__PURE__ */ (0, import_jsx_runtime15.jsxs)("div", { className: "copilotKitCodeBlockToolbarButtons", children: [
        /* @__PURE__ */ (0, import_jsx_runtime15.jsx)("button", { className: "copilotKitCodeBlockToolbarButton", onClick: downloadAsFile, children: DownloadIcon }),
        /* @__PURE__ */ (0, import_jsx_runtime15.jsx)("button", { className: "copilotKitCodeBlockToolbarButton", onClick: onCopy, children: isCopied ? CheckIcon : CopyIcon })
      ] })
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime15.jsx)(
      import_react_syntax_highlighter.Prism,
      {
        language,
        style: highlightStyle,
        PreTag: "div",
        customStyle: {
          margin: 0,
          borderBottomLeftRadius: "0.375rem",
          borderBottomRightRadius: "0.375rem"
        },
        children: value
      }
    )
  ] });
});
CodeBlock.displayName = "CodeBlock";
var highlightStyle = {
  'pre[class*="language-"]': {
    color: "#d4d4d4",
    fontSize: "13px",
    textShadow: "none",
    fontFamily: 'Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace',
    direction: "ltr",
    textAlign: "left",
    whiteSpace: "pre",
    wordSpacing: "normal",
    wordBreak: "normal",
    lineHeight: "1.5",
    MozTabSize: "4",
    OTabSize: "4",
    tabSize: "4",
    WebkitHyphens: "none",
    MozHyphens: "none",
    msHyphens: "none",
    hyphens: "none",
    padding: "1em",
    margin: ".5em 0",
    overflow: "auto",
    background: "#1e1e1e"
  },
  'code[class*="language-"]': {
    color: "#d4d4d4",
    fontSize: "13px",
    textShadow: "none",
    fontFamily: 'Menlo, Monaco, Consolas, "Andale Mono", "Ubuntu Mono", "Courier New", monospace',
    direction: "ltr",
    textAlign: "left",
    whiteSpace: "pre",
    wordSpacing: "normal",
    wordBreak: "normal",
    lineHeight: "1.5",
    MozTabSize: "4",
    OTabSize: "4",
    tabSize: "4",
    WebkitHyphens: "none",
    MozHyphens: "none",
    msHyphens: "none",
    hyphens: "none"
  },
  'pre[class*="language-"]::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  'code[class*="language-"]::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  'pre[class*="language-"] *::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  'code[class*="language-"] *::selection': {
    textShadow: "none",
    background: "#264F78"
  },
  ':not(pre) > code[class*="language-"]': {
    padding: ".1em .3em",
    borderRadius: ".3em",
    color: "#db4c69",
    background: "#1e1e1e"
  },
  ".namespace": {
    Opacity: ".7"
  },
  "doctype.doctype-tag": {
    color: "#569CD6"
  },
  "doctype.name": {
    color: "#9cdcfe"
  },
  comment: {
    color: "#6a9955"
  },
  prolog: {
    color: "#6a9955"
  },
  punctuation: {
    color: "#d4d4d4"
  },
  ".language-html .language-css .token.punctuation": {
    color: "#d4d4d4"
  },
  ".language-html .language-javascript .token.punctuation": {
    color: "#d4d4d4"
  },
  property: {
    color: "#9cdcfe"
  },
  tag: {
    color: "#569cd6"
  },
  boolean: {
    color: "#569cd6"
  },
  number: {
    color: "#b5cea8"
  },
  constant: {
    color: "#9cdcfe"
  },
  symbol: {
    color: "#b5cea8"
  },
  inserted: {
    color: "#b5cea8"
  },
  unit: {
    color: "#b5cea8"
  },
  selector: {
    color: "#d7ba7d"
  },
  "attr-name": {
    color: "#9cdcfe"
  },
  string: {
    color: "#ce9178"
  },
  char: {
    color: "#ce9178"
  },
  builtin: {
    color: "#ce9178"
  },
  deleted: {
    color: "#ce9178"
  },
  ".language-css .token.string.url": {
    textDecoration: "underline"
  },
  operator: {
    color: "#d4d4d4"
  },
  entity: {
    color: "#569cd6"
  },
  "operator.arrow": {
    color: "#569CD6"
  },
  atrule: {
    color: "#ce9178"
  },
  "atrule.rule": {
    color: "#c586c0"
  },
  "atrule.url": {
    color: "#9cdcfe"
  },
  "atrule.url.function": {
    color: "#dcdcaa"
  },
  "atrule.url.punctuation": {
    color: "#d4d4d4"
  },
  keyword: {
    color: "#569CD6"
  },
  "keyword.module": {
    color: "#c586c0"
  },
  "keyword.control-flow": {
    color: "#c586c0"
  },
  function: {
    color: "#dcdcaa"
  },
  "function.maybe-class-name": {
    color: "#dcdcaa"
  },
  regex: {
    color: "#d16969"
  },
  important: {
    color: "#569cd6"
  },
  italic: {
    fontStyle: "italic"
  },
  "class-name": {
    color: "#4ec9b0"
  },
  "maybe-class-name": {
    color: "#4ec9b0"
  },
  console: {
    color: "#9cdcfe"
  },
  parameter: {
    color: "#9cdcfe"
  },
  interpolation: {
    color: "#9cdcfe"
  },
  "punctuation.interpolation-punctuation": {
    color: "#569cd6"
  },
  variable: {
    color: "#9cdcfe"
  },
  "imports.maybe-class-name": {
    color: "#9cdcfe"
  },
  "exports.maybe-class-name": {
    color: "#9cdcfe"
  },
  escape: {
    color: "#d7ba7d"
  },
  "tag.punctuation": {
    color: "#808080"
  },
  cdata: {
    color: "#808080"
  },
  "attr-value": {
    color: "#ce9178"
  },
  "attr-value.punctuation": {
    color: "#ce9178"
  },
  "attr-value.punctuation.attr-equals": {
    color: "#d4d4d4"
  },
  namespace: {
    color: "#4ec9b0"
  },
  'pre[class*="language-javascript"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-javascript"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-jsx"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-jsx"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-typescript"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-typescript"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-tsx"]': {
    color: "#9cdcfe"
  },
  'code[class*="language-tsx"]': {
    color: "#9cdcfe"
  },
  'pre[class*="language-css"]': {
    color: "#ce9178"
  },
  'code[class*="language-css"]': {
    color: "#ce9178"
  },
  'pre[class*="language-html"]': {
    color: "#d4d4d4"
  },
  'code[class*="language-html"]': {
    color: "#d4d4d4"
  },
  ".language-regex .token.anchor": {
    color: "#dcdcaa"
  },
  ".language-html .token.punctuation": {
    color: "#808080"
  },
  'pre[class*="language-"] > code[class*="language-"]': {
    position: "relative",
    zIndex: "1"
  },
  ".line-highlight.line-highlight": {
    background: "#f7ebc6",
    boxShadow: "inset 5px 0 0 #f7d87c",
    zIndex: "0"
  }
};

// src/components/chat/Markdown.tsx
var import_remark_gfm = __toESM(require("remark-gfm"));
var import_remark_math = __toESM(require("remark-math"));
var import_rehype_raw = __toESM(require("rehype-raw"));
var import_jsx_runtime16 = require("react/jsx-runtime");
var defaultComponents = {
  a(_a) {
    var _b = _a, { children } = _b, props = __objRest(_b, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("a", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { target: "_blank", rel: "noopener noreferrer", children }));
  },
  // @ts-expect-error -- inline
  code(_c) {
    var _d = _c, { children, className, inline } = _d, props = __objRest(_d, ["children", "className", "inline"]);
    if (Array.isArray(children) && children.length) {
      if (children[0] == "\u258D") {
        return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)(
          "span",
          {
            style: {
              animation: "pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",
              marginTop: "0.25rem"
            },
            children: "\u258D"
          }
        );
      }
      children[0] = (children == null ? void 0 : children[0]).replace("`\u258D`", "\u258D");
    }
    const match = /language-(\w+)/.exec(className || "");
    if (inline) {
      return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("code", __spreadProps(__spreadValues({ className }, props), { children }));
    }
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)(
      CodeBlock,
      __spreadValues({
        language: match && match[1] || "",
        value: String(children).replace(/\n$/, "")
      }, props),
      Math.random()
    );
  },
  h1: (_e) => {
    var _f = _e, { children } = _f, props = __objRest(_f, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("h1", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h2: (_g) => {
    var _h = _g, { children } = _h, props = __objRest(_h, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("h2", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h3: (_i) => {
    var _j = _i, { children } = _j, props = __objRest(_j, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("h3", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h4: (_k) => {
    var _l = _k, { children } = _l, props = __objRest(_l, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("h4", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h5: (_m) => {
    var _n = _m, { children } = _n, props = __objRest(_n, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("h5", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  h6: (_o) => {
    var _p = _o, { children } = _p, props = __objRest(_p, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("h6", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  p: (_q) => {
    var _r = _q, { children } = _r, props = __objRest(_r, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("p", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  pre: (_s) => {
    var _t = _s, { children } = _t, props = __objRest(_t, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("pre", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  blockquote: (_u) => {
    var _v = _u, { children } = _v, props = __objRest(_v, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("blockquote", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  ul: (_w) => {
    var _x = _w, { children } = _x, props = __objRest(_x, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("ul", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  },
  li: (_y) => {
    var _z = _y, { children } = _z, props = __objRest(_z, ["children"]);
    return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("li", __spreadProps(__spreadValues({ className: "copilotKitMarkdownElement" }, props), { children }));
  }
};
var MemoizedReactMarkdown = (0, import_react11.memo)(
  import_react_markdown.default,
  (prevProps, nextProps) => prevProps.children === nextProps.children && prevProps.components === nextProps.components
);
var Markdown = ({ content, components }) => {
  return /* @__PURE__ */ (0, import_jsx_runtime16.jsx)("div", { className: "copilotKitMarkdown", children: /* @__PURE__ */ (0, import_jsx_runtime16.jsx)(
    MemoizedReactMarkdown,
    {
      components: __spreadValues(__spreadValues({}, defaultComponents), components),
      remarkPlugins: [import_remark_gfm.default, import_remark_math.default],
      rehypePlugins: [import_rehype_raw.default],
      children: content
    }
  ) });
};

// src/components/chat/messages/AssistantMessage.tsx
var import_react12 = require("react");
var import_jsx_runtime17 = require("react/jsx-runtime");
var AssistantMessage = (props) => {
  const { icons, labels } = useChatContext();
  const {
    message,
    isLoading,
    subComponent,
    onRegenerate,
    onCopy,
    onThumbsUp,
    onThumbsDown,
    isCurrentMessage,
    markdownTagRenderers
  } = props;
  const [copied, setCopied] = (0, import_react12.useState)(false);
  const handleCopy = () => {
    if (message && onCopy) {
      navigator.clipboard.writeText(message);
      setCopied(true);
      onCopy(message);
      setTimeout(() => setCopied(false), 2e3);
    } else if (message) {
      navigator.clipboard.writeText(message);
      setCopied(true);
      setTimeout(() => setCopied(false), 2e3);
    }
  };
  const handleRegenerate = () => {
    if (onRegenerate) {
      onRegenerate();
    }
  };
  const handleThumbsUp = () => {
    if (onThumbsUp && message) {
      onThumbsUp(message);
    }
  };
  const handleThumbsDown = () => {
    if (onThumbsDown && message) {
      onThumbsDown(message);
    }
  };
  const LoadingIcon = () => /* @__PURE__ */ (0, import_jsx_runtime17.jsx)("span", { children: icons.activityIcon });
  return /* @__PURE__ */ (0, import_jsx_runtime17.jsxs)(import_jsx_runtime17.Fragment, { children: [
    (message || isLoading) && /* @__PURE__ */ (0, import_jsx_runtime17.jsxs)("div", { className: "copilotKitMessage copilotKitAssistantMessage", children: [
      message && /* @__PURE__ */ (0, import_jsx_runtime17.jsx)(Markdown, { content: message || "", components: markdownTagRenderers }),
      isLoading && /* @__PURE__ */ (0, import_jsx_runtime17.jsx)(LoadingIcon, {}),
      message && !isLoading && /* @__PURE__ */ (0, import_jsx_runtime17.jsxs)(
        "div",
        {
          className: `copilotKitMessageControls ${isCurrentMessage ? "currentMessage" : ""}`,
          children: [
            /* @__PURE__ */ (0, import_jsx_runtime17.jsx)(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleRegenerate,
                "aria-label": labels.regenerateResponse,
                title: labels.regenerateResponse,
                children: icons.regenerateIcon
              }
            ),
            /* @__PURE__ */ (0, import_jsx_runtime17.jsx)(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleCopy,
                "aria-label": labels.copyToClipboard,
                title: labels.copyToClipboard,
                children: copied ? /* @__PURE__ */ (0, import_jsx_runtime17.jsx)("span", { style: { fontSize: "10px", fontWeight: "bold" }, children: "\u2713" }) : icons.copyIcon
              }
            ),
            onThumbsUp && /* @__PURE__ */ (0, import_jsx_runtime17.jsx)(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleThumbsUp,
                "aria-label": labels.thumbsUp,
                title: labels.thumbsUp,
                children: icons.thumbsUpIcon
              }
            ),
            onThumbsDown && /* @__PURE__ */ (0, import_jsx_runtime17.jsx)(
              "button",
              {
                className: "copilotKitMessageControlButton",
                onClick: handleThumbsDown,
                "aria-label": labels.thumbsDown,
                title: labels.thumbsDown,
                children: icons.thumbsDownIcon
              }
            )
          ]
        }
      )
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime17.jsx)("div", { style: { marginBottom: "0.5rem" }, children: subComponent })
  ] });
};

// src/components/chat/messages/RenderTextMessage.tsx
var import_jsx_runtime18 = require("react/jsx-runtime");
function RenderTextMessage(_a) {
  var _b = _a, {
    UserMessage: UserMessage2 = UserMessage,
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "UserMessage",
    "AssistantMessage"
  ]);
  const {
    message,
    inProgress,
    index,
    isCurrentMessage,
    onRegenerate,
    onCopy,
    onThumbsUp,
    onThumbsDown,
    markdownTagRenderers
  } = props;
  if (message.isTextMessage()) {
    if (message.role === "user") {
      return /* @__PURE__ */ (0, import_jsx_runtime18.jsx)(
        UserMessage2,
        {
          "data-message-role": "user",
          message: message.content,
          rawData: message
        },
        index
      );
    } else if (message.role == "assistant") {
      return /* @__PURE__ */ (0, import_jsx_runtime18.jsx)(
        AssistantMessage2,
        {
          "data-message-role": "assistant",
          message: message.content,
          rawData: message,
          isLoading: inProgress && isCurrentMessage && !message.content,
          isGenerating: inProgress && isCurrentMessage && !!message.content,
          isCurrentMessage,
          onRegenerate: () => onRegenerate == null ? void 0 : onRegenerate(message.id),
          onCopy,
          onThumbsUp,
          onThumbsDown,
          markdownTagRenderers
        },
        index
      );
    }
  }
}

// src/components/chat/messages/RenderActionExecutionMessage.tsx
var import_runtime_client_gql2 = require("@copilotkit/runtime-client-gql");
var import_react_core7 = require("@copilotkit/react-core");
var import_jsx_runtime19 = require("react/jsx-runtime");
function RenderActionExecutionMessage(_a) {
  var _b = _a, {
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "AssistantMessage"
  ]);
  const { chatComponentsCache } = (0, import_react_core7.useCopilotContext)();
  const { message, inProgress, index, isCurrentMessage, actionResult } = props;
  if (message.isActionExecutionMessage()) {
    if (chatComponentsCache.current !== null && (chatComponentsCache.current.actions[message.name] || chatComponentsCache.current.actions["*"])) {
      const render = chatComponentsCache.current.actions[message.name] || chatComponentsCache.current.actions["*"];
      if (typeof render === "string") {
        if (isCurrentMessage && inProgress) {
          return /* @__PURE__ */ (0, import_jsx_runtime19.jsx)(
            AssistantMessage2,
            {
              rawData: message,
              "data-message-role": "assistant",
              isLoading: false,
              isGenerating: true,
              message: render
            },
            index
          );
        } else {
          return null;
        }
      } else {
        const args = message.arguments;
        let status = "inProgress";
        if (actionResult !== void 0) {
          status = "complete";
        } else if (message.status.code !== import_runtime_client_gql2.MessageStatusCode.Pending) {
          status = "executing";
        }
        try {
          const toRender = render({
            status,
            args,
            result: actionResult,
            name: message.name
          });
          if (!toRender && status === "complete") {
            return null;
          }
          if (typeof toRender === "string") {
            return /* @__PURE__ */ (0, import_jsx_runtime19.jsx)(
              AssistantMessage2,
              {
                rawData: message,
                "data-message-role": "assistant",
                isLoading: false,
                isGenerating: false,
                message: toRender
              },
              index
            );
          } else {
            return /* @__PURE__ */ (0, import_jsx_runtime19.jsx)(
              AssistantMessage2,
              {
                rawData: message,
                "data-message-role": "action-render",
                isLoading: false,
                isGenerating: false,
                subComponent: toRender
              },
              index
            );
          }
        } catch (e) {
          console.error(`Error executing render function for action ${message.name}: ${e}`);
          return /* @__PURE__ */ (0, import_jsx_runtime19.jsx)(
            AssistantMessage2,
            {
              rawData: message,
              "data-message-role": "assistant",
              isLoading: false,
              isGenerating: false,
              subComponent: /* @__PURE__ */ (0, import_jsx_runtime19.jsxs)("div", { className: "copilotKitMessage copilotKitAssistantMessage", children: [
                /* @__PURE__ */ (0, import_jsx_runtime19.jsxs)("b", { children: [
                  "\u274C Error executing render function for action ",
                  message.name,
                  ":"
                ] }),
                /* @__PURE__ */ (0, import_jsx_runtime19.jsx)("pre", { children: e instanceof Error ? e.message : String(e) })
              ] })
            },
            index
          );
        }
      }
    } else if (!inProgress || !isCurrentMessage) {
      return null;
    } else {
      return /* @__PURE__ */ (0, import_jsx_runtime19.jsx)(
        AssistantMessage2,
        {
          rawData: message,
          "data-message-role": "assistant",
          isLoading: true,
          isGenerating: true
        },
        index
      );
    }
  }
}

// src/components/chat/messages/RenderResultMessage.tsx
var import_jsx_runtime20 = require("react/jsx-runtime");
function RenderResultMessage(_a) {
  var _b = _a, {
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "AssistantMessage"
  ]);
  const { message, inProgress, index, isCurrentMessage } = props;
  if (message.isResultMessage() && inProgress && isCurrentMessage) {
    return /* @__PURE__ */ (0, import_jsx_runtime20.jsx)(
      AssistantMessage2,
      {
        "data-message-role": "assistant",
        rawData: message,
        isLoading: true,
        isGenerating: true
      },
      index
    );
  } else {
    return null;
  }
}

// src/components/chat/messages/RenderAgentStateMessage.tsx
var import_react_core8 = require("@copilotkit/react-core");
var import_jsx_runtime21 = require("react/jsx-runtime");
function RenderAgentStateMessage(_a) {
  var _b = _a, {
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "AssistantMessage"
  ]);
  const { chatComponentsCache } = (0, import_react_core8.useCopilotContext)();
  const { message, inProgress, index, isCurrentMessage } = props;
  if (message.isAgentStateMessage()) {
    let render;
    if (chatComponentsCache.current !== null) {
      render = chatComponentsCache.current.coAgentStateRenders[`${message.agentName}-${message.nodeName}`] || chatComponentsCache.current.coAgentStateRenders[`${message.agentName}-global`];
    }
    if (render) {
      if (typeof render === "string") {
        if (isCurrentMessage && inProgress) {
          return /* @__PURE__ */ (0, import_jsx_runtime21.jsx)(
            AssistantMessage2,
            {
              rawData: message,
              message: render,
              "data-message-role": "assistant",
              isLoading: true,
              isGenerating: true
            },
            index
          );
        } else {
          return null;
        }
      } else {
        const state = message.state;
        let status = message.active ? "inProgress" : "complete";
        const toRender = render({
          status,
          state,
          nodeName: message.nodeName
        });
        if (!toRender && status === "complete") {
          return null;
        }
        if (!toRender && isCurrentMessage && inProgress) {
          return /* @__PURE__ */ (0, import_jsx_runtime21.jsx)(
            AssistantMessage2,
            {
              "data-message-role": "assistant",
              rawData: message,
              isLoading: true,
              isGenerating: true
            },
            index
          );
        } else if (!toRender) {
          return null;
        }
        if (typeof toRender === "string") {
          return /* @__PURE__ */ (0, import_jsx_runtime21.jsx)(
            AssistantMessage2,
            {
              rawData: message,
              message: toRender,
              isLoading: true,
              isGenerating: true,
              "data-message-role": "assistant"
            },
            index
          );
        } else {
          return /* @__PURE__ */ (0, import_jsx_runtime21.jsx)(
            AssistantMessage2,
            {
              rawData: message,
              "data-message-role": "agent-state-render",
              isLoading: false,
              isGenerating: false,
              subComponent: toRender
            },
            index
          );
        }
      }
    } else if (!inProgress || !isCurrentMessage) {
      return null;
    } else {
      return /* @__PURE__ */ (0, import_jsx_runtime21.jsx)(
        AssistantMessage2,
        {
          rawData: message,
          isLoading: true,
          isGenerating: true,
          "data-message-role": "assistant"
        },
        index
      );
    }
  }
}

// src/components/chat/messages/RenderImageMessage.tsx
var import_jsx_runtime22 = require("react/jsx-runtime");
function RenderImageMessage(_a) {
  var _b = _a, {
    UserMessage: UserMessage2 = UserMessage,
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "UserMessage",
    "AssistantMessage"
  ]);
  const {
    message,
    inProgress,
    index,
    isCurrentMessage,
    onRegenerate,
    onCopy,
    onThumbsUp,
    onThumbsDown
  } = props;
  if (message.isImageMessage()) {
    const imageData = `data:${message.format};base64,${message.bytes}`;
    const imageComponent = /* @__PURE__ */ (0, import_jsx_runtime22.jsx)("div", { className: "copilotKitImage", children: /* @__PURE__ */ (0, import_jsx_runtime22.jsx)(
      "img",
      {
        src: imageData,
        alt: "User uploaded image",
        style: { maxWidth: "100%", maxHeight: "300px", borderRadius: "8px" }
      }
    ) });
    if (message.role === "user") {
      return /* @__PURE__ */ (0, import_jsx_runtime22.jsx)(
        UserMessage2,
        {
          "data-message-role": "user",
          message: "",
          rawData: message,
          subComponent: imageComponent
        },
        index
      );
    } else if (message.role === "assistant") {
      return /* @__PURE__ */ (0, import_jsx_runtime22.jsx)(
        AssistantMessage2,
        {
          "data-message-role": "assistant",
          message: "",
          rawData: message,
          subComponent: imageComponent,
          isLoading: inProgress && isCurrentMessage && !message.bytes,
          isGenerating: inProgress && isCurrentMessage && !!message.bytes,
          isCurrentMessage,
          onRegenerate: () => onRegenerate == null ? void 0 : onRegenerate(message.id),
          onCopy,
          onThumbsUp,
          onThumbsDown
        },
        index
      );
    }
  }
  return null;
}

// src/components/chat/Chat.tsx
var import_react13 = __toESM(require("react"));
var import_react_core10 = require("@copilotkit/react-core");

// src/components/chat/Suggestion.tsx
var import_react_core9 = require("@copilotkit/react-core");
var import_shared2 = require("@copilotkit/shared");
var import_runtime_client_gql3 = require("@copilotkit/runtime-client-gql");
var import_jsx_runtime23 = require("react/jsx-runtime");
function Suggestion({ title, onClick, partial, className }) {
  return /* @__PURE__ */ (0, import_jsx_runtime23.jsx)(
    "button",
    {
      disabled: partial,
      onClick: (e) => {
        e.preventDefault();
        onClick();
      },
      className: className || (partial ? "suggestion loading" : "suggestion"),
      "data-test-id": "suggestion",
      children: partial ? SmallSpinnerIcon : /* @__PURE__ */ (0, import_jsx_runtime23.jsx)("span", { children: title })
    }
  );
}
var reloadSuggestions = (context, chatSuggestionConfiguration, setCurrentSuggestions, abortControllerRef) => __async(void 0, null, function* () {
  const abortController = abortControllerRef.current;
  const tools = JSON.stringify(
    Object.values(context.actions).map((action) => ({
      name: action.name,
      description: action.description,
      jsonSchema: JSON.stringify((0, import_shared2.actionParametersToJsonSchema)(action.parameters))
    }))
  );
  const allSuggestions = [];
  for (const config of Object.values(chatSuggestionConfiguration)) {
    try {
      const numOfSuggestionsInstructions = config.minSuggestions === 0 ? `Produce up to ${config.maxSuggestions} suggestions. If there are no highly relevant suggestions you can think of, provide an empty array.` : `Produce between ${config.minSuggestions} and ${config.maxSuggestions} suggestions.`;
      const result = yield (0, import_react_core9.extract)({
        context,
        instructions: "Suggest what the user could say next. Provide clear, highly relevant suggestions. Do not literally suggest function calls. ",
        data: config.instructions + "\n\n" + numOfSuggestionsInstructions + "\n\nAvailable tools: " + tools + "\n\n",
        requestType: import_runtime_client_gql3.CopilotRequestType.Task,
        parameters: [
          {
            name: "suggestions",
            type: "object[]",
            attributes: [
              {
                name: "title",
                description: "The title of the suggestion. This is shown as a button and should be short.",
                type: "string"
              },
              {
                name: "message",
                description: "The message to send when the suggestion is clicked. This should be a clear, complete sentence and will be sent as an instruction to the AI.",
                type: "string"
              }
            ]
          }
        ],
        include: {
          messages: true,
          readable: true
        },
        abortSignal: abortController == null ? void 0 : abortController.signal,
        stream: ({ status, args }) => {
          const suggestions = args.suggestions || [];
          const newSuggestions = [];
          for (let i = 0; i < suggestions.length; i++) {
            if (config.maxSuggestions !== void 0 && i >= config.maxSuggestions) {
              break;
            }
            const { title, message } = suggestions[i];
            const partial = i == suggestions.length - 1 && status !== "complete";
            newSuggestions.push({
              title,
              message,
              partial,
              className: config.className
            });
          }
          setCurrentSuggestions([...allSuggestions, ...newSuggestions]);
        }
      });
      allSuggestions.push(...result.suggestions);
    } catch (error) {
      console.error("Error loading suggestions", error);
    }
  }
  if (abortControllerRef.current === abortController) {
    abortControllerRef.current = null;
  }
});

// src/components/chat/Chat.tsx
var import_runtime_client_gql4 = require("@copilotkit/runtime-client-gql");
var import_shared3 = require("@copilotkit/shared");
var import_react_core11 = require("@copilotkit/react-core");

// src/components/chat/ImageUploadQueue.tsx
var import_jsx_runtime24 = require("react/jsx-runtime");
var ImageUploadQueue = ({
  images,
  onRemoveImage,
  className = ""
}) => {
  if (images.length === 0)
    return null;
  return /* @__PURE__ */ (0, import_jsx_runtime24.jsx)(
    "div",
    {
      className: `copilotKitImageUploadQueue ${className}`,
      style: {
        display: "flex",
        flexWrap: "wrap",
        gap: "8px",
        margin: "8px",
        padding: "8px"
      },
      children: images.map((image, index) => /* @__PURE__ */ (0, import_jsx_runtime24.jsxs)(
        "div",
        {
          className: "copilotKitImageUploadQueueItem",
          style: {
            position: "relative",
            display: "inline-block",
            width: "60px",
            height: "60px",
            borderRadius: "4px",
            overflow: "hidden"
          },
          children: [
            /* @__PURE__ */ (0, import_jsx_runtime24.jsx)(
              "img",
              {
                src: `data:${image.contentType};base64,${image.bytes}`,
                alt: `Selected image ${index + 1}`,
                style: {
                  width: "100%",
                  height: "100%",
                  objectFit: "cover"
                }
              }
            ),
            /* @__PURE__ */ (0, import_jsx_runtime24.jsx)(
              "button",
              {
                onClick: () => onRemoveImage(index),
                className: "copilotKitImageUploadQueueRemoveButton",
                style: {
                  position: "absolute",
                  top: "2px",
                  right: "2px",
                  background: "rgba(0,0,0,0.6)",
                  color: "white",
                  border: "none",
                  borderRadius: "50%",
                  width: "18px",
                  height: "18px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  cursor: "pointer",
                  fontSize: "10px",
                  padding: 0
                },
                children: "\u2715"
              }
            )
          ]
        },
        index
      ))
    }
  );
};

// src/components/chat/Suggestions.tsx
var import_jsx_runtime25 = require("react/jsx-runtime");
function Suggestions({ suggestions, onSuggestionClick }) {
  return /* @__PURE__ */ (0, import_jsx_runtime25.jsx)("div", { className: "suggestions", children: suggestions.map((suggestion, index) => /* @__PURE__ */ (0, import_jsx_runtime25.jsx)(
    Suggestion,
    {
      title: suggestion.title,
      message: suggestion.message,
      partial: suggestion.partial,
      className: suggestion.className,
      onClick: () => onSuggestionClick(suggestion.message)
    },
    index
  )) });
}

// src/components/chat/Chat.tsx
var import_jsx_runtime26 = require("react/jsx-runtime");
function CopilotChat({
  instructions,
  onSubmitMessage,
  makeSystemMessage,
  onInProgress,
  onStopGeneration,
  onReloadMessages,
  onRegenerate,
  onCopy,
  onThumbsUp,
  onThumbsDown,
  markdownTagRenderers,
  Messages: Messages2 = Messages,
  RenderTextMessage: RenderTextMessage2 = RenderTextMessage,
  RenderActionExecutionMessage: RenderActionExecutionMessage2 = RenderActionExecutionMessage,
  RenderAgentStateMessage: RenderAgentStateMessage2 = RenderAgentStateMessage,
  RenderResultMessage: RenderResultMessage2 = RenderResultMessage,
  RenderImageMessage: RenderImageMessage2 = RenderImageMessage,
  RenderSuggestionsList = Suggestions,
  Input: Input2 = Input,
  className,
  icons,
  labels,
  AssistantMessage: AssistantMessage2 = AssistantMessage,
  UserMessage: UserMessage2 = UserMessage,
  imageUploadsEnabled,
  inputFileAccept = "image/*"
}) {
  const { additionalInstructions, setChatInstructions } = (0, import_react_core10.useCopilotContext)();
  const [selectedImages, setSelectedImages] = (0, import_react13.useState)([]);
  const fileInputRef = (0, import_react13.useRef)(null);
  (0, import_react13.useEffect)(() => {
    if (!imageUploadsEnabled)
      return;
    const handlePaste = (e) => __async(this, null, function* () {
      var _a, _b;
      const target = e.target;
      if (!((_a = target.parentElement) == null ? void 0 : _a.classList.contains("copilotKitInput")))
        return;
      const items = Array.from(((_b = e.clipboardData) == null ? void 0 : _b.items) || []);
      const imageItems = items.filter((item) => item.type.startsWith("image/"));
      if (imageItems.length === 0)
        return;
      e.preventDefault();
      const imagePromises = imageItems.map((item) => {
        const file = item.getAsFile();
        if (!file)
          return Promise.resolve(null);
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e2) => {
            var _a2, _b2;
            const base64String = (_b2 = (_a2 = e2.target) == null ? void 0 : _a2.result) == null ? void 0 : _b2.split(",")[1];
            if (base64String) {
              resolve({
                contentType: file.type,
                bytes: base64String
              });
            } else {
              resolve(null);
            }
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
      });
      try {
        const loadedImages = (yield Promise.all(imagePromises)).filter((img) => img !== null);
        setSelectedImages((prev) => [...prev, ...loadedImages]);
      } catch (error) {
        console.error("Error processing pasted images:", error);
      }
    });
    document.addEventListener("paste", handlePaste);
    return () => document.removeEventListener("paste", handlePaste);
  }, [imageUploadsEnabled]);
  (0, import_react13.useEffect)(() => {
    if (!(additionalInstructions == null ? void 0 : additionalInstructions.length)) {
      setChatInstructions(instructions || "");
      return;
    }
    const combinedAdditionalInstructions = [
      instructions,
      "Additionally, follow these instructions:",
      ...additionalInstructions.map((instruction) => `- ${instruction}`)
    ];
    console.log("combinedAdditionalInstructions", combinedAdditionalInstructions);
    setChatInstructions(combinedAdditionalInstructions.join("\n") || "");
  }, [instructions, additionalInstructions]);
  const {
    visibleMessages,
    isLoading,
    currentSuggestions,
    sendMessage,
    stopGeneration,
    reloadMessages
  } = useCopilotChatLogic(
    makeSystemMessage,
    onInProgress,
    onSubmitMessage,
    onStopGeneration,
    onReloadMessages
  );
  const handleSendMessage = (text) => {
    const images = selectedImages;
    setSelectedImages([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    return sendMessage(text, images);
  };
  const chatContext = import_react13.default.useContext(ChatContext);
  const isVisible = chatContext ? chatContext.open : true;
  const handleRegenerate = (messageId) => {
    if (onRegenerate) {
      onRegenerate(messageId);
    }
    reloadMessages(messageId);
  };
  const handleCopy = (message) => {
    if (onCopy) {
      onCopy(message);
    }
  };
  const handleImageUpload = (event) => __async(this, null, function* () {
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }
    const files = Array.from(event.target.files).filter((file) => file.type.startsWith("image/"));
    if (files.length === 0)
      return;
    const fileReadPromises = files.map((file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          var _a, _b;
          const base64String = ((_b = (_a = e.target) == null ? void 0 : _a.result) == null ? void 0 : _b.split(",")[1]) || "";
          if (base64String) {
            resolve({
              contentType: file.type,
              bytes: base64String
            });
          }
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    });
    try {
      const loadedImages = yield Promise.all(fileReadPromises);
      setSelectedImages((prev) => [...prev, ...loadedImages]);
    } catch (error) {
      console.error("Error reading files:", error);
    }
  });
  const removeSelectedImage = (index) => {
    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
  };
  return /* @__PURE__ */ (0, import_jsx_runtime26.jsxs)(WrappedCopilotChat, { icons, labels, className, children: [
    /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(
      Messages2,
      {
        AssistantMessage: AssistantMessage2,
        UserMessage: UserMessage2,
        RenderTextMessage: RenderTextMessage2,
        RenderActionExecutionMessage: RenderActionExecutionMessage2,
        RenderAgentStateMessage: RenderAgentStateMessage2,
        RenderResultMessage: RenderResultMessage2,
        RenderImageMessage: RenderImageMessage2,
        messages: visibleMessages,
        inProgress: isLoading,
        onRegenerate: handleRegenerate,
        onCopy: handleCopy,
        onThumbsUp,
        onThumbsDown,
        markdownTagRenderers,
        children: currentSuggestions.length > 0 && /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(
          RenderSuggestionsList,
          {
            onSuggestionClick: handleSendMessage,
            suggestions: currentSuggestions
          }
        )
      }
    ),
    imageUploadsEnabled && /* @__PURE__ */ (0, import_jsx_runtime26.jsxs)(import_jsx_runtime26.Fragment, { children: [
      /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(ImageUploadQueue, { images: selectedImages, onRemoveImage: removeSelectedImage }),
      /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(
        "input",
        {
          type: "file",
          multiple: true,
          ref: fileInputRef,
          onChange: handleImageUpload,
          accept: inputFileAccept,
          style: { display: "none" }
        }
      )
    ] }),
    /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(
      Input2,
      {
        inProgress: isLoading,
        onSend: handleSendMessage,
        isVisible,
        onStop: stopGeneration,
        onUpload: imageUploadsEnabled ? () => {
          var _a;
          return (_a = fileInputRef.current) == null ? void 0 : _a.click();
        } : void 0
      }
    )
  ] });
}
function WrappedCopilotChat({
  children,
  icons,
  labels,
  className
}) {
  const chatContext = import_react13.default.useContext(ChatContext);
  if (!chatContext) {
    return /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(ChatContextProvider, { icons, labels, open: true, setOpen: () => {
    }, children: /* @__PURE__ */ (0, import_jsx_runtime26.jsx)("div", { className: `copilotKitChat ${className != null ? className : ""}`, children }) });
  }
  return /* @__PURE__ */ (0, import_jsx_runtime26.jsx)(import_jsx_runtime26.Fragment, { children });
}
var SUGGESTIONS_DEBOUNCE_TIMEOUT = 1e3;
var useCopilotChatLogic = (makeSystemMessage, onInProgress, onSubmitMessage, onStopGeneration, onReloadMessages) => {
  var _a;
  const {
    visibleMessages,
    appendMessage,
    reloadMessages: defaultReloadMessages,
    stopGeneration: defaultStopGeneration,
    runChatCompletion,
    isLoading
  } = (0, import_react_core10.useCopilotChat)({
    id: (0, import_shared3.randomId)(),
    makeSystemMessage
  });
  const [currentSuggestions, setCurrentSuggestions] = (0, import_react13.useState)([]);
  const suggestionsAbortControllerRef = (0, import_react13.useRef)(null);
  const debounceTimerRef = (0, import_react13.useRef)();
  const abortSuggestions = () => {
    var _a2;
    (_a2 = suggestionsAbortControllerRef.current) == null ? void 0 : _a2.abort();
    suggestionsAbortControllerRef.current = null;
  };
  const generalContext = (0, import_react_core10.useCopilotContext)();
  const messagesContext = (0, import_react_core10.useCopilotMessagesContext)();
  const context = __spreadValues(__spreadValues({}, generalContext), messagesContext);
  (0, import_react13.useEffect)(() => {
    onInProgress == null ? void 0 : onInProgress(isLoading);
    abortSuggestions();
    debounceTimerRef.current = setTimeout(
      () => {
        if (!isLoading && Object.keys(context.chatSuggestionConfiguration).length !== 0) {
          suggestionsAbortControllerRef.current = new AbortController();
          reloadSuggestions(
            context,
            context.chatSuggestionConfiguration,
            setCurrentSuggestions,
            suggestionsAbortControllerRef
          );
        }
      },
      currentSuggestions.length == 0 ? 0 : SUGGESTIONS_DEBOUNCE_TIMEOUT
    );
    return () => {
      clearTimeout(debounceTimerRef.current);
    };
  }, [
    isLoading,
    context.chatSuggestionConfiguration,
    // hackish way to trigger suggestions reload on reset, but better than moving suggestions to the
    // global context
    visibleMessages.length == 0
  ]);
  const sendMessage = (messageContent, imagesToUse) => __async(void 0, null, function* () {
    const images = imagesToUse || [];
    abortSuggestions();
    setCurrentSuggestions([]);
    let firstMessage = null;
    if (messageContent.trim().length > 0) {
      const textMessage = new import_runtime_client_gql4.TextMessage({
        content: messageContent,
        role: import_runtime_client_gql4.Role.User
      });
      if (onSubmitMessage) {
        try {
          yield onSubmitMessage(messageContent);
        } catch (error) {
          console.error("Error in onSubmitMessage:", error);
        }
      }
      yield appendMessage(textMessage, { followUp: images.length === 0 });
      if (!firstMessage) {
        firstMessage = textMessage;
      }
    }
    if (images.length > 0) {
      for (let i = 0; i < images.length; i++) {
        const imageMessage = new import_runtime_client_gql4.ImageMessage({
          format: images[i].contentType.replace("image/", ""),
          bytes: images[i].bytes,
          role: import_runtime_client_gql4.Role.User
        });
        yield appendMessage(imageMessage, { followUp: i === images.length - 1 });
        if (!firstMessage) {
          firstMessage = imageMessage;
        }
      }
    }
    if (!firstMessage) {
      return new import_runtime_client_gql4.TextMessage({ content: "", role: import_runtime_client_gql4.Role.User });
    }
    return firstMessage;
  });
  const messages = visibleMessages;
  const { setMessages } = messagesContext;
  const currentAgentName = (_a = generalContext.agentSession) == null ? void 0 : _a.agentName;
  const restartCurrentAgent = (hint) => __async(void 0, null, function* () {
    if (generalContext.agentSession) {
      generalContext.setAgentSession(__spreadProps(__spreadValues({}, generalContext.agentSession), {
        nodeName: void 0,
        threadId: void 0
      }));
      generalContext.setCoagentStates((prevAgentStates) => {
        return __spreadProps(__spreadValues({}, prevAgentStates), {
          [generalContext.agentSession.agentName]: __spreadProps(__spreadValues({}, prevAgentStates[generalContext.agentSession.agentName]), {
            threadId: void 0,
            nodeName: void 0,
            runId: void 0
          })
        });
      });
    }
  });
  const runCurrentAgent = (hint) => __async(void 0, null, function* () {
    if (generalContext.agentSession) {
      yield (0, import_react_core11.runAgent)(
        generalContext.agentSession.agentName,
        context,
        appendMessage,
        runChatCompletion,
        hint
      );
    }
  });
  const stopCurrentAgent = () => {
    if (generalContext.agentSession) {
      (0, import_react_core11.stopAgent)(generalContext.agentSession.agentName, context);
    }
  };
  const setCurrentAgentState = (state) => {
    if (generalContext.agentSession) {
      generalContext.setCoagentStates((prevAgentStates) => {
        return __spreadProps(__spreadValues({}, prevAgentStates), {
          [generalContext.agentSession.agentName]: {
            state
          }
        });
      });
    }
  };
  function stopGeneration() {
    if (onStopGeneration) {
      onStopGeneration({
        messages,
        setMessages,
        stopGeneration: defaultStopGeneration,
        currentAgentName,
        restartCurrentAgent,
        stopCurrentAgent,
        runCurrentAgent,
        setCurrentAgentState
      });
    } else {
      defaultStopGeneration();
    }
  }
  function reloadMessages(messageId) {
    if (onReloadMessages) {
      onReloadMessages({
        messages,
        setMessages,
        stopGeneration: defaultStopGeneration,
        currentAgentName,
        restartCurrentAgent,
        stopCurrentAgent,
        runCurrentAgent,
        setCurrentAgentState,
        messageId
      });
    } else {
      defaultReloadMessages(messageId);
    }
  }
  return {
    visibleMessages,
    isLoading,
    currentSuggestions,
    sendMessage,
    stopGeneration,
    reloadMessages
  };
};

// src/components/chat/Modal.tsx
var import_jsx_runtime27 = require("react/jsx-runtime");
var CopilotModal = (_a) => {
  var _b = _a, {
    instructions,
    defaultOpen = false,
    clickOutsideToClose = true,
    hitEscapeToClose = true,
    onSetOpen,
    onSubmitMessage,
    onStopGeneration,
    onReloadMessages,
    shortcut = "/",
    icons,
    labels,
    makeSystemMessage,
    onInProgress,
    Window: Window2 = Window,
    Button: Button2 = Button,
    Header: Header2 = Header,
    Messages: Messages2 = Messages,
    Input: Input2 = Input,
    AssistantMessage: AssistantMessage2 = AssistantMessage,
    UserMessage: UserMessage2 = UserMessage,
    onThumbsUp,
    onThumbsDown,
    onCopy,
    onRegenerate,
    markdownTagRenderers,
    className,
    children
  } = _b, props = __objRest(_b, [
    "instructions",
    "defaultOpen",
    "clickOutsideToClose",
    "hitEscapeToClose",
    "onSetOpen",
    "onSubmitMessage",
    "onStopGeneration",
    "onReloadMessages",
    "shortcut",
    "icons",
    "labels",
    "makeSystemMessage",
    "onInProgress",
    "Window",
    "Button",
    "Header",
    "Messages",
    "Input",
    "AssistantMessage",
    "UserMessage",
    "onThumbsUp",
    "onThumbsDown",
    "onCopy",
    "onRegenerate",
    "markdownTagRenderers",
    "className",
    "children"
  ]);
  const [openState, setOpenState] = import_react14.default.useState(defaultOpen);
  const setOpen = (open) => {
    onSetOpen == null ? void 0 : onSetOpen(open);
    setOpenState(open);
  };
  return /* @__PURE__ */ (0, import_jsx_runtime27.jsxs)(ChatContextProvider, { icons, labels, open: openState, setOpen, children: [
    children,
    /* @__PURE__ */ (0, import_jsx_runtime27.jsxs)("div", { className, children: [
      /* @__PURE__ */ (0, import_jsx_runtime27.jsx)(Button2, {}),
      /* @__PURE__ */ (0, import_jsx_runtime27.jsxs)(
        Window2,
        {
          clickOutsideToClose,
          shortcut,
          hitEscapeToClose,
          children: [
            /* @__PURE__ */ (0, import_jsx_runtime27.jsx)(Header2, {}),
            /* @__PURE__ */ (0, import_jsx_runtime27.jsx)(
              CopilotChat,
              __spreadProps(__spreadValues({}, props), {
                instructions,
                onSubmitMessage,
                onStopGeneration,
                onReloadMessages,
                makeSystemMessage,
                onInProgress,
                Messages: Messages2,
                Input: Input2,
                AssistantMessage: AssistantMessage2,
                UserMessage: UserMessage2,
                onThumbsUp,
                onThumbsDown,
                onCopy,
                onRegenerate,
                markdownTagRenderers
              })
            )
          ]
        }
      )
    ] })
  ] });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CopilotModal
});
//# sourceMappingURL=Modal.js.map