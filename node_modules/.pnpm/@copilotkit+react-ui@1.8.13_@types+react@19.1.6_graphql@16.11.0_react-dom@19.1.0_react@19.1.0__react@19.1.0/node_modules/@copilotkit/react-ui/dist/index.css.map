{"version": 3, "sources": ["../src/css/colors.css", "../src/css/popup.css", "../src/css/sidebar.css", "../src/css/button.css", "../src/css/header.css", "../src/css/input.css", "../src/css/messages.css", "../src/css/window.css", "../src/css/animations.css", "../src/css/markdown.css", "../src/css/suggestions.css", "../src/css/panel.css", "../src/css/console.css", "../src/css/crew.css"], "sourcesContent": ["/* IMPORTANT NOTE:\nTHE DARK AND LIGHT COLORS HERE ARE DUPLICATED BECAUSE NO REUSE METHOD POSSIBLE.\nWHEN MAKING ANY CHANGE, MAKE SURE TO INCLUDE IT IN ALL DUPLICATIONS.\n*/\n\n/* BASE LIGHT THEME */\n:root {\n  /* Semantic color tokens */\n  /* Main brand/action color - used for buttons, interactive elements */\n  --copilot-kit-primary-color: rgb(28, 28, 28);\n  /* Color that contrasts with primary - used for text on primary elements */\n  --copilot-kit-contrast-color: rgb(255, 255, 255);\n  /* Main page/container background color */\n  --copilot-kit-background-color: rgb(255 255 255);\n  /* Input box background color */\n  --copilot-kit-input-background-color: #fbfbfb;\n  /* Secondary background - used for cards, panels, elevated surfaces */\n  --copilot-kit-secondary-color: rgb(255 255 255);\n  /* Primary text color for main content */\n  --copilot-kit-secondary-contrast-color: rgb(28, 28, 28);\n  /* Border color for dividers and containers */\n  --copilot-kit-separator-color: rgb(200 200 200);\n  /* Muted color for disabled/inactive states */\n  --copilot-kit-muted-color: rgb(200 200 200);\n\n  /* Shadow tokens */\n  /* Small shadow for subtle elevation */\n  --copilot-kit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  /* Medium shadow for cards */\n  --copilot-kit-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  /* Large shadow for modals */\n  --copilot-kit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n\n  --copilot-kit-dev-console-bg: #f8f8fa;\n  --copilot-kit-dev-console-text: black;\n}\n\n/* BASE DARK THEME */\n.dark,\nhtml.dark,\nbody.dark,\n[data-theme=\"dark\"],\nhtml[style*=\"color-scheme: dark\"],\nbody[style*=\"color-scheme: dark\"] :root {\n  /* Main brand/action color - used for buttons, interactive elements */\n  --copilot-kit-primary-color: rgb(255, 255, 255);\n  /* Color that contrasts with primary - used for text on primary elements */\n  --copilot-kit-contrast-color: rgb(28, 28, 28);\n  /* Main page/container background color */\n  --copilot-kit-background-color: rgb(17, 17, 17);\n  /* Input box background color */\n  --copilot-kit-input-background-color: #2c2c2c;\n  /* Secondary background - used for cards, panels, elevated surfaces */\n  --copilot-kit-secondary-color: rgb(28, 28, 28);\n  /* Primary text color for main content */\n  --copilot-kit-secondary-contrast-color: rgb(255, 255, 255);\n  /* Border color for dividers and containers */\n  --copilot-kit-separator-color: rgb(45, 45, 45);\n  /* Muted color for disabled/inactive states */\n  --copilot-kit-muted-color: rgb(45, 45, 45);\n\n  /* Small shadow for subtle elevation */\n  --copilot-kit-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);\n  /* Medium shadow for cards */\n  --copilot-kit-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);\n  /* Large shadow for modals */\n  --copilot-kit-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);\n}\n", ".copilotKitPopup {\n  position: fixed;\n  bottom: 1rem;\n  right: 1rem;\n  z-index: 30;\n  line-height: 1.5;\n  -webkit-text-size-adjust: 100%;\n  -moz-tab-size: 4;\n  -o-tab-size: 4;\n  tab-size: 4;\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Robot<PERSON>,\n    \"Helvetica Neue\", <PERSON>l, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-feature-settings: normal;\n  font-variation-settings: normal;\n  touch-action: manipulation;\n}\n\n.copilotKitPopup svg {\n  display: inline-block;\n  vertical-align: middle;\n}\n", ".copilotKitSidebar {\n  position: fixed;\n  bottom: 1rem;\n  right: 1rem;\n  z-index: 30;\n  line-height: 1.5;\n  -webkit-text-size-adjust: 100%;\n  -moz-tab-size: 4;\n  -o-tab-size: 4;\n  tab-size: 4;\n  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Robot<PERSON>,\n    \"Helvetica Neue\", <PERSON>l, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-feature-settings: normal;\n  font-variation-settings: normal;\n  touch-action: manipulation;\n}\n\n.copilotKitSidebar svg {\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.copilotKitSidebarContentWrapper {\n  overflow: visible;\n  margin-right: 0px;\n  transition: margin-right 0.3s ease;\n}\n\n@media (min-width: 640px) {\n  .copilotKitSidebarContentWrapper.sidebarExpanded {\n    margin-right: 28rem;\n  }\n}\n", ".copilotKitButton {\n  width: 3.5rem;\n  height: 3.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  border: 1px solid var(--copilot-kit-primary-color);\n  outline: none;\n  position: relative;\n  transform: scale(1);\n  transition: all 0.2s ease;\n  background-color: var(--copilot-kit-primary-color);\n  color: var(--copilot-kit-contrast-color);\n  cursor: pointer;\n  box-shadow: var(--copilot-kit-shadow-sm);\n}\n\n.copilotKitButton:hover {\n  transform: scale(1.05);\n  box-shadow: var(--copilot-kit-shadow-md);\n}\n\n.copilotKitButton:active {\n  transform: scale(0.95);\n  box-shadow: var(--copilot-kit-shadow-sm);\n}\n\n.copilotKitButtonIcon {\n  transition:\n    opacity 100ms,\n    transform 300ms;\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.copilotKitButtonIcon svg {\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n/* State when the chat is open */\n.copilotKitButton.open .copilotKitButtonIconOpen {\n  transform: translate(-50%, -50%) scale(0) rotate(90deg);\n  opacity: 0;\n}\n\n.copilotKitButton.open .copilotKitButtonIconClose {\n  transform: translate(-50%, -50%) scale(1) rotate(0deg);\n  opacity: 1;\n}\n\n/* State when the chat is closed */\n.copilotKitButton:not(.open) .copilotKitButtonIconOpen {\n  transform: translate(-50%, -50%) scale(1) rotate(0deg);\n  opacity: 1;\n}\n\n.copilotKitButton:not(.open) .copilotKitButtonIconClose {\n  transform: translate(-50%, -50%) scale(0) rotate(-90deg);\n  opacity: 0;\n}\n", ".copilotKitHeader {\n  height: 56px;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  position: relative;\n  color: var(--copilot-kit-primary-color);\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom: 1px solid var(--copilot-kit-separator-color);\n  padding-left: 1.5rem;\n  background-color: var(--copilot-kit-contrast-color);\n  justify-content: space-between;\n  z-index: 2;\n}\n\n.copilotKitSidebar .copilotKitHeader {\n  border-radius: 0;\n}\n\n.copilotKitHeaderControls {\n  display: flex;\n}\n\n.copilotKitHeaderCloseButton {\n  background: none;\n  border: none;\n}\n\n@media (min-width: 640px) {\n  .copilotKitHeader {\n    padding-left: 1.5rem;\n    padding-right: 24px;\n    border-top-left-radius: 8px;\n    border-top-right-radius: 8px;\n  }\n}\n\n.copilotKitHeader > button {\n  border: 0;\n  padding: 8px;\n  position: absolute;\n  top: 50%;\n  right: 16px;\n  transform: translateY(-50%);\n  outline: none;\n  color: var(--copilot-kit-muted-color);\n  background-color: transparent;\n  cursor: pointer;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n  width: 35px;\n  height: 35px;\n}\n\n.copilotKitHeader > button:hover {\n  color: color-mix(in srgb, var(--copilot-kit-muted-color) 80%, black);\n}\n\n.copilotKitHeader > button:focus {\n  outline: none;\n}\n", ".copilotKitInput {\n  cursor: text;\n  position: relative;\n  background-color: var(--copilot-kit-input-background-color);\n  border-radius: 20px;\n  border: 1px solid var(--copilot-kit-separator-color);\n  padding: 12px 14px;\n  min-height: 75px;\n  margin: 0 auto;\n  width: 95%;\n}\n\n.copilotKitInputContainer {\n  width: 100%;\n  padding: 0;\n  padding-bottom: 15px;\n  background: var(--copilot-kit-background-color);\n  border-bottom-left-radius: 0.75rem;\n  border-bottom-right-radius: 0.75rem;\n}\n\n.copilotKitSidebar .copilotKitInputContainer {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.copilotKitInputControlButton {\n  padding: 0;\n  cursor: pointer;\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n  transform: scale(1);\n  color: rgba(0, 0, 0, 0.25);\n  -webkit-appearance: button;\n  appearance: button;\n  background-color: transparent;\n  background-image: none;\n  text-transform: none;\n  font-family: inherit;\n  font-size: 100%;\n  font-weight: inherit;\n  line-height: inherit;\n  border: 0;\n  margin: 0;\n  text-indent: 0px;\n  text-shadow: none;\n  display: inline-block;\n  text-align: center;\n  width: 24px;\n  height: 24px;\n}\n\n.copilotKitInputControlButton:not([disabled]) {\n  color: var(--copilot-kit-primary-color);\n}\n\n.copilotKitInputControlButton:not([disabled]):hover {\n  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);\n  transform: scale(1.05);\n}\n\n.copilotKitInputControlButton[disabled] {\n  color: var(--copilot-kit-muted-color);\n  cursor: default;\n}\n\n.copilotKitInputControls {\n  display: flex;\n  gap: 3px;\n}\n\n.copilotKitInput > textarea {\n  flex: 1; /* Allow textarea to take up remaining space */\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  resize: none;\n  white-space: pre-wrap;\n  overflow-wrap: break-word;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  cursor: text;\n  font-size: 0.875rem;\n  line-height: 1.5rem;\n  margin: 0;\n  padding: 0;\n  font-family: inherit;\n  font-weight: inherit;\n  color: var(--copilot-kit-secondary-contrast-color);\n  border: 0px;\n  background-color: transparent;\n  width: 100%;\n}\n\n.copilotKitInput > textarea::placeholder {\n  color: #808080;\n  opacity: 1;\n}\n\n.copilotKitInputControlButton.copilotKitPushToTalkRecording {\n  background-color: #ec0000;\n  color: white;\n  border-radius: 50%;\n  animation: copilotKitPulseAnimation 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n/* Scrollbar styles */\n.copilotKitInput textarea::-webkit-scrollbar {\n  width: 9px; /* Width of the entire scrollbar */\n}\n\n.copilotKitInput textarea::-webkit-scrollbar-track {\n  background: transparent; /* Color of the tracking area */\n}\n\n.copilotKitInput textarea::-webkit-scrollbar-thumb {\n  background-color: rgb(200 200 200); /* Color of the scroll thumb */\n  border-radius: 10px; /* Roundness of the scroll thumb */\n  border: 2px solid transparent; /* Creates padding around scroll thumb */\n  background-clip: content-box;\n  cursor: pointer;\n}\n\n.copilotKitInput textarea::-webkit-scrollbar-thumb:hover {\n  background-color: color-mix(in srgb, rgb(200 200 200) 80%, black); /* Darker color on hover */\n}\n\n.poweredByContainer {\n  padding: 0;\n}\n\n.poweredBy {\n  background: var(--copilot-kit-background-color) !important;\n  visibility: visible !important;\n  display: block !important;\n  position: static !important;\n  text-align: center !important;\n  font-size: 12px !important;\n  padding: 3px 0 !important;\n  color: rgb(214, 214, 214) !important;\n  margin: 0 !important;\n}\n\n.dark,\nhtml.dark,\nbody.dark,\n[data-theme=\"dark\"],\nhtml[style*=\"color-scheme: dark\"],\nbody[style*=\"color-scheme: dark\"] .poweredBy {\n  color: rgb(69, 69, 69) !important;\n}\n\n", ".copilotKitMessages {\n  overflow-y: scroll;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--copilot-kit-background-color);\n  justify-content: space-between;\n  z-index: 1;\n}\n\n.copilotKitMessagesContainer {\n  /*overflow-y: scroll;*/\n  /*flex: 1;*/\n  padding: 1rem 24px;\n  display: flex;\n  flex-direction: column;\n}\n\n.copilotKitMessagesFooter {\n  display: flex;\n  padding: 0;\n  margin: 0 auto 8px auto;\n  justify-content: flex-start;\n  flex-direction: column;\n  width: 90%;\n}\n\n.copilotKitMessages::-webkit-scrollbar {\n  width: 6px;\n}\n\n.copilotKitMessages::-webkit-scrollbar-thumb {\n  background-color: var(--copilot-kit-separator-color);\n  border-radius: 10rem;\n  border: 2px solid var(--copilot-kit-background-color);\n}\n\n.copilotKitMessages::-webkit-scrollbar-track-piece:start {\n  background: transparent;\n}\n\n.copilotKitMessages::-webkit-scrollbar-track-piece:end {\n  background: transparent;\n}\n\n.copilotKitMessage {\n  border-radius: 15px;\n  padding: 8px 12px;\n  font-size: 1rem;\n  line-height: 1.5;\n  overflow-wrap: break-word;\n  max-width: 80%;\n  margin-bottom: 0.5rem;\n}\n\n.copilotKitMessage.copilotKitUserMessage {\n  background: var(--copilot-kit-primary-color);\n  color: var(--copilot-kit-contrast-color);\n  margin-left: auto;\n  white-space: pre-wrap;\n  line-height: 1.75;\n  font-size: 1rem;\n}\n\n.copilotKitMessage.copilotKitAssistantMessage {\n  background: transparent;\n  margin-right: auto;\n  padding-left: 0;\n  position: relative;\n  max-width: 100%;\n  color: var(--copilot-kit-secondary-contrast-color);\n}\n\n.copilotKitMessage.copilotKitAssistantMessage .copilotKitMessageControls {\n  position: absolute;\n  left: 0;\n  display: flex;\n  gap: 1rem;\n  opacity: 0;\n  transition: opacity 0.2s ease;\n  padding: 5px 0 0 0;\n}\n\n.copilotKitMessageControls.currentMessage {\n  opacity: 1 !important;\n}\n\n.copilotKitMessage.copilotKitAssistantMessage:hover .copilotKitMessageControls {\n  opacity: 1;\n}\n\n/* Always show controls on mobile */\n@media (max-width: 768px) {\n  .copilotKitMessage.copilotKitAssistantMessage .copilotKitMessageControls {\n    opacity: 1;\n  }\n}\n\n.copilotKitMessageControlButton {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  border-radius: 0.5rem;\n  justify-content: center;\n  color: var(--copilot-kit-primary-color);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  padding: 0;\n  z-index: 10;\n  margin: 0;\n  background: transparent;\n  border: none;\n}\n\n.copilotKitMessageControlButton:hover {\n  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);\n  transform: scale(1.05);\n}\n\n.copilotKitMessageControlButton:active {\n  color: color-mix(in srgb, var(--copilot-kit-primary-color) 80%, black);\n  transform: scale(1.05);\n}\n\n.copilotKitMessageControlButton svg {\n  width: 1rem;\n  height: 1rem;\n  display: block;\n  pointer-events: none;\n}\n\n.copilotKitMessage.copilotKitAssistantMessage + .copilotKitMessage.copilotKitUserMessage {\n  margin-top: 1.5rem;\n}\n\n.copilotKitCustomAssistantMessage {\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.copilotKitMessage .inProgressLabel {\n  margin-left: 10px;\n  opacity: 0.7;\n}\n\n@keyframes copilotKitSpinAnimation {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.copilotKitSpinner {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  border: 2px solid var(--copilot-kit-contrast-color);\n  border-radius: 50%;\n  border-top-color: var(--copilot-kit-primary-color);\n  animation: copilotKitSpinAnimation 1s linear infinite;\n  margin-left: 8px;\n}\n\n@keyframes copilotKitActivityDotAnimation {\n  0%, 80%, 100% {\n    transform: scale(0.5);\n    opacity: 0.5;\n  }\n  40% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n.copilotKitActivityDot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: var(--copilot-kit-primary-color);\n  animation: copilotKitActivityDotAnimation 1.4s infinite ease-in-out both;\n}\n", ".copilotKitWindow {\n  position: fixed;\n  inset: 0px;\n  transform-origin: bottom;\n  border-color: rgb(229 231 235);\n  background-color: var(--copilot-kit-background-color);\n  border-radius: 0.75rem;\n  box-shadow: rgba(0, 0, 0, 0.16) 0px 5px 40px;\n  flex-direction: column;\n  transition:\n    opacity 100ms ease-out,\n    transform 200ms ease-out;\n  opacity: 0;\n  transform: scale(0.95) translateY(20px);\n  display: flex;\n  pointer-events: none;\n}\n\n.copilotKitSidebar .copilotKitWindow {\n  border-radius: 0;\n  opacity: 1;\n  transform: translateX(100%);\n}\n\n.copilotKitWindow.open {\n  opacity: 1;\n  transform: scale(1) translateY(0);\n  pointer-events: auto;\n}\n\n.copilotKitSidebar .copilotKitWindow.open {\n  transform: translateX(0);\n}\n\n@media (min-width: 640px) {\n  .copilotKitWindow {\n    transform-origin: bottom right;\n    bottom: 5rem;\n    right: 1rem;\n    top: auto;\n    left: auto;\n    border-width: 0px;\n    margin-bottom: 1rem;\n    width: 24rem;\n    height: 600px;\n    min-height: 200px;\n    max-height: calc(100% - 6rem);\n  }\n\n  .copilotKitSidebar .copilotKitWindow {\n    bottom: 0;\n    right: 0;\n    top: auto;\n    left: auto;\n    width: 28rem;\n    min-height: 100%;\n    margin-bottom: 0;\n    max-height: none;\n  }\n}\n", ".copilotKitActivityDot1 {\n  animation: copilotKitActivityDotsAnimation 1.05s infinite;\n}\n.copilotKitActivityDot2 {\n  animation-delay: 0.1s;\n}\n.copilotKitActivityDot3 {\n  animation-delay: 0.2s;\n}\n@keyframes copilotKitActivityDotsAnimation {\n  0%,\n  57.14% {\n    animation-timing-function: cubic-bezier(0.33, 0.66, 0.66, 1);\n    transform: translate(0);\n  }\n  28.57% {\n    animation-timing-function: cubic-bezier(0.33, 0, 0.66, 0.33);\n    transform: translateY(-6px);\n  }\n  100% {\n    transform: translate(0);\n  }\n}\n\n@keyframes copilotKitSpinAnimation {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes copilotKitPulseAnimation {\n  50% {\n    opacity: 0.5;\n  }\n}\n", "h1.copilotKitMarkdownElement,\nh2.copilotKitMarkdownElement,\nh3.copilotKitMarkdownElement,\nh4.copilotKitMarkdownElement,\nh5.copilotKitMarkdownElement,\nh6.copilotKitMarkdownElement {\n  font-weight: bold;\n  line-height: 1.2;\n}\n\nh1.copilotKitMarkdownElement:not(:last-child),\nh2.copilotKitMarkdownElement:not(:last-child),\nh3.copilotKitMarkdownElement:not(:last-child),\nh4.copilotKitMarkdownElement:not(:last-child),\nh5.copilotKitMarkdownElement:not(:last-child),\nh6.copilotKitMarkdownElement:not(:last-child) {\n  margin-bottom: 1rem;\n}\n\nh1.copilotKitMarkdownElement {\n  font-size: 1.5em;\n}\n\nh2.copilotKitMarkdownElement {\n  font-size: 1.25em;\n  font-weight: 600;\n}\n\nh3.copilotKitMarkdownElement {\n  font-size: 1.1em;\n}\n\nh4.copilotKitMarkdownElement {\n  font-size: 1em;\n}\n\nh5.copilotKitMarkdownElement {\n  font-size: 0.9em;\n}\n\nh6.copilotKitMarkdownElement {\n  font-size: 0.8em;\n}\n\na.copilotKitMarkdownElement {\n  color: blue;\n  text-decoration: underline;\n}\n\np.copilotKitMarkdownElement {\n  padding: 0px;\n  margin: 0px;\n  line-height: 1.75;\n  font-size: 1rem;\n}\n\np.copilotKitMarkdownElement:not(:last-child),\npre.copilotKitMarkdownElement:not(:last-child),\nol.copilotKitMarkdownElement:not(:last-child),\nul.copilotKitMarkdownElement:not(:last-child),\nblockquote.copilotKitMarkdownElement:not(:last-child) {\n  margin-bottom: 1.25em;\n}\n\nblockquote.copilotKitMarkdownElement {\n  border-color: rgb(142, 142, 160);\n  border-left-width: 2px;\n  border-left-style: solid;\n  line-height: 1.2;\n  padding-left: 10px;\n}\n\nblockquote.copilotKitMarkdownElement p {\n  padding: 0.7em 0;\n}\n\nul.copilotKitMarkdownElement {\n  list-style-type: disc;\n  padding-left: 20px;\n  overflow: visible;\n}\n\nli.copilotKitMarkdownElement {\n  list-style-type: inherit;\n  list-style-position: outside;\n  margin-left: 0;\n  padding-left: 0;\n  position: relative;\n  overflow: visible;\n}\n\n.copilotKitCodeBlock {\n  position: relative;\n  width: 100%;\n  background-color: rgb(9 9 11);\n  border-radius: 0.375rem;\n}\n\n.copilotKitCodeBlockToolbar {\n  display: flex;\n  width: 100%;\n  align-items: center;\n  justify-content: space-between;\n  background-color: rgb(39 39 42);\n  padding-left: 1rem;\n  padding-top: 0.09rem;\n  padding-bottom: 0.09rem;\n  color: rgb(228, 228, 228);\n  border-top-left-radius: 0.375rem;\n  border-top-right-radius: 0.375rem;\n  font-family: sans-serif;\n}\n\n.copilotKitCodeBlockToolbarLanguage {\n  font-size: 0.75rem;\n  line-height: 1rem;\n  text-transform: lowercase;\n}\n\n.copilotKitCodeBlockToolbarButtons {\n  display: flex;\n  align-items: center;\n  margin-right: 0.25rem;\n  margin-left: 0.25rem;\n}\n\n.copilotKitCodeBlockToolbarButton {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  height: 2.5rem;\n  padding: 3px;\n  margin: 2px;\n}\n\n.copilotKitCodeBlockToolbarButton:hover {\n  background-color: rgb(55, 55, 58);\n}\n", ".copilotKitMessages footer .suggestions {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n}\n\n.copilotKitMessages footer h6 {\n  font-weight: 500;\n  font-size: 0.7rem;\n  margin-bottom: 8px;\n}\n\n.copilotKitMessages footer .suggestions .suggestion {\n  padding: 6px 10px;\n  font-size: 0.7rem;\n  border-radius: 15px;\n  border: 1px solid var(--copilot-kit-muted-color);\n  color: var(--copilot-kit-secondary-contrast-color);\n  box-shadow: 0 5px 5px 0px rgba(0,0,0,.01),0 2px 3px 0px rgba(0,0,0,.02);\n}\n\n.copilotKitMessages footer .suggestions .suggestion.loading {\n  padding: 0;\n  font-size: 0.7rem;\n  border: none;\n  color: var(--copilot-kit-secondary-contrast-color);\n}\n\n.copilotKitMessages footer .suggestions button {\n  transition: transform 0.3s ease;\n}\n\n.copilotKitMessages footer .suggestions button:not(:disabled):hover {\n  transform: scale(1.03);\n}\n\n.copilotKitMessages footer .suggestions button:disabled {\n  cursor: wait;\n}\n\n.copilotKitMessages footer .suggestions button svg {\n  margin-right: 6px;\n}\n", ".copilotKitChat {\n  z-index: 30;\n  line-height: 1.5;\n  -webkit-text-size-adjust: 100%;\n  -moz-tab-size: 4;\n  -o-tab-size: 4;\n  tab-size: 4;\n  background: var(--copilot-kit-background-color);\n  font-family:\n    ui-sans-serif,\n    system-ui,\n    -apple-system,\n    BlinkMacSystemFont,\n    \"Segoe UI\",\n    Roboto,\n    \"Helvetica Neue\",\n    Arial,\n    \"Noto Sans\",\n    sans-serif,\n    \"Apple Color Emoji\",\n    \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\",\n    \"Noto Color Emoji\";\n  font-feature-settings: normal;\n  font-variation-settings: normal;\n  touch-action: manipulation;\n  display: flex;\n  flex-direction: column;\n  /* height: 100%; */\n}\n\n.copilotKitChat svg {\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.copilotKitChat .copilotKitMessages {\n  flex-grow: 1;\n}\n", ".copilotKitDevConsole {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  margin: 0 15px;\n}\n\n.copilotKitDevConsole.copilotKitDevConsoleWarnOutdated {\n  background-color: var(--copilot-kit-dev-console-bg);\n}\n\n.copilotKitDevConsole .copilotKitVersionInfo {\n  display: flex;\n  position: absolute;\n  bottom: -25px;\n  padding: 3px 5px;\n  left: 0;\n  width: 100%;\n  justify-content: center;\n  gap: 10px;\n  font-size: 0.8rem;\n  align-items: center;\n  background: #ebb305;\n}\n\n.copilotKitDevConsole .copilotKitVersionInfo button {\n  font-size: 11px;\n  font-weight: normal;\n  font-family: monospace;\n  background-color: var(--copilot-kit-dev-console-bg);\n  border: 1px solid #979797;\n  padding: 1px 12px;\n  padding-left: 5px;\n  border-radius: 4px;\n  display: inline-block;\n  text-align: left;\n  overflow: hidden;\n  white-space: nowrap;\n  width: 260px;\n  text-overflow: ellipsis;\n}\n\n.copilotKitDevConsole .copilotKitVersionInfo aside {\n  display: inline;\n  font-weight: normal;\n  color: #7f7a7a;\n  margin-left: 5px;\n}\n\n.copilotKitDevConsole .copilotKitVersionInfo svg {\n  margin-left: 3px;\n  margin-top: -3px;\n}\n\n.copilotKitDevConsole .copilotKitDebugMenuTriggerButton {\n  font-size: 11px;\n  font-weight: bold;\n  display: flex;\n  padding: 0 10px;\n  height: 30px;\n  background-color: transparent;\n  border: 1px solid var(--copilot-kit-muted-color);\n  border-radius: 20px;\n  align-items: center;\n  justify-content: center;\n  outline: none;\n}\n\n.copilotKitDebugMenuTriggerButton.compact {\n  width: 35px;\n  color: var(--copilot-kit-dev-console-bg);\n  justify-content: center;\n  outline: none;\n  font-size: 8px;\n}\n\n.copilotKitDevConsole .copilotKitDebugMenuTriggerButton:hover {\n  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 85%, black);\n  color: var(--copilot-kit-dev-console-text);\n}\n\n.dark,\nhtml.dark,\nbody.dark,\n[data-theme=\"dark\"],\nhtml[style*=\"color-scheme: dark\"],\nbody[style*=\"color-scheme: dark\"] .copilotKitDevConsole .copilotKitDebugMenuTriggerButton {\n  color: white;\n}\n\n.dark,\nhtml.dark,\nbody.dark,\n[data-theme=\"dark\"],\nhtml[style*=\"color-scheme: dark\"],\nbody[style*=\"color-scheme: dark\"] .copilotKitDevConsole .copilotKitDebugMenuTriggerButton:hover {\n  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 20%, black);\n}\n\n.copilotKitDevConsole .copilotKitDebugMenuTriggerButton > svg {\n  margin-left: 10px;\n}\n\n.copilotKitDebugMenu {\n  --copilot-kit-dev-console-border: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 80%, black);\n  margin-top: 2px;\n  border-radius: 6px;\n  background-color: var(--copilot-kit-dev-console-bg);\n  border: 1px solid var(--copilot-kit-dev-console-border);\n  padding: 0.25rem;\n  outline: none;\n  font-size: 13px;\n}\n\n.copilotKitDebugMenuItem {\n  padding-top: 3px;\n  padding-bottom: 3px;\n  padding-left: 10px;\n  padding-right: 10px;\n  display: block;\n  width: 100%;\n  text-align: left;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: var(--copilot-kit-dev-console-text);\n}\n\n.copilotKitDebugMenuItem:hover {\n  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 95%, black);\n  border-radius: 4px;\n}\n\n.copilotKitDebugMenu[data-closed] {\n  transform: scale(0.95); /* data-[closed]:scale-95 */\n  opacity: 0; /* data-[closed]:opacity-0 */\n}\n\n.copilotKitDebugMenu hr {\n  height: 1px;\n  border: none; /* Remove 3D look */\n  background-color: var(--copilot-kit-dev-console-border);\n  margin: 0.25rem;\n}\n\n.copilotKitHelpModal {\n  background-color: var(--copilot-kit-dev-console-bg);\n  color: var(--copilot-kit-dev-console-text);\n}\n\n.copilotKitHelpItemButton {\n  display: block;\n  text-align: center;\n  width: 100%;\n  padding: 4px 6px;\n  border-radius: 15px;\n  font-size: 0.8rem;\n  border: 1px solid var(--copilot-kit-muted-color);\n  color: var(--copilot-kit-dev-console-text);\n  box-shadow: 0 5px 5px 0px rgba(0,0,0,.01),0 2px 3px 0px rgba(0,0,0,.02);\n  background-color: var(--copilot-kit-dev-console-bg);\n}\n.copilotKitHelpItemButton:hover {\n  background-color: color-mix(in srgb, var(--copilot-kit-dev-console-bg) 95%, black);\n}\n\n", "/* Default styles for the ResponseRenderer component */\n\n.copilotkit-response {\n  text-align: right;\n}\n\n.copilotkit-response-content {\n  margin-bottom: 0.5rem;\n  font-size: 0.875rem;\n  color: #4b5563;\n  background-color: #f9fafb;\n  padding: 0.5rem;\n  border-radius: 0.25rem;\n  text-align: left;\n}\n\n.copilotkit-response-actions {\n  display: inline-flex;\n  flex-direction: column;\n  align-items: flex-end;\n}\n\n.copilotkit-response-label {\n  font-size: 0.75rem;\n  color: #6b7280;\n  margin-bottom: 0.25rem;\n  display: flex;\n  align-items: center;\n}\n\n.copilotkit-toggle-button {\n  margin-right: 0.25rem;\n  background: none;\n  border: none;\n  padding: 0;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.copilotkit-icon {\n  height: 0.75rem;\n  width: 0.75rem;\n  color: #6b7280;\n}\n\n.copilotkit-response-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.copilotkit-response-button {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  background-color: #f3f4f6;\n  color: #4b5563;\n  border-radius: 0.25rem;\n  border: none;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.copilotkit-response-button:hover {\n  background-color: #e5e7eb;\n}\n\n.copilotkit-response-button:focus {\n  outline: none;\n}\n\n.copilotkit-response-completed-feedback {\n  background-color: #f9fafb;\n  padding: 0.5rem;\n  border-radius: 0.375rem;\n  display: inline-flex;\n  align-items: center;\n}\n\n.copilotkit-response-completed-feedback span {\n  color: #4b5563;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n/* DefaultStateRenderer styles */\n\n.copilotkit-state {\n  font-size: 0.875rem;\n  margin-bottom: 1rem;\n}\n\n.copilotkit-state-header {\n  display: flex;\n  align-items: center;\n  gap: 0.25rem;\n  cursor: pointer;\n  user-select: none;\n  margin-bottom: 0.25rem;\n}\n\n.copilotkit-state-label {\n  color: #4b5563;\n  font-size: 0.875rem;\n}\n\n.copilotkit-state-label-loading {\n  display: inline-flex;\n  align-items: center;\n  animation: pulse 1.5s infinite;\n}\n\n.copilotkit-state-content {\n  padding-left: 1rem;\n  max-height: 250px;\n  overflow: auto;\n  padding-top: 0.375rem;\n  border-left: 1px solid #e5e7eb;\n  margin-left: 0.375rem;\n}\n\n.copilotkit-state-item {\n  padding: 0.25rem 0;\n  margin-bottom: 0.25rem;\n  transition: all 0.3s ease;\n}\n\n.copilotkit-state-item-newest {\n  animation: appear 0.5s ease-out;\n}\n\n.copilotkit-state-item-header {\n  font-size: 0.75rem;\n  opacity: 0.7;\n}\n\n.copilotkit-state-item-thought {\n  margin-top: 0.125rem;\n  font-size: 0.75rem;\n  opacity: 0.8;\n}\n\n.copilotkit-state-item-result {\n  margin-top: 0.125rem;\n  font-size: 0.75rem;\n}\n\n.copilotkit-state-item-description {\n  margin-top: 0.125rem;\n  font-size: 0.75rem;\n  opacity: 0.8;\n}\n\n.copilotkit-state-empty {\n  padding: 0.25rem 0;\n  font-size: 0.75rem;\n  opacity: 0.7;\n}\n\n.copilotkit-skeleton {\n  padding: 0.125rem 0;\n  animation: pulse 1.5s infinite;\n}\n\n.copilotkit-skeleton-header {\n  display: flex;\n  justify-content: space-between;\n}\n\n.copilotkit-skeleton-title {\n  height: 0.625rem;\n  width: 4rem;\n  background-color: #e5e7eb;\n  border-radius: 0.25rem;\n}\n\n.copilotkit-skeleton-subtitle {\n  height: 0.5rem;\n  width: 2rem;\n  background-color: #e5e7eb;\n  border-radius: 0.25rem;\n}\n\n.copilotkit-skeleton-content {\n  margin-top: 0.125rem;\n  height: 1.5rem;\n  background-color: #e5e7eb;\n  border-radius: 0.25rem;\n}\n\n.copilotkit-loader {\n  animation: spin 1.5s linear infinite;\n}\n\n.copilotkit-spinner {\n  animation: spin 1.5s linear infinite;\n}\n\n/* Animations */\n@keyframes appear {\n  0% {\n    opacity: 0;\n    transform: translateY(8px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes pulse {\n  0%,\n  100% {\n    opacity: 0.4;\n  }\n  50% {\n    opacity: 1;\n  }\n}\n\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n/* Dark mode styles */\n@media (prefers-color-scheme: dark) {\n  /* DefaultResponseRenderer dark styles */\n  .copilotkit-response-content {\n    color: #9ca3af;\n    background-color: #1f2937;\n  }\n\n  .copilotkit-response-label {\n    color: #9ca3af;\n  }\n\n  .copilotkit-icon {\n    color: #9ca3af;\n  }\n\n  .copilotkit-response-button {\n    background-color: #1f2937;\n    color: #d1d5db;\n  }\n\n  .copilotkit-response-button:hover {\n    background-color: #374151;\n  }\n\n  .copilotkit-response-completed-feedback {\n    background-color: #1f2937;\n  }\n\n  .copilotkit-response-completed-feedback span {\n    color: #e5e7eb;\n  }\n\n  /* DefaultStateRenderer dark styles */\n  .copilotkit-state-label {\n    color: #d1d5db;\n  }\n\n  .copilotkit-state-content {\n    border-left-color: #374151;\n  }\n\n  .copilotkit-skeleton-title,\n  .copilotkit-skeleton-subtitle,\n  .copilotkit-skeleton-content {\n    background-color: #374151;\n  }\n}\n"], "mappings": ";AAMA;AAGE;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAIA;AAEA;AAEA;AAEA;AACA;AAAA;AAIF;AAAA;AAAA;AAAA;AAAA;AAAA;AAOE;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAGA;AAEA;AAEA;AAAA;;;AClEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;;;ACpBF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACE;AAAA;AAAA;;;AC/BJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAIF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAIF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;;;ACjEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACE;AACA;AACA;AACA;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;;;AC/DF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAIF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AAAA;;;ACrJF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AAGE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAIF;AACE;AACE;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AAAA;AAEI;AAAA;AAAA;AAGA;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AAAA;AAEI;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;;;ACvLF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACzDJ;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AAAA;AAGI;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AAGA;AAAA;AAAA;AAIJ;AAAA;AAEI;AAAA;AAAA;AAIJ;AAAA;AAEI;AAAA;AAAA;;;AChCJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AACA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAKE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;;;AC5IF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;;;ACzCF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AACA;AACA;AAAA;AAIF;AACE;AACA;AAAA;AAGF;AACE;AAAA;;;ACrCF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;;;ACjKF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAKF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAIF;AAAA;AAEI;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AAIJ;AAAA;AAGI;AAAA;AAAA;AAGA;AAAA;AAAA;AAIJ;AAAA;AAEI;AAAA;AAAA;AAGA;AAAA;AAAA;AAKJ;AAEE;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAIF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AAAA;AAAA;AAGE;AAAA;AAAA;", "names": []}