import {
  AssistantMessage
} from "./chunk-XMNTLIK5.mjs";
import {
  __objRest
} from "./chunk-MRXNTQOX.mjs";

// src/components/chat/messages/RenderResultMessage.tsx
import { jsx } from "react/jsx-runtime";
function RenderResultMessage(_a) {
  var _b = _a, {
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "AssistantMessage"
  ]);
  const { message, inProgress, index, isCurrentMessage } = props;
  if (message.isResultMessage() && inProgress && isCurrentMessage) {
    return /* @__PURE__ */ jsx(
      AssistantMessage2,
      {
        "data-message-role": "assistant",
        rawData: message,
        isLoading: true,
        isGenerating: true
      },
      index
    );
  } else {
    return null;
  }
}

export {
  RenderResultMessage
};
//# sourceMappingURL=chunk-6VUDBDUN.mjs.map