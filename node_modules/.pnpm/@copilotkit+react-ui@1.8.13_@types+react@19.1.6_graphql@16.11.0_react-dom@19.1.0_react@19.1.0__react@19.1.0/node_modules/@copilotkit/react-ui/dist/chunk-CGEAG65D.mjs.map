{"version": 3, "sources": ["../src/components/chat/PoweredByTag.tsx"], "sourcesContent": ["import React from \"react\";\nimport { useDarkMode } from \"../../hooks/use-dark-mode\";\n\nexport function PoweredByTag({ showPoweredBy = true }: { showPoweredBy?: boolean }) {\n  const isDark = useDarkMode();\n\n  if (!showPoweredBy) {\n    return null;\n  }\n\n  const poweredByStyle = {\n    visibility: \"visible\",\n    display: \"block\",\n    position: \"static\",\n    textAlign: \"center\",\n    fontSize: \"12px\",\n    padding: \"3px 0\",\n    color: isDark ? \"rgb(69, 69, 69)\" : \"rgb(214, 214, 214)\",\n  };\n\n  return (\n    <div>\n      {/*@ts-expect-error -- expecting position not to be a string, but it can be.*/}\n      <p className=\"poweredBy\" style={poweredByStyle}>\n        Powered by CopilotKit\n      </p>\n    </div>\n  );\n}\n"], "mappings": ";;;;;AAuBM;AApBC,SAAS,aAAa,EAAE,gBAAgB,KAAK,GAAgC;AAClF,QAAM,SAAS,YAAY;AAE3B,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB;AAAA,IACrB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO,SAAS,oBAAoB;AAAA,EACtC;AAEA,SACE,oBAAC,SAEC,8BAAC,OAAE,WAAU,aAAY,OAAO,gBAAgB,mCAEhD,GACF;AAEJ;", "names": []}