{"name": "@copilotkit/runtime", "private": false, "homepage": "https://github.com/CopilotKit/CopilotKit", "repository": {"type": "git", "url": "https://github.com/CopilotKit/CopilotKit.git"}, "publishConfig": {"access": "public"}, "version": "1.8.13", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": "./dist/index.js"}, "types": "./dist/index.d.ts", "license": "MIT", "devDependencies": {"@jest/globals": "^29.7.0", "@swc/core": "1.5.28", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^18.11.17", "@whatwg-node/server": "^0.9.34", "eslint": "^8.56.0", "jest": "^29.6.4", "nodemon": "^3.1.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsup": "^6.7.0", "typescript": "^5.2.3", "zod-to-json-schema": "^3.23.5", "eslint-config-custom": "1.4.6", "tsconfig": "1.4.6"}, "dependencies": {"@ag-ui/client": "0.0.28", "@ag-ui/core": "0.0.28", "@ag-ui/encoder": "0.0.28", "@ag-ui/proto": "0.0.28", "@anthropic-ai/sdk": "^0.27.3", "@graphql-yoga/plugin-defer-stream": "^3.3.1", "@langchain/community": "^0.3.29", "@langchain/core": "^0.3.38", "@langchain/google-gauth": "^0.1.0", "@langchain/langgraph-sdk": "^0.0.70", "@langchain/openai": "^0.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "express": "^4.19.2", "graphql": "^16.8.1", "graphql-scalars": "^1.23.0", "graphql-yoga": "^5.3.1", "groq-sdk": "^0.5.0", "langchain": "^0.3.3", "openai": "^4.85.1", "partial-json": "^0.1.7", "pino": "^9.2.0", "pino-pretty": "^11.2.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "type-graphql": "2.0.0-rc.1", "zod": "^3.23.3", "@copilotkit/shared": "1.8.13"}, "peerDependencies": {"@ag-ui/client": ">=0.0.28", "@ag-ui/core": ">=0.0.28", "@ag-ui/encoder": ">=0.0.28", "@ag-ui/proto": ">=0.0.28"}, "keywords": ["copilotkit", "copilot", "react", "nextjs", "nodejs", "ai", "assistant", "javascript", "automation", "textarea"], "scripts": {"build": "tsup --onSuccess \"pnpm run generate-graphql-schema\"", "dev": "tsup --watch --onSuccess \"pnpm run generate-graphql-schema\"", "test": "jest --passWithNoTests", "check-types": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next && rm -rf __snapshots__", "generate-graphql-schema": "rm -rf __snapshots__ && ts-node ./scripts/generate-gql-schema.ts", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}}