# Authentication Code Review Standards

## 🎯 **Overview**

This document establishes code review standards specifically for authentication-related code to ensure security, consistency, and maintainability across the PI Lawyer AI application.

## ✅ **Code Review Checklist**

### **🔐 Authentication & Authorization**

#### **API Routes**
- [ ] **Authentication Check**: Every protected API route uses `requireAuth(request)`
- [ ] **Permission Validation**: Appropriate permission checks using `checkPermission()` or `hasRole()`
- [ ] **Tenant Isolation**: Database queries include tenant filtering
- [ ] **Error Handling**: Proper error responses with appropriate status codes
- [ ] **JWT Validation**: Manual JWT validation uses `validateJwt()` when needed

```typescript
// ✅ GOOD: Complete authentication pattern
export async function GET(request: NextRequest) {
  const authResult = await requireAuth(request);
  if (!authResult.success) {
    return NextResponse.json({ error: authResult.error }, { status: authResult.status });
  }
  
  const { user, supabase } = authResult;
  
  if (!checkPermission(user, 'read:cases')) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }
  
  const { data } = await supabase
    .schema('tenants')
    .from('cases')
    .select('*')
    .eq('tenant_id', user.tenantId);
    
  return NextResponse.json(data);
}

// ❌ BAD: Missing authentication
export async function GET() {
  const { data } = await supabase.from('cases').select('*');
  return NextResponse.json(data);
}
```

#### **Database Queries**
- [ ] **Schema Specification**: All tenant data queries use `.schema('tenants')`
- [ ] **Tenant Filtering**: Queries include `.eq('tenant_id', user.tenantId)`
- [ ] **Security Schema**: Security events use `.schema('security')`
- [ ] **Public Schema**: Legal research uses `.schema('public')` when appropriate

```typescript
// ✅ GOOD: Proper schema targeting
const { data } = await supabase
  .schema('tenants')
  .from('cases')
  .select('*')
  .eq('tenant_id', user.tenantId);

// ❌ BAD: Missing schema and tenant isolation
const { data } = await supabase
  .from('cases')
  .select('*');
```

#### **Client-Side Components**
- [ ] **Hook Usage**: Uses `useAuth()`, `useRequireAuth()`, or `useRequireRole()` appropriately
- [ ] **Loading States**: Handles `isLoading` state before checking authentication
- [ ] **Error Boundaries**: Proper error handling for authentication failures
- [ ] **Type Safety**: Uses proper TypeScript types for user and profile objects

```typescript
// ✅ GOOD: Proper hook usage with loading state
function ProtectedComponent() {
  const { user, isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please log in</div>;
  
  return <div>Welcome, {user?.email}</div>;
}

// ❌ BAD: Missing loading state check
function ProtectedComponent() {
  const { user, isAuthenticated } = useAuth();
  
  if (!isAuthenticated) return <div>Please log in</div>; // Triggers during loading!
  
  return <div>Welcome, {user?.email}</div>;
}
```

### **🛡️ Security Requirements**

#### **Input Validation**
- [ ] **Parameter Validation**: All user inputs are validated
- [ ] **SQL Injection Prevention**: Uses parameterized queries (Supabase handles this)
- [ ] **XSS Prevention**: User data is properly escaped in UI
- [ ] **CSRF Protection**: API routes use proper CSRF protection

#### **Error Handling**
- [ ] **No Information Leakage**: Error messages don't reveal sensitive information
- [ ] **Consistent Responses**: Authentication errors use standard format
- [ ] **Logging**: Security events are properly logged
- [ ] **Rate Limiting**: Consider rate limiting for authentication endpoints

```typescript
// ✅ GOOD: Secure error handling
try {
  const payload = await validateJwt(token);
} catch (error) {
  // Log detailed error for debugging
  console.error('JWT validation failed:', {
    error: error.message,
    userId: payload?.sub,
    timestamp: new Date().toISOString()
  });
  
  // Return generic error to client
  return NextResponse.json(
    { error: 'Authentication failed' }, 
    { status: 401 }
  );
}

// ❌ BAD: Information leakage
catch (error) {
  return NextResponse.json(
    { error: error.message }, // Reveals internal details!
    { status: 401 }
  );
}
```

### **📝 Code Quality Standards**

#### **TypeScript Usage**
- [ ] **Strict Types**: No `any` types in authentication code
- [ ] **Null Checks**: Proper null/undefined handling
- [ ] **Interface Compliance**: Uses defined interfaces (`AuthUser`, `UserProfile`, etc.)
- [ ] **Generic Types**: Proper use of generic types where applicable

#### **Function Design**
- [ ] **Single Responsibility**: Functions have clear, single purposes
- [ ] **Pure Functions**: Utility functions are pure when possible
- [ ] **Error Propagation**: Errors are properly propagated or handled
- [ ] **Documentation**: Complex authentication logic is documented

#### **Performance Considerations**
- [ ] **Caching**: Appropriate caching of user data and permissions
- [ ] **Database Efficiency**: Queries are optimized and use proper indexes
- [ ] **Memory Usage**: No memory leaks in authentication state management
- [ ] **Bundle Size**: Authentication code doesn't bloat client bundle

### **🧪 Testing Requirements**

#### **Test Coverage**
- [ ] **Unit Tests**: All authentication utilities have unit tests
- [ ] **Integration Tests**: API routes have integration tests
- [ ] **Security Tests**: Edge cases and security scenarios are tested
- [ ] **Mock Usage**: Proper mocking of external dependencies

#### **Test Quality**
- [ ] **Descriptive Names**: Test names clearly describe what's being tested
- [ ] **Isolated Tests**: Tests don't depend on each other
- [ ] **Edge Cases**: Tests cover error conditions and edge cases
- [ ] **Performance Tests**: Critical paths have performance tests

## 🚨 **Common Anti-Patterns to Reject**

### **❌ Authentication Anti-Patterns**

```typescript
// ❌ DON'T: Skip authentication checks
export async function DELETE() {
  await supabase.from('cases').delete().eq('id', id);
}

// ❌ DON'T: Client-side only permission checks
function DeleteButton() {
  const { user } = useAuth();
  if (user.role === 'admin') { // Only client-side check!
    return <button onClick={deleteCase}>Delete</button>;
  }
}

// ❌ DON'T: Hardcode tenant IDs
const cases = await supabase
  .from('cases')
  .select('*')
  .eq('tenant_id', 'hardcoded-tenant-id');

// ❌ DON'T: Ignore loading states
function Component() {
  const { isAuthenticated } = useAuth();
  if (!isAuthenticated) router.push('/login'); // Triggers during loading!
}

// ❌ DON'T: Use any types
function checkAuth(user: any) { // Should be AuthUser | null
  return user?.role === 'admin';
}
```

### **❌ Database Anti-Patterns**

```typescript
// ❌ DON'T: Query wrong schema
const cases = await supabase.from('cases').select('*'); // Queries public.cases!

// ❌ DON'T: Skip tenant isolation
const cases = await supabase
  .schema('tenants')
  .from('cases')
  .select('*'); // Missing .eq('tenant_id', user.tenantId)

// ❌ DON'T: Use raw SQL without parameterization
const query = `SELECT * FROM cases WHERE id = '${caseId}'`; // SQL injection risk!
```

## 📋 **Review Process**

### **Pre-Review Checklist**

Before submitting authentication-related code for review:

1. **Run Tests**: Ensure all tests pass
2. **Type Check**: Run TypeScript compiler with strict mode
3. **Security Scan**: Review code for security vulnerabilities
4. **Performance Check**: Verify no performance regressions
5. **Documentation**: Update documentation if patterns change

### **Review Priorities**

When reviewing authentication code, prioritize in this order:

1. **🔴 Critical**: Security vulnerabilities, authentication bypasses
2. **🟡 High**: Permission logic errors, tenant isolation issues
3. **🟢 Medium**: Code quality, performance optimizations
4. **🔵 Low**: Style issues, minor refactoring opportunities

### **Approval Criteria**

Code should only be approved if:

- [ ] All security requirements are met
- [ ] Authentication patterns are consistent
- [ ] Tests provide adequate coverage
- [ ] Documentation is updated if needed
- [ ] No anti-patterns are present

## 🔧 **Automated Checks**

### **ESLint Rules**

Consider adding these custom ESLint rules:

```javascript
// .eslintrc.js
module.exports = {
  rules: {
    // Require authentication in API routes
    'custom/require-auth-in-api': 'error',
    
    // Require schema specification in database queries
    'custom/require-schema-in-queries': 'error',
    
    // Require tenant isolation in tenant queries
    'custom/require-tenant-isolation': 'error',
    
    // Prevent any types in auth code
    '@typescript-eslint/no-explicit-any': 'error',
  }
};
```

### **Pre-commit Hooks**

```bash
# .husky/pre-commit
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Run type checking
npm run type-check

# Run authentication-specific tests
npm run test -- src/lib/auth/

# Run security linting
npm run lint:security
```

### **CI/CD Pipeline Checks**

```yaml
# .github/workflows/auth-checks.yml
name: Authentication Security Checks

on: [pull_request]

jobs:
  auth-security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run authentication tests
        run: npm run test -- src/lib/auth/
      - name: Security audit
        run: npm audit --audit-level moderate
      - name: Type checking
        run: npm run type-check:strict
```

## 📚 **Resources**

- [Authentication Documentation](./authentication.md)
- [API Reference](./api-reference.md)
- [Troubleshooting Guide](./troubleshooting.md)
- [Security Best Practices](./security-guide.md)

## 🔄 **Review Template**

Use this template for authentication code reviews:

```markdown
## Authentication Review Checklist

### Security ✅/❌
- [ ] Authentication checks present
- [ ] Permission validation implemented
- [ ] Tenant isolation enforced
- [ ] Error handling secure

### Code Quality ✅/❌
- [ ] TypeScript types correct
- [ ] Functions well-designed
- [ ] Performance optimized
- [ ] Documentation updated

### Testing ✅/❌
- [ ] Unit tests present
- [ ] Edge cases covered
- [ ] Security scenarios tested
- [ ] Integration tests added

### Comments
[Detailed feedback here]

### Approval Status
- [ ] Approved
- [ ] Needs changes
- [ ] Security review required
```
