{"version": 3, "sources": ["../../../src/components/copilot-provider/copilot-messages.tsx", "../../../src/context/copilot-messages-context.tsx", "../../../src/context/copilot-context.tsx"], "sourcesContent": ["/**\n * An internal context to separate the messages state (which is constantly changing) from the rest of CopilotKit context\n */\n\nimport { ReactNode, useEffect, useState, useRef } from \"react\";\nimport { CopilotMessagesContext } from \"../../context/copilot-messages-context\";\nimport { loadMessagesFromJsonRepresentation, Message } from \"@copilotkit/runtime-client-gql\";\nimport { useCopilotContext } from \"../../context/copilot-context\";\n\nexport function CopilotMessages({ children }: { children: ReactNode }) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const lastLoadedThreadId = useRef<string>();\n  const lastLoadedAgentName = useRef<string>();\n  const lastLoadedMessages = useRef<string>();\n\n  const { threadId, agentSession, runtimeClient } = useCopilotContext();\n\n  useEffect(() => {\n    if (!threadId || threadId === lastLoadedThreadId.current) return;\n    if (\n      threadId === lastLoadedThreadId.current &&\n      agentSession?.agentName === lastLoadedAgentName.current\n    ) {\n      return;\n    }\n\n    const fetchMessages = async () => {\n      if (!agentSession?.agentName) return;\n\n      const result = await runtimeClient.loadAgentState({\n        threadId,\n        agentName: agentSession?.agentName,\n      });\n\n      const newMessages = result.data?.loadAgentState?.messages;\n      if (newMessages === lastLoadedMessages.current) return;\n\n      if (result.data?.loadAgentState?.threadExists) {\n        lastLoadedMessages.current = newMessages;\n        lastLoadedThreadId.current = threadId;\n        lastLoadedAgentName.current = agentSession?.agentName;\n\n        const messages = loadMessagesFromJsonRepresentation(JSON.parse(newMessages || \"[]\"));\n        setMessages(messages);\n      }\n    };\n    void fetchMessages();\n  }, [threadId, agentSession?.agentName]);\n\n  return (\n    <CopilotMessagesContext.Provider\n      value={{\n        messages,\n        setMessages,\n      }}\n    >\n      {children}\n    </CopilotMessagesContext.Provider>\n  );\n}\n", "/**\n * An internal context to separate the messages state (which is constantly changing) from the rest of CopilotKit context\n */\n\nimport { Message } from \"@copilotkit/runtime-client-gql\";\nimport React from \"react\";\n\nexport interface CopilotMessagesContextParams {\n  messages: Message[];\n  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;\n}\n\nconst emptyCopilotContext: CopilotMessagesContextParams = {\n  messages: [],\n  setMessages: () => [],\n};\n\nexport const CopilotMessagesContext =\n  React.createContext<CopilotMessagesContextParams>(emptyCopilotContext);\n\nexport function useCopilotMessagesContext(): CopilotMessagesContextParams {\n  const context = React.useContext(CopilotMessagesContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\n      \"A messages consuming component was not wrapped with `<CopilotMessages> {...} </CopilotMessages>`\",\n    );\n  }\n  return context;\n}\n", "import { CopilotCloudConfig, FunctionCallHandler } from \"@copilotkit/shared\";\nimport {\n  ActionRenderProps,\n  CatchAllActionRenderProps,\n  FrontendAction,\n} from \"../types/frontend-action\";\nimport React from \"react\";\nimport { TreeNodeId } from \"../hooks/use-tree\";\nimport { DocumentPointer } from \"../types\";\nimport { CopilotChatSuggestionConfiguration } from \"../types/chat-suggestion-configuration\";\nimport { CoAgentStateRender, CoAgentStateRenderProps } from \"../types/coagent-action\";\nimport { CoagentState } from \"../types/coagent-state\";\nimport {\n  CopilotRuntimeClient,\n  ExtensionsInput,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { Agent } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetter,\n} from \"../types/interrupt-action\";\n\n/**\n * Interface for the configuration of the Copilot API.\n */\nexport interface CopilotApiConfig {\n  /**\n   * The public API key for Copilot Cloud.\n   */\n  publicApiKey?: string;\n\n  /**\n   * The configuration for Copilot Cloud.\n   */\n  cloud?: CopilotCloudConfig;\n\n  /**\n   * The endpoint for the chat API.\n   */\n  chatApiEndpoint: string;\n\n  /**\n   * The endpoint for the Copilot transcribe audio service.\n   */\n  transcribeAudioUrl?: string;\n\n  /**\n   * The endpoint for the Copilot text to speech service.\n   */\n  textToSpeechUrl?: string;\n\n  /**\n   * additional headers to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'Authorization': 'Bearer your_token_here'\n   * }\n   * ```\n   */\n  headers: Record<string, string>;\n\n  /**\n   * Custom properties to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'user_id': 'user_id'\n   * }\n   * ```\n   */\n  properties?: Record<string, any>;\n\n  /**\n   * Indicates whether the user agent should send or receive cookies from the other domain\n   * in the case of cross-origin requests.\n   */\n  credentials?: RequestCredentials;\n\n  /**\n   * Optional configuration for connecting to Model Context Protocol (MCP) servers.\n   * This is typically derived from the CopilotKitProps and used internally.\n   * @experimental\n   */\n  mcpServers?: Array<{ endpoint: string; apiKey?: string }>;\n}\n\nexport type InChatRenderFunction<TProps = ActionRenderProps<any> | CatchAllActionRenderProps<any>> =\n  (props: TProps) => string | JSX.Element;\nexport type CoagentInChatRenderFunction = (\n  props: CoAgentStateRenderProps<any>,\n) => string | JSX.Element | undefined | null;\n\nexport interface ChatComponentsCache {\n  actions: Record<string, InChatRenderFunction | string>;\n  coAgentStateRenders: Record<string, CoagentInChatRenderFunction | string>;\n}\n\nexport interface AgentSession {\n  agentName: string;\n  threadId?: string;\n  nodeName?: string;\n}\n\nexport interface AuthState {\n  status: \"authenticated\" | \"unauthenticated\";\n  authHeaders: Record<string, string>;\n  userId?: string;\n  metadata?: Record<string, any>;\n}\n\nexport type ActionName = string;\n\nexport interface CopilotContextParams {\n  // function-calling\n  actions: Record<string, FrontendAction<any>>;\n  setAction: (id: string, action: FrontendAction<any>) => void;\n  removeAction: (id: string) => void;\n\n  // coagent actions\n  coAgentStateRenders: Record<string, CoAgentStateRender<any>>;\n  setCoAgentStateRender: (id: string, stateRender: CoAgentStateRender<any>) => void;\n  removeCoAgentStateRender: (id: string) => void;\n\n  chatComponentsCache: React.RefObject<ChatComponentsCache>;\n\n  getFunctionCallHandler: (\n    customEntryPoints?: Record<string, FrontendAction<any>>,\n  ) => FunctionCallHandler;\n\n  // text context\n  addContext: (context: string, parentId?: string, categories?: string[]) => TreeNodeId;\n  removeContext: (id: TreeNodeId) => void;\n  getContextString: (documents: DocumentPointer[], categories: string[]) => string;\n\n  // document context\n  addDocumentContext: (documentPointer: DocumentPointer, categories?: string[]) => TreeNodeId;\n  removeDocumentContext: (documentId: string) => void;\n  getDocumentsContext: (categories: string[]) => DocumentPointer[];\n\n  isLoading: boolean;\n  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;\n\n  chatSuggestionConfiguration: { [key: string]: CopilotChatSuggestionConfiguration };\n  addChatSuggestionConfiguration: (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => void;\n  removeChatSuggestionConfiguration: (id: string) => void;\n\n  chatInstructions: string;\n  setChatInstructions: React.Dispatch<React.SetStateAction<string>>;\n\n  additionalInstructions?: string[];\n  setAdditionalInstructions: React.Dispatch<React.SetStateAction<string[]>>;\n\n  // api endpoints\n  copilotApiConfig: CopilotApiConfig;\n\n  showDevConsole: boolean | \"auto\";\n\n  // agents\n  coagentStates: Record<string, CoagentState>;\n  setCoagentStates: React.Dispatch<React.SetStateAction<Record<string, CoagentState>>>;\n  coagentStatesRef: React.RefObject<Record<string, CoagentState>>;\n  setCoagentStatesWithRef: (\n    value:\n      | Record<string, CoagentState>\n      | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n  ) => void;\n\n  agentSession: AgentSession | null;\n  setAgentSession: React.Dispatch<React.SetStateAction<AgentSession | null>>;\n\n  agentLock: string | null;\n\n  threadId: string;\n  setThreadId: React.Dispatch<React.SetStateAction<string>>;\n\n  runId: string | null;\n  setRunId: React.Dispatch<React.SetStateAction<string | null>>;\n\n  // The chat abort controller can be used to stop generation globally,\n  // i.e. when using `stop()` from `useChat`\n  chatAbortControllerRef: React.MutableRefObject<AbortController | null>;\n\n  // runtime\n  runtimeClient: CopilotRuntimeClient;\n\n  /**\n   * The forwarded parameters to use for the task.\n   */\n  forwardedParameters?: Pick<ForwardedParametersInput, \"temperature\">;\n  availableAgents: Agent[];\n\n  /**\n   * The auth states for the CopilotKit.\n   */\n  authStates_c?: Record<ActionName, AuthState>;\n  setAuthStates_c?: React.Dispatch<React.SetStateAction<Record<ActionName, AuthState>>>;\n\n  /**\n   * The auth config for the CopilotKit.\n   */\n  authConfig_c?: {\n    SignInComponent: React.ComponentType<{\n      onSignInComplete: (authState: AuthState) => void;\n    }>;\n  };\n\n  extensions: ExtensionsInput;\n  setExtensions: React.Dispatch<React.SetStateAction<ExtensionsInput>>;\n  langGraphInterruptAction: LangGraphInterruptAction | null;\n  setLangGraphInterruptAction: LangGraphInterruptActionSetter;\n  removeLangGraphInterruptAction: () => void;\n}\n\nconst emptyCopilotContext: CopilotContextParams = {\n  actions: {},\n  setAction: () => {},\n  removeAction: () => {},\n\n  coAgentStateRenders: {},\n  setCoAgentStateRender: () => {},\n  removeCoAgentStateRender: () => {},\n\n  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },\n  getContextString: (documents: DocumentPointer[], categories: string[]) =>\n    returnAndThrowInDebug(\"\"),\n  addContext: () => \"\",\n  removeContext: () => {},\n\n  getFunctionCallHandler: () => returnAndThrowInDebug(async () => {}),\n\n  isLoading: false,\n  setIsLoading: () => returnAndThrowInDebug(false),\n\n  chatInstructions: \"\",\n  setChatInstructions: () => returnAndThrowInDebug(\"\"),\n\n  additionalInstructions: [],\n  setAdditionalInstructions: () => returnAndThrowInDebug([]),\n\n  getDocumentsContext: (categories: string[]) => returnAndThrowInDebug([]),\n  addDocumentContext: () => returnAndThrowInDebug(\"\"),\n  removeDocumentContext: () => {},\n  runtimeClient: {} as any,\n\n  copilotApiConfig: new (class implements CopilotApiConfig {\n    get chatApiEndpoint(): string {\n      throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n    }\n\n    get headers(): Record<string, string> {\n      return {};\n    }\n    get body(): Record<string, any> {\n      return {};\n    }\n  })(),\n\n  chatSuggestionConfiguration: {},\n  addChatSuggestionConfiguration: () => {},\n  removeChatSuggestionConfiguration: () => {},\n  showDevConsole: \"auto\",\n  coagentStates: {},\n  setCoagentStates: () => {},\n  coagentStatesRef: { current: {} },\n  setCoagentStatesWithRef: () => {},\n  agentSession: null,\n  setAgentSession: () => {},\n  forwardedParameters: {},\n  agentLock: null,\n  threadId: \"\",\n  setThreadId: () => {},\n  runId: null,\n  setRunId: () => {},\n  chatAbortControllerRef: { current: null },\n  availableAgents: [],\n  extensions: {},\n  setExtensions: () => {},\n  langGraphInterruptAction: null,\n  setLangGraphInterruptAction: () => null,\n  removeLangGraphInterruptAction: () => null,\n};\n\nexport const CopilotContext = React.createContext<CopilotContextParams>(emptyCopilotContext);\n\nexport function useCopilotContext(): CopilotContextParams {\n  const context = React.useContext(CopilotContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n  }\n  return context;\n}\n\nfunction returnAndThrowInDebug<T>(_value: T): T {\n  throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,IAAAA,gBAAuD;;;ACCvD,mBAAkB;AAOlB,IAAM,sBAAoD;AAAA,EACxD,UAAU,CAAC;AAAA,EACX,aAAa,MAAM,CAAC;AACtB;AAEO,IAAM,yBACX,aAAAC,QAAM,cAA4C,mBAAmB;;;ADZvE,gCAA4D;;;AEA5D,IAAAC,gBAAkB;AAsNlB,IAAMC,uBAA4C;AAAA,EAChD,SAAS,CAAC;AAAA,EACV,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,cAAc,MAAM;AAAA,EAAC;AAAA,EAErB,qBAAqB,CAAC;AAAA,EACtB,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,0BAA0B,MAAM;AAAA,EAAC;AAAA,EAEjC,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,qBAAqB,CAAC,EAAE,EAAE;AAAA,EACzE,kBAAkB,CAAC,WAA8B,eAC/C,sBAAsB,EAAE;AAAA,EAC1B,YAAY,MAAM;AAAA,EAClB,eAAe,MAAM;AAAA,EAAC;AAAA,EAEtB,wBAAwB,MAAM,sBAAsB,MAAY;AAAA,EAAC,EAAC;AAAA,EAElE,WAAW;AAAA,EACX,cAAc,MAAM,sBAAsB,KAAK;AAAA,EAE/C,kBAAkB;AAAA,EAClB,qBAAqB,MAAM,sBAAsB,EAAE;AAAA,EAEnD,wBAAwB,CAAC;AAAA,EACzB,2BAA2B,MAAM,sBAAsB,CAAC,CAAC;AAAA,EAEzD,qBAAqB,CAAC,eAAyB,sBAAsB,CAAC,CAAC;AAAA,EACvE,oBAAoB,MAAM,sBAAsB,EAAE;AAAA,EAClD,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,eAAe,CAAC;AAAA,EAEhB,kBAAkB,IAAK,MAAkC;AAAA,IACvD,IAAI,kBAA0B;AAC5B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAAA,IAEA,IAAI,UAAkC;AACpC,aAAO,CAAC;AAAA,IACV;AAAA,IACA,IAAI,OAA4B;AAC9B,aAAO,CAAC;AAAA,IACV;AAAA,EACF,EAAG;AAAA,EAEH,6BAA6B,CAAC;AAAA,EAC9B,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,mCAAmC,MAAM;AAAA,EAAC;AAAA,EAC1C,gBAAgB;AAAA,EAChB,eAAe,CAAC;AAAA,EAChB,kBAAkB,MAAM;AAAA,EAAC;AAAA,EACzB,kBAAkB,EAAE,SAAS,CAAC,EAAE;AAAA,EAChC,yBAAyB,MAAM;AAAA,EAAC;AAAA,EAChC,cAAc;AAAA,EACd,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,qBAAqB,CAAC;AAAA,EACtB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,OAAO;AAAA,EACP,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,wBAAwB,EAAE,SAAS,KAAK;AAAA,EACxC,iBAAiB,CAAC;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,0BAA0B;AAAA,EAC1B,6BAA6B,MAAM;AAAA,EACnC,gCAAgC,MAAM;AACxC;AAEO,IAAM,iBAAiB,cAAAC,QAAM,cAAoCD,oBAAmB;AAEpF,SAAS,oBAA0C;AACxD,QAAM,UAAU,cAAAC,QAAM,WAAW,cAAc;AAC/C,MAAI,YAAYD,sBAAqB;AACnC,UAAM,IAAI,MAAM,uEAAuE;AAAA,EACzF;AACA,SAAO;AACT;AAEA,SAAS,sBAAyB,QAAc;AAC9C,QAAM,IAAI,MAAM,uEAAuE;AACzF;;;AF3PI;AAzCG,SAAS,gBAAgB,EAAE,SAAS,GAA4B;AACrE,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAoB,CAAC,CAAC;AACtD,QAAM,yBAAqB,sBAAe;AAC1C,QAAM,0BAAsB,sBAAe;AAC3C,QAAM,yBAAqB,sBAAe;AAE1C,QAAM,EAAE,UAAU,cAAc,cAAc,IAAI,kBAAkB;AAEpE,+BAAU,MAAM;AACd,QAAI,CAAC,YAAY,aAAa,mBAAmB;AAAS;AAC1D,QACE,aAAa,mBAAmB,YAChC,6CAAc,eAAc,oBAAoB,SAChD;AACA;AAAA,IACF;AAEA,UAAM,gBAAgB,MAAY;AA1BtC;AA2BM,UAAI,EAAC,6CAAc;AAAW;AAE9B,YAAM,SAAS,MAAM,cAAc,eAAe;AAAA,QAChD;AAAA,QACA,WAAW,6CAAc;AAAA,MAC3B,CAAC;AAED,YAAM,eAAc,kBAAO,SAAP,mBAAa,mBAAb,mBAA6B;AACjD,UAAI,gBAAgB,mBAAmB;AAAS;AAEhD,WAAI,kBAAO,SAAP,mBAAa,mBAAb,mBAA6B,cAAc;AAC7C,2BAAmB,UAAU;AAC7B,2BAAmB,UAAU;AAC7B,4BAAoB,UAAU,6CAAc;AAE5C,cAAME,gBAAW,8DAAmC,KAAK,MAAM,eAAe,IAAI,CAAC;AACnF,oBAAYA,SAAQ;AAAA,MACtB;AAAA,IACF;AACA,SAAK,cAAc;AAAA,EACrB,GAAG,CAAC,UAAU,6CAAc,SAAS,CAAC;AAEtC,SACE;AAAA,IAAC,uBAAuB;AAAA,IAAvB;AAAA,MACC,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MAEC;AAAA;AAAA,EACH;AAEJ;", "names": ["import_react", "React", "import_react", "emptyCopilotContext", "React", "messages"]}