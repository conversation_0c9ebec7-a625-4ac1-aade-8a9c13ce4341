{"version": 3, "sources": ["../../src/client/types.ts", "../../src/graphql/@generated/graphql.ts"], "sourcesContent": ["import { randomId } from \"@copilotkit/shared\";\nimport {\n  ActionExecutionMessageInput,\n  MessageRole,\n  MessageStatus,\n  ResultMessageInput,\n  TextMessageInput,\n  BaseMessageOutput,\n  AgentStateMessageInput,\n  MessageStatusCode,\n  LangGraphInterruptEvent as GqlLangGraphInterruptEvent,\n  MetaEventName,\n  CopilotKitLangGraphInterruptEvent as GqlCopilotKitLangGraphInterruptEvent,\n  ImageMessageInput,\n} from \"../graphql/@generated/graphql\";\nimport { parseJson } from \"@copilotkit/shared\";\n\ntype MessageType =\n  | \"TextMessage\"\n  | \"ActionExecutionMessage\"\n  | \"ResultMessage\"\n  | \"AgentStateMessage\"\n  | \"ImageMessage\";\n\nexport class Message {\n  type: MessageType;\n  id: BaseMessageOutput[\"id\"];\n  createdAt: BaseMessageOutput[\"createdAt\"];\n  status: MessageStatus;\n\n  constructor(props: any) {\n    props.id ??= randomId();\n    props.status ??= { code: MessageStatusCode.Success };\n    props.createdAt ??= new Date();\n    Object.assign(this, props);\n  }\n\n  isTextMessage(): this is TextMessage {\n    return this.type === \"TextMessage\";\n  }\n\n  isActionExecutionMessage(): this is ActionExecutionMessage {\n    return this.type === \"ActionExecutionMessage\";\n  }\n\n  isResultMessage(): this is ResultMessage {\n    return this.type === \"ResultMessage\";\n  }\n\n  isAgentStateMessage(): this is AgentStateMessage {\n    return this.type === \"AgentStateMessage\";\n  }\n\n  isImageMessage(): this is ImageMessage {\n    return this.type === \"ImageMessage\";\n  }\n}\n\n// alias Role to MessageRole\nexport const Role = MessageRole;\n\n// when constructing any message, the base fields are optional\ntype MessageConstructorOptions = Partial<Message>;\n\ntype TextMessageConstructorOptions = MessageConstructorOptions & TextMessageInput;\n\nexport class TextMessage extends Message implements TextMessageConstructorOptions {\n  role: TextMessageInput[\"role\"];\n  content: TextMessageInput[\"content\"];\n  parentMessageId: TextMessageInput[\"parentMessageId\"];\n\n  constructor(props: TextMessageConstructorOptions) {\n    super(props);\n    this.type = \"TextMessage\";\n  }\n}\n\ntype ActionExecutionMessageConstructorOptions = MessageConstructorOptions &\n  Omit<ActionExecutionMessageInput, \"arguments\"> & {\n    arguments: Record<string, any>;\n  };\n\nexport class ActionExecutionMessage\n  extends Message\n  implements Omit<ActionExecutionMessageInput, \"arguments\" | \"scope\">\n{\n  name: ActionExecutionMessageInput[\"name\"];\n  arguments: Record<string, any>;\n  parentMessageId: ActionExecutionMessageInput[\"parentMessageId\"];\n  constructor(props: ActionExecutionMessageConstructorOptions) {\n    super(props);\n    this.type = \"ActionExecutionMessage\";\n  }\n}\n\ntype ResultMessageConstructorOptions = MessageConstructorOptions & ResultMessageInput;\n\nexport class ResultMessage extends Message implements ResultMessageConstructorOptions {\n  actionExecutionId: ResultMessageInput[\"actionExecutionId\"];\n  actionName: ResultMessageInput[\"actionName\"];\n  result: ResultMessageInput[\"result\"];\n\n  constructor(props: ResultMessageConstructorOptions) {\n    super(props);\n    this.type = \"ResultMessage\";\n  }\n\n  static decodeResult(result: string): any {\n    return parseJson(result, result);\n  }\n\n  static encodeResult(result: any): string {\n    if (result === undefined) {\n      return \"\";\n    } else if (typeof result === \"string\") {\n      return result;\n    } else {\n      return JSON.stringify(result);\n    }\n  }\n}\n\nexport class AgentStateMessage extends Message implements Omit<AgentStateMessageInput, \"state\"> {\n  agentName: AgentStateMessageInput[\"agentName\"];\n  state: any;\n  running: AgentStateMessageInput[\"running\"];\n  threadId: AgentStateMessageInput[\"threadId\"];\n  role: AgentStateMessageInput[\"role\"];\n  nodeName: AgentStateMessageInput[\"nodeName\"];\n  runId: AgentStateMessageInput[\"runId\"];\n  active: AgentStateMessageInput[\"active\"];\n\n  constructor(props: any) {\n    super(props);\n    this.type = \"AgentStateMessage\";\n  }\n}\n\ntype ImageMessageConstructorOptions = MessageConstructorOptions & ImageMessageInput;\n\nexport class ImageMessage extends Message implements ImageMessageConstructorOptions {\n  format: ImageMessageInput[\"format\"];\n  bytes: ImageMessageInput[\"bytes\"];\n  role: ImageMessageInput[\"role\"];\n  parentMessageId: ImageMessageInput[\"parentMessageId\"];\n\n  constructor(props: ImageMessageConstructorOptions) {\n    super(props);\n    this.type = \"ImageMessage\";\n  }\n}\n\nexport function langGraphInterruptEvent(\n  eventProps: Omit<LangGraphInterruptEvent, \"name\" | \"type\" | \"__typename\">,\n): LangGraphInterruptEvent {\n  return { ...eventProps, name: MetaEventName.LangGraphInterruptEvent, type: \"MetaEvent\" };\n}\n\nexport type LangGraphInterruptEvent<TValue extends any = any> = GqlLangGraphInterruptEvent & {\n  value: TValue;\n};\n\ntype CopilotKitLangGraphInterruptEvent<TValue extends any = any> =\n  GqlCopilotKitLangGraphInterruptEvent & {\n    data: GqlCopilotKitLangGraphInterruptEvent[\"data\"] & { value: TValue };\n  };\n\nexport type MetaEvent = LangGraphInterruptEvent | CopilotKitLangGraphInterruptEvent;\n", "/* eslint-disable */\nimport type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';\nexport type Maybe<T> = T | null;\nexport type InputMaybe<T> = Maybe<T>;\nexport type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };\nexport type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };\nexport type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };\nexport type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };\nexport type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };\n/** All built-in and custom scalars, mapped to their actual values */\nexport type Scalars = {\n  ID: { input: string; output: string; }\n  String: { input: string; output: string; }\n  Boolean: { input: boolean; output: boolean; }\n  Int: { input: number; output: number; }\n  Float: { input: number; output: number; }\n  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format. */\n  DateTimeISO: { input: any; output: any; }\n  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */\n  JSON: { input: any; output: any; }\n  /** The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */\n  JSONObject: { input: any; output: any; }\n};\n\nexport type ActionExecutionMessageInput = {\n  arguments: Scalars['String']['input'];\n  name: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  /** @deprecated This field will be removed in a future version */\n  scope?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type ActionExecutionMessageOutput = BaseMessageOutput & {\n  __typename?: 'ActionExecutionMessageOutput';\n  arguments: Array<Scalars['String']['output']>;\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  name: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  /** @deprecated This field will be removed in a future version */\n  scope?: Maybe<Scalars['String']['output']>;\n  status: MessageStatus;\n};\n\nexport type ActionInput = {\n  available?: InputMaybe<ActionInputAvailability>;\n  description: Scalars['String']['input'];\n  jsonSchema: Scalars['String']['input'];\n  name: Scalars['String']['input'];\n};\n\n/** The availability of the frontend action */\nexport enum ActionInputAvailability {\n  Disabled = 'disabled',\n  Enabled = 'enabled',\n  Remote = 'remote'\n}\n\nexport type Agent = {\n  __typename?: 'Agent';\n  description: Scalars['String']['output'];\n  id: Scalars['String']['output'];\n  name: Scalars['String']['output'];\n};\n\nexport type AgentSessionInput = {\n  agentName: Scalars['String']['input'];\n  nodeName?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type AgentStateInput = {\n  agentName: Scalars['String']['input'];\n  config?: InputMaybe<Scalars['String']['input']>;\n  state: Scalars['String']['input'];\n};\n\nexport type AgentStateMessageInput = {\n  active: Scalars['Boolean']['input'];\n  agentName: Scalars['String']['input'];\n  nodeName: Scalars['String']['input'];\n  role: MessageRole;\n  runId: Scalars['String']['input'];\n  running: Scalars['Boolean']['input'];\n  state: Scalars['String']['input'];\n  threadId: Scalars['String']['input'];\n};\n\nexport type AgentStateMessageOutput = BaseMessageOutput & {\n  __typename?: 'AgentStateMessageOutput';\n  active: Scalars['Boolean']['output'];\n  agentName: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  nodeName: Scalars['String']['output'];\n  role: MessageRole;\n  runId: Scalars['String']['output'];\n  running: Scalars['Boolean']['output'];\n  state: Scalars['String']['output'];\n  status: MessageStatus;\n  threadId: Scalars['String']['output'];\n};\n\nexport type AgentsResponse = {\n  __typename?: 'AgentsResponse';\n  agents: Array<Agent>;\n};\n\nexport type BaseMessageOutput = {\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  status: MessageStatus;\n};\n\nexport type BaseMetaEvent = {\n  name: MetaEventName;\n  type: Scalars['String']['output'];\n};\n\nexport type BaseResponseStatus = {\n  code: ResponseStatusCode;\n};\n\nexport type CloudInput = {\n  guardrails?: InputMaybe<GuardrailsInput>;\n};\n\nexport type CopilotKitLangGraphInterruptEvent = BaseMetaEvent & {\n  __typename?: 'CopilotKitLangGraphInterruptEvent';\n  data: CopilotKitLangGraphInterruptEventData;\n  name: MetaEventName;\n  response?: Maybe<Scalars['String']['output']>;\n  type: Scalars['String']['output'];\n};\n\nexport type CopilotKitLangGraphInterruptEventData = {\n  __typename?: 'CopilotKitLangGraphInterruptEventData';\n  messages: Array<BaseMessageOutput>;\n  value: Scalars['String']['output'];\n};\n\n/** The type of Copilot request */\nexport enum CopilotRequestType {\n  Chat = 'Chat',\n  Suggestion = 'Suggestion',\n  Task = 'Task',\n  TextareaCompletion = 'TextareaCompletion',\n  TextareaPopover = 'TextareaPopover'\n}\n\nexport type CopilotResponse = {\n  __typename?: 'CopilotResponse';\n  extensions?: Maybe<ExtensionsResponse>;\n  messages: Array<BaseMessageOutput>;\n  metaEvents?: Maybe<Array<BaseMetaEvent>>;\n  runId?: Maybe<Scalars['String']['output']>;\n  status: ResponseStatus;\n  threadId: Scalars['String']['output'];\n};\n\nexport type ExtensionsInput = {\n  openaiAssistantAPI?: InputMaybe<OpenAiApiAssistantApiInput>;\n};\n\nexport type ExtensionsResponse = {\n  __typename?: 'ExtensionsResponse';\n  openaiAssistantAPI?: Maybe<OpenAiApiAssistantApiResponse>;\n};\n\nexport type FailedMessageStatus = {\n  __typename?: 'FailedMessageStatus';\n  code: MessageStatusCode;\n  reason: Scalars['String']['output'];\n};\n\nexport type FailedResponseStatus = BaseResponseStatus & {\n  __typename?: 'FailedResponseStatus';\n  code: ResponseStatusCode;\n  details?: Maybe<Scalars['JSON']['output']>;\n  reason: FailedResponseStatusReason;\n};\n\nexport enum FailedResponseStatusReason {\n  GuardrailsValidationFailed = 'GUARDRAILS_VALIDATION_FAILED',\n  MessageStreamInterrupted = 'MESSAGE_STREAM_INTERRUPTED',\n  UnknownError = 'UNKNOWN_ERROR'\n}\n\nexport type ForwardedParametersInput = {\n  maxTokens?: InputMaybe<Scalars['Float']['input']>;\n  model?: InputMaybe<Scalars['String']['input']>;\n  stop?: InputMaybe<Array<Scalars['String']['input']>>;\n  temperature?: InputMaybe<Scalars['Float']['input']>;\n  toolChoice?: InputMaybe<Scalars['String']['input']>;\n  toolChoiceFunctionName?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type FrontendInput = {\n  actions: Array<ActionInput>;\n  toDeprecate_fullContext?: InputMaybe<Scalars['String']['input']>;\n  url?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type GenerateCopilotResponseInput = {\n  agentSession?: InputMaybe<AgentSessionInput>;\n  agentState?: InputMaybe<AgentStateInput>;\n  agentStates?: InputMaybe<Array<AgentStateInput>>;\n  cloud?: InputMaybe<CloudInput>;\n  extensions?: InputMaybe<ExtensionsInput>;\n  forwardedParameters?: InputMaybe<ForwardedParametersInput>;\n  frontend: FrontendInput;\n  messages: Array<MessageInput>;\n  metaEvents?: InputMaybe<Array<MetaEventInput>>;\n  metadata: GenerateCopilotResponseMetadataInput;\n  runId?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type GenerateCopilotResponseMetadataInput = {\n  requestType?: InputMaybe<CopilotRequestType>;\n};\n\nexport type GuardrailsInput = {\n  inputValidationRules: GuardrailsRuleInput;\n};\n\nexport type GuardrailsRuleInput = {\n  allowList?: InputMaybe<Array<Scalars['String']['input']>>;\n  denyList?: InputMaybe<Array<Scalars['String']['input']>>;\n};\n\nexport type ImageMessageInput = {\n  bytes: Scalars['String']['input'];\n  format: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  role: MessageRole;\n};\n\nexport type ImageMessageOutput = BaseMessageOutput & {\n  __typename?: 'ImageMessageOutput';\n  bytes: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  format: Scalars['String']['output'];\n  id: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  role: MessageRole;\n  status: MessageStatus;\n};\n\nexport type LangGraphInterruptEvent = BaseMetaEvent & {\n  __typename?: 'LangGraphInterruptEvent';\n  name: MetaEventName;\n  response?: Maybe<Scalars['String']['output']>;\n  type: Scalars['String']['output'];\n  value: Scalars['String']['output'];\n};\n\nexport type LoadAgentStateInput = {\n  agentName: Scalars['String']['input'];\n  threadId: Scalars['String']['input'];\n};\n\nexport type LoadAgentStateResponse = {\n  __typename?: 'LoadAgentStateResponse';\n  messages: Scalars['String']['output'];\n  state: Scalars['String']['output'];\n  threadExists: Scalars['Boolean']['output'];\n  threadId: Scalars['String']['output'];\n};\n\nexport type MessageInput = {\n  actionExecutionMessage?: InputMaybe<ActionExecutionMessageInput>;\n  agentStateMessage?: InputMaybe<AgentStateMessageInput>;\n  createdAt: Scalars['DateTimeISO']['input'];\n  id: Scalars['String']['input'];\n  imageMessage?: InputMaybe<ImageMessageInput>;\n  resultMessage?: InputMaybe<ResultMessageInput>;\n  textMessage?: InputMaybe<TextMessageInput>;\n};\n\n/** The role of the message */\nexport enum MessageRole {\n  Assistant = 'assistant',\n  Developer = 'developer',\n  System = 'system',\n  Tool = 'tool',\n  User = 'user'\n}\n\nexport type MessageStatus = FailedMessageStatus | PendingMessageStatus | SuccessMessageStatus;\n\nexport enum MessageStatusCode {\n  Failed = 'Failed',\n  Pending = 'Pending',\n  Success = 'Success'\n}\n\nexport type MetaEventInput = {\n  messages?: InputMaybe<Array<MessageInput>>;\n  name: MetaEventName;\n  response?: InputMaybe<Scalars['String']['input']>;\n  value: Scalars['String']['input'];\n};\n\n/** Meta event types */\nexport enum MetaEventName {\n  CopilotKitLangGraphInterruptEvent = 'CopilotKitLangGraphInterruptEvent',\n  LangGraphInterruptEvent = 'LangGraphInterruptEvent'\n}\n\nexport type Mutation = {\n  __typename?: 'Mutation';\n  generateCopilotResponse: CopilotResponse;\n};\n\n\nexport type MutationGenerateCopilotResponseArgs = {\n  data: GenerateCopilotResponseInput;\n  properties?: InputMaybe<Scalars['JSONObject']['input']>;\n};\n\nexport type OpenAiApiAssistantApiInput = {\n  runId?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type OpenAiApiAssistantApiResponse = {\n  __typename?: 'OpenAIApiAssistantAPIResponse';\n  runId?: Maybe<Scalars['String']['output']>;\n  threadId?: Maybe<Scalars['String']['output']>;\n};\n\nexport type PendingMessageStatus = {\n  __typename?: 'PendingMessageStatus';\n  code: MessageStatusCode;\n};\n\nexport type PendingResponseStatus = BaseResponseStatus & {\n  __typename?: 'PendingResponseStatus';\n  code: ResponseStatusCode;\n};\n\nexport type Query = {\n  __typename?: 'Query';\n  availableAgents: AgentsResponse;\n  hello: Scalars['String']['output'];\n  loadAgentState: LoadAgentStateResponse;\n};\n\n\nexport type QueryLoadAgentStateArgs = {\n  data: LoadAgentStateInput;\n};\n\nexport type ResponseStatus = FailedResponseStatus | PendingResponseStatus | SuccessResponseStatus;\n\nexport enum ResponseStatusCode {\n  Failed = 'Failed',\n  Pending = 'Pending',\n  Success = 'Success'\n}\n\nexport type ResultMessageInput = {\n  actionExecutionId: Scalars['String']['input'];\n  actionName: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  result: Scalars['String']['input'];\n};\n\nexport type ResultMessageOutput = BaseMessageOutput & {\n  __typename?: 'ResultMessageOutput';\n  actionExecutionId: Scalars['String']['output'];\n  actionName: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  result: Scalars['String']['output'];\n  status: MessageStatus;\n};\n\nexport type SuccessMessageStatus = {\n  __typename?: 'SuccessMessageStatus';\n  code: MessageStatusCode;\n};\n\nexport type SuccessResponseStatus = BaseResponseStatus & {\n  __typename?: 'SuccessResponseStatus';\n  code: ResponseStatusCode;\n};\n\nexport type TextMessageInput = {\n  content: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  role: MessageRole;\n};\n\nexport type TextMessageOutput = BaseMessageOutput & {\n  __typename?: 'TextMessageOutput';\n  content: Array<Scalars['String']['output']>;\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  role: MessageRole;\n  status: MessageStatus;\n};\n\nexport type GenerateCopilotResponseMutationVariables = Exact<{\n  data: GenerateCopilotResponseInput;\n  properties?: InputMaybe<Scalars['JSONObject']['input']>;\n}>;\n\n\nexport type GenerateCopilotResponseMutation = { __typename?: 'Mutation', generateCopilotResponse: { __typename?: 'CopilotResponse', threadId: string, runId?: string | null, extensions?: { __typename?: 'ExtensionsResponse', openaiAssistantAPI?: { __typename?: 'OpenAIApiAssistantAPIResponse', runId?: string | null, threadId?: string | null } | null } | null, messages: Array<{ __typename: 'ActionExecutionMessageOutput', id: string, createdAt: any, name: string, arguments: Array<string>, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'AgentStateMessageOutput', id: string, createdAt: any, threadId: string, state: string, running: boolean, agentName: string, nodeName: string, runId: string, active: boolean, role: MessageRole, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ImageMessageOutput', id: string, createdAt: any, format: string, bytes: string, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ResultMessageOutput', id: string, createdAt: any, result: string, actionExecutionId: string, actionName: string, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'TextMessageOutput', id: string, createdAt: any, content: Array<string>, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } }>, metaEvents?: Array<{ __typename?: 'CopilotKitLangGraphInterruptEvent', type: string, name: MetaEventName, data: { __typename?: 'CopilotKitLangGraphInterruptEventData', value: string, messages: Array<{ __typename: 'ActionExecutionMessageOutput', id: string, createdAt: any, name: string, arguments: Array<string>, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'AgentStateMessageOutput', id: string, createdAt: any, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ImageMessageOutput', id: string, createdAt: any, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ResultMessageOutput', id: string, createdAt: any, result: string, actionExecutionId: string, actionName: string, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'TextMessageOutput', id: string, createdAt: any, content: Array<string>, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } }> } } | { __typename?: 'LangGraphInterruptEvent', type: string, name: MetaEventName, value: string }> | null } & ({ __typename?: 'CopilotResponse', status: { __typename?: 'FailedResponseStatus', code: ResponseStatusCode, reason: FailedResponseStatusReason, details?: any | null } | { __typename?: 'PendingResponseStatus', code: ResponseStatusCode } | { __typename?: 'SuccessResponseStatus', code: ResponseStatusCode } } | { __typename?: 'CopilotResponse', status?: never }) };\n\nexport type AvailableAgentsQueryVariables = Exact<{ [key: string]: never; }>;\n\n\nexport type AvailableAgentsQuery = { __typename?: 'Query', availableAgents: { __typename?: 'AgentsResponse', agents: Array<{ __typename?: 'Agent', name: string, id: string, description: string }> } };\n\nexport type LoadAgentStateQueryVariables = Exact<{\n  data: LoadAgentStateInput;\n}>;\n\n\nexport type LoadAgentStateQuery = { __typename?: 'Query', loadAgentState: { __typename?: 'LoadAgentStateResponse', threadId: string, threadExists: boolean, state: string, messages: string } };\n\n\nexport const GenerateCopilotResponseDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"mutation\",\"name\":{\"kind\":\"Name\",\"value\":\"generateCopilotResponse\"},\"variableDefinitions\":[{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}},\"type\":{\"kind\":\"NonNullType\",\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"GenerateCopilotResponseInput\"}}}},{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"}},\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"JSONObject\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"generateCopilotResponse\"},\"arguments\":[{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}}},{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"extensions\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"openaiAssistantAPI\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"CopilotResponse\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseResponseStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedResponseStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"details\"}}]}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"__typename\"}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"createdAt\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"SuccessMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"PendingMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"TextMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"content\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}]},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ImageMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"format\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"bytes\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ActionExecutionMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"arguments\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}]},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ResultMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"result\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionExecutionId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionName\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"AgentStateMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"state\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"running\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"agentName\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"nodeName\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"active\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"metaEvents\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"LangGraphInterruptEvent\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"type\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"value\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"CopilotKitLangGraphInterruptEvent\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"type\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"__typename\"}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"createdAt\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"SuccessMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"PendingMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"TextMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"content\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ActionExecutionMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"arguments\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ResultMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"result\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionExecutionId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionName\"}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"value\"}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<GenerateCopilotResponseMutation, GenerateCopilotResponseMutationVariables>;\nexport const AvailableAgentsDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"query\",\"name\":{\"kind\":\"Name\",\"value\":\"availableAgents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"availableAgents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"agents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"description\"}}]}}]}}]}}]} as unknown as DocumentNode<AvailableAgentsQuery, AvailableAgentsQueryVariables>;\nexport const LoadAgentStateDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"query\",\"name\":{\"kind\":\"Name\",\"value\":\"loadAgentState\"},\"variableDefinitions\":[{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}},\"type\":{\"kind\":\"NonNullType\",\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"LoadAgentStateInput\"}}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"loadAgentState\"},\"arguments\":[{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadExists\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"state\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"}}]}}]}}]} as unknown as DocumentNode<LoadAgentStateQuery, LoadAgentStateQueryVariables>;"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAyB;;;ACyRlB,IAAK,cAAL,kBAAKA,iBAAL;AACL,EAAAA,aAAA,eAAY;AACZ,EAAAA,aAAA,eAAY;AACZ,EAAAA,aAAA,YAAS;AACT,EAAAA,aAAA,UAAO;AACP,EAAAA,aAAA,UAAO;AALG,SAAAA;AAAA,GAAA;;;AD1QZ,IAAAC,iBAA0B;AASnB,IAAM,UAAN,MAAc;AAAA,EAMnB,YAAY,OAAY;AACtB,UAAM,OAAN,MAAM,SAAO,wBAAS;AACtB,UAAM,WAAN,MAAM,SAAW,EAAE,8BAAgC;AACnD,UAAM,cAAN,MAAM,YAAc,oBAAI,KAAK;AAC7B,WAAO,OAAO,MAAM,KAAK;AAAA,EAC3B;AAAA,EAEA,gBAAqC;AACnC,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,2BAA2D;AACzD,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,kBAAyC;AACvC,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,sBAAiD;AAC/C,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,iBAAuC;AACrC,WAAO,KAAK,SAAS;AAAA,EACvB;AACF;AAGO,IAAM,OAAO;AAOb,IAAM,cAAN,cAA0B,QAAiD;AAAA,EAKhF,YAAY,OAAsC;AAChD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAOO,IAAM,yBAAN,cACG,QAEV;AAAA,EAIE,YAAY,OAAiD;AAC3D,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAIO,IAAM,gBAAN,cAA4B,QAAmD;AAAA,EAKpF,YAAY,OAAwC;AAClD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,OAAO,aAAa,QAAqB;AACvC,eAAO,0BAAU,QAAQ,MAAM;AAAA,EACjC;AAAA,EAEA,OAAO,aAAa,QAAqB;AACvC,QAAI,WAAW,QAAW;AACxB,aAAO;AAAA,IACT,WAAW,OAAO,WAAW,UAAU;AACrC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,KAAK,UAAU,MAAM;AAAA,IAC9B;AAAA,EACF;AACF;AAEO,IAAM,oBAAN,cAAgC,QAAyD;AAAA,EAU9F,YAAY,OAAY;AACtB,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAIO,IAAM,eAAN,cAA2B,QAAkD;AAAA,EAMlF,YAAY,OAAuC;AACjD,UAAM,KAAK;AACX,SAAK,OAAO;AAAA,EACd;AACF;AAEO,SAAS,wBACd,YACyB;AACzB,SAAO,EAAE,GAAG,YAAY,+DAA6C,MAAM,YAAY;AACzF;", "names": ["MessageRole", "import_shared"]}