import '@copilotkit/runtime-client-gql';
export { L as LangGraphInterruptAction, d as LangGraphInterruptActionSetter, i as LangGraphInterruptActionSetterArgs, e as LangGraphInterruptRender, g as LangGraphInterruptRenderHandlerProps, h as LangGraphInterruptRenderProps } from '../copilot-context-8fb74a85.js';
import '@copilotkit/shared';
import './frontend-action.js';
import 'react';
import '../hooks/use-tree.js';
import './document-pointer.js';
import './chat-suggestion-configuration.js';
import './coagent-action.js';
import './coagent-state.js';
