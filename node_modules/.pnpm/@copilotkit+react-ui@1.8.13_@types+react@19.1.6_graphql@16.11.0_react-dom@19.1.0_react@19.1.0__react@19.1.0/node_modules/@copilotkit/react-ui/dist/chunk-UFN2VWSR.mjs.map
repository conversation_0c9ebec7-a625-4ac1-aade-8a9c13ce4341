{"version": 3, "sources": ["../src/components/chat/Button.tsx"], "sourcesContent": ["import { ButtonProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\n\nexport const Button = ({}: ButtonProps) => {\n  const { open, setOpen, icons } = useChatContext();\n\n  return (\n    <div onClick={() => setOpen(!open)}>\n      <button\n        className={`copilotKitButton ${open ? \"open\" : \"\"}`}\n        aria-label={open ? \"Close Chat\" : \"Open Chat\"}\n      >\n        <div className=\"copilotKitButtonIcon copilotKitButtonIconOpen\">{icons.openIcon}</div>\n        <div className=\"copilotKitButtonIcon copilotKitButtonIconClose\">{icons.closeIcon}</div>\n      </button>\n    </div>\n  );\n};\n"], "mappings": ";;;;;AAQM,SAIE,KAJF;AALC,IAAM,SAAS,CAAC,CAAC,MAAmB;AACzC,QAAM,EAAE,MAAM,SAAS,MAAM,IAAI,eAAe;AAEhD,SACE,oBAAC,SAAI,SAAS,MAAM,QAAQ,CAAC,IAAI,GAC/B;AAAA,IAAC;AAAA;AAAA,MACC,WAAW,oBAAoB,OAAO,SAAS;AAAA,MAC/C,cAAY,OAAO,eAAe;AAAA,MAElC;AAAA,4BAAC,SAAI,WAAU,iDAAiD,gBAAM,UAAS;AAAA,QAC/E,oBAAC,SAAI,WAAU,kDAAkD,gBAAM,WAAU;AAAA;AAAA;AAAA,EACnF,GACF;AAEJ;", "names": []}