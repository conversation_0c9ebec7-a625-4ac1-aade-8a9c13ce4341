// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import { fileDesc } from "../../../../codegenv2/file.js";
import { file_google_protobuf_descriptor } from "./descriptor_pb.js";
import { messageDesc } from "../../../../codegenv2/message.js";
import { enumDesc } from "../../../../codegenv2/enum.js";
import { extDesc } from "../../../../codegenv2/extension.js";
/**
 * Describes the file google/protobuf/cpp_features.proto.
 */
export const file_google_protobuf_cpp_features = /*@__PURE__*/ fileDesc("CiJnb29nbGUvcHJvdG9idWYvY3BwX2ZlYXR1cmVzLnByb3RvEgJwYiL8AwoLQ3BwRmVhdHVyZXMS+wEKEmxlZ2FjeV9jbG9zZWRfZW51bRgBIAEoCELeAYgBAZgBBJgBAaIBCRIEdHJ1ZRiEB6IBChIFZmFsc2UY5weyAbgBCOgHEOgHGq8BVGhlIGxlZ2FjeSBjbG9zZWQgZW51bSBiZWhhdmlvciBpbiBDKysgaXMgZGVwcmVjYXRlZCBhbmQgaXMgc2NoZWR1bGVkIHRvIGJlIHJlbW92ZWQgaW4gZWRpdGlvbiAyMDI1LiAgU2VlIGh0dHA6Ly9wcm90b2J1Zi5kZXYvcHJvZ3JhbW1pbmctZ3VpZGVzL2VudW0vI2NwcCBmb3IgbW9yZSBpbmZvcm1hdGlvbhJaCgtzdHJpbmdfdHlwZRgCIAEoDjIaLnBiLkNwcEZlYXR1cmVzLlN0cmluZ1R5cGVCKYgBAZgBBJgBAaIBCxIGU1RSSU5HGIQHogEJEgRWSUVXGOkHsgEDCOgHEkwKGmVudW1fbmFtZV91c2VzX3N0cmluZ192aWV3GAMgASgIQiiIAQKYAQaYAQGiAQoSBWZhbHNlGIQHogEJEgR0cnVlGOkHsgEDCOkHIkUKClN0cmluZ1R5cGUSFwoTU1RSSU5HX1RZUEVfVU5LTk9XThAAEggKBFZJRVcQARIICgRDT1JEEAISCgoGU1RSSU5HEAM6PwoDY3BwEhsuZ29vZ2xlLnByb3RvYnVmLkZlYXR1cmVTZXQY6AcgASgLMg8ucGIuQ3BwRmVhdHVyZXNSA2NwcA", [file_google_protobuf_descriptor]);
/**
 * Describes the message pb.CppFeatures.
 * Use `create(CppFeaturesSchema)` to create a new message.
 */
export const CppFeaturesSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_cpp_features, 0);
/**
 * @generated from enum pb.CppFeatures.StringType
 */
export var CppFeatures_StringType;
(function (CppFeatures_StringType) {
    /**
     * @generated from enum value: STRING_TYPE_UNKNOWN = 0;
     */
    CppFeatures_StringType[CppFeatures_StringType["STRING_TYPE_UNKNOWN"] = 0] = "STRING_TYPE_UNKNOWN";
    /**
     * @generated from enum value: VIEW = 1;
     */
    CppFeatures_StringType[CppFeatures_StringType["VIEW"] = 1] = "VIEW";
    /**
     * @generated from enum value: CORD = 2;
     */
    CppFeatures_StringType[CppFeatures_StringType["CORD"] = 2] = "CORD";
    /**
     * @generated from enum value: STRING = 3;
     */
    CppFeatures_StringType[CppFeatures_StringType["STRING"] = 3] = "STRING";
})(CppFeatures_StringType || (CppFeatures_StringType = {}));
/**
 * Describes the enum pb.CppFeatures.StringType.
 */
export const CppFeatures_StringTypeSchema = /*@__PURE__*/ enumDesc(file_google_protobuf_cpp_features, 0, 0);
/**
 * @generated from extension: optional pb.CppFeatures cpp = 1000;
 */
export const cpp = /*@__PURE__*/ extDesc(file_google_protobuf_cpp_features, 0);
