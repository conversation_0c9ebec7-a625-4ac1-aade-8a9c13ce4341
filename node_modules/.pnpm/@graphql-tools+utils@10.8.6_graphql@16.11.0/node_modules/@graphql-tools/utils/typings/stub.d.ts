import { GraphQLInputObjectType, GraphQLInputType, GraphQLInterfaceType, GraphQLNamedType, GraphQLObjectType, GraphQLOutputType, GraphQLType, TypeNode } from 'graphql';
export declare function createNamedStub(name: string, type: 'object'): GraphQLObjectType;
export declare function createNamedStub(name: string, type: 'interface'): GraphQLInterfaceType;
export declare function createNamedStub(name: string, type: 'input'): GraphQLInputObjectType;
export declare function createStub(node: TypeNode, type: 'output'): GraphQLOutputType;
export declare function createStub(node: TypeNode, type: 'input'): GraphQLInputType;
export declare function createStub(node: TypeNode, type: 'output' | 'input'): GraphQLType;
export declare function isNamedStub(type: GraphQLNamedType): boolean;
export declare function getBuiltInForStub(type: GraphQLNamedType): GraphQLNamedType;
