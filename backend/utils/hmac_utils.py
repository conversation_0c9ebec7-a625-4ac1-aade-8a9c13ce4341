"""
HMAC utilities for webhook signature verification.

This module provides utilities for verifying HMAC signatures on webhook payloads.
"""

import hashlib
import hmac
import os
from typing import Optional

from backend.utils.logging import get_logger

logger = get_logger(__name__)


def verify_hmac_signature(
    payload: bytes,
    signature: str,
    secret: Optional[str] = None,
    algorithm: str = "sha256"
) -> bool:
    """
    Verify HMAC signature for webhook payload.
    
    Args:
        payload: The raw request body as bytes
        signature: The signature from the X-Avr-Signature header
        secret: The secret key (defaults to CORE_INTAKE_SECRET env var)
        algorithm: The hash algorithm to use (default: sha256)
    
    Returns:
        bool: True if the signature is valid, False otherwise
    """
    # Get secret from environment if not provided
    if not secret:
        secret = os.getenv("CORE_INTAKE_SECRET")
    
    if not secret:
        logger.warning("CORE_INTAKE_SECRET not set. Webhook verification will fail.")
        return False
    
    if not signature:
        logger.warning("No signature provided in X-Avr-Signature header")
        return False
    
    try:
        # Remove 'sha256=' prefix if present
        if signature.startswith(f"{algorithm}="):
            signature = signature[len(f"{algorithm}="):]
        
        # Calculate the expected signature
        expected_signature = hmac.new(
            key=secret.encode('utf-8'),
            msg=payload,
            digestmod=getattr(hashlib, algorithm)
        ).hexdigest()
        
        # Use constant-time comparison to prevent timing attacks
        is_valid = hmac.compare_digest(expected_signature, signature)
        
        if not is_valid:
            logger.warning(
                "HMAC signature verification failed",
                extra={
                    "expected_signature": expected_signature[:8] + "...",  # Log only first 8 chars for security
                    "provided_signature": signature[:8] + "..." if len(signature) > 8 else signature
                }
            )
        
        return is_valid
        
    except Exception as e:
        logger.error(f"Error verifying HMAC signature: {str(e)}")
        return False


def generate_hmac_signature(
    payload: bytes,
    secret: Optional[str] = None,
    algorithm: str = "sha256"
) -> str:
    """
    Generate HMAC signature for a payload.
    
    This is primarily used for testing purposes.
    
    Args:
        payload: The raw payload as bytes
        secret: The secret key (defaults to CORE_INTAKE_SECRET env var)
        algorithm: The hash algorithm to use (default: sha256)
    
    Returns:
        str: The HMAC signature in the format "algorithm=signature"
    """
    # Get secret from environment if not provided
    if not secret:
        secret = os.getenv("CORE_INTAKE_SECRET")
    
    if not secret:
        raise ValueError("CORE_INTAKE_SECRET not set and no secret provided")
    
    # Calculate the signature
    signature = hmac.new(
        key=secret.encode('utf-8'),
        msg=payload,
        digestmod=getattr(hashlib, algorithm)
    ).hexdigest()
    
    return f"{algorithm}={signature}"
