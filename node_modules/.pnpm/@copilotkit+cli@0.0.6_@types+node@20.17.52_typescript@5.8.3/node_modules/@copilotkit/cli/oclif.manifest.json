{"commands": {"base-command": {"aliases": [], "args": {}, "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "base-command", "pluginAlias": "@copilotkit/cli", "pluginName": "@copilotkit/cli", "pluginType": "core", "strict": true, "enableJsonFlag": false, "isESM": true, "relativePath": ["dist", "commands", "base-command.js"]}, "dev": {"aliases": [], "args": {}, "description": "describe the command here", "examples": ["<%= config.bin %> <%= command.id %>"], "flags": {"port": {"description": "port", "name": "port", "required": true, "hasDynamicHelp": false, "multiple": false, "type": "option"}, "project": {"description": "project", "name": "project", "hasDynamicHelp": false, "multiple": false, "type": "option"}}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "dev", "pluginAlias": "@copilotkit/cli", "pluginName": "@copilotkit/cli", "pluginType": "core", "strict": true, "isESM": true, "relativePath": ["dist", "commands", "dev.js"]}, "login": {"aliases": [], "args": {}, "description": "Authenticate with Copilot Cloud", "examples": ["<%= config.bin %> login"], "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "login", "pluginAlias": "@copilotkit/cli", "pluginName": "@copilotkit/cli", "pluginType": "core", "strict": true, "isESM": true, "relativePath": ["dist", "commands", "login.js"]}, "logout": {"aliases": [], "args": {}, "description": "Authenticate with Copilot Cloud", "examples": ["<%= config.bin %> logout"], "flags": {}, "hasDynamicHelp": false, "hiddenAliases": [], "id": "logout", "pluginAlias": "@copilotkit/cli", "pluginName": "@copilotkit/cli", "pluginType": "core", "strict": true, "isESM": true, "relativePath": ["dist", "commands", "logout.js"]}}, "version": "0.0.6"}