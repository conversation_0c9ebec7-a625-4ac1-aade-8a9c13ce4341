import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '@/lib/supabase/database.types';

export async function GET(_request: NextRequest) {
  try {
    const cookieStore = await cookies();
    
    // Create Supabase client
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get tenant ID from JWT
    const tenantId = session.user.user_metadata?.tenant_id;
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID not found' }, { status: 400 });
    }

    // TODO: Replace with actual phone numbers query
    // For now, return mock data
    const mockNumbers = [
      {
        id: '1',
        tenant_id: tenantId,
        phone_number: '+1234567890',
        status: 'active',
        country_code: 'US',
        number_type: 'local',
        monthly_cost: 100, // in cents
        features: ['voice', 'sms'],
        forwarding_number: '+1987654321',
        created_at: new Date(Date.now() - 86400000).toISOString(),
        purchased_at: new Date(Date.now() - 86400000).toISOString(),
      },
    ];

    return NextResponse.json({ numbers: mockNumbers });

  } catch (error) {
    console.error('Error fetching phone numbers:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
