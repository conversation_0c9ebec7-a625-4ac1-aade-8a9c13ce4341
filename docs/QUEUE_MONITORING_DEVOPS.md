# Queue Monitoring DevOps Guide

This document provides deployment and configuration guidance for the Voice Queue Monitoring system.

## Environment Configuration

### Required Environment Variables

```bash
# Queue polling interval (milliseconds)
QUEUE_POLLING_INTERVAL=5000

# WebSocket configuration (optional)
NEXT_PUBLIC_QUEUE_POLLING_INTERVAL=5000
```

### Default Values
- **QUEUE_POLLING_INTERVAL**: 5000ms (5 seconds)
- **WebSocket Reconnection**: Exponential backoff with max 30 seconds
- **Max Reconnection Attempts**: 5 attempts before giving up

## API Endpoints

### Voice Service (voice-svc) Requirements

The queue monitoring system expects the following endpoints to be available:

#### Tenant Endpoints
```
GET  /api/v1/queue/                  # Tenant queue stats
POST /api/v1/queue/clear            # Clear tenant queue
GET  /api/v1/queue/health           # Worker heartbeat
```

#### Super Admin Endpoints
```
GET  /api/v1/queue/all              # System-wide stats
POST /api/v1/queue/clear?tenant_id= # Clear specific tenant
POST /api/v1/queue/worker/start     # Start workers
POST /api/v1/queue/worker/stop      # Stop workers
```

#### WebSocket Endpoints (Optional)
```
WS   /api/v1/queue/ws               # Tenant real-time updates
WS   /api/v1/queue/ws/system        # System real-time updates
```

## Response Formats

### QueueStats (Tenant)
```json
{
  "tenant_id": "string",
  "queued": 0,
  "processing": 0,
  "completed": 0,
  "failed": 0,
  "total_today": 0,
  "avg_processing_time_seconds": 0.0,
  "last_updated": "2024-01-15T10:30:00Z",
  "worker_status": "healthy|degraded|down"
}
```

### SystemQueueStats (Super Admin)
```json
{
  "tenants": [
    {
      "tenant_id": "string",
      "queued": 0,
      "processing": 0,
      "completed": 0,
      "failed": 0,
      "total_today": 0,
      "avg_processing_time_seconds": 0.0,
      "last_updated": "2024-01-15T10:30:00Z",
      "worker_status": "healthy|degraded|down"
    }
  ],
  "totals": {
    "queued": 0,
    "processing": 0,
    "completed": 0,
    "failed": 0,
    "total_today": 0,
    "active_tenants": 0
  },
  "worker_health": {
    "status": "healthy|degraded|down",
    "last_heartbeat": "2024-01-15T10:30:00Z",
    "workers_active": 0,
    "workers_total": 0
  },
  "last_updated": "2024-01-15T10:30:00Z"
}
```

## WebSocket Message Format

### Queue Stats Update
```json
{
  "type": "queue_stats",
  "data": {
    // QueueStats object
  }
}
```

### System Queue Stats Update
```json
{
  "type": "system_queue_stats",
  "data": {
    // SystemQueueStats object
  }
}
```

## Performance Considerations

### Polling Strategy
- **Default Interval**: 5 seconds
- **Deduplication**: 1-second deduplication window to prevent duplicate requests
- **Focus Revalidation**: Automatic refresh when browser tab gains focus
- **Reconnect Revalidation**: Automatic refresh when network reconnects

### WebSocket Fallback
- **Primary**: SWR polling for reliability
- **Enhancement**: WebSocket for real-time updates
- **Graceful Degradation**: System works without WebSocket
- **Auto-Reconnection**: Exponential backoff with circuit breaker

### Caching Strategy
- **SWR Cache**: In-memory caching with automatic revalidation
- **Cache Key**: URL-based cache keys for proper isolation
- **Mutation**: Optimistic updates for user actions

## Security Configuration

### Authentication
- **Tenant Access**: JWT with valid tenant_id claim
- **Super Admin Access**: JWT with `is_super_admin: true` claim
- **API Security**: All endpoints require valid Bearer token

### RBAC Implementation
```typescript
// Tenant access
if (user.role !== 'partner' || !hasFeature('voice_intake')) {
  redirect('/');
}

// Super admin access
if (!jwtPayload?.is_super_admin) {
  redirect('/dashboard');
}
```

## Monitoring and Alerting

### Health Checks
- **Queue API Health**: Monitor `/api/v1/queue/health` endpoint
- **WebSocket Health**: Track connection success rates
- **Worker Health**: Monitor worker heartbeat timestamps

### Metrics to Track
- **API Response Times**: Queue endpoint performance
- **WebSocket Connection Rate**: Real-time update reliability
- **Error Rates**: Failed API calls and WebSocket disconnections
- **Queue Depths**: Monitor for queue buildup across tenants

### Alerting Thresholds
- **API Latency**: > 2 seconds for queue endpoints
- **Queue Depth**: > 100 items queued for any tenant
- **Worker Down**: No heartbeat for > 5 minutes
- **WebSocket Failures**: > 50% connection failure rate

## Deployment Checklist

### Pre-Deployment
- [ ] Voice service API endpoints are deployed and accessible
- [ ] Environment variables are configured
- [ ] Super admin users have `is_super_admin` claim in JWT
- [ ] Feature flags are properly configured for tenants

### Post-Deployment
- [ ] Verify tenant queue widget loads for partners with `voice_intake`
- [ ] Confirm super admin can access system queue monitor
- [ ] Test queue clearing functionality
- [ ] Validate WebSocket connections (if enabled)
- [ ] Check polling intervals and performance

### Rollback Plan
- [ ] Previous version deployment ready
- [ ] Database migrations are reversible (if any)
- [ ] Feature flags can disable new functionality
- [ ] Monitoring alerts configured for issues

## Troubleshooting

### Common Issues

1. **Queue widget shows "Failed to load"**
   - Check voice-svc API connectivity
   - Verify JWT token has valid tenant_id
   - Confirm user has `voice_intake` feature

2. **Super admin access denied**
   - Verify `is_super_admin` claim in JWT
   - Check user email in super admin list
   - Confirm middleware configuration

3. **WebSocket not connecting**
   - Check WebSocket endpoint availability
   - Verify protocol (ws/wss) matches environment
   - Confirm firewall/proxy WebSocket support

4. **Slow performance**
   - Reduce polling interval if needed
   - Check API response times
   - Monitor network latency

### Debug Commands

```bash
# Check environment variables
echo $QUEUE_POLLING_INTERVAL

# Test API endpoints
curl -H "Authorization: Bearer $JWT_TOKEN" \
     https://your-domain/api/v1/queue/

# Check WebSocket connectivity
wscat -c wss://your-domain/api/v1/queue/ws
```

## Support Contacts

- **Frontend Issues**: Frontend team
- **API Issues**: Voice service team  
- **Infrastructure**: DevOps team
- **Security**: Security team
