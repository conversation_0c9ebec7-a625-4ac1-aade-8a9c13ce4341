# Legal-API Integration Sprint - Kickoff Analysis

## Branch Created
✅ **Branch**: `feature/legal-api-integration-sprint`  
✅ **Documentation**: `docs/sprints/legal-api-integration-sprint.md`

## Open Questions Analysis

Based on the Core AiLex architecture analysis, here are the answers to the sprint's open questions:

### 1. Does JWT already include `jurisdictions`? If not, do we need it for FE filtering?

**Current JWT Structure** (from codebase analysis):
```typescript
interface JWTClaims {
  sub: string;           // user_id
  email: string;
  role: UserRole;
  tenant_id: string;
  exp: number;
  iat: number;
  jti?: string;
}
```

**Answer**: ❌ JWT does **NOT** currently include `jurisdictions` field.

**Recommendation**: 
- **Add `jurisdictions: string[]`** to JWT claims in Supabase auth
- Update `frontend/src/lib/auth/jwt.ts` to handle jurisdictions
- Laws-API can use this for filtering Texas vs other state laws
- Fallback: If not present, default to `["texas"]` for existing users

### 2. Any components besides ResearchCopilot that require raw Pinecone vectors?

**Components Found Using Direct Pinecone**:
```
frontend/src/lib/pinecone/          # Direct Pinecone client
frontend/src/lib/integration/document-insights.ts  # Vector search for documents
backend/agents/interactive/research/  # ResearchCopilot (primary)
src/pi_lawyer/services/task_embedding_service.py  # Task embeddings
```

**Answer**: ✅ **Yes**, several components use raw Pinecone:
1. **Document Insights** - Uses vectors for document similarity
2. **Task Embedding Service** - Generates embeddings for task search
3. **Frontend Pinecone Client** - Direct browser calls

**Migration Strategy**:
- Document insights → Use `/v0/recommend` endpoint
- Task embeddings → Keep internal (tenant-specific data)
- Frontend client → Replace with laws-API calls

### 3. Confirm we can delete Pinecone keys from browser env after migration?

**Current Browser Environment Variables**:
```bash
# From temp_env.sh and frontend config
PINECONE_API_KEY=pcsk_5HiiBi_...  # Used in browser
PINECONE_ENVIRONMENT=us-east-1-aws
PINECONE_INDEX_NAME=texas-laws-voyage3large
```

**Answer**: ✅ **Yes**, we can delete Pinecone keys from browser after migration.

**Safe to Remove**:
- `PINECONE_API_KEY` from frontend env
- `PINECONE_ENVIRONMENT` from frontend env  
- `PINECONE_INDEX_NAME` from frontend env

**Keep for Backend**:
- Backend may still need Pinecone for tenant-specific embeddings
- Task embedding service uses separate tenant-isolated indexes

## Implementation Priority

### High Priority (Week 1)
1. **Frontend Search Migration** - Replace `frontend/src/lib/pinecone/*` with laws-API calls
2. **ResearchCopilot Update** - Primary user-facing feature
3. **Auth Hook Enhancement** - Support laws-API domain in `useAuthenticatedFetch`

### Medium Priority (Week 2)  
1. **Document Insights Migration** - Use `/v0/recommend` for document similarity
2. **Bundle Size Optimization** - Remove unused Pinecone/Neo4j deps
3. **Error Handling** - Rate limiting and retry logic

### Low Priority (Future)
1. **Task Embeddings** - Keep internal for now (tenant-specific)
2. **Advanced Graph Queries** - Use `/v0/graph` for complex legal relationships

## Risk Assessment

### 🔴 High Risk
- **Rate Limiting**: 100 req/min/user could impact heavy research sessions
- **Auth Token Expiry**: Need robust refresh mechanism for long sessions

### 🟡 Medium Risk  
- **Bundle Size**: Need to verify tree-shaking removes unused Pinecone code
- **Performance**: Laws-API latency vs direct Pinecone calls

### 🟢 Low Risk
- **JWT Migration**: Existing auth infrastructure is solid
- **Rollback**: Can feature-flag back to direct calls if needed

## Next Steps

1. **Start with PR #1**: Frontend migration focusing on search components
2. **Add jurisdictions to JWT**: Coordinate with auth team for Supabase claims
3. **Create laws-API client**: Reuse patterns from `useAuthenticatedFetch`
4. **Bundle analysis**: Baseline measurement before removing deps

## Success Metrics Baseline

**Current State** (to measure against):
- Bundle size: ~2.1MB (includes Pinecone client)
- Search page load: ~1.8s p95
- Direct Pinecone calls: ~15-20 per search session

**Target State**:
- Bundle size: <1.8MB (15% reduction)
- Search page load: <1.2s p95 (33% improvement)  
- Laws-API calls: 3-5 per search session (consolidated)

---

**Sprint Status**: 🚀 **Ready to Begin**  
**Next Action**: Create laws-API client and types in `frontend/src/types/laws-api.ts`
