// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../resource.mjs";
export class Projects extends APIResource {
    /**
     * Project
     */
    retrieve(id, options) {
        return this._client.get(`/v1/projects/${id}`, options);
    }
    /**
     * List projects
     */
    list(options) {
        return this._client.get('/v1/projects', options);
    }
    /**
     * Project Usage
     */
    usage(id, options) {
        return this._client.get(`/v1/projects/${id}/usage`, options);
    }
}
//# sourceMappingURL=projects.mjs.map