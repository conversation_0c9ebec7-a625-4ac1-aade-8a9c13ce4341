"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/hooks/use-coagent-state-render.ts
var use_coagent_state_render_exports = {};
__export(use_coagent_state_render_exports, {
  useCoAgentStateRender: () => useCoAgentStateRender
});
module.exports = __toCommonJS(use_coagent_state_render_exports);
var import_react3 = require("react");

// src/context/copilot-context.tsx
var import_react = __toESM(require("react"));
var emptyCopilotContext = {
  actions: {},
  setAction: () => {
  },
  removeAction: () => {
  },
  coAgentStateRenders: {},
  setCoAgentStateRender: () => {
  },
  removeCoAgentStateRender: () => {
  },
  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },
  getContextString: (documents, categories) => returnAndThrowInDebug(""),
  addContext: () => "",
  removeContext: () => {
  },
  getFunctionCallHandler: () => returnAndThrowInDebug(() => __async(void 0, null, function* () {
  })),
  isLoading: false,
  setIsLoading: () => returnAndThrowInDebug(false),
  chatInstructions: "",
  setChatInstructions: () => returnAndThrowInDebug(""),
  additionalInstructions: [],
  setAdditionalInstructions: () => returnAndThrowInDebug([]),
  getDocumentsContext: (categories) => returnAndThrowInDebug([]),
  addDocumentContext: () => returnAndThrowInDebug(""),
  removeDocumentContext: () => {
  },
  runtimeClient: {},
  copilotApiConfig: new class {
    get chatApiEndpoint() {
      throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
    }
    get headers() {
      return {};
    }
    get body() {
      return {};
    }
  }(),
  chatSuggestionConfiguration: {},
  addChatSuggestionConfiguration: () => {
  },
  removeChatSuggestionConfiguration: () => {
  },
  showDevConsole: "auto",
  coagentStates: {},
  setCoagentStates: () => {
  },
  coagentStatesRef: { current: {} },
  setCoagentStatesWithRef: () => {
  },
  agentSession: null,
  setAgentSession: () => {
  },
  forwardedParameters: {},
  agentLock: null,
  threadId: "",
  setThreadId: () => {
  },
  runId: null,
  setRunId: () => {
  },
  chatAbortControllerRef: { current: null },
  availableAgents: [],
  extensions: {},
  setExtensions: () => {
  },
  langGraphInterruptAction: null,
  setLangGraphInterruptAction: () => null,
  removeLangGraphInterruptAction: () => null
};
var CopilotContext = import_react.default.createContext(emptyCopilotContext);
function returnAndThrowInDebug(_value) {
  throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
}

// src/hooks/use-coagent-state-render.ts
var import_shared = require("@copilotkit/shared");

// src/components/toast/toast-provider.tsx
var import_react2 = require("react");
var import_jsx_runtime = require("react/jsx-runtime");
var ToastContext = (0, import_react2.createContext)(void 0);
function useToast() {
  const context = (0, import_react2.useContext)(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}

// src/hooks/use-coagent-state-render.ts
function useCoAgentStateRender(action, dependencies) {
  const {
    setCoAgentStateRender,
    removeCoAgentStateRender,
    coAgentStateRenders,
    chatComponentsCache,
    availableAgents
  } = (0, import_react3.useContext)(CopilotContext);
  const idRef = (0, import_react3.useRef)((0, import_shared.randomId)());
  const { addToast } = useToast();
  (0, import_react3.useEffect)(() => {
    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === action.name)) {
      const message = `(useCoAgentStateRender): Agent "${action.name}" not found. Make sure the agent exists and is properly configured.`;
      addToast({ type: "warning", message });
    }
  }, [availableAgents]);
  const key = `${action.name}-${action.nodeName || "global"}`;
  if (dependencies === void 0) {
    if (coAgentStateRenders[idRef.current]) {
      coAgentStateRenders[idRef.current].handler = action.handler;
      if (typeof action.render === "function") {
        if (chatComponentsCache.current !== null) {
          chatComponentsCache.current.coAgentStateRenders[key] = action.render;
        }
      }
    }
  }
  (0, import_react3.useEffect)(() => {
    const currentId = idRef.current;
    const hasDuplicate = Object.entries(coAgentStateRenders).some(([id, otherAction]) => {
      if (id === currentId)
        return false;
      if (otherAction.name !== action.name)
        return false;
      const hasNodeName = !!action.nodeName;
      const hasOtherNodeName = !!otherAction.nodeName;
      if (!hasNodeName && !hasOtherNodeName)
        return true;
      if (hasNodeName !== hasOtherNodeName)
        return false;
      return action.nodeName === otherAction.nodeName;
    });
    if (hasDuplicate) {
      const message = action.nodeName ? `Found multiple state renders for agent ${action.name} and node ${action.nodeName}. State renders might get overridden` : `Found multiple state renders for agent ${action.name}. State renders might get overridden`;
      addToast({
        type: "warning",
        message,
        id: `dup-action-${action.name}`
      });
    }
  }, [coAgentStateRenders]);
  (0, import_react3.useEffect)(() => {
    setCoAgentStateRender(idRef.current, action);
    if (chatComponentsCache.current !== null && action.render !== void 0) {
      chatComponentsCache.current.coAgentStateRenders[key] = action.render;
    }
    return () => {
      removeCoAgentStateRender(idRef.current);
    };
  }, [
    setCoAgentStateRender,
    removeCoAgentStateRender,
    action.name,
    // include render only if it's a string
    typeof action.render === "string" ? action.render : void 0,
    // dependencies set by the developer
    ...dependencies || []
  ]);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useCoAgentStateRender
});
//# sourceMappingURL=use-coagent-state-render.js.map