{"version": 3, "sources": ["../../src/utils/index.ts", "../../src/graphql/types/response-status.type.ts", "../../src/utils/failed-response-status-reasons.ts"], "sourcesContent": ["export * from \"./failed-response-status-reasons\";\n", "import { <PERSON>rap<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"graphql-scalars\";\nimport { Field, InterfaceType, ObjectType, createUnionType, registerEnumType } from \"type-graphql\";\n\nexport enum ResponseStatusCode {\n  Pending = \"pending\",\n  Success = \"success\",\n  Failed = \"failed\",\n}\n\nregisterEnumType(ResponseStatusCode, {\n  name: \"ResponseStatusCode\",\n});\n\n@InterfaceType({\n  resolveType(value) {\n    if (value.code === ResponseStatusCode.Success) {\n      return SuccessResponseStatus;\n    } else if (value.code === ResponseStatusCode.Failed) {\n      return FailedResponseStatus;\n    } else if (value.code === ResponseStatusCode.Pending) {\n      return PendingResponseStatus;\n    }\n    return undefined;\n  },\n})\n@ObjectType()\nabstract class BaseResponseStatus {\n  @Field(() => ResponseStatusCode)\n  code: ResponseStatusCode;\n}\n\n@ObjectType({ implements: BaseResponseStatus })\nexport class PendingResponseStatus extends BaseResponseStatus {\n  code: ResponseStatusCode = ResponseStatusCode.Pending;\n}\n\n@ObjectType({ implements: BaseResponseStatus })\nexport class SuccessResponseStatus extends BaseResponseStatus {\n  code: ResponseStatusCode = ResponseStatusCode.Success;\n}\n\nexport enum FailedResponseStatusReason {\n  GUARDRAILS_VALIDATION_FAILED = \"GUARDRAILS_VALIDATION_FAILED\",\n  MESSAGE_STREAM_INTERRUPTED = \"MESSAGE_STREAM_INTERRUPTED\",\n  UNKNOWN_ERROR = \"UNKNOWN_ERROR\",\n}\n\nregisterEnumType(FailedResponseStatusReason, {\n  name: \"FailedResponseStatusReason\",\n});\n\n@ObjectType({ implements: BaseResponseStatus })\nexport class FailedResponseStatus extends BaseResponseStatus {\n  code: ResponseStatusCode = ResponseStatusCode.Failed;\n\n  @Field(() => FailedResponseStatusReason)\n  reason: FailedResponseStatusReason;\n\n  @Field(() => GraphQLJSON, { nullable: true })\n  details?: Record<string, any> = null;\n}\n\nexport const ResponseStatusUnion = createUnionType({\n  name: \"ResponseStatus\",\n  types: () => [PendingResponseStatus, SuccessResponseStatus, FailedResponseStatus] as const,\n});\n", "import {\n  FailedResponseStatus,\n  FailedResponseStatusReason,\n} from \"../graphql/types/response-status.type\";\n\nexport class GuardrailsValidationFailureResponse extends FailedResponseStatus {\n  reason = FailedResponseStatusReason.GUARDRAILS_VALIDATION_FAILED;\n  declare details: {\n    guardrailsReason: string;\n  };\n\n  constructor({ guardrailsReason }) {\n    super();\n    this.details = {\n      guardrailsReason,\n    };\n  }\n}\n\nexport class MessageStreamInterruptedResponse extends FailedResponseStatus {\n  reason = FailedResponseStatusReason.MESSAGE_STREAM_INTERRUPTED;\n  declare details: {\n    messageId: string;\n    description: string;\n  };\n\n  constructor({ messageId }: { messageId: string }) {\n    super();\n    this.details = {\n      messageId,\n      description: \"Check the message for mode details\",\n    };\n  }\n}\n\nexport class UnknownErrorResponse extends FailedResponseStatus {\n  reason = FailedResponseStatusReason.UNKNOWN_ERROR;\n  declare details: {\n    description?: string;\n  };\n\n  constructor({ description }: { description?: string }) {\n    super();\n    this.details = {\n      description,\n    };\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;ACAA,6BAA4B;AAC5B,0BAAoF;;;;;;;;;;;;;;;;;;UAExEA,qBAAAA;;;;GAAAA,uBAAAA,qBAAAA,CAAAA,EAAAA;IAMZC,sCAAiBD,oBAAoB;EACnCE,MAAM;AACR,CAAA;AAeA,IAAeC,qBAAf,6BAAeA,oBAAAA;EAEbC;AACF,GAHA;;MACGC,2BAAM,MAAML,kBAAAA;;GADAG,mBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,qBAAAA,aAAAA;MAbdG,mCAAc;IACbC,YAAYC,OAAK;AACf,UAAIA,MAAMJ,SAAI,WAAiC;AAC7C,eAAOK;MACT,WAAWD,MAAMJ,SAAI,UAAgC;AACnD,eAAOM;MACT,WAAWF,MAAMJ,SAAI,WAAiC;AACpD,eAAOO;MACT;AACA,aAAOC;IACT;EACF,CAAA;MACCC,gCAAAA;GACcV,kBAAAA;AAMR,IAAMQ,wBAAN,cAAoCR,mBAAAA;EACzCC,OAAAA;AACF;AAFaO;AAAAA,wBAAAA,aAAAA;MADZE,gCAAW;IAAEC,YAAYX;EAAmB,CAAA;GAChCQ,qBAAAA;AAKN,IAAMF,wBAAN,cAAoCN,mBAAAA;EACzCC,OAAAA;AACF;AAFaK;AAAAA,wBAAAA,aAAAA;MADZI,gCAAW;IAAEC,YAAYX;EAAmB,CAAA;GAChCM,qBAAAA;;UAIDM,6BAAAA;;;;GAAAA,+BAAAA,6BAAAA,CAAAA,EAAAA;IAMZd,sCAAiBc,4BAA4B;EAC3Cb,MAAM;AACR,CAAA;AAGO,IAAMQ,uBAAN,cAAmCP,mBAAAA;EACxCC,OAAAA;EAGAY;EAGAC,UAAgC;AAClC;AARaP;;MAGVL,2BAAM,MAAMU,0BAAAA;;GAHFL,qBAAAA,WAAAA,UAAAA,MAAAA;;MAMVL,2BAAM,MAAMa,oCAAa;IAAEC,UAAU;EAAK,CAAA;qCACjC,WAAA,cAAA,SAAA,MAAA;GAPCT,qBAAAA,WAAAA,WAAAA,MAAAA;AAAAA,uBAAAA,aAAAA;MADZG,gCAAW;IAAEC,YAAYX;EAAmB,CAAA;GAChCO,oBAAAA;AAUN,IAAMU,0BAAsBC,qCAAgB;EACjDnB,MAAM;EACNoB,OAAO,MAAM;IAACX;IAAuBF;IAAuBC;;AAC9D,CAAA;;;AC5DO,IAAMa,sCAAN,cAAkDC,qBAAAA;EACvDC,SAASC,2BAA2BC;EAKpCC,YAAY,EAAEC,iBAAgB,GAAI;AAChC,UAAK;AACL,SAAKC,UAAU;MACbD;IACF;EACF;AACF;AAZaN;AAcN,IAAMQ,mCAAN,cAA+CP,qBAAAA;EACpDC,SAASC,2BAA2BM;EAMpCJ,YAAY,EAAEK,UAAS,GAA2B;AAChD,UAAK;AACL,SAAKH,UAAU;MACbG;MACAC,aAAa;IACf;EACF;AACF;AAdaH;AAgBN,IAAMI,uBAAN,cAAmCX,qBAAAA;EACxCC,SAASC,2BAA2BU;EAKpCR,YAAY,EAAEM,YAAW,GAA8B;AACrD,UAAK;AACL,SAAKJ,UAAU;MACbI;IACF;EACF;AACF;AAZaC;", "names": ["ResponseStatusCode", "registerEnumType", "name", "BaseResponseStatus", "code", "Field", "InterfaceType", "resolveType", "value", "SuccessResponseStatus", "FailedResponseStatus", "PendingResponseStatus", "undefined", "ObjectType", "implements", "FailedResponseStatusReason", "reason", "details", "GraphQLJSON", "nullable", "ResponseStatusUnion", "createUnionType", "types", "GuardrailsValidationFailureResponse", "FailedResponseStatus", "reason", "FailedResponseStatusReason", "GUARDRAILS_VALIDATION_FAILED", "constructor", "guardrailsReason", "details", "MessageStreamInterruptedResponse", "MESSAGE_STREAM_INTERRUPTED", "messageId", "description", "UnknownErrorResponse", "UNKNOWN_ERROR"]}