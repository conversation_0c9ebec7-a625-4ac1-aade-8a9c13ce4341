{"version": 3, "sources": ["../../src/hooks/use-copilot-action.ts", "../../src/context/copilot-context.tsx", "../../src/components/error-boundary/error-utils.tsx", "../../src/components/toast/toast-provider.tsx", "../../src/components/toast/exclamation-mark-icon.tsx"], "sourcesContent": ["/**\n * Example usage of useCopilotAction with complex parameters:\n *\n * @example\n * useCopilotAction({\n *   name: \"myAction\",\n *   parameters: [\n *     { name: \"arg1\", type: \"string\", enum: [\"option1\", \"option2\", \"option3\"], required: false },\n *     { name: \"arg2\", type: \"number\" },\n *     {\n *       name: \"arg3\",\n *       type: \"object\",\n *       attributes: [\n *         { name: \"nestedArg1\", type: \"boolean\" },\n *         { name: \"xyz\", required: false },\n *       ],\n *     },\n *     { name: \"arg4\", type: \"number[]\" },\n *   ],\n *   handler: ({ arg1, arg2, arg3, arg4 }) => {\n *     const x = arg3.nestedArg1;\n *     const z = arg3.xyz;\n *     console.log(arg1, arg2, arg3);\n *   },\n * });\n *\n * @example\n * // Simple action without parameters\n * useCopilotAction({\n *   name: \"myAction\",\n *   handler: () => {\n *     console.log(\"No parameters provided.\");\n *   },\n * });\n *\n * @example\n * // Interactive action with UI rendering and response handling\n * useCopilotAction({\n *   name: \"handleMeeting\",\n *   description: \"Handle a meeting by booking or canceling\",\n *   parameters: [\n *     {\n *       name: \"meeting\",\n *       type: \"string\",\n *       description: \"The meeting to handle\",\n *       required: true,\n *     },\n *     {\n *       name: \"date\",\n *       type: \"string\",\n *       description: \"The date of the meeting\",\n *       required: true,\n *     },\n *     {\n *       name: \"title\",\n *       type: \"string\",\n *       description: \"The title of the meeting\",\n *       required: true,\n *     },\n *   ],\n *   renderAndWaitForResponse: ({ args, respond, status }) => {\n *     const { meeting, date, title } = args;\n *     return (\n *       <MeetingConfirmationDialog\n *         meeting={meeting}\n *         date={date}\n *         title={title}\n *         onConfirm={() => respond('meeting confirmed')}\n *         onCancel={() => respond('meeting canceled')}\n *       />\n *     );\n *   },\n * });\n *\n * @example\n * // Catch all action allows you to render actions that are not defined in the frontend\n * useCopilotAction({\n *   name: \"*\",\n *   render: ({ name, args, status, result, handler, respond }) => {\n *     return <div>Rendering action: {name}</div>;\n *   },\n * });\n */\n\n/**\n * <img src=\"/images/use-copilot-action/useCopilotAction.gif\" width=\"500\" />\n * `useCopilotAction` is a React hook that you can use in your application to provide\n * custom actions that can be called by the AI. Essentially, it allows the Copilot to\n * execute these actions contextually during a chat, based on the user's interactions\n * and needs.\n *\n * Here's how it works:\n *\n * Use `useCopilotAction` to set up actions that the Copilot can call. To provide\n * more context to the Copilot, you can provide it with a `description` (for example to explain\n * what the action does, under which conditions it can be called, etc.).\n *\n * Then you define the parameters of the action, which can be simple, e.g. primitives like strings or numbers,\n * or complex, e.g. objects or arrays.\n *\n * Finally, you provide a `handler` function that receives the parameters and returns a result.\n * CopilotKit takes care of automatically inferring the parameter types, so you get type safety\n * and autocompletion for free.\n *\n * To render a custom UI for the action, you can provide a `render()` function. This function\n * lets you render a custom component or return a string to display.\n *\n * ## Usage\n *\n * ### Simple Usage\n *\n * ```tsx\n * useCopilotAction({\n *   name: \"sayHello\",\n *   description: \"Say hello to someone.\",\n *   parameters: [\n *     {\n *       name: \"name\",\n *       type: \"string\",\n *       description: \"name of the person to say greet\",\n *     },\n *   ],\n *   handler: async ({ name }) => {\n *     alert(`Hello, ${name}!`);\n *   },\n * });\n * ```\n *\n * ## Generative UI\n *\n * This hooks enables you to dynamically generate UI elements and render them in the copilot chat. For more information, check out the [Generative UI](/guides/generative-ui) page.\n */\nimport { Parameter, randomId } from \"@copilotkit/shared\";\nimport { createElement, Fragment, useEffect, useRef } from \"react\";\nimport { useCopilotContext } from \"../context/copilot-context\";\nimport { useAsyncCallback } from \"../components/error-boundary/error-utils\";\nimport {\n  ActionRenderProps,\n  ActionRenderPropsNoArgsWait,\n  ActionRenderPropsWait,\n  CatchAllFrontendAction,\n  FrontendAction,\n} from \"../types/frontend-action\";\nimport { useToast } from \"../components/toast/toast-provider\";\n\n// We implement useCopilotAction dependency handling so that\n// the developer has the option to not provide any dependencies.\n// In this case, we assume they want to update the handler on each rerender.\n// To avoid getting stuck in an infinite loop, we update the handler directly,\n// skipping React state updates.\n// This is ok in this case, because the handler is not part of any UI that\n// needs to be updated.\n// useCallback, useMemo or other memoization techniques are not suitable here,\n// because they will cause a infinite rerender loop.\nexport function useCopilotAction<const T extends Parameter[] | [] = []>(\n  action: FrontendAction<T> | CatchAllFrontendAction,\n  dependencies?: any[],\n): void {\n  const { setAction, removeAction, actions, chatComponentsCache } = useCopilotContext();\n  const idRef = useRef<string>(randomId());\n  const renderAndWaitRef = useRef<RenderAndWaitForResponse | null>(null);\n  const { addToast } = useToast();\n\n  // clone the action to avoid mutating the original object\n  action = { ...action };\n\n  // If the developer provides a renderAndWaitForResponse function, we transform the action\n  // to use a promise internally, so that we can treat it like a normal action.\n  if (\n    // renderAndWaitForResponse is not available for catch all actions\n    isFrontendAction(action) &&\n    // check if renderAndWaitForResponse is set\n    (action.renderAndWait || action.renderAndWaitForResponse)\n  ) {\n    const renderAndWait = action.renderAndWait || action.renderAndWaitForResponse;\n    // remove the renderAndWait function from the action\n    action.renderAndWait = undefined;\n    action.renderAndWaitForResponse = undefined;\n    // add a handler that will be called when the action is executed\n    action.handler = useAsyncCallback(async () => {\n      // we create a new promise when the handler is called\n      let resolve: (result: any) => void;\n      let reject: (error: any) => void;\n      const promise = new Promise<any>((resolvePromise, rejectPromise) => {\n        resolve = resolvePromise;\n        reject = rejectPromise;\n      });\n      renderAndWaitRef.current = { promise, resolve: resolve!, reject: reject! };\n      // then we await the promise (it will be resolved in the original renderAndWait function)\n      return await promise;\n    }, []) as any;\n\n    // add a render function that will be called when the action is rendered\n    action.render = ((props: ActionRenderProps<T>): React.ReactElement => {\n      // Specifically for renderAndWaitForResponse the executing state is set too early, causing a race condition\n      // To fit it: we will wait for the handler to be ready\n      let status = props.status;\n      if (props.status === \"executing\" && !renderAndWaitRef.current) {\n        status = \"inProgress\";\n      }\n      // Create type safe waitProps based on whether T extends empty array or not\n      const waitProps = {\n        status,\n        args: props.args,\n        result: props.result,\n        handler: status === \"executing\" ? renderAndWaitRef.current!.resolve : undefined,\n        respond: status === \"executing\" ? renderAndWaitRef.current!.resolve : undefined,\n      } as T extends [] ? ActionRenderPropsNoArgsWait<T> : ActionRenderPropsWait<T>;\n\n      // Type guard to check if renderAndWait is for no args case\n      const isNoArgsRenderWait = (\n        _fn:\n          | ((props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement)\n          | ((props: ActionRenderPropsWait<T>) => React.ReactElement),\n      ): _fn is (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement => {\n        return action.parameters?.length === 0;\n      };\n\n      // Safely call renderAndWait with correct props type\n      if (renderAndWait) {\n        if (isNoArgsRenderWait(renderAndWait)) {\n          return renderAndWait(waitProps as ActionRenderPropsNoArgsWait<T>);\n        } else {\n          return renderAndWait(waitProps as ActionRenderPropsWait<T>);\n        }\n      }\n\n      // Return empty Fragment instead of null\n      return createElement(Fragment);\n    }) as any;\n  }\n\n  // If the developer doesn't provide dependencies, we assume they want to\n  // update handler and render function when the action object changes.\n  // This ensures that any captured variables in the handler are up to date.\n  if (dependencies === undefined) {\n    if (actions[idRef.current]) {\n      // catch all actions don't have a handler\n      if (isFrontendAction(action)) {\n        actions[idRef.current].handler = action.handler as any;\n      }\n      if (typeof action.render === \"function\") {\n        if (chatComponentsCache.current !== null) {\n          // TODO: using as any here because the type definitions are getting to tricky\n          // not wasting time on this now - we know the types are compatible\n          chatComponentsCache.current.actions[action.name] = action.render as any;\n        }\n      }\n    }\n  }\n\n  useEffect(() => {\n    const hasDuplicate = Object.values(actions).some(\n      (otherAction) => otherAction.name === action.name && otherAction !== actions[idRef.current],\n    );\n\n    if (hasDuplicate) {\n      addToast({\n        type: \"warning\",\n        message: `Found an already registered action with name ${action.name}.`,\n        id: `dup-action-${action.name}`,\n      });\n    }\n  }, [actions]);\n\n  useEffect(() => {\n    setAction(idRef.current, action as any);\n    if (chatComponentsCache.current !== null && action.render !== undefined) {\n      // see comment about type safety above\n      chatComponentsCache.current.actions[action.name] = action.render as any;\n    }\n    return () => {\n      // NOTE: For now, we don't remove the chatComponentsCache entry when the action is removed.\n      // This is because we currently don't have access to the messages array in CopilotContext.\n      // UPDATE: We now have access, we should remove the entry if not referenced by any message.\n      removeAction(idRef.current);\n    };\n  }, [\n    setAction,\n    removeAction,\n    isFrontendAction(action) ? action.description : undefined,\n    action.name,\n    isFrontendAction(action) ? action.disabled : undefined,\n    isFrontendAction(action) ? action.available : undefined,\n    // This should be faster than deep equality checking\n    // In addition, all major JS engines guarantee the order of object keys\n    JSON.stringify(isFrontendAction(action) ? action.parameters : []),\n    // include render only if it's a string\n    typeof action.render === \"string\" ? action.render : undefined,\n    // dependencies set by the developer\n    ...(dependencies || []),\n  ]);\n}\n\nfunction isFrontendAction<T extends Parameter[]>(\n  action: FrontendAction<T> | CatchAllFrontendAction,\n): action is FrontendAction<T> {\n  return action.name !== \"*\";\n}\n\ninterface RenderAndWaitForResponse {\n  promise: Promise<any>;\n  resolve: (result: any) => void;\n  reject: (error: any) => void;\n}\n", "import { CopilotCloudConfig, FunctionCallHandler } from \"@copilotkit/shared\";\nimport {\n  ActionRenderProps,\n  CatchAllActionRenderProps,\n  FrontendAction,\n} from \"../types/frontend-action\";\nimport React from \"react\";\nimport { TreeNodeId } from \"../hooks/use-tree\";\nimport { DocumentPointer } from \"../types\";\nimport { CopilotChatSuggestionConfiguration } from \"../types/chat-suggestion-configuration\";\nimport { CoAgentStateRender, CoAgentStateRenderProps } from \"../types/coagent-action\";\nimport { CoagentState } from \"../types/coagent-state\";\nimport {\n  CopilotRuntimeClient,\n  ExtensionsInput,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { Agent } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetter,\n} from \"../types/interrupt-action\";\n\n/**\n * Interface for the configuration of the Copilot API.\n */\nexport interface CopilotApiConfig {\n  /**\n   * The public API key for Copilot Cloud.\n   */\n  publicApiKey?: string;\n\n  /**\n   * The configuration for Copilot Cloud.\n   */\n  cloud?: CopilotCloudConfig;\n\n  /**\n   * The endpoint for the chat API.\n   */\n  chatApiEndpoint: string;\n\n  /**\n   * The endpoint for the Copilot transcribe audio service.\n   */\n  transcribeAudioUrl?: string;\n\n  /**\n   * The endpoint for the Copilot text to speech service.\n   */\n  textToSpeechUrl?: string;\n\n  /**\n   * additional headers to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'Authorization': 'Bearer your_token_here'\n   * }\n   * ```\n   */\n  headers: Record<string, string>;\n\n  /**\n   * Custom properties to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'user_id': 'user_id'\n   * }\n   * ```\n   */\n  properties?: Record<string, any>;\n\n  /**\n   * Indicates whether the user agent should send or receive cookies from the other domain\n   * in the case of cross-origin requests.\n   */\n  credentials?: RequestCredentials;\n\n  /**\n   * Optional configuration for connecting to Model Context Protocol (MCP) servers.\n   * This is typically derived from the CopilotKitProps and used internally.\n   * @experimental\n   */\n  mcpServers?: Array<{ endpoint: string; apiKey?: string }>;\n}\n\nexport type InChatRenderFunction<TProps = ActionRenderProps<any> | CatchAllActionRenderProps<any>> =\n  (props: TProps) => string | JSX.Element;\nexport type CoagentInChatRenderFunction = (\n  props: CoAgentStateRenderProps<any>,\n) => string | JSX.Element | undefined | null;\n\nexport interface ChatComponentsCache {\n  actions: Record<string, InChatRenderFunction | string>;\n  coAgentStateRenders: Record<string, CoagentInChatRenderFunction | string>;\n}\n\nexport interface AgentSession {\n  agentName: string;\n  threadId?: string;\n  nodeName?: string;\n}\n\nexport interface AuthState {\n  status: \"authenticated\" | \"unauthenticated\";\n  authHeaders: Record<string, string>;\n  userId?: string;\n  metadata?: Record<string, any>;\n}\n\nexport type ActionName = string;\n\nexport interface CopilotContextParams {\n  // function-calling\n  actions: Record<string, FrontendAction<any>>;\n  setAction: (id: string, action: FrontendAction<any>) => void;\n  removeAction: (id: string) => void;\n\n  // coagent actions\n  coAgentStateRenders: Record<string, CoAgentStateRender<any>>;\n  setCoAgentStateRender: (id: string, stateRender: CoAgentStateRender<any>) => void;\n  removeCoAgentStateRender: (id: string) => void;\n\n  chatComponentsCache: React.RefObject<ChatComponentsCache>;\n\n  getFunctionCallHandler: (\n    customEntryPoints?: Record<string, FrontendAction<any>>,\n  ) => FunctionCallHandler;\n\n  // text context\n  addContext: (context: string, parentId?: string, categories?: string[]) => TreeNodeId;\n  removeContext: (id: TreeNodeId) => void;\n  getContextString: (documents: DocumentPointer[], categories: string[]) => string;\n\n  // document context\n  addDocumentContext: (documentPointer: DocumentPointer, categories?: string[]) => TreeNodeId;\n  removeDocumentContext: (documentId: string) => void;\n  getDocumentsContext: (categories: string[]) => DocumentPointer[];\n\n  isLoading: boolean;\n  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;\n\n  chatSuggestionConfiguration: { [key: string]: CopilotChatSuggestionConfiguration };\n  addChatSuggestionConfiguration: (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => void;\n  removeChatSuggestionConfiguration: (id: string) => void;\n\n  chatInstructions: string;\n  setChatInstructions: React.Dispatch<React.SetStateAction<string>>;\n\n  additionalInstructions?: string[];\n  setAdditionalInstructions: React.Dispatch<React.SetStateAction<string[]>>;\n\n  // api endpoints\n  copilotApiConfig: CopilotApiConfig;\n\n  showDevConsole: boolean | \"auto\";\n\n  // agents\n  coagentStates: Record<string, CoagentState>;\n  setCoagentStates: React.Dispatch<React.SetStateAction<Record<string, CoagentState>>>;\n  coagentStatesRef: React.RefObject<Record<string, CoagentState>>;\n  setCoagentStatesWithRef: (\n    value:\n      | Record<string, CoagentState>\n      | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n  ) => void;\n\n  agentSession: AgentSession | null;\n  setAgentSession: React.Dispatch<React.SetStateAction<AgentSession | null>>;\n\n  agentLock: string | null;\n\n  threadId: string;\n  setThreadId: React.Dispatch<React.SetStateAction<string>>;\n\n  runId: string | null;\n  setRunId: React.Dispatch<React.SetStateAction<string | null>>;\n\n  // The chat abort controller can be used to stop generation globally,\n  // i.e. when using `stop()` from `useChat`\n  chatAbortControllerRef: React.MutableRefObject<AbortController | null>;\n\n  // runtime\n  runtimeClient: CopilotRuntimeClient;\n\n  /**\n   * The forwarded parameters to use for the task.\n   */\n  forwardedParameters?: Pick<ForwardedParametersInput, \"temperature\">;\n  availableAgents: Agent[];\n\n  /**\n   * The auth states for the CopilotKit.\n   */\n  authStates_c?: Record<ActionName, AuthState>;\n  setAuthStates_c?: React.Dispatch<React.SetStateAction<Record<ActionName, AuthState>>>;\n\n  /**\n   * The auth config for the CopilotKit.\n   */\n  authConfig_c?: {\n    SignInComponent: React.ComponentType<{\n      onSignInComplete: (authState: AuthState) => void;\n    }>;\n  };\n\n  extensions: ExtensionsInput;\n  setExtensions: React.Dispatch<React.SetStateAction<ExtensionsInput>>;\n  langGraphInterruptAction: LangGraphInterruptAction | null;\n  setLangGraphInterruptAction: LangGraphInterruptActionSetter;\n  removeLangGraphInterruptAction: () => void;\n}\n\nconst emptyCopilotContext: CopilotContextParams = {\n  actions: {},\n  setAction: () => {},\n  removeAction: () => {},\n\n  coAgentStateRenders: {},\n  setCoAgentStateRender: () => {},\n  removeCoAgentStateRender: () => {},\n\n  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },\n  getContextString: (documents: DocumentPointer[], categories: string[]) =>\n    returnAndThrowInDebug(\"\"),\n  addContext: () => \"\",\n  removeContext: () => {},\n\n  getFunctionCallHandler: () => returnAndThrowInDebug(async () => {}),\n\n  isLoading: false,\n  setIsLoading: () => returnAndThrowInDebug(false),\n\n  chatInstructions: \"\",\n  setChatInstructions: () => returnAndThrowInDebug(\"\"),\n\n  additionalInstructions: [],\n  setAdditionalInstructions: () => returnAndThrowInDebug([]),\n\n  getDocumentsContext: (categories: string[]) => returnAndThrowInDebug([]),\n  addDocumentContext: () => returnAndThrowInDebug(\"\"),\n  removeDocumentContext: () => {},\n  runtimeClient: {} as any,\n\n  copilotApiConfig: new (class implements CopilotApiConfig {\n    get chatApiEndpoint(): string {\n      throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n    }\n\n    get headers(): Record<string, string> {\n      return {};\n    }\n    get body(): Record<string, any> {\n      return {};\n    }\n  })(),\n\n  chatSuggestionConfiguration: {},\n  addChatSuggestionConfiguration: () => {},\n  removeChatSuggestionConfiguration: () => {},\n  showDevConsole: \"auto\",\n  coagentStates: {},\n  setCoagentStates: () => {},\n  coagentStatesRef: { current: {} },\n  setCoagentStatesWithRef: () => {},\n  agentSession: null,\n  setAgentSession: () => {},\n  forwardedParameters: {},\n  agentLock: null,\n  threadId: \"\",\n  setThreadId: () => {},\n  runId: null,\n  setRunId: () => {},\n  chatAbortControllerRef: { current: null },\n  availableAgents: [],\n  extensions: {},\n  setExtensions: () => {},\n  langGraphInterruptAction: null,\n  setLangGraphInterruptAction: () => null,\n  removeLangGraphInterruptAction: () => null,\n};\n\nexport const CopilotContext = React.createContext<CopilotContextParams>(emptyCopilotContext);\n\nexport function useCopilotContext(): CopilotContextParams {\n  const context = React.useContext(CopilotContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n  }\n  return context;\n}\n\nfunction returnAndThrowInDebug<T>(_value: T): T {\n  throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n}\n", "import React, { useCallback } from \"react\";\nimport { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport { useToast } from \"../toast/toast-provider\";\nimport { ExclamationMarkIcon } from \"../toast/exclamation-mark-icon\";\nimport ReactMarkdown from \"react-markdown\";\n\ninterface OriginalError {\n  message?: string;\n  stack?: string;\n}\n\nexport function ErrorToast({ errors }: { errors: (Error | GraphQLError)[] }) {\n  const errorsToRender = errors.map((error, idx) => {\n    const originalError =\n      \"extensions\" in error ? (error.extensions?.originalError as undefined | OriginalError) : {};\n    const message = originalError?.message ?? error.message;\n    const code = \"extensions\" in error ? (error.extensions?.code as string) : null;\n\n    return (\n      <div\n        key={idx}\n        style={{\n          marginTop: idx === 0 ? 0 : 10,\n          marginBottom: 14,\n        }}\n      >\n        <ExclamationMarkIcon style={{ marginBottom: 4 }} />\n\n        {code && (\n          <div\n            style={{\n              fontWeight: \"600\",\n              marginBottom: 4,\n            }}\n          >\n            Copilot Cloud Error:{\" \"}\n            <span style={{ fontFamily: \"monospace\", fontWeight: \"normal\" }}>{code}</span>\n          </div>\n        )}\n        <ReactMarkdown>{message}</ReactMarkdown>\n      </div>\n    );\n  });\n  return (\n    <div\n      style={{\n        fontSize: \"13px\",\n        maxWidth: \"600px\",\n      }}\n    >\n      {errorsToRender}\n      <div style={{ fontSize: \"11px\", opacity: 0.75 }}>\n        NOTE: This error only displays during local development.\n      </div>\n    </div>\n  );\n}\n\nexport function useErrorToast() {\n  const { addToast } = useToast();\n\n  return useCallback(\n    (error: (Error | GraphQLError)[]) => {\n      const errorId = error\n        .map((err) => {\n          const message =\n            \"extensions\" in err\n              ? (err.extensions?.originalError as any)?.message || err.message\n              : err.message;\n          const stack = err.stack || \"\";\n          return btoa(message + stack).slice(0, 32); // Create hash from message + stack\n        })\n        .join(\"|\");\n\n      addToast({\n        type: \"error\",\n        id: errorId, // Toast libraries typically dedupe by id\n        message: <ErrorToast errors={error} />,\n      });\n    },\n    [addToast],\n  );\n}\n\nexport function useAsyncCallback<T extends (...args: any[]) => Promise<any>>(\n  callback: T,\n  deps: Parameters<typeof useCallback>[1],\n) {\n  const addErrorToast = useErrorToast();\n  return useCallback(async (...args: Parameters<T>) => {\n    try {\n      return await callback(...args);\n    } catch (error) {\n      console.error(\"Error in async callback:\", error);\n      // @ts-ignore\n      addErrorToast([error]);\n      throw error;\n    }\n  }, deps);\n}\n", "import { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport React, { createContext, useContext, useState, useCallback } from \"react\";\nimport { ErrorToast } from \"../error-boundary/error-utils\";\nimport { PartialBy } from \"@copilotkit/shared\";\n\ninterface Toast {\n  id: string;\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  duration?: number;\n}\n\ninterface ToastContextValue {\n  toasts: Toast[];\n  addToast: (toast: PartialBy<Toast, \"id\">) => void;\n  addGraphQLErrorsToast: (errors: GraphQLError[]) => void;\n  removeToast: (id: string) => void;\n  enabled: boolean;\n}\n\nconst ToastContext = createContext<ToastContextValue | undefined>(undefined);\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error(\"useToast must be used within a ToastProvider\");\n  }\n  return context;\n}\n\nexport function ToastProvider({\n  enabled,\n  children,\n}: {\n  enabled: boolean;\n  children: React.ReactNode;\n}) {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n  const addToast = useCallback(\n    (toast: PartialBy<Toast, \"id\">) => {\n      // We do not display these errors unless we are in dev mode.\n      if (!enabled) {\n        return;\n      }\n\n      const id = toast.id ?? Math.random().toString(36).substring(2, 9);\n\n      setToasts((currentToasts) => {\n        if (currentToasts.find((toast) => toast.id === id)) return currentToasts;\n        return [...currentToasts, { ...toast, id }];\n      });\n\n      if (toast.duration) {\n        setTimeout(() => {\n          removeToast(id);\n        }, toast.duration);\n      }\n    },\n    [enabled],\n  );\n\n  const addGraphQLErrorsToast = useCallback((errors: GraphQLError[]) => {\n    addToast({\n      type: \"error\",\n      message: <ErrorToast errors={errors} />,\n    });\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));\n  }, []);\n\n  const value = {\n    toasts,\n    addToast,\n    addGraphQLErrorsToast,\n    removeToast,\n    enabled,\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      <div\n        style={{\n          position: \"fixed\",\n          bottom: \"1rem\",\n          left: \"50%\",\n          transform: \"translateX(-50%)\",\n          zIndex: 50,\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"0.5rem\",\n        }}\n      >\n        {toasts.length > 1 && (\n          <div style={{ textAlign: \"right\" }}>\n            <button\n              onClick={() => setToasts([])}\n              style={{\n                padding: \"4px 8px\",\n                fontSize: \"12px\",\n                cursor: \"pointer\",\n                background: \"white\",\n                border: \"1px solid rgba(0,0,0,0.2)\",\n                borderRadius: \"4px\",\n              }}\n            >\n              Close All\n            </button>\n          </div>\n        )}\n        {toasts.map((toast) => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n      {children}\n    </ToastContext.Provider>\n  );\n}\n\nfunction Toast({\n  message,\n  type = \"info\",\n  onClose,\n}: {\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  onClose: () => void;\n}) {\n  const bgColors = {\n    info: \"#3b82f6\",\n    success: \"#22c55e\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  };\n\n  return (\n    <div\n      style={{\n        backgroundColor: bgColors[type],\n        color: \"white\",\n        padding: \"0.5rem 1.5rem\",\n        borderRadius: \"0.25rem\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        position: \"relative\",\n        minWidth: \"200px\",\n      }}\n    >\n      <div>{message}</div>\n      <button\n        onClick={onClose}\n        style={{\n          position: \"absolute\",\n          top: \"0\",\n          right: \"0\",\n          background: \"none\",\n          border: \"none\",\n          color: \"white\",\n          cursor: \"pointer\",\n          padding: \"0.5rem\",\n          fontSize: \"1rem\",\n        }}\n      >\n        ✕\n      </button>\n    </div>\n  );\n}\n", "import React from \"react\";\n\nexport const ExclamationMarkIcon = ({\n  className,\n  style,\n}: {\n  className?: string;\n  style?: React.CSSProperties;\n}) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    className={`lucide lucide-circle-alert ${className ? className : \"\"}`}\n    style={style}\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <line x1=\"12\" x2=\"12\" y1=\"8\" y2=\"12\" />\n    <line x1=\"12\" x2=\"12.01\" y1=\"16\" y2=\"16\" />\n  </svg>\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAoIA,oBAAoC;AACpC,IAAAA,gBAA2D;;;AC/H3D,mBAAkB;AAsNlB,IAAM,sBAA4C;AAAA,EAChD,SAAS,CAAC;AAAA,EACV,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,cAAc,MAAM;AAAA,EAAC;AAAA,EAErB,qBAAqB,CAAC;AAAA,EACtB,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,0BAA0B,MAAM;AAAA,EAAC;AAAA,EAEjC,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,qBAAqB,CAAC,EAAE,EAAE;AAAA,EACzE,kBAAkB,CAAC,WAA8B,eAC/C,sBAAsB,EAAE;AAAA,EAC1B,YAAY,MAAM;AAAA,EAClB,eAAe,MAAM;AAAA,EAAC;AAAA,EAEtB,wBAAwB,MAAM,sBAAsB,MAAY;AAAA,EAAC,EAAC;AAAA,EAElE,WAAW;AAAA,EACX,cAAc,MAAM,sBAAsB,KAAK;AAAA,EAE/C,kBAAkB;AAAA,EAClB,qBAAqB,MAAM,sBAAsB,EAAE;AAAA,EAEnD,wBAAwB,CAAC;AAAA,EACzB,2BAA2B,MAAM,sBAAsB,CAAC,CAAC;AAAA,EAEzD,qBAAqB,CAAC,eAAyB,sBAAsB,CAAC,CAAC;AAAA,EACvE,oBAAoB,MAAM,sBAAsB,EAAE;AAAA,EAClD,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,eAAe,CAAC;AAAA,EAEhB,kBAAkB,IAAK,MAAkC;AAAA,IACvD,IAAI,kBAA0B;AAC5B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAAA,IAEA,IAAI,UAAkC;AACpC,aAAO,CAAC;AAAA,IACV;AAAA,IACA,IAAI,OAA4B;AAC9B,aAAO,CAAC;AAAA,IACV;AAAA,EACF,EAAG;AAAA,EAEH,6BAA6B,CAAC;AAAA,EAC9B,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,mCAAmC,MAAM;AAAA,EAAC;AAAA,EAC1C,gBAAgB;AAAA,EAChB,eAAe,CAAC;AAAA,EAChB,kBAAkB,MAAM;AAAA,EAAC;AAAA,EACzB,kBAAkB,EAAE,SAAS,CAAC,EAAE;AAAA,EAChC,yBAAyB,MAAM;AAAA,EAAC;AAAA,EAChC,cAAc;AAAA,EACd,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,qBAAqB,CAAC;AAAA,EACtB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,OAAO;AAAA,EACP,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,wBAAwB,EAAE,SAAS,KAAK;AAAA,EACxC,iBAAiB,CAAC;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,0BAA0B;AAAA,EAC1B,6BAA6B,MAAM;AAAA,EACnC,gCAAgC,MAAM;AACxC;AAEO,IAAM,iBAAiB,aAAAC,QAAM,cAAoC,mBAAmB;AAEpF,SAAS,oBAA0C;AACxD,QAAM,UAAU,aAAAA,QAAM,WAAW,cAAc;AAC/C,MAAI,YAAY,qBAAqB;AACnC,UAAM,IAAI,MAAM,uEAAuE;AAAA,EACzF;AACA,SAAO;AACT;AAEA,SAAS,sBAAyB,QAAc;AAC9C,QAAM,IAAI,MAAM,uEAAuE;AACzF;;;AC7SA,IAAAC,gBAAmC;;;ACCnC,IAAAC,gBAAwE;AA+DzD;AA5Cf,IAAM,mBAAe,6BAA6C,MAAS;AAEpE,SAAS,WAAW;AACzB,QAAM,cAAU,0BAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,SAAO;AACT;;;ACnBE,IAAAC,sBAAA;AAPK,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AACF,MAIE;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,QAAO;AAAA,IACP,aAAY;AAAA,IACZ,eAAc;AAAA,IACd,gBAAe;AAAA,IACf,WAAW,8BAA8B,YAAY,YAAY;AAAA,IACjE;AAAA,IAEA;AAAA,mDAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,MAC/B,6CAAC,UAAK,IAAG,MAAK,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK;AAAA,MACrC,6CAAC,UAAK,IAAG,MAAK,IAAG,SAAQ,IAAG,MAAK,IAAG,MAAK;AAAA;AAAA;AAC3C;;;AFrBF,4BAA0B;AAsBlB,IAAAC,sBAAA;AAfD,SAAS,WAAW,EAAE,OAAO,GAAyC;AAC3E,QAAM,iBAAiB,OAAO,IAAI,CAAC,OAAO,QAAQ;AAZpD;AAaI,UAAM,gBACJ,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,gBAA8C,CAAC;AAC5F,UAAM,WAAU,oDAAe,YAAf,YAA0B,MAAM;AAChD,UAAM,OAAO,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,OAAkB;AAE1E,WACE;AAAA,MAAC;AAAA;AAAA,QAEC,OAAO;AAAA,UACL,WAAW,QAAQ,IAAI,IAAI;AAAA,UAC3B,cAAc;AAAA,QAChB;AAAA,QAEA;AAAA,uDAAC,uBAAoB,OAAO,EAAE,cAAc,EAAE,GAAG;AAAA,UAEhD,QACC;AAAA,YAAC;AAAA;AAAA,cACC,OAAO;AAAA,gBACL,YAAY;AAAA,gBACZ,cAAc;AAAA,cAChB;AAAA,cACD;AAAA;AAAA,gBACsB;AAAA,gBACrB,6CAAC,UAAK,OAAO,EAAE,YAAY,aAAa,YAAY,SAAS,GAAI,gBAAK;AAAA;AAAA;AAAA,UACxE;AAAA,UAEF,6CAAC,sBAAAC,SAAA,EAAe,mBAAQ;AAAA;AAAA;AAAA,MAnBnB;AAAA,IAoBP;AAAA,EAEJ,CAAC;AACD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MAEC;AAAA;AAAA,QACD,6CAAC,SAAI,OAAO,EAAE,UAAU,QAAQ,SAAS,KAAK,GAAG,sEAEjD;AAAA;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,gBAAgB;AAC9B,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,aAAO;AAAA,IACL,CAAC,UAAoC;AACnC,YAAM,UAAU,MACb,IAAI,CAAC,QAAQ;AAhEtB;AAiEU,cAAM,UACJ,gBAAgB,QACX,eAAI,eAAJ,mBAAgB,kBAAhB,mBAAuC,YAAW,IAAI,UACvD,IAAI;AACV,cAAM,QAAQ,IAAI,SAAS;AAC3B,eAAO,KAAK,UAAU,KAAK,EAAE,MAAM,GAAG,EAAE;AAAA,MAC1C,CAAC,EACA,KAAK,GAAG;AAEX,eAAS;AAAA,QACP,MAAM;AAAA,QACN,IAAI;AAAA;AAAA,QACJ,SAAS,6CAAC,cAAW,QAAQ,OAAO;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACF;AAEO,SAAS,iBACd,UACA,MACA;AACA,QAAM,gBAAgB,cAAc;AACpC,aAAO,2BAAY,IAAU,SAAwB;AACnD,QAAI;AACF,aAAO,MAAM,SAAS,GAAG,IAAI;AAAA,IAC/B,SAAS,OAAP;AACA,cAAQ,MAAM,4BAA4B,KAAK;AAE/C,oBAAc,CAAC,KAAK,CAAC;AACrB,YAAM;AAAA,IACR;AAAA,EACF,IAAG,IAAI;AACT;;;AFuDO,SAAS,iBACd,QACA,cACM;AACN,QAAM,EAAE,WAAW,cAAc,SAAS,oBAAoB,IAAI,kBAAkB;AACpF,QAAM,YAAQ,0BAAe,wBAAS,CAAC;AACvC,QAAM,uBAAmB,sBAAwC,IAAI;AACrE,QAAM,EAAE,SAAS,IAAI,SAAS;AAG9B,WAAS,mBAAK;AAId;AAAA;AAAA,IAEE,iBAAiB,MAAM;AAAA,KAEtB,OAAO,iBAAiB,OAAO;AAAA,IAChC;AACA,UAAM,gBAAgB,OAAO,iBAAiB,OAAO;AAErD,WAAO,gBAAgB;AACvB,WAAO,2BAA2B;AAElC,WAAO,UAAU,iBAAiB,MAAY;AAE5C,UAAI;AACJ,UAAI;AACJ,YAAM,UAAU,IAAI,QAAa,CAAC,gBAAgB,kBAAkB;AAClE,kBAAU;AACV,iBAAS;AAAA,MACX,CAAC;AACD,uBAAiB,UAAU,EAAE,SAAS,SAAmB,OAAgB;AAEzE,aAAO,MAAM;AAAA,IACf,IAAG,CAAC,CAAC;AAGL,WAAO,SAAU,CAAC,UAAoD;AAGpE,UAAI,SAAS,MAAM;AACnB,UAAI,MAAM,WAAW,eAAe,CAAC,iBAAiB,SAAS;AAC7D,iBAAS;AAAA,MACX;AAEA,YAAM,YAAY;AAAA,QAChB;AAAA,QACA,MAAM,MAAM;AAAA,QACZ,QAAQ,MAAM;AAAA,QACd,SAAS,WAAW,cAAc,iBAAiB,QAAS,UAAU;AAAA,QACtE,SAAS,WAAW,cAAc,iBAAiB,QAAS,UAAU;AAAA,MACxE;AAGA,YAAM,qBAAqB,CACzB,QAGyE;AAtNjF;AAuNQ,iBAAO,YAAO,eAAP,mBAAmB,YAAW;AAAA,MACvC;AAGA,UAAI,eAAe;AACjB,YAAI,mBAAmB,aAAa,GAAG;AACrC,iBAAO,cAAc,SAA2C;AAAA,QAClE,OAAO;AACL,iBAAO,cAAc,SAAqC;AAAA,QAC5D;AAAA,MACF;AAGA,iBAAO,6BAAc,sBAAQ;AAAA,IAC/B;AAAA,EACF;AAKA,MAAI,iBAAiB,QAAW;AAC9B,QAAI,QAAQ,MAAM,OAAO,GAAG;AAE1B,UAAI,iBAAiB,MAAM,GAAG;AAC5B,gBAAQ,MAAM,OAAO,EAAE,UAAU,OAAO;AAAA,MAC1C;AACA,UAAI,OAAO,OAAO,WAAW,YAAY;AACvC,YAAI,oBAAoB,YAAY,MAAM;AAGxC,8BAAoB,QAAQ,QAAQ,OAAO,IAAI,IAAI,OAAO;AAAA,QAC5D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,+BAAU,MAAM;AACd,UAAM,eAAe,OAAO,OAAO,OAAO,EAAE;AAAA,MAC1C,CAAC,gBAAgB,YAAY,SAAS,OAAO,QAAQ,gBAAgB,QAAQ,MAAM,OAAO;AAAA,IAC5F;AAEA,QAAI,cAAc;AAChB,eAAS;AAAA,QACP,MAAM;AAAA,QACN,SAAS,gDAAgD,OAAO;AAAA,QAChE,IAAI,cAAc,OAAO;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,OAAO,CAAC;AAEZ,+BAAU,MAAM;AACd,cAAU,MAAM,SAAS,MAAa;AACtC,QAAI,oBAAoB,YAAY,QAAQ,OAAO,WAAW,QAAW;AAEvE,0BAAoB,QAAQ,QAAQ,OAAO,IAAI,IAAI,OAAO;AAAA,IAC5D;AACA,WAAO,MAAM;AAIX,mBAAa,MAAM,OAAO;AAAA,IAC5B;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA,iBAAiB,MAAM,IAAI,OAAO,cAAc;AAAA,IAChD,OAAO;AAAA,IACP,iBAAiB,MAAM,IAAI,OAAO,WAAW;AAAA,IAC7C,iBAAiB,MAAM,IAAI,OAAO,YAAY;AAAA;AAAA;AAAA,IAG9C,KAAK,UAAU,iBAAiB,MAAM,IAAI,OAAO,aAAa,CAAC,CAAC;AAAA;AAAA,IAEhE,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS;AAAA;AAAA,IAEpD,GAAI,gBAAgB,CAAC;AAAA,EACvB,CAAC;AACH;AAEA,SAAS,iBACP,QAC6B;AAC7B,SAAO,OAAO,SAAS;AACzB;", "names": ["import_react", "React", "import_react", "import_react", "import_jsx_runtime", "import_jsx_runtime", "ReactMarkdown"]}