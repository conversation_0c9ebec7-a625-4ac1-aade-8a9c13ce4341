#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules/@copilotkit/cli/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules/@copilotkit/cli/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules/@copilotkit/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules/@copilotkit/cli/bin/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules/@copilotkit/cli/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules/@copilotkit/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/@copilotkit+cli@0.0.6_@types+node@20.17.52_typescript@5.8.3/node_modules:/Users/<USER>/CascadeProjects/pi_lawyer_ai/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/run.js" "$@"
else
  exec node  "$basedir/../../bin/run.js" "$@"
fi
