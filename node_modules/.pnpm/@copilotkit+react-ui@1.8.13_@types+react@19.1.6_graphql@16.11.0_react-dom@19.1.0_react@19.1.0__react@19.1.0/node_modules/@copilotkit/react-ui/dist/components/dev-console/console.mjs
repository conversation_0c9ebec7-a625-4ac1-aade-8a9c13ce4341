"use client";
import {
  Co<PERSON>lot<PERSON>ev<PERSON>onsole,
  DebugMenuButton
} from "../../chunk-VGPQYMKJ.mjs";
import "../../chunk-Q5V6S67N.mjs";
import "../../chunk-6TCUJ3B7.mjs";
import "../../chunk-KXE2JCUH.mjs";
import "../../chunk-NRA3CFEE.mjs";
import "../../chunk-BH6PCAAL.mjs";
import "../../chunk-XWG3L6QC.mjs";
import "../../chunk-MRXNTQOX.mjs";
export {
  CopilotDevConsole,
  DebugMenuButton as default
};
//# sourceMappingURL=console.mjs.map