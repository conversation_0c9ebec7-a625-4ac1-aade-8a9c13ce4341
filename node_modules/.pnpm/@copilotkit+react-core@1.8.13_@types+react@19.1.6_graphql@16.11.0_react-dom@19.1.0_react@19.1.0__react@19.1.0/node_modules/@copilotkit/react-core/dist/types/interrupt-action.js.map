{"version": 3, "sources": ["../../src/types/interrupt-action.ts"], "sourcesContent": ["import { LangGraphInterruptEvent } from \"@copilotkit/runtime-client-gql\";\nimport { AgentSession } from \"../context/copilot-context\";\n\nexport interface LangGraphInterruptRenderHandlerProps<TEventValue = any> {\n  event: LangGraphInterruptEvent<TEventValue>;\n  resolve: (resolution: string) => void;\n}\n\nexport interface LangGraphInterruptRenderProps<TEventValue = any> {\n  result: unknown;\n  event: LangGraphInterruptEvent<TEventValue>;\n  resolve: (resolution: string) => void;\n}\n\nexport interface LangGraphInterruptRender<TEventValue = any> {\n  id: string;\n  /**\n   * The handler function to handle the event.\n   */\n  handler?: (props: LangGraphInterruptRenderHandlerProps<TEventValue>) => any | Promise<any>;\n  /**\n   * The render function to handle the event.\n   */\n  render?: (props: LangGraphInterruptRenderProps<TEventValue>) => string | React.ReactElement;\n  /**\n   * Method that returns a boolean, indicating if the interrupt action should run\n   * Useful when using multiple interrupts\n   */\n  enabled?: (args: { eventValue: TEventValue; agentMetadata: AgentSession }) => boolean;\n}\n\nexport type LangGraphInterruptAction = LangGraphInterruptRender & {\n  event?: LangGraphInterruptEvent;\n};\n\nexport type LangGraphInterruptActionSetterArgs =\n  | (Partial<LangGraphInterruptRender> & { event?: Partial<LangGraphInterruptEvent> })\n  | null;\nexport type LangGraphInterruptActionSetter = (action: LangGraphInterruptActionSetterArgs) => void;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}