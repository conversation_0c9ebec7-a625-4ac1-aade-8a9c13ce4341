{"version": 3, "sources": ["../../../src/graphql/@generated/index.ts", "../../../src/graphql/@generated/fragment-masking.ts", "../../../src/graphql/@generated/graphql.ts", "../../../src/graphql/@generated/gql.ts"], "sourcesContent": ["export * from \"./fragment-masking\";\nexport * from \"./gql\";", "/* eslint-disable */\nimport type { ResultOf, DocumentTypeDecoration, TypedDocumentNode } from '@graphql-typed-document-node/core';\nimport type { FragmentDefinitionNode } from 'graphql';\nimport type { Incremental } from './graphql';\n\n\nexport type FragmentType<TDocumentType extends DocumentTypeDecoration<any, any>> = TDocumentType extends DocumentTypeDecoration<\n  infer TType,\n  any\n>\n  ? [TType] extends [{ ' $fragmentName'?: infer TKey }]\n    ? TK<PERSON> extends string\n      ? { ' $fragmentRefs'?: { [key in TKey]: TType } }\n      : never\n    : never\n  : never;\n\n// return non-nullable if `fragmentType` is non-nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>>\n): TType;\n// return nullable if `fragmentType` is undefined\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | undefined\n): TType | undefined;\n// return nullable if `fragmentType` is nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | null\n): TType | null;\n// return nullable if `fragmentType` is nullable or undefined\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | null | undefined\n): TType | null | undefined;\n// return array of non-nullable if `fragmentType` is array of non-nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: Array<FragmentType<DocumentTypeDecoration<TType, any>>>\n): Array<TType>;\n// return array of nullable if `fragmentType` is array of nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: Array<FragmentType<DocumentTypeDecoration<TType, any>>> | null | undefined\n): Array<TType> | null | undefined;\n// return readonly array of non-nullable if `fragmentType` is array of non-nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: ReadonlyArray<FragmentType<DocumentTypeDecoration<TType, any>>>\n): ReadonlyArray<TType>;\n// return readonly array of nullable if `fragmentType` is array of nullable\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: ReadonlyArray<FragmentType<DocumentTypeDecoration<TType, any>>> | null | undefined\n): ReadonlyArray<TType> | null | undefined;\nexport function useFragment<TType>(\n  _documentNode: DocumentTypeDecoration<TType, any>,\n  fragmentType: FragmentType<DocumentTypeDecoration<TType, any>> | Array<FragmentType<DocumentTypeDecoration<TType, any>>> | ReadonlyArray<FragmentType<DocumentTypeDecoration<TType, any>>> | null | undefined\n): TType | Array<TType> | ReadonlyArray<TType> | null | undefined {\n  return fragmentType as any;\n}\n\n\nexport function makeFragmentData<\n  F extends DocumentTypeDecoration<any, any>,\n  FT extends ResultOf<F>\n>(data: FT, _fragment: F): FragmentType<F> {\n  return data as FragmentType<F>;\n}\nexport function isFragmentReady<TQuery, TFrag>(\n  queryNode: DocumentTypeDecoration<TQuery, any>,\n  fragmentNode: TypedDocumentNode<TFrag>,\n  data: FragmentType<TypedDocumentNode<Incremental<TFrag>, any>> | null | undefined\n): data is FragmentType<typeof fragmentNode> {\n  const deferredFields = (queryNode as { __meta__?: { deferredFields: Record<string, (keyof TFrag)[]> } }).__meta__\n    ?.deferredFields;\n\n  if (!deferredFields) return true;\n\n  const fragDef = fragmentNode.definitions[0] as FragmentDefinitionNode | undefined;\n  const fragName = fragDef?.name?.value;\n\n  const fields = (fragName && deferredFields[fragName]) || [];\n  return fields.length > 0 && fields.every(field => data && field in data);\n}\n", "/* eslint-disable */\nimport type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';\nexport type Maybe<T> = T | null;\nexport type InputMaybe<T> = Maybe<T>;\nexport type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };\nexport type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };\nexport type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };\nexport type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };\nexport type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };\n/** All built-in and custom scalars, mapped to their actual values */\nexport type Scalars = {\n  ID: { input: string; output: string; }\n  String: { input: string; output: string; }\n  Boolean: { input: boolean; output: boolean; }\n  Int: { input: number; output: number; }\n  Float: { input: number; output: number; }\n  /** A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format. */\n  DateTimeISO: { input: any; output: any; }\n  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */\n  JSON: { input: any; output: any; }\n  /** The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */\n  JSONObject: { input: any; output: any; }\n};\n\nexport type ActionExecutionMessageInput = {\n  arguments: Scalars['String']['input'];\n  name: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  /** @deprecated This field will be removed in a future version */\n  scope?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type ActionExecutionMessageOutput = BaseMessageOutput & {\n  __typename?: 'ActionExecutionMessageOutput';\n  arguments: Array<Scalars['String']['output']>;\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  name: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  /** @deprecated This field will be removed in a future version */\n  scope?: Maybe<Scalars['String']['output']>;\n  status: MessageStatus;\n};\n\nexport type ActionInput = {\n  available?: InputMaybe<ActionInputAvailability>;\n  description: Scalars['String']['input'];\n  jsonSchema: Scalars['String']['input'];\n  name: Scalars['String']['input'];\n};\n\n/** The availability of the frontend action */\nexport enum ActionInputAvailability {\n  Disabled = 'disabled',\n  Enabled = 'enabled',\n  Remote = 'remote'\n}\n\nexport type Agent = {\n  __typename?: 'Agent';\n  description: Scalars['String']['output'];\n  id: Scalars['String']['output'];\n  name: Scalars['String']['output'];\n};\n\nexport type AgentSessionInput = {\n  agentName: Scalars['String']['input'];\n  nodeName?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type AgentStateInput = {\n  agentName: Scalars['String']['input'];\n  config?: InputMaybe<Scalars['String']['input']>;\n  state: Scalars['String']['input'];\n};\n\nexport type AgentStateMessageInput = {\n  active: Scalars['Boolean']['input'];\n  agentName: Scalars['String']['input'];\n  nodeName: Scalars['String']['input'];\n  role: MessageRole;\n  runId: Scalars['String']['input'];\n  running: Scalars['Boolean']['input'];\n  state: Scalars['String']['input'];\n  threadId: Scalars['String']['input'];\n};\n\nexport type AgentStateMessageOutput = BaseMessageOutput & {\n  __typename?: 'AgentStateMessageOutput';\n  active: Scalars['Boolean']['output'];\n  agentName: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  nodeName: Scalars['String']['output'];\n  role: MessageRole;\n  runId: Scalars['String']['output'];\n  running: Scalars['Boolean']['output'];\n  state: Scalars['String']['output'];\n  status: MessageStatus;\n  threadId: Scalars['String']['output'];\n};\n\nexport type AgentsResponse = {\n  __typename?: 'AgentsResponse';\n  agents: Array<Agent>;\n};\n\nexport type BaseMessageOutput = {\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  status: MessageStatus;\n};\n\nexport type BaseMetaEvent = {\n  name: MetaEventName;\n  type: Scalars['String']['output'];\n};\n\nexport type BaseResponseStatus = {\n  code: ResponseStatusCode;\n};\n\nexport type CloudInput = {\n  guardrails?: InputMaybe<GuardrailsInput>;\n};\n\nexport type CopilotKitLangGraphInterruptEvent = BaseMetaEvent & {\n  __typename?: 'CopilotKitLangGraphInterruptEvent';\n  data: CopilotKitLangGraphInterruptEventData;\n  name: MetaEventName;\n  response?: Maybe<Scalars['String']['output']>;\n  type: Scalars['String']['output'];\n};\n\nexport type CopilotKitLangGraphInterruptEventData = {\n  __typename?: 'CopilotKitLangGraphInterruptEventData';\n  messages: Array<BaseMessageOutput>;\n  value: Scalars['String']['output'];\n};\n\n/** The type of Copilot request */\nexport enum CopilotRequestType {\n  Chat = 'Chat',\n  Suggestion = 'Suggestion',\n  Task = 'Task',\n  TextareaCompletion = 'TextareaCompletion',\n  TextareaPopover = 'TextareaPopover'\n}\n\nexport type CopilotResponse = {\n  __typename?: 'CopilotResponse';\n  extensions?: Maybe<ExtensionsResponse>;\n  messages: Array<BaseMessageOutput>;\n  metaEvents?: Maybe<Array<BaseMetaEvent>>;\n  runId?: Maybe<Scalars['String']['output']>;\n  status: ResponseStatus;\n  threadId: Scalars['String']['output'];\n};\n\nexport type ExtensionsInput = {\n  openaiAssistantAPI?: InputMaybe<OpenAiApiAssistantApiInput>;\n};\n\nexport type ExtensionsResponse = {\n  __typename?: 'ExtensionsResponse';\n  openaiAssistantAPI?: Maybe<OpenAiApiAssistantApiResponse>;\n};\n\nexport type FailedMessageStatus = {\n  __typename?: 'FailedMessageStatus';\n  code: MessageStatusCode;\n  reason: Scalars['String']['output'];\n};\n\nexport type FailedResponseStatus = BaseResponseStatus & {\n  __typename?: 'FailedResponseStatus';\n  code: ResponseStatusCode;\n  details?: Maybe<Scalars['JSON']['output']>;\n  reason: FailedResponseStatusReason;\n};\n\nexport enum FailedResponseStatusReason {\n  GuardrailsValidationFailed = 'GUARDRAILS_VALIDATION_FAILED',\n  MessageStreamInterrupted = 'MESSAGE_STREAM_INTERRUPTED',\n  UnknownError = 'UNKNOWN_ERROR'\n}\n\nexport type ForwardedParametersInput = {\n  maxTokens?: InputMaybe<Scalars['Float']['input']>;\n  model?: InputMaybe<Scalars['String']['input']>;\n  stop?: InputMaybe<Array<Scalars['String']['input']>>;\n  temperature?: InputMaybe<Scalars['Float']['input']>;\n  toolChoice?: InputMaybe<Scalars['String']['input']>;\n  toolChoiceFunctionName?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type FrontendInput = {\n  actions: Array<ActionInput>;\n  toDeprecate_fullContext?: InputMaybe<Scalars['String']['input']>;\n  url?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type GenerateCopilotResponseInput = {\n  agentSession?: InputMaybe<AgentSessionInput>;\n  agentState?: InputMaybe<AgentStateInput>;\n  agentStates?: InputMaybe<Array<AgentStateInput>>;\n  cloud?: InputMaybe<CloudInput>;\n  extensions?: InputMaybe<ExtensionsInput>;\n  forwardedParameters?: InputMaybe<ForwardedParametersInput>;\n  frontend: FrontendInput;\n  messages: Array<MessageInput>;\n  metaEvents?: InputMaybe<Array<MetaEventInput>>;\n  metadata: GenerateCopilotResponseMetadataInput;\n  runId?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type GenerateCopilotResponseMetadataInput = {\n  requestType?: InputMaybe<CopilotRequestType>;\n};\n\nexport type GuardrailsInput = {\n  inputValidationRules: GuardrailsRuleInput;\n};\n\nexport type GuardrailsRuleInput = {\n  allowList?: InputMaybe<Array<Scalars['String']['input']>>;\n  denyList?: InputMaybe<Array<Scalars['String']['input']>>;\n};\n\nexport type ImageMessageInput = {\n  bytes: Scalars['String']['input'];\n  format: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  role: MessageRole;\n};\n\nexport type ImageMessageOutput = BaseMessageOutput & {\n  __typename?: 'ImageMessageOutput';\n  bytes: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  format: Scalars['String']['output'];\n  id: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  role: MessageRole;\n  status: MessageStatus;\n};\n\nexport type LangGraphInterruptEvent = BaseMetaEvent & {\n  __typename?: 'LangGraphInterruptEvent';\n  name: MetaEventName;\n  response?: Maybe<Scalars['String']['output']>;\n  type: Scalars['String']['output'];\n  value: Scalars['String']['output'];\n};\n\nexport type LoadAgentStateInput = {\n  agentName: Scalars['String']['input'];\n  threadId: Scalars['String']['input'];\n};\n\nexport type LoadAgentStateResponse = {\n  __typename?: 'LoadAgentStateResponse';\n  messages: Scalars['String']['output'];\n  state: Scalars['String']['output'];\n  threadExists: Scalars['Boolean']['output'];\n  threadId: Scalars['String']['output'];\n};\n\nexport type MessageInput = {\n  actionExecutionMessage?: InputMaybe<ActionExecutionMessageInput>;\n  agentStateMessage?: InputMaybe<AgentStateMessageInput>;\n  createdAt: Scalars['DateTimeISO']['input'];\n  id: Scalars['String']['input'];\n  imageMessage?: InputMaybe<ImageMessageInput>;\n  resultMessage?: InputMaybe<ResultMessageInput>;\n  textMessage?: InputMaybe<TextMessageInput>;\n};\n\n/** The role of the message */\nexport enum MessageRole {\n  Assistant = 'assistant',\n  Developer = 'developer',\n  System = 'system',\n  Tool = 'tool',\n  User = 'user'\n}\n\nexport type MessageStatus = FailedMessageStatus | PendingMessageStatus | SuccessMessageStatus;\n\nexport enum MessageStatusCode {\n  Failed = 'Failed',\n  Pending = 'Pending',\n  Success = 'Success'\n}\n\nexport type MetaEventInput = {\n  messages?: InputMaybe<Array<MessageInput>>;\n  name: MetaEventName;\n  response?: InputMaybe<Scalars['String']['input']>;\n  value: Scalars['String']['input'];\n};\n\n/** Meta event types */\nexport enum MetaEventName {\n  CopilotKitLangGraphInterruptEvent = 'CopilotKitLangGraphInterruptEvent',\n  LangGraphInterruptEvent = 'LangGraphInterruptEvent'\n}\n\nexport type Mutation = {\n  __typename?: 'Mutation';\n  generateCopilotResponse: CopilotResponse;\n};\n\n\nexport type MutationGenerateCopilotResponseArgs = {\n  data: GenerateCopilotResponseInput;\n  properties?: InputMaybe<Scalars['JSONObject']['input']>;\n};\n\nexport type OpenAiApiAssistantApiInput = {\n  runId?: InputMaybe<Scalars['String']['input']>;\n  threadId?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type OpenAiApiAssistantApiResponse = {\n  __typename?: 'OpenAIApiAssistantAPIResponse';\n  runId?: Maybe<Scalars['String']['output']>;\n  threadId?: Maybe<Scalars['String']['output']>;\n};\n\nexport type PendingMessageStatus = {\n  __typename?: 'PendingMessageStatus';\n  code: MessageStatusCode;\n};\n\nexport type PendingResponseStatus = BaseResponseStatus & {\n  __typename?: 'PendingResponseStatus';\n  code: ResponseStatusCode;\n};\n\nexport type Query = {\n  __typename?: 'Query';\n  availableAgents: AgentsResponse;\n  hello: Scalars['String']['output'];\n  loadAgentState: LoadAgentStateResponse;\n};\n\n\nexport type QueryLoadAgentStateArgs = {\n  data: LoadAgentStateInput;\n};\n\nexport type ResponseStatus = FailedResponseStatus | PendingResponseStatus | SuccessResponseStatus;\n\nexport enum ResponseStatusCode {\n  Failed = 'Failed',\n  Pending = 'Pending',\n  Success = 'Success'\n}\n\nexport type ResultMessageInput = {\n  actionExecutionId: Scalars['String']['input'];\n  actionName: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  result: Scalars['String']['input'];\n};\n\nexport type ResultMessageOutput = BaseMessageOutput & {\n  __typename?: 'ResultMessageOutput';\n  actionExecutionId: Scalars['String']['output'];\n  actionName: Scalars['String']['output'];\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  result: Scalars['String']['output'];\n  status: MessageStatus;\n};\n\nexport type SuccessMessageStatus = {\n  __typename?: 'SuccessMessageStatus';\n  code: MessageStatusCode;\n};\n\nexport type SuccessResponseStatus = BaseResponseStatus & {\n  __typename?: 'SuccessResponseStatus';\n  code: ResponseStatusCode;\n};\n\nexport type TextMessageInput = {\n  content: Scalars['String']['input'];\n  parentMessageId?: InputMaybe<Scalars['String']['input']>;\n  role: MessageRole;\n};\n\nexport type TextMessageOutput = BaseMessageOutput & {\n  __typename?: 'TextMessageOutput';\n  content: Array<Scalars['String']['output']>;\n  createdAt: Scalars['DateTimeISO']['output'];\n  id: Scalars['String']['output'];\n  parentMessageId?: Maybe<Scalars['String']['output']>;\n  role: MessageRole;\n  status: MessageStatus;\n};\n\nexport type GenerateCopilotResponseMutationVariables = Exact<{\n  data: GenerateCopilotResponseInput;\n  properties?: InputMaybe<Scalars['JSONObject']['input']>;\n}>;\n\n\nexport type GenerateCopilotResponseMutation = { __typename?: 'Mutation', generateCopilotResponse: { __typename?: 'CopilotResponse', threadId: string, runId?: string | null, extensions?: { __typename?: 'ExtensionsResponse', openaiAssistantAPI?: { __typename?: 'OpenAIApiAssistantAPIResponse', runId?: string | null, threadId?: string | null } | null } | null, messages: Array<{ __typename: 'ActionExecutionMessageOutput', id: string, createdAt: any, name: string, arguments: Array<string>, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'AgentStateMessageOutput', id: string, createdAt: any, threadId: string, state: string, running: boolean, agentName: string, nodeName: string, runId: string, active: boolean, role: MessageRole, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ImageMessageOutput', id: string, createdAt: any, format: string, bytes: string, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ResultMessageOutput', id: string, createdAt: any, result: string, actionExecutionId: string, actionName: string, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'TextMessageOutput', id: string, createdAt: any, content: Array<string>, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } }>, metaEvents?: Array<{ __typename?: 'CopilotKitLangGraphInterruptEvent', type: string, name: MetaEventName, data: { __typename?: 'CopilotKitLangGraphInterruptEventData', value: string, messages: Array<{ __typename: 'ActionExecutionMessageOutput', id: string, createdAt: any, name: string, arguments: Array<string>, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'AgentStateMessageOutput', id: string, createdAt: any, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ImageMessageOutput', id: string, createdAt: any, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'ResultMessageOutput', id: string, createdAt: any, result: string, actionExecutionId: string, actionName: string, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } } | { __typename: 'TextMessageOutput', id: string, createdAt: any, content: Array<string>, role: MessageRole, parentMessageId?: string | null, status: { __typename?: 'FailedMessageStatus', code: MessageStatusCode, reason: string } | { __typename?: 'PendingMessageStatus', code: MessageStatusCode } | { __typename?: 'SuccessMessageStatus', code: MessageStatusCode } }> } } | { __typename?: 'LangGraphInterruptEvent', type: string, name: MetaEventName, value: string }> | null } & ({ __typename?: 'CopilotResponse', status: { __typename?: 'FailedResponseStatus', code: ResponseStatusCode, reason: FailedResponseStatusReason, details?: any | null } | { __typename?: 'PendingResponseStatus', code: ResponseStatusCode } | { __typename?: 'SuccessResponseStatus', code: ResponseStatusCode } } | { __typename?: 'CopilotResponse', status?: never }) };\n\nexport type AvailableAgentsQueryVariables = Exact<{ [key: string]: never; }>;\n\n\nexport type AvailableAgentsQuery = { __typename?: 'Query', availableAgents: { __typename?: 'AgentsResponse', agents: Array<{ __typename?: 'Agent', name: string, id: string, description: string }> } };\n\nexport type LoadAgentStateQueryVariables = Exact<{\n  data: LoadAgentStateInput;\n}>;\n\n\nexport type LoadAgentStateQuery = { __typename?: 'Query', loadAgentState: { __typename?: 'LoadAgentStateResponse', threadId: string, threadExists: boolean, state: string, messages: string } };\n\n\nexport const GenerateCopilotResponseDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"mutation\",\"name\":{\"kind\":\"Name\",\"value\":\"generateCopilotResponse\"},\"variableDefinitions\":[{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}},\"type\":{\"kind\":\"NonNullType\",\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"GenerateCopilotResponseInput\"}}}},{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"}},\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"JSONObject\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"generateCopilotResponse\"},\"arguments\":[{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}}},{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"properties\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"extensions\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"openaiAssistantAPI\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"CopilotResponse\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseResponseStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedResponseStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"details\"}}]}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"__typename\"}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"createdAt\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"SuccessMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"PendingMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"TextMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"content\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}]},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ImageMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"format\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"bytes\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ActionExecutionMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"arguments\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}]},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ResultMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"result\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionExecutionId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionName\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"AgentStateMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"state\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"running\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"agentName\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"nodeName\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"runId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"active\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"metaEvents\"},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"stream\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"LangGraphInterruptEvent\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"type\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"value\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"CopilotKitLangGraphInterruptEvent\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"type\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"__typename\"}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"createdAt\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"BaseMessageOutput\"}},\"directives\":[{\"kind\":\"Directive\",\"name\":{\"kind\":\"Name\",\"value\":\"defer\"}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"status\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"SuccessMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"FailedMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"reason\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"PendingMessageStatus\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"code\"}}]}}]}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"TextMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"content\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"role\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ActionExecutionMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"arguments\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"parentMessageId\"}}]}},{\"kind\":\"InlineFragment\",\"typeCondition\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"ResultMessageOutput\"}},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"result\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionExecutionId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"actionName\"}}]}}]}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"value\"}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<GenerateCopilotResponseMutation, GenerateCopilotResponseMutationVariables>;\nexport const AvailableAgentsDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"query\",\"name\":{\"kind\":\"Name\",\"value\":\"availableAgents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"availableAgents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"agents\"},\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"name\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"id\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"description\"}}]}}]}}]}}]} as unknown as DocumentNode<AvailableAgentsQuery, AvailableAgentsQueryVariables>;\nexport const LoadAgentStateDocument = {\"kind\":\"Document\",\"definitions\":[{\"kind\":\"OperationDefinition\",\"operation\":\"query\",\"name\":{\"kind\":\"Name\",\"value\":\"loadAgentState\"},\"variableDefinitions\":[{\"kind\":\"VariableDefinition\",\"variable\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}},\"type\":{\"kind\":\"NonNullType\",\"type\":{\"kind\":\"NamedType\",\"name\":{\"kind\":\"Name\",\"value\":\"LoadAgentStateInput\"}}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"loadAgentState\"},\"arguments\":[{\"kind\":\"Argument\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"},\"value\":{\"kind\":\"Variable\",\"name\":{\"kind\":\"Name\",\"value\":\"data\"}}}],\"selectionSet\":{\"kind\":\"SelectionSet\",\"selections\":[{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadId\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"threadExists\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"state\"}},{\"kind\":\"Field\",\"name\":{\"kind\":\"Name\",\"value\":\"messages\"}}]}}]}}]} as unknown as DocumentNode<LoadAgentStateQuery, LoadAgentStateQueryVariables>;", "/* eslint-disable */\nimport * as types from './graphql';\nimport type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';\n\n/**\n * Map of all GraphQL operations in the project.\n *\n * This map has several performance disadvantages:\n * 1. It is not tree-shakeable, so it will include all operations in the project.\n * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.\n * 3. It does not support dead code elimination, so it will add unused operations.\n *\n * Therefore it is highly recommended to use the babel or swc plugin for production.\n */\nconst documents = {\n    \"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\": types.GenerateCopilotResponseDocument,\n    \"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\": types.AvailableAgentsDocument,\n    \"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\": types.LoadAgentStateDocument,\n};\n\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n *\n *\n * @example\n * ```ts\n * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);\n * ```\n *\n * The query argument is unknown!\n * Please regenerate the types.\n */\nexport function graphql(source: string): unknown;\n\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n */\nexport function graphql(source: \"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"): (typeof documents)[\"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"];\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n */\nexport function graphql(source: \"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\"): (typeof documents)[\"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\"];\n/**\n * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.\n */\nexport function graphql(source: \"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\"): (typeof documents)[\"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\"];\n\nexport function graphql(source: string) {\n  return (documents as any)[source] ?? {};\n}\n\nexport type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACyDO,SAAS,YACd,eACA,cACgE;AAChE,SAAO;AACT;AAGO,SAAS,iBAGd,MAAU,WAA+B;AACzC,SAAO;AACT;AACO,SAAS,gBACd,WACA,cACA,MAC2C;AA3E7C;AA4EE,QAAM,kBAAkB,eAAiF,aAAjF,mBACpB;AAEJ,MAAI,CAAC;AAAgB,WAAO;AAE5B,QAAM,UAAU,aAAa,YAAY,CAAC;AAC1C,QAAM,YAAW,wCAAS,SAAT,mBAAe;AAEhC,QAAM,SAAU,YAAY,eAAe,QAAQ,KAAM,CAAC;AAC1D,SAAO,OAAO,SAAS,KAAK,OAAO,MAAM,WAAS,QAAQ,SAAS,IAAI;AACzE;;;ACoVO,IAAM,kCAAkC,EAAC,QAAO,YAAW,eAAc,CAAC,EAAC,QAAO,uBAAsB,aAAY,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,GAAE,uBAAsB,CAAC,EAAC,QAAO,sBAAqB,YAAW,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,QAAO,EAAC,QAAO,eAAc,QAAO,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,+BAA8B,EAAC,EAAC,EAAC,GAAE,EAAC,QAAO,sBAAqB,YAAW,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,GAAE,QAAO,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,GAAE,aAAY,CAAC,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,GAAE,SAAQ,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,EAAC,GAAE,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,GAAE,SAAQ,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,qBAAoB,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,qBAAoB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,KAAI,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,qBAAoB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,+BAA8B,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,0BAAyB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oCAAmC,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,KAAI,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,cAAa,CAAC,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,uBAAsB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,UAAS,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,+BAA8B,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,YAAW,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,kBAAiB,iBAAgB,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,oBAAmB,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,aAAY,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC;AACloT,IAAM,0BAA0B,EAAC,QAAO,YAAW,eAAc,CAAC,EAAC,QAAO,uBAAsB,aAAY,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,kBAAiB,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,SAAQ,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,KAAI,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,cAAa,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC;AAC7mB,IAAM,yBAAyB,EAAC,QAAO,YAAW,eAAc,CAAC,EAAC,QAAO,uBAAsB,aAAY,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,iBAAgB,GAAE,uBAAsB,CAAC,EAAC,QAAO,sBAAqB,YAAW,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,GAAE,QAAO,EAAC,QAAO,eAAc,QAAO,EAAC,QAAO,aAAY,QAAO,EAAC,QAAO,QAAO,SAAQ,sBAAqB,EAAC,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,iBAAgB,GAAE,aAAY,CAAC,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,GAAE,SAAQ,EAAC,QAAO,YAAW,QAAO,EAAC,QAAO,QAAO,SAAQ,OAAM,EAAC,EAAC,CAAC,GAAE,gBAAe,EAAC,QAAO,gBAAe,cAAa,CAAC,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,eAAc,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,QAAO,EAAC,GAAE,EAAC,QAAO,SAAQ,QAAO,EAAC,QAAO,QAAO,SAAQ,WAAU,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC,EAAC,CAAC,EAAC;;;AC9Zz7B,IAAM,YAAY;AAAA,EACd,m/FAAy/F;AAAA,EACz/F,4IAAkJ;AAAA,EAClJ,6KAAmL;AACvL;AA6BO,SAAS,QAAQ,QAAgB;AACtC,SAAQ,UAAkB,MAAM,KAAK,CAAC;AACxC;", "names": []}