"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/components/chat/Header.tsx
var Header_exports = {};
__export(Header_exports, {
  Header: () => Header
});
module.exports = __toCommonJS(Header_exports);

// src/components/chat/ChatContext.tsx
var import_react = __toESM(require("react"));

// src/components/chat/Icons.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var SmallSpinnerIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitSpinner", style: { width: "13px", height: "13px" } });

// src/components/chat/ChatContext.tsx
var import_jsx_runtime2 = require("react/jsx-runtime");
var ChatContext = import_react.default.createContext(void 0);
function useChatContext() {
  const context = import_react.default.useContext(ChatContext);
  if (context === void 0) {
    throw new Error(
      "Context not found. Did you forget to wrap your app in a <ChatContextProvider> component?"
    );
  }
  return context;
}

// src/components/dev-console/utils.ts
var import_react_core = require("@copilotkit/react-core");
function shouldShowDevConsole(showDevConsole) {
  if (typeof showDevConsole === "boolean") {
    return showDevConsole;
  }
  return getHostname() === "localhost" || getHostname() === "127.0.0.1" || getHostname() === "0.0.0.0" || getHostname() === "::1";
}
function getHostname() {
  if (typeof window !== "undefined" && window.location) {
    return window.location.hostname;
  }
  return "";
}
function getPublishedCopilotKitVersion(current, forceCheck = false) {
  return __async(this, null, function* () {
    const LOCAL_STORAGE_KEY = "__copilotkit_version_check__";
    const serializedVersion = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (serializedVersion && !forceCheck) {
      try {
        const parsedVersion = JSON.parse(serializedVersion);
        const oneHour = 60 * 60 * 1e3;
        const now = (/* @__PURE__ */ new Date()).getTime();
        if (parsedVersion.current === current && now - new Date(parsedVersion.lastChecked).getTime() < oneHour) {
          return parsedVersion;
        }
      } catch (error) {
        console.error("Failed to parse CopilotKitVersion from localStorage", error);
      }
    }
    try {
      const response = yield fetch("https://api.cloud.copilotkit.ai/check-for-updates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          packages: [
            {
              packageName: "@copilotkit/shared",
              packageVersion: current
            }
          ]
        })
      });
      const data = yield response.json();
      const version = {
        current,
        lastChecked: (/* @__PURE__ */ new Date()).getTime(),
        latest: data.packages[0].latestVersion,
        severity: data.packages[0].severity,
        advisory: data.packages[0].advisory || null
      };
      localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(version));
      return version;
    } catch (error) {
      console.error("Failed to check for updates", error);
      throw error;
    }
  });
}
function logReadables(context) {
  console.log("%cCurrent Readables:", "font-size: 16px; font-weight: bold;");
  const readables = context.getContextString([], import_react_core.defaultCopilotContextCategories).trim();
  if (readables.length === 0) {
    console.log("No readables found");
    return;
  }
  console.log(readables);
}
function logActions(context) {
  console.log("%cCurrent Actions:", "font-size: 16px; font-weight: bold;");
  if (Object.values(context.actions).length === 0) {
    console.log("No actions found");
    return;
  }
  for (const action of Object.values(context.actions)) {
    console.group(action.name);
    console.log("name", action.name);
    console.log("description", action.description);
    console.log("parameters", action.parameters);
    console.groupEnd();
  }
}
function logMessages(context) {
  console.log("%cCurrent Messages:", "font-size: 16px; font-weight: bold;");
  if (context.messages.length === 0) {
    console.log("No messages found");
    return;
  }
  const tableData = context.messages.map((message) => {
    if (message.isTextMessage()) {
      return {
        id: message.id,
        type: "TextMessage",
        role: message.role,
        name: void 0,
        scope: void 0,
        content: message.content
      };
    } else if (message.isActionExecutionMessage()) {
      return {
        id: message.id,
        type: "ActionExecutionMessage",
        role: void 0,
        name: message.name,
        scope: message.parentMessageId,
        content: message.arguments
      };
    } else if (message.isResultMessage()) {
      return {
        id: message.id,
        type: "ResultMessage",
        role: void 0,
        name: message.actionName,
        scope: message.actionExecutionId,
        content: message.result
      };
    } else if (message.isAgentStateMessage()) {
      return {
        id: message.id,
        type: `AgentStateMessage (running: ${message.running})`,
        role: message.role,
        name: void 0,
        scope: message.threadId,
        content: message.state
      };
    }
  });
  console.table(tableData);
}

// src/components/dev-console/console.tsx
var import_react_core2 = require("@copilotkit/react-core");
var import_react3 = require("react");

// src/components/dev-console/icons.tsx
var import_jsx_runtime3 = require("react/jsx-runtime");
var ExclamationMarkTriangleIcon = /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
  "svg",
  {
    width: "13.3967723px",
    height: "12px",
    viewBox: "0 0 13.3967723 12",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "exclamation-triangle", fill: "#CD2121", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
      "path",
      {
        d: "M5.39935802,0.75 C5.97670802,-0.25 7.42007802,-0.25 7.99742802,0.75 L13.193588,9.75 C13.770888,10.75 13.049288,12 11.894588,12 L1.50223802,12 C0.34753802,12 -0.37414898,10.75 0.20319802,9.75 L5.39935802,0.75 Z M6.69838802,2.5 C7.11260802,2.5 7.44838802,2.83579 7.44838802,3.25 L7.44838802,6.25 C7.44838802,6.66421 7.11260802,7 6.69838802,7 C6.28417802,7 5.94838802,6.66421 5.94838802,6.25 L5.94838802,3.25 C5.94838802,2.83579 6.28417802,2.5 6.69838802,2.5 Z M6.69838802,10.5 C7.25067802,10.5 7.69838802,10.0523 7.69838802,9.5 C7.69838802,8.9477 7.25067802,8.5 6.69838802,8.5 C6.14610802,8.5 5.69838802,8.9477 5.69838802,9.5 C5.69838802,10.0523 6.14610802,10.5 6.69838802,10.5 Z",
        id: "Shape"
      }
    ) }) })
  }
);
var ExclamationMarkIcon = /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
  "svg",
  {
    width: "14px",
    height: "14px",
    viewBox: "0 0 14 14",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "exclamation-circle", fill: "#EC662C", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
      "path",
      {
        d: "M7,14 C10.866,14 14,10.866 14,7 C14,3.13401 10.866,0 7,0 C3.13401,0 0,3.13401 0,7 C0,10.866 3.13401,14 7,14 Z M7,3 C7.41421,3 7.75,3.33579 7.75,3.75 L7.75,6.75 C7.75,7.16421 7.41421,7.5 7,7.5 C6.58579,7.5 6.25,7.16421 6.25,6.75 L6.25,3.75 C6.25,3.33579 6.58579,3 7,3 Z M7,11 C7.55228,11 8,10.5523 8,10 C8,9.4477 7.55228,9 7,9 C6.44772,9 6,9.4477 6,10 C6,10.5523 6.44772,11 7,11 Z",
        id: "Shape"
      }
    ) }) })
  }
);
var ChevronDownIcon = /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
  "svg",
  {
    width: "7px",
    height: "4px",
    viewBox: "0 0 7 4",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    fill: "currentColor",
    children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Group", fill: "currentColor", fillRule: "nonzero", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
      "path",
      {
        d: "M3.71690723,3.90271086 C3.59268176,4.03242971 3.39143629,4.03242971 3.26721082,3.90271086 L0.0853966595,0.57605615 C-0.0314221035,0.444981627 -0.0279751448,0.240725043 0.0931934622,0.114040675 C0.214362069,-0.0126436935 0.409725445,-0.0162475626 0.535093061,0.105888951 L3.49205902,3.19746006 L6.44902499,0.105888951 C6.52834574,0.0168884389 6.64780588,-0.0197473458 6.7605411,0.0103538404 C6.87327633,0.0404550266 6.96130636,0.132492308 6.99009696,0.250359396 C7.01888756,0.368226483 6.98384687,0.493124608 6.89872139,0.57605615 L3.71690723,3.90271086 Z",
        id: "Path"
      }
    ) }) })
  }
);
var CheckIcon = /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
  "svg",
  {
    width: "14px",
    height: "14px",
    viewBox: "0 0 14 14",
    version: "1.1",
    xmlns: "http://www.w3.org/2000/svg",
    children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Page-1", stroke: "none", strokeWidth: "1", fill: "none", fillRule: "evenodd", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Group-2", transform: "translate(-118, 0)", fill: "#1BC030", fillRule: "nonzero", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("g", { id: "Group", transform: "translate(118, 0)", children: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
      "path",
      {
        d: "M0,7 C0,3.13384615 3.13384615,0 7,0 C10.8661538,0 14,3.13384615 14,7 C14,10.8661538 10.8661538,14 7,14 C3.13384615,14 0,10.8661538 0,7 Z M9.59179487,5.69764103 C9.70905818,5.54139023 9.73249341,5.33388318 9.65303227,5.15541491 C9.57357113,4.97694665 9.40367989,4.85551619 9.20909814,4.83811118 C9.01451638,4.82070616 8.82577109,4.91005717 8.71589744,5.07158974 L6.39261538,8.32389744 L5.22666667,7.15794872 C5.01450582,6.96025518 4.68389046,6.9660885 4.47883563,7.17114332 C4.27378081,7.37619815 4.26794748,7.70681351 4.46564103,7.91897436 L6.08102564,9.53435897 C6.19289944,9.64614839 6.3482622,9.70310251 6.50588106,9.69010587 C6.66349993,9.67710922 6.80743532,9.59547613 6.89948718,9.46687179 L9.59179487,5.69764103 L9.59179487,5.69764103 Z",
        id: "Shape"
      }
    ) }) }) })
  }
);

// src/components/dev-console/console.tsx
var import_react4 = require("@headlessui/react");
var import_shared = require("@copilotkit/shared");

// src/components/help-modal/modal.tsx
var import_react2 = require("react");

// src/components/help-modal/icons.tsx
var import_jsx_runtime4 = require("react/jsx-runtime");
var CloseIcon = () => /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor",
    width: "20",
    height: "20",
    children: /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("path", { strokeLinecap: "round", strokeLinejoin: "round", d: "M6 18L18 6M6 6l12 12" })
  }
);

// src/components/help-modal/modal.tsx
var import_jsx_runtime5 = require("react/jsx-runtime");
function CopilotKitHelpModal() {
  const [showHelpModal, setShowHelpModal] = (0, import_react2.useState)(false);
  const buttonRef = (0, import_react2.useRef)(null);
  const popoverRef = (0, import_react2.useRef)(null);
  (0, import_react2.useEffect)(() => {
    const handleClickOutside = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target) && buttonRef.current && !buttonRef.current.contains(event.target)) {
        setShowHelpModal(false);
      }
    };
    if (showHelpModal) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showHelpModal]);
  const HelpButton = () => /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
    "button",
    {
      ref: buttonRef,
      onClick: () => setShowHelpModal(!showHelpModal),
      className: "copilotKitDebugMenuTriggerButton relative",
      "aria-label": "Open Help",
      children: "Help"
    }
  );
  return /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)("div", { className: "relative", children: [
    /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(HelpButton, {}),
    showHelpModal && /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
      "div",
      {
        ref: popoverRef,
        className: "absolute mt-2 z-50",
        style: {
          top: "100%",
          right: "-120px",
          width: "380px"
        },
        children: /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)("div", { className: "copilotKitHelpModal rounded-lg shadow-xl w-full p-4 flex-col relative", children: [
          /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
            "button",
            {
              className: "copilotKitHelpModalCloseButton absolute text-gray-400 hover:text-gray-600 focus:outline-none",
              style: { top: "10px", right: "10px" },
              onClick: () => setShowHelpModal(false),
              "aria-label": "Close",
              children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(CloseIcon, {})
            }
          ),
          /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("div", { className: "w-full flex mb-6 justify-center", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("h2", { className: "text-2xl font-bold", children: "Help Options" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)("div", { className: "space-y-4 mb-4", children: [
            /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("div", { className: "copilotKitHelpItemButton", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
              "a",
              {
                href: "https://docs.copilotkit.ai/coagents/troubleshooting/common-issues",
                target: "_blank",
                rel: "noopener noreferrer",
                children: "Visit the Troubleshooting and FAQ section in the docs"
              }
            ) }),
            /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("div", { className: "copilotKitHelpItemButton", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
              "a",
              {
                href: "https://go.copilotkit.ai/dev-console-support-discord",
                target: "_blank",
                rel: "noopener noreferrer",
                children: "Go to Discord Support Channel (Community Support)"
              }
            ) }),
            /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("div", { className: "copilotKitHelpItemButton", children: /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
              "a",
              {
                href: "https://go.copilotkit.ai/dev-console-support-slack",
                target: "_blank",
                rel: "noopener noreferrer",
                children: "Apply for Priority Direct Slack Support"
              }
            ) })
          ] })
        ] })
      }
    )
  ] });
}

// src/components/dev-console/console.tsx
var import_jsx_runtime6 = require("react/jsx-runtime");
function CopilotDevConsole() {
  const currentVersion = import_shared.COPILOTKIT_VERSION;
  const context = (0, import_react_core2.useCopilotContext)();
  const [showDevConsole, setShowDevConsole] = (0, import_react3.useState)(false);
  (0, import_react3.useEffect)(() => {
    setShowDevConsole(shouldShowDevConsole(context.showDevConsole));
  }, [context.showDevConsole]);
  const dontRunTwiceInDevMode = (0, import_react3.useRef)(false);
  const [versionStatus, setVersionStatus] = (0, import_react3.useState)("unknown");
  const [latestVersion, setLatestVersion] = (0, import_react3.useState)("");
  const consoleRef = (0, import_react3.useRef)(null);
  const [debugButtonMode, setDebugButtonMode] = (0, import_react3.useState)("full");
  const checkForUpdates = (force = false) => {
    setVersionStatus("checking");
    getPublishedCopilotKitVersion(currentVersion, force).then((v) => {
      setLatestVersion(v.latest);
      let versionOk = false;
      if (v.current === v.latest) {
        versionOk = true;
      } else if (/[a-zA-Z]/.test(v.current)) {
        versionOk = true;
      }
      if (versionOk) {
        setVersionStatus("latest");
      } else if (v.severity !== "low") {
        setVersionStatus("outdated");
      } else {
        setVersionStatus("update-available");
      }
    }).catch((e) => {
      console.error(e);
      setVersionStatus("unknown");
    });
  };
  (0, import_react3.useEffect)(() => {
    if (dontRunTwiceInDevMode.current === true) {
      return;
    }
    dontRunTwiceInDevMode.current = true;
    checkForUpdates();
  }, []);
  if (!showDevConsole) {
    return null;
  }
  return /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)(
    "div",
    {
      ref: consoleRef,
      className: "copilotKitDevConsole " + (versionStatus === "update-available" ? "copilotKitDevConsoleUpgrade" : "") + (versionStatus === "outdated" ? "copilotKitDevConsoleWarnOutdated" : ""),
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(
          VersionInfo,
          {
            showDevConsole: context.showDevConsole,
            versionStatus,
            currentVersion,
            latestVersion
          }
        ),
        /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(CopilotKitHelpModal, {}),
        /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(
          DebugMenuButton,
          {
            setShowDevConsole,
            checkForUpdates,
            mode: debugButtonMode
          }
        )
      ]
    }
  );
}
function VersionInfo({
  showDevConsole,
  versionStatus,
  currentVersion,
  latestVersion
}) {
  const [copyStatus, setCopyStatus] = (0, import_react3.useState)("");
  let versionLabel = "";
  let versionIcon = "";
  let currentVersionLabel = currentVersion;
  if (versionStatus === "latest") {
    versionLabel = "latest";
    versionIcon = CheckIcon;
  } else if (versionStatus === "checking") {
    versionLabel = "checking";
    versionIcon = SmallSpinnerIcon;
  } else if (versionStatus === "update-available") {
    versionLabel = "update available";
    versionIcon = ExclamationMarkIcon;
    currentVersionLabel = `${currentVersion} \u2192 ${latestVersion}`;
  } else if (versionStatus === "outdated") {
    versionLabel = "outdated";
    versionIcon = ExclamationMarkTriangleIcon;
    currentVersionLabel = `${currentVersion} \u2192 ${latestVersion}`;
  }
  let asideLabel = "";
  if (showDevConsole === "auto") {
    asideLabel = "(localhost only)";
  } else if (showDevConsole === true) {
    asideLabel = "(always on)";
  }
  const installCommand = [
    `npm install`,
    `@copilotkit/react-core@${latestVersion}`,
    `@copilotkit/react-ui@${latestVersion}`,
    `@copilotkit/react-textarea@${latestVersion}`,
    `&& npm install @copilotkit/runtime@${latestVersion}`
  ].join(" ");
  const handleCopyClick = () => {
    navigator.clipboard.writeText(installCommand.trim()).then(() => {
      setCopyStatus("Command copied to clipboard!");
      setTimeout(() => setCopyStatus(""), 1e3);
    });
  };
  if (versionStatus === "update-available" || versionStatus === "outdated") {
    return /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)("div", { className: "copilotKitVersionInfo", children: [
      /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)("p", { children: [
        currentVersionLabel,
        " ",
        versionIcon
      ] }),
      /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("button", { onClick: handleCopyClick, children: copyStatus || installCommand })
    ] });
  }
  return null;
}
function DebugMenuButton({
  setShowDevConsole,
  checkForUpdates,
  mode
}) {
  const context = (0, import_react_core2.useCopilotContext)();
  const messagesContext = (0, import_react_core2.useCopilotMessagesContext)();
  return /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(import_jsx_runtime6.Fragment, { children: /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)(import_react4.Menu, { children: [
    /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(
      import_react4.MenuButton,
      {
        className: `copilotKitDebugMenuTriggerButton ${mode === "compact" ? "compact" : ""}`,
        children: mode == "compact" ? "Debug" : /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)(import_jsx_runtime6.Fragment, { children: [
          "Debug ",
          ChevronDownIcon
        ] })
      }
    ),
    /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)(
      import_react4.MenuItems,
      {
        transition: true,
        anchor: "bottom end",
        className: "copilotKitDebugMenu",
        style: { zIndex: 40 },
        children: [
          /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(import_react4.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => logReadables(context), children: "Log Readables" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(import_react4.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => logActions(context), children: "Log Actions" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(import_react4.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(
            "button",
            {
              className: "copilotKitDebugMenuItem",
              onClick: () => logMessages(messagesContext),
              children: "Log Messages"
            }
          ) }),
          /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(import_react4.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => checkForUpdates(true), children: "Check for Updates" }) }),
          /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("hr", {}),
          /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(import_react4.MenuItem, { children: /* @__PURE__ */ (0, import_jsx_runtime6.jsx)("button", { className: "copilotKitDebugMenuItem", onClick: () => setShowDevConsole(false), children: "Hide Dev Console" }) })
        ]
      }
    )
  ] }) });
}

// src/components/chat/Header.tsx
var import_jsx_runtime7 = require("react/jsx-runtime");
var Header = ({}) => {
  const { setOpen, icons, labels } = useChatContext();
  return /* @__PURE__ */ (0, import_jsx_runtime7.jsxs)("div", { className: "copilotKitHeader", children: [
    /* @__PURE__ */ (0, import_jsx_runtime7.jsx)("div", { children: labels.title }),
    /* @__PURE__ */ (0, import_jsx_runtime7.jsxs)("div", { className: "copilotKitHeaderControls", children: [
      /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(CopilotDevConsole, {}),
      /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
        "button",
        {
          onClick: () => setOpen(false),
          "aria-label": "Close",
          className: "copilotKitHeaderCloseButton",
          children: icons.headerCloseIcon
        }
      )
    ] })
  ] });
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Header
});
//# sourceMappingURL=Header.js.map