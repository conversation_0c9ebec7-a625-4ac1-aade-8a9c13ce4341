# Prospect Management Security & GDPR Compliance

This document outlines the security measures and GDPR compliance features implemented in the prospect management system.

## Security Architecture

### 1. Data Protection

#### Encryption at Rest
- All prospect data is stored in Supabase with AES-256 encryption
- Sensitive fields (email, phone, names) are protected by database constraints
- PII is never logged in plain text

#### Encryption in Transit
- All API communications use HTTPS/TLS 1.3
- Database connections use SSL encryption
- Email verification tokens are transmitted securely

#### Access Control
```sql
-- Row Level Security ensures proper data isolation
-- Anonymous users can only insert prospects
-- Authenticated users need admin roles for management
-- Service role has full access for backend operations

-- Example RLS policy
CREATE POLICY "Allow anonymous prospect creation" ON public.prospects
    FOR INSERT TO anon
    WITH CHECK (true);
```

### 2. Authentication & Authorization

#### API Endpoint Security
- **Signup Endpoint**: Anonymous access with CAPTCHA protection
- **Verification Endpoint**: Token-based access with expiry
- **Management Endpoint**: Admin-only access with role validation

#### Role-Based Access Control
```typescript
// Admin access check
async function checkAdminAccess(supabase: any): Promise<boolean> {
  const { data: { user } } = await supabase.auth.getUser();
  
  return user?.user_metadata?.is_super_admin === true ||
         user?.app_metadata?.roles?.includes('marketing');
}
```

### 3. Input Validation & Sanitization

#### Zod Schema Validation
```typescript
const prospectSignupSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  phone: z.string().optional(),
  gdprConsent: z.boolean().refine(val => val === true),
});
```

#### SQL Injection Prevention
- All database queries use parameterized statements
- Supabase client provides automatic SQL injection protection
- Input sanitization at API layer

### 4. Bot Protection

#### Cloudflare Turnstile Integration
```typescript
async function verifyTurnstileToken(token: string, ip: string): Promise<boolean> {
  const formData = new URLSearchParams();
  formData.append('secret', process.env.TURNSTILE_SECRET_KEY);
  formData.append('response', token);
  formData.append('remoteip', ip);
  
  const result = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
    method: 'POST',
    body: formData,
  });
  
  return (await result.json()).success === true;
}
```

## GDPR Compliance

### 1. Legal Basis for Processing

#### Consent Management
```sql
-- GDPR consent tracking in prospects table
gdpr_consent BOOLEAN NOT NULL DEFAULT false,
gdpr_consent_date TIMESTAMPTZ,
gdpr_consent_ip TEXT,
gdpr_consent_user_agent TEXT,

-- Constraint ensures consent is properly recorded
CONSTRAINT gdpr_consent_required CHECK (
    (gdpr_consent = true AND gdpr_consent_date IS NOT NULL) OR 
    (gdpr_consent = false)
)
```

#### Legitimate Interest
- Email verification for data quality
- Fraud prevention and security
- Service improvement analytics

### 2. Data Subject Rights

#### Right to Access (Article 15)
```typescript
// API endpoint to retrieve prospect data
GET /api/prospects/data?email=<EMAIL>&token=verification_token

// Returns all stored data about the prospect
{
  "personalData": {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890"
  },
  "preferences": {
    "newsletterSubscribed": true,
    "marketingConsent": true,
    "communicationPreferences": {...}
  },
  "interactions": [...],
  "metadata": {
    "signupDate": "2025-01-27T10:00:00Z",
    "lastUpdated": "2025-01-27T10:00:00Z"
  }
}
```

#### Right to Rectification (Article 16)
```typescript
// Update prospect preferences
PATCH /api/prospects/update
{
  "email": "<EMAIL>",
  "token": "verification_token",
  "updates": {
    "firstName": "Jane",
    "communicationPreferences": {
      "email": false,
      "sms": true
    }
  }
}
```

#### Right to Erasure (Article 17)
```sql
-- Soft delete with GDPR compliance
UPDATE public.prospects 
SET 
  status = 'archived',
  deleted_at = NOW(),
  -- Anonymize PII while keeping analytics data
  email = 'deleted_' || id || '@example.com',
  first_name = NULL,
  last_name = NULL,
  phone = NULL,
  ip_address = NULL,
  user_agent = NULL
WHERE email = '<EMAIL>';
```

#### Right to Data Portability (Article 20)
```typescript
// Export prospect data in machine-readable format
GET /api/prospects/export?email=<EMAIL>&token=verification_token

// Returns JSON with all prospect data
{
  "format": "JSON",
  "exportDate": "2025-01-27T10:00:00Z",
  "data": {
    "profile": {...},
    "preferences": {...},
    "interactions": [...],
    "consent": {...}
  }
}
```

#### Right to Object (Article 21)
```sql
-- Opt-out from marketing
UPDATE public.prospects 
SET 
  marketing_consent = false,
  newsletter_subscribed = false,
  status = 'unsubscribed',
  updated_at = NOW()
WHERE email = '<EMAIL>';
```

### 3. Data Retention & Deletion

#### Automatic Data Retention
```sql
-- Data retention policy
data_retention_until DATE, -- Auto-delete after this date if not converted

-- Cleanup function runs daily
CREATE OR REPLACE FUNCTION public.cleanup_expired_prospects()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Soft delete prospects that have passed their retention date
    UPDATE public.prospects
    SET 
        deleted_at = NOW(),
        status = 'archived',
        updated_at = NOW()
    WHERE data_retention_until < CURRENT_DATE
    AND status NOT IN ('converted', 'archived')
    AND deleted_at IS NULL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
```

#### Retention Periods
- **Active Prospects**: 1 year from signup
- **Converted Users**: Indefinite (part of user account)
- **Unsubscribed**: 30 days for compliance
- **Bounced/Invalid**: 90 days for analytics

### 4. Privacy by Design

#### Data Minimization
- Only collect necessary data for stated purposes
- Optional fields clearly marked
- Progressive data collection based on engagement

#### Purpose Limitation
```typescript
// Clear purpose specification in consent
const consentText = `
I consent to PI Lawyer AI collecting and processing my personal data for:
- Sending legal industry newsletters and updates
- Providing information about legal services
- Improving our services based on usage analytics

You can withdraw consent at any time by clicking unsubscribe.
`;
```

#### Storage Limitation
```sql
-- Automatic data retention enforcement
CREATE OR REPLACE FUNCTION enforce_data_retention()
RETURNS TRIGGER AS $$
BEGIN
    -- Set retention date on insert
    IF NEW.data_retention_until IS NULL THEN
        NEW.data_retention_until := CURRENT_DATE + INTERVAL '1 year';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_data_retention
    BEFORE INSERT ON public.prospects
    FOR EACH ROW EXECUTE FUNCTION enforce_data_retention();
```

## Audit & Compliance

### 1. Audit Logging

#### Interaction Tracking
```sql
-- All prospect interactions are logged
CREATE TABLE public.prospect_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prospect_id UUID NOT NULL REFERENCES public.prospects(id),
    interaction_type TEXT NOT NULL,
    subject TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

#### GDPR Action Logging
```typescript
// Log GDPR-related actions
await supabase.from('prospect_interactions').insert({
  prospect_id: prospectId,
  interaction_type: 'gdpr_action',
  subject: 'Data export requested',
  metadata: {
    action: 'data_export',
    requestedBy: 'data_subject',
    ipAddress: clientIP,
    timestamp: new Date().toISOString()
  }
});
```

### 2. Compliance Monitoring

#### Data Processing Register
```sql
-- Query for compliance reporting
SELECT 
  'Prospect Management' as processing_activity,
  'Marketing and lead generation' as purpose,
  'Email, name, phone, preferences' as data_categories,
  'Prospects, potential clients' as data_subjects,
  'Consent (Article 6(1)(a))' as legal_basis,
  '1 year or until conversion' as retention_period,
  COUNT(*) as active_records
FROM public.prospects 
WHERE status = 'active' AND deleted_at IS NULL;
```

#### Consent Analytics
```sql
-- Consent rate monitoring
SELECT 
  DATE_TRUNC('month', created_at) as month,
  COUNT(*) as total_signups,
  COUNT(*) FILTER (WHERE gdpr_consent = true) as consented,
  COUNT(*) FILTER (WHERE marketing_consent = true) as marketing_consented,
  ROUND(
    COUNT(*) FILTER (WHERE gdpr_consent = true) * 100.0 / COUNT(*), 
    2
  ) as consent_rate
FROM public.prospects 
WHERE created_at >= NOW() - INTERVAL '12 months'
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month;
```

## Security Headers & Configuration

### 1. HTTP Security Headers

```typescript
// Next.js security headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

### 2. CORS Configuration

```typescript
// API CORS settings
const corsOptions = {
  origin: [
    'https://your-website.com',
    'https://www.your-website.com'
  ],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};
```

## Incident Response

### 1. Data Breach Procedures

1. **Detection**: Automated monitoring alerts
2. **Assessment**: Determine scope and impact
3. **Containment**: Isolate affected systems
4. **Notification**: 72-hour GDPR notification if required
5. **Recovery**: Restore services and implement fixes
6. **Review**: Post-incident analysis and improvements

### 2. Emergency Contacts

- **Data Protection Officer**: <EMAIL>
- **Security Team**: <EMAIL>
- **Legal Team**: <EMAIL>

## Regular Security Reviews

### 1. Monthly Checks
- Review access logs
- Check for unusual signup patterns
- Verify data retention compliance
- Update security patches

### 2. Quarterly Audits
- Penetration testing
- GDPR compliance review
- Security policy updates
- Staff training updates

### 3. Annual Assessments
- Full security audit
- Data Protection Impact Assessment (DPIA)
- Third-party security review
- Compliance certification renewal
