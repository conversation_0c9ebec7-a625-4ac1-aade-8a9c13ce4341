{"version": 3, "sources": ["../../src/hooks/use-coagent-state-render.ts", "../../src/context/copilot-context.tsx", "../../src/components/toast/toast-provider.tsx"], "sourcesContent": ["/**\n * The useCoAgentStateRender hook allows you to render UI or text based components on a Agentic Copilot's state in the chat.\n * This is particularly useful for showing intermediate state or progress during Agentic Copilot operations.\n *\n * ## Usage\n *\n * ### Simple Usage\n *\n * ```tsx\n * import { useCoAgentStateRender } from \"@copilotkit/react-core\";\n *\n * type YourAgentState = {\n *   agent_state_property: string;\n * }\n *\n * useCoAgentStateRender<YourAgentState>({\n *   name: \"basic_agent\",\n *   nodeName: \"optionally_specify_a_specific_node\",\n *   render: ({ status, state, nodeName }) => {\n *     return (\n *       <YourComponent\n *         agentStateProperty={state.agent_state_property}\n *         status={status}\n *         nodeName={nodeName}\n *       />\n *     );\n *   },\n * });\n * ```\n *\n * This allows for you to render UI components or text based on what is happening within the agent.\n *\n * ### Example\n * A great example of this is in our Perplexity Clone where we render the progress of an agent's internet search as it is happening.\n * You can play around with it below or learn how to build it with its [demo](/coagents/videos/perplexity-clone).\n *\n * <Callout type=\"info\">\n *   This example is hosted on Vercel and may take a few seconds to load.\n * </Callout>\n *\n * <iframe src=\"https://examples-coagents-ai-researcher-ui.vercel.app/\" className=\"w-full rounded-lg border h-[700px] my-4\" />\n */\n\nimport { useRef, useContext, useEffect } from \"react\";\nimport { CopilotContext } from \"../context/copilot-context\";\nimport { randomId } from \"@copilotkit/shared\";\nimport { CoAgentStateRender } from \"../types/coagent-action\";\nimport { useToast } from \"../components/toast/toast-provider\";\n\n/**\n * This hook is used to render agent state with custom UI components or text. This is particularly\n * useful for showing intermediate state or progress during Agentic Copilot operations.\n * To get started using rendering intermediate state through this hook, checkout the documentation.\n *\n * https://docs.copilotkit.ai/coagents/shared-state/predictive-state-updates\n */\n\n// We implement useCoAgentStateRender dependency handling so that\n// the developer has the option to not provide any dependencies.\n// see useCopilotAction for more details about this approach.\nexport function useCoAgentStateRender<T = any>(\n  action: CoAgentStateRender<T>,\n  dependencies?: any[],\n): void {\n  const {\n    setCoAgentStateRender,\n    removeCoAgentStateRender,\n    coAgentStateRenders,\n    chatComponentsCache,\n    availableAgents,\n  } = useContext(CopilotContext);\n  const idRef = useRef<string>(randomId());\n  const { addToast } = useToast();\n\n  useEffect(() => {\n    if (availableAgents?.length && !availableAgents.some((a) => a.name === action.name)) {\n      const message = `(useCoAgentStateRender): Agent \"${action.name}\" not found. Make sure the agent exists and is properly configured.`;\n      addToast({ type: \"warning\", message });\n    }\n  }, [availableAgents]);\n\n  const key = `${action.name}-${action.nodeName || \"global\"}`;\n\n  if (dependencies === undefined) {\n    if (coAgentStateRenders[idRef.current]) {\n      coAgentStateRenders[idRef.current].handler = action.handler as any;\n      if (typeof action.render === \"function\") {\n        if (chatComponentsCache.current !== null) {\n          chatComponentsCache.current.coAgentStateRenders[key] = action.render;\n        }\n      }\n    }\n  }\n\n  useEffect(() => {\n    // Check for duplicates by comparing against all other actions\n    const currentId = idRef.current;\n    const hasDuplicate = Object.entries(coAgentStateRenders).some(([id, otherAction]) => {\n      // Skip comparing with self\n      if (id === currentId) return false;\n\n      // Different agent names are never duplicates\n      if (otherAction.name !== action.name) return false;\n\n      // Same agent names:\n      const hasNodeName = !!action.nodeName;\n      const hasOtherNodeName = !!otherAction.nodeName;\n\n      // If neither has nodeName, they're duplicates\n      if (!hasNodeName && !hasOtherNodeName) return true;\n\n      // If one has nodeName and other doesn't, they're not duplicates\n      if (hasNodeName !== hasOtherNodeName) return false;\n\n      // If both have nodeName, they're duplicates only if the names match\n      return action.nodeName === otherAction.nodeName;\n    });\n\n    if (hasDuplicate) {\n      const message = action.nodeName\n        ? `Found multiple state renders for agent ${action.name} and node ${action.nodeName}. State renders might get overridden`\n        : `Found multiple state renders for agent ${action.name}. State renders might get overridden`;\n\n      addToast({\n        type: \"warning\",\n        message,\n        id: `dup-action-${action.name}`,\n      });\n    }\n  }, [coAgentStateRenders]);\n\n  useEffect(() => {\n    setCoAgentStateRender(idRef.current, action as any);\n    if (chatComponentsCache.current !== null && action.render !== undefined) {\n      chatComponentsCache.current.coAgentStateRenders[key] = action.render;\n    }\n    return () => {\n      removeCoAgentStateRender(idRef.current);\n    };\n  }, [\n    setCoAgentStateRender,\n    removeCoAgentStateRender,\n    action.name,\n    // include render only if it's a string\n    typeof action.render === \"string\" ? action.render : undefined,\n    // dependencies set by the developer\n    ...(dependencies || []),\n  ]);\n}\n", "import { CopilotCloudConfig, FunctionCallHandler } from \"@copilotkit/shared\";\nimport {\n  ActionRenderProps,\n  CatchAllActionRenderProps,\n  FrontendAction,\n} from \"../types/frontend-action\";\nimport React from \"react\";\nimport { TreeNodeId } from \"../hooks/use-tree\";\nimport { DocumentPointer } from \"../types\";\nimport { CopilotChatSuggestionConfiguration } from \"../types/chat-suggestion-configuration\";\nimport { CoAgentStateRender, CoAgentStateRenderProps } from \"../types/coagent-action\";\nimport { CoagentState } from \"../types/coagent-state\";\nimport {\n  CopilotRuntimeClient,\n  ExtensionsInput,\n  ForwardedParametersInput,\n} from \"@copilotkit/runtime-client-gql\";\nimport { Agent } from \"@copilotkit/runtime-client-gql\";\nimport {\n  LangGraphInterruptAction,\n  LangGraphInterruptActionSetter,\n} from \"../types/interrupt-action\";\n\n/**\n * Interface for the configuration of the Copilot API.\n */\nexport interface CopilotApiConfig {\n  /**\n   * The public API key for Copilot Cloud.\n   */\n  publicApiKey?: string;\n\n  /**\n   * The configuration for Copilot Cloud.\n   */\n  cloud?: CopilotCloudConfig;\n\n  /**\n   * The endpoint for the chat API.\n   */\n  chatApiEndpoint: string;\n\n  /**\n   * The endpoint for the Copilot transcribe audio service.\n   */\n  transcribeAudioUrl?: string;\n\n  /**\n   * The endpoint for the Copilot text to speech service.\n   */\n  textToSpeechUrl?: string;\n\n  /**\n   * additional headers to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'Authorization': 'Bearer your_token_here'\n   * }\n   * ```\n   */\n  headers: Record<string, string>;\n\n  /**\n   * Custom properties to be sent with the request\n   * @default {}\n   * @example\n   * ```\n   * {\n   *   'user_id': 'user_id'\n   * }\n   * ```\n   */\n  properties?: Record<string, any>;\n\n  /**\n   * Indicates whether the user agent should send or receive cookies from the other domain\n   * in the case of cross-origin requests.\n   */\n  credentials?: RequestCredentials;\n\n  /**\n   * Optional configuration for connecting to Model Context Protocol (MCP) servers.\n   * This is typically derived from the CopilotKitProps and used internally.\n   * @experimental\n   */\n  mcpServers?: Array<{ endpoint: string; apiKey?: string }>;\n}\n\nexport type InChatRenderFunction<TProps = ActionRenderProps<any> | CatchAllActionRenderProps<any>> =\n  (props: TProps) => string | JSX.Element;\nexport type CoagentInChatRenderFunction = (\n  props: CoAgentStateRenderProps<any>,\n) => string | JSX.Element | undefined | null;\n\nexport interface ChatComponentsCache {\n  actions: Record<string, InChatRenderFunction | string>;\n  coAgentStateRenders: Record<string, CoagentInChatRenderFunction | string>;\n}\n\nexport interface AgentSession {\n  agentName: string;\n  threadId?: string;\n  nodeName?: string;\n}\n\nexport interface AuthState {\n  status: \"authenticated\" | \"unauthenticated\";\n  authHeaders: Record<string, string>;\n  userId?: string;\n  metadata?: Record<string, any>;\n}\n\nexport type ActionName = string;\n\nexport interface CopilotContextParams {\n  // function-calling\n  actions: Record<string, FrontendAction<any>>;\n  setAction: (id: string, action: FrontendAction<any>) => void;\n  removeAction: (id: string) => void;\n\n  // coagent actions\n  coAgentStateRenders: Record<string, CoAgentStateRender<any>>;\n  setCoAgentStateRender: (id: string, stateRender: CoAgentStateRender<any>) => void;\n  removeCoAgentStateRender: (id: string) => void;\n\n  chatComponentsCache: React.RefObject<ChatComponentsCache>;\n\n  getFunctionCallHandler: (\n    customEntryPoints?: Record<string, FrontendAction<any>>,\n  ) => FunctionCallHandler;\n\n  // text context\n  addContext: (context: string, parentId?: string, categories?: string[]) => TreeNodeId;\n  removeContext: (id: TreeNodeId) => void;\n  getContextString: (documents: DocumentPointer[], categories: string[]) => string;\n\n  // document context\n  addDocumentContext: (documentPointer: DocumentPointer, categories?: string[]) => TreeNodeId;\n  removeDocumentContext: (documentId: string) => void;\n  getDocumentsContext: (categories: string[]) => DocumentPointer[];\n\n  isLoading: boolean;\n  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;\n\n  chatSuggestionConfiguration: { [key: string]: CopilotChatSuggestionConfiguration };\n  addChatSuggestionConfiguration: (\n    id: string,\n    suggestion: CopilotChatSuggestionConfiguration,\n  ) => void;\n  removeChatSuggestionConfiguration: (id: string) => void;\n\n  chatInstructions: string;\n  setChatInstructions: React.Dispatch<React.SetStateAction<string>>;\n\n  additionalInstructions?: string[];\n  setAdditionalInstructions: React.Dispatch<React.SetStateAction<string[]>>;\n\n  // api endpoints\n  copilotApiConfig: CopilotApiConfig;\n\n  showDevConsole: boolean | \"auto\";\n\n  // agents\n  coagentStates: Record<string, CoagentState>;\n  setCoagentStates: React.Dispatch<React.SetStateAction<Record<string, CoagentState>>>;\n  coagentStatesRef: React.RefObject<Record<string, CoagentState>>;\n  setCoagentStatesWithRef: (\n    value:\n      | Record<string, CoagentState>\n      | ((prev: Record<string, CoagentState>) => Record<string, CoagentState>),\n  ) => void;\n\n  agentSession: AgentSession | null;\n  setAgentSession: React.Dispatch<React.SetStateAction<AgentSession | null>>;\n\n  agentLock: string | null;\n\n  threadId: string;\n  setThreadId: React.Dispatch<React.SetStateAction<string>>;\n\n  runId: string | null;\n  setRunId: React.Dispatch<React.SetStateAction<string | null>>;\n\n  // The chat abort controller can be used to stop generation globally,\n  // i.e. when using `stop()` from `useChat`\n  chatAbortControllerRef: React.MutableRefObject<AbortController | null>;\n\n  // runtime\n  runtimeClient: CopilotRuntimeClient;\n\n  /**\n   * The forwarded parameters to use for the task.\n   */\n  forwardedParameters?: Pick<ForwardedParametersInput, \"temperature\">;\n  availableAgents: Agent[];\n\n  /**\n   * The auth states for the CopilotKit.\n   */\n  authStates_c?: Record<ActionName, AuthState>;\n  setAuthStates_c?: React.Dispatch<React.SetStateAction<Record<ActionName, AuthState>>>;\n\n  /**\n   * The auth config for the CopilotKit.\n   */\n  authConfig_c?: {\n    SignInComponent: React.ComponentType<{\n      onSignInComplete: (authState: AuthState) => void;\n    }>;\n  };\n\n  extensions: ExtensionsInput;\n  setExtensions: React.Dispatch<React.SetStateAction<ExtensionsInput>>;\n  langGraphInterruptAction: LangGraphInterruptAction | null;\n  setLangGraphInterruptAction: LangGraphInterruptActionSetter;\n  removeLangGraphInterruptAction: () => void;\n}\n\nconst emptyCopilotContext: CopilotContextParams = {\n  actions: {},\n  setAction: () => {},\n  removeAction: () => {},\n\n  coAgentStateRenders: {},\n  setCoAgentStateRender: () => {},\n  removeCoAgentStateRender: () => {},\n\n  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },\n  getContextString: (documents: DocumentPointer[], categories: string[]) =>\n    returnAndThrowInDebug(\"\"),\n  addContext: () => \"\",\n  removeContext: () => {},\n\n  getFunctionCallHandler: () => returnAndThrowInDebug(async () => {}),\n\n  isLoading: false,\n  setIsLoading: () => returnAndThrowInDebug(false),\n\n  chatInstructions: \"\",\n  setChatInstructions: () => returnAndThrowInDebug(\"\"),\n\n  additionalInstructions: [],\n  setAdditionalInstructions: () => returnAndThrowInDebug([]),\n\n  getDocumentsContext: (categories: string[]) => returnAndThrowInDebug([]),\n  addDocumentContext: () => returnAndThrowInDebug(\"\"),\n  removeDocumentContext: () => {},\n  runtimeClient: {} as any,\n\n  copilotApiConfig: new (class implements CopilotApiConfig {\n    get chatApiEndpoint(): string {\n      throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n    }\n\n    get headers(): Record<string, string> {\n      return {};\n    }\n    get body(): Record<string, any> {\n      return {};\n    }\n  })(),\n\n  chatSuggestionConfiguration: {},\n  addChatSuggestionConfiguration: () => {},\n  removeChatSuggestionConfiguration: () => {},\n  showDevConsole: \"auto\",\n  coagentStates: {},\n  setCoagentStates: () => {},\n  coagentStatesRef: { current: {} },\n  setCoagentStatesWithRef: () => {},\n  agentSession: null,\n  setAgentSession: () => {},\n  forwardedParameters: {},\n  agentLock: null,\n  threadId: \"\",\n  setThreadId: () => {},\n  runId: null,\n  setRunId: () => {},\n  chatAbortControllerRef: { current: null },\n  availableAgents: [],\n  extensions: {},\n  setExtensions: () => {},\n  langGraphInterruptAction: null,\n  setLangGraphInterruptAction: () => null,\n  removeLangGraphInterruptAction: () => null,\n};\n\nexport const CopilotContext = React.createContext<CopilotContextParams>(emptyCopilotContext);\n\nexport function useCopilotContext(): CopilotContextParams {\n  const context = React.useContext(CopilotContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n  }\n  return context;\n}\n\nfunction returnAndThrowInDebug<T>(_value: T): T {\n  throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n}\n", "import { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport React, { createContext, useContext, useState, useCallback } from \"react\";\nimport { ErrorToast } from \"../error-boundary/error-utils\";\nimport { PartialBy } from \"@copilotkit/shared\";\n\ninterface Toast {\n  id: string;\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  duration?: number;\n}\n\ninterface ToastContextValue {\n  toasts: Toast[];\n  addToast: (toast: PartialBy<Toast, \"id\">) => void;\n  addGraphQLErrorsToast: (errors: GraphQLError[]) => void;\n  removeToast: (id: string) => void;\n  enabled: boolean;\n}\n\nconst ToastContext = createContext<ToastContextValue | undefined>(undefined);\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error(\"useToast must be used within a ToastProvider\");\n  }\n  return context;\n}\n\nexport function ToastProvider({\n  enabled,\n  children,\n}: {\n  enabled: boolean;\n  children: React.ReactNode;\n}) {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n  const addToast = useCallback(\n    (toast: PartialBy<Toast, \"id\">) => {\n      // We do not display these errors unless we are in dev mode.\n      if (!enabled) {\n        return;\n      }\n\n      const id = toast.id ?? Math.random().toString(36).substring(2, 9);\n\n      setToasts((currentToasts) => {\n        if (currentToasts.find((toast) => toast.id === id)) return currentToasts;\n        return [...currentToasts, { ...toast, id }];\n      });\n\n      if (toast.duration) {\n        setTimeout(() => {\n          removeToast(id);\n        }, toast.duration);\n      }\n    },\n    [enabled],\n  );\n\n  const addGraphQLErrorsToast = useCallback((errors: GraphQLError[]) => {\n    addToast({\n      type: \"error\",\n      message: <ErrorToast errors={errors} />,\n    });\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));\n  }, []);\n\n  const value = {\n    toasts,\n    addToast,\n    addGraphQLErrorsToast,\n    removeToast,\n    enabled,\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      <div\n        style={{\n          position: \"fixed\",\n          bottom: \"1rem\",\n          left: \"50%\",\n          transform: \"translateX(-50%)\",\n          zIndex: 50,\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"0.5rem\",\n        }}\n      >\n        {toasts.length > 1 && (\n          <div style={{ textAlign: \"right\" }}>\n            <button\n              onClick={() => setToasts([])}\n              style={{\n                padding: \"4px 8px\",\n                fontSize: \"12px\",\n                cursor: \"pointer\",\n                background: \"white\",\n                border: \"1px solid rgba(0,0,0,0.2)\",\n                borderRadius: \"4px\",\n              }}\n            >\n              Close All\n            </button>\n          </div>\n        )}\n        {toasts.map((toast) => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n      {children}\n    </ToastContext.Provider>\n  );\n}\n\nfunction Toast({\n  message,\n  type = \"info\",\n  onClose,\n}: {\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  onClose: () => void;\n}) {\n  const bgColors = {\n    info: \"#3b82f6\",\n    success: \"#22c55e\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  };\n\n  return (\n    <div\n      style={{\n        backgroundColor: bgColors[type],\n        color: \"white\",\n        padding: \"0.5rem 1.5rem\",\n        borderRadius: \"0.25rem\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        position: \"relative\",\n        minWidth: \"200px\",\n      }}\n    >\n      <div>{message}</div>\n      <button\n        onClick={onClose}\n        style={{\n          position: \"absolute\",\n          top: \"0\",\n          right: \"0\",\n          background: \"none\",\n          border: \"none\",\n          color: \"white\",\n          cursor: \"pointer\",\n          padding: \"0.5rem\",\n          fontSize: \"1rem\",\n        }}\n      >\n        ✕\n      </button>\n    </div>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AA2CA,IAAAA,gBAA8C;;;ACrC9C,mBAAkB;AAsNlB,IAAM,sBAA4C;AAAA,EAChD,SAAS,CAAC;AAAA,EACV,WAAW,MAAM;AAAA,EAAC;AAAA,EAClB,cAAc,MAAM;AAAA,EAAC;AAAA,EAErB,qBAAqB,CAAC;AAAA,EACtB,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,0BAA0B,MAAM;AAAA,EAAC;AAAA,EAEjC,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,GAAG,qBAAqB,CAAC,EAAE,EAAE;AAAA,EACzE,kBAAkB,CAAC,WAA8B,eAC/C,sBAAsB,EAAE;AAAA,EAC1B,YAAY,MAAM;AAAA,EAClB,eAAe,MAAM;AAAA,EAAC;AAAA,EAEtB,wBAAwB,MAAM,sBAAsB,MAAY;AAAA,EAAC,EAAC;AAAA,EAElE,WAAW;AAAA,EACX,cAAc,MAAM,sBAAsB,KAAK;AAAA,EAE/C,kBAAkB;AAAA,EAClB,qBAAqB,MAAM,sBAAsB,EAAE;AAAA,EAEnD,wBAAwB,CAAC;AAAA,EACzB,2BAA2B,MAAM,sBAAsB,CAAC,CAAC;AAAA,EAEzD,qBAAqB,CAAC,eAAyB,sBAAsB,CAAC,CAAC;AAAA,EACvE,oBAAoB,MAAM,sBAAsB,EAAE;AAAA,EAClD,uBAAuB,MAAM;AAAA,EAAC;AAAA,EAC9B,eAAe,CAAC;AAAA,EAEhB,kBAAkB,IAAK,MAAkC;AAAA,IACvD,IAAI,kBAA0B;AAC5B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AAAA,IAEA,IAAI,UAAkC;AACpC,aAAO,CAAC;AAAA,IACV;AAAA,IACA,IAAI,OAA4B;AAC9B,aAAO,CAAC;AAAA,IACV;AAAA,EACF,EAAG;AAAA,EAEH,6BAA6B,CAAC;AAAA,EAC9B,gCAAgC,MAAM;AAAA,EAAC;AAAA,EACvC,mCAAmC,MAAM;AAAA,EAAC;AAAA,EAC1C,gBAAgB;AAAA,EAChB,eAAe,CAAC;AAAA,EAChB,kBAAkB,MAAM;AAAA,EAAC;AAAA,EACzB,kBAAkB,EAAE,SAAS,CAAC,EAAE;AAAA,EAChC,yBAAyB,MAAM;AAAA,EAAC;AAAA,EAChC,cAAc;AAAA,EACd,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,qBAAqB,CAAC;AAAA,EACtB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa,MAAM;AAAA,EAAC;AAAA,EACpB,OAAO;AAAA,EACP,UAAU,MAAM;AAAA,EAAC;AAAA,EACjB,wBAAwB,EAAE,SAAS,KAAK;AAAA,EACxC,iBAAiB,CAAC;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,eAAe,MAAM;AAAA,EAAC;AAAA,EACtB,0BAA0B;AAAA,EAC1B,6BAA6B,MAAM;AAAA,EACnC,gCAAgC,MAAM;AACxC;AAEO,IAAM,iBAAiB,aAAAC,QAAM,cAAoC,mBAAmB;AAU3F,SAAS,sBAAyB,QAAc;AAC9C,QAAM,IAAI,MAAM,uEAAuE;AACzF;;;ADhQA,oBAAyB;;;AE5CzB,IAAAC,gBAAwE;AA+DzD;AA5Cf,IAAM,mBAAe,6BAA6C,MAAS;AAEpE,SAAS,WAAW;AACzB,QAAM,cAAU,0BAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,SAAO;AACT;;;AFgCO,SAAS,sBACd,QACA,cACM;AACN,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,QAAI,0BAAW,cAAc;AAC7B,QAAM,YAAQ,0BAAe,wBAAS,CAAC;AACvC,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,+BAAU,MAAM;AACd,SAAI,mDAAiB,WAAU,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE,SAAS,OAAO,IAAI,GAAG;AACnF,YAAM,UAAU,mCAAmC,OAAO;AAC1D,eAAS,EAAE,MAAM,WAAW,QAAQ,CAAC;AAAA,IACvC;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,QAAM,MAAM,GAAG,OAAO,QAAQ,OAAO,YAAY;AAEjD,MAAI,iBAAiB,QAAW;AAC9B,QAAI,oBAAoB,MAAM,OAAO,GAAG;AACtC,0BAAoB,MAAM,OAAO,EAAE,UAAU,OAAO;AACpD,UAAI,OAAO,OAAO,WAAW,YAAY;AACvC,YAAI,oBAAoB,YAAY,MAAM;AACxC,8BAAoB,QAAQ,oBAAoB,GAAG,IAAI,OAAO;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,+BAAU,MAAM;AAEd,UAAM,YAAY,MAAM;AACxB,UAAM,eAAe,OAAO,QAAQ,mBAAmB,EAAE,KAAK,CAAC,CAAC,IAAI,WAAW,MAAM;AAEnF,UAAI,OAAO;AAAW,eAAO;AAG7B,UAAI,YAAY,SAAS,OAAO;AAAM,eAAO;AAG7C,YAAM,cAAc,CAAC,CAAC,OAAO;AAC7B,YAAM,mBAAmB,CAAC,CAAC,YAAY;AAGvC,UAAI,CAAC,eAAe,CAAC;AAAkB,eAAO;AAG9C,UAAI,gBAAgB;AAAkB,eAAO;AAG7C,aAAO,OAAO,aAAa,YAAY;AAAA,IACzC,CAAC;AAED,QAAI,cAAc;AAChB,YAAM,UAAU,OAAO,WACnB,0CAA0C,OAAO,iBAAiB,OAAO,iDACzE,0CAA0C,OAAO;AAErD,eAAS;AAAA,QACP,MAAM;AAAA,QACN;AAAA,QACA,IAAI,cAAc,OAAO;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,mBAAmB,CAAC;AAExB,+BAAU,MAAM;AACd,0BAAsB,MAAM,SAAS,MAAa;AAClD,QAAI,oBAAoB,YAAY,QAAQ,OAAO,WAAW,QAAW;AACvE,0BAAoB,QAAQ,oBAAoB,GAAG,IAAI,OAAO;AAAA,IAChE;AACA,WAAO,MAAM;AACX,+BAAyB,MAAM,OAAO;AAAA,IACxC;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA;AAAA,IACA,OAAO;AAAA;AAAA,IAEP,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS;AAAA;AAAA,IAEpD,GAAI,gBAAgB,CAAC;AAAA,EACvB,CAAC;AACH;", "names": ["import_react", "React", "import_react"]}