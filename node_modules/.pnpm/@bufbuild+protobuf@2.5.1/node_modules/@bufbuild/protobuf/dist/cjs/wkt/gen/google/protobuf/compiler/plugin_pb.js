"use strict";
// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeGeneratorResponse_FeatureSchema = exports.CodeGeneratorResponse_Feature = exports.CodeGeneratorResponse_FileSchema = exports.CodeGeneratorResponseSchema = exports.CodeGeneratorRequestSchema = exports.VersionSchema = exports.file_google_protobuf_compiler_plugin = void 0;
const file_js_1 = require("../../../../../codegenv2/file.js");
const descriptor_pb_js_1 = require("../descriptor_pb.js");
const message_js_1 = require("../../../../../codegenv2/message.js");
const enum_js_1 = require("../../../../../codegenv2/enum.js");
/**
 * Describes the file google/protobuf/compiler/plugin.proto.
 */
exports.file_google_protobuf_compiler_plugin = (0, file_js_1.fileDesc)("CiVnb29nbGUvcHJvdG9idWYvY29tcGlsZXIvcGx1Z2luLnByb3RvEhhnb29nbGUucHJvdG9idWYuY29tcGlsZXIiRgoHVmVyc2lvbhINCgVtYWpvchgBIAEoBRINCgVtaW5vchgCIAEoBRINCgVwYXRjaBgDIAEoBRIOCgZzdWZmaXgYBCABKAkigQIKFENvZGVHZW5lcmF0b3JSZXF1ZXN0EhgKEGZpbGVfdG9fZ2VuZXJhdGUYASADKAkSEQoJcGFyYW1ldGVyGAIgASgJEjgKCnByb3RvX2ZpbGUYDyADKAsyJC5nb29nbGUucHJvdG9idWYuRmlsZURlc2NyaXB0b3JQcm90bxJFChdzb3VyY2VfZmlsZV9kZXNjcmlwdG9ycxgRIAMoCzIkLmdvb2dsZS5wcm90b2J1Zi5GaWxlRGVzY3JpcHRvclByb3RvEjsKEGNvbXBpbGVyX3ZlcnNpb24YAyABKAsyIS5nb29nbGUucHJvdG9idWYuY29tcGlsZXIuVmVyc2lvbiKSAwoVQ29kZUdlbmVyYXRvclJlc3BvbnNlEg0KBWVycm9yGAEgASgJEhoKEnN1cHBvcnRlZF9mZWF0dXJlcxgCIAEoBBIXCg9taW5pbXVtX2VkaXRpb24YAyABKAUSFwoPbWF4aW11bV9lZGl0aW9uGAQgASgFEkIKBGZpbGUYDyADKAsyNC5nb29nbGUucHJvdG9idWYuY29tcGlsZXIuQ29kZUdlbmVyYXRvclJlc3BvbnNlLkZpbGUafwoERmlsZRIMCgRuYW1lGAEgASgJEhcKD2luc2VydGlvbl9wb2ludBgCIAEoCRIPCgdjb250ZW50GA8gASgJEj8KE2dlbmVyYXRlZF9jb2RlX2luZm8YECABKAsyIi5nb29nbGUucHJvdG9idWYuR2VuZXJhdGVkQ29kZUluZm8iVwoHRmVhdHVyZRIQCgxGRUFUVVJFX05PTkUQABIbChdGRUFUVVJFX1BST1RPM19PUFRJT05BTBABEh0KGUZFQVRVUkVfU1VQUE9SVFNfRURJVElPTlMQAkJyChxjb20uZ29vZ2xlLnByb3RvYnVmLmNvbXBpbGVyQgxQbHVnaW5Qcm90b3NaKWdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL3BsdWdpbnBiqgIYR29vZ2xlLlByb3RvYnVmLkNvbXBpbGVy", [descriptor_pb_js_1.file_google_protobuf_descriptor]);
/**
 * Describes the message google.protobuf.compiler.Version.
 * Use `create(VersionSchema)` to create a new message.
 */
exports.VersionSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_compiler_plugin, 0);
/**
 * Describes the message google.protobuf.compiler.CodeGeneratorRequest.
 * Use `create(CodeGeneratorRequestSchema)` to create a new message.
 */
exports.CodeGeneratorRequestSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_compiler_plugin, 1);
/**
 * Describes the message google.protobuf.compiler.CodeGeneratorResponse.
 * Use `create(CodeGeneratorResponseSchema)` to create a new message.
 */
exports.CodeGeneratorResponseSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_compiler_plugin, 2);
/**
 * Describes the message google.protobuf.compiler.CodeGeneratorResponse.File.
 * Use `create(CodeGeneratorResponse_FileSchema)` to create a new message.
 */
exports.CodeGeneratorResponse_FileSchema = (0, message_js_1.messageDesc)(exports.file_google_protobuf_compiler_plugin, 2, 0);
/**
 * Sync with code_generator.h.
 *
 * @generated from enum google.protobuf.compiler.CodeGeneratorResponse.Feature
 */
var CodeGeneratorResponse_Feature;
(function (CodeGeneratorResponse_Feature) {
    /**
     * @generated from enum value: FEATURE_NONE = 0;
     */
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["NONE"] = 0] = "NONE";
    /**
     * @generated from enum value: FEATURE_PROTO3_OPTIONAL = 1;
     */
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["PROTO3_OPTIONAL"] = 1] = "PROTO3_OPTIONAL";
    /**
     * @generated from enum value: FEATURE_SUPPORTS_EDITIONS = 2;
     */
    CodeGeneratorResponse_Feature[CodeGeneratorResponse_Feature["SUPPORTS_EDITIONS"] = 2] = "SUPPORTS_EDITIONS";
})(CodeGeneratorResponse_Feature || (exports.CodeGeneratorResponse_Feature = CodeGeneratorResponse_Feature = {}));
/**
 * Describes the enum google.protobuf.compiler.CodeGeneratorResponse.Feature.
 */
exports.CodeGeneratorResponse_FeatureSchema = (0, enum_js_1.enumDesc)(exports.file_google_protobuf_compiler_plugin, 2, 0);
