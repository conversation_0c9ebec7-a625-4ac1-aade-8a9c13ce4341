import type { GenFile, GenMessage } from "../../../../codegenv2/types.js";
import type { Message } from "../../../../types.js";
/**
 * Describes the file google/protobuf/source_context.proto.
 */
export declare const file_google_protobuf_source_context: GenFile;
/**
 * `SourceContext` represents information about the source of a
 * protobuf element, like the file in which it is defined.
 *
 * @generated from message google.protobuf.SourceContext
 */
export type SourceContext = Message<"google.protobuf.SourceContext"> & {
    /**
     * The path-qualified name of the .proto file that contained the associated
     * protobuf element.  For example: `"google/protobuf/source_context.proto"`.
     *
     * @generated from field: string file_name = 1;
     */
    fileName: string;
};
/**
 * `SourceContext` represents information about the source of a
 * protobuf element, like the file in which it is defined.
 *
 * @generated from message google.protobuf.SourceContext
 */
export type SourceContextJson = {
    /**
     * The path-qualified name of the .proto file that contained the associated
     * protobuf element.  For example: `"google/protobuf/source_context.proto"`.
     *
     * @generated from field: string file_name = 1;
     */
    fileName?: string;
};
/**
 * Describes the message google.protobuf.SourceContext.
 * Use `create(SourceContextSchema)` to create a new message.
 */
export declare const SourceContextSchema: GenMessage<SourceContext, {
    jsonType: SourceContextJson;
}>;
