{"version": 3, "sources": ["../../src/utils/trpc.ts"], "sourcesContent": ["import {createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink} from '@trpc/client'\nimport type {CLIRouter} from '@repo/trpc-cli'\nimport superjson from 'superjson'\n\nexport const COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || 'https://cloud.copilotkit.ai'\n\nexport function createTRPCClient(cliToken: string) {\n  return createTRPClient_<CLIRouter>({\n    links: [\n      unstable_httpBatchStreamLink({\n        transformer: superjson,\n        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,\n        headers: () => {\n          const headers = new Headers()\n          headers.set('x-trpc-source', 'cli')\n          headers.set('x-cli-token', cliToken)\n          return headers\n        },\n      }),\n    ],\n  })\n}\n"], "mappings": ";AAAA,SAAQ,oBAAoB,kBAAkB,oCAAmC;AAEjF,OAAO,eAAe;AAEf,IAAM,yBAAyB,QAAQ,IAAI,0BAA0B;AAErE,SAAS,iBAAiB,UAAkB;AACjD,SAAO,iBAA4B;AAAA,IACjC,OAAO;AAAA,MACL,6BAA6B;AAAA,QAC3B,aAAa;AAAA,QACb,KAAK,GAAG,sBAAsB;AAAA,QAC9B,SAAS,MAAM;AACb,gBAAM,UAAU,IAAI,QAAQ;AAC5B,kBAAQ,IAAI,iBAAiB,KAAK;AAClC,kBAAQ,IAAI,eAAe,QAAQ;AACnC,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;", "names": []}