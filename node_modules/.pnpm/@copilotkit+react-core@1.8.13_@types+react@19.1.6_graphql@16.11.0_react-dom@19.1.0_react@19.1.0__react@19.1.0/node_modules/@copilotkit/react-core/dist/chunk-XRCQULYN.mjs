import {
  defaultCopilotContextCategories
} from "./chunk-O54FEOCV.mjs";
import {
  processActionsForRuntimeRequest
} from "./chunk-4CEQJ2X6.mjs";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-SKC7AJIV.mjs";

// src/lib/copilot-task.ts
import {
  CopilotRuntimeClient,
  Role,
  TextMessage,
  convertGqlOutputToMessages,
  convertMessagesToGqlInput,
  filterAgentStateMessages,
  CopilotRequestType
} from "@copilotkit/runtime-client-gql";
var CopilotTask = class {
  constructor(config) {
    this.instructions = config.instructions;
    this.actions = config.actions || [];
    this.includeCopilotReadable = config.includeCopilotReadable !== false;
    this.includeCopilotActions = config.includeCopilotActions !== false;
    this.forwardedParameters = config.forwardedParameters;
  }
  /**
   * Run the task.
   * @param context The CopilotContext to use for the task. Use `useCopilotContext` to obtain the current context.
   * @param data The data to use for the task.
   */
  run(context, data) {
    return __async(this, null, function* () {
      var _a, _b, _c;
      const actions = this.includeCopilotActions ? Object.assign({}, context.actions) : {};
      for (const fn of this.actions) {
        actions[fn.name] = fn;
      }
      let contextString = "";
      if (data) {
        contextString = (typeof data === "string" ? data : JSON.stringify(data)) + "\n\n";
      }
      if (this.includeCopilotReadable) {
        contextString += context.getContextString([], defaultCopilotContextCategories);
      }
      const systemMessage = new TextMessage({
        content: taskSystemMessage(contextString, this.instructions),
        role: Role.System
      });
      const messages = [systemMessage];
      const runtimeClient = new CopilotRuntimeClient({
        url: context.copilotApiConfig.chatApiEndpoint,
        publicApiKey: context.copilotApiConfig.publicApiKey,
        headers: context.copilotApiConfig.headers,
        credentials: context.copilotApiConfig.credentials
      });
      const response = yield runtimeClient.generateCopilotResponse({
        data: {
          frontend: {
            actions: processActionsForRuntimeRequest(Object.values(actions)),
            url: window.location.href
          },
          messages: convertMessagesToGqlInput(filterAgentStateMessages(messages)),
          metadata: {
            requestType: CopilotRequestType.Task
          },
          forwardedParameters: __spreadProps(__spreadValues({}, (_a = this.forwardedParameters) != null ? _a : {}), {
            toolChoice: "required"
          })
        },
        properties: context.copilotApiConfig.properties
      }).toPromise();
      const functionCallHandler = context.getFunctionCallHandler(actions);
      const functionCalls = convertGqlOutputToMessages(
        ((_c = (_b = response.data) == null ? void 0 : _b.generateCopilotResponse) == null ? void 0 : _c.messages) || []
      ).filter((m) => m.isActionExecutionMessage());
      for (const functionCall of functionCalls) {
        yield functionCallHandler({
          messages,
          name: functionCall.name,
          args: functionCall.arguments
        });
      }
    });
  }
};
function taskSystemMessage(contextString, instructions) {
  return `
Please act as an efficient, competent, conscientious, and industrious professional assistant.

Help the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.
Always be polite and respectful, and prefer brevity over verbosity.

The user has provided you with the following context:
\`\`\`
${contextString}
\`\`\`

They have also provided you with functions you can call to initiate actions on their behalf.

Please assist them as best you can.

This is not a conversation, so please do not ask questions. Just call a function without saying anything else.

The user has given you the following task to complete:

\`\`\`
${instructions}
\`\`\`
`;
}

export {
  CopilotTask
};
//# sourceMappingURL=chunk-XRCQULYN.mjs.map