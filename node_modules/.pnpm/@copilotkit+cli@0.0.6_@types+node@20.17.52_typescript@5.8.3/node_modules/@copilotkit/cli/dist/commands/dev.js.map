{"version": 3, "sources": ["../../src/commands/dev.ts", "../../src/services/auth.service.ts", "../../src/utils/trpc.ts", "../../src/services/analytics.service.ts", "../../src/utils/detect-endpoint-type.utils.ts", "../../src/services/tunnel.service.ts", "../../src/commands/base-command.ts", "../../src/utils/version.ts"], "sourcesContent": ["import {Config, Flags} from '@oclif/core'\nimport inquirer from 'inquirer'\nimport {createId} from '@paralleldrive/cuid2'\nimport ora, {Ora} from 'ora'\nimport chalk from 'chalk'\n\nimport {AuthService} from '../services/auth.service.js'\nimport {createTRPCClient} from '../utils/trpc.js'\nimport {\n  detectRemoteEndpointType,\n  getHumanReadableEndpointType,\n  RemoteEndpointType,\n} from '../utils/detect-endpoint-type.utils.js'\nimport {TunnelService} from '../services/tunnel.service.js'\nimport {AnalyticsService} from '../services/analytics.service.js'\nimport {BaseCommand} from './base-command.js'\n\nexport default class Dev extends BaseCommand {\n  static override flags = {\n    port: Flags.string({description: 'port', required: true}),\n    project: Flags.string({description: 'project'}),\n  }\n\n  static override description = 'describe the command here'\n  static override examples = ['<%= config.bin %> <%= command.id %>']\n\n  private trpcClient: ReturnType<typeof createTRPCClient> | null = null\n  private copilotCloudTunnelId: string | null = null\n\n  constructor(\n    argv: string[],\n    config: Config,\n    private authService = new AuthService(),\n    private tunnelService = new TunnelService(),\n  ) {\n    super(argv, config)\n  }\n\n  private async pingTunnelRecursively(): Promise<void> {\n    if (!this.copilotCloudTunnelId) {\n      return\n    }\n\n    try {\n      await this.trpcClient!.pingLocalTunnel.query({\n        localTunnelId: this.copilotCloudTunnelId!,\n      })\n    } catch (error: any) {\n      if (error?.data?.code === 'NOT_FOUND') {\n        this.error(error.message)\n      } else {\n        this.error('Failed to ping tunnel. The connection may have been lost.')\n      }\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, 5000))\n    await this.pingTunnelRecursively()\n  }\n\n  public async run(): Promise<void> {\n    const {flags} = await this.parse(Dev)\n\n    // Check authentication\n    const {cliToken, organization, user} = await this.authService.requireLogin(this)\n    const analytics = new AnalyticsService({userId: user.id, organizationId: organization.id, email: user.email})\n\n    this.trpcClient = createTRPCClient(cliToken)\n\n    const availableProjects = await this.trpcClient.listOrgProjects.query({orgId: organization.id})\n    let selectedProjectId: string | null = null\n\n    // Get project ID\n    if (flags.project) {\n      if (!availableProjects.some((project: any) => project.id === flags.project)) {\n        this.log(chalk.red(`Project with ID ${flags.project} not found`))\n        process.exit(1)\n      }\n\n      selectedProjectId = flags.project\n      this.log(chalk.green(`✅ Selected project ${selectedProjectId}`))\n    } else {\n      const {projectId} = await inquirer.prompt([\n        {\n          name: 'projectId',\n          type: 'list',\n          message: 'Select a project',\n          choices: availableProjects.map((project: any) => ({\n            value: project.id,\n            name: `${project.name} (ID: ${project.id})${availableProjects.length === 1 ? ' (press Enter to confirm)' : ''}`,\n          })),\n        },\n      ])\n\n      selectedProjectId = projectId\n    }\n\n    // Remote endpoint type detection\n    const {type: remoteEndpointType} = await detectRemoteEndpointType(`http://localhost:${flags.port}`)\n\n    if (remoteEndpointType === RemoteEndpointType.Invalid) {\n      this.error(\n        `Invalid remote endpoint. Please ensure you are running a compatible endpoint at port ${flags.port} and try again.`,\n      )\n    }\n\n    const humanReadableRemoteEndpointType = getHumanReadableEndpointType(remoteEndpointType)\n\n    analytics.track({\n      event: 'cli.dev.initiatied',\n      properties: {\n        port: flags.port,\n        projectId: selectedProjectId!,\n        endpointType: remoteEndpointType,\n      },\n    })\n\n    this.log(chalk.green(`✅ ${humanReadableRemoteEndpointType} endpoint detected`))\n    const spinner = ora('Creating tunnel...\\n').start()\n\n    const tunnelId = createId()\n\n    // Starting tunnel\n    const setupTunnel = this.setupTunnel({\n      tunnelId,\n      port: parseInt(flags.port),\n      subdomain: createId(),\n      onSuccess: async ({url, id}) => {\n        // Print tunnel info\n        this.log('\\nTunnel Information:\\n')\n        this.log(`${chalk.bold.cyan('• Tunnel URL:\\t\\t')} ${chalk.white(url)}`)\n        this.log(`${chalk.bold.cyan('• Endpoint Type:\\t')} ${chalk.white(humanReadableRemoteEndpointType)}`)\n        this.log(\n          `${chalk.bold.cyan('• Project:\\t\\t')} ${chalk.white(`${process.env.COPILOT_CLOUD_BASE_URL}/projects/${selectedProjectId!}`)}`,\n        )\n        this.log(chalk.yellow('\\nPress Ctrl+C to stop the tunnel'))\n        this.log('\\n')\n\n        spinner.text = 'Linking local tunnel to Copilot Cloud...'\n\n        // Report to Cloud\n        const {localTunnelId} = await this.trpcClient!.reportRemoteEndpointLocalTunnel.mutate({\n          tunnelId: id,\n          projectId: selectedProjectId!,\n          endpointType: remoteEndpointType === RemoteEndpointType.CopilotKit ? 'CopilotKit' : 'LangGraphCloud',\n          tunnelUrl: url,\n          port: parseInt(flags.port),\n        })\n\n        this.copilotCloudTunnelId = localTunnelId\n\n        analytics.track({\n          event: 'cli.dev.tunnel.created',\n          properties: {\n            tunnelId: localTunnelId,\n            port: flags.port,\n            projectId: selectedProjectId!,\n            endpointType: remoteEndpointType,\n          },\n        })\n\n        spinner.color = 'green'\n        spinner.text = '🚀 Local tunnel is live and linked to Copilot Cloud!\\n'\n        spinner.succeed()\n\n        await this.pingTunnelRecursively()\n      },\n      onTunnelClose: async ({id}) => {\n        if (this.copilotCloudTunnelId) {\n          analytics.track({\n            event: 'cli.dev.tunnel.closed',\n            properties: {\n              tunnelId: id,\n            },\n          })\n\n          await this.trpcClient!.deleteLocalTunnel.mutate({\n            localTunnelId: this.copilotCloudTunnelId!,\n          })\n          this.copilotCloudTunnelId = null\n        }\n      },\n      spinner,\n    })\n\n    await Promise.all([setupTunnel])\n  }\n\n  private async setupTunnel({\n    port,\n    subdomain,\n    onSuccess,\n    onTunnelClose,\n    spinner,\n    tunnelId,\n  }: {\n    port: number\n    subdomain?: string\n    onSuccess: (params: {url: string; id: string}) => Promise<void>\n    onTunnelClose: (params: {id: string}) => Promise<void>\n    spinner: Ora\n    tunnelId: string\n  }) {\n    // Create the tunnel using the service\n    const tunnel = await this.tunnelService.create({\n      port,\n      subdomain: tunnelId,\n    })\n    // Handle tunnel events\n    tunnel.on('request', (info) => {\n      this.log(`${chalk.green('➜')} ${chalk.white(new Date().toISOString())} - ${info.method} ${info.path}`)\n    })\n\n    tunnel.on('error', (err) => {\n      this.error(chalk.red(`Tunnel error: ${err.message}`))\n    })\n\n    tunnel.on('close', async () => {\n      this.log(chalk.yellow('\\nTunnel closed'))\n      // eslint-disable-next-line n/no-process-exit, unicorn/no-process-exit\n      await onTunnelClose({id: tunnelId})\n      process.exit(0)\n    })\n\n    // Keep the process alive until Ctrl+C\n    await Promise.all([\n      new Promise<void>(() => {\n        process.on('SIGINT', async () => {\n          this.log('\\nShutting down tunnel...')\n          await onTunnelClose({id: tunnelId})\n          tunnel.close()\n          process.exit(0)\n        })\n\n        process.on('SIGTERM', async () => {\n          this.log('\\nShutting down tunnel...')\n          await onTunnelClose({id: tunnelId})\n          tunnel.close()\n          process.exit(0)\n        })\n      }),\n      onSuccess({url: tunnel.url, id: tunnelId}),\n    ])\n  }\n}\n", "// @ts-ignore\nimport Conf from 'conf'\nimport cors from 'cors'\nimport express from 'express'\nimport crypto from 'node:crypto'\nimport open from 'open'\nimport getPort from 'get-port'\nimport ora from 'ora'\nimport chalk from 'chalk'\nimport inquirer from 'inquirer'\nimport {Command} from '@oclif/core'\nimport {createTRPCClient} from '../utils/trpc.js'\nimport { AnalyticsService } from '../services/analytics.service.js'\n\ninterface LoginResponse {\n  cliToken: string\n  user: {\n    email: string\n    id: string\n  }\n  organization: {\n    id: string\n  }\n}\n\nexport class AuthService {\n  private readonly config = new Conf({projectName: 'CopilotKitCLI'})\n  private readonly COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || 'https://cloud.copilotkit.ai'\n\n  getToken(): string | undefined {\n    return this.config.get('cliToken') as string | undefined\n  }\n\n  getCLIToken(): string | undefined {\n    const cliToken = this.config.get('cliToken') as string | undefined\n    return cliToken\n  }\n\n  async logout(): Promise<void> {\n    const cliToken = this.getCLIToken();\n    \n    if (!cliToken) {\n      throw new Error('You are not logged in');\n    }\n\n    const trpcClient = createTRPCClient(cliToken)\n    const me = await trpcClient.me.query()\n\n    const analytics = new AnalyticsService({ userId: me.user!.id, organizationId: me.organization!.id, email: me.user!.email });\n    \n    this.config.delete('cliToken')\n    \n    analytics.track({\n      event: \"cli.logout\",\n      properties: {\n        organizationId: me.organization!.id,\n        userId: me.user!.id,\n        email: me.user!.email,\n      }\n    });\n  }\n\n  async requireLogin(cmd: Command): Promise<LoginResponse> {\n    let cliToken = this.getCLIToken()\n\n    // Check authentication\n    if (!cliToken) {\n      try {\n        const {shouldLogin} = await inquirer.prompt([\n          {\n            name: 'shouldLogin',\n            type: 'confirm',\n            message: 'You are not yet authenticated. Authenticate with Copilot Cloud? (press Enter to confirm)',\n            default: true,\n          },\n        ])\n\n        if (shouldLogin) {\n          const loginResult = await this.login()\n          cliToken = loginResult.cliToken\n          cmd.log(`🪁 Logged in as ${chalk.hex('#7553fc')(loginResult.user.email)}\\n`)\n          return loginResult\n        } else {\n          cmd.error('Authentication required to proceed.')\n        }\n      } catch (error) {\n        if (error instanceof Error && error.name === 'ExitPromptError') {\n          cmd.error(chalk.yellow('\\nAuthentication cancelled'))\n        }\n\n        throw error\n      }\n    }\n\n    let me;\n\n    const trpcClient = createTRPCClient(cliToken)\n    try {\n      me = await trpcClient.me.query()\n    } catch (error) {\n      cmd.log(chalk.red(\"Could not authenticate with Copilot Cloud. Please try again.\"))\n      process.exit(1)\n    }\n\n    if (!me.organization || !me.user) {\n      cmd.error('Authentication required to proceed.')\n    }\n\n    return {cliToken, user: me.user, organization: me.organization}\n  }\n\n  async login(): Promise<LoginResponse> {\n    let analytics: AnalyticsService;\n    analytics = new AnalyticsService();\n\n    const app = express()\n    app.use(cors())\n    app.use(express.urlencoded({extended: true}))\n    app.use(express.json())\n\n    const port = await getPort()\n    const state = crypto.randomBytes(16).toString('hex')\n\n    return new Promise((resolve) => {\n      const server = app.listen(port, () => {})\n\n      analytics.track({\n        event: \"cli.login.initiated\",\n        properties: {}\n      });\n\n      const spinner = ora('Waiting for browser authentication to complete...\\n').start()\n\n      app.post('/callback', async (req, res) => {\n        const {cliToken, user, organization} = req.body\n\n        analytics = new AnalyticsService({ userId: user.id, organizationId: organization.id, email: user.email });\n        analytics.track({\n          event: \"cli.login.success\",\n          properties: {\n            organizationId: organization.id,\n            userId: user.id,\n            email: user.email,\n          }\n        });\n\n        if (state !== req.query.state) {\n          res.status(401).json({message: 'Invalid state'})\n          spinner.fail('Invalid state')\n          return\n        }\n\n        this.config.set('cliToken', cliToken)\n        res.status(200).json({message: 'Callback called'})\n        spinner.succeed(`🪁 Successfully logged in as ${chalk.hex('#7553fc')(user.email)}\\n`)\n        server.close()\n        resolve({\n          cliToken,\n          organization,\n          user,\n        })\n      })\n\n      open(`${this.COPILOT_CLOUD_BASE_URL}/cli-auth?callbackUrl=http://localhost:${port}/callback&state=${state}`)\n    })\n  }\n}\n", "import {createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink} from '@trpc/client'\nimport type {CLIRouter} from '@repo/trpc-cli'\nimport superjson from 'superjson'\n\nexport const COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || 'https://cloud.copilotkit.ai'\n\nexport function createTRPCClient(cliToken: string) {\n  return createTRPClient_<CLIRouter>({\n    links: [\n      unstable_httpBatchStreamLink({\n        transformer: superjson,\n        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,\n        headers: () => {\n          const headers = new Headers()\n          headers.set('x-trpc-source', 'cli')\n          headers.set('x-cli-token', cliToken)\n          return headers\n        },\n      }),\n    ],\n  })\n}\n", "import {Analytics} from '@segment/analytics-node'\nimport {AnalyticsEvents} from './events.js'\nimport Conf from 'conf'\n\nexport class AnalyticsService {\n  private segment: Analytics | undefined\n  private globalProperties: Record<string, any> = {}\n  private userId: string | undefined;\n  private email: string | undefined;\n  private organizationId: string | undefined;\n  private config = new Conf({projectName: 'CopilotKitCLI'})\n\n  constructor(private readonly authData?: {\n    userId: string,\n    email: string,\n    organizationId: string,\n  }) {\n    if (process.env.SEGMENT_DISABLED === 'true') {\n      return;\n    }\n\n    const segmentWriteKey = process.env.SEGMENT_WRITE_KEY || \"9Pv6QyExYef2P4hPz4gks6QAvNMi2AOf\"\n\n    this.globalProperties = {\n      service: 'cli',\n    }\n\n\n    if (this.authData?.userId) {\n      this.userId = this.authData.userId\n    }\n\n    if (this.authData?.email) {\n      this.email = this.authData.email\n      this.globalProperties.email = this.authData.email\n    }\n\n    if (this.authData?.organizationId) {\n      this.organizationId = this.authData.organizationId\n    }\n\n    this.segment = new Analytics({\n      writeKey: segmentWriteKey,\n      disable: process.env.SEGMENT_DISABLE === 'true',\n    })\n\n    const config = new Conf({projectName: 'CopilotKitCLI'})\n    if (!config.get('anonymousId')) {\n      config.set('anonymousId', crypto.randomUUID())\n    }\n  }\n\n  private getAnonymousId(): string {\n    const anonymousId = this.config.get('anonymousId')\n    if (!anonymousId) {\n      const anonymousId = crypto.randomUUID()\n      this.config.set('anonymousId', anonymousId)\n      return anonymousId\n    }\n\n    return anonymousId as string;\n  }\n\n  public track<K extends keyof AnalyticsEvents>(\n    event: Omit<Parameters<Analytics['track']>[0], 'userId'> & {\n      event: K\n      properties: AnalyticsEvents[K]\n    },\n  ): void {\n    if (!this.segment) {\n      return;\n    }\n\n    const payload = {\n      userId: this.userId ? this.userId : undefined,\n      email: this.email ? this.email : undefined,\n      anonymousId: this.getAnonymousId(),\n      event: event.event,\n      properties: {\n        ...this.globalProperties,\n        ...event.properties,\n        $groups: this.organizationId ? {\n          segment_group: this.organizationId,\n        } : undefined,\n        eventProperties: {\n          ...event.properties,\n          ...this.globalProperties,\n        },\n      },\n    }\n\n    this.segment.track(payload)\n  }\n}\n", "import { TRPCError } from \"@trpc/server\";\nimport { z } from \"zod\";\n\nexport enum RemoteEndpointType {\n  LangGraphPlatform = 'LangGraphPlatform',\n  CopilotKit = 'CopilotKit',\n  Invalid = 'Invalid',\n}\n\nconst removeTrailingSlash = (url: string) => url.replace(/\\/$/, '');\n\nexport const getHumanReadableEndpointType = (type: RemoteEndpointType) => {\n  switch (type) {\n    case RemoteEndpointType.LangGraphPlatform:\n      return 'LangGraph Platform';\n    case RemoteEndpointType.CopilotKit:\n      return 'CopilotKit';\n    default:\n      return 'Invalid';\n  }\n}\n\nexport async function detectRemoteEndpointType(url: string): Promise<{\n  url: string;\n  type: RemoteEndpointType;\n  humanReadableType: string;\n}> {\n\n  const promises = [\n    isLangGraphPlatformEndpoint(url),\n    isCopilotKitEndpoint(url),\n  ];\n\n  if (!url.endsWith('/copilotkit')) {\n    promises.push(isCopilotKitEndpoint(`${removeTrailingSlash(url)}/copilotkit`));\n  }\n\n  const results = await Promise.all(promises);\n\n  // LangGraph Platform\n  if (results[0]) {\n    return {\n      url,\n      type: RemoteEndpointType.LangGraphPlatform,\n      humanReadableType: 'LangGraph Platform',\n    }\n  }\n\n  // CopilotKit\n  if (results[1]) {\n    return {\n      url,\n      type: RemoteEndpointType.CopilotKit,\n      humanReadableType: 'CopilotKit',\n    }\n  }\n\n  // CopilotKit with appended /copilotkit\n  if (results[2]) {\n    return {\n      url: `${removeTrailingSlash(url)}/copilotkit`,\n      type: RemoteEndpointType.CopilotKit,\n      humanReadableType: 'CopilotKit',\n    }\n  }\n\n  return {\n    url,\n    type: RemoteEndpointType.Invalid,\n    humanReadableType: 'Invalid',\n  };\n}\n\nasync function isLangGraphPlatformEndpoint(url: string, retries: number = 0): Promise<boolean> {\n  let response\n\n  try {\n    response = await fetch(`${url}/assistants/search`, {\n      method: 'POST',\n\n      body: JSON.stringify({\n        metadata: {},\n        limit: 99,\n        offset: 0,\n      }),\n    });\n  } catch (error) {\n    return false;\n  }\n\n  if (!response.ok) {\n    if (response.status === 502) {\n      if (retries < 3) {\n        console.log(\"RETRYING LGC\", retries + 1);\n        return isLangGraphPlatformEndpoint(url, retries + 1);\n      }\n    }\n\n    if (response.status === 403) {\n      return true;\n    }\n\n    return false;\n  }\n\n  const data = await response.json();\n\n  if (data[0].assistant_id) {\n    return true;\n  }\n\n  return false;\n}\n\nasync function isCopilotKitEndpoint(url: string, retries: number = 0): Promise<boolean> {\n  let response\n\n  try {\n    response = await fetch(`${url}/info`, {\n      method: \"POST\",\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    return false;\n  }\n\n  if (!response.ok) {\n    if (response.status === 502) {\n      if (retries < 3) {\n        console.log(\"RETRYING CK\", retries + 1);\n        return isCopilotKitEndpoint(url, retries + 1);\n      }\n    }\n\n    return false;\n  }\n\n  const data = await response.json();\n\n  if (data.agents && data.actions) {\n    return true;\n  }\n\n  return false;\n}", "import type {Tunnel} from 'localtunnel'\n\nimport axios from 'axios'\nimport localtunnel from 'localtunnel'\n\nexport interface TunnelOptions {\n  port: number\n  subdomain?: string\n}\n\nexport class TunnelService {\n  private readonly META_DATA_URL = 'https://metadata-cdn.copilotkit.ai/cloud.config.json'\n\n  async create(options: TunnelOptions): Promise<Tunnel> {\n    return localtunnel(options)\n  }\n\n  async getMetaData() {\n    const response = await axios.get<{\n      tunnelHost: string\n    }>(this.META_DATA_URL)\n    return response.data\n  }\n}\n", "import { Command } from \"@oclif/core\";\nimport Sentry, { consoleIntegration } from \"@sentry/node\";\nimport { LIB_VERSION } from \"../utils/version.js\";\nimport { COPILOT_CLOUD_BASE_URL } from \"../utils/trpc.js\";\nimport chalk from \"chalk\";\n\nexport class BaseCommand extends Command {\n  async init() {\n    await this.checkCLIVersion();\n\n    if (process.env.SENTRY_DISABLED === 'true') {\n      return;\n    }\n\n    Sentry.init({\n      dsn: process.env.SENTRY_DSN || \"https://<EMAIL>/4508581448581120\",\n      integrations: [\n        consoleIntegration(),\n      ],\n      // Tracing\n      tracesSampleRate: 1.0, //  Capture 100% of the transactions\n    });\n  }\n\n  async catch(err: any) {\n    if (process.env.SENTRY_DISABLED === 'true') {\n      super.catch(err)\n      return;\n    }\n\n    Sentry.captureException(err)\n    super.catch(err)\n  }\n\n  async finally() {\n    if (process.env.SENTRY_DISABLED === 'true') {\n      return;\n    }\n\n    Sentry.close()\n  }\n  \n  async run() {}\n\n  async checkCLIVersion() {\n    const response = await fetch(`${COPILOT_CLOUD_BASE_URL}/api/healthz`)\n    const data = await response.json()\n    const cloudVersion = data.cliVersion\n\n    if (cloudVersion === LIB_VERSION) {\n      return;\n    }\n\n    this.log(chalk.yellow('================ New version available! =================\\n'))\n    this.log(`A new CopilotKit CLI version is available (${LIB_VERSION}).\\n`)\n    this.log('Please update your CLI to the latest version:\\n\\n')\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('npm:')))}\\t npm install -g @copilotkit/cli@latest\\n`)\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('pnpm:')))}\\t pnpm install -g @copilotkit/cli@latest\\n`)\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('yarn:')))}\\t yarn global add @copilotkit/cli@latest\\n`)\n\n    process.exit(0)\n  }\n}\n", "// This is auto generated!\nexport const LIB_VERSION = \"0.0.6\";\n"], "mappings": ";AAAA,SAAgB,aAAY;AAC5B,OAAOA,eAAc;AACrB,SAAQ,gBAAe;AACvB,OAAOC,UAAgB;AACvB,OAAOC,YAAW;;;ACHlB,OAAOC,WAAU;AACjB,OAAO,UAAU;AACjB,OAAO,aAAa;AACpB,OAAOC,aAAY;AACnB,OAAO,UAAU;AACjB,OAAO,aAAa;AACpB,OAAO,SAAS;AAChB,OAAO,WAAW;AAClB,OAAO,cAAc;;;ACTrB,SAAQ,oBAAoB,kBAAkB,oCAAmC;AAEjF,OAAO,eAAe;AAEf,IAAM,yBAAyB,QAAQ,IAAI,0BAA0B;AAErE,SAAS,iBAAiB,UAAkB;AACjD,SAAO,iBAA4B;AAAA,IACjC,OAAO;AAAA,MACL,6BAA6B;AAAA,QAC3B,aAAa;AAAA,QACb,KAAK,GAAG,sBAAsB;AAAA,QAC9B,SAAS,MAAM;AACb,gBAAM,UAAU,IAAI,QAAQ;AAC5B,kBAAQ,IAAI,iBAAiB,KAAK;AAClC,kBAAQ,IAAI,eAAe,QAAQ;AACnC,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;;;ACrBA,SAAQ,iBAAgB;AAExB,OAAO,UAAU;AAEV,IAAM,mBAAN,MAAuB;AAAA,EAQ5B,YAA6B,UAI1B;AAJ0B;AAK3B,QAAI,QAAQ,IAAI,qBAAqB,QAAQ;AAC3C;AAAA,IACF;AAEA,UAAM,kBAAkB,QAAQ,IAAI,qBAAqB;AAEzD,SAAK,mBAAmB;AAAA,MACtB,SAAS;AAAA,IACX;AAGA,QAAI,KAAK,UAAU,QAAQ;AACzB,WAAK,SAAS,KAAK,SAAS;AAAA,IAC9B;AAEA,QAAI,KAAK,UAAU,OAAO;AACxB,WAAK,QAAQ,KAAK,SAAS;AAC3B,WAAK,iBAAiB,QAAQ,KAAK,SAAS;AAAA,IAC9C;AAEA,QAAI,KAAK,UAAU,gBAAgB;AACjC,WAAK,iBAAiB,KAAK,SAAS;AAAA,IACtC;AAEA,SAAK,UAAU,IAAI,UAAU;AAAA,MAC3B,UAAU;AAAA,MACV,SAAS,QAAQ,IAAI,oBAAoB;AAAA,IAC3C,CAAC;AAED,UAAM,SAAS,IAAI,KAAK,EAAC,aAAa,gBAAe,CAAC;AACtD,QAAI,CAAC,OAAO,IAAI,aAAa,GAAG;AAC9B,aAAO,IAAI,eAAe,OAAO,WAAW,CAAC;AAAA,IAC/C;AAAA,EACF;AAAA,EA7CQ;AAAA,EACA,mBAAwC,CAAC;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,IAAI,KAAK,EAAC,aAAa,gBAAe,CAAC;AAAA,EA0ChD,iBAAyB;AAC/B,UAAM,cAAc,KAAK,OAAO,IAAI,aAAa;AACjD,QAAI,CAAC,aAAa;AAChB,YAAMC,eAAc,OAAO,WAAW;AACtC,WAAK,OAAO,IAAI,eAAeA,YAAW;AAC1C,aAAOA;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEO,MACL,OAIM;AACN,QAAI,CAAC,KAAK,SAAS;AACjB;AAAA,IACF;AAEA,UAAM,UAAU;AAAA,MACd,QAAQ,KAAK,SAAS,KAAK,SAAS;AAAA,MACpC,OAAO,KAAK,QAAQ,KAAK,QAAQ;AAAA,MACjC,aAAa,KAAK,eAAe;AAAA,MACjC,OAAO,MAAM;AAAA,MACb,YAAY;AAAA,QACV,GAAG,KAAK;AAAA,QACR,GAAG,MAAM;AAAA,QACT,SAAS,KAAK,iBAAiB;AAAA,UAC7B,eAAe,KAAK;AAAA,QACtB,IAAI;AAAA,QACJ,iBAAiB;AAAA,UACf,GAAG,MAAM;AAAA,UACT,GAAG,KAAK;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAEA,SAAK,QAAQ,MAAM,OAAO;AAAA,EAC5B;AACF;;;AFpEO,IAAM,cAAN,MAAkB;AAAA,EACN,SAAS,IAAIC,MAAK,EAAC,aAAa,gBAAe,CAAC;AAAA,EAChD,yBAAyB,QAAQ,IAAI,0BAA0B;AAAA,EAEhF,WAA+B;AAC7B,WAAO,KAAK,OAAO,IAAI,UAAU;AAAA,EACnC;AAAA,EAEA,cAAkC;AAChC,UAAM,WAAW,KAAK,OAAO,IAAI,UAAU;AAC3C,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,SAAwB;AAC5B,UAAM,WAAW,KAAK,YAAY;AAElC,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACzC;AAEA,UAAM,aAAa,iBAAiB,QAAQ;AAC5C,UAAM,KAAK,MAAM,WAAW,GAAG,MAAM;AAErC,UAAM,YAAY,IAAI,iBAAiB,EAAE,QAAQ,GAAG,KAAM,IAAI,gBAAgB,GAAG,aAAc,IAAI,OAAO,GAAG,KAAM,MAAM,CAAC;AAE1H,SAAK,OAAO,OAAO,UAAU;AAE7B,cAAU,MAAM;AAAA,MACd,OAAO;AAAA,MACP,YAAY;AAAA,QACV,gBAAgB,GAAG,aAAc;AAAA,QACjC,QAAQ,GAAG,KAAM;AAAA,QACjB,OAAO,GAAG,KAAM;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,aAAa,KAAsC;AACvD,QAAI,WAAW,KAAK,YAAY;AAGhC,QAAI,CAAC,UAAU;AACb,UAAI;AACF,cAAM,EAAC,YAAW,IAAI,MAAM,SAAS,OAAO;AAAA,UAC1C;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAED,YAAI,aAAa;AACf,gBAAM,cAAc,MAAM,KAAK,MAAM;AACrC,qBAAW,YAAY;AACvB,cAAI,IAAI,0BAAmB,MAAM,IAAI,SAAS,EAAE,YAAY,KAAK,KAAK,CAAC;AAAA,CAAI;AAC3E,iBAAO;AAAA,QACT,OAAO;AACL,cAAI,MAAM,qCAAqC;AAAA,QACjD;AAAA,MACF,SAAS,OAAO;AACd,YAAI,iBAAiB,SAAS,MAAM,SAAS,mBAAmB;AAC9D,cAAI,MAAM,MAAM,OAAO,4BAA4B,CAAC;AAAA,QACtD;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI;AAEJ,UAAM,aAAa,iBAAiB,QAAQ;AAC5C,QAAI;AACF,WAAK,MAAM,WAAW,GAAG,MAAM;AAAA,IACjC,SAAS,OAAO;AACd,UAAI,IAAI,MAAM,IAAI,8DAA8D,CAAC;AACjF,cAAQ,KAAK,CAAC;AAAA,IAChB;AAEA,QAAI,CAAC,GAAG,gBAAgB,CAAC,GAAG,MAAM;AAChC,UAAI,MAAM,qCAAqC;AAAA,IACjD;AAEA,WAAO,EAAC,UAAU,MAAM,GAAG,MAAM,cAAc,GAAG,aAAY;AAAA,EAChE;AAAA,EAEA,MAAM,QAAgC;AACpC,QAAI;AACJ,gBAAY,IAAI,iBAAiB;AAEjC,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI,KAAK,CAAC;AACd,QAAI,IAAI,QAAQ,WAAW,EAAC,UAAU,KAAI,CAAC,CAAC;AAC5C,QAAI,IAAI,QAAQ,KAAK,CAAC;AAEtB,UAAM,OAAO,MAAM,QAAQ;AAC3B,UAAM,QAAQC,QAAO,YAAY,EAAE,EAAE,SAAS,KAAK;AAEnD,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAM,SAAS,IAAI,OAAO,MAAM,MAAM;AAAA,MAAC,CAAC;AAExC,gBAAU,MAAM;AAAA,QACd,OAAO;AAAA,QACP,YAAY,CAAC;AAAA,MACf,CAAC;AAED,YAAM,UAAU,IAAI,qDAAqD,EAAE,MAAM;AAEjF,UAAI,KAAK,aAAa,OAAO,KAAK,QAAQ;AACxC,cAAM,EAAC,UAAU,MAAM,aAAY,IAAI,IAAI;AAE3C,oBAAY,IAAI,iBAAiB,EAAE,QAAQ,KAAK,IAAI,gBAAgB,aAAa,IAAI,OAAO,KAAK,MAAM,CAAC;AACxG,kBAAU,MAAM;AAAA,UACd,OAAO;AAAA,UACP,YAAY;AAAA,YACV,gBAAgB,aAAa;AAAA,YAC7B,QAAQ,KAAK;AAAA,YACb,OAAO,KAAK;AAAA,UACd;AAAA,QACF,CAAC;AAED,YAAI,UAAU,IAAI,MAAM,OAAO;AAC7B,cAAI,OAAO,GAAG,EAAE,KAAK,EAAC,SAAS,gBAAe,CAAC;AAC/C,kBAAQ,KAAK,eAAe;AAC5B;AAAA,QACF;AAEA,aAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,YAAI,OAAO,GAAG,EAAE,KAAK,EAAC,SAAS,kBAAiB,CAAC;AACjD,gBAAQ,QAAQ,uCAAgC,MAAM,IAAI,SAAS,EAAE,KAAK,KAAK,CAAC;AAAA,CAAI;AACpF,eAAO,MAAM;AACb,gBAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,WAAK,GAAG,KAAK,sBAAsB,0CAA0C,IAAI,mBAAmB,KAAK,EAAE;AAAA,IAC7G,CAAC;AAAA,EACH;AACF;;;AG7JA,IAAM,sBAAsB,CAAC,QAAgB,IAAI,QAAQ,OAAO,EAAE;AAE3D,IAAM,+BAA+B,CAAC,SAA6B;AACxE,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEA,eAAsB,yBAAyB,KAI5C;AAED,QAAM,WAAW;AAAA,IACf,4BAA4B,GAAG;AAAA,IAC/B,qBAAqB,GAAG;AAAA,EAC1B;AAEA,MAAI,CAAC,IAAI,SAAS,aAAa,GAAG;AAChC,aAAS,KAAK,qBAAqB,GAAG,oBAAoB,GAAG,CAAC,aAAa,CAAC;AAAA,EAC9E;AAEA,QAAM,UAAU,MAAM,QAAQ,IAAI,QAAQ;AAG1C,MAAI,QAAQ,CAAC,GAAG;AACd,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,EACF;AAGA,MAAI,QAAQ,CAAC,GAAG;AACd,WAAO;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,EACF;AAGA,MAAI,QAAQ,CAAC,GAAG;AACd,WAAO;AAAA,MACL,KAAK,GAAG,oBAAoB,GAAG,CAAC;AAAA,MAChC,MAAM;AAAA,MACN,mBAAmB;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,mBAAmB;AAAA,EACrB;AACF;AAEA,eAAe,4BAA4B,KAAa,UAAkB,GAAqB;AAC7F,MAAI;AAEJ,MAAI;AACF,eAAW,MAAM,MAAM,GAAG,GAAG,sBAAsB;AAAA,MACjD,QAAQ;AAAA,MAER,MAAM,KAAK,UAAU;AAAA,QACnB,UAAU,CAAC;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,QAAI,SAAS,WAAW,KAAK;AAC3B,UAAI,UAAU,GAAG;AACf,gBAAQ,IAAI,gBAAgB,UAAU,CAAC;AACvC,eAAO,4BAA4B,KAAK,UAAU,CAAC;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,SAAS,WAAW,KAAK;AAC3B,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,MAAI,KAAK,CAAC,EAAE,cAAc;AACxB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,eAAe,qBAAqB,KAAa,UAAkB,GAAqB;AACtF,MAAI;AAEJ,MAAI;AACF,eAAW,MAAM,MAAM,GAAG,GAAG,SAAS;AAAA,MACpC,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU,CAAC,CAAC;AAAA,IACzB,CAAC;AAAA,EACH,SAAS,OAAO;AACd,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,QAAI,SAAS,WAAW,KAAK;AAC3B,UAAI,UAAU,GAAG;AACf,gBAAQ,IAAI,eAAe,UAAU,CAAC;AACtC,eAAO,qBAAqB,KAAK,UAAU,CAAC;AAAA,MAC9C;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,MAAI,KAAK,UAAU,KAAK,SAAS;AAC/B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC9IA,OAAO,WAAW;AAClB,OAAO,iBAAiB;AAOjB,IAAM,gBAAN,MAAoB;AAAA,EACR,gBAAgB;AAAA,EAEjC,MAAM,OAAO,SAAyC;AACpD,WAAO,YAAY,OAAO;AAAA,EAC5B;AAAA,EAEA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,MAAM,IAE1B,KAAK,aAAa;AACrB,WAAO,SAAS;AAAA,EAClB;AACF;;;ACvBA,SAAS,eAAe;AACxB,OAAO,UAAU,0BAA0B;;;ACApC,IAAM,cAAc;;;ADG3B,OAAOC,YAAW;AAEX,IAAM,cAAN,cAA0B,QAAQ;AAAA,EACvC,MAAM,OAAO;AACX,UAAM,KAAK,gBAAgB;AAE3B,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C;AAAA,IACF;AAEA,WAAO,KAAK;AAAA,MACV,KAAK,QAAQ,IAAI,cAAc;AAAA,MAC/B,cAAc;AAAA,QACZ,mBAAmB;AAAA,MACrB;AAAA;AAAA,MAEA,kBAAkB;AAAA;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,MAAM,KAAU;AACpB,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C,YAAM,MAAM,GAAG;AACf;AAAA,IACF;AAEA,WAAO,iBAAiB,GAAG;AAC3B,UAAM,MAAM,GAAG;AAAA,EACjB;AAAA,EAEA,MAAM,UAAU;AACd,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C;AAAA,IACF;AAEA,WAAO,MAAM;AAAA,EACf;AAAA,EAEA,MAAM,MAAM;AAAA,EAAC;AAAA,EAEb,MAAM,kBAAkB;AACtB,UAAM,WAAW,MAAM,MAAM,GAAG,sBAAsB,cAAc;AACpE,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAM,eAAe,KAAK;AAE1B,QAAI,iBAAiB,aAAa;AAChC;AAAA,IACF;AAEA,SAAK,IAAIA,OAAM,OAAO,6DAA6D,CAAC;AACpF,SAAK,IAAI,8CAA8C,WAAW;AAAA,CAAM;AACxE,SAAK,IAAI,mDAAmD;AAC5D,SAAK,IAAI,GAAGA,OAAM,KAAKA,OAAM,UAAUA,OAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,CAA4C;AACvG,SAAK,IAAI,GAAGA,OAAM,KAAKA,OAAM,UAAUA,OAAM,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,CAA6C;AACzG,SAAK,IAAI,GAAGA,OAAM,KAAKA,OAAM,UAAUA,OAAM,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,CAA6C;AAEzG,YAAQ,KAAK,CAAC;AAAA,EAChB;AACF;;;AN7CA,IAAqB,MAArB,MAAqB,aAAY,YAAY;AAAA,EAY3C,YACE,MACA,QACQ,cAAc,IAAI,YAAY,GAC9B,gBAAgB,IAAI,cAAc,GAC1C;AACA,UAAM,MAAM,MAAM;AAHV;AACA;AAAA,EAGV;AAAA,EAlBA,OAAgB,QAAQ;AAAA,IACtB,MAAM,MAAM,OAAO,EAAC,aAAa,QAAQ,UAAU,KAAI,CAAC;AAAA,IACxD,SAAS,MAAM,OAAO,EAAC,aAAa,UAAS,CAAC;AAAA,EAChD;AAAA,EAEA,OAAgB,cAAc;AAAA,EAC9B,OAAgB,WAAW,CAAC,qCAAqC;AAAA,EAEzD,aAAyD;AAAA,EACzD,uBAAsC;AAAA,EAW9C,MAAc,wBAAuC;AACnD,QAAI,CAAC,KAAK,sBAAsB;AAC9B;AAAA,IACF;AAEA,QAAI;AACF,YAAM,KAAK,WAAY,gBAAgB,MAAM;AAAA,QAC3C,eAAe,KAAK;AAAA,MACtB,CAAC;AAAA,IACH,SAAS,OAAY;AACnB,UAAI,OAAO,MAAM,SAAS,aAAa;AACrC,aAAK,MAAM,MAAM,OAAO;AAAA,MAC1B,OAAO;AACL,aAAK,MAAM,2DAA2D;AAAA,MACxE;AAAA,IACF;AAEA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAI,CAAC;AACxD,UAAM,KAAK,sBAAsB;AAAA,EACnC;AAAA,EAEA,MAAa,MAAqB;AAChC,UAAM,EAAC,MAAK,IAAI,MAAM,KAAK,MAAM,IAAG;AAGpC,UAAM,EAAC,UAAU,cAAc,KAAI,IAAI,MAAM,KAAK,YAAY,aAAa,IAAI;AAC/E,UAAM,YAAY,IAAI,iBAAiB,EAAC,QAAQ,KAAK,IAAI,gBAAgB,aAAa,IAAI,OAAO,KAAK,MAAK,CAAC;AAE5G,SAAK,aAAa,iBAAiB,QAAQ;AAE3C,UAAM,oBAAoB,MAAM,KAAK,WAAW,gBAAgB,MAAM,EAAC,OAAO,aAAa,GAAE,CAAC;AAC9F,QAAI,oBAAmC;AAGvC,QAAI,MAAM,SAAS;AACjB,UAAI,CAAC,kBAAkB,KAAK,CAAC,YAAiB,QAAQ,OAAO,MAAM,OAAO,GAAG;AAC3E,aAAK,IAAIC,OAAM,IAAI,mBAAmB,MAAM,OAAO,YAAY,CAAC;AAChE,gBAAQ,KAAK,CAAC;AAAA,MAChB;AAEA,0BAAoB,MAAM;AAC1B,WAAK,IAAIA,OAAM,MAAM,2BAAsB,iBAAiB,EAAE,CAAC;AAAA,IACjE,OAAO;AACL,YAAM,EAAC,UAAS,IAAI,MAAMC,UAAS,OAAO;AAAA,QACxC;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,kBAAkB,IAAI,CAAC,aAAkB;AAAA,YAChD,OAAO,QAAQ;AAAA,YACf,MAAM,GAAG,QAAQ,IAAI,SAAS,QAAQ,EAAE,IAAI,kBAAkB,WAAW,IAAI,8BAA8B,EAAE;AAAA,UAC/G,EAAE;AAAA,QACJ;AAAA,MACF,CAAC;AAED,0BAAoB;AAAA,IACtB;AAGA,UAAM,EAAC,MAAM,mBAAkB,IAAI,MAAM,yBAAyB,oBAAoB,MAAM,IAAI,EAAE;AAElG,QAAI,gDAAmD;AACrD,WAAK;AAAA,QACH,wFAAwF,MAAM,IAAI;AAAA,MACpG;AAAA,IACF;AAEA,UAAM,kCAAkC,6BAA6B,kBAAkB;AAEvF,cAAU,MAAM;AAAA,MACd,OAAO;AAAA,MACP,YAAY;AAAA,QACV,MAAM,MAAM;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAED,SAAK,IAAID,OAAM,MAAM,UAAK,+BAA+B,oBAAoB,CAAC;AAC9E,UAAM,UAAUE,KAAI,sBAAsB,EAAE,MAAM;AAElD,UAAM,WAAW,SAAS;AAG1B,UAAM,cAAc,KAAK,YAAY;AAAA,MACnC;AAAA,MACA,MAAM,SAAS,MAAM,IAAI;AAAA,MACzB,WAAW,SAAS;AAAA,MACpB,WAAW,OAAO,EAAC,KAAK,GAAE,MAAM;AAE9B,aAAK,IAAI,yBAAyB;AAClC,aAAK,IAAI,GAAGF,OAAM,KAAK,KAAK,sBAAmB,CAAC,IAAIA,OAAM,MAAM,GAAG,CAAC,EAAE;AACtE,aAAK,IAAI,GAAGA,OAAM,KAAK,KAAK,wBAAoB,CAAC,IAAIA,OAAM,MAAM,+BAA+B,CAAC,EAAE;AACnG,aAAK;AAAA,UACH,GAAGA,OAAM,KAAK,KAAK,mBAAgB,CAAC,IAAIA,OAAM,MAAM,GAAG,QAAQ,IAAI,sBAAsB,aAAa,iBAAkB,EAAE,CAAC;AAAA,QAC7H;AACA,aAAK,IAAIA,OAAM,OAAO,mCAAmC,CAAC;AAC1D,aAAK,IAAI,IAAI;AAEb,gBAAQ,OAAO;AAGf,cAAM,EAAC,cAAa,IAAI,MAAM,KAAK,WAAY,gCAAgC,OAAO;AAAA,UACpF,UAAU;AAAA,UACV,WAAW;AAAA,UACX,cAAc,uDAAuD,eAAe;AAAA,UACpF,WAAW;AAAA,UACX,MAAM,SAAS,MAAM,IAAI;AAAA,QAC3B,CAAC;AAED,aAAK,uBAAuB;AAE5B,kBAAU,MAAM;AAAA,UACd,OAAO;AAAA,UACP,YAAY;AAAA,YACV,UAAU;AAAA,YACV,MAAM,MAAM;AAAA,YACZ,WAAW;AAAA,YACX,cAAc;AAAA,UAChB;AAAA,QACF,CAAC;AAED,gBAAQ,QAAQ;AAChB,gBAAQ,OAAO;AACf,gBAAQ,QAAQ;AAEhB,cAAM,KAAK,sBAAsB;AAAA,MACnC;AAAA,MACA,eAAe,OAAO,EAAC,GAAE,MAAM;AAC7B,YAAI,KAAK,sBAAsB;AAC7B,oBAAU,MAAM;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,cACV,UAAU;AAAA,YACZ;AAAA,UACF,CAAC;AAED,gBAAM,KAAK,WAAY,kBAAkB,OAAO;AAAA,YAC9C,eAAe,KAAK;AAAA,UACtB,CAAC;AACD,eAAK,uBAAuB;AAAA,QAC9B;AAAA,MACF;AAAA,MACA;AAAA,IACF,CAAC;AAED,UAAM,QAAQ,IAAI,CAAC,WAAW,CAAC;AAAA,EACjC;AAAA,EAEA,MAAc,YAAY;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAOG;AAED,UAAM,SAAS,MAAM,KAAK,cAAc,OAAO;AAAA,MAC7C;AAAA,MACA,WAAW;AAAA,IACb,CAAC;AAED,WAAO,GAAG,WAAW,CAAC,SAAS;AAC7B,WAAK,IAAI,GAAGA,OAAM,MAAM,QAAG,CAAC,IAAIA,OAAM,OAAM,oBAAI,KAAK,GAAE,YAAY,CAAC,CAAC,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE;AAAA,IACvG,CAAC;AAED,WAAO,GAAG,SAAS,CAAC,QAAQ;AAC1B,WAAK,MAAMA,OAAM,IAAI,iBAAiB,IAAI,OAAO,EAAE,CAAC;AAAA,IACtD,CAAC;AAED,WAAO,GAAG,SAAS,YAAY;AAC7B,WAAK,IAAIA,OAAM,OAAO,iBAAiB,CAAC;AAExC,YAAM,cAAc,EAAC,IAAI,SAAQ,CAAC;AAClC,cAAQ,KAAK,CAAC;AAAA,IAChB,CAAC;AAGD,UAAM,QAAQ,IAAI;AAAA,MAChB,IAAI,QAAc,MAAM;AACtB,gBAAQ,GAAG,UAAU,YAAY;AAC/B,eAAK,IAAI,2BAA2B;AACpC,gBAAM,cAAc,EAAC,IAAI,SAAQ,CAAC;AAClC,iBAAO,MAAM;AACb,kBAAQ,KAAK,CAAC;AAAA,QAChB,CAAC;AAED,gBAAQ,GAAG,WAAW,YAAY;AAChC,eAAK,IAAI,2BAA2B;AACpC,gBAAM,cAAc,EAAC,IAAI,SAAQ,CAAC;AAClC,iBAAO,MAAM;AACb,kBAAQ,KAAK,CAAC;AAAA,QAChB,CAAC;AAAA,MACH,CAAC;AAAA,MACD,UAAU,EAAC,KAAK,OAAO,KAAK,IAAI,SAAQ,CAAC;AAAA,IAC3C,CAAC;AAAA,EACH;AACF;", "names": ["inquirer", "ora", "chalk", "Conf", "crypto", "anonymousId", "Conf", "crypto", "chalk", "chalk", "inquirer", "ora"]}