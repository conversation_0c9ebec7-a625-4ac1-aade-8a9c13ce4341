{"version": 3, "sources": ["../src/components/dev-console/utils.ts"], "sourcesContent": ["import {\n  CopilotContextParams,\n  CopilotMessagesContextParams,\n  defaultCopilotContextCategories,\n} from \"@copilotkit/react-core\";\nimport { CopilotKitVersion } from \"./types\";\nimport { ActionExecutionMessage, ResultMessage, TextMessage } from \"@copilotkit/runtime-client-gql\";\nimport { AgentStateMessage } from \"@copilotkit/runtime-client-gql\";\n\nexport function shouldShowDevConsole(showDevConsole: boolean | \"auto\"): boolean {\n  if (typeof showDevConsole === \"boolean\") {\n    return showDevConsole;\n  }\n  return (\n    getHostname() === \"localhost\" ||\n    getHostname() === \"127.0.0.1\" ||\n    getHostname() === \"0.0.0.0\" ||\n    getHostname() === \"::1\"\n  );\n}\n\nfunction getHostname(): string {\n  if (typeof window !== \"undefined\" && window.location) {\n    return window.location.hostname;\n  }\n  return \"\";\n}\n\nexport async function getPublishedCopilotKitVersion(\n  current: string,\n  forceCheck: boolean = false,\n): Promise<CopilotKitVersion> {\n  const LOCAL_STORAGE_KEY = \"__copilotkit_version_check__\";\n  const serializedVersion = localStorage.getItem(LOCAL_STORAGE_KEY);\n  if (serializedVersion && !forceCheck) {\n    try {\n      const parsedVersion: CopilotKitVersion = JSON.parse(serializedVersion);\n      const oneHour = 60 * 60 * 1000;\n      const now = new Date().getTime();\n\n      if (\n        parsedVersion.current === current &&\n        now - new Date(parsedVersion.lastChecked).getTime() < oneHour\n      ) {\n        return parsedVersion;\n      }\n    } catch (error) {\n      console.error(\"Failed to parse CopilotKitVersion from localStorage\", error);\n    }\n  }\n\n  try {\n    const response = await fetch(\"https://api.cloud.copilotkit.ai/check-for-updates\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        packages: [\n          {\n            packageName: \"@copilotkit/shared\",\n            packageVersion: current,\n          },\n        ],\n      }),\n    });\n\n    const data = await response.json();\n\n    const version: CopilotKitVersion = {\n      current,\n      lastChecked: new Date().getTime(),\n      latest: data.packages[0].latestVersion,\n      severity: data.packages[0].severity,\n      advisory: data.packages[0].advisory || null,\n    };\n\n    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(version));\n    return version;\n  } catch (error) {\n    console.error(\"Failed to check for updates\", error);\n    throw error;\n  }\n}\n\nexport function logReadables(context: CopilotContextParams) {\n  console.log(\"%cCurrent Readables:\", \"font-size: 16px; font-weight: bold;\");\n\n  const readables = context.getContextString([], defaultCopilotContextCategories).trim();\n  if (readables.length === 0) {\n    console.log(\"No readables found\");\n    return;\n  }\n  console.log(readables);\n}\n\nexport function logActions(context: CopilotContextParams) {\n  console.log(\"%cCurrent Actions:\", \"font-size: 16px; font-weight: bold;\");\n\n  if (Object.values(context.actions).length === 0) {\n    console.log(\"No actions found\");\n    return;\n  }\n  for (const action of Object.values(context.actions)) {\n    console.group(action.name);\n    console.log(\"name\", action.name);\n    console.log(\"description\", action.description);\n    console.log(\"parameters\", action.parameters);\n\n    console.groupEnd();\n  }\n}\n\nexport function logMessages(context: CopilotMessagesContextParams) {\n  console.log(\"%cCurrent Messages:\", \"font-size: 16px; font-weight: bold;\");\n\n  if (context.messages.length === 0) {\n    console.log(\"No messages found\");\n    return;\n  }\n\n  const tableData = context.messages.map((message) => {\n    if (message.isTextMessage()) {\n      return {\n        id: message.id,\n        type: \"TextMessage\",\n        role: message.role,\n        name: undefined,\n        scope: undefined,\n        content: message.content,\n      };\n    } else if (message.isActionExecutionMessage()) {\n      return {\n        id: message.id,\n        type: \"ActionExecutionMessage\",\n        role: undefined,\n        name: message.name,\n        scope: message.parentMessageId,\n        content: message.arguments,\n      };\n    } else if (message.isResultMessage()) {\n      return {\n        id: message.id,\n        type: \"ResultMessage\",\n        role: undefined,\n        name: message.actionName,\n        scope: message.actionExecutionId,\n        content: message.result,\n      };\n    } else if (message.isAgentStateMessage()) {\n      return {\n        id: message.id,\n        type: `AgentStateMessage (running: ${message.running})`,\n        role: message.role,\n        name: undefined,\n        scope: message.threadId,\n        content: message.state,\n      };\n    }\n  });\n  console.table(tableData);\n}\n"], "mappings": ";;;;;AAAA;AAAA,EAGE;AAAA,OACK;AAKA,SAAS,qBAAqB,gBAA2C;AAC9E,MAAI,OAAO,mBAAmB,WAAW;AACvC,WAAO;AAAA,EACT;AACA,SACE,YAAY,MAAM,eAClB,YAAY,MAAM,eAClB,YAAY,MAAM,aAClB,YAAY,MAAM;AAEtB;AAEA,SAAS,cAAsB;AAC7B,MAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,WAAO,OAAO,SAAS;AAAA,EACzB;AACA,SAAO;AACT;AAEA,SAAsB,8BACpB,SACA,aAAsB,OACM;AAAA;AAC5B,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB,aAAa,QAAQ,iBAAiB;AAChE,QAAI,qBAAqB,CAAC,YAAY;AACpC,UAAI;AACF,cAAM,gBAAmC,KAAK,MAAM,iBAAiB;AACrE,cAAM,UAAU,KAAK,KAAK;AAC1B,cAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAE/B,YACE,cAAc,YAAY,WAC1B,MAAM,IAAI,KAAK,cAAc,WAAW,EAAE,QAAQ,IAAI,SACtD;AACA,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,uDAAuD,KAAK;AAAA,MAC5E;AAAA,IACF;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,qDAAqD;AAAA,QAChF,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,UAAU;AAAA,YACR;AAAA,cACE,aAAa;AAAA,cACb,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,YAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,YAAM,UAA6B;AAAA,QACjC;AAAA,QACA,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAAA,QAChC,QAAQ,KAAK,SAAS,CAAC,EAAE;AAAA,QACzB,UAAU,KAAK,SAAS,CAAC,EAAE;AAAA,QAC3B,UAAU,KAAK,SAAS,CAAC,EAAE,YAAY;AAAA,MACzC;AAEA,mBAAa,QAAQ,mBAAmB,KAAK,UAAU,OAAO,CAAC;AAC/D,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA+B,KAAK;AAClD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAEO,SAAS,aAAa,SAA+B;AAC1D,UAAQ,IAAI,wBAAwB,qCAAqC;AAEzE,QAAM,YAAY,QAAQ,iBAAiB,CAAC,GAAG,+BAA+B,EAAE,KAAK;AACrF,MAAI,UAAU,WAAW,GAAG;AAC1B,YAAQ,IAAI,oBAAoB;AAChC;AAAA,EACF;AACA,UAAQ,IAAI,SAAS;AACvB;AAEO,SAAS,WAAW,SAA+B;AACxD,UAAQ,IAAI,sBAAsB,qCAAqC;AAEvE,MAAI,OAAO,OAAO,QAAQ,OAAO,EAAE,WAAW,GAAG;AAC/C,YAAQ,IAAI,kBAAkB;AAC9B;AAAA,EACF;AACA,aAAW,UAAU,OAAO,OAAO,QAAQ,OAAO,GAAG;AACnD,YAAQ,MAAM,OAAO,IAAI;AACzB,YAAQ,IAAI,QAAQ,OAAO,IAAI;AAC/B,YAAQ,IAAI,eAAe,OAAO,WAAW;AAC7C,YAAQ,IAAI,cAAc,OAAO,UAAU;AAE3C,YAAQ,SAAS;AAAA,EACnB;AACF;AAEO,SAAS,YAAY,SAAuC;AACjE,UAAQ,IAAI,uBAAuB,qCAAqC;AAExE,MAAI,QAAQ,SAAS,WAAW,GAAG;AACjC,YAAQ,IAAI,mBAAmB;AAC/B;AAAA,EACF;AAEA,QAAM,YAAY,QAAQ,SAAS,IAAI,CAAC,YAAY;AAClD,QAAI,QAAQ,cAAc,GAAG;AAC3B,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,gBAAgB,GAAG;AACpC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,oBAAoB,GAAG;AACxC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM,+BAA+B,QAAQ;AAAA,QAC7C,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,QACN,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,UAAQ,MAAM,SAAS;AACzB;", "names": []}