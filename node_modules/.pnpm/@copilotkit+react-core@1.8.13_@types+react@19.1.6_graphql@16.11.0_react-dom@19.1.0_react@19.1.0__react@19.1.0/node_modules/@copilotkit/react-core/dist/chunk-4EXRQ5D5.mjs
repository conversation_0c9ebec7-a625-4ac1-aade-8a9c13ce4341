import {
  useCopilotContext
} from "./chunk-7WG4MNRX.mjs";

// src/hooks/use-copilot-additional-instructions.ts
import { useEffect } from "react";
function useCopilotAdditionalInstructions({ instructions, available = "enabled" }, dependencies) {
  const { setAdditionalInstructions } = useCopilotContext();
  useEffect(() => {
    if (available === "disabled")
      return;
    setAdditionalInstructions((prevInstructions) => [...prevInstructions || [], instructions]);
    return () => {
      setAdditionalInstructions(
        (prevInstructions) => (prevInstructions == null ? void 0 : prevInstructions.filter((instruction) => instruction !== instructions)) || []
      );
    };
  }, [available, instructions, setAdditionalInstructions, ...dependencies || []]);
}

export {
  useCopilotAdditionalInstructions
};
//# sourceMappingURL=chunk-4EXRQ5D5.mjs.map