{"version": 3, "sources": ["../../../src/components/error-boundary/error-boundary.tsx", "../../../src/lib/status-checker.ts", "../../../src/components/usage-banner.tsx", "../../../src/components/error-boundary/error-utils.tsx", "../../../src/components/toast/toast-provider.tsx", "../../../src/components/toast/exclamation-mark-icon.tsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { Severity, CopilotKitError } from \"@copilotkit/shared\";\nimport { StatusChecker } from \"../../lib/status-checker\";\nimport { renderCopilotKitUsage, UsageBanner } from \"../usage-banner\";\nimport { useErrorToast } from \"./error-utils\";\nimport { COPILOT_CLOUD_ERROR_NAMES } from \"@copilotkit/shared\";\n\nconst statusChecker = new StatusChecker();\n\ninterface Props {\n  children: React.ReactNode;\n  publicApiKey?: string;\n  showUsageBanner?: boolean;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: CopilotKitError;\n  status?: {\n    severity: Severity;\n    message: string;\n  };\n}\n\nexport class CopilotErrorBoundary extends React.Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n    };\n  }\n\n  static getDerivedStateFromError(error: CopilotKitError): State {\n    return { hasError: true, error };\n  }\n\n  componentDidMount() {\n    if (this.props.publicApiKey) {\n      statusChecker.start(this.props.publicApiKey, (newStatus) => {\n        this.setState((prevState) => {\n          if (newStatus?.severity !== prevState.status?.severity) {\n            return { status: newStatus ?? undefined };\n          }\n          return null;\n        });\n      });\n    }\n  }\n\n  componentWillUnmount() {\n    statusChecker.stop();\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error(\"CopilotKit Error:\", error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.state.error instanceof CopilotKitError) {\n        // @ts-expect-error -- It's a copilotkit error at this state. Name is valid\n        if (COPILOT_CLOUD_ERROR_NAMES.includes(this.state.error.name)) {\n          return (\n            <ErrorToast error={this.state.error}>\n              {renderCopilotKitUsage(this.state.error)}\n            </ErrorToast>\n          );\n        }\n\n        return (\n          <>\n            {this.props.children}\n            {this.props.showUsageBanner && (\n              <UsageBanner\n                severity={this.state.status?.severity}\n                message={this.state.status?.message}\n              />\n            )}\n          </>\n        );\n      }\n      throw this.state.error;\n    }\n\n    return this.props.children;\n  }\n}\n\nexport function ErrorToast({ error, children }: { error?: Error; children: React.ReactNode }) {\n  const addErrorToast = useErrorToast();\n\n  useEffect(() => {\n    if (error) {\n      addErrorToast([error]);\n    }\n  }, [error, addErrorToast]);\n\n  if (!error) throw error;\n  return children;\n}\n", "import {\n  COPILOT_CLOUD_API_URL,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  Severity,\n} from \"@copilotkit/shared\";\n\nconst STATUS_CHECK_INTERVAL = 1000 * 60 * 5; // 5 minutes\n\nexport type Status = {\n  severity: Severity;\n  message: string;\n};\n\nexport class StatusChecker {\n  private activeKey: string | null = null;\n  private intervalId: ReturnType<typeof setInterval> | null = null;\n  private instanceCount = 0;\n  private lastResponse: Status | null = null;\n\n  async start(publicApiKey: string, onUpdate?: (status: Status | null) => void) {\n    this.instanceCount++;\n    if (this.activeKey === publicApiKey) return;\n\n    if (this.intervalId) clearInterval(this.intervalId);\n\n    const checkStatus = async () => {\n      try {\n        const response = await fetch(`${COPILOT_CLOUD_API_URL}/ciu`, {\n          method: \"GET\",\n          headers: {\n            [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey,\n          },\n        }).then((response) => response.json() as Promise<Status>);\n        this.lastResponse = response;\n        onUpdate?.(response);\n        return response;\n      } catch (error) {\n        // Silently fail\n        return null;\n      }\n    };\n\n    const initialResponse = await checkStatus();\n    this.intervalId = setInterval(checkStatus, STATUS_CHECK_INTERVAL);\n    this.activeKey = publicApiKey;\n    return initialResponse;\n  }\n\n  getLastResponse() {\n    return this.lastResponse;\n  }\n\n  stop() {\n    this.instanceCount--;\n    if (this.instanceCount === 0) {\n      if (this.intervalId) {\n        clearInterval(this.intervalId);\n        this.intervalId = null;\n        this.activeKey = null;\n        this.lastResponse = null;\n      }\n    }\n  }\n}\n", "import { Severity, CopilotKitError, ERROR_NAMES } from \"@copilotkit/shared\";\n\ninterface UsageBannerProps {\n  severity?: Severity;\n  message?: string;\n  icon?: React.ReactNode;\n  actions?: {\n    primary?: {\n      label: string;\n      onClick: () => void;\n    };\n    secondary?: {\n      label: string;\n      onClick: () => void;\n    };\n  };\n}\n\nconst defaultIcons: Record<Severity, JSX.Element> = {\n  [Severity.Error]: (\n    <svg\n      viewBox=\"0 0 24 24\"\n      width=\"20\"\n      height=\"20\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      fill=\"none\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" />\n      <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" />\n      <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" />\n    </svg>\n  ),\n};\n\nexport function UsageBanner({\n  severity = Severity.Error,\n  message = \"\",\n  icon,\n  actions,\n}: UsageBannerProps) {\n  if (!message || !severity) {\n    return null;\n  }\n\n  const Icon = icon || defaultIcons[severity];\n\n  const bgColor = {\n    info: \"#dbeafe\",\n    warning: \"#fef3c7\",\n    error: \"#fee2e2\",\n  }[severity];\n\n  const textColor = {\n    info: \"#1e40af\",\n    warning: \"#854d0e\",\n    error: \"#991b1b\",\n  }[severity];\n\n  const iconColor = {\n    info: \"#3b82f6\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  }[severity];\n\n  const primaryButtonColor = {\n    info: \"#3b82f6\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  }[severity];\n\n  const primaryButtonHoverColor = {\n    info: \"#2563eb\",\n    warning: \"#ca8a04\",\n    error: \"#dc2626\",\n  }[severity];\n\n  return (\n    <div\n      style={{\n        position: \"fixed\",\n        bottom: \"16px\",\n        left: \"50%\",\n        transform: \"translateX(-50%)\",\n        maxWidth: \"90%\",\n        zIndex: 9999,\n      }}\n    >\n      <div\n        style={{\n          display: \"flex\",\n          flexWrap: \"wrap\",\n          alignItems: \"center\",\n          gap: \"12px\",\n          borderRadius: \"9999px\",\n          border: \"1px solid #e5e7eb\",\n          backgroundColor: bgColor,\n          padding: \"8px 16px\",\n          boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\",\n        }}\n      >\n        <div style={{ color: iconColor }}>{Icon}</div>\n        <span\n          style={{\n            flex: 1,\n            fontSize: \"14px\",\n            fontWeight: 500,\n            color: textColor,\n            whiteSpace: \"normal\",\n            wordBreak: \"break-word\",\n          }}\n        >\n          {message}\n        </span>\n        <div\n          style={{\n            display: \"flex\",\n            gap: \"8px\",\n            flexWrap: \"wrap\",\n          }}\n        >\n          {actions?.secondary && (\n            <button\n              onClick={actions.secondary.onClick}\n              style={{\n                borderRadius: \"9999px\",\n                padding: \"4px 12px\",\n                fontSize: \"14px\",\n                fontWeight: 500,\n                color: textColor,\n                backgroundColor: \"transparent\",\n                border: \"none\",\n                cursor: \"pointer\",\n                transition: \"background-color 0.2s\",\n              }}\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.5)\")}\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = \"transparent\")}\n            >\n              {actions.secondary.label}\n            </button>\n          )}\n          {actions?.primary && (\n            <button\n              onClick={actions.primary.onClick}\n              style={{\n                borderRadius: \"9999px\",\n                padding: \"4px 12px\",\n                fontSize: \"14px\",\n                fontWeight: 500,\n                color: \"#fff\",\n                backgroundColor: primaryButtonColor,\n                border: \"none\",\n                cursor: \"pointer\",\n                transition: \"background-color 0.2s\",\n              }}\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = primaryButtonHoverColor)}\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = primaryButtonColor)}\n            >\n              {actions.primary.label}\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function renderCopilotKitUsage(error: CopilotKitError) {\n  switch (error.name) {\n    case ERROR_NAMES.CONFIGURATION_ERROR:\n      return <UsageBanner severity={error.severity} message={error.message} />;\n    case ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR:\n      return (\n        <UsageBanner\n          severity={error.severity}\n          message={error.message}\n          actions={{\n            primary: {\n              label: \"Sign In\",\n              onClick: () => {\n                window.location.href = \"https://cloud.copilotkit.ai\";\n              },\n            },\n          }}\n        />\n      );\n    case ERROR_NAMES.UPGRADE_REQUIRED_ERROR:\n      return (\n        <UsageBanner\n          severity={error.severity}\n          message={error.message}\n          actions={{\n            primary: {\n              label: \"Upgrade\",\n              onClick: () => {\n                window.location.href = \"https://copilotkit.ai/\";\n              },\n            },\n          }}\n        />\n      );\n  }\n}\n", "import React, { useCallback } from \"react\";\nimport { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport { useToast } from \"../toast/toast-provider\";\nimport { ExclamationMarkIcon } from \"../toast/exclamation-mark-icon\";\nimport ReactMarkdown from \"react-markdown\";\n\ninterface OriginalError {\n  message?: string;\n  stack?: string;\n}\n\nexport function ErrorToast({ errors }: { errors: (Error | GraphQLError)[] }) {\n  const errorsToRender = errors.map((error, idx) => {\n    const originalError =\n      \"extensions\" in error ? (error.extensions?.originalError as undefined | OriginalError) : {};\n    const message = originalError?.message ?? error.message;\n    const code = \"extensions\" in error ? (error.extensions?.code as string) : null;\n\n    return (\n      <div\n        key={idx}\n        style={{\n          marginTop: idx === 0 ? 0 : 10,\n          marginBottom: 14,\n        }}\n      >\n        <ExclamationMarkIcon style={{ marginBottom: 4 }} />\n\n        {code && (\n          <div\n            style={{\n              fontWeight: \"600\",\n              marginBottom: 4,\n            }}\n          >\n            Copilot Cloud Error:{\" \"}\n            <span style={{ fontFamily: \"monospace\", fontWeight: \"normal\" }}>{code}</span>\n          </div>\n        )}\n        <ReactMarkdown>{message}</ReactMarkdown>\n      </div>\n    );\n  });\n  return (\n    <div\n      style={{\n        fontSize: \"13px\",\n        maxWidth: \"600px\",\n      }}\n    >\n      {errorsToRender}\n      <div style={{ fontSize: \"11px\", opacity: 0.75 }}>\n        NOTE: This error only displays during local development.\n      </div>\n    </div>\n  );\n}\n\nexport function useErrorToast() {\n  const { addToast } = useToast();\n\n  return useCallback(\n    (error: (Error | GraphQLError)[]) => {\n      const errorId = error\n        .map((err) => {\n          const message =\n            \"extensions\" in err\n              ? (err.extensions?.originalError as any)?.message || err.message\n              : err.message;\n          const stack = err.stack || \"\";\n          return btoa(message + stack).slice(0, 32); // Create hash from message + stack\n        })\n        .join(\"|\");\n\n      addToast({\n        type: \"error\",\n        id: errorId, // Toast libraries typically dedupe by id\n        message: <ErrorToast errors={error} />,\n      });\n    },\n    [addToast],\n  );\n}\n\nexport function useAsyncCallback<T extends (...args: any[]) => Promise<any>>(\n  callback: T,\n  deps: Parameters<typeof useCallback>[1],\n) {\n  const addErrorToast = useErrorToast();\n  return useCallback(async (...args: Parameters<T>) => {\n    try {\n      return await callback(...args);\n    } catch (error) {\n      console.error(\"Error in async callback:\", error);\n      // @ts-ignore\n      addErrorToast([error]);\n      throw error;\n    }\n  }, deps);\n}\n", "import { GraphQLError } from \"@copilotkit/runtime-client-gql\";\nimport React, { createContext, useContext, useState, useCallback } from \"react\";\nimport { ErrorToast } from \"../error-boundary/error-utils\";\nimport { PartialBy } from \"@copilotkit/shared\";\n\ninterface Toast {\n  id: string;\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  duration?: number;\n}\n\ninterface ToastContextValue {\n  toasts: Toast[];\n  addToast: (toast: PartialBy<Toast, \"id\">) => void;\n  addGraphQLErrorsToast: (errors: GraphQLError[]) => void;\n  removeToast: (id: string) => void;\n  enabled: boolean;\n}\n\nconst ToastContext = createContext<ToastContextValue | undefined>(undefined);\n\nexport function useToast() {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error(\"useToast must be used within a ToastProvider\");\n  }\n  return context;\n}\n\nexport function ToastProvider({\n  enabled,\n  children,\n}: {\n  enabled: boolean;\n  children: React.ReactNode;\n}) {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n  const addToast = useCallback(\n    (toast: PartialBy<Toast, \"id\">) => {\n      // We do not display these errors unless we are in dev mode.\n      if (!enabled) {\n        return;\n      }\n\n      const id = toast.id ?? Math.random().toString(36).substring(2, 9);\n\n      setToasts((currentToasts) => {\n        if (currentToasts.find((toast) => toast.id === id)) return currentToasts;\n        return [...currentToasts, { ...toast, id }];\n      });\n\n      if (toast.duration) {\n        setTimeout(() => {\n          removeToast(id);\n        }, toast.duration);\n      }\n    },\n    [enabled],\n  );\n\n  const addGraphQLErrorsToast = useCallback((errors: GraphQLError[]) => {\n    addToast({\n      type: \"error\",\n      message: <ErrorToast errors={errors} />,\n    });\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));\n  }, []);\n\n  const value = {\n    toasts,\n    addToast,\n    addGraphQLErrorsToast,\n    removeToast,\n    enabled,\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      <div\n        style={{\n          position: \"fixed\",\n          bottom: \"1rem\",\n          left: \"50%\",\n          transform: \"translateX(-50%)\",\n          zIndex: 50,\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"0.5rem\",\n        }}\n      >\n        {toasts.length > 1 && (\n          <div style={{ textAlign: \"right\" }}>\n            <button\n              onClick={() => setToasts([])}\n              style={{\n                padding: \"4px 8px\",\n                fontSize: \"12px\",\n                cursor: \"pointer\",\n                background: \"white\",\n                border: \"1px solid rgba(0,0,0,0.2)\",\n                borderRadius: \"4px\",\n              }}\n            >\n              Close All\n            </button>\n          </div>\n        )}\n        {toasts.map((toast) => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n      {children}\n    </ToastContext.Provider>\n  );\n}\n\nfunction Toast({\n  message,\n  type = \"info\",\n  onClose,\n}: {\n  message: string | React.ReactNode;\n  type: \"info\" | \"success\" | \"warning\" | \"error\";\n  onClose: () => void;\n}) {\n  const bgColors = {\n    info: \"#3b82f6\",\n    success: \"#22c55e\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  };\n\n  return (\n    <div\n      style={{\n        backgroundColor: bgColors[type],\n        color: \"white\",\n        padding: \"0.5rem 1.5rem\",\n        borderRadius: \"0.25rem\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        position: \"relative\",\n        minWidth: \"200px\",\n      }}\n    >\n      <div>{message}</div>\n      <button\n        onClick={onClose}\n        style={{\n          position: \"absolute\",\n          top: \"0\",\n          right: \"0\",\n          background: \"none\",\n          border: \"none\",\n          color: \"white\",\n          cursor: \"pointer\",\n          padding: \"0.5rem\",\n          fontSize: \"1rem\",\n        }}\n      >\n        ✕\n      </button>\n    </div>\n  );\n}\n", "import React from \"react\";\n\nexport const ExclamationMarkIcon = ({\n  className,\n  style,\n}: {\n  className?: string;\n  style?: React.CSSProperties;\n}) => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    className={`lucide lucide-circle-alert ${className ? className : \"\"}`}\n    style={style}\n  >\n    <circle cx=\"12\" cy=\"12\" r=\"10\" />\n    <line x1=\"12\" x2=\"12\" y1=\"8\" y2=\"12\" />\n    <line x1=\"12\" x2=\"12.01\" y1=\"16\" y2=\"16\" />\n  </svg>\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,oBAAAA;AAAA;AAAA;AAAA,IAAAC,gBAAiC;AACjC,IAAAC,iBAA0C;;;ACD1C,oBAIO;AAEP,IAAM,wBAAwB,MAAO,KAAK;AAOnC,IAAM,gBAAN,MAAoB;AAAA,EAApB;AACL,SAAQ,YAA2B;AACnC,SAAQ,aAAoD;AAC5D,SAAQ,gBAAgB;AACxB,SAAQ,eAA8B;AAAA;AAAA,EAEhC,MAAM,cAAsB,UAA4C;AAAA;AAC5E,WAAK;AACL,UAAI,KAAK,cAAc;AAAc;AAErC,UAAI,KAAK;AAAY,sBAAc,KAAK,UAAU;AAElD,YAAM,cAAc,MAAY;AAC9B,YAAI;AACF,gBAAM,WAAW,MAAM,MAAM,GAAG,2CAA6B;AAAA,YAC3D,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,CAAC,iDAAmC,GAAG;AAAA,YACzC;AAAA,UACF,CAAC,EAAE,KAAK,CAACC,cAAaA,UAAS,KAAK,CAAoB;AACxD,eAAK,eAAe;AACpB,+CAAW;AACX,iBAAO;AAAA,QACT,SAAS,OAAP;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,kBAAkB,MAAM,YAAY;AAC1C,WAAK,aAAa,YAAY,aAAa,qBAAqB;AAChE,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO;AACL,SAAK;AACL,QAAI,KAAK,kBAAkB,GAAG;AAC5B,UAAI,KAAK,YAAY;AACnB,sBAAc,KAAK,UAAU;AAC7B,aAAK,aAAa;AAClB,aAAK,YAAY;AACjB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;;;AC/DA,IAAAC,iBAAuD;AAoBnD;AAFJ,IAAM,eAA8C;AAAA,EAClD,CAAC,wBAAS,KAAK,GACb;AAAA,IAAC;AAAA;AAAA,MACC,SAAQ;AAAA,MACR,OAAM;AAAA,MACN,QAAO;AAAA,MACP,QAAO;AAAA,MACP,aAAY;AAAA,MACZ,MAAK;AAAA,MACL,eAAc;AAAA,MACd,gBAAe;AAAA,MAEf;AAAA,oDAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,QAC/B,4CAAC,UAAK,IAAG,MAAK,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK;AAAA,QACpC,4CAAC,UAAK,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,MAAK;AAAA;AAAA;AAAA,EACtC;AAEJ;AAEO,SAAS,YAAY;AAAA,EAC1B,WAAW,wBAAS;AAAA,EACpB,UAAU;AAAA,EACV;AAAA,EACA;AACF,GAAqB;AACnB,MAAI,CAAC,WAAW,CAAC,UAAU;AACzB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,QAAQ,aAAa,QAAQ;AAE1C,QAAM,UAAU;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,qBAAqB;AAAA,IACzB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,0BAA0B;AAAA,IAC9B,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MAEA;AAAA,QAAC;AAAA;AAAA,UACC,OAAO;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,KAAK;AAAA,YACL,cAAc;AAAA,YACd,QAAQ;AAAA,YACR,iBAAiB;AAAA,YACjB,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UAEA;AAAA,wDAAC,SAAI,OAAO,EAAE,OAAO,UAAU,GAAI,gBAAK;AAAA,YACxC;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,MAAM;AAAA,kBACN,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,YAAY;AAAA,kBACZ,WAAW;AAAA,gBACb;AAAA,gBAEC;AAAA;AAAA,YACH;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,KAAK;AAAA,kBACL,UAAU;AAAA,gBACZ;AAAA,gBAEC;AAAA,sDAAS,cACR;AAAA,oBAAC;AAAA;AAAA,sBACC,SAAS,QAAQ,UAAU;AAAA,sBAC3B,OAAO;AAAA,wBACL,cAAc;AAAA,wBACd,SAAS;AAAA,wBACT,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO;AAAA,wBACP,iBAAiB;AAAA,wBACjB,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA,aAAa,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAC7D,YAAY,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAE3D,kBAAQ,UAAU;AAAA;AAAA,kBACrB;AAAA,mBAED,mCAAS,YACR;AAAA,oBAAC;AAAA;AAAA,sBACC,SAAS,QAAQ,QAAQ;AAAA,sBACzB,OAAO;AAAA,wBACL,cAAc;AAAA,wBACd,SAAS;AAAA,wBACT,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO;AAAA,wBACP,iBAAiB;AAAA,wBACjB,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA,aAAa,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAC7D,YAAY,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAE3D,kBAAQ,QAAQ;AAAA;AAAA,kBACnB;AAAA;AAAA;AAAA,YAEJ;AAAA;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,sBAAsB,OAAwB;AAC5D,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK,2BAAY;AACf,aAAO,4CAAC,eAAY,UAAU,MAAM,UAAU,SAAS,MAAM,SAAS;AAAA,IACxE,KAAK,2BAAY;AACf,aACE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,YACP,SAAS;AAAA,cACP,OAAO;AAAA,cACP,SAAS,MAAM;AACb,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,IAEJ,KAAK,2BAAY;AACf,aACE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,YACP,SAAS;AAAA,cACP,OAAO;AAAA,cACP,SAAS,MAAM;AACb,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,EAEN;AACF;;;AC5MA,IAAAC,gBAAmC;;;ACCnC,mBAAwE;AA+DzD,IAAAC,sBAAA;AA5Cf,IAAM,mBAAe,4BAA6C,MAAS;AAEpE,SAAS,WAAW;AACzB,QAAM,cAAU,yBAAW,YAAY;AACvC,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,SAAO;AACT;;;ACnBE,IAAAC,sBAAA;AAPK,IAAM,sBAAsB,CAAC;AAAA,EAClC;AAAA,EACA;AACF,MAIE;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,QAAO;AAAA,IACP,aAAY;AAAA,IACZ,eAAc;AAAA,IACd,gBAAe;AAAA,IACf,WAAW,8BAA8B,YAAY,YAAY;AAAA,IACjE;AAAA,IAEA;AAAA,mDAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,MAC/B,6CAAC,UAAK,IAAG,MAAK,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK;AAAA,MACrC,6CAAC,UAAK,IAAG,MAAK,IAAG,SAAQ,IAAG,MAAK,IAAG,MAAK;AAAA;AAAA;AAC3C;;;AFrBF,4BAA0B;AAsBlB,IAAAC,sBAAA;AAfD,SAAS,WAAW,EAAE,OAAO,GAAyC;AAC3E,QAAM,iBAAiB,OAAO,IAAI,CAAC,OAAO,QAAQ;AAZpD;AAaI,UAAM,gBACJ,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,gBAA8C,CAAC;AAC5F,UAAM,WAAU,oDAAe,YAAf,YAA0B,MAAM;AAChD,UAAM,OAAO,gBAAgB,SAAS,WAAM,eAAN,mBAAkB,OAAkB;AAE1E,WACE;AAAA,MAAC;AAAA;AAAA,QAEC,OAAO;AAAA,UACL,WAAW,QAAQ,IAAI,IAAI;AAAA,UAC3B,cAAc;AAAA,QAChB;AAAA,QAEA;AAAA,uDAAC,uBAAoB,OAAO,EAAE,cAAc,EAAE,GAAG;AAAA,UAEhD,QACC;AAAA,YAAC;AAAA;AAAA,cACC,OAAO;AAAA,gBACL,YAAY;AAAA,gBACZ,cAAc;AAAA,cAChB;AAAA,cACD;AAAA;AAAA,gBACsB;AAAA,gBACrB,6CAAC,UAAK,OAAO,EAAE,YAAY,aAAa,YAAY,SAAS,GAAI,gBAAK;AAAA;AAAA;AAAA,UACxE;AAAA,UAEF,6CAAC,sBAAAC,SAAA,EAAe,mBAAQ;AAAA;AAAA;AAAA,MAnBnB;AAAA,IAoBP;AAAA,EAEJ,CAAC;AACD,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,MAEC;AAAA;AAAA,QACD,6CAAC,SAAI,OAAO,EAAE,UAAU,QAAQ,SAAS,KAAK,GAAG,sEAEjD;AAAA;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,gBAAgB;AAC9B,QAAM,EAAE,SAAS,IAAI,SAAS;AAE9B,aAAO;AAAA,IACL,CAAC,UAAoC;AACnC,YAAM,UAAU,MACb,IAAI,CAAC,QAAQ;AAhEtB;AAiEU,cAAM,UACJ,gBAAgB,QACX,eAAI,eAAJ,mBAAgB,kBAAhB,mBAAuC,YAAW,IAAI,UACvD,IAAI;AACV,cAAM,QAAQ,IAAI,SAAS;AAC3B,eAAO,KAAK,UAAU,KAAK,EAAE,MAAM,GAAG,EAAE;AAAA,MAC1C,CAAC,EACA,KAAK,GAAG;AAEX,eAAS;AAAA,QACP,MAAM;AAAA,QACN,IAAI;AAAA;AAAA,QACJ,SAAS,6CAAC,cAAW,QAAQ,OAAO;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,IACA,CAAC,QAAQ;AAAA,EACX;AACF;;;AH7EA,IAAAC,iBAA0C;AA0D9B,IAAAC,sBAAA;AAxDZ,IAAM,gBAAgB,IAAI,cAAc;AAiBjC,IAAM,uBAAN,cAAmC,cAAAC,QAAM,UAAwB;AAAA,EACtE,YAAY,OAAc;AACxB,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EAEA,OAAO,yBAAyB,OAA+B;AAC7D,WAAO,EAAE,UAAU,MAAM,MAAM;AAAA,EACjC;AAAA,EAEA,oBAAoB;AAClB,QAAI,KAAK,MAAM,cAAc;AAC3B,oBAAc,MAAM,KAAK,MAAM,cAAc,CAAC,cAAc;AAC1D,aAAK,SAAS,CAAC,cAAc;AAvCrC;AAwCU,eAAI,uCAAW,gBAAa,eAAU,WAAV,mBAAkB,WAAU;AACtD,mBAAO,EAAE,QAAQ,gCAAa,OAAU;AAAA,UAC1C;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,kBAAc,KAAK;AAAA,EACrB;AAAA,EAEA,kBAAkB,OAAc,WAA4B;AAC1D,YAAQ,MAAM,qBAAqB,OAAO,SAAS;AAAA,EACrD;AAAA,EAEA,SAAS;AAzDX;AA0DI,QAAI,KAAK,MAAM,UAAU;AACvB,UAAI,KAAK,MAAM,iBAAiB,gCAAiB;AAE/C,YAAI,yCAA0B,SAAS,KAAK,MAAM,MAAM,IAAI,GAAG;AAC7D,iBACE,6CAACC,aAAA,EAAW,OAAO,KAAK,MAAM,OAC3B,gCAAsB,KAAK,MAAM,KAAK,GACzC;AAAA,QAEJ;AAEA,eACE,8EACG;AAAA,eAAK,MAAM;AAAA,UACX,KAAK,MAAM,mBACV;AAAA,YAAC;AAAA;AAAA,cACC,WAAU,UAAK,MAAM,WAAX,mBAAmB;AAAA,cAC7B,UAAS,UAAK,MAAM,WAAX,mBAAmB;AAAA;AAAA,UAC9B;AAAA,WAEJ;AAAA,MAEJ;AACA,YAAM,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AAEO,SAASA,YAAW,EAAE,OAAO,SAAS,GAAiD;AAC5F,QAAM,gBAAgB,cAAc;AAEpC,+BAAU,MAAM;AACd,QAAI,OAAO;AACT,oBAAc,CAAC,KAAK,CAAC;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,OAAO,aAAa,CAAC;AAEzB,MAAI,CAAC;AAAO,UAAM;AAClB,SAAO;AACT;", "names": ["ErrorToast", "import_react", "import_shared", "response", "import_shared", "import_react", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "ReactMarkdown", "import_shared", "import_jsx_runtime", "React", "ErrorToast"]}