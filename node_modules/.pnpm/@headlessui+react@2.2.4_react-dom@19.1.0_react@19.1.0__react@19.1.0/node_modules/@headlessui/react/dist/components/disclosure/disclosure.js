"use client";import{useFocusRing as z}from"@react-aria/focus";import{useHover as Q}from"@react-aria/interactions";import y,{Fragment as k,createContext as I,useContext as x,useEffect as w,useMemo as C,useReducer as Y,useRef as K,useState as Z}from"react";import{useActivePress as ee}from'../../hooks/use-active-press.js';import{useEvent as P}from'../../hooks/use-event.js';import{useId as W}from'../../hooks/use-id.js';import{useResolveButtonType as te}from'../../hooks/use-resolve-button-type.js';import{optionalRef as ne,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{transitionDataAttributes as oe,useTransition as le}from'../../hooks/use-transition.js';import{CloseProvider as re}from'../../internal/close-provider.js';import{OpenClosedProvider as se,ResetOpenClosedProvider as ue,State as R,useOpenClosed as ie}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ae}from'../../utils/bugs.js';import*as j from'../../utils/dom.js';import{match as B}from'../../utils/match.js';import{getOwnerDocument as pe}from'../../utils/owner.js';import{RenderFeatures as V,forwardRefWithAs as O,mergeProps as $,useRender as v}from'../../utils/render.js';import{startTransition as ce}from'../../utils/start-transition.js';import{Keys as A}from'../keyboard.js';var de=(l=>(l[l.Open=0]="Open",l[l.Closed=1]="Closed",l))(de||{}),Te=(n=>(n[n.ToggleDisclosure=0]="ToggleDisclosure",n[n.CloseDisclosure=1]="CloseDisclosure",n[n.SetButtonId=2]="SetButtonId",n[n.SetPanelId=3]="SetPanelId",n[n.SetButtonElement=4]="SetButtonElement",n[n.SetPanelElement=5]="SetPanelElement",n))(Te||{});let me={[0]:e=>({...e,disclosureState:B(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,t){return e.buttonId===t.buttonId?e:{...e,buttonId:t.buttonId}},[3](e,t){return e.panelId===t.panelId?e:{...e,panelId:t.panelId}},[4](e,t){return e.buttonElement===t.element?e:{...e,buttonElement:t.element}},[5](e,t){return e.panelElement===t.element?e:{...e,panelElement:t.element}}},_=I(null);_.displayName="DisclosureContext";function M(e){let t=x(_);if(t===null){let l=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,M),l}return t}let F=I(null);F.displayName="DisclosureAPIContext";function J(e){let t=x(F);if(t===null){let l=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,J),l}return t}let H=I(null);H.displayName="DisclosurePanelContext";function fe(){return x(H)}function De(e,t){return B(t.type,me,e,t)}let ye=k;function Pe(e,t){let{defaultOpen:l=!1,...p}=e,a=K(null),c=L(t,ne(u=>{a.current=u},e.as===void 0||e.as===k)),n=Y(De,{disclosureState:l?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:o,buttonId:r},f]=n,s=P(u=>{f({type:1});let d=pe(a);if(!d||!r)return;let T=(()=>u?j.isHTMLorSVGElement(u)?u:"current"in u&&j.isHTMLorSVGElement(u.current)?u.current:d.getElementById(r):d.getElementById(r))();T==null||T.focus()}),E=C(()=>({close:s}),[s]),m=C(()=>({open:o===0,close:s}),[o,s]),D={ref:c},S=v();return y.createElement(_.Provider,{value:n},y.createElement(F.Provider,{value:E},y.createElement(re,{value:s},y.createElement(se,{value:B(o,{[0]:R.Open,[1]:R.Closed})},S({ourProps:D,theirProps:p,slot:m,defaultTag:ye,name:"Disclosure"})))))}let Ee="button";function Se(e,t){let l=W(),{id:p=`headlessui-disclosure-button-${l}`,disabled:a=!1,autoFocus:c=!1,...n}=e,[o,r]=M("Disclosure.Button"),f=fe(),s=f===null?!1:f===o.panelId,E=K(null),m=L(E,t,P(i=>{if(!s)return r({type:4,element:i})}));w(()=>{if(!s)return r({type:2,buttonId:p}),()=>{r({type:2,buttonId:null})}},[p,r,s]);let D=P(i=>{var g;if(s){if(o.disclosureState===1)return;switch(i.key){case A.Space:case A.Enter:i.preventDefault(),i.stopPropagation(),r({type:0}),(g=o.buttonElement)==null||g.focus();break}}else switch(i.key){case A.Space:case A.Enter:i.preventDefault(),i.stopPropagation(),r({type:0});break}}),S=P(i=>{switch(i.key){case A.Space:i.preventDefault();break}}),u=P(i=>{var g;ae(i.currentTarget)||a||(s?(r({type:0}),(g=o.buttonElement)==null||g.focus()):r({type:0}))}),{isFocusVisible:d,focusProps:T}=z({autoFocus:c}),{isHovered:b,hoverProps:h}=Q({isDisabled:a}),{pressed:U,pressProps:G}=ee({disabled:a}),X=C(()=>({open:o.disclosureState===0,hover:b,active:U,disabled:a,focus:d,autofocus:c}),[o,b,U,d,a,c]),N=te(e,o.buttonElement),q=s?$({ref:m,type:N,disabled:a||void 0,autoFocus:c,onKeyDown:D,onClick:u},T,h,G):$({ref:m,id:p,type:N,"aria-expanded":o.disclosureState===0,"aria-controls":o.panelElement?o.panelId:void 0,disabled:a||void 0,autoFocus:c,onKeyDown:D,onKeyUp:S,onClick:u},T,h,G);return v()({ourProps:q,theirProps:n,slot:X,defaultTag:Ee,name:"Disclosure.Button"})}let ge="div",Ae=V.RenderStrategy|V.Static;function be(e,t){let l=W(),{id:p=`headlessui-disclosure-panel-${l}`,transition:a=!1,...c}=e,[n,o]=M("Disclosure.Panel"),{close:r}=J("Disclosure.Panel"),[f,s]=Z(null),E=L(t,P(b=>{ce(()=>o({type:5,element:b}))}),s);w(()=>(o({type:3,panelId:p}),()=>{o({type:3,panelId:null})}),[p,o]);let m=ie(),[D,S]=le(a,f,m!==null?(m&R.Open)===R.Open:n.disclosureState===0),u=C(()=>({open:n.disclosureState===0,close:r}),[n.disclosureState,r]),d={ref:E,id:p,...oe(S)},T=v();return y.createElement(ue,null,y.createElement(H.Provider,{value:n.panelId},T({ourProps:d,theirProps:c,slot:u,defaultTag:ge,features:Ae,visible:D,name:"Disclosure.Panel"})))}let Ce=O(Pe),Re=O(Se),Ie=O(be),Ve=Object.assign(Ce,{Button:Re,Panel:Ie});export{Ve as Disclosure,Re as DisclosureButton,Ie as DisclosurePanel};
