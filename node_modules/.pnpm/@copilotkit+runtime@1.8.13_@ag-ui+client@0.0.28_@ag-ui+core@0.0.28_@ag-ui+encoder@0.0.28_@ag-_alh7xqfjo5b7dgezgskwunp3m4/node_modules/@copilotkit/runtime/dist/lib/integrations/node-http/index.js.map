{"version": 3, "sources": ["../../../../package.json", "../../../../src/lib/integrations/node-http/index.ts", "../../../../src/lib/integrations/shared.ts", "../../../../src/graphql/resolvers/copilot.resolver.ts", "../../../../src/graphql/inputs/generate-copilot-response.input.ts", "../../../../src/graphql/inputs/message.input.ts", "../../../../src/graphql/types/enums.ts", "../../../../src/graphql/types/base/index.ts", "../../../../src/graphql/inputs/frontend.input.ts", "../../../../src/graphql/inputs/action.input.ts", "../../../../src/graphql/inputs/cloud.input.ts", "../../../../src/graphql/inputs/cloud-guardrails.input.ts", "../../../../src/graphql/inputs/forwarded-parameters.input.ts", "../../../../src/graphql/inputs/agent-session.input.ts", "../../../../src/graphql/inputs/agent-state.input.ts", "../../../../src/graphql/inputs/extensions.input.ts", "../../../../src/graphql/inputs/meta-event.input.ts", "../../../../src/graphql/types/meta-events.type.ts", "../../../../src/graphql/types/copilot-response.type.ts", "../../../../src/graphql/types/message-status.type.ts", "../../../../src/graphql/types/response-status.type.ts", "../../../../src/graphql/types/extensions-response.type.ts", "../../../../src/service-adapters/events.ts", "../../../../src/lib/telemetry-client.ts", "../../../../src/lib/runtime/remote-actions.ts", "../../../../src/lib/runtime/copilot-runtime.ts", "../../../../src/graphql/types/converted/index.ts", "../../../../src/utils/failed-response-status-reasons.ts", "../../../../src/graphql/types/agents-response.type.ts", "../../../../src/lib/logger.ts", "../../../../src/graphql/resolvers/state.resolver.ts", "../../../../src/graphql/types/load-agent-state-response.type.ts", "../../../../src/graphql/inputs/load-agent-state.input.ts"], "sourcesContent": ["{\n  \"name\": \"@copilotkit/runtime\",\n  \"private\": false,\n  \"homepage\": \"https://github.com/CopilotKit/CopilotKit\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/CopilotKit/CopilotKit.git\"\n  },\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"version\": \"1.8.13\",\n  \"sideEffects\": false,\n  \"main\": \"./dist/index.js\",\n  \"module\": \"./dist/index.mjs\",\n  \"exports\": {\n    \".\": \"./dist/index.js\"\n  },\n  \"types\": \"./dist/index.d.ts\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"build\": \"tsup --onSuccess \\\"pnpm run generate-graphql-schema\\\"\",\n    \"dev\": \"tsup --watch --onSuccess \\\"pnpm run generate-graphql-schema\\\"\",\n    \"test\": \"jest --passWithNoTests\",\n    \"check-types\": \"tsc --noEmit\",\n    \"clean\": \"rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next && rm -rf __snapshots__\",\n    \"generate-graphql-schema\": \"rm -rf __snapshots__ && ts-node ./scripts/generate-gql-schema.ts\",\n    \"link:global\": \"pnpm link --global\",\n    \"unlink:global\": \"pnpm unlink --global\"\n  },\n  \"devDependencies\": {\n    \"@jest/globals\": \"^29.7.0\",\n    \"@swc/core\": \"1.5.28\",\n    \"@types/express\": \"^4.17.21\",\n    \"@types/jest\": \"^29.5.12\",\n    \"@types/node\": \"^18.11.17\",\n    \"@whatwg-node/server\": \"^0.9.34\",\n    \"eslint\": \"^8.56.0\",\n    \"eslint-config-custom\": \"workspace:*\",\n    \"jest\": \"^29.6.4\",\n    \"nodemon\": \"^3.1.3\",\n    \"ts-jest\": \"^29.1.1\",\n    \"ts-node\": \"^10.9.2\",\n    \"tsconfig\": \"workspace:*\",\n    \"tsup\": \"^6.7.0\",\n    \"typescript\": \"^5.2.3\",\n    \"zod-to-json-schema\": \"^3.23.5\"\n  },\n  \"dependencies\": {\n    \"@ag-ui/client\": \"0.0.28\",\n    \"@ag-ui/core\": \"0.0.28\",\n    \"@ag-ui/encoder\": \"0.0.28\",\n    \"@ag-ui/proto\": \"0.0.28\",\n    \"@anthropic-ai/sdk\": \"^0.27.3\",\n    \"@copilotkit/shared\": \"workspace:*\",\n    \"@graphql-yoga/plugin-defer-stream\": \"^3.3.1\",\n    \"@langchain/community\": \"^0.3.29\",\n    \"@langchain/core\": \"^0.3.38\",\n    \"@langchain/google-gauth\": \"^0.1.0\",\n    \"@langchain/langgraph-sdk\": \"^0.0.70\",\n    \"@langchain/openai\": \"^0.4.2\",\n    \"class-transformer\": \"^0.5.1\",\n    \"class-validator\": \"^0.14.1\",\n    \"express\": \"^4.19.2\",\n    \"graphql\": \"^16.8.1\",\n    \"graphql-scalars\": \"^1.23.0\",\n    \"graphql-yoga\": \"^5.3.1\",\n    \"groq-sdk\": \"^0.5.0\",\n    \"langchain\": \"^0.3.3\",\n    \"openai\": \"^4.85.1\",\n    \"partial-json\": \"^0.1.7\",\n    \"pino\": \"^9.2.0\",\n    \"pino-pretty\": \"^11.2.1\",\n    \"reflect-metadata\": \"^0.2.2\",\n    \"rxjs\": \"^7.8.1\",\n    \"type-graphql\": \"2.0.0-rc.1\",\n    \"zod\": \"^3.23.3\"\n  },\n  \"peerDependencies\": {\n    \"@ag-ui/client\": \">=0.0.28\",\n    \"@ag-ui/core\": \">=0.0.28\",\n    \"@ag-ui/encoder\": \">=0.0.28\",\n    \"@ag-ui/proto\": \">=0.0.28\"\n  },\n  \"keywords\": [\n    \"copilotkit\",\n    \"copilot\",\n    \"react\",\n    \"nextjs\",\n    \"nodejs\",\n    \"ai\",\n    \"assistant\",\n    \"javascript\",\n    \"automation\",\n    \"textarea\"\n  ]\n}\n", "import { createYoga } from \"graphql-yoga\";\nimport { CreateCopilotRuntimeServerOptions, getCommonConfig } from \"../shared\";\nimport telemetry, { getRuntimeInstanceTelemetryInfo } from \"../../telemetry-client\";\n\nexport function copilotRuntimeNodeHttpEndpoint(options: CreateCopilotRuntimeServerOptions) {\n  const commonConfig = getCommonConfig(options);\n\n  telemetry.setGlobalProperties({\n    runtime: {\n      framework: \"node-http\",\n    },\n  });\n\n  if (options.properties?._copilotkit) {\n    telemetry.setGlobalProperties({\n      _copilotkit: options.properties._copilotkit,\n    });\n  }\n\n  telemetry.capture(\n    \"oss.runtime.instance_created\",\n    getRuntimeInstanceTelemetryInfo(options.runtime),\n  );\n\n  const logger = commonConfig.logging;\n  logger.debug(\"Creating Node HTTP endpoint\");\n\n  const yoga = createYoga({\n    ...commonConfig,\n    graphqlEndpoint: options.endpoint,\n  });\n\n  return yoga;\n}\n", "import { YogaInitialContext } from \"graphql-yoga\";\nimport { buildSchemaSync } from \"type-graphql\";\nimport { CopilotResolver } from \"../../graphql/resolvers/copilot.resolver\";\nimport { useDeferStream } from \"@graphql-yoga/plugin-defer-stream\";\nimport { CopilotRuntime } from \"../runtime/copilot-runtime\";\nimport { CopilotServiceAdapter } from \"../../service-adapters\";\nimport { CopilotCloudOptions } from \"../cloud\";\nimport { LogLevel, createLogger } from \"../../lib/logger\";\nimport { createYoga } from \"graphql-yoga\";\nimport telemetry from \"../telemetry-client\";\nimport { StateResolver } from \"../../graphql/resolvers/state.resolver\";\nimport * as packageJson from \"../../../package.json\";\n\nconst logger = createLogger();\n\nexport const addCustomHeaderPlugin = {\n  onResponse({ response }) {\n    // Set your custom header; adjust the header name and value as needed\n    response.headers.set(\"X-CopilotKit-Runtime-Version\", packageJson.version);\n  },\n};\n\ntype AnyPrimitive = string | boolean | number | null;\nexport type CopilotRequestContextProperties = Record<\n  string,\n  AnyPrimitive | Record<string, AnyPrimitive>\n>;\n\nexport type GraphQLContext = YogaInitialContext & {\n  _copilotkit: CreateCopilotRuntimeServerOptions;\n  properties: CopilotRequestContextProperties;\n  logger: typeof logger;\n};\n\nexport interface CreateCopilotRuntimeServerOptions {\n  runtime: CopilotRuntime<any>;\n  serviceAdapter: CopilotServiceAdapter;\n  endpoint: string;\n  baseUrl?: string;\n  cloud?: CopilotCloudOptions;\n  properties?: CopilotRequestContextProperties;\n  logLevel?: LogLevel;\n}\n\nexport async function createContext(\n  initialContext: YogaInitialContext,\n  copilotKitContext: CreateCopilotRuntimeServerOptions,\n  contextLogger: typeof logger,\n  properties: CopilotRequestContextProperties = {},\n): Promise<Partial<GraphQLContext>> {\n  logger.debug({ copilotKitContext }, \"Creating GraphQL context\");\n  const ctx: GraphQLContext = {\n    ...initialContext,\n    _copilotkit: {\n      ...copilotKitContext,\n    },\n    properties: { ...properties },\n    logger: contextLogger,\n  };\n  return ctx;\n}\n\nexport function buildSchema(\n  options: {\n    emitSchemaFile?: string;\n  } = {},\n) {\n  logger.debug(\"Building GraphQL schema...\");\n  const schema = buildSchemaSync({\n    resolvers: [CopilotResolver, StateResolver],\n    emitSchemaFile: options.emitSchemaFile,\n  });\n  logger.debug(\"GraphQL schema built successfully\");\n  return schema;\n}\n\nexport type CommonConfig = {\n  logging: typeof logger;\n  schema: ReturnType<typeof buildSchema>;\n  plugins: Parameters<typeof createYoga>[0][\"plugins\"];\n  context: (ctx: YogaInitialContext) => Promise<Partial<GraphQLContext>>;\n};\n\nexport function getCommonConfig(options: CreateCopilotRuntimeServerOptions): CommonConfig {\n  const logLevel = (process.env.LOG_LEVEL as LogLevel) || (options.logLevel as LogLevel) || \"error\";\n  const logger = createLogger({ level: logLevel, component: \"getCommonConfig\" });\n\n  const contextLogger = createLogger({ level: logLevel });\n\n  if (options.cloud) {\n    telemetry.setCloudConfiguration({\n      publicApiKey: options.cloud.publicApiKey,\n      baseUrl: options.cloud.baseUrl,\n    });\n  }\n\n  if (options.properties?._copilotkit) {\n    telemetry.setGlobalProperties({\n      _copilotkit: {\n        ...(options.properties._copilotkit as Record<string, any>),\n      },\n    });\n  }\n\n  telemetry.setGlobalProperties({\n    runtime: {\n      serviceAdapter: options.serviceAdapter.constructor.name,\n    },\n  });\n\n  return {\n    logging: createLogger({ component: \"Yoga GraphQL\", level: logLevel }),\n    schema: buildSchema(),\n    plugins: [useDeferStream(), addCustomHeaderPlugin],\n    context: (ctx: YogaInitialContext): Promise<Partial<GraphQLContext>> =>\n      createContext(ctx, options, contextLogger, options.properties),\n  };\n}\n", "import { Arg, Ctx, Mutation, Query, Resolver } from \"type-graphql\";\nimport {\n  ReplaySubject,\n  Subject,\n  Subscription,\n  filter,\n  finalize,\n  firstValueFrom,\n  shareReplay,\n  skipWhile,\n  take,\n  takeWhile,\n  tap,\n} from \"rxjs\";\nimport { GenerateCopilotResponseInput } from \"../inputs/generate-copilot-response.input\";\nimport { CopilotResponse } from \"../types/copilot-response.type\";\nimport {\n  CopilotKitLangGraphInterruptEvent,\n  LangGraphInterruptEvent,\n} from \"../types/meta-events.type\";\nimport { ActionInputAvailability, MessageRole } from \"../types/enums\";\nimport { Repeater } from \"graphql-yoga\";\nimport type { CopilotRequestContextProperties, GraphQLContext } from \"../../lib/integrations\";\nimport {\n  RuntimeEvent,\n  RuntimeEventTypes,\n  RuntimeMetaEventName,\n} from \"../../service-adapters/events\";\nimport {\n  FailedMessageStatus,\n  MessageStatusCode,\n  MessageStatusUnion,\n  SuccessMessageStatus,\n} from \"../types/message-status.type\";\nimport { ResponseStatusUnion, SuccessResponseStatus } from \"../types/response-status.type\";\nimport { GraphQLJSONObject } from \"graphql-scalars\";\nimport { plainToInstance } from \"class-transformer\";\nimport { GuardrailsResult } from \"../types/guardrails-result.type\";\nimport { GraphQLError } from \"graphql\";\nimport {\n  GuardrailsValidationFailureResponse,\n  MessageStreamInterruptedResponse,\n  UnknownErrorResponse,\n} from \"../../utils\";\nimport {\n  ActionExecutionMessage,\n  AgentStateMessage,\n  Message,\n  MessageType,\n  ResultMessage,\n  TextMessage,\n} from \"../types/converted\";\nimport telemetry from \"../../lib/telemetry-client\";\nimport { randomId } from \"@copilotkit/shared\";\nimport { AgentsResponse } from \"../types/agents-response.type\";\n\nconst invokeGuardrails = async ({\n  baseUrl,\n  copilotCloudPublicApiKey,\n  data,\n  onResult,\n  onError,\n}: {\n  baseUrl: string;\n  copilotCloudPublicApiKey: string;\n  data: GenerateCopilotResponseInput;\n  onResult: (result: GuardrailsResult) => void;\n  onError: (err: Error) => void;\n}) => {\n  if (\n    data.messages.length &&\n    data.messages[data.messages.length - 1].textMessage?.role === MessageRole.user\n  ) {\n    const messages = data.messages\n      .filter(\n        (m) =>\n          m.textMessage !== undefined &&\n          (m.textMessage.role === MessageRole.user || m.textMessage.role === MessageRole.assistant),\n      )\n      .map((m) => ({\n        role: m.textMessage!.role,\n        content: m.textMessage.content,\n      }));\n\n    const lastMessage = messages[messages.length - 1];\n    const restOfMessages = messages.slice(0, -1);\n\n    const body = {\n      input: lastMessage.content,\n      validTopics: data.cloud.guardrails.inputValidationRules.allowList,\n      invalidTopics: data.cloud.guardrails.inputValidationRules.denyList,\n      messages: restOfMessages,\n    };\n\n    const guardrailsResult = await fetch(`${baseUrl}/guardrails/validate`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"X-CopilotCloud-Public-API-Key\": copilotCloudPublicApiKey,\n      },\n      body: JSON.stringify(body),\n    });\n\n    if (guardrailsResult.ok) {\n      const resultJson: GuardrailsResult = await guardrailsResult.json();\n      onResult(resultJson);\n    } else {\n      onError(await guardrailsResult.json());\n    }\n  }\n};\n\n@Resolver(() => CopilotResponse)\nexport class CopilotResolver {\n  @Query(() => String)\n  async hello() {\n    return \"Hello World\";\n  }\n\n  @Query(() => AgentsResponse)\n  async availableAgents(@Ctx() ctx: GraphQLContext) {\n    let logger = ctx.logger.child({ component: \"CopilotResolver.availableAgents\" });\n\n    logger.debug(\"Processing\");\n    const agentsWithEndpoints = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);\n\n    logger.debug(\"Event source created, creating response\");\n\n    return {\n      agents: agentsWithEndpoints.map(\n        ({ endpoint, ...agentWithoutEndpoint }) => agentWithoutEndpoint,\n      ),\n    };\n  }\n\n  @Mutation(() => CopilotResponse)\n  async generateCopilotResponse(\n    @Ctx() ctx: GraphQLContext,\n    @Arg(\"data\") data: GenerateCopilotResponseInput,\n    @Arg(\"properties\", () => GraphQLJSONObject, { nullable: true })\n    properties?: CopilotRequestContextProperties,\n  ) {\n    telemetry.capture(\"oss.runtime.copilot_request_created\", {\n      \"cloud.guardrails.enabled\": data.cloud?.guardrails !== undefined,\n      requestType: data.metadata.requestType,\n    });\n\n    let logger = ctx.logger.child({ component: \"CopilotResolver.generateCopilotResponse\" });\n    logger.debug({ data }, \"Generating Copilot response\");\n\n    if (properties) {\n      logger.debug(\"Properties provided, merging with context properties\");\n      ctx.properties = { ...ctx.properties, ...properties };\n    }\n\n    const copilotRuntime = ctx._copilotkit.runtime;\n    const serviceAdapter = ctx._copilotkit.serviceAdapter;\n\n    let copilotCloudPublicApiKey: string | null = null;\n    let copilotCloudBaseUrl: string;\n\n    if (data.cloud) {\n      logger = logger.child({ cloud: true });\n      logger.debug(\"Cloud configuration provided, checking for public API key in headers\");\n      const key = ctx.request.headers.get(\"x-copilotcloud-public-api-key\");\n      if (key) {\n        logger.debug(\"Public API key found in headers\");\n        copilotCloudPublicApiKey = key;\n      } else {\n        logger.error(\"Public API key not found in headers\");\n        throw new GraphQLError(\"X-CopilotCloud-Public-API-Key header is required\");\n      }\n\n      if (process.env.COPILOT_CLOUD_BASE_URL) {\n        copilotCloudBaseUrl = process.env.COPILOT_CLOUD_BASE_URL;\n      } else if (ctx._copilotkit.cloud?.baseUrl) {\n        copilotCloudBaseUrl = ctx._copilotkit.cloud?.baseUrl;\n      } else {\n        copilotCloudBaseUrl = \"https://api.cloud.copilotkit.ai\";\n      }\n\n      logger = logger.child({ copilotCloudBaseUrl });\n    }\n\n    logger.debug(\"Setting up subjects\");\n    const responseStatus$ = new ReplaySubject<typeof ResponseStatusUnion>();\n    const interruptStreaming$ = new ReplaySubject<{ reason: string; messageId?: string }>();\n    const guardrailsResult$ = new ReplaySubject<GuardrailsResult>();\n\n    let outputMessages: Message[] = [];\n    let resolveOutputMessagesPromise: (messages: Message[]) => void;\n    let rejectOutputMessagesPromise: (err: Error) => void;\n\n    const outputMessagesPromise = new Promise<Message[]>((resolve, reject) => {\n      resolveOutputMessagesPromise = resolve;\n      rejectOutputMessagesPromise = reject;\n    });\n\n    if (copilotCloudPublicApiKey) {\n      ctx.properties[\"copilotCloudPublicApiKey\"] = copilotCloudPublicApiKey;\n    }\n\n    logger.debug(\"Processing\");\n    const {\n      eventSource,\n      threadId = randomId(),\n      runId,\n      serverSideActions,\n      actionInputsWithoutAgents,\n      extensions,\n    } = await copilotRuntime.processRuntimeRequest({\n      serviceAdapter,\n      messages: data.messages,\n      actions: data.frontend.actions.filter(\n        (action) => action.available !== ActionInputAvailability.disabled,\n      ),\n      threadId: data.threadId,\n      runId: data.runId,\n      publicApiKey: copilotCloudPublicApiKey,\n      outputMessagesPromise,\n      graphqlContext: ctx,\n      forwardedParameters: data.forwardedParameters,\n      agentSession: data.agentSession,\n      agentStates: data.agentStates,\n      url: data.frontend.url,\n      extensions: data.extensions,\n      metaEvents: data.metaEvents,\n    });\n\n    logger.debug(\"Event source created, creating response\");\n    // run and process the event stream\n    const eventStream = eventSource\n      .processRuntimeEvents({\n        serverSideActions,\n        guardrailsResult$: data.cloud?.guardrails ? guardrailsResult$ : null,\n        actionInputsWithoutAgents: actionInputsWithoutAgents.filter(\n          // TODO-AGENTS: do not exclude ALL server side actions\n          (action) =>\n            !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name),\n        ),\n        threadId,\n      })\n      .pipe(\n        // shareReplay() ensures that later subscribers will see the whole stream instead of\n        // just the events that were emitted after the subscriber was added.\n        shareReplay(),\n        finalize(() => {\n          logger.debug(\"Event stream finalized\");\n        }),\n      );\n\n    const response = {\n      threadId,\n      runId,\n      status: firstValueFrom(responseStatus$),\n      extensions,\n      metaEvents: new Repeater(async (push, stop) => {\n        let eventStreamSubscription: Subscription;\n\n        eventStreamSubscription = eventStream.subscribe({\n          next: async (event) => {\n            if (event.type != RuntimeEventTypes.MetaEvent) {\n              return;\n            }\n            switch (event.name) {\n              case RuntimeMetaEventName.LangGraphInterruptEvent:\n                push(\n                  plainToInstance(LangGraphInterruptEvent, {\n                    type: event.type,\n                    name: event.name,\n                    value: event.value,\n                  }),\n                );\n                break;\n              case RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent:\n                push(\n                  plainToInstance(CopilotKitLangGraphInterruptEvent, {\n                    type: event.type,\n                    name: event.name,\n                    data: {\n                      value: event.data.value,\n                      messages: event.data.messages.map((message) => {\n                        if (\n                          message.type === \"TextMessage\" ||\n                          (\"content\" in message && \"role\" in message)\n                        ) {\n                          return plainToInstance(TextMessage, {\n                            id: message.id,\n                            createdAt: new Date(),\n                            content: [(message as TextMessage).content],\n                            role: (message as TextMessage).role,\n                            status: new SuccessMessageStatus(),\n                          });\n                        }\n                        if (\"arguments\" in message) {\n                          return plainToInstance(ActionExecutionMessage, {\n                            name: message.name,\n                            id: message.id,\n                            arguments: [JSON.stringify(message.arguments)],\n                            createdAt: new Date(),\n                            status: new SuccessMessageStatus(),\n                          });\n                        }\n                        throw new Error(\"Unknown message in metaEvents copilot resolver\");\n                      }),\n                    },\n                  }),\n                );\n                break;\n            }\n          },\n          error: (err) => {\n            logger.error({ err }, \"Error in meta events stream\");\n            responseStatus$.next(\n              new UnknownErrorResponse({\n                description: `An unknown error has occurred in the event stream`,\n              }),\n            );\n            eventStreamSubscription?.unsubscribe();\n            stop();\n          },\n          complete: async () => {\n            logger.debug(\"Meta events stream completed\");\n            responseStatus$.next(new SuccessResponseStatus());\n            eventStreamSubscription?.unsubscribe();\n            stop();\n          },\n        });\n      }),\n      messages: new Repeater(async (pushMessage, stopStreamingMessages) => {\n        logger.debug(\"Messages repeater created\");\n\n        if (data.cloud?.guardrails) {\n          logger = logger.child({ guardrails: true });\n          logger.debug(\"Guardrails is enabled, validating input\");\n\n          invokeGuardrails({\n            baseUrl: copilotCloudBaseUrl,\n            copilotCloudPublicApiKey,\n            data,\n            onResult: (result) => {\n              logger.debug({ status: result.status }, \"Guardrails validation done\");\n              guardrailsResult$.next(result);\n\n              // Guardrails validation failed\n              if (result.status === \"denied\") {\n                // send the reason to the client and interrupt streaming\n                responseStatus$.next(\n                  new GuardrailsValidationFailureResponse({ guardrailsReason: result.reason }),\n                );\n                interruptStreaming$.next({\n                  reason: `Interrupted due to Guardrails validation failure. Reason: ${result.reason}`,\n                });\n\n                // resolve messages promise to the middleware\n                outputMessages = [\n                  plainToInstance(TextMessage, {\n                    id: randomId(),\n                    createdAt: new Date(),\n                    content: result.reason,\n                    role: MessageRole.assistant,\n                  }),\n                ];\n                resolveOutputMessagesPromise(outputMessages);\n              }\n            },\n            onError: (err) => {\n              logger.error({ err }, \"Error in guardrails validation\");\n              responseStatus$.next(\n                new UnknownErrorResponse({\n                  description: `An unknown error has occurred in the guardrails validation`,\n                }),\n              );\n              interruptStreaming$.next({\n                reason: `Interrupted due to unknown error in guardrails validation`,\n              });\n\n              // reject the middleware promise\n              rejectOutputMessagesPromise(err);\n            },\n          });\n        }\n\n        let eventStreamSubscription: Subscription;\n\n        logger.debug(\"Event stream created, subscribing to event stream\");\n\n        eventStreamSubscription = eventStream.subscribe({\n          next: async (event) => {\n            switch (event.type) {\n              case RuntimeEventTypes.MetaEvent:\n                break;\n              ////////////////////////////////\n              // TextMessageStart\n              ////////////////////////////////\n              case RuntimeEventTypes.TextMessageStart:\n                // create a sub stream that contains the message content\n                const textMessageContentStream = eventStream.pipe(\n                  // skip until this message start event\n                  skipWhile((e) => e !== event),\n                  // take until the message end event\n                  takeWhile(\n                    (e) =>\n                      !(\n                        e.type === RuntimeEventTypes.TextMessageEnd &&\n                        e.messageId == event.messageId\n                      ),\n                  ),\n                  // filter out any other message events or message ids\n                  filter(\n                    (e) =>\n                      e.type == RuntimeEventTypes.TextMessageContent &&\n                      e.messageId == event.messageId,\n                  ),\n                );\n\n                // signal when we are done streaming\n                const streamingTextStatus = new Subject<typeof MessageStatusUnion>();\n\n                const messageId = event.messageId;\n                // push the new message\n                pushMessage({\n                  id: messageId,\n                  parentMessageId: event.parentMessageId,\n                  status: firstValueFrom(streamingTextStatus),\n                  createdAt: new Date(),\n                  role: MessageRole.assistant,\n                  content: new Repeater(async (pushTextChunk, stopStreamingText) => {\n                    logger.debug(\"Text message content repeater created\");\n\n                    const textChunks: string[] = [];\n                    let textSubscription: Subscription;\n\n                    interruptStreaming$\n                      .pipe(\n                        shareReplay(),\n                        take(1),\n                        tap(({ reason, messageId }) => {\n                          logger.debug({ reason, messageId }, \"Text streaming interrupted\");\n\n                          streamingTextStatus.next(\n                            plainToInstance(FailedMessageStatus, { reason }),\n                          );\n\n                          responseStatus$.next(new MessageStreamInterruptedResponse({ messageId }));\n                          stopStreamingText();\n                          textSubscription?.unsubscribe();\n                        }),\n                      )\n                      .subscribe();\n\n                    logger.debug(\"Subscribing to text message content stream\");\n\n                    textSubscription = textMessageContentStream.subscribe({\n                      next: async (e: RuntimeEvent) => {\n                        if (e.type == RuntimeEventTypes.TextMessageContent) {\n                          await pushTextChunk(e.content);\n                          textChunks.push(e.content);\n                        }\n                      },\n                      error: (err) => {\n                        logger.error({ err }, \"Error in text message content stream\");\n                        interruptStreaming$.next({\n                          reason: \"Error streaming message content\",\n                          messageId,\n                        });\n                        stopStreamingText();\n                        textSubscription?.unsubscribe();\n                      },\n                      complete: () => {\n                        logger.debug(\"Text message content stream completed\");\n                        streamingTextStatus.next(new SuccessMessageStatus());\n                        stopStreamingText();\n                        textSubscription?.unsubscribe();\n\n                        outputMessages.push(\n                          plainToInstance(TextMessage, {\n                            id: messageId,\n                            createdAt: new Date(),\n                            content: textChunks.join(\"\"),\n                            role: MessageRole.assistant,\n                          }),\n                        );\n                      },\n                    });\n                  }),\n                });\n                break;\n              ////////////////////////////////\n              // ActionExecutionStart\n              ////////////////////////////////\n              case RuntimeEventTypes.ActionExecutionStart:\n                logger.debug(\"Action execution start event received\");\n                const actionExecutionArgumentStream = eventStream.pipe(\n                  skipWhile((e) => e !== event),\n                  // take until the action execution end event\n                  takeWhile(\n                    (e) =>\n                      !(\n                        e.type === RuntimeEventTypes.ActionExecutionEnd &&\n                        e.actionExecutionId == event.actionExecutionId\n                      ),\n                  ),\n                  // filter out any other action execution events or action execution ids\n                  filter(\n                    (e) =>\n                      e.type == RuntimeEventTypes.ActionExecutionArgs &&\n                      e.actionExecutionId == event.actionExecutionId,\n                  ),\n                );\n                const streamingArgumentsStatus = new Subject<typeof MessageStatusUnion>();\n                pushMessage({\n                  id: event.actionExecutionId,\n                  parentMessageId: event.parentMessageId,\n                  status: firstValueFrom(streamingArgumentsStatus),\n                  createdAt: new Date(),\n                  name: event.actionName,\n                  arguments: new Repeater(async (pushArgumentsChunk, stopStreamingArguments) => {\n                    logger.debug(\"Action execution argument stream created\");\n\n                    const argumentChunks: string[] = [];\n                    let actionExecutionArgumentSubscription: Subscription;\n\n                    actionExecutionArgumentSubscription = actionExecutionArgumentStream.subscribe({\n                      next: async (e: RuntimeEvent) => {\n                        if (e.type == RuntimeEventTypes.ActionExecutionArgs) {\n                          await pushArgumentsChunk(e.args);\n                          argumentChunks.push(e.args);\n                        }\n                      },\n                      error: (err) => {\n                        logger.error({ err }, \"Error in action execution argument stream\");\n                        streamingArgumentsStatus.next(\n                          plainToInstance(FailedMessageStatus, {\n                            reason:\n                              \"An unknown error has occurred in the action execution argument stream\",\n                          }),\n                        );\n                        stopStreamingArguments();\n                        actionExecutionArgumentSubscription?.unsubscribe();\n                      },\n                      complete: () => {\n                        logger.debug(\"Action execution argument stream completed\");\n                        streamingArgumentsStatus.next(new SuccessMessageStatus());\n                        stopStreamingArguments();\n                        actionExecutionArgumentSubscription?.unsubscribe();\n\n                        outputMessages.push(\n                          plainToInstance(ActionExecutionMessage, {\n                            id: event.actionExecutionId,\n                            createdAt: new Date(),\n                            name: event.actionName,\n                            arguments: argumentChunks.join(\"\"),\n                          }),\n                        );\n                      },\n                    });\n                  }),\n                });\n                break;\n              ////////////////////////////////\n              // ActionExecutionResult\n              ////////////////////////////////\n              case RuntimeEventTypes.ActionExecutionResult:\n                logger.debug({ result: event.result }, \"Action execution result event received\");\n                pushMessage({\n                  id: \"result-\" + event.actionExecutionId,\n                  status: new SuccessMessageStatus(),\n                  createdAt: new Date(),\n                  actionExecutionId: event.actionExecutionId,\n                  actionName: event.actionName,\n                  result: event.result,\n                });\n\n                outputMessages.push(\n                  plainToInstance(ResultMessage, {\n                    id: \"result-\" + event.actionExecutionId,\n                    createdAt: new Date(),\n                    actionExecutionId: event.actionExecutionId,\n                    actionName: event.actionName,\n                    result: event.result,\n                  }),\n                );\n                break;\n              ////////////////////////////////\n              // AgentStateMessage\n              ////////////////////////////////\n              case RuntimeEventTypes.AgentStateMessage:\n                logger.debug({ event }, \"Agent message event received\");\n                pushMessage({\n                  id: randomId(),\n                  status: new SuccessMessageStatus(),\n                  threadId: event.threadId,\n                  agentName: event.agentName,\n                  nodeName: event.nodeName,\n                  runId: event.runId,\n                  active: event.active,\n                  state: event.state,\n                  running: event.running,\n                  role: MessageRole.assistant,\n                  createdAt: new Date(),\n                });\n                outputMessages.push(\n                  plainToInstance(AgentStateMessage, {\n                    id: randomId(),\n                    threadId: event.threadId,\n                    agentName: event.agentName,\n                    nodeName: event.nodeName,\n                    runId: event.runId,\n                    active: event.active,\n                    state: event.state,\n                    running: event.running,\n                    role: MessageRole.assistant,\n                    createdAt: new Date(),\n                  }),\n                );\n                break;\n            }\n          },\n          error: (err) => {\n            logger.error({ err }, \"Error in event stream\");\n            responseStatus$.next(\n              new UnknownErrorResponse({\n                description: `An unknown error has occurred in the event stream`,\n              }),\n            );\n            eventStreamSubscription?.unsubscribe();\n            stopStreamingMessages();\n\n            rejectOutputMessagesPromise(err);\n          },\n          complete: async () => {\n            logger.debug(\"Event stream completed\");\n            if (data.cloud?.guardrails) {\n              logger.debug(\"Guardrails is enabled, waiting for guardrails result\");\n              await firstValueFrom(guardrailsResult$);\n            }\n            responseStatus$.next(new SuccessResponseStatus());\n            eventStreamSubscription?.unsubscribe();\n            stopStreamingMessages();\n\n            resolveOutputMessagesPromise(outputMessages);\n          },\n        });\n      }),\n    };\n\n    return response;\n  }\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { MessageInput } from \"./message.input\";\nimport { FrontendInput } from \"./frontend.input\";\nimport { CloudInput } from \"./cloud.input\";\nimport { CopilotRequestType } from \"../types/enums\";\nimport { ForwardedParametersInput } from \"./forwarded-parameters.input\";\nimport { AgentSessionInput } from \"./agent-session.input\";\nimport { AgentStateInput } from \"./agent-state.input\";\nimport { ExtensionsInput } from \"./extensions.input\";\nimport { MetaEventInput } from \"./meta-event.input\";\n\n@InputType()\nexport class GenerateCopilotResponseMetadataInput {\n  @Field(() => CopilotRequestType, { nullable: true })\n  requestType: CopilotRequestType;\n}\n\n@InputType()\nexport class GenerateCopilotResponseInput {\n  @Field(() => GenerateCopilotResponseMetadataInput, { nullable: false })\n  metadata: GenerateCopilotResponseMetadataInput;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n\n  @Field(() => String, { nullable: true })\n  runId?: string;\n\n  @Field(() => [MessageInput])\n  messages: MessageInput[];\n\n  @Field(() => FrontendInput)\n  frontend: FrontendInput;\n\n  @Field(() => CloudInput, { nullable: true })\n  cloud?: CloudInput;\n\n  @Field(() => ForwardedParametersInput, { nullable: true })\n  forwardedParameters?: ForwardedParametersInput;\n\n  @Field(() => AgentSessionInput, { nullable: true })\n  agentSession?: AgentSessionInput;\n\n  @Field(() => AgentStateInput, { nullable: true })\n  agentState?: AgentStateInput;\n\n  @Field(() => [AgentStateInput], { nullable: true })\n  agentStates?: AgentStateInput[];\n\n  @Field(() => ExtensionsInput, { nullable: true })\n  extensions?: ExtensionsInput;\n\n  @Field(() => [MetaEventInput], { nullable: true })\n  metaEvents?: MetaEventInput[];\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { MessageRole } from \"../types/enums\";\nimport { BaseMessageInput } from \"../types/base\";\n\n// GraphQL does not support union types in inputs, so we need to use\n// optional fields for the different subtypes.\n@InputType()\nexport class MessageInput extends BaseMessageInput {\n  @Field(() => TextMessageInput, { nullable: true })\n  textMessage?: TextMessageInput;\n\n  @Field(() => ActionExecutionMessageInput, { nullable: true })\n  actionExecutionMessage?: ActionExecutionMessageInput;\n\n  @Field(() => ResultMessageInput, { nullable: true })\n  resultMessage?: ResultMessageInput;\n\n  @Field(() => AgentStateMessageInput, { nullable: true })\n  agentStateMessage?: AgentStateMessageInput;\n\n  @Field(() => ImageMessageInput, { nullable: true })\n  imageMessage?: ImageMessageInput;\n}\n\n@InputType()\nexport class TextMessageInput {\n  @Field(() => String)\n  content: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n}\n\n@InputType()\nexport class ActionExecutionMessageInput {\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String)\n  arguments: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => String, {\n    nullable: true,\n    deprecationReason: \"This field will be removed in a future version\",\n  })\n  scope?: String;\n}\n\n@InputType()\nexport class ResultMessageInput {\n  @Field(() => String)\n  actionExecutionId: string;\n\n  @Field(() => String)\n  actionName: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => String)\n  result: string;\n}\n\n@InputType()\nexport class AgentStateMessageInput {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => Boolean)\n  running: boolean;\n\n  @Field(() => String)\n  nodeName: string;\n\n  @Field(() => String)\n  runId: string;\n\n  @Field(() => Boolean)\n  active: boolean;\n}\n\n@InputType()\nexport class ImageMessageInput {\n  @Field(() => String)\n  format: string;\n\n  @Field(() => String)\n  bytes: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n}\n", "import { registerEnumType } from \"type-graphql\";\n\nexport enum MessageRole {\n  user = \"user\",\n  assistant = \"assistant\",\n  system = \"system\",\n  tool = \"tool\",\n  developer = \"developer\",\n}\n\nexport enum CopilotRequestType {\n  Chat = \"Chat\",\n  Task = \"Task\",\n  TextareaCompletion = \"TextareaCompletion\",\n  TextareaPopover = \"TextareaPopover\",\n  Suggestion = \"Suggestion\",\n}\n\nexport enum ActionInputAvailability {\n  disabled = \"disabled\",\n  enabled = \"enabled\",\n  remote = \"remote\",\n}\n\nregisterEnumType(MessageRole, {\n  name: \"MessageRole\",\n  description: \"The role of the message\",\n});\n\nregisterEnumType(CopilotRequestType, {\n  name: \"CopilotRequestType\",\n  description: \"The type of Copilot request\",\n});\n\nregisterEnumType(ActionInputAvailability, {\n  name: \"ActionInputAvailability\",\n  description: \"The availability of the frontend action\",\n});\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class BaseMessageInput {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => Date)\n  createdAt: Date;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { ActionInput } from \"./action.input\";\n\n@InputType()\nexport class FrontendInput {\n  @Field(() => String, { nullable: true })\n  toDeprecate_fullContext?: string;\n\n  @Field(() => [ActionInput])\n  actions: ActionInput[];\n\n  @Field(() => String, { nullable: true })\n  url?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { ActionInputAvailability } from \"../types/enums\";\n@InputType()\nexport class ActionInput {\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String)\n  description: string;\n\n  @Field(() => String)\n  jsonSchema: string;\n\n  @Field(() => ActionInputAvailability, { nullable: true })\n  available?: ActionInputAvailability;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { GuardrailsInput } from \"./cloud-guardrails.input\";\n\n@InputType()\nexport class CloudInput {\n  @Field(() => GuardrailsInput, { nullable: true })\n  guardrails?: GuardrailsInput;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class GuardrailsRuleInput {\n  @Field(() => [String], { nullable: true })\n  allowList?: string[] = [];\n\n  @Field(() => [String], { nullable: true })\n  denyList?: string[] = [];\n}\n\n@InputType()\nexport class GuardrailsInput {\n  @Field(() => GuardrailsRuleInput, { nullable: false })\n  inputValidationRules: GuardrailsRuleInput;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class ForwardedParametersInput {\n  @Field(() => String, { nullable: true })\n  model?: string;\n\n  @Field(() => Number, { nullable: true })\n  maxTokens?: number;\n\n  @Field(() => [String], { nullable: true })\n  stop?: string[];\n\n  @Field(() => String, { nullable: true })\n  toolChoice?: String;\n\n  @Field(() => String, { nullable: true })\n  toolChoiceFunctionName?: string;\n\n  @Field(() => Number, { nullable: true })\n  temperature?: number;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class AgentSessionInput {\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n\n  @Field(() => String, { nullable: true })\n  nodeName?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class AgentStateInput {\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => String, { nullable: true })\n  config?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n/**\n * The extensions input is used to pass additional information to the copilot runtime, specific to a\n * service adapter or agent framework.\n */\n\n@InputType()\nexport class ExtensionsInput {\n  @Field(() => OpenAIApiAssistantAPIInput, { nullable: true })\n  openaiAssistantAPI?: OpenAIApiAssistantAPIInput;\n}\n\n@InputType()\nexport class OpenAIApiAssistantAPIInput {\n  @Field(() => String, { nullable: true })\n  runId?: string;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { MetaEventName } from \"../types/meta-events.type\";\nimport { MessageInput } from \"./message.input\";\n\n@InputType()\nexport class MetaEventInput {\n  @Field(() => MetaEventName)\n  name: MetaEventName;\n\n  @Field(() => String)\n  value?: string;\n\n  @Field(() => String, { nullable: true })\n  response?: string;\n\n  @Field(() => [MessageInput], { nullable: true })\n  messages?: MessageInput[];\n}\n", "import { createUnionType, Field, InterfaceType, ObjectType, registerEnumType } from \"type-graphql\";\nimport {\n  ActionExecutionMessageOutput,\n  AgentStateMessageOutput,\n  BaseMessageOutput,\n  ResultMessageOutput,\n  TextMessageOutput,\n} from \"./copilot-response.type\";\n\nexport enum MetaEventName {\n  LangGraphInterruptEvent = \"LangGraphInterruptEvent\",\n  CopilotKitLangGraphInterruptEvent = \"CopilotKitLangGraphInterruptEvent\",\n}\n\nregisterEnumType(MetaEventName, {\n  name: \"MetaEventName\",\n  description: \"Meta event types\",\n});\n\n@InterfaceType({\n  resolveType(value) {\n    if (value.name === MetaEventName.LangGraphInterruptEvent) {\n      return LangGraphInterruptEvent;\n    } else if (value.name === MetaEventName.CopilotKitLangGraphInterruptEvent) {\n      return CopilotKitLangGraphInterruptEvent;\n    }\n    return undefined;\n  },\n})\n@InterfaceType()\nexport abstract class BaseMetaEvent {\n  @Field(() => String)\n  type: \"MetaEvent\" = \"MetaEvent\";\n\n  @Field(() => MetaEventName)\n  name: MetaEventName;\n}\n\n@ObjectType()\nexport class CopilotKitLangGraphInterruptEventData {\n  @Field(() => String)\n  value: string;\n\n  @Field(() => [BaseMessageOutput])\n  messages: (typeof BaseMessageOutput)[];\n}\n\n@ObjectType({ implements: BaseMetaEvent })\nexport class LangGraphInterruptEvent {\n  @Field(() => MetaEventName)\n  name: MetaEventName.LangGraphInterruptEvent = MetaEventName.LangGraphInterruptEvent;\n\n  @Field(() => String)\n  value: string;\n\n  @Field(() => String, { nullable: true })\n  response?: string;\n}\n\n@ObjectType({ implements: BaseMetaEvent })\nexport class CopilotKitLangGraphInterruptEvent {\n  @Field(() => MetaEventName)\n  name: MetaEventName.CopilotKitLangGraphInterruptEvent =\n    MetaEventName.CopilotKitLangGraphInterruptEvent;\n\n  @Field(() => CopilotKitLangGraphInterruptEventData)\n  data: CopilotKitLangGraphInterruptEventData;\n\n  @Field(() => String, { nullable: true })\n  response?: string;\n}\n", "import { Field, InterfaceType, ObjectType } from \"type-graphql\";\nimport { MessageRole } from \"./enums\";\nimport { MessageStatusUnion } from \"./message-status.type\";\nimport { ResponseStatusUnion } from \"./response-status.type\";\nimport { ExtensionsResponse } from \"./extensions-response.type\";\nimport { BaseMetaEvent } from \"./meta-events.type\";\n\n@InterfaceType({\n  resolveType(value) {\n    if (value.hasOwnProperty(\"content\")) {\n      return TextMessageOutput;\n    } else if (value.hasOwnProperty(\"name\")) {\n      return ActionExecutionMessageOutput;\n    } else if (value.hasOwnProperty(\"result\")) {\n      return ResultMessageOutput;\n    } else if (value.hasOwnProperty(\"state\")) {\n      return AgentStateMessageOutput;\n    } else if (value.hasOwnProperty(\"format\") && value.hasOwnProperty(\"bytes\")) {\n      return ImageMessageOutput;\n    }\n    return undefined;\n  },\n})\nexport abstract class BaseMessageOutput {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => Date)\n  createdAt: Date;\n\n  @Field(() => MessageStatusUnion)\n  status: typeof MessageStatusUnion;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class TextMessageOutput {\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => [String])\n  content: string[];\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class ActionExecutionMessageOutput {\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String, {\n    nullable: true,\n    deprecationReason: \"This field will be removed in a future version\",\n  })\n  scope?: string;\n\n  @Field(() => [String])\n  arguments: string[];\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class ResultMessageOutput {\n  @Field(() => String)\n  actionExecutionId: string;\n\n  @Field(() => String)\n  actionName: string;\n\n  @Field(() => String)\n  result: string;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class AgentStateMessageOutput {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => String)\n  nodeName: string;\n\n  @Field(() => String)\n  runId: string;\n\n  @Field(() => Boolean)\n  active: boolean;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => Boolean)\n  running: boolean;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class ImageMessageOutput {\n  @Field(() => String)\n  format: string;\n\n  @Field(() => String)\n  bytes: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n}\n\n@ObjectType()\nexport class CopilotResponse {\n  @Field(() => String)\n  threadId!: string;\n\n  @Field(() => ResponseStatusUnion)\n  status: typeof ResponseStatusUnion;\n\n  @Field({ nullable: true })\n  runId?: string;\n\n  @Field(() => [BaseMessageOutput])\n  messages: (typeof BaseMessageOutput)[];\n\n  @Field(() => ExtensionsResponse, { nullable: true })\n  extensions?: ExtensionsResponse;\n\n  @Field(() => [BaseMetaEvent], { nullable: true })\n  metaEvents?: (typeof BaseMetaEvent)[];\n}\n", "import { Field, ObjectType, createUnionType, registerEnumType } from \"type-graphql\";\n\nexport enum MessageStatusCode {\n  Pending = \"pending\",\n  Success = \"success\",\n  Failed = \"failed\",\n}\n\nregisterEnumType(MessageStatusCode, {\n  name: \"MessageStatusCode\",\n});\n\n@ObjectType()\nclass BaseMessageStatus {\n  @Field(() => MessageStatusCode)\n  code: MessageStatusCode;\n}\n\n@ObjectType()\nexport class PendingMessageStatus extends BaseMessageStatus {\n  code: MessageStatusCode = MessageStatusCode.Pending;\n}\n\n@ObjectType()\nexport class SuccessMessageStatus extends BaseMessageStatus {\n  code: MessageStatusCode = MessageStatusCode.Success;\n}\n\n@ObjectType()\nexport class FailedMessageStatus extends BaseMessageStatus {\n  code: MessageStatusCode = MessageStatusCode.Failed;\n\n  @Field(() => String)\n  reason: string;\n}\n\nexport const MessageStatusUnion = createUnionType({\n  name: \"MessageStatus\",\n  types: () => [PendingMessageStatus, SuccessMessageStatus, FailedMessageStatus] as const,\n});\n", "import { <PERSON>rap<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"graphql-scalars\";\nimport { Field, InterfaceType, ObjectType, createUnionType, registerEnumType } from \"type-graphql\";\n\nexport enum ResponseStatusCode {\n  Pending = \"pending\",\n  Success = \"success\",\n  Failed = \"failed\",\n}\n\nregisterEnumType(ResponseStatusCode, {\n  name: \"ResponseStatusCode\",\n});\n\n@InterfaceType({\n  resolveType(value) {\n    if (value.code === ResponseStatusCode.Success) {\n      return SuccessResponseStatus;\n    } else if (value.code === ResponseStatusCode.Failed) {\n      return FailedResponseStatus;\n    } else if (value.code === ResponseStatusCode.Pending) {\n      return PendingResponseStatus;\n    }\n    return undefined;\n  },\n})\n@ObjectType()\nabstract class BaseResponseStatus {\n  @Field(() => ResponseStatusCode)\n  code: ResponseStatusCode;\n}\n\n@ObjectType({ implements: BaseResponseStatus })\nexport class PendingResponseStatus extends BaseResponseStatus {\n  code: ResponseStatusCode = ResponseStatusCode.Pending;\n}\n\n@ObjectType({ implements: BaseResponseStatus })\nexport class SuccessResponseStatus extends BaseResponseStatus {\n  code: ResponseStatusCode = ResponseStatusCode.Success;\n}\n\nexport enum FailedResponseStatusReason {\n  GUARDRAILS_VALIDATION_FAILED = \"GUARDRAILS_VALIDATION_FAILED\",\n  MESSAGE_STREAM_INTERRUPTED = \"MESSAGE_STREAM_INTERRUPTED\",\n  UNKNOWN_ERROR = \"UNKNOWN_ERROR\",\n}\n\nregisterEnumType(FailedResponseStatusReason, {\n  name: \"FailedResponseStatusReason\",\n});\n\n@ObjectType({ implements: BaseResponseStatus })\nexport class FailedResponseStatus extends BaseResponseStatus {\n  code: ResponseStatusCode = ResponseStatusCode.Failed;\n\n  @Field(() => FailedResponseStatusReason)\n  reason: FailedResponseStatusReason;\n\n  @Field(() => GraphQLJSON, { nullable: true })\n  details?: Record<string, any> = null;\n}\n\nexport const ResponseStatusUnion = createUnionType({\n  name: \"ResponseStatus\",\n  types: () => [PendingResponseStatus, SuccessResponseStatus, FailedResponseStatus] as const,\n});\n", "import { Field, ObjectType } from \"type-graphql\";\n\n/**\n * The extensions response is used to receive additional information from the copilot runtime, specific to a\n * service adapter or agent framework.\n *\n * Next time a request to the runtime is made, the extensions response will be included in the request as input.\n */\n\n@ObjectType()\nexport class ExtensionsResponse {\n  @Field(() => OpenAIApiAssistantAPIResponse, { nullable: true })\n  openaiAssistantAPI?: OpenAIApiAssistantAPIResponse;\n}\n\n@ObjectType()\nexport class OpenAIApiAssistantAPIResponse {\n  @Field(() => String, { nullable: true })\n  runId?: string;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n}\n", "import { Action, randomId } from \"@copilotkit/shared\";\nimport {\n  of,\n  concat,\n  scan,\n  concatMap,\n  ReplaySubject,\n  Subject,\n  firstValueFrom,\n  from,\n  catchError,\n  EMPTY,\n} from \"rxjs\";\nimport { streamLangChainResponse } from \"./langchain/utils\";\nimport { GuardrailsResult } from \"../graphql/types/guardrails-result.type\";\nimport telemetry from \"../lib/telemetry-client\";\nimport { isRemoteAgentAction } from \"../lib/runtime/remote-actions\";\nimport { ActionInput } from \"../graphql/inputs/action.input\";\nimport { ActionExecutionMessage, ResultMessage, TextMessage } from \"../graphql/types/converted\";\nimport { plainToInstance } from \"class-transformer\";\n\nexport enum RuntimeEventTypes {\n  TextMessageStart = \"TextMessageStart\",\n  TextMessageContent = \"TextMessageContent\",\n  TextMessageEnd = \"TextMessageEnd\",\n  ActionExecutionStart = \"ActionExecutionStart\",\n  ActionExecutionArgs = \"ActionExecutionArgs\",\n  ActionExecutionEnd = \"ActionExecutionEnd\",\n  ActionExecutionResult = \"ActionExecutionResult\",\n  AgentStateMessage = \"AgentStateMessage\",\n  MetaEvent = \"MetaEvent\",\n}\n\nexport enum RuntimeMetaEventName {\n  LangGraphInterruptEvent = \"LangGraphInterruptEvent\",\n  LangGraphInterruptResumeEvent = \"LangGraphInterruptResumeEvent\",\n  CopilotKitLangGraphInterruptEvent = \"CopilotKitLangGraphInterruptEvent\",\n}\n\nexport type RunTimeMetaEvent =\n  | {\n      type: RuntimeEventTypes.MetaEvent;\n      name: RuntimeMetaEventName.LangGraphInterruptEvent;\n      value: string;\n    }\n  | {\n      type: RuntimeEventTypes.MetaEvent;\n      name: RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent;\n      data: { value: string; messages: (TextMessage | ActionExecutionMessage | ResultMessage)[] };\n    }\n  | {\n      type: RuntimeEventTypes.MetaEvent;\n      name: RuntimeMetaEventName.LangGraphInterruptResumeEvent;\n      data: string;\n    };\n\nexport type RuntimeEvent =\n  | { type: RuntimeEventTypes.TextMessageStart; messageId: string; parentMessageId?: string }\n  | {\n      type: RuntimeEventTypes.TextMessageContent;\n      messageId: string;\n      content: string;\n    }\n  | { type: RuntimeEventTypes.TextMessageEnd; messageId: string }\n  | {\n      type: RuntimeEventTypes.ActionExecutionStart;\n      actionExecutionId: string;\n      actionName: string;\n      parentMessageId?: string;\n    }\n  | { type: RuntimeEventTypes.ActionExecutionArgs; actionExecutionId: string; args: string }\n  | { type: RuntimeEventTypes.ActionExecutionEnd; actionExecutionId: string }\n  | {\n      type: RuntimeEventTypes.ActionExecutionResult;\n      actionName: string;\n      actionExecutionId: string;\n      result: string;\n    }\n  | {\n      type: RuntimeEventTypes.AgentStateMessage;\n      threadId: string;\n      agentName: string;\n      nodeName: string;\n      runId: string;\n      active: boolean;\n      role: string;\n      state: string;\n      running: boolean;\n    }\n  | RunTimeMetaEvent;\n\ninterface RuntimeEventWithState {\n  event: RuntimeEvent | null;\n  callActionServerSide: boolean;\n  action: Action<any> | null;\n  actionExecutionId: string | null;\n  args: string;\n  actionExecutionParentMessageId: string | null;\n}\n\ntype EventSourceCallback = (eventStream$: RuntimeEventSubject) => Promise<void>;\n\nexport class RuntimeEventSubject extends ReplaySubject<RuntimeEvent> {\n  constructor() {\n    super();\n  }\n\n  sendTextMessageStart({\n    messageId,\n    parentMessageId,\n  }: {\n    messageId: string;\n    parentMessageId?: string;\n  }) {\n    this.next({ type: RuntimeEventTypes.TextMessageStart, messageId, parentMessageId });\n  }\n\n  sendTextMessageContent({ messageId, content }: { messageId: string; content: string }) {\n    this.next({ type: RuntimeEventTypes.TextMessageContent, content, messageId });\n  }\n\n  sendTextMessageEnd({ messageId }: { messageId: string }) {\n    this.next({ type: RuntimeEventTypes.TextMessageEnd, messageId });\n  }\n\n  sendTextMessage(messageId: string, content: string) {\n    this.sendTextMessageStart({ messageId });\n    this.sendTextMessageContent({ messageId, content });\n    this.sendTextMessageEnd({ messageId });\n  }\n\n  sendActionExecutionStart({\n    actionExecutionId,\n    actionName,\n    parentMessageId,\n  }: {\n    actionExecutionId: string;\n    actionName: string;\n    parentMessageId?: string;\n  }) {\n    this.next({\n      type: RuntimeEventTypes.ActionExecutionStart,\n      actionExecutionId,\n      actionName,\n      parentMessageId,\n    });\n  }\n\n  sendActionExecutionArgs({\n    actionExecutionId,\n    args,\n  }: {\n    actionExecutionId: string;\n    args: string;\n  }) {\n    this.next({ type: RuntimeEventTypes.ActionExecutionArgs, args, actionExecutionId });\n  }\n\n  sendActionExecutionEnd({ actionExecutionId }: { actionExecutionId: string }) {\n    this.next({ type: RuntimeEventTypes.ActionExecutionEnd, actionExecutionId });\n  }\n\n  sendActionExecution({\n    actionExecutionId,\n    actionName,\n    args,\n    parentMessageId,\n  }: {\n    actionExecutionId: string;\n    actionName: string;\n    args: string;\n    parentMessageId?: string;\n  }) {\n    this.sendActionExecutionStart({ actionExecutionId, actionName, parentMessageId });\n    this.sendActionExecutionArgs({ actionExecutionId, args });\n    this.sendActionExecutionEnd({ actionExecutionId });\n  }\n\n  sendActionExecutionResult({\n    actionExecutionId,\n    actionName,\n    result,\n    error,\n  }: {\n    actionExecutionId: string;\n    actionName: string;\n    result?: string;\n    error?: { code: string; message: string };\n  }) {\n    this.next({\n      type: RuntimeEventTypes.ActionExecutionResult,\n      actionName,\n      actionExecutionId,\n      result: ResultMessage.encodeResult(result, error),\n    });\n  }\n\n  sendAgentStateMessage({\n    threadId,\n    agentName,\n    nodeName,\n    runId,\n    active,\n    role,\n    state,\n    running,\n  }: {\n    threadId: string;\n    agentName: string;\n    nodeName: string;\n    runId: string;\n    active: boolean;\n    role: string;\n    state: string;\n    running: boolean;\n  }) {\n    this.next({\n      type: RuntimeEventTypes.AgentStateMessage,\n      threadId,\n      agentName,\n      nodeName,\n      runId,\n      active,\n      role,\n      state,\n      running,\n    });\n  }\n}\n\nexport class RuntimeEventSource {\n  private eventStream$ = new RuntimeEventSubject();\n  private callback!: EventSourceCallback;\n\n  async stream(callback: EventSourceCallback): Promise<void> {\n    this.callback = callback;\n  }\n\n  sendErrorMessageToChat(message = \"An error occurred. Please try again.\") {\n    const errorMessage = `❌ ${message}`;\n    if (!this.callback) {\n      this.stream(async (eventStream$) => {\n        eventStream$.sendTextMessage(randomId(), errorMessage);\n      });\n    } else {\n      this.eventStream$.sendTextMessage(randomId(), errorMessage);\n    }\n  }\n\n  processRuntimeEvents({\n    serverSideActions,\n    guardrailsResult$,\n    actionInputsWithoutAgents,\n    threadId,\n  }: {\n    serverSideActions: Action<any>[];\n    guardrailsResult$?: Subject<GuardrailsResult>;\n    actionInputsWithoutAgents: ActionInput[];\n    threadId: string;\n  }) {\n    this.callback(this.eventStream$).catch((error) => {\n      console.error(\"Error in event source callback\", error);\n      this.sendErrorMessageToChat();\n      this.eventStream$.complete();\n    });\n    return this.eventStream$.pipe(\n      // track state\n      scan(\n        (acc, event) => {\n          // It seems like this is needed so that rxjs recognizes the object has changed\n          // This fixes an issue where action were executed multiple times\n          // Not investigating further for now (Markus)\n          acc = { ...acc };\n\n          if (event.type === RuntimeEventTypes.ActionExecutionStart) {\n            acc.callActionServerSide =\n              serverSideActions.find((action) => action.name === event.actionName) !== undefined;\n            acc.args = \"\";\n            acc.actionExecutionId = event.actionExecutionId;\n            if (acc.callActionServerSide) {\n              acc.action = serverSideActions.find((action) => action.name === event.actionName);\n            }\n            acc.actionExecutionParentMessageId = event.parentMessageId;\n          } else if (event.type === RuntimeEventTypes.ActionExecutionArgs) {\n            acc.args += event.args;\n          }\n\n          acc.event = event;\n\n          return acc;\n        },\n        {\n          event: null,\n          callActionServerSide: false,\n          args: \"\",\n          actionExecutionId: null,\n          action: null,\n          actionExecutionParentMessageId: null,\n        } as RuntimeEventWithState,\n      ),\n      concatMap((eventWithState) => {\n        if (\n          eventWithState.event!.type === RuntimeEventTypes.ActionExecutionEnd &&\n          eventWithState.callActionServerSide\n        ) {\n          const toolCallEventStream$ = new RuntimeEventSubject();\n          executeAction(\n            toolCallEventStream$,\n            guardrailsResult$ ? guardrailsResult$ : null,\n            eventWithState.action!,\n            eventWithState.args,\n            eventWithState.actionExecutionParentMessageId,\n            eventWithState.actionExecutionId,\n            actionInputsWithoutAgents,\n            threadId,\n          ).catch((error) => {\n            console.error(error);\n          });\n\n          telemetry.capture(\"oss.runtime.server_action_executed\", {});\n          return concat(of(eventWithState.event!), toolCallEventStream$).pipe(\n            catchError((error) => {\n              console.error(\"Error in tool call stream\", error);\n              this.sendErrorMessageToChat();\n              return EMPTY;\n            }),\n          );\n        } else {\n          return of(eventWithState.event!);\n        }\n      }),\n    );\n  }\n}\n\nasync function executeAction(\n  eventStream$: RuntimeEventSubject,\n  guardrailsResult$: Subject<GuardrailsResult> | null,\n  action: Action<any>,\n  actionArguments: string,\n  actionExecutionParentMessageId: string | null,\n  actionExecutionId: string,\n  actionInputsWithoutAgents: ActionInput[],\n  threadId: string,\n) {\n  if (guardrailsResult$) {\n    const { status } = await firstValueFrom(guardrailsResult$);\n\n    if (status === \"denied\") {\n      eventStream$.complete();\n      return;\n    }\n  }\n\n  // Prepare arguments for function calling\n  let args: Record<string, any>[] = [];\n  if (actionArguments) {\n    try {\n      args = JSON.parse(actionArguments);\n    } catch (e) {\n      console.error(\"Action argument unparsable\", { actionArguments });\n      eventStream$.sendActionExecutionResult({\n        actionExecutionId,\n        actionName: action.name,\n        error: {\n          code: \"INVALID_ARGUMENTS\",\n          message: \"Failed to parse action arguments\",\n        },\n      });\n      return;\n    }\n  }\n\n  // handle LangGraph agents\n  if (isRemoteAgentAction(action)) {\n    const result = `${action.name} agent started`;\n\n    const agentExecution = plainToInstance(ActionExecutionMessage, {\n      id: actionExecutionId,\n      createdAt: new Date(),\n      name: action.name,\n      arguments: JSON.parse(actionArguments),\n      parentMessageId: actionExecutionParentMessageId ?? actionExecutionId,\n    });\n\n    const agentExecutionResult = plainToInstance(ResultMessage, {\n      id: \"result-\" + actionExecutionId,\n      createdAt: new Date(),\n      actionExecutionId,\n      actionName: action.name,\n      result,\n    });\n\n    eventStream$.sendActionExecutionResult({\n      actionExecutionId,\n      actionName: action.name,\n      result,\n    });\n\n    const stream = await action.remoteAgentHandler({\n      name: action.name,\n      threadId,\n      actionInputsWithoutAgents,\n      additionalMessages: [agentExecution, agentExecutionResult],\n    });\n\n    // forward to eventStream$\n    from(stream).subscribe({\n      next: (event) => eventStream$.next(event),\n      error: (err) => {\n        console.error(\"Error in stream\", err);\n        eventStream$.sendActionExecutionResult({\n          actionExecutionId,\n          actionName: action.name,\n          error: {\n            code: \"STREAM_ERROR\",\n            message: err.message,\n          },\n        });\n        eventStream$.complete();\n      },\n      complete: () => eventStream$.complete(),\n    });\n  } else {\n    // call the function\n    try {\n      const result = await action.handler?.(args);\n      await streamLangChainResponse({\n        result,\n        eventStream$,\n        actionExecution: {\n          name: action.name,\n          id: actionExecutionId,\n        },\n      });\n    } catch (e) {\n      console.error(\"Error in action handler\", e);\n      eventStream$.sendActionExecutionResult({\n        actionExecutionId,\n        actionName: action.name,\n        error: {\n          code: \"HANDLER_ERROR\",\n          message: e.message,\n        },\n      });\n      eventStream$.complete();\n    }\n  }\n}\n", "import { TelemetryClient } from \"@copilotkit/shared\";\nimport { EndpointType, LangGraphPlatformEndpoint } from \"./runtime/remote-actions\";\nimport { createHash } from \"node:crypto\";\nimport { CopilotRuntime, resolveEndpointType } from \"./runtime/copilot-runtime\";\nimport { RuntimeInstanceCreatedInfo } from \"@copilotkit/shared/src/telemetry/events\";\nconst packageJson = require(\"../../package.json\");\n\nconst telemetryClient = new TelemetryClient({\n  packageName: packageJson.name,\n  packageVersion: packageJson.version,\n});\n\nexport function getRuntimeInstanceTelemetryInfo(\n  runtime: CopilotRuntime,\n): RuntimeInstanceCreatedInfo {\n  const endpointsInfo = runtime.remoteEndpointDefinitions.reduce(\n    (acc, endpoint) => {\n      let info = { ...acc };\n\n      const endpointType = resolveEndpointType(endpoint);\n      if (!info.endpointTypes.includes(endpointType)) {\n        info = {\n          ...info,\n          endpointTypes: [...info.endpointTypes, endpointType],\n        };\n      }\n\n      if (endpointType === EndpointType.LangGraphPlatform) {\n        // When type is resolved, recreating a const with casting of type\n        const ep = endpoint as LangGraphPlatformEndpoint;\n        info = {\n          ...info,\n          agentsAmount: ep.agents.length,\n          hashedKey: ep.langsmithApiKey\n            ? createHash(\"sha256\").update(ep.langsmithApiKey).digest(\"hex\")\n            : null,\n        };\n      }\n\n      return info;\n    },\n    { endpointTypes: [], agentsAmount: null, hashedKey: null },\n  );\n\n  return {\n    actionsAmount: runtime.actions.length,\n    endpointsAmount: runtime.remoteEndpointDefinitions.length,\n    endpointTypes: endpointsInfo.endpointTypes,\n    agentsAmount: endpointsInfo.agentsAmount,\n    hashedLgcKey: endpointsInfo.hashedKey,\n  };\n}\n\nexport default telemetryClient;\n", "import { Action, CopilotKitErrorCode } from \"@copilotkit/shared\";\nimport { GraphQLContext } from \"../integrations/shared\";\nimport { Logger } from \"pino\";\nimport { Message } from \"../../graphql/types/converted\";\nimport { RuntimeEvent } from \"../../service-adapters/events\";\nimport { Observable } from \"rxjs\";\nimport { ActionInput } from \"../../graphql/inputs/action.input\";\nimport { AgentStateInput } from \"../../graphql/inputs/agent-state.input\";\nimport {\n  constructLGCRemoteAction,\n  constructRemoteActions,\n  createHeaders,\n} from \"./remote-action-constructors\";\nimport {\n  CopilotKitLowLevelError,\n  ResolvedCopilotKitError,\n  CopilotKitError,\n} from \"@copilotkit/shared\";\nimport { MetaEventInput } from \"../../graphql/inputs/meta-event.input\";\nimport { AbstractAgent } from \"@ag-ui/client\";\nimport { constructAgentWireRemoteAction } from \"./agentwire-action\";\n\nexport type EndpointDefinition = CopilotKitEndpoint | LangGraphPlatformEndpoint;\n\nexport enum EndpointType {\n  CopilotKit = \"copilotKit\",\n  LangGraphPlatform = \"langgraph-platform\",\n}\n\nexport interface BaseEndpointDefinition<TActionType extends EndpointType> {\n  type?: TActionType;\n}\n\nexport interface CopilotKitEndpoint extends BaseEndpointDefinition<EndpointType.CopilotKit> {\n  url: string;\n  onBeforeRequest?: ({ ctx }: { ctx: GraphQLContext }) => {\n    headers?: Record<string, string> | undefined;\n  };\n}\n\nexport interface LangGraphPlatformAgent {\n  name: string;\n  description: string;\n  assistantId?: string;\n}\n\nexport interface LangGraphPlatformEndpoint\n  extends BaseEndpointDefinition<EndpointType.LangGraphPlatform> {\n  deploymentUrl: string;\n  langsmithApiKey?: string | null;\n  agents: LangGraphPlatformAgent[];\n}\n\nexport type RemoteActionInfoResponse = {\n  actions: any[];\n  agents: any[];\n};\n\nexport type RemoteAgentHandlerParams = {\n  name: string;\n  actionInputsWithoutAgents: ActionInput[];\n  threadId?: string;\n  nodeName?: string;\n  additionalMessages?: Message[];\n  metaEvents?: MetaEventInput[];\n};\n\nexport type RemoteAgentAction = Action<any> & {\n  remoteAgentHandler: (params: RemoteAgentHandlerParams) => Promise<Observable<RuntimeEvent>>;\n};\n\nexport function isRemoteAgentAction(action: Action<any>): action is RemoteAgentAction {\n  if (!action) {\n    return false;\n  }\n  return typeof (action as RemoteAgentAction).remoteAgentHandler === \"function\";\n}\n\nasync function fetchRemoteInfo({\n  url,\n  onBeforeRequest,\n  graphqlContext,\n  logger,\n  frontendUrl,\n}: {\n  url: string;\n  onBeforeRequest?: CopilotKitEndpoint[\"onBeforeRequest\"];\n  graphqlContext: GraphQLContext;\n  logger: Logger;\n  frontendUrl?: string;\n}): Promise<RemoteActionInfoResponse> {\n  logger.debug({ url }, \"Fetching actions from url\");\n  const headers = createHeaders(onBeforeRequest, graphqlContext);\n\n  const fetchUrl = `${url}/info`;\n  try {\n    const response = await fetch(fetchUrl, {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify({ properties: graphqlContext.properties, frontendUrl }),\n    });\n\n    if (!response.ok) {\n      logger.error(\n        { url, status: response.status, body: await response.text() },\n        \"Failed to fetch actions from url\",\n      );\n      throw new ResolvedCopilotKitError({\n        status: response.status,\n        url: fetchUrl,\n        isRemoteEndpoint: true,\n      });\n    }\n\n    const json = await response.json();\n    logger.debug({ json }, \"Fetched actions from url\");\n    return json;\n  } catch (error) {\n    if (error instanceof CopilotKitError) {\n      throw error;\n    }\n    throw new CopilotKitLowLevelError({ error, url: fetchUrl });\n  }\n}\n\nexport async function setupRemoteActions({\n  remoteEndpointDefinitions,\n  graphqlContext,\n  messages,\n  agentStates,\n  frontendUrl,\n  agents,\n}: {\n  remoteEndpointDefinitions: EndpointDefinition[];\n  graphqlContext: GraphQLContext;\n  messages: Message[];\n  agentStates?: AgentStateInput[];\n  frontendUrl?: string;\n  agents: Record<string, AbstractAgent>;\n}): Promise<Action[]> {\n  const logger = graphqlContext.logger.child({ component: \"remote-actions.fetchRemoteActions\" });\n  logger.debug({ remoteEndpointDefinitions }, \"Fetching from remote endpoints\");\n\n  // Remove duplicates of remoteEndpointDefinitions.url\n  const filtered = remoteEndpointDefinitions.filter((value, index, self) => {\n    if (value.type === EndpointType.LangGraphPlatform) {\n      return value;\n    }\n    return index === self.findIndex((t: CopilotKitEndpoint) => t.url === value.url);\n  });\n\n  const result = await Promise.all(\n    filtered.map(async (endpoint) => {\n      // Check for properties that can distinguish LG platform from other actions\n      if (endpoint.type === EndpointType.LangGraphPlatform) {\n        return constructLGCRemoteAction({\n          endpoint,\n          messages,\n          graphqlContext,\n          logger: logger.child({\n            component: \"remote-actions.constructLGCRemoteAction\",\n            endpoint,\n          }),\n          agentStates,\n        });\n      }\n\n      const json = await fetchRemoteInfo({\n        url: endpoint.url,\n        onBeforeRequest: endpoint.onBeforeRequest,\n        graphqlContext,\n        logger: logger.child({ component: \"remote-actions.fetchActionsFromUrl\", endpoint }),\n        frontendUrl,\n      });\n\n      return constructRemoteActions({\n        json,\n        messages,\n        url: endpoint.url,\n        onBeforeRequest: endpoint.onBeforeRequest,\n        graphqlContext,\n        logger: logger.child({ component: \"remote-actions.constructActions\", endpoint }),\n        agentStates,\n      });\n    }),\n  );\n\n  for (const [key, agent] of Object.entries(agents)) {\n    if (agent.agentId !== undefined && agent.agentId !== key) {\n      throw new CopilotKitError({\n        message: `Agent ${key} has agentId ${agent.agentId} which does not match the key ${key}`,\n        code: CopilotKitErrorCode.UNKNOWN,\n      });\n    } else if (agent.agentId === undefined) {\n      agent.agentId = key;\n    }\n\n    result.push(\n      constructAgentWireRemoteAction({\n        logger,\n        messages,\n        agentStates,\n        agent: agent,\n      }),\n    );\n  }\n\n  return result.flat();\n}\n", "/**\n * <Callout type=\"info\">\n *   This is the reference for the `CopilotRuntime` class. For more information and example code snippets, please see [Concept: Copilot Runtime](/concepts/copilot-runtime).\n * </Callout>\n *\n * ## Usage\n *\n * ```tsx\n * import { CopilotRuntime } from \"@copilotkit/runtime\";\n *\n * const copilotKit = new CopilotRuntime();\n * ```\n */\n\nimport {\n  Action,\n  actionParametersToJsonSchema,\n  Parameter,\n  ResolvedCopilotKitError,\n  CopilotKitApiDiscoveryError,\n  randomId,\n  CopilotKitError,\n  CopilotKitLowLevelError,\n  CopilotKitAgentDiscoveryError,\n  CopilotKitMisuseError,\n} from \"@copilotkit/shared\";\nimport {\n  CopilotServiceAdapter,\n  EmptyAdapter,\n  RemoteChain,\n  RemoteChainParameters,\n} from \"../../service-adapters\";\n\nimport { MessageInput } from \"../../graphql/inputs/message.input\";\nimport { ActionInput } from \"../../graphql/inputs/action.input\";\nimport { RuntimeEventSource, RuntimeEventTypes } from \"../../service-adapters/events\";\nimport { convertGqlInputToMessages } from \"../../service-adapters/conversion\";\nimport { Message } from \"../../graphql/types/converted\";\nimport { ForwardedParametersInput } from \"../../graphql/inputs/forwarded-parameters.input\";\n\nimport {\n  isRemoteAgentAction,\n  RemoteAgentAction,\n  EndpointType,\n  setupRemoteActions,\n  EndpointDefinition,\n  CopilotKitEndpoint,\n  LangGraphPlatformEndpoint,\n} from \"./remote-actions\";\n\nimport { GraphQLContext } from \"../integrations/shared\";\nimport { AgentSessionInput } from \"../../graphql/inputs/agent-session.input\";\nimport { from } from \"rxjs\";\nimport { AgentStateInput } from \"../../graphql/inputs/agent-state.input\";\nimport { ActionInputAvailability } from \"../../graphql/types/enums\";\nimport { createHeaders } from \"./remote-action-constructors\";\nimport { Agent } from \"../../graphql/types/agents-response.type\";\nimport { ExtensionsInput } from \"../../graphql/inputs/extensions.input\";\nimport { ExtensionsResponse } from \"../../graphql/types/extensions-response.type\";\nimport { LoadAgentStateResponse } from \"../../graphql/types/load-agent-state-response.type\";\nimport { Client as LangGraphClient } from \"@langchain/langgraph-sdk\";\nimport { langchainMessagesToCopilotKit } from \"./remote-lg-action\";\nimport { MetaEventInput } from \"../../graphql/inputs/meta-event.input\";\nimport {\n  CopilotObservabilityConfig,\n  LLMRequestData,\n  LLMResponseData,\n  LLMErrorData,\n} from \"../observability\";\nimport { AbstractAgent } from \"@ag-ui/client\";\nimport { MessageRole } from \"../../graphql/types/enums\";\n\n// +++ MCP Imports +++\nimport {\n  MCPClient,\n  MCPEndpointConfig,\n  MCPTool,\n  convertMCPToolsToActions,\n  generateMcpToolInstructions,\n} from \"./mcp-tools-utils\";\n// Define the function type alias here or import if defined elsewhere\ntype CreateMCPClientFunction = (config: MCPEndpointConfig) => Promise<MCPClient>;\n// --- MCP Imports ---\n\nexport interface CopilotRuntimeRequest {\n  serviceAdapter: CopilotServiceAdapter;\n  messages: MessageInput[];\n  actions: ActionInput[];\n  agentSession?: AgentSessionInput;\n  agentStates?: AgentStateInput[];\n  outputMessagesPromise: Promise<Message[]>;\n  threadId?: string;\n  runId?: string;\n  publicApiKey?: string;\n  graphqlContext: GraphQLContext;\n  forwardedParameters?: ForwardedParametersInput;\n  url?: string;\n  extensions?: ExtensionsInput;\n  metaEvents?: MetaEventInput[];\n}\n\ninterface CopilotRuntimeResponse {\n  threadId: string;\n  runId?: string;\n  eventSource: RuntimeEventSource;\n  serverSideActions: Action<any>[];\n  actionInputsWithoutAgents: ActionInput[];\n  extensions?: ExtensionsResponse;\n}\n\ntype ActionsConfiguration<T extends Parameter[] | [] = []> =\n  | Action<T>[]\n  | ((ctx: { properties: any; url?: string }) => Action<T>[]);\n\ninterface OnBeforeRequestOptions {\n  threadId?: string;\n  runId?: string;\n  inputMessages: Message[];\n  properties: any;\n  url?: string;\n}\n\ntype OnBeforeRequestHandler = (options: OnBeforeRequestOptions) => void | Promise<void>;\n\ninterface OnAfterRequestOptions {\n  threadId: string;\n  runId?: string;\n  inputMessages: Message[];\n  outputMessages: Message[];\n  properties: any;\n  url?: string;\n}\n\ntype OnAfterRequestHandler = (options: OnAfterRequestOptions) => void | Promise<void>;\n\ninterface Middleware {\n  /**\n   * A function that is called before the request is processed.\n   */\n  onBeforeRequest?: OnBeforeRequestHandler;\n\n  /**\n   * A function that is called after the request is processed.\n   */\n  onAfterRequest?: OnAfterRequestHandler;\n}\n\ntype AgentWithEndpoint = Agent & { endpoint: EndpointDefinition };\n\nexport interface CopilotRuntimeConstructorParams<T extends Parameter[] | [] = []> {\n  /**\n   * Middleware to be used by the runtime.\n   *\n   * ```ts\n   * onBeforeRequest: (options: {\n   *   threadId?: string;\n   *   runId?: string;\n   *   inputMessages: Message[];\n   *   properties: any;\n   * }) => void | Promise<void>;\n   * ```\n   *\n   * ```ts\n   * onAfterRequest: (options: {\n   *   threadId?: string;\n   *   runId?: string;\n   *   inputMessages: Message[];\n   *   outputMessages: Message[];\n   *   properties: any;\n   * }) => void | Promise<void>;\n   * ```\n   */\n  middleware?: Middleware;\n\n  /*\n   * A list of server side actions that can be executed. Will be ignored when remoteActions are set\n   */\n  actions?: ActionsConfiguration<T>;\n\n  /*\n   * Deprecated: Use `remoteEndpoints`.\n   */\n  remoteActions?: CopilotKitEndpoint[];\n\n  /*\n   * A list of remote actions that can be executed.\n   */\n  remoteEndpoints?: EndpointDefinition[];\n\n  /*\n   * An array of LangServer URLs.\n   */\n  langserve?: RemoteChainParameters[];\n\n  /*\n   * A map of agent names to AgentWire agents.\n   */\n  agents?: Record<string, AbstractAgent>;\n\n  /*\n   * Delegates agent state processing to the service adapter.\n   *\n   * When enabled, individual agent state requests will not be processed by the agent itself.\n   * Instead, all processing will be handled by the service adapter.\n   */\n  delegateAgentProcessingToServiceAdapter?: boolean;\n\n  /**\n   * Configuration for LLM request/response logging.\n   * Requires publicApiKey from CopilotKit component to be set:\n   *\n   * ```tsx\n   * <CopilotKit publicApiKey=\"ck_pub_...\" />\n   * ```\n   *\n   * Example logging config:\n   * ```ts\n   * logging: {\n   *   enabled: true, // Enable or disable logging\n   *   progressive: true, // Set to false for buffered logging\n   *   logger: {\n   *     logRequest: (data) => langfuse.trace({ name: \"LLM Request\", input: data }),\n   *     logResponse: (data) => langfuse.trace({ name: \"LLM Response\", output: data }),\n   *     logError: (errorData) => langfuse.trace({ name: \"LLM Error\", metadata: errorData }),\n   *   },\n   * }\n   * ```\n   */\n  observability_c?: CopilotObservabilityConfig;\n\n  /**\n   * Configuration for connecting to Model Context Protocol (MCP) servers.\n   * Allows fetching and using tools defined on external MCP-compliant servers.\n   * Requires providing the `createMCPClient` function during instantiation.\n   * @experimental\n   */\n  mcpServers?: MCPEndpointConfig[];\n\n  /**\n   * A function that creates an MCP client instance for a given endpoint configuration.\n   * This function is responsible for using the appropriate MCP client library\n   * (e.g., `@copilotkit/runtime`, `ai`) to establish a connection.\n   * Required if `mcpServers` is provided.\n   *\n   * ```typescript\n   * import { experimental_createMCPClient } from \"ai\"; // Import from vercel ai library\n   * // ...\n   * const runtime = new CopilotRuntime({\n   *   mcpServers: [{ endpoint: \"...\" }],\n   *   async createMCPClient(config) {\n   *     return await experimental_createMCPClient({\n   *       transport: {\n   *         type: \"sse\",\n   *         url: config.endpoint,\n   *         headers: config.apiKey\n   *           ? { Authorization: `Bearer ${config.apiKey}` }\n   *           : undefined,\n   *       },\n   *     });\n   *   }\n   * });\n   * ```\n   */\n  createMCPClient?: CreateMCPClientFunction;\n}\n\nexport class CopilotRuntime<const T extends Parameter[] | [] = []> {\n  public actions: ActionsConfiguration<T>;\n  public agents: Record<string, AbstractAgent>;\n  public remoteEndpointDefinitions: EndpointDefinition[];\n  private langserve: Promise<Action<any>>[] = [];\n  private onBeforeRequest?: OnBeforeRequestHandler;\n  private onAfterRequest?: OnAfterRequestHandler;\n  private delegateAgentProcessingToServiceAdapter: boolean;\n  private observability?: CopilotObservabilityConfig;\n  private availableAgents: Pick<AgentWithEndpoint, \"name\" | \"id\">[];\n\n  // +++ MCP Properties +++\n  private readonly mcpServersConfig?: MCPEndpointConfig[];\n  private mcpActionCache = new Map<string, Action<any>[]>();\n  // --- MCP Properties ---\n\n  // +++ MCP Client Factory +++\n  private readonly createMCPClientImpl?: CreateMCPClientFunction;\n  // --- MCP Client Factory ---\n\n  constructor(params?: CopilotRuntimeConstructorParams<T>) {\n    if (\n      params?.actions &&\n      params?.remoteEndpoints &&\n      params?.remoteEndpoints.some((e) => e.type === EndpointType.LangGraphPlatform)\n    ) {\n      console.warn(\"Actions set in runtime instance will not be available for the agent\");\n    }\n    this.actions = params?.actions || [];\n    this.availableAgents = [];\n\n    for (const chain of params?.langserve || []) {\n      const remoteChain = new RemoteChain(chain);\n      this.langserve.push(remoteChain.toAction());\n    }\n\n    this.remoteEndpointDefinitions = params?.remoteEndpoints ?? params?.remoteActions ?? [];\n\n    this.onBeforeRequest = params?.middleware?.onBeforeRequest;\n    this.onAfterRequest = params?.middleware?.onAfterRequest;\n    this.delegateAgentProcessingToServiceAdapter =\n      params?.delegateAgentProcessingToServiceAdapter || false;\n    this.observability = params?.observability_c;\n    this.agents = params?.agents ?? {};\n    // +++ MCP Initialization +++\n    this.mcpServersConfig = params?.mcpServers;\n    this.createMCPClientImpl = params?.createMCPClient;\n\n    // Validate: If mcpServers are provided, createMCPClient must also be provided\n    if (this.mcpServersConfig && this.mcpServersConfig.length > 0 && !this.createMCPClientImpl) {\n      throw new CopilotKitMisuseError({\n        message:\n          \"MCP Integration Error: `mcpServers` were provided, but the `createMCPClient` function was not passed to the CopilotRuntime constructor. \" +\n          \"Please provide an implementation for `createMCPClient`.\",\n      });\n    }\n\n    // Warning if actions are defined alongside LangGraph platform (potentially MCP too?)\n    if (\n      params?.actions &&\n      (params?.remoteEndpoints?.some((e) => e.type === EndpointType.LangGraphPlatform) ||\n        this.mcpServersConfig?.length)\n    ) {\n      console.warn(\n        \"Local 'actions' defined in CopilotRuntime might not be available to remote agents (LangGraph, MCP). Consider defining actions closer to the agent implementation if needed.\",\n      );\n    }\n  }\n\n  // +++ MCP Instruction Injection Method +++\n  private injectMCPToolInstructions(\n    messages: MessageInput[],\n    currentActions: Action<any>[],\n  ): MessageInput[] {\n    // Filter the *passed-in* actions for MCP tools\n    const mcpActionsForRequest = currentActions.filter((action) => (action as any)._isMCPTool);\n\n    if (!mcpActionsForRequest || mcpActionsForRequest.length === 0) {\n      return messages; // No MCP tools for this specific request\n    }\n\n    // Create a map to deduplicate tools by name (keeping the last one if duplicates exist)\n    const uniqueMcpTools = new Map<string, Action<any>>();\n\n    // Add all MCP tools to the map with their names as keys\n    mcpActionsForRequest.forEach((action) => {\n      uniqueMcpTools.set(action.name, action);\n    });\n\n    // Format instructions from the unique tools map\n    // Convert Action objects to MCPTool format for the instruction generator\n    const toolsMap: Record<string, MCPTool> = {};\n    Array.from(uniqueMcpTools.values()).forEach((action) => {\n      toolsMap[action.name] = {\n        description: action.description || \"\",\n        schema: action.parameters\n          ? {\n              parameters: {\n                properties: action.parameters.reduce(\n                  (acc, p) => ({\n                    ...acc,\n                    [p.name]: { type: p.type, description: p.description },\n                  }),\n                  {},\n                ),\n                required: action.parameters.filter((p) => p.required).map((p) => p.name),\n              },\n            }\n          : {},\n        execute: async () => ({}), // Placeholder, not used for instructions\n      };\n    });\n\n    // Generate instructions using the exported helper\n    const mcpToolInstructions = generateMcpToolInstructions(toolsMap);\n\n    if (!mcpToolInstructions) {\n      return messages; // No MCP tools to describe\n    }\n\n    const instructions =\n      \"You have access to the following tools provided by external Model Context Protocol (MCP) servers:\\n\" +\n      mcpToolInstructions +\n      \"\\nUse them when appropriate to fulfill the user's request.\";\n\n    const systemMessageIndex = messages.findIndex((msg) => msg.textMessage?.role === \"system\");\n\n    const newMessages = [...messages]; // Create a mutable copy\n\n    if (systemMessageIndex !== -1) {\n      const existingMsg = newMessages[systemMessageIndex];\n      if (existingMsg.textMessage) {\n        existingMsg.textMessage.content =\n          (existingMsg.textMessage.content ? existingMsg.textMessage.content + \"\\n\\n\" : \"\") +\n          instructions;\n      }\n    } else {\n      newMessages.unshift({\n        id: randomId(),\n        createdAt: new Date(),\n        textMessage: {\n          role: MessageRole.system,\n          content: instructions,\n        },\n        actionExecutionMessage: undefined,\n        resultMessage: undefined,\n        agentStateMessage: undefined,\n      });\n    }\n\n    return newMessages;\n  }\n  // --- MCP Instruction Injection Method ---\n\n  async processRuntimeRequest(request: CopilotRuntimeRequest): Promise<CopilotRuntimeResponse> {\n    const {\n      serviceAdapter,\n      messages: rawMessages,\n      actions: clientSideActionsInput,\n      threadId,\n      runId,\n      outputMessagesPromise,\n      graphqlContext,\n      forwardedParameters,\n      url,\n      extensions,\n      agentSession,\n      agentStates,\n      publicApiKey,\n    } = request;\n\n    const eventSource = new RuntimeEventSource();\n    // Track request start time for logging\n    const requestStartTime = Date.now();\n    // For storing streamed chunks if progressive logging is enabled\n    const streamedChunks: any[] = [];\n\n    try {\n      if (agentSession && !this.delegateAgentProcessingToServiceAdapter) {\n        return await this.processAgentRequest(request);\n      }\n      if (serviceAdapter instanceof EmptyAdapter) {\n        throw new CopilotKitMisuseError({\n          message: `Invalid adapter configuration: EmptyAdapter is only meant to be used with agent lock mode. \nFor non-agent components like useCopilotChatSuggestions, CopilotTextarea, or CopilotTask, \nplease use an LLM adapter instead.`,\n        });\n      }\n\n      // +++ Get Server Side Actions (including dynamic MCP) EARLY +++\n      const serverSideActions = await this.getServerSideActions(request);\n      // --- Get Server Side Actions (including dynamic MCP) EARLY ---\n\n      // Filter raw messages *before* injection\n      const filteredRawMessages = rawMessages.filter((message) => !message.agentStateMessage);\n\n      // +++ Inject MCP Instructions based on current actions +++\n      const messagesWithInjectedInstructions = this.injectMCPToolInstructions(\n        filteredRawMessages,\n        serverSideActions,\n      );\n      const inputMessages = convertGqlInputToMessages(messagesWithInjectedInstructions);\n      // --- Inject MCP Instructions based on current actions ---\n\n      // Log LLM request if logging is enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          const requestData: LLMRequestData = {\n            threadId,\n            runId,\n            model: forwardedParameters?.model,\n            messages: inputMessages,\n            actions: clientSideActionsInput,\n            forwardedParameters,\n            timestamp: requestStartTime,\n            provider: this.detectProvider(serviceAdapter),\n          };\n\n          await this.observability.hooks.handleRequest(requestData);\n        } catch (error) {\n          console.error(\"Error logging LLM request:\", error);\n        }\n      }\n\n      const serverSideActionsInput: ActionInput[] = serverSideActions.map((action) => ({\n        name: action.name,\n        description: action.description,\n        jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters)),\n      }));\n\n      const actionInputs = flattenToolCallsNoDuplicates([\n        ...serverSideActionsInput,\n        ...clientSideActionsInput.filter(\n          // Filter remote actions from CopilotKit core loop\n          (action) => action.available !== ActionInputAvailability.remote,\n        ),\n      ]);\n\n      await this.onBeforeRequest?.({\n        threadId,\n        runId,\n        inputMessages,\n        properties: graphqlContext.properties,\n        url,\n      });\n\n      const result = await serviceAdapter.process({\n        messages: inputMessages,\n        actions: actionInputs,\n        threadId,\n        runId,\n        eventSource,\n        forwardedParameters,\n        extensions,\n        agentSession,\n        agentStates,\n      });\n\n      // for backwards compatibility, we deal with the case that no threadId is provided\n      // by the frontend, by using the threadId from the response\n      const nonEmptyThreadId = threadId ?? result.threadId;\n\n      outputMessagesPromise\n        .then((outputMessages) => {\n          this.onAfterRequest?.({\n            threadId: nonEmptyThreadId,\n            runId: result.runId,\n            inputMessages,\n            outputMessages,\n            properties: graphqlContext.properties,\n            url,\n          });\n        })\n        .catch((_error) => {});\n\n      // After getting the response, log it if logging is enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          outputMessagesPromise\n            .then((outputMessages) => {\n              const responseData: LLMResponseData = {\n                threadId: result.threadId,\n                runId: result.runId,\n                model: forwardedParameters?.model,\n                // Use collected chunks for progressive mode or outputMessages for regular mode\n                output: this.observability.progressive ? streamedChunks : outputMessages,\n                latency: Date.now() - requestStartTime,\n                timestamp: Date.now(),\n                provider: this.detectProvider(serviceAdapter),\n                // Indicate this is the final response\n                isFinalResponse: true,\n              };\n\n              try {\n                this.observability.hooks.handleResponse(responseData);\n              } catch (logError) {\n                console.error(\"Error logging LLM response:\", logError);\n              }\n            })\n            .catch((error) => {\n              console.error(\"Failed to get output messages for logging:\", error);\n            });\n        } catch (error) {\n          console.error(\"Error setting up logging for LLM response:\", error);\n        }\n      }\n\n      // Add progressive logging if enabled\n      if (this.observability?.enabled && this.observability.progressive && publicApiKey) {\n        // Keep reference to original stream function\n        const originalStream = eventSource.stream.bind(eventSource);\n\n        // Wrap the stream function to intercept events\n        eventSource.stream = async (callback) => {\n          await originalStream(async (eventStream$) => {\n            // Create subscription to capture streaming events\n            eventStream$.subscribe({\n              next: (event) => {\n                // Only log content chunks\n                if (event.type === RuntimeEventTypes.TextMessageContent) {\n                  // Store the chunk\n                  streamedChunks.push(event.content);\n\n                  // Log each chunk separately for progressive mode\n                  try {\n                    const progressiveData: LLMResponseData = {\n                      threadId: threadId || \"\",\n                      runId,\n                      model: forwardedParameters?.model,\n                      output: event.content,\n                      latency: Date.now() - requestStartTime,\n                      timestamp: Date.now(),\n                      provider: this.detectProvider(serviceAdapter),\n                      isProgressiveChunk: true,\n                    };\n\n                    // Use Promise to handle async logger without awaiting\n                    Promise.resolve()\n                      .then(() => {\n                        this.observability.hooks.handleResponse(progressiveData);\n                      })\n                      .catch((error) => {\n                        console.error(\"Error in progressive logging:\", error);\n                      });\n                  } catch (error) {\n                    console.error(\"Error preparing progressive log data:\", error);\n                  }\n                }\n              },\n            });\n\n            // Call the original callback with the event stream\n            await callback(eventStream$);\n          });\n        };\n      }\n\n      return {\n        threadId: nonEmptyThreadId,\n        runId: result.runId,\n        eventSource,\n        serverSideActions,\n        actionInputsWithoutAgents: actionInputs.filter(\n          (action) =>\n            // TODO-AGENTS: do not exclude ALL server side actions\n            !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name),\n          // !isRemoteAgentAction(\n          //   serverSideActions.find((serverSideAction) => serverSideAction.name == action.name),\n          // ),\n        ),\n        extensions: result.extensions,\n      };\n    } catch (error) {\n      // Log error if logging is enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          const errorData: LLMErrorData = {\n            threadId,\n            runId,\n            model: forwardedParameters?.model,\n            error: error instanceof Error ? error : String(error),\n            timestamp: Date.now(),\n            latency: Date.now() - requestStartTime,\n            provider: this.detectProvider(serviceAdapter),\n          };\n\n          await this.observability.hooks.handleError(errorData);\n        } catch (logError) {\n          console.error(\"Error logging LLM error:\", logError);\n        }\n      }\n\n      if (error instanceof CopilotKitError) {\n        throw error;\n      }\n      console.error(\"Error getting response:\", error);\n      eventSource.sendErrorMessageToChat();\n      throw error;\n    }\n  }\n\n  async discoverAgentsFromEndpoints(graphqlContext: GraphQLContext): Promise<AgentWithEndpoint[]> {\n    const agents: Promise<AgentWithEndpoint[]> = this.remoteEndpointDefinitions.reduce(\n      async (acc: Promise<Agent[]>, endpoint) => {\n        const agents = await acc;\n        if (endpoint.type === EndpointType.LangGraphPlatform) {\n          const propertyHeaders = graphqlContext.properties.authorization\n            ? { authorization: `Bearer ${graphqlContext.properties.authorization}` }\n            : null;\n\n          const client = new LangGraphClient({\n            apiUrl: endpoint.deploymentUrl,\n            apiKey: endpoint.langsmithApiKey,\n            defaultHeaders: { ...propertyHeaders },\n          });\n          let data: Array<{ assistant_id: string; graph_id: string }> | { detail: string } = [];\n          try {\n            data = await client.assistants.search();\n\n            if (data && \"detail\" in data && (data.detail as string).toLowerCase() === \"not found\") {\n              throw new CopilotKitAgentDiscoveryError({ availableAgents: this.availableAgents });\n            }\n          } catch (e) {\n            throw new CopilotKitMisuseError({\n              message: `\n              Failed to find or contact remote endpoint at url ${endpoint.deploymentUrl}.\n              Make sure the API is running and that it's indeed a LangGraph platform url.\n              \n              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`,\n            });\n          }\n          const endpointAgents = data.map((entry) => ({\n            name: entry.graph_id,\n            id: entry.assistant_id,\n            description: \"\",\n            endpoint,\n          }));\n          return [...agents, ...endpointAgents];\n        }\n\n        interface InfoResponse {\n          agents?: Array<{\n            name: string;\n            description: string;\n          }>;\n        }\n        const cpkEndpoint = endpoint as CopilotKitEndpoint;\n        const fetchUrl = `${endpoint.url}/info`;\n        try {\n          const response = await fetch(fetchUrl, {\n            method: \"POST\",\n            headers: createHeaders(cpkEndpoint.onBeforeRequest, graphqlContext),\n            body: JSON.stringify({ properties: graphqlContext.properties }),\n          });\n          if (!response.ok) {\n            if (response.status === 404) {\n              throw new CopilotKitApiDiscoveryError({ url: fetchUrl });\n            }\n            throw new ResolvedCopilotKitError({\n              status: response.status,\n              url: fetchUrl,\n              isRemoteEndpoint: true,\n            });\n          }\n\n          const data: InfoResponse = await response.json();\n          const endpointAgents = (data?.agents ?? []).map((agent) => ({\n            name: agent.name,\n            description: agent.description ?? \"\" ?? \"\",\n            id: randomId(), // Required by Agent type\n            endpoint,\n          }));\n          return [...agents, ...endpointAgents];\n        } catch (error) {\n          if (error instanceof CopilotKitError) {\n            throw error;\n          }\n          throw new CopilotKitLowLevelError({ error: error as Error, url: fetchUrl });\n        }\n      },\n      Promise.resolve([]),\n    );\n    this.availableAgents = ((await agents) ?? []).map((a) => ({ name: a.name, id: a.id }));\n\n    return agents;\n  }\n\n  async loadAgentState(\n    graphqlContext: GraphQLContext,\n    threadId: string,\n    agentName: string,\n  ): Promise<LoadAgentStateResponse> {\n    const agentsWithEndpoints = await this.discoverAgentsFromEndpoints(graphqlContext);\n\n    const agentWithEndpoint = agentsWithEndpoints.find((agent) => agent.name === agentName);\n    if (!agentWithEndpoint) {\n      throw new Error(\"Agent not found\");\n    }\n\n    if (agentWithEndpoint.endpoint.type === EndpointType.LangGraphPlatform) {\n      const propertyHeaders = graphqlContext.properties.authorization\n        ? { authorization: `Bearer ${graphqlContext.properties.authorization}` }\n        : null;\n\n      const client = new LangGraphClient({\n        apiUrl: agentWithEndpoint.endpoint.deploymentUrl,\n        apiKey: agentWithEndpoint.endpoint.langsmithApiKey,\n        defaultHeaders: { ...propertyHeaders },\n      });\n      let state: any = {};\n      try {\n        state = (await client.threads.getState(threadId)).values as any;\n      } catch (error) {}\n\n      if (Object.keys(state).length === 0) {\n        return {\n          threadId: threadId || \"\",\n          threadExists: false,\n          state: JSON.stringify({}),\n          messages: JSON.stringify([]),\n        };\n      } else {\n        const { messages, ...stateWithoutMessages } = state;\n        const copilotkitMessages = langchainMessagesToCopilotKit(messages);\n        return {\n          threadId: threadId || \"\",\n          threadExists: true,\n          state: JSON.stringify(stateWithoutMessages),\n          messages: JSON.stringify(copilotkitMessages),\n        };\n      }\n    } else if (\n      agentWithEndpoint.endpoint.type === EndpointType.CopilotKit ||\n      !(\"type\" in agentWithEndpoint.endpoint)\n    ) {\n      const cpkEndpoint = agentWithEndpoint.endpoint as CopilotKitEndpoint;\n      const fetchUrl = `${cpkEndpoint.url}/agents/state`;\n      try {\n        const response = await fetch(fetchUrl, {\n          method: \"POST\",\n          headers: createHeaders(cpkEndpoint.onBeforeRequest, graphqlContext),\n          body: JSON.stringify({\n            properties: graphqlContext.properties,\n            threadId,\n            name: agentName,\n          }),\n        });\n        if (!response.ok) {\n          if (response.status === 404) {\n            throw new CopilotKitApiDiscoveryError({ url: fetchUrl });\n          }\n          throw new ResolvedCopilotKitError({\n            status: response.status,\n            url: fetchUrl,\n            isRemoteEndpoint: true,\n          });\n        }\n\n        const data: LoadAgentStateResponse = await response.json();\n\n        return {\n          ...data,\n          state: JSON.stringify(data.state),\n          messages: JSON.stringify(data.messages),\n        };\n      } catch (error) {\n        if (error instanceof CopilotKitError) {\n          throw error;\n        }\n        throw new CopilotKitLowLevelError({ error, url: fetchUrl });\n      }\n    } else {\n      throw new Error(`Unknown endpoint type: ${(agentWithEndpoint.endpoint as any).type}`);\n    }\n  }\n\n  private async processAgentRequest(\n    request: CopilotRuntimeRequest,\n  ): Promise<CopilotRuntimeResponse> {\n    const {\n      messages: rawMessages,\n      outputMessagesPromise,\n      graphqlContext,\n      agentSession,\n      threadId: threadIdFromRequest,\n      metaEvents,\n      publicApiKey,\n      forwardedParameters,\n    } = request;\n    const { agentName, nodeName } = agentSession;\n\n    // Track request start time for observability\n    const requestStartTime = Date.now();\n    // For storing streamed chunks if progressive logging is enabled\n    const streamedChunks: any[] = [];\n\n    // for backwards compatibility, deal with the case when no threadId is provided\n    const threadId = threadIdFromRequest ?? agentSession.threadId;\n\n    const serverSideActions = await this.getServerSideActions(request);\n\n    const messages = convertGqlInputToMessages(rawMessages);\n\n    const currentAgent = serverSideActions.find(\n      (action) => action.name === agentName && isRemoteAgentAction(action),\n    ) as RemoteAgentAction;\n\n    if (!currentAgent) {\n      throw new CopilotKitAgentDiscoveryError({ agentName, availableAgents: this.availableAgents });\n    }\n\n    // Filter actions to include:\n    // 1. Regular (non-agent) actions\n    // 2. Other agents' actions (but prevent self-calls to avoid infinite loops)\n    const availableActionsForCurrentAgent: ActionInput[] = serverSideActions\n      .filter(\n        (action) =>\n          // Case 1: Keep all regular (non-agent) actions\n          !isRemoteAgentAction(action) ||\n          // Case 2: For agent actions, keep all except self (prevent infinite loops)\n          (isRemoteAgentAction(action) && action.name !== agentName) /* prevent self-calls */,\n      )\n      .map((action) => ({\n        name: action.name,\n        description: action.description,\n        jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters)),\n      }));\n\n    const allAvailableActions = flattenToolCallsNoDuplicates([\n      ...availableActionsForCurrentAgent,\n      ...request.actions,\n    ]);\n\n    // Log agent request if observability is enabled\n    if (this.observability?.enabled && publicApiKey) {\n      try {\n        const requestData: LLMRequestData = {\n          threadId,\n          runId: undefined,\n          model: forwardedParameters?.model,\n          messages,\n          actions: allAvailableActions,\n          forwardedParameters,\n          timestamp: requestStartTime,\n          provider: \"agent\",\n          agentName, // Add agent-specific context\n          nodeName,\n        };\n\n        await this.observability.hooks.handleRequest(requestData);\n      } catch (error) {\n        console.error(\"Error logging agent request:\", error);\n      }\n    }\n\n    await this.onBeforeRequest?.({\n      threadId,\n      runId: undefined,\n      inputMessages: messages,\n      properties: graphqlContext.properties,\n    });\n\n    try {\n      const eventSource = new RuntimeEventSource();\n      const stream = await currentAgent.remoteAgentHandler({\n        name: agentName,\n        threadId,\n        nodeName,\n        metaEvents,\n        actionInputsWithoutAgents: allAvailableActions,\n      });\n\n      // Add progressive observability if enabled\n      if (this.observability?.enabled && this.observability.progressive && publicApiKey) {\n        // Wrap the stream function to intercept events for observability without changing core logic\n        const originalStream = eventSource.stream.bind(eventSource);\n\n        eventSource.stream = async (callback) => {\n          await originalStream(async (eventStream$) => {\n            // Create subscription to capture streaming events\n            eventStream$.subscribe({\n              next: (event) => {\n                // Only log content chunks\n                if (event.type === RuntimeEventTypes.TextMessageContent) {\n                  // Store the chunk\n                  streamedChunks.push(event.content);\n\n                  // Log each chunk separately for progressive mode\n                  try {\n                    const progressiveData: LLMResponseData = {\n                      threadId: threadId || \"\",\n                      runId: undefined,\n                      model: forwardedParameters?.model,\n                      output: event.content,\n                      latency: Date.now() - requestStartTime,\n                      timestamp: Date.now(),\n                      provider: \"agent\",\n                      isProgressiveChunk: true,\n                      agentName,\n                      nodeName,\n                    };\n\n                    // Use Promise to handle async logger without awaiting\n                    Promise.resolve()\n                      .then(() => {\n                        this.observability.hooks.handleResponse(progressiveData);\n                      })\n                      .catch((error) => {\n                        console.error(\"Error in progressive agent logging:\", error);\n                      });\n                  } catch (error) {\n                    console.error(\"Error preparing progressive agent log data:\", error);\n                  }\n                }\n              },\n            });\n\n            // Call the original callback with the event stream\n            await callback(eventStream$);\n          });\n        };\n      }\n\n      eventSource.stream(async (eventStream$) => {\n        from(stream).subscribe({\n          next: (event) => eventStream$.next(event),\n          error: (err) => {\n            console.error(\"Error in stream\", err);\n\n            // Log error with observability if enabled\n            if (this.observability?.enabled && publicApiKey) {\n              try {\n                const errorData: LLMErrorData = {\n                  threadId,\n                  runId: undefined,\n                  model: forwardedParameters?.model,\n                  error: err instanceof Error ? err : String(err),\n                  timestamp: Date.now(),\n                  latency: Date.now() - requestStartTime,\n                  provider: \"agent\",\n                  agentName,\n                  nodeName,\n                };\n\n                this.observability.hooks.handleError(errorData);\n              } catch (logError) {\n                console.error(\"Error logging agent error:\", logError);\n              }\n            }\n\n            eventStream$.error(err);\n            eventStream$.complete();\n          },\n          complete: () => eventStream$.complete(),\n        });\n      });\n\n      // Log final agent response when outputs are available\n      if (this.observability?.enabled && publicApiKey) {\n        outputMessagesPromise\n          .then((outputMessages) => {\n            const responseData: LLMResponseData = {\n              threadId,\n              runId: undefined,\n              model: forwardedParameters?.model,\n              // Use collected chunks for progressive mode or outputMessages for regular mode\n              output: this.observability.progressive ? streamedChunks : outputMessages,\n              latency: Date.now() - requestStartTime,\n              timestamp: Date.now(),\n              provider: \"agent\",\n              isFinalResponse: true,\n              agentName,\n              nodeName,\n            };\n\n            try {\n              this.observability.hooks.handleResponse(responseData);\n            } catch (logError) {\n              console.error(\"Error logging agent response:\", logError);\n            }\n          })\n          .catch((error) => {\n            console.error(\"Failed to get output messages for agent logging:\", error);\n          });\n      }\n\n      outputMessagesPromise\n        .then((outputMessages) => {\n          this.onAfterRequest?.({\n            threadId,\n            runId: undefined,\n            inputMessages: messages,\n            outputMessages,\n            properties: graphqlContext.properties,\n          });\n        })\n        .catch((_error) => {});\n\n      return {\n        threadId,\n        runId: undefined,\n        eventSource,\n        serverSideActions,\n        actionInputsWithoutAgents: allAvailableActions,\n      };\n    } catch (error) {\n      // Log error with observability if enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          const errorData: LLMErrorData = {\n            threadId,\n            runId: undefined,\n            model: forwardedParameters?.model,\n            error: error instanceof Error ? error : String(error),\n            timestamp: Date.now(),\n            latency: Date.now() - requestStartTime,\n            provider: \"agent\",\n            agentName,\n            nodeName,\n          };\n\n          await this.observability.hooks.handleError(errorData);\n        } catch (logError) {\n          console.error(\"Error logging agent error:\", logError);\n        }\n      }\n\n      console.error(\"Error getting response:\", error);\n      throw error;\n    }\n  }\n\n  private async getServerSideActions(request: CopilotRuntimeRequest): Promise<Action<any>[]> {\n    const { graphqlContext, messages: rawMessages, agentStates, url } = request;\n\n    // --- Standard Action Fetching (unchanged) ---\n    const inputMessages = convertGqlInputToMessages(rawMessages);\n    const langserveFunctions: Action<any>[] = [];\n    for (const chainPromise of this.langserve) {\n      try {\n        const chain = await chainPromise;\n        langserveFunctions.push(chain);\n      } catch (error) {\n        console.error(\"Error loading langserve chain:\", error);\n      }\n    }\n\n    const remoteEndpointDefinitions = this.remoteEndpointDefinitions.map(\n      (endpoint) => ({ ...endpoint, type: resolveEndpointType(endpoint) }) as EndpointDefinition,\n    );\n\n    const remoteActions = await setupRemoteActions({\n      remoteEndpointDefinitions,\n      graphqlContext,\n      messages: inputMessages,\n      agentStates,\n      frontendUrl: url,\n      agents: this.agents,\n    });\n\n    const configuredActions =\n      typeof this.actions === \"function\"\n        ? this.actions({ properties: graphqlContext.properties, url })\n        : this.actions;\n    // --- Standard Action Fetching (unchanged) ---\n\n    // +++ Dynamic MCP Action Fetching +++\n    const requestSpecificMCPActions: Action<any>[] = [];\n    if (this.createMCPClientImpl) {\n      // 1. Determine effective MCP endpoints for this request\n      const baseEndpoints = this.mcpServersConfig || [];\n      // Assuming frontend passes config via properties.mcpServers\n      const requestEndpoints = (graphqlContext.properties?.mcpServers ||\n        graphqlContext.properties?.mcpEndpoints ||\n        []) as MCPEndpointConfig[];\n\n      // Merge and deduplicate endpoints based on URL\n      const effectiveEndpointsMap = new Map<string, MCPEndpointConfig>();\n\n      // First add base endpoints (from runtime configuration)\n      [...baseEndpoints].forEach((ep) => {\n        if (ep && ep.endpoint) {\n          effectiveEndpointsMap.set(ep.endpoint, ep);\n        }\n      });\n\n      // Then add request endpoints (from frontend), which will override duplicates\n      [...requestEndpoints].forEach((ep) => {\n        if (ep && ep.endpoint) {\n          effectiveEndpointsMap.set(ep.endpoint, ep);\n        }\n      });\n\n      const effectiveEndpoints = Array.from(effectiveEndpointsMap.values());\n\n      // 2. Fetch/Cache actions for effective endpoints\n      for (const config of effectiveEndpoints) {\n        const endpointUrl = config.endpoint;\n        let actionsForEndpoint: Action<any>[] | undefined = this.mcpActionCache.get(endpointUrl);\n\n        if (!actionsForEndpoint) {\n          // Not cached, fetch now\n          let client: MCPClient | null = null;\n          try {\n            client = await this.createMCPClientImpl(config);\n            const tools = await client.tools();\n            actionsForEndpoint = convertMCPToolsToActions(tools, endpointUrl);\n            this.mcpActionCache.set(endpointUrl, actionsForEndpoint); // Store in cache\n          } catch (error) {\n            console.error(\n              `MCP: Failed to fetch tools from endpoint ${endpointUrl}. Skipping. Error:`,\n              error,\n            );\n            actionsForEndpoint = []; // Assign empty array on error to prevent re-fetching constantly\n            this.mcpActionCache.set(endpointUrl, actionsForEndpoint); // Cache the failure (empty array)\n          }\n        }\n        requestSpecificMCPActions.push(...(actionsForEndpoint || []));\n      }\n    }\n    // --- Dynamic MCP Action Fetching ---\n\n    // Combine all action sources, including the dynamically fetched MCP actions\n    return [\n      ...configuredActions,\n      ...langserveFunctions,\n      ...remoteActions,\n      ...requestSpecificMCPActions,\n    ];\n  }\n\n  // Add helper method to detect provider\n  private detectProvider(serviceAdapter: CopilotServiceAdapter): string | undefined {\n    const adapterName = serviceAdapter.constructor.name;\n    if (adapterName.includes(\"OpenAI\")) return \"openai\";\n    if (adapterName.includes(\"Anthropic\")) return \"anthropic\";\n    if (adapterName.includes(\"Google\")) return \"google\";\n    if (adapterName.includes(\"Groq\")) return \"groq\";\n    if (adapterName.includes(\"LangChain\")) return \"langchain\";\n    return undefined;\n  }\n}\n\nexport function flattenToolCallsNoDuplicates(toolsByPriority: ActionInput[]): ActionInput[] {\n  let allTools: ActionInput[] = [];\n  const allToolNames: string[] = [];\n  for (const tool of toolsByPriority) {\n    if (!allToolNames.includes(tool.name)) {\n      allTools.push(tool);\n      allToolNames.push(tool.name);\n    }\n  }\n  return allTools;\n}\n\n// The two functions below are \"factory functions\", meant to create the action objects that adhere to the expected interfaces\nexport function copilotKitEndpoint(config: Omit<CopilotKitEndpoint, \"type\">): CopilotKitEndpoint {\n  return {\n    ...config,\n    type: EndpointType.CopilotKit,\n  };\n}\n\nexport function langGraphPlatformEndpoint(\n  config: Omit<LangGraphPlatformEndpoint, \"type\">,\n): LangGraphPlatformEndpoint {\n  return {\n    ...config,\n    type: EndpointType.LangGraphPlatform,\n  };\n}\n\nexport function resolveEndpointType(endpoint: EndpointDefinition) {\n  if (!endpoint.type) {\n    if (\"deploymentUrl\" in endpoint && \"agents\" in endpoint) {\n      return EndpointType.LangGraphPlatform;\n    } else {\n      return EndpointType.CopilotKit;\n    }\n  }\n\n  return endpoint.type;\n}\n", "import {\n  ActionExecutionMessageInput,\n  ResultMessageInput,\n  TextMessageInput,\n  AgentStateMessageInput,\n  ImageMessageInput,\n} from \"../../inputs/message.input\";\nimport { BaseMessageInput } from \"../base\";\nimport { MessageRole } from \"../enums\";\n\nexport type MessageType =\n  | \"TextMessage\"\n  | \"ActionExecutionMessage\"\n  | \"ResultMessage\"\n  | \"AgentStateMessage\"\n  | \"ImageMessage\";\n\nexport class Message extends BaseMessageInput {\n  type: MessageType;\n\n  isTextMessage(): this is TextMessage {\n    return this.type === \"TextMessage\";\n  }\n\n  isActionExecutionMessage(): this is ActionExecutionMessage {\n    return this.type === \"ActionExecutionMessage\";\n  }\n\n  isResultMessage(): this is ResultMessage {\n    return this.type === \"ResultMessage\";\n  }\n\n  isAgentStateMessage(): this is AgentStateMessage {\n    return this.type === \"AgentStateMessage\";\n  }\n\n  isImageMessage(): this is ImageMessage {\n    return this.type === \"ImageMessage\";\n  }\n}\n\nexport class TextMessage extends Message implements TextMessageInput {\n  type: MessageType = \"TextMessage\";\n  content: string;\n  role: MessageRole;\n  parentMessageId?: string;\n}\n\nexport class ActionExecutionMessage\n  extends Message\n  implements Omit<ActionExecutionMessageInput, \"arguments\" | \"scope\">\n{\n  type: MessageType = \"ActionExecutionMessage\";\n  name: string;\n  arguments: Record<string, any>;\n  parentMessageId?: string;\n}\n\nexport class ResultMessage extends Message implements ResultMessageInput {\n  type: MessageType = \"ResultMessage\";\n  actionExecutionId: string;\n  actionName: string;\n  result: string;\n\n  static encodeResult(\n    result: any,\n    error?: { code: string; message: string } | string | Error,\n  ): string {\n    const errorObj = error\n      ? typeof error === \"string\"\n        ? { code: \"ERROR\", message: error }\n        : error instanceof Error\n          ? { code: \"ERROR\", message: error.message }\n          : error\n      : undefined;\n\n    if (errorObj) {\n      return JSON.stringify({\n        error: errorObj,\n        result: result || \"\",\n      });\n    }\n    if (result === undefined) {\n      return \"\";\n    }\n    return typeof result === \"string\" ? result : JSON.stringify(result);\n  }\n\n  static decodeResult(result: string): {\n    error?: { code: string; message: string };\n    result: string;\n  } {\n    if (!result) {\n      return { result: \"\" };\n    }\n    try {\n      const parsed = JSON.parse(result);\n      if (parsed && typeof parsed === \"object\") {\n        if (\"error\" in parsed) {\n          return {\n            error: parsed.error,\n            result: parsed.result || \"\",\n          };\n        }\n        return { result: JSON.stringify(parsed) };\n      }\n      return { result };\n    } catch (e) {\n      return { result };\n    }\n  }\n\n  hasError(): boolean {\n    try {\n      const { error } = ResultMessage.decodeResult(this.result);\n      return !!error;\n    } catch {\n      return false;\n    }\n  }\n\n  getError(): { code: string; message: string } | undefined {\n    try {\n      const { error } = ResultMessage.decodeResult(this.result);\n      return error;\n    } catch {\n      return undefined;\n    }\n  }\n}\n\nexport class AgentStateMessage extends Message implements Omit<AgentStateMessageInput, \"state\"> {\n  type: MessageType = \"AgentStateMessage\";\n  threadId: string;\n  agentName: string;\n  nodeName: string;\n  runId: string;\n  active: boolean;\n  role: MessageRole;\n  state: any;\n  running: boolean;\n}\n\nexport class ImageMessage extends Message implements ImageMessageInput {\n  type: MessageType = \"ImageMessage\";\n  format: string;\n  bytes: string;\n  role: MessageRole;\n  parentMessageId?: string;\n}\n", "import {\n  FailedResponseStatus,\n  FailedResponseStatusReason,\n} from \"../graphql/types/response-status.type\";\n\nexport class GuardrailsValidationFailureResponse extends FailedResponseStatus {\n  reason = FailedResponseStatusReason.GUARDRAILS_VALIDATION_FAILED;\n  declare details: {\n    guardrailsReason: string;\n  };\n\n  constructor({ guardrailsReason }) {\n    super();\n    this.details = {\n      guardrailsReason,\n    };\n  }\n}\n\nexport class MessageStreamInterruptedResponse extends FailedResponseStatus {\n  reason = FailedResponseStatusReason.MESSAGE_STREAM_INTERRUPTED;\n  declare details: {\n    messageId: string;\n    description: string;\n  };\n\n  constructor({ messageId }: { messageId: string }) {\n    super();\n    this.details = {\n      messageId,\n      description: \"Check the message for mode details\",\n    };\n  }\n}\n\nexport class UnknownErrorResponse extends FailedResponseStatus {\n  reason = FailedResponseStatusReason.UNKNOWN_ERROR;\n  declare details: {\n    description?: string;\n  };\n\n  constructor({ description }: { description?: string }) {\n    super();\n    this.details = {\n      description,\n    };\n  }\n}\n", "import { Field, ObjectType } from \"type-graphql\";\n\n@ObjectType()\nexport class Agent {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String)\n  description?: string;\n}\n\n@ObjectType()\nexport class AgentsResponse {\n  @Field(() => [Agent])\n  agents: Agent[];\n}\n", "import createPinoLogger from \"pino\";\nimport pretty from \"pino-pretty\";\n\nexport type LogLevel = \"debug\" | \"info\" | \"warn\" | \"error\";\n\nexport type CopilotRuntimeLogger = ReturnType<typeof createLogger>;\n\nexport function createLogger(options?: { level?: LogLevel; component?: string }) {\n  const { level, component } = options || {};\n  const stream = pretty({ colorize: true });\n\n  const logger = createPinoLogger(\n    {\n      level: process.env.LOG_LEVEL || level || \"error\",\n      redact: {\n        paths: [\"pid\", \"hostname\"],\n        remove: true,\n      },\n    },\n    stream,\n  );\n\n  if (component) {\n    return logger.child({ component });\n  } else {\n    return logger;\n  }\n}\n", "import { Arg, Resolver } from \"type-graphql\";\nimport { Ctx } from \"type-graphql\";\nimport { Query } from \"type-graphql\";\nimport { LoadAgentStateResponse } from \"../types/load-agent-state-response.type\";\nimport type { GraphQLContext } from \"../../lib/integrations\";\nimport { LoadAgentStateInput } from \"../inputs/load-agent-state.input\";\n\n@Resolver(() => LoadAgentStateResponse)\nexport class StateResolver {\n  @Query(() => LoadAgentStateResponse)\n  async loadAgentState(@Ctx() ctx: GraphQLContext, @Arg(\"data\") data: LoadAgentStateInput) {\n    const agents = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);\n    const agent = agents.find((agent) => agent.name === data.agentName);\n\n    if (!agent) {\n      return {\n        threadId: data.threadId || \"\",\n        threadExists: false,\n        state: JSON.stringify({}),\n        messages: JSON.stringify([]),\n      };\n    }\n\n    const state = await ctx._copilotkit.runtime.loadAgentState(ctx, data.threadId, data.agentName);\n\n    return state;\n  }\n}\n", "import { Field, ObjectType } from \"type-graphql\";\nimport { BaseMessageOutput } from \"./copilot-response.type\";\n\n@ObjectType()\nexport class LoadAgentStateResponse {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => Boolean)\n  threadExists: boolean;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => String)\n  messages: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class LoadAgentStateInput {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => String)\n  agentName: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,0BAAAA,SAAA;AAAA,IAAAA,QAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,UAAY;AAAA,MACZ,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,eAAiB;AAAA,QACf,QAAU;AAAA,MACZ;AAAA,MACA,SAAW;AAAA,MACX,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,SAAW;AAAA,QACT,KAAK;AAAA,MACP;AAAA,MACA,OAAS;AAAA,MACT,SAAW;AAAA,MACX,SAAW;AAAA,QACT,OAAS;AAAA,QACT,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,eAAe;AAAA,QACf,OAAS;AAAA,QACT,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAmB;AAAA,QACjB,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,QAAU;AAAA,QACV,wBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,YAAc;AAAA,QACd,sBAAsB;AAAA,MACxB;AAAA,MACA,cAAgB;AAAA,QACd,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qCAAqC;AAAA,QACrC,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,WAAa;AAAA,QACb,QAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAQ;AAAA,QACR,eAAe;AAAA,QACf,oBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,KAAO;AAAA,MACT;AAAA,MACA,kBAAoB;AAAA,QAClB,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AChGA;;;;;IAAAC,uBAA2B;;;ACC3B,IAAAC,wBAAgC;;;ACDhC,IAAAC,wBAAoD;AACpD,IAAAC,eAYO;;;ACbP,IAAAC,wBAAiC;;;ACAjC,IAAAC,uBAAiC;;;ACAjC,0BAAiC;;UAErBC,cAAAA;;;;;;GAAAA,gBAAAA,cAAAA,CAAAA,EAAAA;;UAQAC,qBAAAA;;;;;;GAAAA,uBAAAA,qBAAAA,CAAAA,EAAAA;;UAQAC,0BAAAA;;;;GAAAA,4BAAAA,0BAAAA,CAAAA,EAAAA;IAMZC,sCAAiBH,aAAa;EAC5BI,MAAM;EACNC,aAAa;AACf,CAAA;IAEAF,sCAAiBF,oBAAoB;EACnCG,MAAM;EACNC,aAAa;AACf,CAAA;IAEAF,sCAAiBD,yBAAyB;EACxCE,MAAM;EACNC,aAAa;AACf,CAAA;;;ACrCA,IAAAC,uBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,mBAAN,MAAMA;EAEXC;EAGAC;AACF;AANaF;;MACVG,4BAAM,MAAMC,MAAAA;;GADFJ,iBAAAA,WAAAA,MAAAA,MAAAA;;MAIVG,4BAAM,MAAME,IAAAA;qCACF,SAAA,cAAA,SAAA,IAAA;GALAL,iBAAAA,WAAAA,aAAAA,MAAAA;AAAAA,mBAAAA,aAAAA;MADZM,gCAAAA;GACYN,gBAAAA;;;;;;;;;;;;;;;;;;;AFIN,IAAMO,eAAN,cAA2BC,iBAAAA;EAEhCC;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AAfaN;;MACVO,4BAAM,MAAMC,kBAAkB;IAAEC,UAAU;EAAK,CAAA;sCAClC,qBAAA,cAAA,SAAA,gBAAA;GAFHT,aAAAA,WAAAA,eAAAA,MAAAA;;MAIVO,4BAAM,MAAMG,6BAA6B;IAAED,UAAU;EAAK,CAAA;sCAClC,gCAAA,cAAA,SAAA,2BAAA;GALdT,aAAAA,WAAAA,0BAAAA,MAAAA;;MAOVO,4BAAM,MAAMI,oBAAoB;IAAEF,UAAU;EAAK,CAAA;sCAClC,uBAAA,cAAA,SAAA,kBAAA;GARLT,aAAAA,WAAAA,iBAAAA,MAAAA;;MAUVO,4BAAM,MAAMK,wBAAwB;IAAEH,UAAU;EAAK,CAAA;sCAClC,2BAAA,cAAA,SAAA,sBAAA;GAXTT,aAAAA,WAAAA,qBAAAA,MAAAA;;MAaVO,4BAAM,MAAMM,mBAAmB;IAAEJ,UAAU;EAAK,CAAA;sCAClC,sBAAA,cAAA,SAAA,iBAAA;GAdJT,aAAAA,WAAAA,gBAAAA,MAAAA;AAAAA,eAAAA,cAAAA;MADZc,gCAAAA;GACYd,YAAAA;AAkBN,IAAMQ,mBAAN,MAAMA;EAEXO;EAGAC;EAGAC;AACF;AATaT;;MACVD,4BAAM,MAAMW,MAAAA;;GADFV,iBAAAA,WAAAA,WAAAA,MAAAA;;MAIVD,4BAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAJ3BD,iBAAAA,WAAAA,mBAAAA,MAAAA;;MAOVD,4BAAM,MAAMY,WAAAA;sCACP,gBAAA,cAAA,SAAA,WAAA;GARKX,iBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,mBAAAA,cAAAA;MADZM,gCAAAA;GACYN,gBAAAA;AAYN,IAAME,8BAAN,MAAMA;EAEXU;EAGAC;EAGAL;EAMAM;AACF;AAfaZ;;MACVH,4BAAM,MAAMW,MAAAA;;GADFR,4BAAAA,WAAAA,QAAAA,MAAAA;;MAIVH,4BAAM,MAAMW,MAAAA;;GAJFR,4BAAAA,WAAAA,aAAAA,MAAAA;;MAOVH,4BAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAP3BC,4BAAAA,WAAAA,mBAAAA,MAAAA;;MAUVH,4BAAM,MAAMW,QAAQ;IACnBT,UAAU;IACVc,mBAAmB;EACrB,CAAA;sCACQ,WAAA,cAAA,SAAA,MAAA;GAdGb,4BAAAA,WAAAA,SAAAA,MAAAA;AAAAA,8BAAAA,cAAAA;MADZI,gCAAAA;GACYJ,2BAAAA;AAkBN,IAAMC,qBAAN,MAAMA;EAEXa;EAGAC;EAGAT;EAGAU;AACF;AAZaf;;MACVJ,4BAAM,MAAMW,MAAAA;;GADFP,mBAAAA,WAAAA,qBAAAA,MAAAA;;MAIVJ,4BAAM,MAAMW,MAAAA;;GAJFP,mBAAAA,WAAAA,cAAAA,MAAAA;;MAOVJ,4BAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAP3BE,mBAAAA,WAAAA,mBAAAA,MAAAA;;MAUVJ,4BAAM,MAAMW,MAAAA;;GAVFP,mBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,qBAAAA,cAAAA;MADZG,gCAAAA;GACYH,kBAAAA;AAeN,IAAMC,yBAAN,MAAMA;EAEXe;EAGAC;EAGAX;EAGAY;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AAxBarB;;MACVL,4BAAM,MAAMW,MAAAA;;GADFN,uBAAAA,WAAAA,YAAAA,MAAAA;;MAIVL,4BAAM,MAAMW,MAAAA;;GAJFN,uBAAAA,WAAAA,aAAAA,MAAAA;;MAOVL,4BAAM,MAAMY,WAAAA;sCACP,gBAAA,cAAA,SAAA,WAAA;GARKP,uBAAAA,WAAAA,QAAAA,MAAAA;;MAUVL,4BAAM,MAAMW,MAAAA;;GAVFN,uBAAAA,WAAAA,SAAAA,MAAAA;;MAaVL,4BAAM,MAAM2B,OAAAA;;GAbFtB,uBAAAA,WAAAA,WAAAA,MAAAA;;MAgBVL,4BAAM,MAAMW,MAAAA;;GAhBFN,uBAAAA,WAAAA,YAAAA,MAAAA;;MAmBVL,4BAAM,MAAMW,MAAAA;;GAnBFN,uBAAAA,WAAAA,SAAAA,MAAAA;;MAsBVL,4BAAM,MAAM2B,OAAAA;;GAtBFtB,uBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,yBAAAA,cAAAA;MADZE,gCAAAA;GACYF,sBAAAA;AA2BN,IAAMC,oBAAN,MAAMA;EAEXsB;EAGAC;EAGApB;EAGAC;AACF;AAZaJ;;MACVN,4BAAM,MAAMW,MAAAA;;GADFL,kBAAAA,WAAAA,UAAAA,MAAAA;;MAIVN,4BAAM,MAAMW,MAAAA;;GAJFL,kBAAAA,WAAAA,SAAAA,MAAAA;;MAOVN,4BAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAP3BI,kBAAAA,WAAAA,mBAAAA,MAAAA;;MAUVN,4BAAM,MAAMY,WAAAA;sCACP,gBAAA,cAAA,SAAA,WAAA;GAXKN,kBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,oBAAAA,cAAAA;MADZC,gCAAAA;GACYD,iBAAAA;;;AGjGb,IAAAwB,uBAAiC;;;ACAjC,IAAAC,uBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,cAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;AACF;AAZaJ;;MACVK,4BAAM,MAAMC,MAAAA;;GADFN,YAAAA,WAAAA,QAAAA,MAAAA;;MAIVK,4BAAM,MAAMC,MAAAA;;GAJFN,YAAAA,WAAAA,eAAAA,MAAAA;;MAOVK,4BAAM,MAAMC,MAAAA;;GAPFN,YAAAA,WAAAA,cAAAA,MAAAA;;MAUVK,4BAAM,MAAME,yBAAyB;IAAEC,UAAU;EAAK,CAAA;sCAC3C,4BAAA,cAAA,SAAA,uBAAA;GAXDR,YAAAA,WAAAA,aAAAA,MAAAA;AAAAA,cAAAA,cAAAA;MADZS,gCAAAA;GACYT,WAAAA;;;;;;;;;;;;;;;;;;;ADCN,IAAMU,gBAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;MACVI,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAD3BN,cAAAA,WAAAA,2BAAAA,MAAAA;;MAIVI,4BAAM,MAAM;IAACG;GAAY;;GAJfP,cAAAA,WAAAA,WAAAA,MAAAA;;MAOVI,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BN,cAAAA,WAAAA,OAAAA,MAAAA;AAAAA,gBAAAA,cAAAA;MADZQ,gCAAAA;GACYR,aAAAA;;;AEJb,IAAAS,uBAAiC;;;ACAjC,IAAAC,uBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,sBAAN,MAAMA;EAEXC,YAAuB,CAAA;EAGvBC,WAAsB,CAAA;AACxB;AANaF;;MACVG,4BAAM,MAAM;IAACC;KAAS;IAAEC,UAAU;EAAK,CAAA;;GAD7BL,oBAAAA,WAAAA,aAAAA,MAAAA;;MAIVG,4BAAM,MAAM;IAACC;KAAS;IAAEC,UAAU;EAAK,CAAA;;GAJ7BL,oBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,sBAAAA,cAAAA;MADZM,gCAAAA;GACYN,mBAAAA;AASN,IAAMO,kBAAN,MAAMA;EAEXC;AACF;AAHaD;;MACVJ,4BAAM,MAAMH,qBAAqB;IAAEK,UAAU;EAAM,CAAA;sCAC9B,wBAAA,cAAA,SAAA,mBAAA;GAFXE,gBAAAA,WAAAA,wBAAAA,MAAAA;AAAAA,kBAAAA,cAAAA;MADZD,gCAAAA;GACYC,eAAAA;;;;;;;;;;;;;;;;;;;ADRN,IAAME,aAAN,MAAMA;EAEXC;AACF;AAHaD;;MACVE,4BAAM,MAAMC,iBAAiB;IAAEC,UAAU;EAAK,CAAA;sCAClC,oBAAA,cAAA,SAAA,eAAA;GAFFJ,WAAAA,WAAAA,cAAAA,MAAAA;AAAAA,aAAAA,cAAAA;MADZK,gCAAAA;GACYL,UAAAA;;;AEJb,IAAAM,uBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,2BAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AAlBaN;;MACVO,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAD3BT,yBAAAA,WAAAA,SAAAA,MAAAA;;MAIVO,4BAAM,MAAMG,QAAQ;IAAED,UAAU;EAAK,CAAA;;GAJ3BT,yBAAAA,WAAAA,aAAAA,MAAAA;;MAOVO,4BAAM,MAAM;IAACC;KAAS;IAAEC,UAAU;EAAK,CAAA;;GAP7BT,yBAAAA,WAAAA,QAAAA,MAAAA;;MAUVO,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;sCACzB,WAAA,cAAA,SAAA,MAAA;GAXFT,yBAAAA,WAAAA,cAAAA,MAAAA;;MAaVO,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAb3BT,yBAAAA,WAAAA,0BAAAA,MAAAA;;MAgBVO,4BAAM,MAAMG,QAAQ;IAAED,UAAU;EAAK,CAAA;;GAhB3BT,yBAAAA,WAAAA,eAAAA,MAAAA;AAAAA,2BAAAA,cAAAA;MADZW,gCAAAA;GACYX,wBAAAA;;;ACHb,IAAAY,uBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,oBAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;MACVI,4BAAM,MAAMC,MAAAA;;GADFL,kBAAAA,WAAAA,aAAAA,MAAAA;;MAIVI,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAJ3BN,kBAAAA,WAAAA,YAAAA,MAAAA;;MAOVI,4BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BN,kBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,oBAAAA,cAAAA;MADZO,gCAAAA;GACYP,iBAAAA;;;ACHb,IAAAQ,wBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,kBAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;MACVI,6BAAM,MAAMC,MAAAA;;GADFL,gBAAAA,WAAAA,aAAAA,MAAAA;;MAIVI,6BAAM,MAAMC,MAAAA;;GAJFL,gBAAAA,WAAAA,SAAAA,MAAAA;;MAOVI,6BAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BN,gBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,kBAAAA,cAAAA;MADZO,iCAAAA;GACYP,eAAAA;;;ACHb,IAAAQ,wBAAiC;;;;;;;;;;;;;;;;;AAQ1B,IAAMC,kBAAN,MAAMA;EAEXC;AACF;AAHaD;;MACVE,6BAAM,MAAMC,4BAA4B;IAAEC,UAAU;EAAK,CAAA;uCACrC,+BAAA,cAAA,SAAA,0BAAA;GAFVJ,gBAAAA,WAAAA,sBAAAA,MAAAA;AAAAA,kBAAAA,eAAAA;MADZK,iCAAAA;GACYL,eAAAA;AAMN,IAAMG,6BAAN,MAAMA;EAEXG;EAGAC;AACF;AANaJ;;MACVD,6BAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAD3BD,2BAAAA,WAAAA,SAAAA,MAAAA;;MAIVD,6BAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAJ3BD,2BAAAA,WAAAA,YAAAA,MAAAA;AAAAA,6BAAAA,eAAAA;MADZE,iCAAAA;GACYF,0BAAAA;;;ACdb,IAAAM,wBAAiC;;;ACAjC,IAAAC,wBAAoF;;;ACApF,IAAAC,wBAAiD;;;ACAjD,IAAAC,wBAAqE;;;;;;;;;;;;;;;;;;UAEzDC,oBAAAA;;;;GAAAA,sBAAAA,oBAAAA,CAAAA,EAAAA;IAMZC,wCAAiBD,mBAAmB;EAClCE,MAAM;AACR,CAAA;AAEA,IACMC,oBADN,6BACMA,mBAAAA;EAEJC;AACF,GAJA;;MAEGC,6BAAM,MAAML,iBAAAA;;GADTG,kBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,oBAAAA,eAAAA;MADLG,kCAAAA;GACKH,iBAAAA;AAMC,IAAMI,uBAAN,cAAmCJ,kBAAAA;EACxCC,OAAAA;AACF;AAFaG;AAAAA,uBAAAA,eAAAA;MADZD,kCAAAA;GACYC,oBAAAA;AAKN,IAAMC,uBAAN,cAAmCL,kBAAAA;EACxCC,OAAAA;AACF;AAFaI;AAAAA,uBAAAA,eAAAA;MADZF,kCAAAA;GACYE,oBAAAA;AAKN,IAAMC,sBAAN,cAAkCN,kBAAAA;EACvCC,OAAAA;EAGAM;AACF;AALaD;;MAGVJ,6BAAM,MAAMM,MAAAA;;GAHFF,oBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,sBAAAA,eAAAA;MADZH,kCAAAA;GACYG,mBAAAA;AAON,IAAMG,yBAAqBC,uCAAgB;EAChDX,MAAM;EACNY,OAAO,MAAM;IAACP;IAAsBC;IAAsBC;;AAC5D,CAAA;;;ACvCA,6BAA4B;AAC5B,IAAAM,wBAAoF;;;;;;;;;;;;;;;;;;UAExEC,qBAAAA;;;;GAAAA,uBAAAA,qBAAAA,CAAAA,EAAAA;IAMZC,wCAAiBD,oBAAoB;EACnCE,MAAM;AACR,CAAA;AAeA,IAAeC,qBAAf,6BAAeA,oBAAAA;EAEbC;AACF,GAHA;;MACGC,6BAAM,MAAML,kBAAAA;;GADAG,mBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,qBAAAA,eAAAA;MAbdG,qCAAc;IACbC,YAAYC,OAAK;AACf,UAAIA,MAAMJ,SAAI,WAAiC;AAC7C,eAAOK;MACT,WAAWD,MAAMJ,SAAI,UAAgC;AACnD,eAAOM;MACT,WAAWF,MAAMJ,SAAI,WAAiC;AACpD,eAAOO;MACT;AACA,aAAOC;IACT;EACF,CAAA;MACCC,kCAAAA;GACcV,kBAAAA;AAMR,IAAMQ,wBAAN,cAAoCR,mBAAAA;EACzCC,OAAAA;AACF;AAFaO;AAAAA,wBAAAA,eAAAA;MADZE,kCAAW;IAAEC,YAAYX;EAAmB,CAAA;GAChCQ,qBAAAA;AAKN,IAAMF,wBAAN,cAAoCN,mBAAAA;EACzCC,OAAAA;AACF;AAFaK;AAAAA,wBAAAA,eAAAA;MADZI,kCAAW;IAAEC,YAAYX;EAAmB,CAAA;GAChCM,qBAAAA;;UAIDM,6BAAAA;;;;GAAAA,+BAAAA,6BAAAA,CAAAA,EAAAA;IAMZd,wCAAiBc,4BAA4B;EAC3Cb,MAAM;AACR,CAAA;AAGO,IAAMQ,uBAAN,cAAmCP,mBAAAA;EACxCC,OAAAA;EAGAY;EAGAC,UAAgC;AAClC;AARaP;;MAGVL,6BAAM,MAAMU,0BAAAA;;GAHFL,qBAAAA,WAAAA,UAAAA,MAAAA;;MAMVL,6BAAM,MAAMa,oCAAa;IAAEC,UAAU;EAAK,CAAA;uCACjC,WAAA,cAAA,SAAA,MAAA;GAPCT,qBAAAA,WAAAA,WAAAA,MAAAA;AAAAA,uBAAAA,eAAAA;MADZG,kCAAW;IAAEC,YAAYX;EAAmB,CAAA;GAChCO,oBAAAA;AAUN,IAAMU,0BAAsBC,uCAAgB;EACjDnB,MAAM;EACNoB,OAAO,MAAM;IAACX;IAAuBF;IAAuBC;;AAC9D,CAAA;;;ACjEA,IAAAa,wBAAkC;;;;;;;;;;;;;;;;;AAU3B,IAAMC,qBAAN,MAAMA;EAEXC;AACF;AAHaD;;MACVE,6BAAM,MAAMC,+BAA+B;IAAEC,UAAU;EAAK,CAAA;uCACxC,kCAAA,cAAA,SAAA,6BAAA;GAFVJ,mBAAAA,WAAAA,sBAAAA,MAAAA;AAAAA,qBAAAA,eAAAA;MADZK,kCAAAA;GACYL,kBAAAA;AAMN,IAAMG,gCAAN,MAAMA;EAEXG;EAGAC;AACF;AANaJ;;MACVD,6BAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAD3BD,8BAAAA,WAAAA,SAAAA,MAAAA;;MAIVD,6BAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAJ3BD,8BAAAA,WAAAA,YAAAA,MAAAA;AAAAA,gCAAAA,eAAAA;MADZE,kCAAAA;GACYF,6BAAAA;;;;;;;;;;;;;;;;;;;AHON,IAAeM,oBAAf,MAAeA;EAEpBC;EAGAC;EAGAC;AACF;AATsBH;;MACnBI,6BAAM,MAAMC,MAAAA;;GADOL,kBAAAA,WAAAA,MAAAA,MAAAA;;MAInBI,6BAAM,MAAME,IAAAA;uCACF,SAAA,cAAA,SAAA,IAAA;GALSN,kBAAAA,WAAAA,aAAAA,MAAAA;;MAOnBI,6BAAM,MAAMG,kBAAAA;;GAPOP,kBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,oBAAAA,eAAAA;MAhBrBQ,qCAAc;IACbC,YAAYC,OAAK;AACf,UAAIA,MAAMC,eAAe,SAAA,GAAY;AACnC,eAAOC;MACT,WAAWF,MAAMC,eAAe,MAAA,GAAS;AACvC,eAAOE;MACT,WAAWH,MAAMC,eAAe,QAAA,GAAW;AACzC,eAAOG;MACT,WAAWJ,MAAMC,eAAe,OAAA,GAAU;AACxC,eAAOI;MACT,WAAWL,MAAMC,eAAe,QAAA,KAAaD,MAAMC,eAAe,OAAA,GAAU;AAC1E,eAAOK;MACT;AACA,aAAOC;IACT;EACF,CAAA;GACsBjB,iBAAAA;AAYf,IAAMY,oBAAN,MAAMA;EAEXM;EAGAC;EAGAC;AACF;AATaR;;MACVR,6BAAM,MAAMiB,WAAAA;uCACP,gBAAA,cAAA,SAAA,WAAA;GAFKT,kBAAAA,WAAAA,QAAAA,MAAAA;;MAIVR,6BAAM,MAAM;IAACC;GAAO;;GAJVO,kBAAAA,WAAAA,WAAAA,MAAAA;;MAOVR,6BAAM,MAAMC,QAAQ;IAAEiB,UAAU;EAAK,CAAA;;GAP3BV,kBAAAA,WAAAA,mBAAAA,MAAAA;AAAAA,oBAAAA,eAAAA;MADZW,kCAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/BY,iBAAAA;AAYN,IAAMC,+BAAN,MAAMA;EAEXY;EAMAC;EAGAC;EAGAP;AACF;AAfaP;;MACVT,6BAAM,MAAMC,MAAAA;;GADFQ,6BAAAA,WAAAA,QAAAA,MAAAA;;MAIVT,6BAAM,MAAMC,QAAQ;IACnBiB,UAAU;IACVM,mBAAmB;EACrB,CAAA;;GAPWf,6BAAAA,WAAAA,SAAAA,MAAAA;;MAUVT,6BAAM,MAAM;IAACC;GAAO;;GAVVQ,6BAAAA,WAAAA,aAAAA,MAAAA;;MAaVT,6BAAM,MAAMC,QAAQ;IAAEiB,UAAU;EAAK,CAAA;;GAb3BT,6BAAAA,WAAAA,mBAAAA,MAAAA;AAAAA,+BAAAA,eAAAA;MADZU,kCAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/Ba,4BAAAA;AAkBN,IAAMC,sBAAN,MAAMA;EAEXe;EAGAC;EAGAC;AACF;AATajB;;MACVV,6BAAM,MAAMC,MAAAA;;GADFS,oBAAAA,WAAAA,qBAAAA,MAAAA;;MAIVV,6BAAM,MAAMC,MAAAA;;GAJFS,oBAAAA,WAAAA,cAAAA,MAAAA;;MAOVV,6BAAM,MAAMC,MAAAA;;GAPFS,oBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,sBAAAA,eAAAA;MADZS,kCAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/Bc,mBAAAA;AAYN,IAAMC,0BAAN,MAAMA;EAEXiB;EAGAC;EAGAC;EAGAC;EAGAC;EAGAlB;EAGAmB;EAGAC;AACF;AAxBavB;;MACVX,6BAAM,MAAMC,MAAAA;;GADFU,wBAAAA,WAAAA,YAAAA,MAAAA;;MAIVX,6BAAM,MAAMC,MAAAA;;GAJFU,wBAAAA,WAAAA,aAAAA,MAAAA;;MAOVX,6BAAM,MAAMC,MAAAA;;GAPFU,wBAAAA,WAAAA,YAAAA,MAAAA;;MAUVX,6BAAM,MAAMC,MAAAA;;GAVFU,wBAAAA,WAAAA,SAAAA,MAAAA;;MAaVX,6BAAM,MAAMmC,OAAAA;;GAbFxB,wBAAAA,WAAAA,UAAAA,MAAAA;;MAgBVX,6BAAM,MAAMiB,WAAAA;uCACP,gBAAA,cAAA,SAAA,WAAA;GAjBKN,wBAAAA,WAAAA,QAAAA,MAAAA;;MAmBVX,6BAAM,MAAMC,MAAAA;;GAnBFU,wBAAAA,WAAAA,SAAAA,MAAAA;;MAsBVX,6BAAM,MAAMmC,OAAAA;;GAtBFxB,wBAAAA,WAAAA,WAAAA,MAAAA;AAAAA,0BAAAA,eAAAA;MADZQ,kCAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/Be,uBAAAA;AA2BN,IAAMC,qBAAN,MAAMA;EAEXwB;EAGAC;EAGAvB;EAGAE;AACF;AAZaJ;;MACVZ,6BAAM,MAAMC,MAAAA;;GADFW,mBAAAA,WAAAA,UAAAA,MAAAA;;MAIVZ,6BAAM,MAAMC,MAAAA;;GAJFW,mBAAAA,WAAAA,SAAAA,MAAAA;;MAOVZ,6BAAM,MAAMiB,WAAAA;uCACP,gBAAA,cAAA,SAAA,WAAA;GARKL,mBAAAA,WAAAA,QAAAA,MAAAA;;MAUVZ,6BAAM,MAAMC,QAAQ;IAAEiB,UAAU;EAAK,CAAA;;GAV3BN,mBAAAA,WAAAA,mBAAAA,MAAAA;AAAAA,qBAAAA,eAAAA;MADZO,kCAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/BgB,kBAAAA;AAeN,IAAM0B,kBAAN,MAAMA;EAEXV;EAGA7B;EAGAgC;EAGAQ;EAGAC;EAGAC;AACF;AAlBaH;;MACVtC,6BAAM,MAAMC,MAAAA;;GADFqC,gBAAAA,WAAAA,YAAAA,MAAAA;;MAIVtC,6BAAM,MAAM0C,mBAAAA;;GAJFJ,gBAAAA,WAAAA,UAAAA,MAAAA;;MAOVtC,6BAAM;IAAEkB,UAAU;EAAK,CAAA;;GAPboB,gBAAAA,WAAAA,SAAAA,MAAAA;;MAUVtC,6BAAM,MAAM;IAACJ;GAAkB;;GAVrB0C,gBAAAA,WAAAA,YAAAA,MAAAA;;MAaVtC,6BAAM,MAAM2C,oBAAoB;IAAEzB,UAAU;EAAK,CAAA;uCACrC,uBAAA,cAAA,SAAA,kBAAA;GAdFoB,gBAAAA,WAAAA,cAAAA,MAAAA;;MAgBVtC,6BAAM,MAAM;IAAC4C;KAAgB;IAAE1B,UAAU;EAAK,CAAA;;GAhBpCoB,gBAAAA,WAAAA,cAAAA,MAAAA;AAAAA,kBAAAA,eAAAA;MADZnB,kCAAAA;GACYmB,eAAAA;;;;;;;;;;;;;;;;;;;;UD9GDO,gBAAAA;;;GAAAA,kBAAAA,gBAAAA,CAAAA,EAAAA;IAKZC,wCAAiBD,eAAe;EAC9BE,MAAM;EACNC,aAAa;AACf,CAAA;AAaO,IAAeC,gBAAf,MAAeA;EAEpBC,OAAoB;EAGpBH;AACF;AANsBE;;MACnBE,6BAAM,MAAMC,MAAAA;;GADOH,cAAAA,WAAAA,QAAAA,MAAAA;;MAInBE,6BAAM,MAAMN,aAAAA;;GAJOI,cAAAA,WAAAA,QAAAA,MAAAA;AAAAA,gBAAAA,eAAAA;MAXrBI,qCAAc;IACbC,YAAYC,OAAK;AACf,UAAIA,MAAMR,SAAI,2BAA4C;AACxD,eAAOS;MACT,WAAWD,MAAMR,SAAI,qCAAsD;AACzE,eAAOU;MACT;AACA,aAAOC;IACT;EACF,CAAA;MACCL,qCAAAA;GACqBJ,aAAAA;AASf,IAAMU,wCAAN,MAAMA;EAEXJ;EAGAK;AACF;AANaD;;MACVR,6BAAM,MAAMC,MAAAA;;GADFO,sCAAAA,WAAAA,SAAAA,MAAAA;;MAIVR,6BAAM,MAAM;IAACU;GAAkB;;GAJrBF,sCAAAA,WAAAA,YAAAA,MAAAA;AAAAA,wCAAAA,eAAAA;MADZG,kCAAAA;GACYH,qCAAAA;AASN,IAAMH,0BAAN,MAAMA;EAEXT,OAAAA;EAGAQ;EAGAQ;AACF;AATaP;;MACVL,6BAAM,MAAMN,aAAAA;uCACP,kBAAA,eAAA,QAAA,SAAA,yBAAA;GAFKW,wBAAAA,WAAAA,QAAAA,MAAAA;;MAIVL,6BAAM,MAAMC,MAAAA;;GAJFI,wBAAAA,WAAAA,SAAAA,MAAAA;;MAOVL,6BAAM,MAAMC,QAAQ;IAAEY,UAAU;EAAK,CAAA;;GAP3BR,wBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,0BAAAA,eAAAA;MADZM,kCAAW;IAAEG,YAAYhB;EAAc,CAAA;GAC3BO,uBAAAA;AAYN,IAAMC,oCAAN,MAAMA;EAEXV,OAAAA;EAIAmB;EAGAH;AACF;AAVaN;;MACVN,6BAAM,MAAMN,aAAAA;uCACP,kBAAA,eAAA,QAAA,SAAA,mCAAA;GAFKY,kCAAAA,WAAAA,QAAAA,MAAAA;;MAKVN,6BAAM,MAAMQ,qCAAAA;uCACP,0CAAA,cAAA,SAAA,qCAAA;GANKF,kCAAAA,WAAAA,QAAAA,MAAAA;;MAQVN,6BAAM,MAAMC,QAAQ;IAAEY,UAAU;EAAK,CAAA;;GAR3BP,kCAAAA,WAAAA,YAAAA,MAAAA;AAAAA,oCAAAA,eAAAA;MADZK,kCAAW;IAAEG,YAAYhB;EAAc,CAAA;GAC3BQ,iCAAAA;;;;;;;;;;;;;;;;;;;ADvDN,IAAMU,iBAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;AACF;AAZaJ;;MACVK,6BAAM,MAAMC,aAAAA;uCACP,kBAAA,cAAA,SAAA,aAAA;GAFKN,eAAAA,WAAAA,QAAAA,MAAAA;;MAIVK,6BAAM,MAAME,MAAAA;;GAJFP,eAAAA,WAAAA,SAAAA,MAAAA;;MAOVK,6BAAM,MAAME,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BR,eAAAA,WAAAA,YAAAA,MAAAA;;MAUVK,6BAAM,MAAM;IAACI;KAAe;IAAED,UAAU;EAAK,CAAA;;GAVnCR,eAAAA,WAAAA,YAAAA,MAAAA;AAAAA,iBAAAA,eAAAA;MADZU,iCAAAA;GACYV,cAAAA;;;;;;;;;;;;;;;;;;;AZON,IAAMW,uCAAN,MAAMA;EAEXC;AACF;AAHaD;;MACVE,6BAAM,MAAMC,oBAAoB;IAAEC,UAAU;EAAK,CAAA;uCACrC,uBAAA,cAAA,SAAA,kBAAA;GAFFJ,qCAAAA,WAAAA,eAAAA,MAAAA;AAAAA,uCAAAA,eAAAA;MADZK,iCAAAA;GACYL,oCAAAA;AAMN,IAAMM,+BAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AApCaZ;;MACVJ,6BAAM,MAAMF,sCAAsC;IAAEI,UAAU;EAAM,CAAA;uCAC3D,yCAAA,cAAA,SAAA,oCAAA;GAFCE,6BAAAA,WAAAA,YAAAA,MAAAA;;MAIVJ,6BAAM,MAAMiB,QAAQ;IAAEf,UAAU;EAAK,CAAA;;GAJ3BE,6BAAAA,WAAAA,YAAAA,MAAAA;;MAOVJ,6BAAM,MAAMiB,QAAQ;IAAEf,UAAU;EAAK,CAAA;;GAP3BE,6BAAAA,WAAAA,SAAAA,MAAAA;;MAUVJ,6BAAM,MAAM;IAACkB;GAAa;;GAVhBd,6BAAAA,WAAAA,YAAAA,MAAAA;;MAaVJ,6BAAM,MAAMmB,aAAAA;uCACH,kBAAA,cAAA,SAAA,aAAA;GAdCf,6BAAAA,WAAAA,YAAAA,MAAAA;;MAgBVJ,6BAAM,MAAMoB,YAAY;IAAElB,UAAU;EAAK,CAAA;uCAClC,eAAA,cAAA,SAAA,UAAA;GAjBGE,6BAAAA,WAAAA,SAAAA,MAAAA;;MAmBVJ,6BAAM,MAAMqB,0BAA0B;IAAEnB,UAAU;EAAK,CAAA;uCAClC,6BAAA,cAAA,SAAA,wBAAA;GApBXE,6BAAAA,WAAAA,uBAAAA,MAAAA;;MAsBVJ,6BAAM,MAAMsB,mBAAmB;IAAEpB,UAAU;EAAK,CAAA;uCAClC,sBAAA,cAAA,SAAA,iBAAA;GAvBJE,6BAAAA,WAAAA,gBAAAA,MAAAA;;MAyBVJ,6BAAM,MAAMuB,iBAAiB;IAAErB,UAAU;EAAK,CAAA;uCAClC,oBAAA,cAAA,SAAA,eAAA;GA1BFE,6BAAAA,WAAAA,cAAAA,MAAAA;;MA4BVJ,6BAAM,MAAM;IAACuB;KAAkB;IAAErB,UAAU;EAAK,CAAA;;GA5BtCE,6BAAAA,WAAAA,eAAAA,MAAAA;;MA+BVJ,6BAAM,MAAMwB,iBAAiB;IAAEtB,UAAU;EAAK,CAAA;uCAClC,oBAAA,cAAA,SAAA,eAAA;GAhCFE,6BAAAA,WAAAA,cAAAA,MAAAA;;MAkCVJ,6BAAM,MAAM;IAACyB;KAAiB;IAAEvB,UAAU;EAAK,CAAA;;GAlCrCE,6BAAAA,WAAAA,cAAAA,MAAAA;AAAAA,+BAAAA,eAAAA;MADZD,iCAAAA;GACYC,4BAAAA;;;ADGb,0BAAyB;;;AmBrBzB,IAAAsB,iBAAiC;AACjC,IAAAC,eAWO;;;ACZP,IAAAC,iBAAgC;;;ACAhC,oBAA4C;AAa5C,IAAAC,iBAIO;;UAOKC,eAAAA;;;GAAAA,iBAAAA,eAAAA,CAAAA,EAAAA;;;ADtBZ,yBAA2B;;;AEY3B,IAAAC,iBAWO;;;ACRA,IAAMC,UAAN,cAAsBC,iBAAAA;EAC3BC;EAEAC,gBAAqC;AACnC,WAAO,KAAKD,SAAS;EACvB;EAEAE,2BAA2D;AACzD,WAAO,KAAKF,SAAS;EACvB;EAEAG,kBAAyC;AACvC,WAAO,KAAKH,SAAS;EACvB;EAEAI,sBAAiD;AAC/C,WAAO,KAAKJ,SAAS;EACvB;EAEAK,iBAAuC;AACrC,WAAO,KAAKL,SAAS;EACvB;AACF;AAtBaF;AAwBN,IAAMQ,cAAN,cAA0BR,QAAAA;EAC/BE,OAAoB;EACpBO;EACAC;EACAC;AACF;AALaH;AAON,IAAMI,yBAAN,cACGZ,QAAAA;EAGRE,OAAoB;EACpBW;EACAC;EACAH;AACF;AARaC;AAUN,IAAMG,gBAAN,cAA4Bf,QAAAA;EACjCE,OAAoB;EACpBc;EACAC;EACAC;EAEA,OAAOC,aACLD,QACAE,OACQ;AACR,UAAMC,WAAWD,QACb,OAAOA,UAAU,WACf;MAAEE,MAAM;MAASC,SAASH;IAAM,IAChCA,iBAAiBI,QACf;MAAEF,MAAM;MAASC,SAASH,MAAMG;IAAQ,IACxCH,QACJK;AAEJ,QAAIJ,UAAU;AACZ,aAAOK,KAAKC,UAAU;QACpBP,OAAOC;QACPH,QAAQA,UAAU;MACpB,CAAA;IACF;AACA,QAAIA,WAAWO,QAAW;AACxB,aAAO;IACT;AACA,WAAO,OAAOP,WAAW,WAAWA,SAASQ,KAAKC,UAAUT,MAAAA;EAC9D;EAEA,OAAOU,aAAaV,QAGlB;AACA,QAAI,CAACA,QAAQ;AACX,aAAO;QAAEA,QAAQ;MAAG;IACtB;AACA,QAAI;AACF,YAAMW,SAASH,KAAKI,MAAMZ,MAAAA;AAC1B,UAAIW,UAAU,OAAOA,WAAW,UAAU;AACxC,YAAI,WAAWA,QAAQ;AACrB,iBAAO;YACLT,OAAOS,OAAOT;YACdF,QAAQW,OAAOX,UAAU;UAC3B;QACF;AACA,eAAO;UAAEA,QAAQQ,KAAKC,UAAUE,MAAAA;QAAQ;MAC1C;AACA,aAAO;QAAEX;MAAO;IAClB,SAASa,GAAP;AACA,aAAO;QAAEb;MAAO;IAClB;EACF;EAEAc,WAAoB;AAClB,QAAI;AACF,YAAM,EAAEZ,MAAK,IAAKL,cAAca,aAAa,KAAKV,MAAM;AACxD,aAAO,CAAC,CAACE;IACX,QAAE;AACA,aAAO;IACT;EACF;EAEAa,WAA0D;AACxD,QAAI;AACF,YAAM,EAAEb,MAAK,IAAKL,cAAca,aAAa,KAAKV,MAAM;AACxD,aAAOE;IACT,QAAE;AACA,aAAOK;IACT;EACF;AACF;AAvEaV;AAyEN,IAAMmB,oBAAN,cAAgClC,QAAAA;EACrCE,OAAoB;EACpBiC;EACAC;EACAC;EACAC;EACAC;EACA7B;EACA8B;EACAC;AACF;AAVaP;;;AD/Eb,kBAAqB;AAQrB,2BAA0C;AA0pCnC,SAASQ,oBAAoBC,UAA4B;AAC9D,MAAI,CAACA,SAASC,MAAM;AAClB,QAAI,mBAAmBD,YAAY,YAAYA,UAAU;AACvD,aAAOE,aAAaC;IACtB,OAAO;AACL,aAAOD,aAAaE;IACtB;EACF;AAEA,SAAOJ,SAASC;AAClB;AAVgBF;;;AFjtChB,IAAMM,cAAcC;AAEpB,IAAMC,kBAAkB,IAAIC,+BAAgB;EAC1CC,aAAaJ,YAAYK;EACzBC,gBAAgBN,YAAYO;AAC9B,CAAA;AAEO,SAASC,gCACdC,SAAuB;AAEvB,QAAMC,gBAAgBD,QAAQE,0BAA0BC,OACtD,CAACC,KAAKC,aAAAA;AACJ,QAAIC,OAAO;MAAE,GAAGF;IAAI;AAEpB,UAAMG,eAAeC,oBAAoBH,QAAAA;AACzC,QAAI,CAACC,KAAKG,cAAcC,SAASH,YAAAA,GAAe;AAC9CD,aAAO;QACL,GAAGA;QACHG,eAAe;aAAIH,KAAKG;UAAeF;;MACzC;IACF;AAEA,QAAIA,iBAAiBI,aAAaC,mBAAmB;AAEnD,YAAMC,KAAKR;AACXC,aAAO;QACL,GAAGA;QACHQ,cAAcD,GAAGE,OAAOC;QACxBC,WAAWJ,GAAGK,sBACVC,+BAAW,QAAA,EAAUC,OAAOP,GAAGK,eAAe,EAAEG,OAAO,KAAA,IACvD;MACN;IACF;AAEA,WAAOf;EACT,GACA;IAAEG,eAAe,CAAA;IAAIK,cAAc;IAAMG,WAAW;EAAK,CAAA;AAG3D,SAAO;IACLK,eAAetB,QAAQuB,QAAQP;IAC/BQ,iBAAiBxB,QAAQE,0BAA0Bc;IACnDP,eAAeR,cAAcQ;IAC7BK,cAAcb,cAAca;IAC5BW,cAAcxB,cAAcgB;EAC9B;AACF;AAvCgBlB;AAyChB,IAAA,2BAAeN;;;ADlCf,+BAAgC;;UAEpBiC,oBAAAA;;;;;;;;;;GAAAA,sBAAAA,oBAAAA,CAAAA,EAAAA;;UAYAC,uBAAAA;;;;GAAAA,yBAAAA,uBAAAA,CAAAA,EAAAA;;;AnBEZ,IAAAC,0BAAkC;AAClC,IAAAC,4BAAgC;AAEhC,qBAA6B;;;AwBjCtB,IAAMC,sCAAN,cAAkDC,qBAAAA;EACvDC,SAASC,2BAA2BC;EAKpCC,YAAY,EAAEC,iBAAgB,GAAI;AAChC,UAAK;AACL,SAAKC,UAAU;MACbD;IACF;EACF;AACF;AAZaN;AAcN,IAAMQ,mCAAN,cAA+CP,qBAAAA;EACpDC,SAASC,2BAA2BM;EAMpCJ,YAAY,EAAEK,UAAS,GAA2B;AAChD,UAAK;AACL,SAAKH,UAAU;MACbG;MACAC,aAAa;IACf;EACF;AACF;AAdaH;AAgBN,IAAMI,uBAAN,cAAmCX,qBAAAA;EACxCC,SAASC,2BAA2BU;EAKpCR,YAAY,EAAEM,YAAW,GAA8B;AACrD,UAAK;AACL,SAAKJ,UAAU;MACbI;IACF;EACF;AACF;AAZaC;;;AxBkBb,IAAAE,iBAAyB;;;AyBrDzB,IAAAC,wBAAkC;;;;;;;;;;;;;;;;;AAG3B,IAAMC,QAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;MACVI,6BAAM,MAAMC,MAAAA;;GADFL,MAAAA,WAAAA,MAAAA,MAAAA;;MAIVI,6BAAM,MAAMC,MAAAA;;GAJFL,MAAAA,WAAAA,QAAAA,MAAAA;;MAOVI,6BAAM,MAAMC,MAAAA;;GAPFL,MAAAA,WAAAA,eAAAA,MAAAA;AAAAA,QAAAA,eAAAA;MADZM,kCAAAA;GACYN,KAAAA;AAYN,IAAMO,iBAAN,MAAMA;EAEXC;AACF;AAHaD;;MACVH,6BAAM,MAAM;IAACJ;GAAM;;GADTO,eAAAA,WAAAA,UAAAA,MAAAA;AAAAA,iBAAAA,eAAAA;MADZD,kCAAAA;GACYC,cAAAA;;;;;;;;;;;;;;;;;;;;;;;;;AzByCb,IAAME,mBAAmB,8BAAO,EAC9BC,SACAC,0BACAC,MACAC,UACAC,QAAO,MAOR;;AACC,MACEF,KAAKG,SAASC,YACdJ,UAAKG,SAASH,KAAKG,SAASC,SAAS,CAAA,EAAGC,gBAAxCL,mBAAqDM,UAASC,YAAYC,MAC1E;AACA,UAAML,WAAWH,KAAKG,SACnBM,OACC,CAACC,MACCA,EAAEL,gBAAgBM,WACjBD,EAAEL,YAAYC,SAASC,YAAYC,QAAQE,EAAEL,YAAYC,SAASC,YAAYK,UAAQ,EAE1FC,IAAI,CAACH,OAAO;MACXJ,MAAMI,EAAEL,YAAaC;MACrBQ,SAASJ,EAAEL,YAAYS;IACzB,EAAA;AAEF,UAAMC,cAAcZ,SAASA,SAASC,SAAS,CAAA;AAC/C,UAAMY,iBAAiBb,SAASc,MAAM,GAAG,EAAC;AAE1C,UAAMC,OAAO;MACXC,OAAOJ,YAAYD;MACnBM,aAAapB,KAAKqB,MAAMC,WAAWC,qBAAqBC;MACxDC,eAAezB,KAAKqB,MAAMC,WAAWC,qBAAqBG;MAC1DvB,UAAUa;IACZ;AAEA,UAAMW,mBAAmB,MAAMC,MAAM,GAAG9B,+BAA+B;MACrE+B,QAAQ;MACRC,SAAS;QACP,gBAAgB;QAChB,iCAAiC/B;MACnC;MACAmB,MAAMa,KAAKC,UAAUd,IAAAA;IACvB,CAAA;AAEA,QAAIS,iBAAiBM,IAAI;AACvB,YAAMC,aAA+B,MAAMP,iBAAiBQ,KAAI;AAChElC,eAASiC,UAAAA;IACX,OAAO;AACLhC,cAAQ,MAAMyB,iBAAiBQ,KAAI,CAAA;IACrC;EACF;AACF,GAtDyB;AAyDlB,IAAMC,kBAAN,MAAMA;EACX,MACMC,QAAQ;AACZ,WAAO;EACT;EAEA,MACMC,gBAAuBC,KAAqB;AAChD,QAAIC,UAASD,IAAIC,OAAOC,MAAM;MAAEC,WAAW;IAAkC,CAAA;AAE7EF,IAAAA,QAAOG,MAAM,YAAA;AACb,UAAMC,sBAAsB,MAAML,IAAIM,YAAYC,QAAQC,4BAA4BR,GAAAA;AAEtFC,IAAAA,QAAOG,MAAM,yCAAA;AAEb,WAAO;MACLK,QAAQJ,oBAAoB/B,IAC1B,CAAC,EAAEoC,UAAU,GAAGC,qBAAAA,MAA2BA,oBAAAA;IAE/C;EACF;EAEA,MACMC,wBACGZ,KACMvC,MAEboD,YACA;;AACAC,6BAAUC,QAAQ,uCAAuC;MACvD,8BAA4BtD,UAAKqB,UAALrB,mBAAYsB,gBAAeX;MACvD4C,aAAavD,KAAKwD,SAASD;IAC7B,CAAA;AAEA,QAAIf,UAASD,IAAIC,OAAOC,MAAM;MAAEC,WAAW;IAA0C,CAAA;AACrFF,IAAAA,QAAOG,MAAM;MAAE3C;IAAK,GAAG,6BAAA;AAEvB,QAAIoD,YAAY;AACdZ,MAAAA,QAAOG,MAAM,sDAAA;AACbJ,UAAIa,aAAa;QAAE,GAAGb,IAAIa;QAAY,GAAGA;MAAW;IACtD;AAEA,UAAMK,iBAAiBlB,IAAIM,YAAYC;AACvC,UAAMY,iBAAiBnB,IAAIM,YAAYa;AAEvC,QAAI3D,2BAA0C;AAC9C,QAAI4D;AAEJ,QAAI3D,KAAKqB,OAAO;AACdmB,MAAAA,UAASA,QAAOC,MAAM;QAAEpB,OAAO;MAAK,CAAA;AACpCmB,MAAAA,QAAOG,MAAM,sEAAA;AACb,YAAMiB,MAAMrB,IAAIsB,QAAQ/B,QAAQgC,IAAI,+BAAA;AACpC,UAAIF,KAAK;AACPpB,QAAAA,QAAOG,MAAM,iCAAA;AACb5C,mCAA2B6D;MAC7B,OAAO;AACLpB,QAAAA,QAAOuB,MAAM,qCAAA;AACb,cAAM,IAAIC,4BAAa,kDAAA;MACzB;AAEA,UAAIC,QAAQC,IAAIC,wBAAwB;AACtCR,8BAAsBM,QAAQC,IAAIC;MACpC,YAAW5B,SAAIM,YAAYxB,UAAhBkB,mBAAuBzC,SAAS;AACzC6D,+BAAsBpB,SAAIM,YAAYxB,UAAhBkB,mBAAuBzC;MAC/C,OAAO;AACL6D,8BAAsB;MACxB;AAEAnB,MAAAA,UAASA,QAAOC,MAAM;QAAEkB;MAAoB,CAAA;IAC9C;AAEAnB,IAAAA,QAAOG,MAAM,qBAAA;AACb,UAAMyB,kBAAkB,IAAIC,2BAAAA;AAC5B,UAAMC,sBAAsB,IAAID,2BAAAA;AAChC,UAAME,oBAAoB,IAAIF,2BAAAA;AAE9B,QAAIG,iBAA4B,CAAA;AAChC,QAAIC;AACJ,QAAIC;AAEJ,UAAMC,wBAAwB,IAAIC,QAAmB,CAACC,SAASC,WAAAA;AAC7DL,qCAA+BI;AAC/BH,oCAA8BI;IAChC,CAAA;AAEA,QAAI/E,0BAA0B;AAC5BwC,UAAIa,WAAW,0BAAA,IAA8BrD;IAC/C;AAEAyC,IAAAA,QAAOG,MAAM,YAAA;AACb,UAAM,EACJoC,aACAC,eAAWC,yBAAAA,GACXC,OACAC,mBACAC,2BACAC,WAAU,IACR,MAAM5B,eAAe6B,sBAAsB;MAC7C5B;MACAvD,UAAUH,KAAKG;MACfoF,SAASvF,KAAKwF,SAASD,QAAQ9E,OAC7B,CAACgF,WAAWA,OAAOC,cAAcC,wBAAwBC,QAAQ;MAEnEZ,UAAUhF,KAAKgF;MACfE,OAAOlF,KAAKkF;MACZW,cAAc9F;MACd4E;MACAmB,gBAAgBvD;MAChBwD,qBAAqB/F,KAAK+F;MAC1BC,cAAchG,KAAKgG;MACnBC,aAAajG,KAAKiG;MAClBC,KAAKlG,KAAKwF,SAASU;MACnBb,YAAYrF,KAAKqF;MACjBc,YAAYnG,KAAKmG;IACnB,CAAA;AAEA3D,IAAAA,QAAOG,MAAM,yCAAA;AAEb,UAAMyD,cAAcrB,YACjBsB,qBAAqB;MACpBlB;MACAZ,qBAAmBvE,UAAKqB,UAALrB,mBAAYsB,cAAaiD,oBAAoB;MAChEa,2BAA2BA,0BAA0B3E;;QAEnD,CAACgF,WACC,CAACN,kBAAkBmB,KAAK,CAACC,qBAAqBA,iBAAiBC,QAAQf,OAAOe,IAAI;MAAA;MAEtFxB;IACF,CAAA,EACCyB;;;UAGCC,0BAAAA;UACAC,uBAAS,MAAA;AACPnE,QAAAA,QAAOG,MAAM,wBAAA;MACf,CAAA;IAAA;AAGJ,UAAMiE,WAAW;MACf5B;MACAE;MACA2B,YAAQC,6BAAe1C,eAAAA;MACvBiB;MACAc,YAAY,IAAIY,6BAAS,OAAOC,MAAMC,SAAAA;AACpC,YAAIC;AAEJA,kCAA0Bd,YAAYe,UAAU;UAC9CC,MAAM,OAAOC,UAAAA;AACX,gBAAIA,MAAMC,QAAQC,kBAAkBC,WAAW;AAC7C;YACF;AACA,oBAAQH,MAAMb,MAAI;cAChB,KAAKiB,qBAAqBC;AACxBV,yBACEW,2CAAgBD,yBAAyB;kBACvCJ,MAAMD,MAAMC;kBACZd,MAAMa,MAAMb;kBACZoB,OAAOP,MAAMO;gBACf,CAAA,CAAA;AAEF;cACF,KAAKH,qBAAqBI;AACxBb,yBACEW,2CAAgBE,mCAAmC;kBACjDP,MAAMD,MAAMC;kBACZd,MAAMa,MAAMb;kBACZxG,MAAM;oBACJ4H,OAAOP,MAAMrH,KAAK4H;oBAClBzH,UAAUkH,MAAMrH,KAAKG,SAASU,IAAI,CAACiH,YAAAA;AACjC,0BACEA,QAAQR,SAAS,iBAChB,aAAaQ,WAAW,UAAUA,SACnC;AACA,mCAAOH,2CAAgBI,aAAa;0BAClCC,IAAIF,QAAQE;0BACZC,WAAW,oBAAIC,KAAAA;0BACfpH,SAAS;4BAAEgH,QAAwBhH;;0BACnCR,MAAOwH,QAAwBxH;0BAC/BuG,QAAQ,IAAIsB,qBAAAA;wBACd,CAAA;sBACF;AACA,0BAAI,eAAeL,SAAS;AAC1B,mCAAOH,2CAAgBS,wBAAwB;0BAC7C5B,MAAMsB,QAAQtB;0BACdwB,IAAIF,QAAQE;0BACZK,WAAW;4BAACtG,KAAKC,UAAU8F,QAAQO,SAAS;;0BAC5CJ,WAAW,oBAAIC,KAAAA;0BACfrB,QAAQ,IAAIsB,qBAAAA;wBACd,CAAA;sBACF;AACA,4BAAM,IAAIG,MAAM,gDAAA;oBAClB,CAAA;kBACF;gBACF,CAAA,CAAA;AAEF;YACJ;UACF;UACAvE,OAAO,CAACwE,QAAAA;AACN/F,YAAAA,QAAOuB,MAAM;cAAEwE;YAAI,GAAG,6BAAA;AACtBnE,4BAAgBgD,KACd,IAAIoB,qBAAqB;cACvBC,aAAa;YACf,CAAA,CAAA;AAEFvB,+EAAyBwB;AACzBzB,iBAAAA;UACF;UACA0B,UAAU,YAAA;AACRnG,YAAAA,QAAOG,MAAM,8BAAA;AACbyB,4BAAgBgD,KAAK,IAAIwB,sBAAAA,CAAAA;AACzB1B,+EAAyBwB;AACzBzB,iBAAAA;UACF;QACF,CAAA;MACF,CAAA;MACA9G,UAAU,IAAI4G,6BAAS,OAAO8B,aAAaC,0BAAAA;;AACzCtG,QAAAA,QAAOG,MAAM,2BAAA;AAEb,aAAI3C,MAAAA,KAAKqB,UAALrB,gBAAAA,IAAYsB,YAAY;AAC1BkB,UAAAA,UAASA,QAAOC,MAAM;YAAEnB,YAAY;UAAK,CAAA;AACzCkB,UAAAA,QAAOG,MAAM,yCAAA;AAEb9C,2BAAiB;YACfC,SAAS6D;YACT5D;YACAC;YACAC,UAAU,CAAC8I,WAAAA;AACTvG,cAAAA,QAAOG,MAAM;gBAAEkE,QAAQkC,OAAOlC;cAAO,GAAG,4BAAA;AACxCtC,gCAAkB6C,KAAK2B,MAAAA;AAGvB,kBAAIA,OAAOlC,WAAW,UAAU;AAE9BzC,gCAAgBgD,KACd,IAAI4B,oCAAoC;kBAAEC,kBAAkBF,OAAOG;gBAAO,CAAA,CAAA;AAE5E5E,oCAAoB8C,KAAK;kBACvB8B,QAAQ,6DAA6DH,OAAOG;gBAC9E,CAAA;AAGA1E,iCAAiB;sBACfmD,2CAAgBI,aAAa;oBAC3BC,QAAI/C,yBAAAA;oBACJgD,WAAW,oBAAIC,KAAAA;oBACfpH,SAASiI,OAAOG;oBAChB5I,MAAMC,YAAYK;kBACpB,CAAA;;AAEF6D,6CAA6BD,cAAAA;cAC/B;YACF;YACAtE,SAAS,CAACqI,QAAAA;AACR/F,cAAAA,QAAOuB,MAAM;gBAAEwE;cAAI,GAAG,gCAAA;AACtBnE,8BAAgBgD,KACd,IAAIoB,qBAAqB;gBACvBC,aAAa;cACf,CAAA,CAAA;AAEFnE,kCAAoB8C,KAAK;gBACvB8B,QAAQ;cACV,CAAA;AAGAxE,0CAA4B6D,GAAAA;YAC9B;UACF,CAAA;QACF;AAEA,YAAIrB;AAEJ1E,QAAAA,QAAOG,MAAM,mDAAA;AAEbuE,kCAA0Bd,YAAYe,UAAU;UAC9CC,MAAM,OAAOC,UAAAA;AACX,oBAAQA,MAAMC,MAAI;cAChB,KAAKC,kBAAkBC;AACrB;cAIF,KAAKD,kBAAkB4B;AAErB,sBAAMC,2BAA2BhD,YAAYK;;sBAE3C4C,wBAAU,CAACC,MAAMA,MAAMjC,KAAAA;;sBAEvBkC,wBACE,CAACD,MACC,EACEA,EAAEhC,SAASC,kBAAkBiC,kBAC7BF,EAAEG,aAAapC,MAAMoC,UAAQ;;sBAInChJ,qBACE,CAAC6I,MACCA,EAAEhC,QAAQC,kBAAkBmC,sBAC5BJ,EAAEG,aAAapC,MAAMoC,SAAS;gBAAA;AAKpC,sBAAME,sBAAsB,IAAIC,qBAAAA;AAEhC,sBAAMH,YAAYpC,MAAMoC;AAExBZ,4BAAY;kBACVb,IAAIyB;kBACJI,iBAAiBxC,MAAMwC;kBACvBhD,YAAQC,6BAAe6C,mBAAAA;kBACvB1B,WAAW,oBAAIC,KAAAA;kBACf5H,MAAMC,YAAYK;kBAClBE,SAAS,IAAIiG,6BAAS,OAAO+C,eAAeC,sBAAAA;AAC1CvH,oBAAAA,QAAOG,MAAM,uCAAA;AAEb,0BAAMqH,aAAuB,CAAA;AAC7B,wBAAIC;AAEJ3F,wCACGmC,SACCC,0BAAAA,OACAwD,mBAAK,CAAA,OACLC,kBAAI,CAAC,EAAEjB,QAAQO,WAAAA,WAAS,MAAE;AACxBjH,sBAAAA,QAAOG,MAAM;wBAAEuG;wBAAQO,WAAAA;sBAAU,GAAG,4BAAA;AAEpCE,0CAAoBvC,SAClBO,2CAAgByC,qBAAqB;wBAAElB;sBAAO,CAAA,CAAA;AAGhD9E,sCAAgBgD,KAAK,IAAIiD,iCAAiC;wBAAEZ,WAAAA;sBAAU,CAAA,CAAA;AACtEM,wCAAAA;AACAE,2EAAkBvB;oBACpB,CAAA,CAAA,EAEDvB,UAAS;AAEZ3E,oBAAAA,QAAOG,MAAM,4CAAA;AAEbsH,uCAAmBb,yBAAyBjC,UAAU;sBACpDC,MAAM,OAAOkC,MAAAA;AACX,4BAAIA,EAAEhC,QAAQC,kBAAkBmC,oBAAoB;AAClD,gCAAMI,cAAcR,EAAExI,OAAO;AAC7BkJ,qCAAWhD,KAAKsC,EAAExI,OAAO;wBAC3B;sBACF;sBACAiD,OAAO,CAACwE,QAAAA;AACN/F,wBAAAA,QAAOuB,MAAM;0BAAEwE;wBAAI,GAAG,sCAAA;AACtBjE,4CAAoB8C,KAAK;0BACvB8B,QAAQ;0BACRO;wBACF,CAAA;AACAM,0CAAAA;AACAE,6EAAkBvB;sBACpB;sBACAC,UAAU,MAAA;AACRnG,wBAAAA,QAAOG,MAAM,uCAAA;AACbgH,4CAAoBvC,KAAK,IAAIe,qBAAAA,CAAAA;AAC7B4B,0CAAAA;AACAE,6EAAkBvB;AAElBlE,uCAAewC,SACbW,2CAAgBI,aAAa;0BAC3BC,IAAIyB;0BACJxB,WAAW,oBAAIC,KAAAA;0BACfpH,SAASkJ,WAAWM,KAAK,EAAA;0BACzBhK,MAAMC,YAAYK;wBACpB,CAAA,CAAA;sBAEJ;oBACF,CAAA;kBACF,CAAA;gBACF,CAAA;AACA;cAIF,KAAK2G,kBAAkBgD;AACrB/H,gBAAAA,QAAOG,MAAM,uCAAA;AACb,sBAAM6H,gCAAgCpE,YAAYK;sBAChD4C,wBAAU,CAACC,MAAMA,MAAMjC,KAAAA;;sBAEvBkC,wBACE,CAACD,MACC,EACEA,EAAEhC,SAASC,kBAAkBkD,sBAC7BnB,EAAEoB,qBAAqBrD,MAAMqD,kBAAgB;;sBAInDjK,qBACE,CAAC6I,MACCA,EAAEhC,QAAQC,kBAAkBoD,uBAC5BrB,EAAEoB,qBAAqBrD,MAAMqD,iBAAiB;gBAAA;AAGpD,sBAAME,2BAA2B,IAAIhB,qBAAAA;AACrCf,4BAAY;kBACVb,IAAIX,MAAMqD;kBACVb,iBAAiBxC,MAAMwC;kBACvBhD,YAAQC,6BAAe8D,wBAAAA;kBACvB3C,WAAW,oBAAIC,KAAAA;kBACf1B,MAAMa,MAAMwD;kBACZxC,WAAW,IAAItB,6BAAS,OAAO+D,oBAAoBC,2BAAAA;AACjDvI,oBAAAA,QAAOG,MAAM,0CAAA;AAEb,0BAAMqI,iBAA2B,CAAA;AACjC,wBAAIC;AAEJA,0DAAsCT,8BAA8BrD,UAAU;sBAC5EC,MAAM,OAAOkC,MAAAA;AACX,4BAAIA,EAAEhC,QAAQC,kBAAkBoD,qBAAqB;AACnD,gCAAMG,mBAAmBxB,EAAE4B,IAAI;AAC/BF,yCAAehE,KAAKsC,EAAE4B,IAAI;wBAC5B;sBACF;sBACAnH,OAAO,CAACwE,QAAAA;AACN/F,wBAAAA,QAAOuB,MAAM;0BAAEwE;wBAAI,GAAG,2CAAA;AACtBqC,iDAAyBxD,SACvBO,2CAAgByC,qBAAqB;0BACnClB,QACE;wBACJ,CAAA,CAAA;AAEF6B,+CAAAA;AACAE,mHAAqCvC;sBACvC;sBACAC,UAAU,MAAA;AACRnG,wBAAAA,QAAOG,MAAM,4CAAA;AACbiI,iDAAyBxD,KAAK,IAAIe,qBAAAA,CAAAA;AAClC4C,+CAAAA;AACAE,mHAAqCvC;AAErClE,uCAAewC,SACbW,2CAAgBS,wBAAwB;0BACtCJ,IAAIX,MAAMqD;0BACVzC,WAAW,oBAAIC,KAAAA;0BACf1B,MAAMa,MAAMwD;0BACZxC,WAAW2C,eAAeV,KAAK,EAAA;wBACjC,CAAA,CAAA;sBAEJ;oBACF,CAAA;kBACF,CAAA;gBACF,CAAA;AACA;cAIF,KAAK/C,kBAAkB4D;AACrB3I,gBAAAA,QAAOG,MAAM;kBAAEoG,QAAQ1B,MAAM0B;gBAAO,GAAG,wCAAA;AACvCF,4BAAY;kBACVb,IAAI,YAAYX,MAAMqD;kBACtB7D,QAAQ,IAAIsB,qBAAAA;kBACZF,WAAW,oBAAIC,KAAAA;kBACfwC,mBAAmBrD,MAAMqD;kBACzBG,YAAYxD,MAAMwD;kBAClB9B,QAAQ1B,MAAM0B;gBAChB,CAAA;AAEAvE,+BAAewC,SACbW,2CAAgByD,eAAe;kBAC7BpD,IAAI,YAAYX,MAAMqD;kBACtBzC,WAAW,oBAAIC,KAAAA;kBACfwC,mBAAmBrD,MAAMqD;kBACzBG,YAAYxD,MAAMwD;kBAClB9B,QAAQ1B,MAAM0B;gBAChB,CAAA,CAAA;AAEF;cAIF,KAAKxB,kBAAkB8D;AACrB7I,gBAAAA,QAAOG,MAAM;kBAAE0E;gBAAM,GAAG,8BAAA;AACxBwB,4BAAY;kBACVb,QAAI/C,yBAAAA;kBACJ4B,QAAQ,IAAIsB,qBAAAA;kBACZnD,UAAUqC,MAAMrC;kBAChBsG,WAAWjE,MAAMiE;kBACjBC,UAAUlE,MAAMkE;kBAChBrG,OAAOmC,MAAMnC;kBACbsG,QAAQnE,MAAMmE;kBACdC,OAAOpE,MAAMoE;kBACbC,SAASrE,MAAMqE;kBACfpL,MAAMC,YAAYK;kBAClBqH,WAAW,oBAAIC,KAAAA;gBACjB,CAAA;AACA1D,+BAAewC,SACbW,2CAAgB0D,mBAAmB;kBACjCrD,QAAI/C,yBAAAA;kBACJD,UAAUqC,MAAMrC;kBAChBsG,WAAWjE,MAAMiE;kBACjBC,UAAUlE,MAAMkE;kBAChBrG,OAAOmC,MAAMnC;kBACbsG,QAAQnE,MAAMmE;kBACdC,OAAOpE,MAAMoE;kBACbC,SAASrE,MAAMqE;kBACfpL,MAAMC,YAAYK;kBAClBqH,WAAW,oBAAIC,KAAAA;gBACjB,CAAA,CAAA;AAEF;YACJ;UACF;UACAnE,OAAO,CAACwE,QAAAA;AACN/F,YAAAA,QAAOuB,MAAM;cAAEwE;YAAI,GAAG,uBAAA;AACtBnE,4BAAgBgD,KACd,IAAIoB,qBAAqB;cACvBC,aAAa;YACf,CAAA,CAAA;AAEFvB,+EAAyBwB;AACzBI,kCAAAA;AAEApE,wCAA4B6D,GAAAA;UAC9B;UACAI,UAAU,YAAA;;AACRnG,YAAAA,QAAOG,MAAM,wBAAA;AACb,iBAAI3C,MAAAA,KAAKqB,UAALrB,gBAAAA,IAAYsB,YAAY;AAC1BkB,cAAAA,QAAOG,MAAM,sDAAA;AACb,wBAAMmE,6BAAevC,iBAAAA;YACvB;AACAH,4BAAgBgD,KAAK,IAAIwB,sBAAAA,CAAAA;AACzB1B,+EAAyBwB;AACzBI,kCAAAA;AAEArE,yCAA6BD,cAAAA;UAC/B;QACF,CAAA;MACF,CAAA;IACF;AAEA,WAAOoC;EACT;AACF;AAxhBaxE;;MACVuJ,6BAAM,MAAMC,MAAAA;;;;GADFxJ,gBAAAA,WAAAA,SAAAA,IAAAA;;MAMVuJ,6BAAM,MAAME,cAAAA;EACUC,UAAAA,OAAAA,2BAAAA,CAAAA;;;WAAW,mBAAA,cAAA,SAAA;;;GAPvB1J,gBAAAA,WAAAA,mBAAAA,IAAAA;;MAsBV2J,gCAAS,MAAMC,eAAAA;EAEbF,UAAAA,OAAAA,2BAAAA,CAAAA;EACAG,UAAAA,OAAAA,2BAAI,MAAA,CAAA;EACJA,UAAAA,OAAAA,2BAAI,cAAc,MAAMC,2CAAmB;IAAEC,UAAU;EAAK,CAAA,CAAA;;;WAFjD,mBAAA,cAAA,SAAA;WACO,iCAAA,cAAA,SAAA;WAEN,oCAAA,cAAA,SAAA;;;GA3BJ/J,gBAAAA,WAAAA,2BAAAA,IAAAA;AAAAA,kBAAAA,eAAAA;MADZgK,gCAAS,MAAMJ,eAAAA;GACH5J,eAAAA;;;AD9Gb,iCAA+B;;;A2BH/B,kBAA6B;AAC7B,yBAAmB;AAMZ,SAASiK,aAAaC,SAAkD;AAC7E,QAAM,EAAEC,OAAOC,UAAS,IAAKF,WAAW,CAAC;AACzC,QAAMG,aAASC,mBAAAA,SAAO;IAAEC,UAAU;EAAK,CAAA;AAEvC,QAAMC,cAASC,YAAAA,SACb;IACEN,OAAOO,QAAQC,IAAIC,aAAaT,SAAS;IACzCU,QAAQ;MACNC,OAAO;QAAC;QAAO;;MACfC,QAAQ;IACV;EACF,GACAV,MAAAA;AAGF,MAAID,WAAW;AACb,WAAOI,QAAOQ,MAAM;MAAEZ;IAAU,CAAA;EAClC,OAAO;AACL,WAAOI;EACT;AACF;AApBgBP;;;ACPhB,IAAAgB,wBAA8B;AAC9B,IAAAA,wBAAoB;AACpB,IAAAA,wBAAsB;;;ACFtB,IAAAC,wBAAkC;;;;;;;;;;;;;;;;;AAI3B,IAAMC,yBAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;AACF;AAZaJ;;MACVK,6BAAM,MAAMC,MAAAA;;GADFN,uBAAAA,WAAAA,YAAAA,MAAAA;;MAIVK,6BAAM,MAAME,OAAAA;;GAJFP,uBAAAA,WAAAA,gBAAAA,MAAAA;;MAOVK,6BAAM,MAAMC,MAAAA;;GAPFN,uBAAAA,WAAAA,SAAAA,MAAAA;;MAUVK,6BAAM,MAAMC,MAAAA;;GAVFN,uBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,yBAAAA,eAAAA;MADZQ,kCAAAA;GACYR,sBAAAA;;;ACJb,IAAAS,wBAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMC,sBAAN,MAAMA;EAEXC;EAGAC;AACF;AANaF;;MACVG,6BAAM,MAAMC,MAAAA;;GADFJ,oBAAAA,WAAAA,YAAAA,MAAAA;;MAIVG,6BAAM,MAAMC,MAAAA;;GAJFJ,oBAAAA,WAAAA,aAAAA,MAAAA;AAAAA,sBAAAA,eAAAA;MADZK,iCAAAA;GACYL,mBAAAA;;;;;;;;;;;;;;;;;;;;;;;;;AFKN,IAAMM,gBAAN,MAAMA;EACX,MACMC,eAAsBC,KAAkCC,MAA2B;AACvF,UAAMC,SAAS,MAAMF,IAAIG,YAAYC,QAAQC,4BAA4BL,GAAAA;AACzE,UAAMM,QAAQJ,OAAOK,KAAK,CAACD,WAAUA,OAAME,SAASP,KAAKQ,SAAS;AAElE,QAAI,CAACH,OAAO;AACV,aAAO;QACLI,UAAUT,KAAKS,YAAY;QAC3BC,cAAc;QACdC,OAAOC,KAAKC,UAAU,CAAC,CAAA;QACvBC,UAAUF,KAAKC,UAAU,CAAA,CAAE;MAC7B;IACF;AAEA,UAAMF,QAAQ,MAAMZ,IAAIG,YAAYC,QAAQL,eAAeC,KAAKC,KAAKS,UAAUT,KAAKQ,SAAS;AAE7F,WAAOG;EACT;AACF;AAnBad;;MACVkB,6BAAM,MAAMC,sBAAAA;EACSC,WAAAA,OAAAA,2BAAAA,CAAAA;EAA4BC,WAAAA,OAAAA,2BAAI,MAAA,CAAA;;;WAArB,mBAAA,cAAA,SAAA;WAAmC,wBAAA,cAAA,SAAA;;;GAFzDrB,cAAAA,WAAAA,kBAAAA,IAAAA;AAAAA,gBAAAA,eAAAA;MADZsB,gCAAS,MAAMH,sBAAAA;GACHnB,aAAAA;;;A5BGb,IAAAuB,eAA6B;AAE7B,IAAMC,SAASC,aAAAA;AAER,IAAMC,wBAAwB;EACnCC,WAAW,EAAEC,SAAQ,GAAE;AAErBA,aAASC,QAAQC,IAAI,gCAA4CC,oBAAO;EAC1E;AACF;AAwBA,eAAsBC,cACpBC,gBACAC,mBACAC,eACAC,aAA8C,CAAC,GAAC;AAEhDZ,SAAOa,MAAM;IAAEH;EAAkB,GAAG,0BAAA;AACpC,QAAMI,MAAsB;IAC1B,GAAGL;IACHM,aAAa;MACX,GAAGL;IACL;IACAE,YAAY;MAAE,GAAGA;IAAW;IAC5BZ,QAAQW;EACV;AACA,SAAOG;AACT;AAhBsBN;AAkBf,SAASQ,YACdC,UAEI,CAAC,GAAC;AAENjB,SAAOa,MAAM,4BAAA;AACb,QAAMK,aAASC,uCAAgB;IAC7BC,WAAW;MAACC;MAAiBC;;IAC7BC,gBAAgBN,QAAQM;EAC1B,CAAA;AACAvB,SAAOa,MAAM,mCAAA;AACb,SAAOK;AACT;AAZgBF;AAqBT,SAASQ,gBAAgBP,SAA0C;AAlF1E;AAmFE,QAAMQ,WAAYC,QAAQC,IAAIC,aAA2BX,QAAQQ,YAAyB;AAC1F,QAAMzB,UAASC,aAAa;IAAE4B,OAAOJ;IAAUK,WAAW;EAAkB,CAAA;AAE5E,QAAMnB,gBAAgBV,aAAa;IAAE4B,OAAOJ;EAAS,CAAA;AAErD,MAAIR,QAAQc,OAAO;AACjBC,6BAAUC,sBAAsB;MAC9BC,cAAcjB,QAAQc,MAAMG;MAC5BC,SAASlB,QAAQc,MAAMI;IACzB,CAAA;EACF;AAEA,OAAIlB,aAAQL,eAARK,mBAAoBF,aAAa;AACnCiB,6BAAUI,oBAAoB;MAC5BrB,aAAa;QACX,GAAIE,QAAQL,WAAWG;MACzB;IACF,CAAA;EACF;AAEAiB,2BAAUI,oBAAoB;IAC5BC,SAAS;MACPC,gBAAgBrB,QAAQqB,eAAeC,YAAYC;IACrD;EACF,CAAA;AAEA,SAAO;IACLC,SAASxC,aAAa;MAAE6B,WAAW;MAAgBD,OAAOJ;IAAS,CAAA;IACnEP,QAAQF,YAAAA;IACR0B,SAAS;UAACC,2CAAAA;MAAkBzC;;IAC5B0C,SAAS,CAAC9B,QACRN,cAAcM,KAAKG,SAASN,eAAeM,QAAQL,UAAU;EACjE;AACF;AAlCgBY;;;AD/ET,SAASqB,+BAA+BC,SAA0C;AAJzF;AAKE,QAAMC,eAAeC,gBAAgBF,OAAAA;AAErCG,2BAAUC,oBAAoB;IAC5BC,SAAS;MACPC,WAAW;IACb;EACF,CAAA;AAEA,OAAIN,aAAQO,eAARP,mBAAoBQ,aAAa;AACnCL,6BAAUC,oBAAoB;MAC5BI,aAAaR,QAAQO,WAAWC;IAClC,CAAA;EACF;AAEAL,2BAAUM,QACR,gCACAC,gCAAgCV,QAAQK,OAAO,CAAA;AAGjD,QAAMM,UAASV,aAAaW;AAC5BD,EAAAA,QAAOE,MAAM,6BAAA;AAEb,QAAMC,WAAOC,iCAAW;IACtB,GAAGd;IACHe,iBAAiBhB,QAAQiB;EAC3B,CAAA;AAEA,SAAOH;AACT;AA7BgBf;", "names": ["module", "import_graphql_yoga", "import_type_graphql", "import_type_graphql", "import_rxjs", "import_type_graphql", "import_type_graphql", "MessageRole", "CopilotRequestType", "ActionInputAvailability", "registerEnumType", "name", "description", "import_type_graphql", "BaseMessageInput", "id", "createdAt", "Field", "String", "Date", "InputType", "MessageInput", "BaseMessageInput", "textMessage", "actionExecutionMessage", "resultMessage", "agentStateMessage", "imageMessage", "Field", "TextMessageInput", "nullable", "ActionExecutionMessageInput", "ResultMessageInput", "AgentStateMessageInput", "ImageMessageInput", "InputType", "content", "parentMessageId", "role", "String", "MessageRole", "name", "arguments", "scope", "deprecationReason", "actionExecutionId", "actionName", "result", "threadId", "<PERSON><PERSON><PERSON>", "state", "running", "nodeName", "runId", "active", "Boolean", "format", "bytes", "import_type_graphql", "import_type_graphql", "ActionInput", "name", "description", "jsonSchema", "available", "Field", "String", "ActionInputAvailability", "nullable", "InputType", "FrontendInput", "toDeprecate_fullContext", "actions", "url", "Field", "String", "nullable", "ActionInput", "InputType", "import_type_graphql", "import_type_graphql", "GuardrailsRuleInput", "allowList", "denyList", "Field", "String", "nullable", "InputType", "GuardrailsInput", "inputValidationRules", "CloudInput", "guardrails", "Field", "GuardrailsInput", "nullable", "InputType", "import_type_graphql", "ForwardedParametersInput", "model", "maxTokens", "stop", "toolChoice", "toolChoiceFunctionName", "temperature", "Field", "String", "nullable", "Number", "InputType", "import_type_graphql", "AgentSessionInput", "<PERSON><PERSON><PERSON>", "threadId", "nodeName", "Field", "String", "nullable", "InputType", "import_type_graphql", "AgentStateInput", "<PERSON><PERSON><PERSON>", "state", "config", "Field", "String", "nullable", "InputType", "import_type_graphql", "ExtensionsInput", "openaiAssistantAPI", "Field", "OpenAIApiAssistantAPIInput", "nullable", "InputType", "runId", "threadId", "String", "import_type_graphql", "import_type_graphql", "import_type_graphql", "import_type_graphql", "MessageStatusCode", "registerEnumType", "name", "BaseMessageStatus", "code", "Field", "ObjectType", "PendingMessageStatus", "SuccessMessageStatus", "FailedMessageStatus", "reason", "String", "MessageStatusUnion", "createUnionType", "types", "import_type_graphql", "ResponseStatusCode", "registerEnumType", "name", "BaseResponseStatus", "code", "Field", "InterfaceType", "resolveType", "value", "SuccessResponseStatus", "FailedResponseStatus", "PendingResponseStatus", "undefined", "ObjectType", "implements", "FailedResponseStatusReason", "reason", "details", "GraphQLJSON", "nullable", "ResponseStatusUnion", "createUnionType", "types", "import_type_graphql", "ExtensionsResponse", "openaiAssistantAPI", "Field", "OpenAIApiAssistantAPIResponse", "nullable", "ObjectType", "runId", "threadId", "String", "BaseMessageOutput", "id", "createdAt", "status", "Field", "String", "Date", "MessageStatusUnion", "InterfaceType", "resolveType", "value", "hasOwnProperty", "TextMessageOutput", "ActionExecutionMessageOutput", "ResultMessageOutput", "AgentStateMessageOutput", "ImageMessageOutput", "undefined", "role", "content", "parentMessageId", "MessageRole", "nullable", "ObjectType", "implements", "name", "scope", "arguments", "deprecationReason", "actionExecutionId", "actionName", "result", "threadId", "<PERSON><PERSON><PERSON>", "nodeName", "runId", "active", "state", "running", "Boolean", "format", "bytes", "CopilotResponse", "messages", "extensions", "metaEvents", "ResponseStatusUnion", "ExtensionsResponse", "BaseMetaEvent", "MetaEventName", "registerEnumType", "name", "description", "BaseMetaEvent", "type", "Field", "String", "InterfaceType", "resolveType", "value", "LangGraphInterruptEvent", "CopilotKitLangGraphInterruptEvent", "undefined", "CopilotKitLangGraphInterruptEventData", "messages", "BaseMessageOutput", "ObjectType", "response", "nullable", "implements", "data", "MetaEventInput", "name", "value", "response", "messages", "Field", "MetaEventName", "String", "nullable", "MessageInput", "InputType", "GenerateCopilotResponseMetadataInput", "requestType", "Field", "CopilotRequestType", "nullable", "InputType", "GenerateCopilotResponseInput", "metadata", "threadId", "runId", "messages", "frontend", "cloud", "forwardedParameters", "agentSession", "agentState", "agentStates", "extensions", "metaEvents", "String", "MessageInput", "FrontendInput", "CloudInput", "ForwardedParametersInput", "AgentSessionInput", "AgentStateInput", "ExtensionsInput", "MetaEventInput", "import_shared", "import_rxjs", "import_shared", "import_shared", "EndpointType", "import_shared", "Message", "BaseMessageInput", "type", "isTextMessage", "isActionExecutionMessage", "isResultMessage", "isAgentStateMessage", "isImageMessage", "TextMessage", "content", "role", "parentMessageId", "ActionExecutionMessage", "name", "arguments", "ResultMessage", "actionExecutionId", "actionName", "result", "encodeResult", "error", "errorObj", "code", "message", "Error", "undefined", "JSON", "stringify", "decodeResult", "parsed", "parse", "e", "<PERSON><PERSON><PERSON><PERSON>", "getError", "AgentStateMessage", "threadId", "<PERSON><PERSON><PERSON>", "nodeName", "runId", "active", "state", "running", "resolveEndpointType", "endpoint", "type", "EndpointType", "LangGraphPlatform", "CopilotKit", "packageJson", "require", "telemetryClient", "TelemetryClient", "packageName", "name", "packageVersion", "version", "getRuntimeInstanceTelemetryInfo", "runtime", "endpointsInfo", "remoteEndpointDefinitions", "reduce", "acc", "endpoint", "info", "endpointType", "resolveEndpointType", "endpointTypes", "includes", "EndpointType", "LangGraphPlatform", "ep", "agentsAmount", "agents", "length", "hashed<PERSON><PERSON>", "langsmithApiKey", "createHash", "update", "digest", "actionsAmount", "actions", "endpointsAmount", "hashedLgcKey", "RuntimeEventTypes", "RuntimeMetaEventName", "import_graphql_scalars", "import_class_transformer", "GuardrailsValidationFailureResponse", "FailedResponseStatus", "reason", "FailedResponseStatusReason", "GUARDRAILS_VALIDATION_FAILED", "constructor", "guardrailsReason", "details", "MessageStreamInterruptedResponse", "MESSAGE_STREAM_INTERRUPTED", "messageId", "description", "UnknownErrorResponse", "UNKNOWN_ERROR", "import_shared", "import_type_graphql", "Agent", "id", "name", "description", "Field", "String", "ObjectType", "AgentsResponse", "agents", "invokeGuardrails", "baseUrl", "copilotCloudPublicApiKey", "data", "onResult", "onError", "messages", "length", "textMessage", "role", "MessageRole", "user", "filter", "m", "undefined", "assistant", "map", "content", "lastMessage", "restOfMessages", "slice", "body", "input", "validTopics", "cloud", "guardrails", "inputValidationRules", "allowList", "invalidTopics", "denyList", "guardrailsResult", "fetch", "method", "headers", "JSON", "stringify", "ok", "result<PERSON><PERSON>", "json", "CopilotResolver", "hello", "availableAgents", "ctx", "logger", "child", "component", "debug", "agentsWithEndpoints", "_copilotkit", "runtime", "discoverAgentsFromEndpoints", "agents", "endpoint", "agentWithoutEndpoint", "generateCopilotResponse", "properties", "telemetry", "capture", "requestType", "metadata", "copilotRuntime", "serviceAdapter", "copilotCloudBaseUrl", "key", "request", "get", "error", "GraphQLError", "process", "env", "COPILOT_CLOUD_BASE_URL", "responseStatus$", "ReplaySubject", "interruptStreaming$", "guardrailsResult$", "outputMessages", "resolveOutputMessagesPromise", "rejectOutputMessagesPromise", "outputMessagesPromise", "Promise", "resolve", "reject", "eventSource", "threadId", "randomId", "runId", "serverSideActions", "actionInputsWithoutAgents", "extensions", "processRuntimeRequest", "actions", "frontend", "action", "available", "ActionInputAvailability", "disabled", "publicApiKey", "graphqlContext", "forwardedParameters", "agentSession", "agentStates", "url", "metaEvents", "eventStream", "processRuntimeEvents", "find", "serverSideAction", "name", "pipe", "shareReplay", "finalize", "response", "status", "firstValueFrom", "<PERSON><PERSON><PERSON>", "push", "stop", "eventStreamSubscription", "subscribe", "next", "event", "type", "RuntimeEventTypes", "MetaEvent", "RuntimeMetaEventName", "LangGraphInterruptEvent", "plainToInstance", "value", "CopilotKitLangGraphInterruptEvent", "message", "TextMessage", "id", "createdAt", "Date", "SuccessMessageStatus", "ActionExecutionMessage", "arguments", "Error", "err", "UnknownErrorResponse", "description", "unsubscribe", "complete", "SuccessResponseStatus", "pushMessage", "stopStreamingMessages", "result", "GuardrailsValidationFailureResponse", "guardrailsReason", "reason", "TextMessageStart", "textMessageContentStream", "<PERSON><PERSON><PERSON><PERSON>", "e", "<PERSON><PERSON><PERSON><PERSON>", "TextMessageEnd", "messageId", "TextMessageContent", "streamingTextStatus", "Subject", "parentMessageId", "pushTextChunk", "stopStreamingText", "textChunks", "textSubscription", "take", "tap", "FailedMessageStatus", "MessageStreamInterruptedResponse", "join", "ActionExecutionStart", "actionExecutionArgumentStream", "ActionExecutionEnd", "actionExecutionId", "ActionExecutionArgs", "streamingArgumentsStatus", "actionName", "pushArgumentsChunk", "stopStreamingArguments", "argumentChunks", "actionExecutionArgumentSubscription", "args", "ActionExecutionResult", "ResultMessage", "AgentStateMessage", "<PERSON><PERSON><PERSON>", "nodeName", "active", "state", "running", "Query", "String", "AgentsResponse", "Ctx", "Mutation", "CopilotResponse", "Arg", "GraphQLJSONObject", "nullable", "Resolver", "createLogger", "options", "level", "component", "stream", "pretty", "colorize", "logger", "createPinoLogger", "process", "env", "LOG_LEVEL", "redact", "paths", "remove", "child", "import_type_graphql", "import_type_graphql", "LoadAgentStateResponse", "threadId", "threadExists", "state", "messages", "Field", "String", "Boolean", "ObjectType", "import_type_graphql", "LoadAgentStateInput", "threadId", "<PERSON><PERSON><PERSON>", "Field", "String", "InputType", "StateResolver", "loadAgentState", "ctx", "data", "agents", "_copilotkit", "runtime", "discoverAgentsFromEndpoints", "agent", "find", "name", "<PERSON><PERSON><PERSON>", "threadId", "threadExists", "state", "JSON", "stringify", "messages", "Query", "LoadAgentStateResponse", "Ctx", "Arg", "Resolver", "packageJson", "logger", "createLogger", "addCustomHeaderPlugin", "onResponse", "response", "headers", "set", "version", "createContext", "initialContext", "copilotKitContext", "contextLogger", "properties", "debug", "ctx", "_copilotkit", "buildSchema", "options", "schema", "buildSchemaSync", "resolvers", "CopilotResolver", "StateResolver", "emitSchemaFile", "getCommonConfig", "logLevel", "process", "env", "LOG_LEVEL", "level", "component", "cloud", "telemetry", "setCloudConfiguration", "publicApiKey", "baseUrl", "setGlobalProperties", "runtime", "serviceAdapter", "constructor", "name", "logging", "plugins", "useDeferStream", "context", "copilotRuntimeNodeHttpEndpoint", "options", "commonConfig", "getCommonConfig", "telemetry", "setGlobalProperties", "runtime", "framework", "properties", "_copilotkit", "capture", "getRuntimeInstanceTelemetryInfo", "logger", "logging", "debug", "yoga", "createYoga", "graphqlEndpoint", "endpoint"]}