"use strict";
/* eslint-disable @typescript-eslint/no-unsafe-function-type */
Object.defineProperty(exports, "__esModule", { value: true });
exports.pipe = pipe;
function pipe(a, ab, bc, cd, de, ef, fg, gh, hi) {
    switch (arguments.length) {
        case 1:
            return a;
        case 2:
            return ab(a);
        case 3:
            return bc(ab(a));
        case 4:
            return cd(bc(ab(a)));
        case 5:
            return de(cd(bc(ab(a))));
        case 6:
            return ef(de(cd(bc(ab(a)))));
        case 7:
            return fg(ef(de(cd(bc(ab(a))))));
        case 8:
            return gh(fg(ef(de(cd(bc(ab(a)))))));
        case 9:
            return hi(gh(fg(ef(de(cd(bc(ab(a))))))));
        default:
            // eslint-disable-next-line no-case-declarations, prefer-rest-params
            let ret = arguments[0];
            for (let i = 1; i < arguments.length; i++) {
                // eslint-disable-next-line prefer-rest-params
                ret = arguments[i](ret);
            }
            return ret;
    }
}
