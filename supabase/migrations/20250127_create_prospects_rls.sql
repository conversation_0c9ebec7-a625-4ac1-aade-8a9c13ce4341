-- Row Level Security policies for prospects tables
-- Ensures proper access control for prospect data

-- Enable R<PERSON> on all prospect tables
ALTER TABLE public.prospects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prospect_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prospect_notes ENABLE ROW LEVEL SECURITY;

-- Prospects table policies
-- Allow anonymous users to insert (for signup forms)
CREATE POLICY "Allow anonymous prospect creation" ON public.prospects
    FOR INSERT TO anon
    WITH CHECK (true);

-- Allow prospects to view their own data via email verification token
CREATE POLICY "Allow prospect self-access via token" ON public.prospects
    FOR SELECT TO anon
    USING (
        email_verification_token IS NOT NULL AND 
        email_verification_token = current_setting('request.jwt.claims', true)::json->>'verification_token'
    );

-- Allow prospects to update their own preferences via token
CREATE POLICY "Allow prospect self-update via token" ON public.prospects
    FOR UPDATE TO anon
    USING (
        email_verification_token IS NOT NULL AND 
        email_verification_token = current_setting('request.jwt.claims', true)::json->>'verification_token'
    )
    WITH CHECK (
        email_verification_token IS NOT NULL AND 
        email_verification_token = current_setting('request.jwt.claims', true)::json->>'verification_token'
    );

-- Allow authenticated users to view prospects (for admin/marketing purposes)
CREATE POLICY "Allow authenticated users to view prospects" ON public.prospects
    FOR SELECT TO authenticated
    USING (true);

-- Allow super admins to manage all prospects
CREATE POLICY "Allow super admin full access to prospects" ON public.prospects
    FOR ALL TO authenticated
    USING (
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    )
    WITH CHECK (
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    );

-- Allow service role full access (for backend operations)
CREATE POLICY "Allow service role full access to prospects" ON public.prospects
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Prospect interactions policies
-- Allow anonymous users to create interactions (tracking)
CREATE POLICY "Allow anonymous interaction creation" ON public.prospect_interactions
    FOR INSERT TO anon
    WITH CHECK (true);

-- Allow authenticated users to view interactions
CREATE POLICY "Allow authenticated users to view interactions" ON public.prospect_interactions
    FOR SELECT TO authenticated
    USING (true);

-- Allow super admins full access to interactions
CREATE POLICY "Allow super admin full access to interactions" ON public.prospect_interactions
    FOR ALL TO authenticated
    USING (
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    )
    WITH CHECK (
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    );

-- Allow service role full access to interactions
CREATE POLICY "Allow service role full access to interactions" ON public.prospect_interactions
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Prospect notes policies
-- Allow authenticated users to create notes
CREATE POLICY "Allow authenticated users to create notes" ON public.prospect_notes
    FOR INSERT TO authenticated
    WITH CHECK (true);

-- Allow authenticated users to view notes
CREATE POLICY "Allow authenticated users to view notes" ON public.prospect_notes
    FOR SELECT TO authenticated
    USING (true);

-- Allow note creators to update their own notes
CREATE POLICY "Allow note creators to update own notes" ON public.prospect_notes
    FOR UPDATE TO authenticated
    USING (
        created_by_user_id = auth.uid() OR
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    )
    WITH CHECK (
        created_by_user_id = auth.uid() OR
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    );

-- Allow super admins full access to notes
CREATE POLICY "Allow super admin full access to notes" ON public.prospect_notes
    FOR ALL TO authenticated
    USING (
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    )
    WITH CHECK (
        COALESCE(
            (current_setting('request.jwt.claims', true)::json->>'is_super_admin')::boolean,
            false
        ) = true
    );

-- Allow service role full access to notes
CREATE POLICY "Allow service role full access to notes" ON public.prospect_notes
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Grant necessary permissions
GRANT SELECT, INSERT ON public.prospects TO anon;
GRANT SELECT, INSERT ON public.prospect_interactions TO anon;
GRANT SELECT, INSERT, UPDATE ON public.prospects TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.prospect_interactions TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.prospect_notes TO authenticated;

-- Grant sequence permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
