import {
  CopilotDevConsole
} from "./chunk-VGPQYMKJ.mjs";
import {
  useChatContext
} from "./chunk-IEMQ2SQW.mjs";

// src/components/chat/Header.tsx
import { jsx, jsxs } from "react/jsx-runtime";
var Header = ({}) => {
  const { setOpen, icons, labels } = useChatContext();
  return /* @__PURE__ */ jsxs("div", { className: "copilotKitHeader", children: [
    /* @__PURE__ */ jsx("div", { children: labels.title }),
    /* @__PURE__ */ jsxs("div", { className: "copilotKitHeaderControls", children: [
      /* @__PURE__ */ jsx(CopilotDevConsole, {}),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: () => setOpen(false),
          "aria-label": "Close",
          className: "copilotKitHeaderCloseButton",
          children: icons.headerCloseIcon
        }
      )
    ] })
  ] });
};

export {
  Header
};
//# sourceMappingURL=chunk-UH2UFL5W.mjs.map