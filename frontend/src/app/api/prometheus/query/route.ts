import { NextRequest, NextResponse } from 'next/server';
import { verifyJWT } from '@/lib/auth/jwt';

/**
 * Prometheus Proxy API Route
 * 
 * This endpoint proxies requests to Prometheus to avoid CORS issues
 * and provides authentication for super admin access only.
 */

interface PrometheusResponse {
  status: string;
  data: {
    resultType: string;
    result: Array<{
      metric: Record<string, string>;
      value: [number, string];
    }>;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Validate JWT and check for super admin access
    const authResult = await verifyJWT(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized: Super admin access required' },
        { status: 403 }
      );
    }

    // Get query parameter
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');

    if (!query) {
      return NextResponse.json(
        { error: 'Missing query parameter' },
        { status: 400 }
      );
    }

    // Get Prometheus URL from environment
    const prometheusUrl = process.env.PROMETHEUS_URL || 'http://localhost:9090';
    
    // Construct Prometheus API URL
    const prometheusApiUrl = `${prometheusUrl}/api/v1/query?query=${encodeURIComponent(query)}`;

    // Fetch from Prometheus
    const response = await fetch(prometheusApiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    if (!response.ok) {
      console.error(`Prometheus API error: ${response.status} ${response.statusText}`);
      
      // Return mock data for development/testing when Prometheus is not available
      if (process.env.NODE_ENV === 'development') {
        const mockResponse: PrometheusResponse = {
          status: 'success',
          data: {
            resultType: 'vector',
            result: [{
              metric: {},
              value: [Date.now() / 1000, getMockValue(query)]
            }]
          }
        };
        
        return NextResponse.json(mockResponse);
      }
      
      return NextResponse.json(
        { error: `Prometheus server error: ${response.status}` },
        { status: response.status }
      );
    }

    const data: PrometheusResponse = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Prometheus proxy error:', error);
    
    // Return mock data for development when there's an error
    if (process.env.NODE_ENV === 'development') {
      const query = new URL(request.url).searchParams.get('query') || '';
      const mockResponse: PrometheusResponse = {
        status: 'success',
        data: {
          resultType: 'vector',
          result: [{
            metric: {},
            value: [Date.now() / 1000, getMockValue(query)]
          }]
        }
      };
      
      return NextResponse.json(mockResponse);
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Generate mock values for development/testing
 */
function getMockValue(query: string): string {
  // Generate realistic mock values based on the query
  if (query.includes('avr_active_calls_total')) {
    return Math.floor(Math.random() * 25).toString(); // 0-25 active calls
  }
  
  if (query.includes('avr_queue_depth_total')) {
    return Math.floor(Math.random() * 8).toString(); // 0-8 queue depth
  }
  
  if (query.includes('avr_call_duration_seconds_bucket')) {
    return (Math.random() * 3 + 0.5).toFixed(3); // 0.5-3.5 seconds latency
  }
  
  if (query.includes('avr_call_rejects_total')) {
    return Math.floor(Math.random() * 3).toString(); // 0-3 rejects per minute
  }
  
  // Default mock value
  return Math.floor(Math.random() * 100).toString();
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
