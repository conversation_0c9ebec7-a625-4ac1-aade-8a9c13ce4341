{"version": 3, "sources": ["../../src/commands/base-command.ts", "../../src/utils/version.ts", "../../src/utils/trpc.ts"], "sourcesContent": ["import { Command } from \"@oclif/core\";\nimport Sentry, { consoleIntegration } from \"@sentry/node\";\nimport { LIB_VERSION } from \"../utils/version.js\";\nimport { COPILOT_CLOUD_BASE_URL } from \"../utils/trpc.js\";\nimport chalk from \"chalk\";\n\nexport class BaseCommand extends Command {\n  async init() {\n    await this.checkCLIVersion();\n\n    if (process.env.SENTRY_DISABLED === 'true') {\n      return;\n    }\n\n    Sentry.init({\n      dsn: process.env.SENTRY_DSN || \"https://<EMAIL>/4508581448581120\",\n      integrations: [\n        consoleIntegration(),\n      ],\n      // Tracing\n      tracesSampleRate: 1.0, //  Capture 100% of the transactions\n    });\n  }\n\n  async catch(err: any) {\n    if (process.env.SENTRY_DISABLED === 'true') {\n      super.catch(err)\n      return;\n    }\n\n    Sentry.captureException(err)\n    super.catch(err)\n  }\n\n  async finally() {\n    if (process.env.SENTRY_DISABLED === 'true') {\n      return;\n    }\n\n    Sentry.close()\n  }\n  \n  async run() {}\n\n  async checkCLIVersion() {\n    const response = await fetch(`${COPILOT_CLOUD_BASE_URL}/api/healthz`)\n    const data = await response.json()\n    const cloudVersion = data.cliVersion\n\n    if (cloudVersion === LIB_VERSION) {\n      return;\n    }\n\n    this.log(chalk.yellow('================ New version available! =================\\n'))\n    this.log(`A new CopilotKit CLI version is available (${LIB_VERSION}).\\n`)\n    this.log('Please update your CLI to the latest version:\\n\\n')\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('npm:')))}\\t npm install -g @copilotkit/cli@latest\\n`)\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('pnpm:')))}\\t pnpm install -g @copilotkit/cli@latest\\n`)\n    this.log(`${chalk.cyan(chalk.underline(chalk.bold('yarn:')))}\\t yarn global add @copilotkit/cli@latest\\n`)\n\n    process.exit(0)\n  }\n}\n", "// This is auto generated!\nexport const LIB_VERSION = \"0.0.6\";\n", "import {createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink} from '@trpc/client'\nimport type {CLIRouter} from '@repo/trpc-cli'\nimport superjson from 'superjson'\n\nexport const COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || 'https://cloud.copilotkit.ai'\n\nexport function createTRPCClient(cliToken: string) {\n  return createTRPClient_<CLIRouter>({\n    links: [\n      unstable_httpBatchStreamLink({\n        transformer: superjson,\n        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,\n        headers: () => {\n          const headers = new Headers()\n          headers.set('x-trpc-source', 'cli')\n          headers.set('x-cli-token', cliToken)\n          return headers\n        },\n      }),\n    ],\n  })\n}\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,OAAO,UAAU,0BAA0B;;;ACApC,IAAM,cAAc;;;ACD3B,SAAQ,oBAAoB,kBAAkB,oCAAmC;AAEjF,OAAO,eAAe;AAEf,IAAM,yBAAyB,QAAQ,IAAI,0BAA0B;;;AFA5E,OAAO,WAAW;AAEX,IAAM,cAAN,cAA0B,QAAQ;AAAA,EACvC,MAAM,OAAO;AACX,UAAM,KAAK,gBAAgB;AAE3B,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C;AAAA,IACF;AAEA,WAAO,KAAK;AAAA,MACV,KAAK,QAAQ,IAAI,cAAc;AAAA,MAC/B,cAAc;AAAA,QACZ,mBAAmB;AAAA,MACrB;AAAA;AAAA,MAEA,kBAAkB;AAAA;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EAEA,MAAM,MAAM,KAAU;AACpB,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C,YAAM,MAAM,GAAG;AACf;AAAA,IACF;AAEA,WAAO,iBAAiB,GAAG;AAC3B,UAAM,MAAM,GAAG;AAAA,EACjB;AAAA,EAEA,MAAM,UAAU;AACd,QAAI,QAAQ,IAAI,oBAAoB,QAAQ;AAC1C;AAAA,IACF;AAEA,WAAO,MAAM;AAAA,EACf;AAAA,EAEA,MAAM,MAAM;AAAA,EAAC;AAAA,EAEb,MAAM,kBAAkB;AACtB,UAAM,WAAW,MAAM,MAAM,GAAG,sBAAsB,cAAc;AACpE,UAAM,OAAO,MAAM,SAAS,KAAK;AACjC,UAAM,eAAe,KAAK;AAE1B,QAAI,iBAAiB,aAAa;AAChC;AAAA,IACF;AAEA,SAAK,IAAI,MAAM,OAAO,6DAA6D,CAAC;AACpF,SAAK,IAAI,8CAA8C,WAAW;AAAA,CAAM;AACxE,SAAK,IAAI,mDAAmD;AAC5D,SAAK,IAAI,GAAG,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,CAA4C;AACvG,SAAK,IAAI,GAAG,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,CAA6C;AACzG,SAAK,IAAI,GAAG,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC;AAAA,CAA6C;AAEzG,YAAQ,KAAK,CAAC;AAAA,EAChB;AACF;", "names": []}