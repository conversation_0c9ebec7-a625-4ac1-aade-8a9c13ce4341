/**
 * The default categories of evaluations to run if none is specified.
 * These categories represent different styles or types of tasks.
 */
declare const DEFAULT_EVAL_CATEGORIES: string[];
declare const useTextExtract: boolean;
declare const useAccessibilityTree: boolean;
/**
 * Variables for filtering which tasks to run:
 * - `filterByCategory`: if provided, only tasks that belong to this category will be run.
 * - `filterByEvalName`: if provided, only the task with this name will be run.
 */
declare let filterByCategory: string | null;
declare let filterByEvalName: string | null;
export { filterByCategory, filterByEvalName, useTextExtract, useAccessibilityTree, DEFAULT_EVAL_CATEGORIES, };
