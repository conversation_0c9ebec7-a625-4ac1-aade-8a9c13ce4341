{"version": 3, "sources": ["../../src/lib/status-checker.ts"], "sourcesContent": ["import {\n  COPILOT_CLOUD_API_URL,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  Severity,\n} from \"@copilotkit/shared\";\n\nconst STATUS_CHECK_INTERVAL = 1000 * 60 * 5; // 5 minutes\n\nexport type Status = {\n  severity: Severity;\n  message: string;\n};\n\nexport class StatusChecker {\n  private activeKey: string | null = null;\n  private intervalId: ReturnType<typeof setInterval> | null = null;\n  private instanceCount = 0;\n  private lastResponse: Status | null = null;\n\n  async start(publicApiKey: string, onUpdate?: (status: Status | null) => void) {\n    this.instanceCount++;\n    if (this.activeKey === publicApiKey) return;\n\n    if (this.intervalId) clearInterval(this.intervalId);\n\n    const checkStatus = async () => {\n      try {\n        const response = await fetch(`${COPILOT_CLOUD_API_URL}/ciu`, {\n          method: \"GET\",\n          headers: {\n            [COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey,\n          },\n        }).then((response) => response.json() as Promise<Status>);\n        this.lastResponse = response;\n        onUpdate?.(response);\n        return response;\n      } catch (error) {\n        // Silently fail\n        return null;\n      }\n    };\n\n    const initialResponse = await checkStatus();\n    this.intervalId = setInterval(checkStatus, STATUS_CHECK_INTERVAL);\n    this.activeKey = publicApiKey;\n    return initialResponse;\n  }\n\n  getLastResponse() {\n    return this.lastResponse;\n  }\n\n  stop() {\n    this.instanceCount--;\n    if (this.instanceCount === 0) {\n      if (this.intervalId) {\n        clearInterval(this.intervalId);\n        this.intervalId = null;\n        this.activeKey = null;\n        this.lastResponse = null;\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAIO;AAEP,IAAM,wBAAwB,MAAO,KAAK;AAOnC,IAAM,gBAAN,MAAoB;AAAA,EAApB;AACL,SAAQ,YAA2B;AACnC,SAAQ,aAAoD;AAC5D,SAAQ,gBAAgB;AACxB,SAAQ,eAA8B;AAAA;AAAA,EAEhC,MAAM,cAAsB,UAA4C;AAAA;AAC5E,WAAK;AACL,UAAI,KAAK,cAAc;AAAc;AAErC,UAAI,KAAK;AAAY,sBAAc,KAAK,UAAU;AAElD,YAAM,cAAc,MAAY;AAC9B,YAAI;AACF,gBAAM,WAAW,MAAM,MAAM,GAAG,2CAA6B;AAAA,YAC3D,QAAQ;AAAA,YACR,SAAS;AAAA,cACP,CAAC,iDAAmC,GAAG;AAAA,YACzC;AAAA,UACF,CAAC,EAAE,KAAK,CAACA,cAAaA,UAAS,KAAK,CAAoB;AACxD,eAAK,eAAe;AACpB,+CAAW;AACX,iBAAO;AAAA,QACT,SAAS,OAAP;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,kBAAkB,MAAM,YAAY;AAC1C,WAAK,aAAa,YAAY,aAAa,qBAAqB;AAChE,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,OAAO;AACL,SAAK;AACL,QAAI,KAAK,kBAAkB,GAAG;AAC5B,UAAI,KAAK,YAAY;AACnB,sBAAc,KAAK,UAAU;AAC7B,aAAK,aAAa;AAClB,aAAK,YAAY;AACjB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;", "names": ["response"]}