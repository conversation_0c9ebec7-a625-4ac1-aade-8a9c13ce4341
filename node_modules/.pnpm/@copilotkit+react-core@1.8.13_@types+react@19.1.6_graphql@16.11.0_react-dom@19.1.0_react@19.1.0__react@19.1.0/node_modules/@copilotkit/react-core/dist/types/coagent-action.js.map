{"version": 3, "sources": ["../../src/types/coagent-action.ts"], "sourcesContent": ["export type CoAgentStateRenderProps<T> = {\n  state: T;\n  nodeName: string;\n  status: \"inProgress\" | \"complete\";\n};\n\nexport type CoAgentStateRenderHandlerArguments<T> = {\n  nodeName: string;\n  state: T;\n};\n\nexport interface CoAgentStateRender<T = any> {\n  /**\n   * The name of the coagent.\n   */\n  name: string;\n  /**\n   * The node name of the coagent.\n   */\n  nodeName?: string;\n  /**\n   * The handler function to handle the state of the agent.\n   */\n  handler?: (props: CoAgentStateRenderHandlerArguments<T>) => void | Promise<void>;\n  /**\n   * The render function to handle the state of the agent.\n   */\n  render?:\n    | ((props: CoAgentStateRenderProps<T>) => string | React.ReactElement | undefined | null)\n    | string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}