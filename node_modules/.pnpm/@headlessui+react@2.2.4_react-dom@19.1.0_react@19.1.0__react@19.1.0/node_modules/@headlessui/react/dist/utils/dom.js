function o(e){return typeof e!="object"||e===null?!1:"nodeType"in e}function t(e){return o(e)&&"tagName"in e}function n(e){return t(e)&&"accessKey"in e}function i(e){return t(e)&&"tabIndex"in e}function r(e){return t(e)&&"style"in e}function u(e){return n(e)&&e.nodeName==="IFRAME"}function l(e){return n(e)&&e.nodeName==="INPUT"}function s(e){return n(e)&&e.nodeName==="TEXTAREA"}function m(e){return n(e)&&e.nodeName==="LABEL"}function a(e){return n(e)&&e.nodeName==="FIELDSET"}function E(e){return n(e)&&e.nodeName==="LEGEND"}function L(e){return t(e)?e.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]'):!1}export{r as hasInlineStyle,t as isElement,n as isHTMLElement,a as isHTMLFieldSetElement,u as isHTMLIframeElement,l as isHTMLInputElement,m as isHTMLLabelElement,E as isHTMLLegendElement,s as isHTMLTextAreaElement,i as isHTMLorSVGElement,L as isInteractiveElement,o as isNode};
