"use client";import o,{use<PERSON>emo as b}from"react";import{useResolvedTag as E}from'../../hooks/use-resolved-tag.js';import{useSyncRefs as P}from'../../hooks/use-sync-refs.js';import{Disabled<PERSON>rovider as u,useDisabled as g}from'../../internal/disabled.js';import{forwardRefWithAs as D,useRender as A}from'../../utils/render.js';import{useLabels as L}from'../label/label.js';let d="fieldset";function _(t,a){var s;let i=g(),{disabled:e=i||!1,...p}=t,[n,T]=E((s=t.as)!=null?s:d),l=P(a,T),[r,f]=L(),m=b(()=>({disabled:e}),[e]),y=n==="fieldset"?{ref:l,"aria-labelledby":r,disabled:e||void 0}:{ref:l,role:"group","aria-labelledby":r,"aria-disabled":e||void 0},F=A();return o.createElement(u,{value:e},o.createElement(f,null,F({ourProps:y,theirProps:p,slot:m,defaultTag:d,name:"Fieldset"})))}let G=D(_);export{G as Fieldset};
