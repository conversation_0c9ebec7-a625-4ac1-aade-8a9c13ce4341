import {
  ExclamationMarkIcon
} from "./chunk-O7ARI5CV.mjs";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-SKC7AJIV.mjs";

// src/components/error-boundary/error-utils.tsx
import { useCallback as useCallback2 } from "react";

// src/components/toast/toast-provider.tsx
import { createContext, useContext, useState, useCallback } from "react";
import { jsx, jsxs } from "react/jsx-runtime";
var ToastContext = createContext(void 0);
function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}
function ToastProvider({
  enabled,
  children
}) {
  const [toasts, setToasts] = useState([]);
  const addToast = useCallback(
    (toast) => {
      var _a;
      if (!enabled) {
        return;
      }
      const id = (_a = toast.id) != null ? _a : Math.random().toString(36).substring(2, 9);
      setToasts((currentToasts) => {
        if (currentToasts.find((toast2) => toast2.id === id))
          return currentToasts;
        return [...currentToasts, __spreadProps(__spreadValues({}, toast), { id })];
      });
      if (toast.duration) {
        setTimeout(() => {
          removeToast(id);
        }, toast.duration);
      }
    },
    [enabled]
  );
  const addGraphQLErrorsToast = useCallback((errors) => {
    addToast({
      type: "error",
      message: /* @__PURE__ */ jsx(ErrorToast, { errors })
    });
  }, []);
  const removeToast = useCallback((id) => {
    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));
  }, []);
  const value = {
    toasts,
    addToast,
    addGraphQLErrorsToast,
    removeToast,
    enabled
  };
  return /* @__PURE__ */ jsxs(ToastContext.Provider, { value, children: [
    /* @__PURE__ */ jsxs(
      "div",
      {
        style: {
          position: "fixed",
          bottom: "1rem",
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: 50,
          display: "flex",
          flexDirection: "column",
          gap: "0.5rem"
        },
        children: [
          toasts.length > 1 && /* @__PURE__ */ jsx("div", { style: { textAlign: "right" }, children: /* @__PURE__ */ jsx(
            "button",
            {
              onClick: () => setToasts([]),
              style: {
                padding: "4px 8px",
                fontSize: "12px",
                cursor: "pointer",
                background: "white",
                border: "1px solid rgba(0,0,0,0.2)",
                borderRadius: "4px"
              },
              children: "Close All"
            }
          ) }),
          toasts.map((toast) => /* @__PURE__ */ jsx(
            Toast,
            {
              message: toast.message,
              type: toast.type,
              onClose: () => removeToast(toast.id)
            },
            toast.id
          ))
        ]
      }
    ),
    children
  ] });
}
function Toast({
  message,
  type = "info",
  onClose
}) {
  const bgColors = {
    info: "#3b82f6",
    success: "#22c55e",
    warning: "#eab308",
    error: "#ef4444"
  };
  return /* @__PURE__ */ jsxs(
    "div",
    {
      style: {
        backgroundColor: bgColors[type],
        color: "white",
        padding: "0.5rem 1.5rem",
        borderRadius: "0.25rem",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        position: "relative",
        minWidth: "200px"
      },
      children: [
        /* @__PURE__ */ jsx("div", { children: message }),
        /* @__PURE__ */ jsx(
          "button",
          {
            onClick: onClose,
            style: {
              position: "absolute",
              top: "0",
              right: "0",
              background: "none",
              border: "none",
              color: "white",
              cursor: "pointer",
              padding: "0.5rem",
              fontSize: "1rem"
            },
            children: "\u2715"
          }
        )
      ]
    }
  );
}

// src/components/error-boundary/error-utils.tsx
import ReactMarkdown from "react-markdown";
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
function ErrorToast({ errors }) {
  const errorsToRender = errors.map((error, idx) => {
    var _a, _b, _c;
    const originalError = "extensions" in error ? (_a = error.extensions) == null ? void 0 : _a.originalError : {};
    const message = (_b = originalError == null ? void 0 : originalError.message) != null ? _b : error.message;
    const code = "extensions" in error ? (_c = error.extensions) == null ? void 0 : _c.code : null;
    return /* @__PURE__ */ jsxs2(
      "div",
      {
        style: {
          marginTop: idx === 0 ? 0 : 10,
          marginBottom: 14
        },
        children: [
          /* @__PURE__ */ jsx2(ExclamationMarkIcon, { style: { marginBottom: 4 } }),
          code && /* @__PURE__ */ jsxs2(
            "div",
            {
              style: {
                fontWeight: "600",
                marginBottom: 4
              },
              children: [
                "Copilot Cloud Error:",
                " ",
                /* @__PURE__ */ jsx2("span", { style: { fontFamily: "monospace", fontWeight: "normal" }, children: code })
              ]
            }
          ),
          /* @__PURE__ */ jsx2(ReactMarkdown, { children: message })
        ]
      },
      idx
    );
  });
  return /* @__PURE__ */ jsxs2(
    "div",
    {
      style: {
        fontSize: "13px",
        maxWidth: "600px"
      },
      children: [
        errorsToRender,
        /* @__PURE__ */ jsx2("div", { style: { fontSize: "11px", opacity: 0.75 }, children: "NOTE: This error only displays during local development." })
      ]
    }
  );
}
function useErrorToast() {
  const { addToast } = useToast();
  return useCallback2(
    (error) => {
      const errorId = error.map((err) => {
        var _a, _b;
        const message = "extensions" in err ? ((_b = (_a = err.extensions) == null ? void 0 : _a.originalError) == null ? void 0 : _b.message) || err.message : err.message;
        const stack = err.stack || "";
        return btoa(message + stack).slice(0, 32);
      }).join("|");
      addToast({
        type: "error",
        id: errorId,
        // Toast libraries typically dedupe by id
        message: /* @__PURE__ */ jsx2(ErrorToast, { errors: error })
      });
    },
    [addToast]
  );
}
function useAsyncCallback(callback, deps) {
  const addErrorToast = useErrorToast();
  return useCallback2((...args) => __async(this, null, function* () {
    try {
      return yield callback(...args);
    } catch (error) {
      console.error("Error in async callback:", error);
      addErrorToast([error]);
      throw error;
    }
  }), deps);
}

export {
  ErrorToast,
  useErrorToast,
  useAsyncCallback,
  useToast,
  ToastProvider
};
//# sourceMappingURL=chunk-22ENANUU.mjs.map