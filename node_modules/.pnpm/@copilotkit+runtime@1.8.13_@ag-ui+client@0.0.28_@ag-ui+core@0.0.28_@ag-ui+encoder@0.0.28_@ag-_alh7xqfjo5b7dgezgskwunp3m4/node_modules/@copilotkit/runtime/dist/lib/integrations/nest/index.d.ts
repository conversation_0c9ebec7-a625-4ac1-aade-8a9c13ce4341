import * as graphql_yoga from 'graphql-yoga';
import { b as CreateCopilotRuntimeServerOptions, G as GraphQLContext } from '../../../shared-0c31d7c5.js';
import 'graphql';
import 'pino';
import '@copilotkit/shared';
import '../../../langserve-4a5c9217.js';
import '../../../index-d4614f9b.js';
import '../../../graphql/types/base/index.js';
import 'rxjs';
import '../../cloud/index.js';
import '@ag-ui/client';

declare function copilotRuntimeNestEndpoint(options: CreateCopilotRuntimeServerOptions): graphql_yoga.YogaServerInstance<{}, Partial<GraphQLContext>>;

export { copilotRuntimeNestEndpoint };
