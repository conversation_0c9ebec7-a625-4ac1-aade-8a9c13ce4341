// src/services/auth.service.ts
import Conf2 from "conf";
import cors from "cors";
import express from "express";
import crypto2 from "node:crypto";
import open from "open";
import getPort from "get-port";
import ora from "ora";
import chalk from "chalk";
import inquirer from "inquirer";

// src/utils/trpc.ts
import { createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink } from "@trpc/client";
import superjson from "superjson";
var COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || "https://cloud.copilotkit.ai";
function createTRPCClient(cliToken) {
  return createTRPClient_({
    links: [
      unstable_httpBatchStreamLink({
        transformer: superjson,
        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,
        headers: () => {
          const headers = new Headers();
          headers.set("x-trpc-source", "cli");
          headers.set("x-cli-token", cliToken);
          return headers;
        }
      })
    ]
  });
}

// src/services/analytics.service.ts
import { Analytics } from "@segment/analytics-node";
import Conf from "conf";
var AnalyticsService = class {
  constructor(authData) {
    this.authData = authData;
    if (process.env.SEGMENT_DISABLED === "true") {
      return;
    }
    const segmentWriteKey = process.env.SEGMENT_WRITE_KEY || "9Pv6QyExYef2P4hPz4gks6QAvNMi2AOf";
    this.globalProperties = {
      service: "cli"
    };
    if (this.authData?.userId) {
      this.userId = this.authData.userId;
    }
    if (this.authData?.email) {
      this.email = this.authData.email;
      this.globalProperties.email = this.authData.email;
    }
    if (this.authData?.organizationId) {
      this.organizationId = this.authData.organizationId;
    }
    this.segment = new Analytics({
      writeKey: segmentWriteKey,
      disable: process.env.SEGMENT_DISABLE === "true"
    });
    const config = new Conf({ projectName: "CopilotKitCLI" });
    if (!config.get("anonymousId")) {
      config.set("anonymousId", crypto.randomUUID());
    }
  }
  segment;
  globalProperties = {};
  userId;
  email;
  organizationId;
  config = new Conf({ projectName: "CopilotKitCLI" });
  getAnonymousId() {
    const anonymousId = this.config.get("anonymousId");
    if (!anonymousId) {
      const anonymousId2 = crypto.randomUUID();
      this.config.set("anonymousId", anonymousId2);
      return anonymousId2;
    }
    return anonymousId;
  }
  track(event) {
    if (!this.segment) {
      return;
    }
    const payload = {
      userId: this.userId ? this.userId : void 0,
      email: this.email ? this.email : void 0,
      anonymousId: this.getAnonymousId(),
      event: event.event,
      properties: {
        ...this.globalProperties,
        ...event.properties,
        $groups: this.organizationId ? {
          segment_group: this.organizationId
        } : void 0,
        eventProperties: {
          ...event.properties,
          ...this.globalProperties
        }
      }
    };
    this.segment.track(payload);
  }
};

// src/services/auth.service.ts
var AuthService = class {
  config = new Conf2({ projectName: "CopilotKitCLI" });
  COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || "https://cloud.copilotkit.ai";
  getToken() {
    return this.config.get("cliToken");
  }
  getCLIToken() {
    const cliToken = this.config.get("cliToken");
    return cliToken;
  }
  async logout() {
    const cliToken = this.getCLIToken();
    if (!cliToken) {
      throw new Error("You are not logged in");
    }
    const trpcClient = createTRPCClient(cliToken);
    const me = await trpcClient.me.query();
    const analytics = new AnalyticsService({ userId: me.user.id, organizationId: me.organization.id, email: me.user.email });
    this.config.delete("cliToken");
    analytics.track({
      event: "cli.logout",
      properties: {
        organizationId: me.organization.id,
        userId: me.user.id,
        email: me.user.email
      }
    });
  }
  async requireLogin(cmd) {
    let cliToken = this.getCLIToken();
    if (!cliToken) {
      try {
        const { shouldLogin } = await inquirer.prompt([
          {
            name: "shouldLogin",
            type: "confirm",
            message: "You are not yet authenticated. Authenticate with Copilot Cloud? (press Enter to confirm)",
            default: true
          }
        ]);
        if (shouldLogin) {
          const loginResult = await this.login();
          cliToken = loginResult.cliToken;
          cmd.log(`\u{1FA81} Logged in as ${chalk.hex("#7553fc")(loginResult.user.email)}
`);
          return loginResult;
        } else {
          cmd.error("Authentication required to proceed.");
        }
      } catch (error) {
        if (error instanceof Error && error.name === "ExitPromptError") {
          cmd.error(chalk.yellow("\nAuthentication cancelled"));
        }
        throw error;
      }
    }
    let me;
    const trpcClient = createTRPCClient(cliToken);
    try {
      me = await trpcClient.me.query();
    } catch (error) {
      cmd.log(chalk.red("Could not authenticate with Copilot Cloud. Please try again."));
      process.exit(1);
    }
    if (!me.organization || !me.user) {
      cmd.error("Authentication required to proceed.");
    }
    return { cliToken, user: me.user, organization: me.organization };
  }
  async login() {
    let analytics;
    analytics = new AnalyticsService();
    const app = express();
    app.use(cors());
    app.use(express.urlencoded({ extended: true }));
    app.use(express.json());
    const port = await getPort();
    const state = crypto2.randomBytes(16).toString("hex");
    return new Promise((resolve) => {
      const server = app.listen(port, () => {
      });
      analytics.track({
        event: "cli.login.initiated",
        properties: {}
      });
      const spinner = ora("Waiting for browser authentication to complete...\n").start();
      app.post("/callback", async (req, res) => {
        const { cliToken, user, organization } = req.body;
        analytics = new AnalyticsService({ userId: user.id, organizationId: organization.id, email: user.email });
        analytics.track({
          event: "cli.login.success",
          properties: {
            organizationId: organization.id,
            userId: user.id,
            email: user.email
          }
        });
        if (state !== req.query.state) {
          res.status(401).json({ message: "Invalid state" });
          spinner.fail("Invalid state");
          return;
        }
        this.config.set("cliToken", cliToken);
        res.status(200).json({ message: "Callback called" });
        spinner.succeed(`\u{1FA81} Successfully logged in as ${chalk.hex("#7553fc")(user.email)}
`);
        server.close();
        resolve({
          cliToken,
          organization,
          user
        });
      });
      open(`${this.COPILOT_CLOUD_BASE_URL}/cli-auth?callbackUrl=http://localhost:${port}/callback&state=${state}`);
    });
  }
};

// src/commands/base-command.ts
import { Command } from "@oclif/core";
import Sentry, { consoleIntegration } from "@sentry/node";

// src/utils/version.ts
var LIB_VERSION = "0.0.6";

// src/commands/base-command.ts
import chalk2 from "chalk";
var BaseCommand = class extends Command {
  async init() {
    await this.checkCLIVersion();
    if (process.env.SENTRY_DISABLED === "true") {
      return;
    }
    Sentry.init({
      dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4508581448581120",
      integrations: [
        consoleIntegration()
      ],
      // Tracing
      tracesSampleRate: 1
      //  Capture 100% of the transactions
    });
  }
  async catch(err) {
    if (process.env.SENTRY_DISABLED === "true") {
      super.catch(err);
      return;
    }
    Sentry.captureException(err);
    super.catch(err);
  }
  async finally() {
    if (process.env.SENTRY_DISABLED === "true") {
      return;
    }
    Sentry.close();
  }
  async run() {
  }
  async checkCLIVersion() {
    const response = await fetch(`${COPILOT_CLOUD_BASE_URL}/api/healthz`);
    const data = await response.json();
    const cloudVersion = data.cliVersion;
    if (cloudVersion === LIB_VERSION) {
      return;
    }
    this.log(chalk2.yellow("================ New version available! =================\n"));
    this.log(`A new CopilotKit CLI version is available (${LIB_VERSION}).
`);
    this.log("Please update your CLI to the latest version:\n\n");
    this.log(`${chalk2.cyan(chalk2.underline(chalk2.bold("npm:")))}	 npm install -g @copilotkit/cli@latest
`);
    this.log(`${chalk2.cyan(chalk2.underline(chalk2.bold("pnpm:")))}	 pnpm install -g @copilotkit/cli@latest
`);
    this.log(`${chalk2.cyan(chalk2.underline(chalk2.bold("yarn:")))}	 yarn global add @copilotkit/cli@latest
`);
    process.exit(0);
  }
};

// src/commands/login.ts
var CloudLogin = class _CloudLogin extends BaseCommand {
  constructor(argv, config, authService = new AuthService()) {
    super(argv, config);
    this.authService = authService;
  }
  static description = "Authenticate with Copilot Cloud";
  static examples = ["<%= config.bin %> login"];
  async run() {
    await this.parse(_CloudLogin);
    try {
      await this.authService.login();
    } catch (error) {
      this.error(error.message);
    }
  }
};
export {
  CloudLogin as default
};
//# sourceMappingURL=login.js.map