export { AssistantMessageProps, ButtonProps, ComponentsMap, HeaderProps, InputProps, MessagesProps, RenderMessageProps, RenderSuggestionsListProps, Renderer, SuggestionsProps, UserMessageProps, WindowProps } from './chat/props.js';
export { CopilotPopup } from './chat/Popup.js';
export { CopilotSidebar } from './chat/Sidebar.js';
export { CopilotChat } from './chat/Chat.js';
export { Markdown } from './chat/Markdown.js';
export { AssistantMessage } from './chat/messages/AssistantMessage.js';
export { UserMessage } from './chat/messages/UserMessage.js';
export { useChatContext } from './chat/ChatContext.js';
export { RenderImageMessage } from './chat/messages/RenderImageMessage.js';
export { Suggestions as RenderSuggestionsList } from './chat/Suggestions.js';
export { Suggestion as RenderSuggestion } from './chat/Suggestion.js';
export { shouldShowDevConsole } from './dev-console/utils.js';
export { CopilotDevConsole } from './dev-console/console.js';
import '@copilotkit/runtime-client-gql';
import '../types/suggestions.js';
import 'react';
import 'react/jsx-runtime';
import './chat/Modal.js';
import '@copilotkit/react-core';
import 'react-markdown';
import './dev-console/types.js';
