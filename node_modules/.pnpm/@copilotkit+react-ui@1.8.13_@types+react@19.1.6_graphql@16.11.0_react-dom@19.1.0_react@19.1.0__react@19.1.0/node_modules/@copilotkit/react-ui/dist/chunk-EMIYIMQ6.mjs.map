{"version": 3, "sources": ["../src/components/chat/Input.tsx"], "sourcesContent": ["import React, { useRef, useState } from \"react\";\nimport { InputProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\nimport AutoResizingTextarea from \"./Textarea\";\nimport { usePushToTalk } from \"../../hooks/use-push-to-talk\";\nimport { useCopilotContext } from \"@copilotkit/react-core\";\nimport { PoweredByTag } from \"./PoweredByTag\";\n\nconst MAX_NEWLINES = 6;\n\nexport const Input = ({ inProgress, onSend, isVisible = false, onStop, onUpload }: InputProps) => {\n  const context = useChatContext();\n  const copilotContext = useCopilotContext();\n\n  const showPoweredBy = !copilotContext.copilotApiConfig?.publicApiKey;\n\n  const pushToTalkConfigured =\n    copilotContext.copilotApiConfig.textToSpeechUrl !== undefined &&\n    copilotContext.copilotApiConfig.transcribeAudioUrl !== undefined;\n\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n  const handleDivClick = (event: React.MouseEvent<HTMLDivElement>) => {\n    const target = event.target as HTMLElement;\n\n    // If the user clicked a button or inside a button, don't focus the textarea\n    if (target.closest(\"button\")) return;\n\n    // If the user clicked the textarea, do nothing (it's already focused)\n    if (target.tagName === \"TEXTAREA\") return;\n\n    // Otherwise, focus the textarea\n    textareaRef.current?.focus();\n  };\n\n  const [text, setText] = useState(\"\");\n  const send = () => {\n    if (inProgress) return;\n    onSend(text);\n    setText(\"\");\n\n    textareaRef.current?.focus();\n  };\n\n  // tylerslaton:\n  //\n  // This scrolls CopilotKit into view always. Reading the commit history, it was likely\n  // added to fix a bug but it is causing issues now.\n  //\n  // For the future, if we want this behavior again, we will need to find a way to do it without\n  // forcing CopilotKit to always be in view. This code causes this because focusing an element\n  // in most browsers will scroll that element into view.\n  //\n  // useEffect(() => {\n  //   if (isVisible) {\n  //     textareaRef.current?.focus();\n  //   }\n  // }, [isVisible]);\n\n  const { pushToTalkState, setPushToTalkState } = usePushToTalk({\n    sendFunction: onSend,\n    inProgress,\n  });\n\n  const isInProgress = inProgress || pushToTalkState === \"transcribing\";\n  const buttonIcon = isInProgress ? context.icons.stopIcon : context.icons.sendIcon;\n  const showPushToTalk =\n    pushToTalkConfigured &&\n    (pushToTalkState === \"idle\" || pushToTalkState === \"recording\") &&\n    !inProgress;\n\n  const canSend = () => {\n    const interruptEvent = copilotContext.langGraphInterruptAction?.event;\n    const interruptInProgress =\n      interruptEvent?.name === \"LangGraphInterruptEvent\" && !interruptEvent?.response;\n\n    return (\n      (isInProgress || (!isInProgress && text.trim().length > 0)) &&\n      pushToTalkState === \"idle\" &&\n      !interruptInProgress\n    );\n  };\n\n  const sendDisabled = !canSend();\n\n  return (\n    <div className={`copilotKitInputContainer ${showPoweredBy ? \"poweredByContainer\" : \"\"}`}>\n      <div className=\"copilotKitInput\" onClick={handleDivClick}>\n        <AutoResizingTextarea\n          ref={textareaRef}\n          placeholder={context.labels.placeholder}\n          autoFocus={false}\n          maxRows={MAX_NEWLINES}\n          value={text}\n          onChange={(event) => setText(event.target.value)}\n          onKeyDown={(event) => {\n            if (event.key === \"Enter\" && !event.shiftKey) {\n              event.preventDefault();\n              if (canSend()) {\n                send();\n              }\n            }\n          }}\n        />\n        <div className=\"copilotKitInputControls\">\n          {onUpload && (\n            <button onClick={onUpload} className=\"copilotKitInputControlButton\">\n              {context.icons.uploadIcon}\n            </button>\n          )}\n\n          <div style={{ flexGrow: 1 }} />\n\n          {showPushToTalk && (\n            <button\n              onClick={() =>\n                setPushToTalkState(pushToTalkState === \"idle\" ? \"recording\" : \"transcribing\")\n              }\n              className={\n                pushToTalkState === \"recording\"\n                  ? \"copilotKitInputControlButton copilotKitPushToTalkRecording\"\n                  : \"copilotKitInputControlButton\"\n              }\n            >\n              {context.icons.pushToTalkIcon}\n            </button>\n          )}\n          <button\n            disabled={sendDisabled}\n            onClick={isInProgress ? onStop : send}\n            data-copilotkit-in-progress={inProgress}\n            data-test-id={inProgress ? \"copilot-chat-request-in-progress\" : \"copilot-chat-ready\"}\n            className=\"copilotKitInputControlButton\"\n          >\n            {buttonIcon}\n          </button>\n        </div>\n      </div>\n      <PoweredByTag showPoweredBy={showPoweredBy} />\n    </div>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAgB,QAAQ,gBAAgB;AAKxC,SAAS,yBAAyB;AAmF1B,cAgBA,YAhBA;AAhFR,IAAM,eAAe;AAEd,IAAM,QAAQ,CAAC,EAAE,YAAY,QAAQ,YAAY,OAAO,QAAQ,SAAS,MAAkB;AAVlG;AAWE,QAAM,UAAU,eAAe;AAC/B,QAAM,iBAAiB,kBAAkB;AAEzC,QAAM,gBAAgB,GAAC,oBAAe,qBAAf,mBAAiC;AAExD,QAAM,uBACJ,eAAe,iBAAiB,oBAAoB,UACpD,eAAe,iBAAiB,uBAAuB;AAEzD,QAAM,cAAc,OAA4B,IAAI;AAEpD,QAAM,iBAAiB,CAAC,UAA4C;AAtBtE,QAAAA;AAuBI,UAAM,SAAS,MAAM;AAGrB,QAAI,OAAO,QAAQ,QAAQ;AAAG;AAG9B,QAAI,OAAO,YAAY;AAAY;AAGnC,KAAAA,MAAA,YAAY,YAAZ,gBAAAA,IAAqB;AAAA,EACvB;AAEA,QAAM,CAAC,MAAM,OAAO,IAAI,SAAS,EAAE;AACnC,QAAM,OAAO,MAAM;AApCrB,QAAAA;AAqCI,QAAI;AAAY;AAChB,WAAO,IAAI;AACX,YAAQ,EAAE;AAEV,KAAAA,MAAA,YAAY,YAAZ,gBAAAA,IAAqB;AAAA,EACvB;AAiBA,QAAM,EAAE,iBAAiB,mBAAmB,IAAI,cAAc;AAAA,IAC5D,cAAc;AAAA,IACd;AAAA,EACF,CAAC;AAED,QAAM,eAAe,cAAc,oBAAoB;AACvD,QAAM,aAAa,eAAe,QAAQ,MAAM,WAAW,QAAQ,MAAM;AACzE,QAAM,iBACJ,yBACC,oBAAoB,UAAU,oBAAoB,gBACnD,CAAC;AAEH,QAAM,UAAU,MAAM;AAvExB,QAAAA;AAwEI,UAAM,kBAAiBA,MAAA,eAAe,6BAAf,gBAAAA,IAAyC;AAChE,UAAM,uBACJ,iDAAgB,UAAS,6BAA6B,EAAC,iDAAgB;AAEzE,YACG,gBAAiB,CAAC,gBAAgB,KAAK,KAAK,EAAE,SAAS,MACxD,oBAAoB,UACpB,CAAC;AAAA,EAEL;AAEA,QAAM,eAAe,CAAC,QAAQ;AAE9B,SACE,qBAAC,SAAI,WAAW,4BAA4B,gBAAgB,uBAAuB,MACjF;AAAA,yBAAC,SAAI,WAAU,mBAAkB,SAAS,gBACxC;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,KAAK;AAAA,UACL,aAAa,QAAQ,OAAO;AAAA,UAC5B,WAAW;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU,CAAC,UAAU,QAAQ,MAAM,OAAO,KAAK;AAAA,UAC/C,WAAW,CAAC,UAAU;AACpB,gBAAI,MAAM,QAAQ,WAAW,CAAC,MAAM,UAAU;AAC5C,oBAAM,eAAe;AACrB,kBAAI,QAAQ,GAAG;AACb,qBAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,MACA,qBAAC,SAAI,WAAU,2BACZ;AAAA,oBACC,oBAAC,YAAO,SAAS,UAAU,WAAU,gCAClC,kBAAQ,MAAM,YACjB;AAAA,QAGF,oBAAC,SAAI,OAAO,EAAE,UAAU,EAAE,GAAG;AAAA,QAE5B,kBACC;AAAA,UAAC;AAAA;AAAA,YACC,SAAS,MACP,mBAAmB,oBAAoB,SAAS,cAAc,cAAc;AAAA,YAE9E,WACE,oBAAoB,cAChB,+DACA;AAAA,YAGL,kBAAQ,MAAM;AAAA;AAAA,QACjB;AAAA,QAEF;AAAA,UAAC;AAAA;AAAA,YACC,UAAU;AAAA,YACV,SAAS,eAAe,SAAS;AAAA,YACjC,+BAA6B;AAAA,YAC7B,gBAAc,aAAa,qCAAqC;AAAA,YAChE,WAAU;AAAA,YAET;AAAA;AAAA,QACH;AAAA,SACF;AAAA,OACF;AAAA,IACA,oBAAC,gBAAa,eAA8B;AAAA,KAC9C;AAEJ;", "names": ["_a"]}