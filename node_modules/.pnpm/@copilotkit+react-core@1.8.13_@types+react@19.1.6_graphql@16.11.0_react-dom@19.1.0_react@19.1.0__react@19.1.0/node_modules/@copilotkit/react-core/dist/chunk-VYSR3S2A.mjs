import {
  useCopilotContext
} from "./chunk-7WG4MNRX.mjs";
import {
  CopilotMessagesContext
} from "./chunk-DCTJZ742.mjs";
import {
  __async
} from "./chunk-SKC7AJIV.mjs";

// src/components/copilot-provider/copilot-messages.tsx
import { useEffect, useState, useRef } from "react";
import { loadMessagesFromJsonRepresentation } from "@copilotkit/runtime-client-gql";
import { jsx } from "react/jsx-runtime";
function CopilotMessages({ children }) {
  const [messages, setMessages] = useState([]);
  const lastLoadedThreadId = useRef();
  const lastLoadedAgentName = useRef();
  const lastLoadedMessages = useRef();
  const { threadId, agentSession, runtimeClient } = useCopilotContext();
  useEffect(() => {
    if (!threadId || threadId === lastLoadedThreadId.current)
      return;
    if (threadId === lastLoadedThreadId.current && (agentSession == null ? void 0 : agentSession.agentName) === lastLoadedAgentName.current) {
      return;
    }
    const fetchMessages = () => __async(this, null, function* () {
      var _a, _b, _c, _d;
      if (!(agentSession == null ? void 0 : agentSession.agentName))
        return;
      const result = yield runtimeClient.loadAgentState({
        threadId,
        agentName: agentSession == null ? void 0 : agentSession.agentName
      });
      const newMessages = (_b = (_a = result.data) == null ? void 0 : _a.loadAgentState) == null ? void 0 : _b.messages;
      if (newMessages === lastLoadedMessages.current)
        return;
      if ((_d = (_c = result.data) == null ? void 0 : _c.loadAgentState) == null ? void 0 : _d.threadExists) {
        lastLoadedMessages.current = newMessages;
        lastLoadedThreadId.current = threadId;
        lastLoadedAgentName.current = agentSession == null ? void 0 : agentSession.agentName;
        const messages2 = loadMessagesFromJsonRepresentation(JSON.parse(newMessages || "[]"));
        setMessages(messages2);
      }
    });
    void fetchMessages();
  }, [threadId, agentSession == null ? void 0 : agentSession.agentName]);
  return /* @__PURE__ */ jsx(
    CopilotMessagesContext.Provider,
    {
      value: {
        messages,
        setMessages
      },
      children
    }
  );
}

export {
  CopilotMessages
};
//# sourceMappingURL=chunk-VYSR3S2A.mjs.map