// src/commands/dev.ts
import { Flags } from "@oclif/core";
import inquirer2 from "inquirer";
import { createId } from "@paralleldrive/cuid2";
import ora2 from "ora";
import chalk3 from "chalk";

// src/services/auth.service.ts
import Conf2 from "conf";
import cors from "cors";
import express from "express";
import crypto2 from "node:crypto";
import open from "open";
import getPort from "get-port";
import ora from "ora";
import chalk from "chalk";
import inquirer from "inquirer";

// src/utils/trpc.ts
import { createTRPCClient as createTRPClient_, unstable_httpBatchStreamLink } from "@trpc/client";
import superjson from "superjson";
var COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || "https://cloud.copilotkit.ai";
function createTRPCClient(cliToken) {
  return createTRPClient_({
    links: [
      unstable_httpBatchStreamLink({
        transformer: superjson,
        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,
        headers: () => {
          const headers = new Headers();
          headers.set("x-trpc-source", "cli");
          headers.set("x-cli-token", cliToken);
          return headers;
        }
      })
    ]
  });
}

// src/services/analytics.service.ts
import { Analytics } from "@segment/analytics-node";
import Conf from "conf";
var AnalyticsService = class {
  constructor(authData) {
    this.authData = authData;
    if (process.env.SEGMENT_DISABLED === "true") {
      return;
    }
    const segmentWriteKey = process.env.SEGMENT_WRITE_KEY || "9Pv6QyExYef2P4hPz4gks6QAvNMi2AOf";
    this.globalProperties = {
      service: "cli"
    };
    if (this.authData?.userId) {
      this.userId = this.authData.userId;
    }
    if (this.authData?.email) {
      this.email = this.authData.email;
      this.globalProperties.email = this.authData.email;
    }
    if (this.authData?.organizationId) {
      this.organizationId = this.authData.organizationId;
    }
    this.segment = new Analytics({
      writeKey: segmentWriteKey,
      disable: process.env.SEGMENT_DISABLE === "true"
    });
    const config = new Conf({ projectName: "CopilotKitCLI" });
    if (!config.get("anonymousId")) {
      config.set("anonymousId", crypto.randomUUID());
    }
  }
  segment;
  globalProperties = {};
  userId;
  email;
  organizationId;
  config = new Conf({ projectName: "CopilotKitCLI" });
  getAnonymousId() {
    const anonymousId = this.config.get("anonymousId");
    if (!anonymousId) {
      const anonymousId2 = crypto.randomUUID();
      this.config.set("anonymousId", anonymousId2);
      return anonymousId2;
    }
    return anonymousId;
  }
  track(event) {
    if (!this.segment) {
      return;
    }
    const payload = {
      userId: this.userId ? this.userId : void 0,
      email: this.email ? this.email : void 0,
      anonymousId: this.getAnonymousId(),
      event: event.event,
      properties: {
        ...this.globalProperties,
        ...event.properties,
        $groups: this.organizationId ? {
          segment_group: this.organizationId
        } : void 0,
        eventProperties: {
          ...event.properties,
          ...this.globalProperties
        }
      }
    };
    this.segment.track(payload);
  }
};

// src/services/auth.service.ts
var AuthService = class {
  config = new Conf2({ projectName: "CopilotKitCLI" });
  COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || "https://cloud.copilotkit.ai";
  getToken() {
    return this.config.get("cliToken");
  }
  getCLIToken() {
    const cliToken = this.config.get("cliToken");
    return cliToken;
  }
  async logout() {
    const cliToken = this.getCLIToken();
    if (!cliToken) {
      throw new Error("You are not logged in");
    }
    const trpcClient = createTRPCClient(cliToken);
    const me = await trpcClient.me.query();
    const analytics = new AnalyticsService({ userId: me.user.id, organizationId: me.organization.id, email: me.user.email });
    this.config.delete("cliToken");
    analytics.track({
      event: "cli.logout",
      properties: {
        organizationId: me.organization.id,
        userId: me.user.id,
        email: me.user.email
      }
    });
  }
  async requireLogin(cmd) {
    let cliToken = this.getCLIToken();
    if (!cliToken) {
      try {
        const { shouldLogin } = await inquirer.prompt([
          {
            name: "shouldLogin",
            type: "confirm",
            message: "You are not yet authenticated. Authenticate with Copilot Cloud? (press Enter to confirm)",
            default: true
          }
        ]);
        if (shouldLogin) {
          const loginResult = await this.login();
          cliToken = loginResult.cliToken;
          cmd.log(`\u{1FA81} Logged in as ${chalk.hex("#7553fc")(loginResult.user.email)}
`);
          return loginResult;
        } else {
          cmd.error("Authentication required to proceed.");
        }
      } catch (error) {
        if (error instanceof Error && error.name === "ExitPromptError") {
          cmd.error(chalk.yellow("\nAuthentication cancelled"));
        }
        throw error;
      }
    }
    let me;
    const trpcClient = createTRPCClient(cliToken);
    try {
      me = await trpcClient.me.query();
    } catch (error) {
      cmd.log(chalk.red("Could not authenticate with Copilot Cloud. Please try again."));
      process.exit(1);
    }
    if (!me.organization || !me.user) {
      cmd.error("Authentication required to proceed.");
    }
    return { cliToken, user: me.user, organization: me.organization };
  }
  async login() {
    let analytics;
    analytics = new AnalyticsService();
    const app = express();
    app.use(cors());
    app.use(express.urlencoded({ extended: true }));
    app.use(express.json());
    const port = await getPort();
    const state = crypto2.randomBytes(16).toString("hex");
    return new Promise((resolve) => {
      const server = app.listen(port, () => {
      });
      analytics.track({
        event: "cli.login.initiated",
        properties: {}
      });
      const spinner = ora("Waiting for browser authentication to complete...\n").start();
      app.post("/callback", async (req, res) => {
        const { cliToken, user, organization } = req.body;
        analytics = new AnalyticsService({ userId: user.id, organizationId: organization.id, email: user.email });
        analytics.track({
          event: "cli.login.success",
          properties: {
            organizationId: organization.id,
            userId: user.id,
            email: user.email
          }
        });
        if (state !== req.query.state) {
          res.status(401).json({ message: "Invalid state" });
          spinner.fail("Invalid state");
          return;
        }
        this.config.set("cliToken", cliToken);
        res.status(200).json({ message: "Callback called" });
        spinner.succeed(`\u{1FA81} Successfully logged in as ${chalk.hex("#7553fc")(user.email)}
`);
        server.close();
        resolve({
          cliToken,
          organization,
          user
        });
      });
      open(`${this.COPILOT_CLOUD_BASE_URL}/cli-auth?callbackUrl=http://localhost:${port}/callback&state=${state}`);
    });
  }
};

// src/utils/detect-endpoint-type.utils.ts
var removeTrailingSlash = (url) => url.replace(/\/$/, "");
var getHumanReadableEndpointType = (type) => {
  switch (type) {
    case "LangGraphPlatform" /* LangGraphPlatform */:
      return "LangGraph Platform";
    case "CopilotKit" /* CopilotKit */:
      return "CopilotKit";
    default:
      return "Invalid";
  }
};
async function detectRemoteEndpointType(url) {
  const promises = [
    isLangGraphPlatformEndpoint(url),
    isCopilotKitEndpoint(url)
  ];
  if (!url.endsWith("/copilotkit")) {
    promises.push(isCopilotKitEndpoint(`${removeTrailingSlash(url)}/copilotkit`));
  }
  const results = await Promise.all(promises);
  if (results[0]) {
    return {
      url,
      type: "LangGraphPlatform" /* LangGraphPlatform */,
      humanReadableType: "LangGraph Platform"
    };
  }
  if (results[1]) {
    return {
      url,
      type: "CopilotKit" /* CopilotKit */,
      humanReadableType: "CopilotKit"
    };
  }
  if (results[2]) {
    return {
      url: `${removeTrailingSlash(url)}/copilotkit`,
      type: "CopilotKit" /* CopilotKit */,
      humanReadableType: "CopilotKit"
    };
  }
  return {
    url,
    type: "Invalid" /* Invalid */,
    humanReadableType: "Invalid"
  };
}
async function isLangGraphPlatformEndpoint(url, retries = 0) {
  let response;
  try {
    response = await fetch(`${url}/assistants/search`, {
      method: "POST",
      body: JSON.stringify({
        metadata: {},
        limit: 99,
        offset: 0
      })
    });
  } catch (error) {
    return false;
  }
  if (!response.ok) {
    if (response.status === 502) {
      if (retries < 3) {
        console.log("RETRYING LGC", retries + 1);
        return isLangGraphPlatformEndpoint(url, retries + 1);
      }
    }
    if (response.status === 403) {
      return true;
    }
    return false;
  }
  const data = await response.json();
  if (data[0].assistant_id) {
    return true;
  }
  return false;
}
async function isCopilotKitEndpoint(url, retries = 0) {
  let response;
  try {
    response = await fetch(`${url}/info`, {
      method: "POST",
      body: JSON.stringify({})
    });
  } catch (error) {
    return false;
  }
  if (!response.ok) {
    if (response.status === 502) {
      if (retries < 3) {
        console.log("RETRYING CK", retries + 1);
        return isCopilotKitEndpoint(url, retries + 1);
      }
    }
    return false;
  }
  const data = await response.json();
  if (data.agents && data.actions) {
    return true;
  }
  return false;
}

// src/services/tunnel.service.ts
import axios from "axios";
import localtunnel from "localtunnel";
var TunnelService = class {
  META_DATA_URL = "https://metadata-cdn.copilotkit.ai/cloud.config.json";
  async create(options) {
    return localtunnel(options);
  }
  async getMetaData() {
    const response = await axios.get(this.META_DATA_URL);
    return response.data;
  }
};

// src/commands/base-command.ts
import { Command } from "@oclif/core";
import Sentry, { consoleIntegration } from "@sentry/node";

// src/utils/version.ts
var LIB_VERSION = "0.0.6";

// src/commands/base-command.ts
import chalk2 from "chalk";
var BaseCommand = class extends Command {
  async init() {
    await this.checkCLIVersion();
    if (process.env.SENTRY_DISABLED === "true") {
      return;
    }
    Sentry.init({
      dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4508581448581120",
      integrations: [
        consoleIntegration()
      ],
      // Tracing
      tracesSampleRate: 1
      //  Capture 100% of the transactions
    });
  }
  async catch(err) {
    if (process.env.SENTRY_DISABLED === "true") {
      super.catch(err);
      return;
    }
    Sentry.captureException(err);
    super.catch(err);
  }
  async finally() {
    if (process.env.SENTRY_DISABLED === "true") {
      return;
    }
    Sentry.close();
  }
  async run() {
  }
  async checkCLIVersion() {
    const response = await fetch(`${COPILOT_CLOUD_BASE_URL}/api/healthz`);
    const data = await response.json();
    const cloudVersion = data.cliVersion;
    if (cloudVersion === LIB_VERSION) {
      return;
    }
    this.log(chalk2.yellow("================ New version available! =================\n"));
    this.log(`A new CopilotKit CLI version is available (${LIB_VERSION}).
`);
    this.log("Please update your CLI to the latest version:\n\n");
    this.log(`${chalk2.cyan(chalk2.underline(chalk2.bold("npm:")))}	 npm install -g @copilotkit/cli@latest
`);
    this.log(`${chalk2.cyan(chalk2.underline(chalk2.bold("pnpm:")))}	 pnpm install -g @copilotkit/cli@latest
`);
    this.log(`${chalk2.cyan(chalk2.underline(chalk2.bold("yarn:")))}	 yarn global add @copilotkit/cli@latest
`);
    process.exit(0);
  }
};

// src/commands/dev.ts
var Dev = class _Dev extends BaseCommand {
  constructor(argv, config, authService = new AuthService(), tunnelService = new TunnelService()) {
    super(argv, config);
    this.authService = authService;
    this.tunnelService = tunnelService;
  }
  static flags = {
    port: Flags.string({ description: "port", required: true }),
    project: Flags.string({ description: "project" })
  };
  static description = "describe the command here";
  static examples = ["<%= config.bin %> <%= command.id %>"];
  trpcClient = null;
  copilotCloudTunnelId = null;
  async pingTunnelRecursively() {
    if (!this.copilotCloudTunnelId) {
      return;
    }
    try {
      await this.trpcClient.pingLocalTunnel.query({
        localTunnelId: this.copilotCloudTunnelId
      });
    } catch (error) {
      if (error?.data?.code === "NOT_FOUND") {
        this.error(error.message);
      } else {
        this.error("Failed to ping tunnel. The connection may have been lost.");
      }
    }
    await new Promise((resolve) => setTimeout(resolve, 5e3));
    await this.pingTunnelRecursively();
  }
  async run() {
    const { flags } = await this.parse(_Dev);
    const { cliToken, organization, user } = await this.authService.requireLogin(this);
    const analytics = new AnalyticsService({ userId: user.id, organizationId: organization.id, email: user.email });
    this.trpcClient = createTRPCClient(cliToken);
    const availableProjects = await this.trpcClient.listOrgProjects.query({ orgId: organization.id });
    let selectedProjectId = null;
    if (flags.project) {
      if (!availableProjects.some((project) => project.id === flags.project)) {
        this.log(chalk3.red(`Project with ID ${flags.project} not found`));
        process.exit(1);
      }
      selectedProjectId = flags.project;
      this.log(chalk3.green(`\u2705 Selected project ${selectedProjectId}`));
    } else {
      const { projectId } = await inquirer2.prompt([
        {
          name: "projectId",
          type: "list",
          message: "Select a project",
          choices: availableProjects.map((project) => ({
            value: project.id,
            name: `${project.name} (ID: ${project.id})${availableProjects.length === 1 ? " (press Enter to confirm)" : ""}`
          }))
        }
      ]);
      selectedProjectId = projectId;
    }
    const { type: remoteEndpointType } = await detectRemoteEndpointType(`http://localhost:${flags.port}`);
    if (remoteEndpointType === "Invalid" /* Invalid */) {
      this.error(
        `Invalid remote endpoint. Please ensure you are running a compatible endpoint at port ${flags.port} and try again.`
      );
    }
    const humanReadableRemoteEndpointType = getHumanReadableEndpointType(remoteEndpointType);
    analytics.track({
      event: "cli.dev.initiatied",
      properties: {
        port: flags.port,
        projectId: selectedProjectId,
        endpointType: remoteEndpointType
      }
    });
    this.log(chalk3.green(`\u2705 ${humanReadableRemoteEndpointType} endpoint detected`));
    const spinner = ora2("Creating tunnel...\n").start();
    const tunnelId = createId();
    const setupTunnel = this.setupTunnel({
      tunnelId,
      port: parseInt(flags.port),
      subdomain: createId(),
      onSuccess: async ({ url, id }) => {
        this.log("\nTunnel Information:\n");
        this.log(`${chalk3.bold.cyan("\u2022 Tunnel URL:		")} ${chalk3.white(url)}`);
        this.log(`${chalk3.bold.cyan("\u2022 Endpoint Type:	")} ${chalk3.white(humanReadableRemoteEndpointType)}`);
        this.log(
          `${chalk3.bold.cyan("\u2022 Project:		")} ${chalk3.white(`${process.env.COPILOT_CLOUD_BASE_URL}/projects/${selectedProjectId}`)}`
        );
        this.log(chalk3.yellow("\nPress Ctrl+C to stop the tunnel"));
        this.log("\n");
        spinner.text = "Linking local tunnel to Copilot Cloud...";
        const { localTunnelId } = await this.trpcClient.reportRemoteEndpointLocalTunnel.mutate({
          tunnelId: id,
          projectId: selectedProjectId,
          endpointType: remoteEndpointType === "CopilotKit" /* CopilotKit */ ? "CopilotKit" : "LangGraphCloud",
          tunnelUrl: url,
          port: parseInt(flags.port)
        });
        this.copilotCloudTunnelId = localTunnelId;
        analytics.track({
          event: "cli.dev.tunnel.created",
          properties: {
            tunnelId: localTunnelId,
            port: flags.port,
            projectId: selectedProjectId,
            endpointType: remoteEndpointType
          }
        });
        spinner.color = "green";
        spinner.text = "\u{1F680} Local tunnel is live and linked to Copilot Cloud!\n";
        spinner.succeed();
        await this.pingTunnelRecursively();
      },
      onTunnelClose: async ({ id }) => {
        if (this.copilotCloudTunnelId) {
          analytics.track({
            event: "cli.dev.tunnel.closed",
            properties: {
              tunnelId: id
            }
          });
          await this.trpcClient.deleteLocalTunnel.mutate({
            localTunnelId: this.copilotCloudTunnelId
          });
          this.copilotCloudTunnelId = null;
        }
      },
      spinner
    });
    await Promise.all([setupTunnel]);
  }
  async setupTunnel({
    port,
    subdomain,
    onSuccess,
    onTunnelClose,
    spinner,
    tunnelId
  }) {
    const tunnel = await this.tunnelService.create({
      port,
      subdomain: tunnelId
    });
    tunnel.on("request", (info) => {
      this.log(`${chalk3.green("\u279C")} ${chalk3.white((/* @__PURE__ */ new Date()).toISOString())} - ${info.method} ${info.path}`);
    });
    tunnel.on("error", (err) => {
      this.error(chalk3.red(`Tunnel error: ${err.message}`));
    });
    tunnel.on("close", async () => {
      this.log(chalk3.yellow("\nTunnel closed"));
      await onTunnelClose({ id: tunnelId });
      process.exit(0);
    });
    await Promise.all([
      new Promise(() => {
        process.on("SIGINT", async () => {
          this.log("\nShutting down tunnel...");
          await onTunnelClose({ id: tunnelId });
          tunnel.close();
          process.exit(0);
        });
        process.on("SIGTERM", async () => {
          this.log("\nShutting down tunnel...");
          await onTunnelClose({ id: tunnelId });
          tunnel.close();
          process.exit(0);
        });
      }),
      onSuccess({ url: tunnel.url, id: tunnelId })
    ]);
  }
};
export {
  Dev as default
};
//# sourceMappingURL=dev.js.map