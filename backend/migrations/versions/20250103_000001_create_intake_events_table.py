"""Create intake_events table

Revision ID: 20250103_000001
Revises: 20240101_000000
Create Date: 2025-01-03 00:00:01.000000

"""
from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '20250103_000001'
down_revision: Union[str, None] = '20240101_000000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create intake_events table in tenants schema
    op.create_table('intake_events',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column('payload', postgresql.JSONB(), nullable=False),
        sa.Column('received_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('processed', sa.Boolean(), nullable=False, server_default='false'),
        schema='tenants'
    )

    # Create indexes for better query performance
    op.create_index('ix_tenants_intake_events_tenant_id', 'intake_events', ['tenant_id'], schema='tenants')
    op.create_index('ix_tenants_intake_events_received_at', 'intake_events', ['received_at'], schema='tenants')
    op.create_index('ix_tenants_intake_events_processed', 'intake_events', ['processed'], schema='tenants')


def downgrade() -> None:
    # Drop the intake_events table
    op.drop_table('intake_events', schema='tenants')
