{"name": "@envelop/types", "version": "5.2.1", "sideEffects": false, "dependencies": {"@whatwg-node/promise-helpers": "^1.0.0", "tslib": "^2.5.0"}, "repository": {"type": "git", "url": "https://github.com/n1ru4l/envelop.git", "directory": "packages/types"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=18.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./typings/*.d.cts", "default": "./cjs/*.js"}, "import": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}, "default": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}}, "./package.json": "./package.json"}}