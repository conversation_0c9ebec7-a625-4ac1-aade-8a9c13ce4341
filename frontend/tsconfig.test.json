{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "ignoreDeprecations": "5.0", "types": ["jest", "@testing-library/jest-dom", "node"], "typeRoots": ["./node_modules/@types", "./src/types"], "paths": {"@/*": ["./src/*"]}}, "include": ["__tests__/**/*.ts", "__tests__/**/*.tsx", "vitest.setup.ts", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", ".next", "cypress"]}