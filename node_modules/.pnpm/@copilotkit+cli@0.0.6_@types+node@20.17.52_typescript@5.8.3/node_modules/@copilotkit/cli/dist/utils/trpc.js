// src/utils/trpc.ts
import { createTRPC<PERSON>lient as createTRPClient_, unstable_httpBatchStreamLink } from "@trpc/client";
import superjson from "superjson";
var COPILOT_CLOUD_BASE_URL = process.env.COPILOT_CLOUD_BASE_URL || "https://cloud.copilotkit.ai";
function createTRPCClient(cliToken) {
  return createTRPClient_({
    links: [
      unstable_httpBatchStreamLink({
        transformer: superjson,
        url: `${COPILOT_CLOUD_BASE_URL}/api/trpc-cli`,
        headers: () => {
          const headers = new Headers();
          headers.set("x-trpc-source", "cli");
          headers.set("x-cli-token", cliToken);
          return headers;
        }
      })
    ]
  });
}
export {
  COPILOT_CLOUD_BASE_URL,
  createTRPCClient
};
//# sourceMappingURL=trpc.js.map