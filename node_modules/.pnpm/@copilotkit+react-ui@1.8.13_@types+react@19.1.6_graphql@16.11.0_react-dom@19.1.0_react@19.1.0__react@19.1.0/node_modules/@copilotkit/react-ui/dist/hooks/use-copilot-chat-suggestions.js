"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/hooks/use-copilot-chat-suggestions.tsx
var use_copilot_chat_suggestions_exports = {};
__export(use_copilot_chat_suggestions_exports, {
  useCopilotChatSuggestions: () => useCopilotChatSuggestions
});
module.exports = __toCommonJS(use_copilot_chat_suggestions_exports);
var import_react = require("react");
var import_react_core = require("@copilotkit/react-core");
var import_shared = require("@copilotkit/shared");
function useCopilotChatSuggestions({
  available = "enabled",
  instructions,
  className,
  minSuggestions = 1,
  maxSuggestions = 3
}, dependencies = []) {
  const context = (0, import_react_core.useCopilotContext)();
  (0, import_react.useEffect)(() => {
    if (available === "disabled")
      return;
    const id = (0, import_shared.randomId)();
    context.addChatSuggestionConfiguration(id, {
      instructions,
      minSuggestions,
      maxSuggestions,
      className
    });
    return () => {
      context.removeChatSuggestionConfiguration(id);
    };
  }, [...dependencies, instructions, minSuggestions, maxSuggestions, className, available]);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  useCopilotChatSuggestions
});
//# sourceMappingURL=use-copilot-chat-suggestions.js.map