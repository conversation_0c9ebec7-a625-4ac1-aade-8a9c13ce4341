// src/services/tunnel.service.ts
import axios from "axios";
import localtunnel from "localtunnel";
var TunnelService = class {
  META_DATA_URL = "https://metadata-cdn.copilotkit.ai/cloud.config.json";
  async create(options) {
    return localtunnel(options);
  }
  async getMetaData() {
    const response = await axios.get(this.META_DATA_URL);
    return response.data;
  }
};
export {
  TunnelService
};
//# sourceMappingURL=tunnel.service.js.map