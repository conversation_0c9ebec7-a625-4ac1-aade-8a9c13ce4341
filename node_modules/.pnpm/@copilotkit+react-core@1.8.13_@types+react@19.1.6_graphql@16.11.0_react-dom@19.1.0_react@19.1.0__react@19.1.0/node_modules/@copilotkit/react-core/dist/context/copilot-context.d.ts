import '@copilotkit/shared';
import '../types/frontend-action.js';
import 'react';
import '../hooks/use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';
import '@copilotkit/runtime-client-gql';
export { k as ActionName, A as AgentSession, f as AuthState, j as ChatComponentsCache, b as CoagentInChatRenderFunction, c as CopilotApiConfig, C as CopilotContext, a as CopilotContextParams, I as InChatRenderFunction, u as useCopilotContext } from '../copilot-context-8fb74a85.js';
