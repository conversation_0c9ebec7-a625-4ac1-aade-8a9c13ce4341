"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JWTRequestBaseAuthenticator = exports.RequestFunctionJWTTokenManager = void 0;
/**
 * (C) Copyright IBM Corp. 2025.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/* eslint-disable max-classes-per-file */
const ibm_cloud_sdk_core_1 = require("ibm-cloud-sdk-core");
class RequestFunctionJWTTokenManager extends ibm_cloud_sdk_core_1.JwtTokenManager {
    constructor(options, requestToken) {
        super(options);
        super.requestToken = requestToken;
    }
}
exports.RequestFunctionJWTTokenManager = RequestFunctionJWTTokenManager;
class JWTRequestBaseAuthenticator extends ibm_cloud_sdk_core_1.TokenRequestBasedAuthenticator {
    constructor(options, requestToken) {
        super(options);
        this.tokenManager = new RequestFunctionJWTTokenManager(options, requestToken);
    }
}
exports.JWTRequestBaseAuthenticator = JWTRequestBaseAuthenticator;
JWTRequestBaseAuthenticator.AUTHTYPE_ZEN = 'zen';
//# sourceMappingURL=authenticators.js.map