// src/components/chat/ImageUploadQueue.tsx
import { jsx, jsxs } from "react/jsx-runtime";
var ImageUploadQueue = ({
  images,
  onRemoveImage,
  className = ""
}) => {
  if (images.length === 0)
    return null;
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: `copilotKitImageUploadQueue ${className}`,
      style: {
        display: "flex",
        flexWrap: "wrap",
        gap: "8px",
        margin: "8px",
        padding: "8px"
      },
      children: images.map((image, index) => /* @__PURE__ */ jsxs(
        "div",
        {
          className: "copilotKitImageUploadQueueItem",
          style: {
            position: "relative",
            display: "inline-block",
            width: "60px",
            height: "60px",
            borderRadius: "4px",
            overflow: "hidden"
          },
          children: [
            /* @__PURE__ */ jsx(
              "img",
              {
                src: `data:${image.contentType};base64,${image.bytes}`,
                alt: `Selected image ${index + 1}`,
                style: {
                  width: "100%",
                  height: "100%",
                  objectFit: "cover"
                }
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                onClick: () => onRemoveImage(index),
                className: "copilotKitImageUploadQueueRemoveButton",
                style: {
                  position: "absolute",
                  top: "2px",
                  right: "2px",
                  background: "rgba(0,0,0,0.6)",
                  color: "white",
                  border: "none",
                  borderRadius: "50%",
                  width: "18px",
                  height: "18px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  cursor: "pointer",
                  fontSize: "10px",
                  padding: 0
                },
                children: "\u2715"
              }
            )
          ]
        },
        index
      ))
    }
  );
};

export {
  ImageUploadQueue
};
//# sourceMappingURL=chunk-PLHTVHUW.mjs.map