{"version": 3, "sources": ["../package.json", "../src/lib/integrations/node-http/index.ts", "../src/lib/integrations/shared.ts", "../src/graphql/resolvers/copilot.resolver.ts", "../src/graphql/inputs/generate-copilot-response.input.ts", "../src/graphql/inputs/message.input.ts", "../src/graphql/types/enums.ts", "../src/graphql/inputs/frontend.input.ts", "../src/graphql/inputs/action.input.ts", "../src/graphql/inputs/cloud.input.ts", "../src/graphql/inputs/cloud-guardrails.input.ts", "../src/graphql/inputs/forwarded-parameters.input.ts", "../src/graphql/inputs/agent-session.input.ts", "../src/graphql/inputs/agent-state.input.ts", "../src/graphql/inputs/extensions.input.ts", "../src/graphql/inputs/meta-event.input.ts", "../src/graphql/types/meta-events.type.ts", "../src/graphql/types/copilot-response.type.ts", "../src/graphql/types/message-status.type.ts", "../src/graphql/types/extensions-response.type.ts", "../src/service-adapters/events.ts", "../src/lib/telemetry-client.ts", "../src/lib/runtime/remote-actions.ts", "../src/lib/runtime/remote-action-constructors.ts", "../src/agents/langgraph/event-source.ts", "../src/agents/langgraph/events.ts", "../src/lib/runtime/remote-lg-action.ts", "../src/lib/streaming.ts", "../src/lib/runtime/agentwire-action.ts", "../src/lib/runtime/copilot-runtime.ts", "../src/service-adapters/conversion.ts", "../src/lib/runtime/mcp-tools-utils.ts", "../src/graphql/types/agents-response.type.ts", "../src/lib/logger.ts", "../src/graphql/resolvers/state.resolver.ts", "../src/graphql/types/load-agent-state-response.type.ts", "../src/graphql/inputs/load-agent-state.input.ts"], "sourcesContent": ["{\n  \"name\": \"@copilotkit/runtime\",\n  \"private\": false,\n  \"homepage\": \"https://github.com/CopilotKit/CopilotKit\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/CopilotKit/CopilotKit.git\"\n  },\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"version\": \"1.8.13\",\n  \"sideEffects\": false,\n  \"main\": \"./dist/index.js\",\n  \"module\": \"./dist/index.mjs\",\n  \"exports\": {\n    \".\": \"./dist/index.js\"\n  },\n  \"types\": \"./dist/index.d.ts\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"build\": \"tsup --onSuccess \\\"pnpm run generate-graphql-schema\\\"\",\n    \"dev\": \"tsup --watch --onSuccess \\\"pnpm run generate-graphql-schema\\\"\",\n    \"test\": \"jest --passWithNoTests\",\n    \"check-types\": \"tsc --noEmit\",\n    \"clean\": \"rm -rf .turbo && rm -rf node_modules && rm -rf dist && rm -rf .next && rm -rf __snapshots__\",\n    \"generate-graphql-schema\": \"rm -rf __snapshots__ && ts-node ./scripts/generate-gql-schema.ts\",\n    \"link:global\": \"pnpm link --global\",\n    \"unlink:global\": \"pnpm unlink --global\"\n  },\n  \"devDependencies\": {\n    \"@jest/globals\": \"^29.7.0\",\n    \"@swc/core\": \"1.5.28\",\n    \"@types/express\": \"^4.17.21\",\n    \"@types/jest\": \"^29.5.12\",\n    \"@types/node\": \"^18.11.17\",\n    \"@whatwg-node/server\": \"^0.9.34\",\n    \"eslint\": \"^8.56.0\",\n    \"eslint-config-custom\": \"workspace:*\",\n    \"jest\": \"^29.6.4\",\n    \"nodemon\": \"^3.1.3\",\n    \"ts-jest\": \"^29.1.1\",\n    \"ts-node\": \"^10.9.2\",\n    \"tsconfig\": \"workspace:*\",\n    \"tsup\": \"^6.7.0\",\n    \"typescript\": \"^5.2.3\",\n    \"zod-to-json-schema\": \"^3.23.5\"\n  },\n  \"dependencies\": {\n    \"@ag-ui/client\": \"0.0.28\",\n    \"@ag-ui/core\": \"0.0.28\",\n    \"@ag-ui/encoder\": \"0.0.28\",\n    \"@ag-ui/proto\": \"0.0.28\",\n    \"@anthropic-ai/sdk\": \"^0.27.3\",\n    \"@copilotkit/shared\": \"workspace:*\",\n    \"@graphql-yoga/plugin-defer-stream\": \"^3.3.1\",\n    \"@langchain/community\": \"^0.3.29\",\n    \"@langchain/core\": \"^0.3.38\",\n    \"@langchain/google-gauth\": \"^0.1.0\",\n    \"@langchain/langgraph-sdk\": \"^0.0.70\",\n    \"@langchain/openai\": \"^0.4.2\",\n    \"class-transformer\": \"^0.5.1\",\n    \"class-validator\": \"^0.14.1\",\n    \"express\": \"^4.19.2\",\n    \"graphql\": \"^16.8.1\",\n    \"graphql-scalars\": \"^1.23.0\",\n    \"graphql-yoga\": \"^5.3.1\",\n    \"groq-sdk\": \"^0.5.0\",\n    \"langchain\": \"^0.3.3\",\n    \"openai\": \"^4.85.1\",\n    \"partial-json\": \"^0.1.7\",\n    \"pino\": \"^9.2.0\",\n    \"pino-pretty\": \"^11.2.1\",\n    \"reflect-metadata\": \"^0.2.2\",\n    \"rxjs\": \"^7.8.1\",\n    \"type-graphql\": \"2.0.0-rc.1\",\n    \"zod\": \"^3.23.3\"\n  },\n  \"peerDependencies\": {\n    \"@ag-ui/client\": \">=0.0.28\",\n    \"@ag-ui/core\": \">=0.0.28\",\n    \"@ag-ui/encoder\": \">=0.0.28\",\n    \"@ag-ui/proto\": \">=0.0.28\"\n  },\n  \"keywords\": [\n    \"copilotkit\",\n    \"copilot\",\n    \"react\",\n    \"nextjs\",\n    \"nodejs\",\n    \"ai\",\n    \"assistant\",\n    \"javascript\",\n    \"automation\",\n    \"textarea\"\n  ]\n}\n", "import { createYoga } from \"graphql-yoga\";\nimport { CreateCopilotRuntimeServerOptions, getCommonConfig } from \"../shared\";\nimport telemetry, { getRuntimeInstanceTelemetryInfo } from \"../../telemetry-client\";\n\nexport function copilotRuntimeNodeHttpEndpoint(options: CreateCopilotRuntimeServerOptions) {\n  const commonConfig = getCommonConfig(options);\n\n  telemetry.setGlobalProperties({\n    runtime: {\n      framework: \"node-http\",\n    },\n  });\n\n  if (options.properties?._copilotkit) {\n    telemetry.setGlobalProperties({\n      _copilotkit: options.properties._copilotkit,\n    });\n  }\n\n  telemetry.capture(\n    \"oss.runtime.instance_created\",\n    getRuntimeInstanceTelemetryInfo(options.runtime),\n  );\n\n  const logger = commonConfig.logging;\n  logger.debug(\"Creating Node HTTP endpoint\");\n\n  const yoga = createYoga({\n    ...commonConfig,\n    graphqlEndpoint: options.endpoint,\n  });\n\n  return yoga;\n}\n", "import { YogaInitialContext } from \"graphql-yoga\";\nimport { buildSchemaSync } from \"type-graphql\";\nimport { CopilotResolver } from \"../../graphql/resolvers/copilot.resolver\";\nimport { useDeferStream } from \"@graphql-yoga/plugin-defer-stream\";\nimport { CopilotRuntime } from \"../runtime/copilot-runtime\";\nimport { CopilotServiceAdapter } from \"../../service-adapters\";\nimport { CopilotCloudOptions } from \"../cloud\";\nimport { LogLevel, createLogger } from \"../../lib/logger\";\nimport { createYoga } from \"graphql-yoga\";\nimport telemetry from \"../telemetry-client\";\nimport { StateResolver } from \"../../graphql/resolvers/state.resolver\";\nimport * as packageJson from \"../../../package.json\";\n\nconst logger = createLogger();\n\nexport const addCustomHeaderPlugin = {\n  onResponse({ response }) {\n    // Set your custom header; adjust the header name and value as needed\n    response.headers.set(\"X-CopilotKit-Runtime-Version\", packageJson.version);\n  },\n};\n\ntype AnyPrimitive = string | boolean | number | null;\nexport type CopilotRequestContextProperties = Record<\n  string,\n  AnyPrimitive | Record<string, AnyPrimitive>\n>;\n\nexport type GraphQLContext = YogaInitialContext & {\n  _copilotkit: CreateCopilotRuntimeServerOptions;\n  properties: CopilotRequestContextProperties;\n  logger: typeof logger;\n};\n\nexport interface CreateCopilotRuntimeServerOptions {\n  runtime: CopilotRuntime<any>;\n  serviceAdapter: CopilotServiceAdapter;\n  endpoint: string;\n  baseUrl?: string;\n  cloud?: CopilotCloudOptions;\n  properties?: CopilotRequestContextProperties;\n  logLevel?: LogLevel;\n}\n\nexport async function createContext(\n  initialContext: YogaInitialContext,\n  copilotKitContext: CreateCopilotRuntimeServerOptions,\n  contextLogger: typeof logger,\n  properties: CopilotRequestContextProperties = {},\n): Promise<Partial<GraphQLContext>> {\n  logger.debug({ copilotKitContext }, \"Creating GraphQL context\");\n  const ctx: GraphQLContext = {\n    ...initialContext,\n    _copilotkit: {\n      ...copilotKitContext,\n    },\n    properties: { ...properties },\n    logger: contextLogger,\n  };\n  return ctx;\n}\n\nexport function buildSchema(\n  options: {\n    emitSchemaFile?: string;\n  } = {},\n) {\n  logger.debug(\"Building GraphQL schema...\");\n  const schema = buildSchemaSync({\n    resolvers: [CopilotResolver, StateResolver],\n    emitSchemaFile: options.emitSchemaFile,\n  });\n  logger.debug(\"GraphQL schema built successfully\");\n  return schema;\n}\n\nexport type CommonConfig = {\n  logging: typeof logger;\n  schema: ReturnType<typeof buildSchema>;\n  plugins: Parameters<typeof createYoga>[0][\"plugins\"];\n  context: (ctx: YogaInitialContext) => Promise<Partial<GraphQLContext>>;\n};\n\nexport function getCommonConfig(options: CreateCopilotRuntimeServerOptions): CommonConfig {\n  const logLevel = (process.env.LOG_LEVEL as LogLevel) || (options.logLevel as LogLevel) || \"error\";\n  const logger = createLogger({ level: logLevel, component: \"getCommonConfig\" });\n\n  const contextLogger = createLogger({ level: logLevel });\n\n  if (options.cloud) {\n    telemetry.setCloudConfiguration({\n      publicApiKey: options.cloud.publicApiKey,\n      baseUrl: options.cloud.baseUrl,\n    });\n  }\n\n  if (options.properties?._copilotkit) {\n    telemetry.setGlobalProperties({\n      _copilotkit: {\n        ...(options.properties._copilotkit as Record<string, any>),\n      },\n    });\n  }\n\n  telemetry.setGlobalProperties({\n    runtime: {\n      serviceAdapter: options.serviceAdapter.constructor.name,\n    },\n  });\n\n  return {\n    logging: createLogger({ component: \"Yoga GraphQL\", level: logLevel }),\n    schema: buildSchema(),\n    plugins: [useDeferStream(), addCustomHeaderPlugin],\n    context: (ctx: YogaInitialContext): Promise<Partial<GraphQLContext>> =>\n      createContext(ctx, options, contextLogger, options.properties),\n  };\n}\n", "import { Arg, Ctx, Mutation, Query, Resolver } from \"type-graphql\";\nimport {\n  ReplaySubject,\n  Subject,\n  Subscription,\n  filter,\n  finalize,\n  firstValueFrom,\n  shareReplay,\n  skipWhile,\n  take,\n  takeWhile,\n  tap,\n} from \"rxjs\";\nimport { GenerateCopilotResponseInput } from \"../inputs/generate-copilot-response.input\";\nimport { CopilotResponse } from \"../types/copilot-response.type\";\nimport {\n  CopilotKitLangGraphInterruptEvent,\n  LangGraphInterruptEvent,\n} from \"../types/meta-events.type\";\nimport { ActionInputAvailability, MessageRole } from \"../types/enums\";\nimport { Repeater } from \"graphql-yoga\";\nimport type { CopilotRequestContextProperties, GraphQLContext } from \"../../lib/integrations\";\nimport {\n  RuntimeEvent,\n  RuntimeEventTypes,\n  RuntimeMetaEventName,\n} from \"../../service-adapters/events\";\nimport {\n  FailedMessageStatus,\n  MessageStatusCode,\n  MessageStatusUnion,\n  SuccessMessageStatus,\n} from \"../types/message-status.type\";\nimport { ResponseStatusUnion, SuccessResponseStatus } from \"../types/response-status.type\";\nimport { GraphQLJSONObject } from \"graphql-scalars\";\nimport { plainToInstance } from \"class-transformer\";\nimport { GuardrailsResult } from \"../types/guardrails-result.type\";\nimport { GraphQLError } from \"graphql\";\nimport {\n  GuardrailsValidationFailureResponse,\n  MessageStreamInterruptedResponse,\n  UnknownErrorResponse,\n} from \"../../utils\";\nimport {\n  ActionExecutionMessage,\n  AgentStateMessage,\n  Message,\n  MessageType,\n  ResultMessage,\n  TextMessage,\n} from \"../types/converted\";\nimport telemetry from \"../../lib/telemetry-client\";\nimport { randomId } from \"@copilotkit/shared\";\nimport { AgentsResponse } from \"../types/agents-response.type\";\n\nconst invokeGuardrails = async ({\n  baseUrl,\n  copilotCloudPublicApiKey,\n  data,\n  onResult,\n  onError,\n}: {\n  baseUrl: string;\n  copilotCloudPublicApiKey: string;\n  data: GenerateCopilotResponseInput;\n  onResult: (result: GuardrailsResult) => void;\n  onError: (err: Error) => void;\n}) => {\n  if (\n    data.messages.length &&\n    data.messages[data.messages.length - 1].textMessage?.role === MessageRole.user\n  ) {\n    const messages = data.messages\n      .filter(\n        (m) =>\n          m.textMessage !== undefined &&\n          (m.textMessage.role === MessageRole.user || m.textMessage.role === MessageRole.assistant),\n      )\n      .map((m) => ({\n        role: m.textMessage!.role,\n        content: m.textMessage.content,\n      }));\n\n    const lastMessage = messages[messages.length - 1];\n    const restOfMessages = messages.slice(0, -1);\n\n    const body = {\n      input: lastMessage.content,\n      validTopics: data.cloud.guardrails.inputValidationRules.allowList,\n      invalidTopics: data.cloud.guardrails.inputValidationRules.denyList,\n      messages: restOfMessages,\n    };\n\n    const guardrailsResult = await fetch(`${baseUrl}/guardrails/validate`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"X-CopilotCloud-Public-API-Key\": copilotCloudPublicApiKey,\n      },\n      body: JSON.stringify(body),\n    });\n\n    if (guardrailsResult.ok) {\n      const resultJson: GuardrailsResult = await guardrailsResult.json();\n      onResult(resultJson);\n    } else {\n      onError(await guardrailsResult.json());\n    }\n  }\n};\n\n@Resolver(() => CopilotResponse)\nexport class CopilotResolver {\n  @Query(() => String)\n  async hello() {\n    return \"Hello World\";\n  }\n\n  @Query(() => AgentsResponse)\n  async availableAgents(@Ctx() ctx: GraphQLContext) {\n    let logger = ctx.logger.child({ component: \"CopilotResolver.availableAgents\" });\n\n    logger.debug(\"Processing\");\n    const agentsWithEndpoints = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);\n\n    logger.debug(\"Event source created, creating response\");\n\n    return {\n      agents: agentsWithEndpoints.map(\n        ({ endpoint, ...agentWithoutEndpoint }) => agentWithoutEndpoint,\n      ),\n    };\n  }\n\n  @Mutation(() => CopilotResponse)\n  async generateCopilotResponse(\n    @Ctx() ctx: GraphQLContext,\n    @Arg(\"data\") data: GenerateCopilotResponseInput,\n    @Arg(\"properties\", () => GraphQLJSONObject, { nullable: true })\n    properties?: CopilotRequestContextProperties,\n  ) {\n    telemetry.capture(\"oss.runtime.copilot_request_created\", {\n      \"cloud.guardrails.enabled\": data.cloud?.guardrails !== undefined,\n      requestType: data.metadata.requestType,\n    });\n\n    let logger = ctx.logger.child({ component: \"CopilotResolver.generateCopilotResponse\" });\n    logger.debug({ data }, \"Generating Copilot response\");\n\n    if (properties) {\n      logger.debug(\"Properties provided, merging with context properties\");\n      ctx.properties = { ...ctx.properties, ...properties };\n    }\n\n    const copilotRuntime = ctx._copilotkit.runtime;\n    const serviceAdapter = ctx._copilotkit.serviceAdapter;\n\n    let copilotCloudPublicApiKey: string | null = null;\n    let copilotCloudBaseUrl: string;\n\n    if (data.cloud) {\n      logger = logger.child({ cloud: true });\n      logger.debug(\"Cloud configuration provided, checking for public API key in headers\");\n      const key = ctx.request.headers.get(\"x-copilotcloud-public-api-key\");\n      if (key) {\n        logger.debug(\"Public API key found in headers\");\n        copilotCloudPublicApiKey = key;\n      } else {\n        logger.error(\"Public API key not found in headers\");\n        throw new GraphQLError(\"X-CopilotCloud-Public-API-Key header is required\");\n      }\n\n      if (process.env.COPILOT_CLOUD_BASE_URL) {\n        copilotCloudBaseUrl = process.env.COPILOT_CLOUD_BASE_URL;\n      } else if (ctx._copilotkit.cloud?.baseUrl) {\n        copilotCloudBaseUrl = ctx._copilotkit.cloud?.baseUrl;\n      } else {\n        copilotCloudBaseUrl = \"https://api.cloud.copilotkit.ai\";\n      }\n\n      logger = logger.child({ copilotCloudBaseUrl });\n    }\n\n    logger.debug(\"Setting up subjects\");\n    const responseStatus$ = new ReplaySubject<typeof ResponseStatusUnion>();\n    const interruptStreaming$ = new ReplaySubject<{ reason: string; messageId?: string }>();\n    const guardrailsResult$ = new ReplaySubject<GuardrailsResult>();\n\n    let outputMessages: Message[] = [];\n    let resolveOutputMessagesPromise: (messages: Message[]) => void;\n    let rejectOutputMessagesPromise: (err: Error) => void;\n\n    const outputMessagesPromise = new Promise<Message[]>((resolve, reject) => {\n      resolveOutputMessagesPromise = resolve;\n      rejectOutputMessagesPromise = reject;\n    });\n\n    if (copilotCloudPublicApiKey) {\n      ctx.properties[\"copilotCloudPublicApiKey\"] = copilotCloudPublicApiKey;\n    }\n\n    logger.debug(\"Processing\");\n    const {\n      eventSource,\n      threadId = randomId(),\n      runId,\n      serverSideActions,\n      actionInputsWithoutAgents,\n      extensions,\n    } = await copilotRuntime.processRuntimeRequest({\n      serviceAdapter,\n      messages: data.messages,\n      actions: data.frontend.actions.filter(\n        (action) => action.available !== ActionInputAvailability.disabled,\n      ),\n      threadId: data.threadId,\n      runId: data.runId,\n      publicApiKey: copilotCloudPublicApiKey,\n      outputMessagesPromise,\n      graphqlContext: ctx,\n      forwardedParameters: data.forwardedParameters,\n      agentSession: data.agentSession,\n      agentStates: data.agentStates,\n      url: data.frontend.url,\n      extensions: data.extensions,\n      metaEvents: data.metaEvents,\n    });\n\n    logger.debug(\"Event source created, creating response\");\n    // run and process the event stream\n    const eventStream = eventSource\n      .processRuntimeEvents({\n        serverSideActions,\n        guardrailsResult$: data.cloud?.guardrails ? guardrailsResult$ : null,\n        actionInputsWithoutAgents: actionInputsWithoutAgents.filter(\n          // TODO-AGENTS: do not exclude ALL server side actions\n          (action) =>\n            !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name),\n        ),\n        threadId,\n      })\n      .pipe(\n        // shareReplay() ensures that later subscribers will see the whole stream instead of\n        // just the events that were emitted after the subscriber was added.\n        shareReplay(),\n        finalize(() => {\n          logger.debug(\"Event stream finalized\");\n        }),\n      );\n\n    const response = {\n      threadId,\n      runId,\n      status: firstValueFrom(responseStatus$),\n      extensions,\n      metaEvents: new Repeater(async (push, stop) => {\n        let eventStreamSubscription: Subscription;\n\n        eventStreamSubscription = eventStream.subscribe({\n          next: async (event) => {\n            if (event.type != RuntimeEventTypes.MetaEvent) {\n              return;\n            }\n            switch (event.name) {\n              case RuntimeMetaEventName.LangGraphInterruptEvent:\n                push(\n                  plainToInstance(LangGraphInterruptEvent, {\n                    type: event.type,\n                    name: event.name,\n                    value: event.value,\n                  }),\n                );\n                break;\n              case RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent:\n                push(\n                  plainToInstance(CopilotKitLangGraphInterruptEvent, {\n                    type: event.type,\n                    name: event.name,\n                    data: {\n                      value: event.data.value,\n                      messages: event.data.messages.map((message) => {\n                        if (\n                          message.type === \"TextMessage\" ||\n                          (\"content\" in message && \"role\" in message)\n                        ) {\n                          return plainToInstance(TextMessage, {\n                            id: message.id,\n                            createdAt: new Date(),\n                            content: [(message as TextMessage).content],\n                            role: (message as TextMessage).role,\n                            status: new SuccessMessageStatus(),\n                          });\n                        }\n                        if (\"arguments\" in message) {\n                          return plainToInstance(ActionExecutionMessage, {\n                            name: message.name,\n                            id: message.id,\n                            arguments: [JSON.stringify(message.arguments)],\n                            createdAt: new Date(),\n                            status: new SuccessMessageStatus(),\n                          });\n                        }\n                        throw new Error(\"Unknown message in metaEvents copilot resolver\");\n                      }),\n                    },\n                  }),\n                );\n                break;\n            }\n          },\n          error: (err) => {\n            logger.error({ err }, \"Error in meta events stream\");\n            responseStatus$.next(\n              new UnknownErrorResponse({\n                description: `An unknown error has occurred in the event stream`,\n              }),\n            );\n            eventStreamSubscription?.unsubscribe();\n            stop();\n          },\n          complete: async () => {\n            logger.debug(\"Meta events stream completed\");\n            responseStatus$.next(new SuccessResponseStatus());\n            eventStreamSubscription?.unsubscribe();\n            stop();\n          },\n        });\n      }),\n      messages: new Repeater(async (pushMessage, stopStreamingMessages) => {\n        logger.debug(\"Messages repeater created\");\n\n        if (data.cloud?.guardrails) {\n          logger = logger.child({ guardrails: true });\n          logger.debug(\"Guardrails is enabled, validating input\");\n\n          invokeGuardrails({\n            baseUrl: copilotCloudBaseUrl,\n            copilotCloudPublicApiKey,\n            data,\n            onResult: (result) => {\n              logger.debug({ status: result.status }, \"Guardrails validation done\");\n              guardrailsResult$.next(result);\n\n              // Guardrails validation failed\n              if (result.status === \"denied\") {\n                // send the reason to the client and interrupt streaming\n                responseStatus$.next(\n                  new GuardrailsValidationFailureResponse({ guardrailsReason: result.reason }),\n                );\n                interruptStreaming$.next({\n                  reason: `Interrupted due to Guardrails validation failure. Reason: ${result.reason}`,\n                });\n\n                // resolve messages promise to the middleware\n                outputMessages = [\n                  plainToInstance(TextMessage, {\n                    id: randomId(),\n                    createdAt: new Date(),\n                    content: result.reason,\n                    role: MessageRole.assistant,\n                  }),\n                ];\n                resolveOutputMessagesPromise(outputMessages);\n              }\n            },\n            onError: (err) => {\n              logger.error({ err }, \"Error in guardrails validation\");\n              responseStatus$.next(\n                new UnknownErrorResponse({\n                  description: `An unknown error has occurred in the guardrails validation`,\n                }),\n              );\n              interruptStreaming$.next({\n                reason: `Interrupted due to unknown error in guardrails validation`,\n              });\n\n              // reject the middleware promise\n              rejectOutputMessagesPromise(err);\n            },\n          });\n        }\n\n        let eventStreamSubscription: Subscription;\n\n        logger.debug(\"Event stream created, subscribing to event stream\");\n\n        eventStreamSubscription = eventStream.subscribe({\n          next: async (event) => {\n            switch (event.type) {\n              case RuntimeEventTypes.MetaEvent:\n                break;\n              ////////////////////////////////\n              // TextMessageStart\n              ////////////////////////////////\n              case RuntimeEventTypes.TextMessageStart:\n                // create a sub stream that contains the message content\n                const textMessageContentStream = eventStream.pipe(\n                  // skip until this message start event\n                  skipWhile((e) => e !== event),\n                  // take until the message end event\n                  takeWhile(\n                    (e) =>\n                      !(\n                        e.type === RuntimeEventTypes.TextMessageEnd &&\n                        e.messageId == event.messageId\n                      ),\n                  ),\n                  // filter out any other message events or message ids\n                  filter(\n                    (e) =>\n                      e.type == RuntimeEventTypes.TextMessageContent &&\n                      e.messageId == event.messageId,\n                  ),\n                );\n\n                // signal when we are done streaming\n                const streamingTextStatus = new Subject<typeof MessageStatusUnion>();\n\n                const messageId = event.messageId;\n                // push the new message\n                pushMessage({\n                  id: messageId,\n                  parentMessageId: event.parentMessageId,\n                  status: firstValueFrom(streamingTextStatus),\n                  createdAt: new Date(),\n                  role: MessageRole.assistant,\n                  content: new Repeater(async (pushTextChunk, stopStreamingText) => {\n                    logger.debug(\"Text message content repeater created\");\n\n                    const textChunks: string[] = [];\n                    let textSubscription: Subscription;\n\n                    interruptStreaming$\n                      .pipe(\n                        shareReplay(),\n                        take(1),\n                        tap(({ reason, messageId }) => {\n                          logger.debug({ reason, messageId }, \"Text streaming interrupted\");\n\n                          streamingTextStatus.next(\n                            plainToInstance(FailedMessageStatus, { reason }),\n                          );\n\n                          responseStatus$.next(new MessageStreamInterruptedResponse({ messageId }));\n                          stopStreamingText();\n                          textSubscription?.unsubscribe();\n                        }),\n                      )\n                      .subscribe();\n\n                    logger.debug(\"Subscribing to text message content stream\");\n\n                    textSubscription = textMessageContentStream.subscribe({\n                      next: async (e: RuntimeEvent) => {\n                        if (e.type == RuntimeEventTypes.TextMessageContent) {\n                          await pushTextChunk(e.content);\n                          textChunks.push(e.content);\n                        }\n                      },\n                      error: (err) => {\n                        logger.error({ err }, \"Error in text message content stream\");\n                        interruptStreaming$.next({\n                          reason: \"Error streaming message content\",\n                          messageId,\n                        });\n                        stopStreamingText();\n                        textSubscription?.unsubscribe();\n                      },\n                      complete: () => {\n                        logger.debug(\"Text message content stream completed\");\n                        streamingTextStatus.next(new SuccessMessageStatus());\n                        stopStreamingText();\n                        textSubscription?.unsubscribe();\n\n                        outputMessages.push(\n                          plainToInstance(TextMessage, {\n                            id: messageId,\n                            createdAt: new Date(),\n                            content: textChunks.join(\"\"),\n                            role: MessageRole.assistant,\n                          }),\n                        );\n                      },\n                    });\n                  }),\n                });\n                break;\n              ////////////////////////////////\n              // ActionExecutionStart\n              ////////////////////////////////\n              case RuntimeEventTypes.ActionExecutionStart:\n                logger.debug(\"Action execution start event received\");\n                const actionExecutionArgumentStream = eventStream.pipe(\n                  skipWhile((e) => e !== event),\n                  // take until the action execution end event\n                  takeWhile(\n                    (e) =>\n                      !(\n                        e.type === RuntimeEventTypes.ActionExecutionEnd &&\n                        e.actionExecutionId == event.actionExecutionId\n                      ),\n                  ),\n                  // filter out any other action execution events or action execution ids\n                  filter(\n                    (e) =>\n                      e.type == RuntimeEventTypes.ActionExecutionArgs &&\n                      e.actionExecutionId == event.actionExecutionId,\n                  ),\n                );\n                const streamingArgumentsStatus = new Subject<typeof MessageStatusUnion>();\n                pushMessage({\n                  id: event.actionExecutionId,\n                  parentMessageId: event.parentMessageId,\n                  status: firstValueFrom(streamingArgumentsStatus),\n                  createdAt: new Date(),\n                  name: event.actionName,\n                  arguments: new Repeater(async (pushArgumentsChunk, stopStreamingArguments) => {\n                    logger.debug(\"Action execution argument stream created\");\n\n                    const argumentChunks: string[] = [];\n                    let actionExecutionArgumentSubscription: Subscription;\n\n                    actionExecutionArgumentSubscription = actionExecutionArgumentStream.subscribe({\n                      next: async (e: RuntimeEvent) => {\n                        if (e.type == RuntimeEventTypes.ActionExecutionArgs) {\n                          await pushArgumentsChunk(e.args);\n                          argumentChunks.push(e.args);\n                        }\n                      },\n                      error: (err) => {\n                        logger.error({ err }, \"Error in action execution argument stream\");\n                        streamingArgumentsStatus.next(\n                          plainToInstance(FailedMessageStatus, {\n                            reason:\n                              \"An unknown error has occurred in the action execution argument stream\",\n                          }),\n                        );\n                        stopStreamingArguments();\n                        actionExecutionArgumentSubscription?.unsubscribe();\n                      },\n                      complete: () => {\n                        logger.debug(\"Action execution argument stream completed\");\n                        streamingArgumentsStatus.next(new SuccessMessageStatus());\n                        stopStreamingArguments();\n                        actionExecutionArgumentSubscription?.unsubscribe();\n\n                        outputMessages.push(\n                          plainToInstance(ActionExecutionMessage, {\n                            id: event.actionExecutionId,\n                            createdAt: new Date(),\n                            name: event.actionName,\n                            arguments: argumentChunks.join(\"\"),\n                          }),\n                        );\n                      },\n                    });\n                  }),\n                });\n                break;\n              ////////////////////////////////\n              // ActionExecutionResult\n              ////////////////////////////////\n              case RuntimeEventTypes.ActionExecutionResult:\n                logger.debug({ result: event.result }, \"Action execution result event received\");\n                pushMessage({\n                  id: \"result-\" + event.actionExecutionId,\n                  status: new SuccessMessageStatus(),\n                  createdAt: new Date(),\n                  actionExecutionId: event.actionExecutionId,\n                  actionName: event.actionName,\n                  result: event.result,\n                });\n\n                outputMessages.push(\n                  plainToInstance(ResultMessage, {\n                    id: \"result-\" + event.actionExecutionId,\n                    createdAt: new Date(),\n                    actionExecutionId: event.actionExecutionId,\n                    actionName: event.actionName,\n                    result: event.result,\n                  }),\n                );\n                break;\n              ////////////////////////////////\n              // AgentStateMessage\n              ////////////////////////////////\n              case RuntimeEventTypes.AgentStateMessage:\n                logger.debug({ event }, \"Agent message event received\");\n                pushMessage({\n                  id: randomId(),\n                  status: new SuccessMessageStatus(),\n                  threadId: event.threadId,\n                  agentName: event.agentName,\n                  nodeName: event.nodeName,\n                  runId: event.runId,\n                  active: event.active,\n                  state: event.state,\n                  running: event.running,\n                  role: MessageRole.assistant,\n                  createdAt: new Date(),\n                });\n                outputMessages.push(\n                  plainToInstance(AgentStateMessage, {\n                    id: randomId(),\n                    threadId: event.threadId,\n                    agentName: event.agentName,\n                    nodeName: event.nodeName,\n                    runId: event.runId,\n                    active: event.active,\n                    state: event.state,\n                    running: event.running,\n                    role: MessageRole.assistant,\n                    createdAt: new Date(),\n                  }),\n                );\n                break;\n            }\n          },\n          error: (err) => {\n            logger.error({ err }, \"Error in event stream\");\n            responseStatus$.next(\n              new UnknownErrorResponse({\n                description: `An unknown error has occurred in the event stream`,\n              }),\n            );\n            eventStreamSubscription?.unsubscribe();\n            stopStreamingMessages();\n\n            rejectOutputMessagesPromise(err);\n          },\n          complete: async () => {\n            logger.debug(\"Event stream completed\");\n            if (data.cloud?.guardrails) {\n              logger.debug(\"Guardrails is enabled, waiting for guardrails result\");\n              await firstValueFrom(guardrailsResult$);\n            }\n            responseStatus$.next(new SuccessResponseStatus());\n            eventStreamSubscription?.unsubscribe();\n            stopStreamingMessages();\n\n            resolveOutputMessagesPromise(outputMessages);\n          },\n        });\n      }),\n    };\n\n    return response;\n  }\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { MessageInput } from \"./message.input\";\nimport { FrontendInput } from \"./frontend.input\";\nimport { CloudInput } from \"./cloud.input\";\nimport { CopilotRequestType } from \"../types/enums\";\nimport { ForwardedParametersInput } from \"./forwarded-parameters.input\";\nimport { AgentSessionInput } from \"./agent-session.input\";\nimport { AgentStateInput } from \"./agent-state.input\";\nimport { ExtensionsInput } from \"./extensions.input\";\nimport { MetaEventInput } from \"./meta-event.input\";\n\n@InputType()\nexport class GenerateCopilotResponseMetadataInput {\n  @Field(() => CopilotRequestType, { nullable: true })\n  requestType: CopilotRequestType;\n}\n\n@InputType()\nexport class GenerateCopilotResponseInput {\n  @Field(() => GenerateCopilotResponseMetadataInput, { nullable: false })\n  metadata: GenerateCopilotResponseMetadataInput;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n\n  @Field(() => String, { nullable: true })\n  runId?: string;\n\n  @Field(() => [MessageInput])\n  messages: MessageInput[];\n\n  @Field(() => FrontendInput)\n  frontend: FrontendInput;\n\n  @Field(() => CloudInput, { nullable: true })\n  cloud?: CloudInput;\n\n  @Field(() => ForwardedParametersInput, { nullable: true })\n  forwardedParameters?: ForwardedParametersInput;\n\n  @Field(() => AgentSessionInput, { nullable: true })\n  agentSession?: AgentSessionInput;\n\n  @Field(() => AgentStateInput, { nullable: true })\n  agentState?: AgentStateInput;\n\n  @Field(() => [AgentStateInput], { nullable: true })\n  agentStates?: AgentStateInput[];\n\n  @Field(() => ExtensionsInput, { nullable: true })\n  extensions?: ExtensionsInput;\n\n  @Field(() => [MetaEventInput], { nullable: true })\n  metaEvents?: MetaEventInput[];\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { MessageRole } from \"../types/enums\";\nimport { BaseMessageInput } from \"../types/base\";\n\n// GraphQL does not support union types in inputs, so we need to use\n// optional fields for the different subtypes.\n@InputType()\nexport class MessageInput extends BaseMessageInput {\n  @Field(() => TextMessageInput, { nullable: true })\n  textMessage?: TextMessageInput;\n\n  @Field(() => ActionExecutionMessageInput, { nullable: true })\n  actionExecutionMessage?: ActionExecutionMessageInput;\n\n  @Field(() => ResultMessageInput, { nullable: true })\n  resultMessage?: ResultMessageInput;\n\n  @Field(() => AgentStateMessageInput, { nullable: true })\n  agentStateMessage?: AgentStateMessageInput;\n\n  @Field(() => ImageMessageInput, { nullable: true })\n  imageMessage?: ImageMessageInput;\n}\n\n@InputType()\nexport class TextMessageInput {\n  @Field(() => String)\n  content: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n}\n\n@InputType()\nexport class ActionExecutionMessageInput {\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String)\n  arguments: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => String, {\n    nullable: true,\n    deprecationReason: \"This field will be removed in a future version\",\n  })\n  scope?: String;\n}\n\n@InputType()\nexport class ResultMessageInput {\n  @Field(() => String)\n  actionExecutionId: string;\n\n  @Field(() => String)\n  actionName: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => String)\n  result: string;\n}\n\n@InputType()\nexport class AgentStateMessageInput {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => Boolean)\n  running: boolean;\n\n  @Field(() => String)\n  nodeName: string;\n\n  @Field(() => String)\n  runId: string;\n\n  @Field(() => Boolean)\n  active: boolean;\n}\n\n@InputType()\nexport class ImageMessageInput {\n  @Field(() => String)\n  format: string;\n\n  @Field(() => String)\n  bytes: string;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n}\n", "import { registerEnumType } from \"type-graphql\";\n\nexport enum MessageRole {\n  user = \"user\",\n  assistant = \"assistant\",\n  system = \"system\",\n  tool = \"tool\",\n  developer = \"developer\",\n}\n\nexport enum CopilotRequestType {\n  Chat = \"Chat\",\n  Task = \"Task\",\n  TextareaCompletion = \"TextareaCompletion\",\n  TextareaPopover = \"TextareaPopover\",\n  Suggestion = \"Suggestion\",\n}\n\nexport enum ActionInputAvailability {\n  disabled = \"disabled\",\n  enabled = \"enabled\",\n  remote = \"remote\",\n}\n\nregisterEnumType(MessageRole, {\n  name: \"MessageRole\",\n  description: \"The role of the message\",\n});\n\nregisterEnumType(CopilotRequestType, {\n  name: \"CopilotRequestType\",\n  description: \"The type of Copilot request\",\n});\n\nregisterEnumType(ActionInputAvailability, {\n  name: \"ActionInputAvailability\",\n  description: \"The availability of the frontend action\",\n});\n", "import { Field, InputType } from \"type-graphql\";\nimport { ActionInput } from \"./action.input\";\n\n@InputType()\nexport class FrontendInput {\n  @Field(() => String, { nullable: true })\n  toDeprecate_fullContext?: string;\n\n  @Field(() => [ActionInput])\n  actions: ActionInput[];\n\n  @Field(() => String, { nullable: true })\n  url?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { ActionInputAvailability } from \"../types/enums\";\n@InputType()\nexport class ActionInput {\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String)\n  description: string;\n\n  @Field(() => String)\n  jsonSchema: string;\n\n  @Field(() => ActionInputAvailability, { nullable: true })\n  available?: ActionInputAvailability;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { GuardrailsInput } from \"./cloud-guardrails.input\";\n\n@InputType()\nexport class CloudInput {\n  @Field(() => GuardrailsInput, { nullable: true })\n  guardrails?: GuardrailsInput;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class GuardrailsRuleInput {\n  @Field(() => [String], { nullable: true })\n  allowList?: string[] = [];\n\n  @Field(() => [String], { nullable: true })\n  denyList?: string[] = [];\n}\n\n@InputType()\nexport class GuardrailsInput {\n  @Field(() => GuardrailsRuleInput, { nullable: false })\n  inputValidationRules: GuardrailsRuleInput;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class ForwardedParametersInput {\n  @Field(() => String, { nullable: true })\n  model?: string;\n\n  @Field(() => Number, { nullable: true })\n  maxTokens?: number;\n\n  @Field(() => [String], { nullable: true })\n  stop?: string[];\n\n  @Field(() => String, { nullable: true })\n  toolChoice?: String;\n\n  @Field(() => String, { nullable: true })\n  toolChoiceFunctionName?: string;\n\n  @Field(() => Number, { nullable: true })\n  temperature?: number;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class AgentSessionInput {\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n\n  @Field(() => String, { nullable: true })\n  nodeName?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class AgentStateInput {\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => String, { nullable: true })\n  config?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n/**\n * The extensions input is used to pass additional information to the copilot runtime, specific to a\n * service adapter or agent framework.\n */\n\n@InputType()\nexport class ExtensionsInput {\n  @Field(() => OpenAIApiAssistantAPIInput, { nullable: true })\n  openaiAssistantAPI?: OpenAIApiAssistantAPIInput;\n}\n\n@InputType()\nexport class OpenAIApiAssistantAPIInput {\n  @Field(() => String, { nullable: true })\n  runId?: string;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\nimport { MetaEventName } from \"../types/meta-events.type\";\nimport { MessageInput } from \"./message.input\";\n\n@InputType()\nexport class MetaEventInput {\n  @Field(() => MetaEventName)\n  name: MetaEventName;\n\n  @Field(() => String)\n  value?: string;\n\n  @Field(() => String, { nullable: true })\n  response?: string;\n\n  @Field(() => [MessageInput], { nullable: true })\n  messages?: MessageInput[];\n}\n", "import { createUnionType, Field, InterfaceType, ObjectType, registerEnumType } from \"type-graphql\";\nimport {\n  ActionExecutionMessageOutput,\n  AgentStateMessageOutput,\n  BaseMessageOutput,\n  ResultMessageOutput,\n  TextMessageOutput,\n} from \"./copilot-response.type\";\n\nexport enum MetaEventName {\n  LangGraphInterruptEvent = \"LangGraphInterruptEvent\",\n  CopilotKitLangGraphInterruptEvent = \"CopilotKitLangGraphInterruptEvent\",\n}\n\nregisterEnumType(MetaEventName, {\n  name: \"MetaEventName\",\n  description: \"Meta event types\",\n});\n\n@InterfaceType({\n  resolveType(value) {\n    if (value.name === MetaEventName.LangGraphInterruptEvent) {\n      return LangGraphInterruptEvent;\n    } else if (value.name === MetaEventName.CopilotKitLangGraphInterruptEvent) {\n      return CopilotKitLangGraphInterruptEvent;\n    }\n    return undefined;\n  },\n})\n@InterfaceType()\nexport abstract class BaseMetaEvent {\n  @Field(() => String)\n  type: \"MetaEvent\" = \"MetaEvent\";\n\n  @Field(() => MetaEventName)\n  name: MetaEventName;\n}\n\n@ObjectType()\nexport class CopilotKitLangGraphInterruptEventData {\n  @Field(() => String)\n  value: string;\n\n  @Field(() => [BaseMessageOutput])\n  messages: (typeof BaseMessageOutput)[];\n}\n\n@ObjectType({ implements: BaseMetaEvent })\nexport class LangGraphInterruptEvent {\n  @Field(() => MetaEventName)\n  name: MetaEventName.LangGraphInterruptEvent = MetaEventName.LangGraphInterruptEvent;\n\n  @Field(() => String)\n  value: string;\n\n  @Field(() => String, { nullable: true })\n  response?: string;\n}\n\n@ObjectType({ implements: BaseMetaEvent })\nexport class CopilotKitLangGraphInterruptEvent {\n  @Field(() => MetaEventName)\n  name: MetaEventName.CopilotKitLangGraphInterruptEvent =\n    MetaEventName.CopilotKitLangGraphInterruptEvent;\n\n  @Field(() => CopilotKitLangGraphInterruptEventData)\n  data: CopilotKitLangGraphInterruptEventData;\n\n  @Field(() => String, { nullable: true })\n  response?: string;\n}\n", "import { Field, InterfaceType, ObjectType } from \"type-graphql\";\nimport { MessageRole } from \"./enums\";\nimport { MessageStatusUnion } from \"./message-status.type\";\nimport { ResponseStatusUnion } from \"./response-status.type\";\nimport { ExtensionsResponse } from \"./extensions-response.type\";\nimport { BaseMetaEvent } from \"./meta-events.type\";\n\n@InterfaceType({\n  resolveType(value) {\n    if (value.hasOwnProperty(\"content\")) {\n      return TextMessageOutput;\n    } else if (value.hasOwnProperty(\"name\")) {\n      return ActionExecutionMessageOutput;\n    } else if (value.hasOwnProperty(\"result\")) {\n      return ResultMessageOutput;\n    } else if (value.hasOwnProperty(\"state\")) {\n      return AgentStateMessageOutput;\n    } else if (value.hasOwnProperty(\"format\") && value.hasOwnProperty(\"bytes\")) {\n      return ImageMessageOutput;\n    }\n    return undefined;\n  },\n})\nexport abstract class BaseMessageOutput {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => Date)\n  createdAt: Date;\n\n  @Field(() => MessageStatusUnion)\n  status: typeof MessageStatusUnion;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class TextMessageOutput {\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => [String])\n  content: string[];\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class ActionExecutionMessageOutput {\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String, {\n    nullable: true,\n    deprecationReason: \"This field will be removed in a future version\",\n  })\n  scope?: string;\n\n  @Field(() => [String])\n  arguments: string[];\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class ResultMessageOutput {\n  @Field(() => String)\n  actionExecutionId: string;\n\n  @Field(() => String)\n  actionName: string;\n\n  @Field(() => String)\n  result: string;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class AgentStateMessageOutput {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => String)\n  agentName: string;\n\n  @Field(() => String)\n  nodeName: string;\n\n  @Field(() => String)\n  runId: string;\n\n  @Field(() => Boolean)\n  active: boolean;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => Boolean)\n  running: boolean;\n}\n\n@ObjectType({ implements: BaseMessageOutput })\nexport class ImageMessageOutput {\n  @Field(() => String)\n  format: string;\n\n  @Field(() => String)\n  bytes: string;\n\n  @Field(() => MessageRole)\n  role: MessageRole;\n\n  @Field(() => String, { nullable: true })\n  parentMessageId?: string;\n}\n\n@ObjectType()\nexport class CopilotResponse {\n  @Field(() => String)\n  threadId!: string;\n\n  @Field(() => ResponseStatusUnion)\n  status: typeof ResponseStatusUnion;\n\n  @Field({ nullable: true })\n  runId?: string;\n\n  @Field(() => [BaseMessageOutput])\n  messages: (typeof BaseMessageOutput)[];\n\n  @Field(() => ExtensionsResponse, { nullable: true })\n  extensions?: ExtensionsResponse;\n\n  @Field(() => [BaseMetaEvent], { nullable: true })\n  metaEvents?: (typeof BaseMetaEvent)[];\n}\n", "import { Field, ObjectType, createUnionType, registerEnumType } from \"type-graphql\";\n\nexport enum MessageStatusCode {\n  Pending = \"pending\",\n  Success = \"success\",\n  Failed = \"failed\",\n}\n\nregisterEnumType(MessageStatusCode, {\n  name: \"MessageStatusCode\",\n});\n\n@ObjectType()\nclass BaseMessageStatus {\n  @Field(() => MessageStatusCode)\n  code: MessageStatusCode;\n}\n\n@ObjectType()\nexport class PendingMessageStatus extends BaseMessageStatus {\n  code: MessageStatusCode = MessageStatusCode.Pending;\n}\n\n@ObjectType()\nexport class SuccessMessageStatus extends BaseMessageStatus {\n  code: MessageStatusCode = MessageStatusCode.Success;\n}\n\n@ObjectType()\nexport class FailedMessageStatus extends BaseMessageStatus {\n  code: MessageStatusCode = MessageStatusCode.Failed;\n\n  @Field(() => String)\n  reason: string;\n}\n\nexport const MessageStatusUnion = createUnionType({\n  name: \"MessageStatus\",\n  types: () => [PendingMessageStatus, SuccessMessageStatus, FailedMessageStatus] as const,\n});\n", "import { Field, ObjectType } from \"type-graphql\";\n\n/**\n * The extensions response is used to receive additional information from the copilot runtime, specific to a\n * service adapter or agent framework.\n *\n * Next time a request to the runtime is made, the extensions response will be included in the request as input.\n */\n\n@ObjectType()\nexport class ExtensionsResponse {\n  @Field(() => OpenAIApiAssistantAPIResponse, { nullable: true })\n  openaiAssistantAPI?: OpenAIApiAssistantAPIResponse;\n}\n\n@ObjectType()\nexport class OpenAIApiAssistantAPIResponse {\n  @Field(() => String, { nullable: true })\n  runId?: string;\n\n  @Field(() => String, { nullable: true })\n  threadId?: string;\n}\n", "import { Action, randomId } from \"@copilotkit/shared\";\nimport {\n  of,\n  concat,\n  scan,\n  concatMap,\n  ReplaySubject,\n  Subject,\n  firstValueFrom,\n  from,\n  catchError,\n  EMPTY,\n} from \"rxjs\";\nimport { streamLangChainResponse } from \"./langchain/utils\";\nimport { GuardrailsResult } from \"../graphql/types/guardrails-result.type\";\nimport telemetry from \"../lib/telemetry-client\";\nimport { isRemoteAgentAction } from \"../lib/runtime/remote-actions\";\nimport { ActionInput } from \"../graphql/inputs/action.input\";\nimport { ActionExecutionMessage, ResultMessage, TextMessage } from \"../graphql/types/converted\";\nimport { plainToInstance } from \"class-transformer\";\n\nexport enum RuntimeEventTypes {\n  TextMessageStart = \"TextMessageStart\",\n  TextMessageContent = \"TextMessageContent\",\n  TextMessageEnd = \"TextMessageEnd\",\n  ActionExecutionStart = \"ActionExecutionStart\",\n  ActionExecutionArgs = \"ActionExecutionArgs\",\n  ActionExecutionEnd = \"ActionExecutionEnd\",\n  ActionExecutionResult = \"ActionExecutionResult\",\n  AgentStateMessage = \"AgentStateMessage\",\n  MetaEvent = \"MetaEvent\",\n}\n\nexport enum RuntimeMetaEventName {\n  LangGraphInterruptEvent = \"LangGraphInterruptEvent\",\n  LangGraphInterruptResumeEvent = \"LangGraphInterruptResumeEvent\",\n  CopilotKitLangGraphInterruptEvent = \"CopilotKitLangGraphInterruptEvent\",\n}\n\nexport type RunTimeMetaEvent =\n  | {\n      type: RuntimeEventTypes.MetaEvent;\n      name: RuntimeMetaEventName.LangGraphInterruptEvent;\n      value: string;\n    }\n  | {\n      type: RuntimeEventTypes.MetaEvent;\n      name: RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent;\n      data: { value: string; messages: (TextMessage | ActionExecutionMessage | ResultMessage)[] };\n    }\n  | {\n      type: RuntimeEventTypes.MetaEvent;\n      name: RuntimeMetaEventName.LangGraphInterruptResumeEvent;\n      data: string;\n    };\n\nexport type RuntimeEvent =\n  | { type: RuntimeEventTypes.TextMessageStart; messageId: string; parentMessageId?: string }\n  | {\n      type: RuntimeEventTypes.TextMessageContent;\n      messageId: string;\n      content: string;\n    }\n  | { type: RuntimeEventTypes.TextMessageEnd; messageId: string }\n  | {\n      type: RuntimeEventTypes.ActionExecutionStart;\n      actionExecutionId: string;\n      actionName: string;\n      parentMessageId?: string;\n    }\n  | { type: RuntimeEventTypes.ActionExecutionArgs; actionExecutionId: string; args: string }\n  | { type: RuntimeEventTypes.ActionExecutionEnd; actionExecutionId: string }\n  | {\n      type: RuntimeEventTypes.ActionExecutionResult;\n      actionName: string;\n      actionExecutionId: string;\n      result: string;\n    }\n  | {\n      type: RuntimeEventTypes.AgentStateMessage;\n      threadId: string;\n      agentName: string;\n      nodeName: string;\n      runId: string;\n      active: boolean;\n      role: string;\n      state: string;\n      running: boolean;\n    }\n  | RunTimeMetaEvent;\n\ninterface RuntimeEventWithState {\n  event: RuntimeEvent | null;\n  callActionServerSide: boolean;\n  action: Action<any> | null;\n  actionExecutionId: string | null;\n  args: string;\n  actionExecutionParentMessageId: string | null;\n}\n\ntype EventSourceCallback = (eventStream$: RuntimeEventSubject) => Promise<void>;\n\nexport class RuntimeEventSubject extends ReplaySubject<RuntimeEvent> {\n  constructor() {\n    super();\n  }\n\n  sendTextMessageStart({\n    messageId,\n    parentMessageId,\n  }: {\n    messageId: string;\n    parentMessageId?: string;\n  }) {\n    this.next({ type: RuntimeEventTypes.TextMessageStart, messageId, parentMessageId });\n  }\n\n  sendTextMessageContent({ messageId, content }: { messageId: string; content: string }) {\n    this.next({ type: RuntimeEventTypes.TextMessageContent, content, messageId });\n  }\n\n  sendTextMessageEnd({ messageId }: { messageId: string }) {\n    this.next({ type: RuntimeEventTypes.TextMessageEnd, messageId });\n  }\n\n  sendTextMessage(messageId: string, content: string) {\n    this.sendTextMessageStart({ messageId });\n    this.sendTextMessageContent({ messageId, content });\n    this.sendTextMessageEnd({ messageId });\n  }\n\n  sendActionExecutionStart({\n    actionExecutionId,\n    actionName,\n    parentMessageId,\n  }: {\n    actionExecutionId: string;\n    actionName: string;\n    parentMessageId?: string;\n  }) {\n    this.next({\n      type: RuntimeEventTypes.ActionExecutionStart,\n      actionExecutionId,\n      actionName,\n      parentMessageId,\n    });\n  }\n\n  sendActionExecutionArgs({\n    actionExecutionId,\n    args,\n  }: {\n    actionExecutionId: string;\n    args: string;\n  }) {\n    this.next({ type: RuntimeEventTypes.ActionExecutionArgs, args, actionExecutionId });\n  }\n\n  sendActionExecutionEnd({ actionExecutionId }: { actionExecutionId: string }) {\n    this.next({ type: RuntimeEventTypes.ActionExecutionEnd, actionExecutionId });\n  }\n\n  sendActionExecution({\n    actionExecutionId,\n    actionName,\n    args,\n    parentMessageId,\n  }: {\n    actionExecutionId: string;\n    actionName: string;\n    args: string;\n    parentMessageId?: string;\n  }) {\n    this.sendActionExecutionStart({ actionExecutionId, actionName, parentMessageId });\n    this.sendActionExecutionArgs({ actionExecutionId, args });\n    this.sendActionExecutionEnd({ actionExecutionId });\n  }\n\n  sendActionExecutionResult({\n    actionExecutionId,\n    actionName,\n    result,\n    error,\n  }: {\n    actionExecutionId: string;\n    actionName: string;\n    result?: string;\n    error?: { code: string; message: string };\n  }) {\n    this.next({\n      type: RuntimeEventTypes.ActionExecutionResult,\n      actionName,\n      actionExecutionId,\n      result: ResultMessage.encodeResult(result, error),\n    });\n  }\n\n  sendAgentStateMessage({\n    threadId,\n    agentName,\n    nodeName,\n    runId,\n    active,\n    role,\n    state,\n    running,\n  }: {\n    threadId: string;\n    agentName: string;\n    nodeName: string;\n    runId: string;\n    active: boolean;\n    role: string;\n    state: string;\n    running: boolean;\n  }) {\n    this.next({\n      type: RuntimeEventTypes.AgentStateMessage,\n      threadId,\n      agentName,\n      nodeName,\n      runId,\n      active,\n      role,\n      state,\n      running,\n    });\n  }\n}\n\nexport class RuntimeEventSource {\n  private eventStream$ = new RuntimeEventSubject();\n  private callback!: EventSourceCallback;\n\n  async stream(callback: EventSourceCallback): Promise<void> {\n    this.callback = callback;\n  }\n\n  sendErrorMessageToChat(message = \"An error occurred. Please try again.\") {\n    const errorMessage = `❌ ${message}`;\n    if (!this.callback) {\n      this.stream(async (eventStream$) => {\n        eventStream$.sendTextMessage(randomId(), errorMessage);\n      });\n    } else {\n      this.eventStream$.sendTextMessage(randomId(), errorMessage);\n    }\n  }\n\n  processRuntimeEvents({\n    serverSideActions,\n    guardrailsResult$,\n    actionInputsWithoutAgents,\n    threadId,\n  }: {\n    serverSideActions: Action<any>[];\n    guardrailsResult$?: Subject<GuardrailsResult>;\n    actionInputsWithoutAgents: ActionInput[];\n    threadId: string;\n  }) {\n    this.callback(this.eventStream$).catch((error) => {\n      console.error(\"Error in event source callback\", error);\n      this.sendErrorMessageToChat();\n      this.eventStream$.complete();\n    });\n    return this.eventStream$.pipe(\n      // track state\n      scan(\n        (acc, event) => {\n          // It seems like this is needed so that rxjs recognizes the object has changed\n          // This fixes an issue where action were executed multiple times\n          // Not investigating further for now (Markus)\n          acc = { ...acc };\n\n          if (event.type === RuntimeEventTypes.ActionExecutionStart) {\n            acc.callActionServerSide =\n              serverSideActions.find((action) => action.name === event.actionName) !== undefined;\n            acc.args = \"\";\n            acc.actionExecutionId = event.actionExecutionId;\n            if (acc.callActionServerSide) {\n              acc.action = serverSideActions.find((action) => action.name === event.actionName);\n            }\n            acc.actionExecutionParentMessageId = event.parentMessageId;\n          } else if (event.type === RuntimeEventTypes.ActionExecutionArgs) {\n            acc.args += event.args;\n          }\n\n          acc.event = event;\n\n          return acc;\n        },\n        {\n          event: null,\n          callActionServerSide: false,\n          args: \"\",\n          actionExecutionId: null,\n          action: null,\n          actionExecutionParentMessageId: null,\n        } as RuntimeEventWithState,\n      ),\n      concatMap((eventWithState) => {\n        if (\n          eventWithState.event!.type === RuntimeEventTypes.ActionExecutionEnd &&\n          eventWithState.callActionServerSide\n        ) {\n          const toolCallEventStream$ = new RuntimeEventSubject();\n          executeAction(\n            toolCallEventStream$,\n            guardrailsResult$ ? guardrailsResult$ : null,\n            eventWithState.action!,\n            eventWithState.args,\n            eventWithState.actionExecutionParentMessageId,\n            eventWithState.actionExecutionId,\n            actionInputsWithoutAgents,\n            threadId,\n          ).catch((error) => {\n            console.error(error);\n          });\n\n          telemetry.capture(\"oss.runtime.server_action_executed\", {});\n          return concat(of(eventWithState.event!), toolCallEventStream$).pipe(\n            catchError((error) => {\n              console.error(\"Error in tool call stream\", error);\n              this.sendErrorMessageToChat();\n              return EMPTY;\n            }),\n          );\n        } else {\n          return of(eventWithState.event!);\n        }\n      }),\n    );\n  }\n}\n\nasync function executeAction(\n  eventStream$: RuntimeEventSubject,\n  guardrailsResult$: Subject<GuardrailsResult> | null,\n  action: Action<any>,\n  actionArguments: string,\n  actionExecutionParentMessageId: string | null,\n  actionExecutionId: string,\n  actionInputsWithoutAgents: ActionInput[],\n  threadId: string,\n) {\n  if (guardrailsResult$) {\n    const { status } = await firstValueFrom(guardrailsResult$);\n\n    if (status === \"denied\") {\n      eventStream$.complete();\n      return;\n    }\n  }\n\n  // Prepare arguments for function calling\n  let args: Record<string, any>[] = [];\n  if (actionArguments) {\n    try {\n      args = JSON.parse(actionArguments);\n    } catch (e) {\n      console.error(\"Action argument unparsable\", { actionArguments });\n      eventStream$.sendActionExecutionResult({\n        actionExecutionId,\n        actionName: action.name,\n        error: {\n          code: \"INVALID_ARGUMENTS\",\n          message: \"Failed to parse action arguments\",\n        },\n      });\n      return;\n    }\n  }\n\n  // handle LangGraph agents\n  if (isRemoteAgentAction(action)) {\n    const result = `${action.name} agent started`;\n\n    const agentExecution = plainToInstance(ActionExecutionMessage, {\n      id: actionExecutionId,\n      createdAt: new Date(),\n      name: action.name,\n      arguments: JSON.parse(actionArguments),\n      parentMessageId: actionExecutionParentMessageId ?? actionExecutionId,\n    });\n\n    const agentExecutionResult = plainToInstance(ResultMessage, {\n      id: \"result-\" + actionExecutionId,\n      createdAt: new Date(),\n      actionExecutionId,\n      actionName: action.name,\n      result,\n    });\n\n    eventStream$.sendActionExecutionResult({\n      actionExecutionId,\n      actionName: action.name,\n      result,\n    });\n\n    const stream = await action.remoteAgentHandler({\n      name: action.name,\n      threadId,\n      actionInputsWithoutAgents,\n      additionalMessages: [agentExecution, agentExecutionResult],\n    });\n\n    // forward to eventStream$\n    from(stream).subscribe({\n      next: (event) => eventStream$.next(event),\n      error: (err) => {\n        console.error(\"Error in stream\", err);\n        eventStream$.sendActionExecutionResult({\n          actionExecutionId,\n          actionName: action.name,\n          error: {\n            code: \"STREAM_ERROR\",\n            message: err.message,\n          },\n        });\n        eventStream$.complete();\n      },\n      complete: () => eventStream$.complete(),\n    });\n  } else {\n    // call the function\n    try {\n      const result = await action.handler?.(args);\n      await streamLangChainResponse({\n        result,\n        eventStream$,\n        actionExecution: {\n          name: action.name,\n          id: actionExecutionId,\n        },\n      });\n    } catch (e) {\n      console.error(\"Error in action handler\", e);\n      eventStream$.sendActionExecutionResult({\n        actionExecutionId,\n        actionName: action.name,\n        error: {\n          code: \"HANDLER_ERROR\",\n          message: e.message,\n        },\n      });\n      eventStream$.complete();\n    }\n  }\n}\n", "import { TelemetryClient } from \"@copilotkit/shared\";\nimport { EndpointType, LangGraphPlatformEndpoint } from \"./runtime/remote-actions\";\nimport { createHash } from \"node:crypto\";\nimport { CopilotRuntime, resolveEndpointType } from \"./runtime/copilot-runtime\";\nimport { RuntimeInstanceCreatedInfo } from \"@copilotkit/shared/src/telemetry/events\";\nconst packageJson = require(\"../../package.json\");\n\nconst telemetryClient = new TelemetryClient({\n  packageName: packageJson.name,\n  packageVersion: packageJson.version,\n});\n\nexport function getRuntimeInstanceTelemetryInfo(\n  runtime: CopilotRuntime,\n): RuntimeInstanceCreatedInfo {\n  const endpointsInfo = runtime.remoteEndpointDefinitions.reduce(\n    (acc, endpoint) => {\n      let info = { ...acc };\n\n      const endpointType = resolveEndpointType(endpoint);\n      if (!info.endpointTypes.includes(endpointType)) {\n        info = {\n          ...info,\n          endpointTypes: [...info.endpointTypes, endpointType],\n        };\n      }\n\n      if (endpointType === EndpointType.LangGraphPlatform) {\n        // When type is resolved, recreating a const with casting of type\n        const ep = endpoint as LangGraphPlatformEndpoint;\n        info = {\n          ...info,\n          agentsAmount: ep.agents.length,\n          hashedKey: ep.langsmithApiKey\n            ? createHash(\"sha256\").update(ep.langsmithApiKey).digest(\"hex\")\n            : null,\n        };\n      }\n\n      return info;\n    },\n    { endpointTypes: [], agentsAmount: null, hashedKey: null },\n  );\n\n  return {\n    actionsAmount: runtime.actions.length,\n    endpointsAmount: runtime.remoteEndpointDefinitions.length,\n    endpointTypes: endpointsInfo.endpointTypes,\n    agentsAmount: endpointsInfo.agentsAmount,\n    hashedLgcKey: endpointsInfo.hashedKey,\n  };\n}\n\nexport default telemetryClient;\n", "import { Action, CopilotKitErrorCode } from \"@copilotkit/shared\";\nimport { GraphQLContext } from \"../integrations/shared\";\nimport { Logger } from \"pino\";\nimport { Message } from \"../../graphql/types/converted\";\nimport { RuntimeEvent } from \"../../service-adapters/events\";\nimport { Observable } from \"rxjs\";\nimport { ActionInput } from \"../../graphql/inputs/action.input\";\nimport { AgentStateInput } from \"../../graphql/inputs/agent-state.input\";\nimport {\n  constructLGCRemoteAction,\n  constructRemoteActions,\n  createHeaders,\n} from \"./remote-action-constructors\";\nimport {\n  CopilotKitLowLevelError,\n  ResolvedCopilotKitError,\n  CopilotKitError,\n} from \"@copilotkit/shared\";\nimport { MetaEventInput } from \"../../graphql/inputs/meta-event.input\";\nimport { AbstractAgent } from \"@ag-ui/client\";\nimport { constructAgentWireRemoteAction } from \"./agentwire-action\";\n\nexport type EndpointDefinition = CopilotKitEndpoint | LangGraphPlatformEndpoint;\n\nexport enum EndpointType {\n  CopilotKit = \"copilotKit\",\n  LangGraphPlatform = \"langgraph-platform\",\n}\n\nexport interface BaseEndpointDefinition<TActionType extends EndpointType> {\n  type?: TActionType;\n}\n\nexport interface CopilotKitEndpoint extends BaseEndpointDefinition<EndpointType.CopilotKit> {\n  url: string;\n  onBeforeRequest?: ({ ctx }: { ctx: GraphQLContext }) => {\n    headers?: Record<string, string> | undefined;\n  };\n}\n\nexport interface LangGraphPlatformAgent {\n  name: string;\n  description: string;\n  assistantId?: string;\n}\n\nexport interface LangGraphPlatformEndpoint\n  extends BaseEndpointDefinition<EndpointType.LangGraphPlatform> {\n  deploymentUrl: string;\n  langsmithApiKey?: string | null;\n  agents: LangGraphPlatformAgent[];\n}\n\nexport type RemoteActionInfoResponse = {\n  actions: any[];\n  agents: any[];\n};\n\nexport type RemoteAgentHandlerParams = {\n  name: string;\n  actionInputsWithoutAgents: ActionInput[];\n  threadId?: string;\n  nodeName?: string;\n  additionalMessages?: Message[];\n  metaEvents?: MetaEventInput[];\n};\n\nexport type RemoteAgentAction = Action<any> & {\n  remoteAgentHandler: (params: RemoteAgentHandlerParams) => Promise<Observable<RuntimeEvent>>;\n};\n\nexport function isRemoteAgentAction(action: Action<any>): action is RemoteAgentAction {\n  if (!action) {\n    return false;\n  }\n  return typeof (action as RemoteAgentAction).remoteAgentHandler === \"function\";\n}\n\nasync function fetchRemoteInfo({\n  url,\n  onBeforeRequest,\n  graphqlContext,\n  logger,\n  frontendUrl,\n}: {\n  url: string;\n  onBeforeRequest?: CopilotKitEndpoint[\"onBeforeRequest\"];\n  graphqlContext: GraphQLContext;\n  logger: Logger;\n  frontendUrl?: string;\n}): Promise<RemoteActionInfoResponse> {\n  logger.debug({ url }, \"Fetching actions from url\");\n  const headers = createHeaders(onBeforeRequest, graphqlContext);\n\n  const fetchUrl = `${url}/info`;\n  try {\n    const response = await fetch(fetchUrl, {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify({ properties: graphqlContext.properties, frontendUrl }),\n    });\n\n    if (!response.ok) {\n      logger.error(\n        { url, status: response.status, body: await response.text() },\n        \"Failed to fetch actions from url\",\n      );\n      throw new ResolvedCopilotKitError({\n        status: response.status,\n        url: fetchUrl,\n        isRemoteEndpoint: true,\n      });\n    }\n\n    const json = await response.json();\n    logger.debug({ json }, \"Fetched actions from url\");\n    return json;\n  } catch (error) {\n    if (error instanceof CopilotKitError) {\n      throw error;\n    }\n    throw new CopilotKitLowLevelError({ error, url: fetchUrl });\n  }\n}\n\nexport async function setupRemoteActions({\n  remoteEndpointDefinitions,\n  graphqlContext,\n  messages,\n  agentStates,\n  frontendUrl,\n  agents,\n}: {\n  remoteEndpointDefinitions: EndpointDefinition[];\n  graphqlContext: GraphQLContext;\n  messages: Message[];\n  agentStates?: AgentStateInput[];\n  frontendUrl?: string;\n  agents: Record<string, AbstractAgent>;\n}): Promise<Action[]> {\n  const logger = graphqlContext.logger.child({ component: \"remote-actions.fetchRemoteActions\" });\n  logger.debug({ remoteEndpointDefinitions }, \"Fetching from remote endpoints\");\n\n  // Remove duplicates of remoteEndpointDefinitions.url\n  const filtered = remoteEndpointDefinitions.filter((value, index, self) => {\n    if (value.type === EndpointType.LangGraphPlatform) {\n      return value;\n    }\n    return index === self.findIndex((t: CopilotKitEndpoint) => t.url === value.url);\n  });\n\n  const result = await Promise.all(\n    filtered.map(async (endpoint) => {\n      // Check for properties that can distinguish LG platform from other actions\n      if (endpoint.type === EndpointType.LangGraphPlatform) {\n        return constructLGCRemoteAction({\n          endpoint,\n          messages,\n          graphqlContext,\n          logger: logger.child({\n            component: \"remote-actions.constructLGCRemoteAction\",\n            endpoint,\n          }),\n          agentStates,\n        });\n      }\n\n      const json = await fetchRemoteInfo({\n        url: endpoint.url,\n        onBeforeRequest: endpoint.onBeforeRequest,\n        graphqlContext,\n        logger: logger.child({ component: \"remote-actions.fetchActionsFromUrl\", endpoint }),\n        frontendUrl,\n      });\n\n      return constructRemoteActions({\n        json,\n        messages,\n        url: endpoint.url,\n        onBeforeRequest: endpoint.onBeforeRequest,\n        graphqlContext,\n        logger: logger.child({ component: \"remote-actions.constructActions\", endpoint }),\n        agentStates,\n      });\n    }),\n  );\n\n  for (const [key, agent] of Object.entries(agents)) {\n    if (agent.agentId !== undefined && agent.agentId !== key) {\n      throw new CopilotKitError({\n        message: `Agent ${key} has agentId ${agent.agentId} which does not match the key ${key}`,\n        code: CopilotKitErrorCode.UNKNOWN,\n      });\n    } else if (agent.agentId === undefined) {\n      agent.agentId = key;\n    }\n\n    result.push(\n      constructAgentWireRemoteAction({\n        logger,\n        messages,\n        agentStates,\n        agent: agent,\n      }),\n    );\n  }\n\n  return result.flat();\n}\n", "import { createHash } from \"node:crypto\";\nimport {\n  CopilotKitEndpoint,\n  RemoteAgentHandlerParams,\n  RemoteActionInfoResponse,\n  LangGraphPlatformEndpoint,\n} from \"./remote-actions\";\nimport { GraphQLContext } from \"../integrations\";\nimport { Logger } from \"pino\";\nimport { Message } from \"../../graphql/types/converted\";\nimport { AgentStateInput } from \"../../graphql/inputs/agent-state.input\";\nimport { Observable } from \"rxjs\";\nimport { RuntimeEvent, RuntimeEventSubject } from \"../../service-adapters/events\";\nimport telemetry from \"../telemetry-client\";\nimport { RemoteLangGraphEventSource } from \"../../agents/langgraph/event-source\";\nimport { Action } from \"@copilotkit/shared\";\nimport { execute } from \"./remote-lg-action\";\nimport { CopilotKitError, CopilotKitLowLevelError } from \"@copilotkit/shared\";\nimport { writeJsonLineResponseToEventStream } from \"../streaming\";\nimport { CopilotKitApiDiscoveryError, ResolvedCopilotKitError } from \"@copilotkit/shared\";\nimport { parseJson, tryMap } from \"@copilotkit/shared\";\nimport { ActionInput } from \"../../graphql/inputs/action.input\";\n\nexport function constructLGCRemoteAction({\n  endpoint,\n  graphqlContext,\n  logger,\n  messages,\n  agentStates,\n}: {\n  endpoint: LangGraphPlatformEndpoint;\n  graphqlContext: GraphQLContext;\n  logger: Logger;\n  messages: Message[];\n  agentStates?: AgentStateInput[];\n}) {\n  const agents = endpoint.agents.map((agent) => ({\n    name: agent.name,\n    description: agent.description,\n    parameters: [],\n    handler: async (_args: any) => {},\n    remoteAgentHandler: async ({\n      name,\n      actionInputsWithoutAgents,\n      threadId,\n      nodeName,\n      additionalMessages = [],\n      metaEvents,\n    }: RemoteAgentHandlerParams): Promise<Observable<RuntimeEvent>> => {\n      logger.debug({ actionName: agent.name }, \"Executing LangGraph Platform agent\");\n\n      telemetry.capture(\"oss.runtime.remote_action_executed\", {\n        agentExecution: true,\n        type: \"langgraph-platform\",\n        agentsAmount: endpoint.agents.length,\n        hashedLgcKey: endpoint.langsmithApiKey\n          ? createHash(\"sha256\").update(endpoint.langsmithApiKey).digest(\"hex\")\n          : null,\n      });\n\n      let state = {};\n      let config = {};\n      if (agentStates) {\n        const jsonState = agentStates.find((state) => state.agentName === name);\n        if (jsonState) {\n          state = parseJson(jsonState.state, {});\n          config = parseJson(jsonState.config, {});\n        }\n      }\n\n      try {\n        const response = await execute({\n          logger: logger.child({ component: \"remote-actions.remote-lg-action.streamEvents\" }),\n          deploymentUrl: endpoint.deploymentUrl,\n          langsmithApiKey: endpoint.langsmithApiKey,\n          agent,\n          threadId,\n          nodeName,\n          messages: [...messages, ...additionalMessages],\n          state,\n          config,\n          properties: graphqlContext.properties,\n          actions: tryMap(actionInputsWithoutAgents, (action: ActionInput) => ({\n            name: action.name,\n            description: action.description,\n            parameters: JSON.parse(action.jsonSchema),\n          })),\n          metaEvents,\n        });\n\n        const eventSource = new RemoteLangGraphEventSource();\n        writeJsonLineResponseToEventStream(response, eventSource.eventStream$);\n        return eventSource.processLangGraphEvents();\n      } catch (error) {\n        logger.error(\n          { url: endpoint.deploymentUrl, status: 500, body: error.message },\n          \"Failed to execute LangGraph Platform agent\",\n        );\n        throw new Error(\"Failed to execute LangGraph Platform agent\");\n      }\n    },\n  }));\n\n  return [...agents];\n}\n\nexport enum RemoteAgentType {\n  LangGraph = \"langgraph\",\n  CrewAI = \"crewai\",\n}\n\nexport function constructRemoteActions({\n  json,\n  url,\n  onBeforeRequest,\n  graphqlContext,\n  logger,\n  messages,\n  agentStates,\n}: {\n  json: RemoteActionInfoResponse;\n  url: string;\n  onBeforeRequest?: CopilotKitEndpoint[\"onBeforeRequest\"];\n  graphqlContext: GraphQLContext;\n  logger: Logger;\n  messages: Message[];\n  agentStates?: AgentStateInput[];\n}): Action<any>[] {\n  const totalAgents = Array.isArray(json[\"agents\"]) ? json[\"agents\"].length : 0;\n\n  const actions = json[\"actions\"].map((action) => ({\n    name: action.name,\n    description: action.description,\n    parameters: action.parameters,\n    handler: async (args: any) => {\n      logger.debug({ actionName: action.name, args }, \"Executing remote action\");\n\n      const headers = createHeaders(onBeforeRequest, graphqlContext);\n      telemetry.capture(\"oss.runtime.remote_action_executed\", {\n        agentExecution: false,\n        type: \"self-hosted\",\n        agentsAmount: totalAgents,\n      });\n\n      const fetchUrl = `${url}/actions/execute`;\n      try {\n        const response = await fetch(fetchUrl, {\n          method: \"POST\",\n          headers,\n          body: JSON.stringify({\n            name: action.name,\n            arguments: args,\n            properties: graphqlContext.properties,\n          }),\n        });\n\n        if (!response.ok) {\n          logger.error(\n            { url, status: response.status, body: await response.text() },\n            \"Failed to execute remote action\",\n          );\n          if (response.status === 404) {\n            throw new CopilotKitApiDiscoveryError({ url: fetchUrl });\n          }\n          throw new ResolvedCopilotKitError({\n            status: response.status,\n            url: fetchUrl,\n            isRemoteEndpoint: true,\n          });\n        }\n\n        const requestResult = await response.json();\n\n        const result = requestResult[\"result\"];\n        logger.debug({ actionName: action.name, result }, \"Executed remote action\");\n        return result;\n      } catch (error) {\n        if (error instanceof CopilotKitError) {\n          throw error;\n        }\n        throw new CopilotKitLowLevelError({ error, url: fetchUrl });\n      }\n    },\n  }));\n\n  const agents = totalAgents\n    ? json[\"agents\"].map((agent) => ({\n        name: agent.name,\n        description: agent.description,\n        parameters: [],\n        handler: async (_args: any) => {},\n\n        remoteAgentHandler: async ({\n          name,\n          actionInputsWithoutAgents,\n          threadId,\n          nodeName,\n          additionalMessages = [],\n          metaEvents,\n        }: RemoteAgentHandlerParams): Promise<Observable<RuntimeEvent>> => {\n          logger.debug({ actionName: agent.name }, \"Executing remote agent\");\n\n          const headers = createHeaders(onBeforeRequest, graphqlContext);\n          telemetry.capture(\"oss.runtime.remote_action_executed\", {\n            agentExecution: true,\n            type: \"self-hosted\",\n            agentsAmount: json[\"agents\"].length,\n          });\n\n          let state = {};\n          let config = {};\n          if (agentStates) {\n            const jsonState = agentStates.find((state) => state.agentName === name);\n            if (jsonState) {\n              state = parseJson(jsonState.state, {});\n              config = parseJson(jsonState.config, {});\n            }\n          }\n\n          const fetchUrl = `${url}/agents/execute`;\n          try {\n            const response = await fetch(fetchUrl, {\n              method: \"POST\",\n              headers,\n              body: JSON.stringify({\n                name,\n                threadId,\n                nodeName,\n                messages: [...messages, ...additionalMessages],\n                state,\n                config,\n                properties: graphqlContext.properties,\n                actions: tryMap(actionInputsWithoutAgents, (action: ActionInput) => ({\n                  name: action.name,\n                  description: action.description,\n                  parameters: JSON.parse(action.jsonSchema),\n                })),\n                metaEvents,\n              }),\n            });\n\n            if (!response.ok) {\n              logger.error(\n                { url, status: response.status, body: await response.text() },\n                \"Failed to execute remote agent\",\n              );\n              if (response.status === 404) {\n                throw new CopilotKitApiDiscoveryError({ url: fetchUrl });\n              }\n              throw new ResolvedCopilotKitError({\n                status: response.status,\n                url: fetchUrl,\n                isRemoteEndpoint: true,\n              });\n            }\n\n            if (agent.type === RemoteAgentType.LangGraph) {\n              const eventSource = new RemoteLangGraphEventSource();\n              writeJsonLineResponseToEventStream(response.body!, eventSource.eventStream$);\n              return eventSource.processLangGraphEvents();\n            } else if (agent.type === RemoteAgentType.CrewAI) {\n              const eventStream$ = new RuntimeEventSubject();\n              writeJsonLineResponseToEventStream(response.body!, eventStream$);\n              return eventStream$;\n            } else {\n              throw new Error(\"Unsupported agent type\");\n            }\n          } catch (error) {\n            if (error instanceof CopilotKitError) {\n              throw error;\n            }\n            throw new CopilotKitLowLevelError({ error, url: fetchUrl });\n          }\n        },\n      }))\n    : [];\n\n  return [...actions, ...agents];\n}\n\nexport function createHeaders(\n  onBeforeRequest: CopilotKitEndpoint[\"onBeforeRequest\"],\n  graphqlContext: GraphQLContext,\n) {\n  const headers = {\n    \"Content-Type\": \"application/json\",\n  };\n\n  if (onBeforeRequest) {\n    const { headers: additionalHeaders } = onBeforeRequest({ ctx: graphqlContext });\n    if (additionalHeaders) {\n      Object.assign(headers, additionalHeaders);\n    }\n  }\n\n  return headers;\n}\n", "import { catchError, mergeMap, ReplaySubject, scan } from \"rxjs\";\nimport { CustomEventNames, LangGraphEvent, LangGraphEventTypes } from \"./events\";\nimport {\n  RuntimeEvent,\n  RuntimeEventTypes,\n  RuntimeMetaEventName,\n} from \"../../service-adapters/events\";\nimport { randomId } from \"@copilotkit/shared\";\n\ninterface LangGraphEventWithState {\n  event: LangGraphEvent | null;\n\n  isMessageStart: boolean;\n  isMessageEnd: boolean;\n  isToolCallStart: boolean;\n  isToolCallEnd: boolean;\n  isToolCall: boolean;\n\n  lastMessageId: string | null;\n  lastToolCallId: string | null;\n  lastToolCallName: string | null;\n  currentContent: string | null;\n  processedToolCallIds: Set<string>;\n}\n\nexport class RemoteLangGraphEventSource {\n  public eventStream$ = new ReplaySubject<LangGraphEvent>();\n\n  private shouldEmitToolCall(\n    shouldEmitToolCalls: string | string[] | boolean,\n    toolCallName: string,\n  ) {\n    if (typeof shouldEmitToolCalls === \"boolean\") {\n      return shouldEmitToolCalls;\n    }\n    if (Array.isArray(shouldEmitToolCalls)) {\n      return shouldEmitToolCalls.includes(toolCallName);\n    }\n    return shouldEmitToolCalls === toolCallName;\n  }\n\n  private getCurrentContent(event: LangGraphEvent) {\n    // @ts-expect-error -- LangGraph Platform implementation stores data outside of kwargs\n    const content = event.data?.chunk?.kwargs?.content ?? event.data?.chunk?.content;\n\n    if (!content) {\n      const toolCallChunks = this.getCurrentToolCallChunks(event) ?? [];\n      for (const chunk of toolCallChunks) {\n        if (chunk.args) {\n          return chunk.args;\n        }\n      }\n    }\n\n    if (typeof content === \"string\") {\n      return content;\n    } else if (Array.isArray(content) && content.length > 0) {\n      return content[0].text;\n    }\n\n    return null;\n  }\n\n  private getCurrentMessageId(event: LangGraphEvent) {\n    // @ts-expect-error -- LangGraph Platform implementation stores data outside of kwargs\n    return event.data?.chunk?.kwargs?.id ?? event.data?.chunk?.id;\n  }\n\n  private getCurrentToolCallChunks(event: LangGraphEvent) {\n    // @ts-expect-error -- LangGraph Platform implementation stores data outside of kwargs\n    return event.data?.chunk?.kwargs?.tool_call_chunks ?? event.data?.chunk?.tool_call_chunks;\n  }\n\n  private getResponseMetadata(event: LangGraphEvent) {\n    // @ts-expect-error -- LangGraph Platform implementation stores data outside of kwargs\n    return event.data?.chunk?.kwargs?.response_metadata ?? event.data?.chunk?.response_metadata;\n  }\n\n  processLangGraphEvents() {\n    let lastEventWithState: LangGraphEventWithState | null = null;\n\n    return this.eventStream$.pipe(\n      scan(\n        (acc, event) => {\n          if (event.event === LangGraphEventTypes.OnChatModelStream) {\n            const prevMessageId = acc.lastMessageId;\n            acc.currentContent = this.getCurrentContent(event);\n            acc.lastMessageId = this.getCurrentMessageId(event) ?? acc.lastMessageId;\n            const toolCallChunks = this.getCurrentToolCallChunks(event) ?? [];\n            const responseMetadata = this.getResponseMetadata(event);\n            // Check if a given event is a tool call\n            const toolCallCheck = toolCallChunks && toolCallChunks.length > 0;\n            let isToolCallEnd = responseMetadata?.finish_reason === \"tool_calls\";\n\n            acc.isToolCallStart = toolCallChunks.some((chunk: any) => chunk.name && chunk.id);\n            acc.isMessageStart = prevMessageId !== acc.lastMessageId && !acc.isToolCallStart;\n\n            let previousRoundHadToolCall = acc.isToolCall;\n            acc.isToolCall = toolCallCheck;\n            // Previous \"acc.isToolCall\" was set but now it won't pass the check, it means the tool call just ended.\n            if (previousRoundHadToolCall && !toolCallCheck) {\n              isToolCallEnd = true;\n            }\n            acc.isToolCallEnd = isToolCallEnd;\n            acc.isMessageEnd = responseMetadata?.finish_reason === \"stop\";\n            ({ name: acc.lastToolCallName, id: acc.lastToolCallId } = toolCallChunks.find(\n              (chunk: any) => chunk.name && chunk.id,\n            ) ?? { name: acc.lastToolCallName, id: acc.lastToolCallId });\n          }\n          acc.event = event;\n          lastEventWithState = acc; // Capture the state\n          return acc;\n        },\n        {\n          event: null,\n          isMessageStart: false,\n          isMessageEnd: false,\n          isToolCallStart: false,\n          isToolCallEnd: false,\n          isToolCall: false,\n          lastMessageId: null,\n          lastToolCallId: null,\n          lastToolCallName: null,\n          currentContent: null,\n          processedToolCallIds: new Set<string>(),\n        } as LangGraphEventWithState,\n      ),\n      mergeMap((acc): RuntimeEvent[] => {\n        const events: RuntimeEvent[] = [];\n\n        let shouldEmitMessages = true;\n        let shouldEmitToolCalls: string | string[] | boolean = true;\n\n        if (acc.event.event == LangGraphEventTypes.OnChatModelStream) {\n          if (\"copilotkit:emit-tool-calls\" in (acc.event.metadata || {})) {\n            shouldEmitToolCalls = acc.event.metadata[\"copilotkit:emit-tool-calls\"];\n          }\n          if (\"copilotkit:emit-messages\" in (acc.event.metadata || {})) {\n            shouldEmitMessages = acc.event.metadata[\"copilotkit:emit-messages\"];\n          }\n        }\n\n        if (acc.event.event === LangGraphEventTypes.OnInterrupt) {\n          events.push({\n            type: RuntimeEventTypes.MetaEvent,\n            name: RuntimeMetaEventName.LangGraphInterruptEvent,\n            value: acc.event.value,\n          });\n        }\n        if (acc.event.event === LangGraphEventTypes.OnCopilotKitInterrupt) {\n          events.push({\n            type: RuntimeEventTypes.MetaEvent,\n            name: RuntimeMetaEventName.CopilotKitLangGraphInterruptEvent,\n            data: acc.event.data,\n          });\n        }\n\n        const responseMetadata = this.getResponseMetadata(acc.event);\n\n        // Tool call ended: emit ActionExecutionEnd\n        if (\n          acc.isToolCallEnd &&\n          this.shouldEmitToolCall(shouldEmitToolCalls, acc.lastToolCallName) &&\n          acc.lastToolCallId &&\n          !acc.processedToolCallIds.has(acc.lastToolCallId)\n        ) {\n          acc.processedToolCallIds.add(acc.lastToolCallId);\n\n          events.push({\n            type: RuntimeEventTypes.ActionExecutionEnd,\n            actionExecutionId: acc.lastToolCallId,\n          });\n        }\n\n        // Message ended: emit TextMessageEnd\n        else if (responseMetadata?.finish_reason === \"stop\" && shouldEmitMessages) {\n          events.push({\n            type: RuntimeEventTypes.TextMessageEnd,\n            messageId: acc.lastMessageId,\n          });\n        }\n\n        switch (acc.event!.event) {\n          //\n          // Custom events\n          //\n          case LangGraphEventTypes.OnCustomEvent:\n            //\n            // Manually emit a message\n            //\n            if (acc.event.name === CustomEventNames.CopilotKitManuallyEmitMessage) {\n              events.push({\n                type: RuntimeEventTypes.TextMessageStart,\n                messageId: acc.event.data.message_id,\n              });\n              events.push({\n                type: RuntimeEventTypes.TextMessageContent,\n                messageId: acc.event.data.message_id,\n                content: acc.event.data.message,\n              });\n              events.push({\n                type: RuntimeEventTypes.TextMessageEnd,\n                messageId: acc.event.data.message_id,\n              });\n            }\n            //\n            // Manually emit a tool call\n            //\n            else if (acc.event.name === CustomEventNames.CopilotKitManuallyEmitToolCall) {\n              events.push({\n                type: RuntimeEventTypes.ActionExecutionStart,\n                actionExecutionId: acc.event.data.id,\n                actionName: acc.event.data.name,\n                parentMessageId: acc.event.data.id,\n              });\n              events.push({\n                type: RuntimeEventTypes.ActionExecutionArgs,\n                actionExecutionId: acc.event.data.id,\n                args: JSON.stringify(acc.event.data.args),\n              });\n              events.push({\n                type: RuntimeEventTypes.ActionExecutionEnd,\n                actionExecutionId: acc.event.data.id,\n              });\n            }\n            break;\n          case LangGraphEventTypes.OnCopilotKitStateSync:\n            events.push({\n              type: RuntimeEventTypes.AgentStateMessage,\n              threadId: acc.event.thread_id,\n              role: acc.event.role,\n              agentName: acc.event.agent_name,\n              nodeName: acc.event.node_name,\n              runId: acc.event.run_id,\n              active: acc.event.active,\n              state: JSON.stringify(acc.event.state),\n              running: acc.event.running,\n            });\n            break;\n          case LangGraphEventTypes.OnChatModelStream:\n            if (\n              acc.isToolCallStart &&\n              this.shouldEmitToolCall(shouldEmitToolCalls, acc.lastToolCallName)\n            ) {\n              events.push({\n                type: RuntimeEventTypes.ActionExecutionStart,\n                actionExecutionId: acc.lastToolCallId,\n                actionName: acc.lastToolCallName,\n                parentMessageId: acc.lastMessageId,\n              });\n            }\n            // Message started: emit TextMessageStart\n            else if (acc.isMessageStart && shouldEmitMessages) {\n              acc.processedToolCallIds.clear();\n              events.push({\n                type: RuntimeEventTypes.TextMessageStart,\n                messageId: acc.lastMessageId,\n              });\n            }\n\n            // Tool call args: emit ActionExecutionArgs\n            if (\n              acc.isToolCall &&\n              acc.currentContent &&\n              this.shouldEmitToolCall(shouldEmitToolCalls, acc.lastToolCallName)\n            ) {\n              events.push({\n                type: RuntimeEventTypes.ActionExecutionArgs,\n                actionExecutionId: acc.lastToolCallId,\n                args: acc.currentContent,\n              });\n            }\n            // Message content: emit TextMessageContent\n            else if (!acc.isToolCall && acc.currentContent && shouldEmitMessages) {\n              events.push({\n                type: RuntimeEventTypes.TextMessageContent,\n                messageId: acc.lastMessageId,\n                content: acc.currentContent,\n              });\n            }\n            break;\n        }\n        return events;\n      }),\n      catchError((error) => {\n        console.error(error);\n        const events: RuntimeEvent[] = [];\n\n        if (lastEventWithState?.lastMessageId && !lastEventWithState.isToolCall) {\n          events.push({\n            type: RuntimeEventTypes.TextMessageEnd,\n            messageId: lastEventWithState.lastMessageId,\n          });\n        }\n        if (lastEventWithState?.lastToolCallId) {\n          events.push({\n            type: RuntimeEventTypes.ActionExecutionEnd,\n            actionExecutionId: lastEventWithState.lastToolCallId,\n          });\n        }\n\n        const messageId = randomId();\n\n        events.push({\n          type: RuntimeEventTypes.TextMessageStart,\n          messageId: messageId,\n        });\n        events.push({\n          type: RuntimeEventTypes.TextMessageContent,\n          messageId: messageId,\n          content: \"❌ An error occurred. Please try again.\",\n        });\n        events.push({\n          type: RuntimeEventTypes.TextMessageEnd,\n          messageId: messageId,\n        });\n\n        return events;\n      }),\n    );\n  }\n}\n", "import { ActionExecutionMessage, ResultMessage, TextMessage } from \"../../graphql/types/converted\";\n\nexport enum LangGraphEventTypes {\n  OnChainStart = \"on_chain_start\",\n  OnChainStream = \"on_chain_stream\",\n  OnChainEnd = \"on_chain_end\",\n  OnChatModelStart = \"on_chat_model_start\",\n  OnChatModelStream = \"on_chat_model_stream\",\n  OnChatModelEnd = \"on_chat_model_end\",\n  OnToolStart = \"on_tool_start\",\n  OnToolEnd = \"on_tool_end\",\n  OnCopilotKitStateSync = \"on_copilotkit_state_sync\",\n  OnCopilotKitEmitMessage = \"on_copilotkit_emit_message\",\n  OnCopilotKitEmitToolCall = \"on_copilotkit_emit_tool_call\",\n  OnCustomEvent = \"on_custom_event\",\n  OnInterrupt = \"on_interrupt\",\n  OnCopilotKitInterrupt = \"on_copilotkit_interrupt\",\n}\n\nexport enum MetaEventNames {\n  LangGraphInterruptEvent = \"LangGraphInterruptEvent\",\n  CopilotKitLangGraphInterruptEvent = \"CopilotKitLangGraphInterruptEvent\",\n}\n\nexport enum CustomEventNames {\n  CopilotKitManuallyEmitMessage = \"copilotkit_manually_emit_message\",\n  CopilotKitManuallyEmitToolCall = \"copilotkit_manually_emit_tool_call\",\n  CopilotKitManuallyEmitIntermediateState = \"copilotkit_manually_emit_intermediate_state\",\n  CopilotKitExit = \"copilotkit_exit\",\n}\n\ntype LangGraphOnCopilotKitStateSyncEvent = {\n  event: LangGraphEventTypes.OnCopilotKitStateSync;\n  thread_id: string;\n  agent_name: string;\n  node_name: string;\n  run_id: string;\n  active: boolean;\n  role: string;\n  state: any;\n  running: boolean;\n};\n\ntype LangGraphOnChainStartEvent = {\n  event: LangGraphEventTypes.OnChainStart;\n  run_id: string;\n  name: string;\n  tags: string[];\n  metadata: { thread_id: string };\n  data: {\n    input: any;\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnChainEndEvent = {\n  event: LangGraphEventTypes.OnChainEnd;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_task_idx: number;\n    thread_ts: string;\n  };\n  data: {\n    input: any;\n    output: any;\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnChatModelStartEvent = {\n  event: LangGraphEventTypes.OnChatModelStart;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_task_idx: number;\n    thread_ts: string;\n    ls_provider: string;\n    ls_model_name: string;\n    ls_model_type: string;\n    ls_temperature: number;\n  };\n  data: {\n    input: {\n      messages: {\n        lc: number;\n        type: string;\n        id: string[];\n        kwargs: {\n          content: string;\n          type: string;\n          id: string;\n        };\n      }[][];\n    };\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnChatModelStreamEvent = {\n  event: LangGraphEventTypes.OnChatModelStream;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_task_idx: number;\n    thread_ts: string;\n    ls_provider: string;\n    ls_model_name: string;\n    ls_model_type: string;\n    ls_temperature: number;\n  };\n  data: {\n    chunk: {\n      lc: number;\n      type: string;\n      id: string;\n      kwargs: {\n        content: string | { text: string; type: string; index: number }[];\n        additional_kwargs: {\n          tool_calls: {\n            index: number;\n            id: string;\n            function: { arguments: string; name: string };\n            type: string;\n          }[];\n        };\n        type: string;\n        id: string;\n        tool_calls: { name: string; args: {}; id: string; type: string }[];\n        tool_call_chunks: {\n          name: string;\n          args: string;\n          id: string;\n          index: number;\n          type: string;\n        }[];\n        invalid_tool_calls: any[];\n      };\n    };\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnChatModelEndEvent = {\n  event: LangGraphEventTypes.OnChatModelEnd;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_task_idx: number;\n    thread_ts: string;\n    ls_provider: string;\n    ls_model_name: string;\n    ls_model_type: string;\n    ls_temperature: number;\n  };\n  data: {\n    input: any;\n    output: {\n      generations: {\n        text: string;\n        generation_info: {\n          finish_reason: string;\n          model_name: string;\n          system_fingerprint: string;\n        };\n        type: string;\n        message: {\n          lc: number;\n          type: string;\n          id: string[];\n          kwargs: {\n            content: string;\n            additional_kwargs: {\n              tool_calls: {\n                index: number;\n                id: string;\n                function: { arguments: string; name: string };\n                type: string;\n              }[];\n            };\n            response_metadata: {\n              finish_reason: string;\n              model_name: string;\n              system_fingerprint: string;\n            };\n            type: string;\n            id: string;\n            tool_calls: { name: string; args: { query: string }; id: string; type: string }[];\n            invalid_tool_calls: any[];\n          };\n        };\n      }[][];\n      llm_output: any;\n      run: any;\n    };\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnChainStreamEvent = {\n  event: LangGraphEventTypes.OnChainStream;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step?: number;\n    langgraph_node?: string;\n    langgraph_triggers?: string[];\n    langgraph_task_idx?: number;\n    thread_ts?: string;\n  };\n  data: {\n    chunk: {\n      messages: {\n        lc: number;\n        type: string;\n        id: string[];\n        kwargs: {\n          content: string;\n          additional_kwargs?: {\n            tool_calls?: {\n              index: number;\n              id: string;\n              function: { arguments: string; name: string };\n              type: string;\n            }[];\n          };\n          response_metadata?: {\n            finish_reason: string;\n            model_name: string;\n            system_fingerprint: string;\n          };\n          type: string;\n          id: string;\n          tool_calls?: { name: string; args: { query: string }; id: string; type: string }[];\n          invalid_tool_calls?: any[];\n        };\n      }[];\n    };\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnToolStartEvent = {\n  event: LangGraphEventTypes.OnToolStart;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_task_idx: number;\n    thread_ts: string;\n  };\n  data: {\n    input: {\n      query: string;\n    };\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnToolEndEvent = {\n  event: LangGraphEventTypes.OnToolEnd;\n  name: string;\n  run_id: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_task_idx: number;\n    thread_ts: string;\n  };\n  data: {\n    input: {\n      query: string;\n    };\n    output: {\n      lc: number;\n      type: string;\n      id: string[];\n      kwargs: {\n        content: string[];\n        type: string;\n        name: string;\n        tool_call_id: string;\n        status: string;\n      };\n    };\n  };\n  parent_ids: string[];\n};\n\ntype LangGraphOnCustomEvent = {\n  event: LangGraphEventTypes.OnCustomEvent;\n  run_id: string;\n  name: string;\n  tags: string[];\n  metadata: {\n    thread_id: string;\n    langgraph_step: number;\n    langgraph_node: string;\n    langgraph_triggers: string[];\n    langgraph_path: [string, string];\n    langgraph_checkpoint_ns: string;\n    checkpoint_ns: string;\n  };\n  data: any;\n  parent_ids: string[];\n};\n\ninterface LangGraphInterruptEvent {\n  event: LangGraphEventTypes.OnInterrupt;\n  value: string;\n}\n\ninterface CopilotKitLangGraphInterruptEvent {\n  event: LangGraphEventTypes.OnCopilotKitInterrupt;\n  data: { value: string; messages: (TextMessage | ActionExecutionMessage | ResultMessage)[] };\n}\n\nexport type LangGraphEvent =\n  | LangGraphOnChainStartEvent\n  | LangGraphOnChainStreamEvent\n  | LangGraphOnChainEndEvent\n  | LangGraphOnChatModelStartEvent\n  | LangGraphOnChatModelStreamEvent\n  | LangGraphOnChatModelEndEvent\n  | LangGraphOnToolStartEvent\n  | LangGraphOnToolEndEvent\n  | LangGraphOnCopilotKitStateSyncEvent\n  | LangGraphOnCustomEvent\n  | LangGraphInterruptEvent\n  | CopilotKitLangGraphInterruptEvent;\n", "import {\n  Client as LangGraphClient,\n  EventsStreamEvent,\n  GraphSchema,\n  StreamMode,\n} from \"@langchain/langgraph-sdk\";\nimport { createHash } from \"node:crypto\";\nimport { isValidUUID, randomUUID } from \"@copilotkit/shared\";\nimport { parse as parsePartialJson } from \"partial-json\";\nimport { Logger } from \"pino\";\nimport { ActionInput } from \"../../graphql/inputs/action.input\";\nimport { LangGraphPlatformAgent, LangGraphPlatformEndpoint } from \"./remote-actions\";\nimport { CopilotRequestContextProperties } from \"../integrations\";\nimport { ActionExecutionMessage, Message, MessageType } from \"../../graphql/types/converted\";\nimport { MessageRole } from \"../../graphql/types/enums\";\nimport { CustomEventNames, LangGraphEventTypes } from \"../../agents/langgraph/events\";\nimport telemetry from \"../telemetry-client\";\nimport { MetaEventInput } from \"../../graphql/inputs/meta-event.input\";\nimport { MetaEventName } from \"../../graphql/types/meta-events.type\";\nimport { parseJson, CopilotKitMisuseError } from \"@copilotkit/shared\";\nimport { RemoveMessage } from \"@langchain/core/messages\";\n\ntype State = Record<string, any>;\n\ntype ExecutionAction = Pick<ActionInput, \"name\" | \"description\"> & { parameters: string };\n\ninterface ExecutionArgs extends Omit<LangGraphPlatformEndpoint, \"agents\"> {\n  agent: LangGraphPlatformAgent;\n  threadId: string;\n  nodeName: string;\n  messages: Message[];\n  state: State;\n  config?: {\n    configurable?: Record<string, any>;\n    [key: string]: any;\n  };\n  properties: CopilotRequestContextProperties;\n  actions: ExecutionAction[];\n  logger: Logger;\n  metaEvents?: MetaEventInput[];\n}\n\n// The following types are our own definition to the messages accepted by LangGraph Platform, enhanced with some of our extra data.\ninterface ToolCall {\n  id: string;\n  name: string;\n  args: Record<string, unknown>;\n}\n\ntype BaseLangGraphPlatformMessage = Omit<\n  Message,\n  | \"isResultMessage\"\n  | \"isTextMessage\"\n  | \"isImageMessage\"\n  | \"isActionExecutionMessage\"\n  | \"isAgentStateMessage\"\n  | \"type\"\n  | \"createdAt\"\n> & {\n  content: string;\n  role: MessageRole;\n  additional_kwargs?: Record<string, unknown>;\n  type: MessageType;\n};\n\ninterface LangGraphPlatformResultMessage extends BaseLangGraphPlatformMessage {\n  tool_call_id: string;\n  name: string;\n}\n\ninterface LangGraphPlatformActionExecutionMessage extends BaseLangGraphPlatformMessage {\n  tool_calls: ToolCall[];\n}\n\ntype LangGraphPlatformMessage =\n  | LangGraphPlatformActionExecutionMessage\n  | LangGraphPlatformResultMessage\n  | BaseLangGraphPlatformMessage;\n\ntype SchemaKeys = {\n  input: string[] | null;\n  output: string[] | null;\n  config: string[] | null;\n} | null;\n\nlet activeInterruptEvent = false;\n\nexport async function execute(args: ExecutionArgs): Promise<ReadableStream<Uint8Array>> {\n  return new ReadableStream({\n    async start(controller) {\n      try {\n        await streamEvents(controller, args);\n        controller.close();\n      } catch (err) {\n        // Unwrap the possible cause\n        const cause = err?.cause;\n\n        // Check code directly if it exists\n        const errorCode = cause?.code || err?.code;\n\n        if (errorCode === \"ECONNREFUSED\") {\n          throw new CopilotKitMisuseError({\n            message: `\n              The LangGraph client could not connect to the graph. Please further check previous logs, which includes further details.\n              \n              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`,\n          });\n        } else {\n          throw new CopilotKitMisuseError({\n            message: `\n              The LangGraph client threw unhandled error ${err}.\n              \n              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`,\n          });\n        }\n      }\n    },\n  });\n}\n\nasync function streamEvents(controller: ReadableStreamDefaultController, args: ExecutionArgs) {\n  const {\n    deploymentUrl,\n    langsmithApiKey,\n    threadId: argsInitialThreadId,\n    agent,\n    nodeName: initialNodeName,\n    state: initialState,\n    config: explicitConfig,\n    messages,\n    actions,\n    logger,\n    properties,\n    metaEvents,\n  } = args;\n\n  let nodeName = initialNodeName;\n  let state = initialState;\n  const { name, assistantId: initialAssistantId } = agent;\n\n  const propertyHeaders = properties.authorization\n    ? { authorization: `Bearer ${properties.authorization}` }\n    : null;\n\n  const client = new LangGraphClient({\n    apiUrl: deploymentUrl,\n    apiKey: langsmithApiKey,\n    defaultHeaders: { ...propertyHeaders },\n  });\n\n  let threadId = argsInitialThreadId ?? randomUUID();\n  if (argsInitialThreadId && argsInitialThreadId.startsWith(\"ck-\")) {\n    threadId = argsInitialThreadId.substring(3);\n  }\n\n  if (!isValidUUID(threadId)) {\n    console.warn(\n      `Cannot use the threadId ${threadId} with LangGraph Platform. Must be a valid UUID.`,\n    );\n  }\n\n  let wasInitiatedWithExistingThread = true;\n  try {\n    await client.threads.get(threadId);\n  } catch (error) {\n    wasInitiatedWithExistingThread = false;\n    await client.threads.create({ threadId });\n  }\n\n  let agentState = { values: {} };\n  if (wasInitiatedWithExistingThread) {\n    agentState = await client.threads.getState(threadId);\n  }\n\n  const agentStateValues = agentState.values as State;\n  state.messages = agentStateValues.messages;\n  const mode =\n    threadId && nodeName != \"__end__\" && nodeName != undefined && nodeName != null\n      ? \"continue\"\n      : \"start\";\n  let formattedMessages = [];\n  try {\n    formattedMessages = copilotkitMessagesToLangChain(messages);\n  } catch (e) {\n    logger.error(e, `Error event thrown: ${e.message}`);\n  }\n  state = langGraphDefaultMergeState(state, formattedMessages, actions, name);\n\n  const streamInput = mode === \"start\" ? state : null;\n\n  const payload = {\n    input: streamInput,\n    streamMode: [\"events\", \"values\", \"updates\"] satisfies StreamMode[],\n    command: undefined,\n  };\n\n  const lgInterruptMetaEvent = metaEvents?.find(\n    (ev) => ev.name === MetaEventName.LangGraphInterruptEvent,\n  );\n  if (activeInterruptEvent && !lgInterruptMetaEvent) {\n    // state.messages includes only messages that were not processed by the agent, which are the interrupt messages\n    payload.command = { resume: state.messages };\n  }\n  if (lgInterruptMetaEvent?.response) {\n    let response = lgInterruptMetaEvent.response;\n    payload.command = { resume: parseJson(response, response) };\n  }\n\n  if (mode === \"continue\" && !activeInterruptEvent) {\n    await client.threads.updateState(threadId, { values: state, asNode: nodeName });\n  }\n\n  let streamInfo: {\n    provider?: string;\n    langGraphHost?: string;\n    langGraphVersion?: string;\n    hashedLgcKey?: string | null;\n  } = {\n    hashedLgcKey: langsmithApiKey\n      ? createHash(\"sha256\").update(langsmithApiKey).digest(\"hex\")\n      : null,\n  };\n\n  const assistants = await client.assistants.search();\n  const retrievedAssistant = assistants.find(\n    (a) => a.name === name || a.assistant_id === initialAssistantId,\n  );\n  if (!retrievedAssistant) {\n    telemetry.capture(\"oss.runtime.agent_execution_stream_errored\", {\n      ...streamInfo,\n      error: `Found no assistants for given information, while ${assistants.length} assistants exists`,\n    });\n    console.error(`\n      No agent found for the agent name specified in CopilotKit provider\n      Please check your available agents or provide an agent ID in the LangGraph Platform endpoint definition.\\n\n      \n      These are the available agents: [${assistants.map((a) => `${a.name} (ID: ${a.assistant_id})`).join(\", \")}]\n      `);\n    throw new Error(\"No agent id found\");\n  }\n  const assistantId = retrievedAssistant.assistant_id;\n\n  const graphInfo = await client.assistants.getGraph(assistantId);\n  const graphSchema = await client.assistants.getSchemas(assistantId);\n  const schemaKeys = getSchemaKeys(graphSchema);\n\n  if (explicitConfig) {\n    let filteredConfigurable = retrievedAssistant.config.configurable;\n    if (explicitConfig.configurable) {\n      filteredConfigurable = schemaKeys?.config\n        ? filterObjectBySchemaKeys(explicitConfig?.configurable, schemaKeys?.config)\n        : explicitConfig?.configurable;\n    }\n\n    const newConfig = {\n      ...retrievedAssistant.config,\n      ...explicitConfig,\n      configurable: filteredConfigurable,\n    };\n\n    // LG does not return recursion limit if it's the default, therefore we check: if no recursion limit is currently set, and the user asked for 25, there is no change.\n    const isRecursionLimitSetToDefault =\n      retrievedAssistant.config.recursion_limit == null && explicitConfig.recursion_limit === 25;\n    // Deep compare configs to avoid unnecessary update calls\n    const configsAreDifferent =\n      JSON.stringify(newConfig) !== JSON.stringify(retrievedAssistant.config);\n\n    // Check if the only difference is the recursion_limit being set to default\n    const isOnlyRecursionLimitDifferent =\n      isRecursionLimitSetToDefault &&\n      JSON.stringify({ ...newConfig, recursion_limit: null }) ===\n        JSON.stringify({ ...retrievedAssistant.config, recursion_limit: null });\n\n    // If configs are different, we further check: Is the only diff a request to set the recursion limit to its already default?\n    if (configsAreDifferent && !isOnlyRecursionLimitDifferent) {\n      await client.assistants.update(assistantId, {\n        config: newConfig,\n      });\n    }\n  }\n\n  // Do not input keys that are not part of the input schema\n  if (payload.input && schemaKeys?.input) {\n    payload.input = filterObjectBySchemaKeys(payload.input, schemaKeys.input);\n  }\n\n  let streamingStateExtractor = new StreamingStateExtractor([]);\n  let prevNodeName = null;\n  let emitIntermediateStateUntilEnd = null;\n  let shouldExit = false;\n  let externalRunId = null;\n\n  const streamResponse = client.runs.stream(threadId, assistantId, payload);\n\n  const emit = (message: string) => controller.enqueue(new TextEncoder().encode(message));\n\n  let latestStateValues = {};\n  let updatedState = state;\n  // If a manual emittance happens, it is the ultimate source of truth of state, unless a node has exited.\n  // Therefore, this value should either hold null, or the only edition of state that should be used.\n  let manuallyEmittedState = null;\n\n  activeInterruptEvent = false;\n  try {\n    telemetry.capture(\"oss.runtime.agent_execution_stream_started\", {\n      hashedLgcKey: streamInfo.hashedLgcKey,\n    });\n    for await (let streamResponseChunk of streamResponse) {\n      if (![\"events\", \"values\", \"error\", \"updates\"].includes(streamResponseChunk.event)) continue;\n\n      if (streamResponseChunk.event === \"error\") {\n        throw new Error(`Error event thrown: ${streamResponseChunk.data.message}`);\n      }\n\n      // Force event type, as data is not properly defined on the LG side.\n      type EventsChunkData = {\n        __interrupt__?: any;\n        metadata: Record<string, any>;\n        event: string;\n        data: any;\n        [key: string]: unknown;\n      };\n      const chunk = streamResponseChunk as EventsStreamEvent & { data: EventsChunkData };\n\n      const interruptEvents = chunk.data.__interrupt__;\n      if (interruptEvents?.length) {\n        activeInterruptEvent = true;\n        const interruptValue = interruptEvents?.[0].value;\n        if (\n          typeof interruptValue != \"string\" &&\n          \"__copilotkit_interrupt_value__\" in interruptValue\n        ) {\n          const evValue = interruptValue.__copilotkit_interrupt_value__;\n          emit(\n            JSON.stringify({\n              event: LangGraphEventTypes.OnCopilotKitInterrupt,\n              data: {\n                value: typeof evValue === \"string\" ? evValue : JSON.stringify(evValue),\n                messages: langchainMessagesToCopilotKit(interruptValue.__copilotkit_messages__),\n              },\n            }) + \"\\n\",\n          );\n        } else {\n          emit(\n            JSON.stringify({\n              event: LangGraphEventTypes.OnInterrupt,\n              value:\n                typeof interruptValue === \"string\"\n                  ? interruptValue\n                  : JSON.stringify(interruptValue),\n            }) + \"\\n\",\n          );\n        }\n        continue;\n      }\n      if (streamResponseChunk.event === \"updates\") continue;\n\n      if (streamResponseChunk.event === \"values\") {\n        latestStateValues = chunk.data;\n        continue;\n      }\n\n      const chunkData = chunk.data;\n      const currentNodeName = chunkData.metadata.langgraph_node;\n      const eventType = chunkData.event;\n      const runId = chunkData.metadata.run_id;\n      externalRunId = runId;\n      const metadata = chunkData.metadata;\n      if (chunkData.data?.output?.model != null && chunkData.data?.output?.model != \"\") {\n        streamInfo.provider = chunkData.data?.output?.model;\n      }\n      if (metadata.langgraph_host != null && metadata.langgraph_host != \"\") {\n        streamInfo.langGraphHost = metadata.langgraph_host;\n      }\n      if (metadata.langgraph_version != null && metadata.langgraph_version != \"\") {\n        streamInfo.langGraphVersion = metadata.langgraph_version;\n      }\n\n      shouldExit =\n        shouldExit ||\n        (eventType === LangGraphEventTypes.OnCustomEvent &&\n          chunkData.name === CustomEventNames.CopilotKitExit);\n\n      const emitIntermediateState = metadata[\"copilotkit:emit-intermediate-state\"];\n      const manuallyEmitIntermediateState =\n        eventType === LangGraphEventTypes.OnCustomEvent &&\n        chunkData.name === CustomEventNames.CopilotKitManuallyEmitIntermediateState;\n\n      const exitingNode =\n        nodeName === currentNodeName && eventType === LangGraphEventTypes.OnChainEnd;\n\n      // See manuallyEmittedState for explanation\n      if (exitingNode) {\n        manuallyEmittedState = null;\n      }\n\n      // we only want to update the node name under certain conditions\n      // since we don't need any internal node names to be sent to the frontend\n      if (graphInfo[\"nodes\"].some((node) => node.id === currentNodeName)) {\n        nodeName = currentNodeName;\n      }\n\n      updatedState = manuallyEmittedState ?? latestStateValues;\n\n      if (!nodeName) {\n        continue;\n      }\n\n      if (manuallyEmitIntermediateState) {\n        // See manuallyEmittedState for explanation\n        manuallyEmittedState = chunkData.data;\n        emit(\n          getStateSyncEvent({\n            threadId,\n            runId,\n            agentName: agent.name,\n            nodeName,\n            state: manuallyEmittedState,\n            running: true,\n            active: true,\n            schemaKeys,\n          }),\n        );\n        continue;\n      }\n\n      if (emitIntermediateState && emitIntermediateStateUntilEnd == null) {\n        emitIntermediateStateUntilEnd = nodeName;\n      }\n\n      if (emitIntermediateState && eventType === LangGraphEventTypes.OnChatModelStart) {\n        // reset the streaming state extractor\n        streamingStateExtractor = new StreamingStateExtractor(emitIntermediateState);\n      }\n\n      if (emitIntermediateState && eventType === LangGraphEventTypes.OnChatModelStream) {\n        streamingStateExtractor.bufferToolCalls(chunkData);\n      }\n\n      if (emitIntermediateStateUntilEnd !== null) {\n        updatedState = {\n          ...updatedState,\n          ...streamingStateExtractor.extractState(),\n        };\n      }\n\n      if (\n        !emitIntermediateState &&\n        currentNodeName === emitIntermediateStateUntilEnd &&\n        eventType === LangGraphEventTypes.OnChainEnd\n      ) {\n        // stop emitting function call state\n        emitIntermediateStateUntilEnd = null;\n      }\n\n      if (\n        JSON.stringify(updatedState) !== JSON.stringify(state) ||\n        prevNodeName != nodeName ||\n        exitingNode\n      ) {\n        state = updatedState;\n        prevNodeName = nodeName;\n        emit(\n          getStateSyncEvent({\n            threadId,\n            runId,\n            agentName: agent.name,\n            nodeName,\n            state,\n            running: true,\n            active: !exitingNode,\n            schemaKeys,\n          }),\n        );\n      }\n\n      emit(JSON.stringify(chunkData) + \"\\n\");\n    }\n\n    state = await client.threads.getState(threadId);\n    const interrupts = state.tasks?.[0]?.interrupts;\n    nodeName = interrupts ? nodeName : Object.keys(state.metadata.writes)[0];\n    const isEndNode = state.next.length === 0 && !interrupts;\n\n    telemetry.capture(\"oss.runtime.agent_execution_stream_ended\", streamInfo);\n\n    emit(\n      getStateSyncEvent({\n        threadId,\n        runId: externalRunId,\n        agentName: agent.name,\n        nodeName: isEndNode ? \"__end__\" : nodeName,\n        state: state.values,\n        running: !shouldExit,\n        active: false,\n        includeMessages: true,\n        schemaKeys,\n      }),\n    );\n\n    return Promise.resolve();\n  } catch (e) {\n    logger.error(e);\n    telemetry.capture(\"oss.runtime.agent_execution_stream_errored\", {\n      ...streamInfo,\n      error: e.message,\n    });\n    return Promise.resolve();\n  }\n}\n\nfunction getStateSyncEvent({\n  threadId,\n  runId,\n  agentName,\n  nodeName,\n  state,\n  running,\n  active,\n  includeMessages = false,\n  schemaKeys,\n}: {\n  threadId: string;\n  runId: string;\n  agentName: string;\n  nodeName: string;\n  state: State;\n  running: boolean;\n  active: boolean;\n  includeMessages?: boolean;\n  schemaKeys: SchemaKeys;\n}): string {\n  if (!includeMessages) {\n    state = Object.keys(state).reduce((acc, key) => {\n      if (key !== \"messages\") {\n        acc[key] = state[key];\n      }\n      return acc;\n    }, {} as State);\n  } else {\n    state = {\n      ...state,\n      messages: langchainMessagesToCopilotKit(state.messages || []),\n    };\n  }\n\n  // Do not emit state keys that are not part of the output schema\n  if (schemaKeys?.output) {\n    state = filterObjectBySchemaKeys(state, schemaKeys.output);\n  }\n\n  return (\n    JSON.stringify({\n      event: LangGraphEventTypes.OnCopilotKitStateSync,\n      thread_id: threadId,\n      run_id: runId,\n      agent_name: agentName,\n      node_name: nodeName,\n      active: active,\n      state: state,\n      running: running,\n      role: \"assistant\",\n    }) + \"\\n\"\n  );\n}\n\nclass StreamingStateExtractor {\n  private emitIntermediateState: { [key: string]: any }[];\n  private toolCallBuffer: { [key: string]: string };\n  private currentToolCall: string | null;\n  private previouslyParsableState: { [key: string]: any };\n\n  constructor(emitIntermediateState: { [key: string]: any }[]) {\n    this.emitIntermediateState = emitIntermediateState;\n    this.toolCallBuffer = {};\n    this.currentToolCall = null;\n    this.previouslyParsableState = {};\n  }\n\n  bufferToolCalls(event: {\n    data: { chunk: { tool_call_chunks: { name: string | null; args: string }[] } };\n  }) {\n    if (event.data.chunk.tool_call_chunks.length > 0) {\n      const chunk = event.data.chunk.tool_call_chunks[0];\n\n      if (chunk.name !== null && chunk.name !== undefined) {\n        this.currentToolCall = chunk.name;\n        this.toolCallBuffer[this.currentToolCall] = chunk.args;\n      } else if (this.currentToolCall !== null && this.currentToolCall !== undefined) {\n        this.toolCallBuffer[this.currentToolCall] += chunk.args;\n      }\n    }\n  }\n\n  getEmitStateConfig(currentToolName: string): [string | null, string | null] {\n    for (const config of this.emitIntermediateState) {\n      const stateKey = config[\"state_key\"];\n      const tool = config[\"tool\"];\n      const toolArgument = config[\"tool_argument\"];\n\n      if (currentToolName === tool) {\n        return [toolArgument, stateKey];\n      }\n    }\n    return [null, null];\n  }\n\n  extractState(): State {\n    const state: State = {};\n\n    for (const [key, value] of Object.entries(this.toolCallBuffer)) {\n      const [argumentName, stateKey] = this.getEmitStateConfig(key);\n\n      if (stateKey === null) {\n        continue;\n      }\n\n      let parsedValue;\n      try {\n        parsedValue = parsePartialJson(value);\n      } catch (error) {\n        if (key in this.previouslyParsableState) {\n          parsedValue = this.previouslyParsableState[key];\n        } else {\n          continue;\n        }\n      }\n\n      this.previouslyParsableState[key] = parsedValue;\n\n      if (!argumentName) {\n        state[stateKey] = parsedValue;\n      } else {\n        state[stateKey] = parsedValue[argumentName];\n      }\n    }\n\n    return state;\n  }\n}\n\n// Start of Selection\nfunction langGraphDefaultMergeState(\n  state: State,\n  messages: LangGraphPlatformMessage[],\n  actions: ExecutionAction[],\n  agentName: string,\n): State {\n  if (messages.length > 0 && \"role\" in messages[0] && messages[0].role === \"system\") {\n    // remove system message\n    messages = messages.slice(1);\n  }\n\n  // merge with existing messages\n  const existingMessages: LangGraphPlatformMessage[] = state.messages || [];\n  const existingMessageIds = new Set(existingMessages.map((message) => message.id));\n  const messageIds = new Set(messages.map((message) => message.id));\n\n  let removedMessages = [];\n  if (messages.length < existingMessages.length) {\n    // Messages were removed\n    removedMessages = existingMessages\n      .filter((m) => !messageIds.has(m.id))\n      .map((m) => new RemoveMessage({ id: m.id }));\n  }\n\n  const newMessages = messages.filter((message) => !existingMessageIds.has(message.id));\n\n  return {\n    ...state,\n    messages: [...removedMessages, ...newMessages],\n    copilotkit: {\n      actions,\n    },\n  };\n}\n\nexport function langchainMessagesToCopilotKit(messages: any[]): any[] {\n  const result: any[] = [];\n  const tool_call_names: Record<string, string> = {};\n\n  // First pass: gather all tool call names from AI messages\n  for (const message of messages) {\n    if (message.type === \"ai\") {\n      for (const tool_call of message.tool_calls) {\n        tool_call_names[tool_call.id] = tool_call.name;\n      }\n    }\n  }\n\n  for (const message of messages) {\n    let content: any = message.content;\n    if (content instanceof Array) {\n      content = content[0];\n    }\n    if (content instanceof Object) {\n      content = content.text;\n    }\n\n    if (message.type === \"human\") {\n      result.push({\n        role: \"user\",\n        content: content,\n        id: message.id,\n      });\n    } else if (message.type === \"system\") {\n      result.push({\n        role: \"system\",\n        content: content,\n        id: message.id,\n      });\n    } else if (message.type === \"ai\") {\n      if (message.tool_calls && message.tool_calls.length > 0) {\n        for (const tool_call of message.tool_calls) {\n          result.push({\n            id: tool_call.id,\n            name: tool_call.name,\n            arguments: tool_call.args,\n            parentMessageId: message.id,\n          });\n        }\n      } else {\n        result.push({\n          role: \"assistant\",\n          content: content,\n          id: message.id,\n          parentMessageId: message.id,\n        });\n      }\n    } else if (message.type === \"tool\") {\n      const actionName = tool_call_names[message.tool_call_id] || message.name || \"\";\n      result.push({\n        actionExecutionId: message.tool_call_id,\n        actionName: actionName,\n        result: content,\n        id: message.id,\n      });\n    }\n  }\n  const resultsDict: Record<string, any> = {};\n  for (const msg of result) {\n    if (msg.actionExecutionId) {\n      resultsDict[msg.actionExecutionId] = msg;\n    }\n  }\n\n  const reorderedResult: Message[] = [];\n\n  for (const msg of result) {\n    // If it's not a tool result, just append it\n    if (!(\"actionExecutionId\" in msg)) {\n      reorderedResult.push(msg);\n    }\n\n    // If the message has arguments (i.e., is a tool call invocation),\n    // append the corresponding result right after it\n    if (\"arguments\" in msg) {\n      const msgId = msg.id;\n      if (msgId in resultsDict) {\n        reorderedResult.push(resultsDict[msgId]);\n      }\n    }\n  }\n\n  return reorderedResult;\n}\n\nfunction copilotkitMessagesToLangChain(messages: Message[]): LangGraphPlatformMessage[] {\n  const result: LangGraphPlatformMessage[] = [];\n  const processedActionExecutions = new Set<string>();\n\n  for (const message of messages) {\n    // Handle TextMessage\n    if (message.isTextMessage()) {\n      if (message.role === \"user\") {\n        // Human message\n        result.push({\n          ...message,\n          role: MessageRole.user,\n        });\n      } else if (message.role === \"system\") {\n        // System message\n        result.push({\n          ...message,\n          role: MessageRole.system,\n        });\n      } else if (message.role === \"assistant\") {\n        // Assistant message\n        result.push({\n          ...message,\n          role: MessageRole.assistant,\n        });\n      }\n      continue;\n    }\n\n    // Handle ImageMessage\n    if (message.isImageMessage()) {\n      if (message.role === \"user\") {\n        result.push({\n          ...message,\n          role: MessageRole.user,\n          content: \"\",\n        });\n      } else if (message.role === \"assistant\") {\n        result.push({\n          ...message,\n          role: MessageRole.assistant,\n          content: \"\",\n        });\n      }\n      continue;\n    }\n\n    // Handle ActionExecutionMessage (multiple tool calls per parentMessageId)\n    if (message.isActionExecutionMessage()) {\n      const messageId = message.parentMessageId ?? message.id;\n\n      // If we've already processed this action execution group, skip\n      if (processedActionExecutions.has(messageId)) {\n        continue;\n      }\n\n      processedActionExecutions.add(messageId);\n\n      // Gather all tool calls related to this messageId\n      const relatedActionExecutions = messages.filter(\n        (m) =>\n          m.isActionExecutionMessage() &&\n          ((m.parentMessageId && m.parentMessageId === messageId) || m.id === messageId),\n      ) as ActionExecutionMessage[];\n\n      const tool_calls: ToolCall[] = relatedActionExecutions.map((m) => ({\n        name: m.name,\n        args: m.arguments,\n        id: m.id,\n      }));\n\n      result.push({\n        id: messageId,\n        type: \"ActionExecutionMessage\",\n        content: \"\",\n        tool_calls: tool_calls,\n        role: MessageRole.assistant,\n      } satisfies LangGraphPlatformActionExecutionMessage);\n\n      continue;\n    }\n\n    // Handle ResultMessage\n    if (message.isResultMessage()) {\n      result.push({\n        type: message.type,\n        content: message.result,\n        id: message.id,\n        tool_call_id: message.actionExecutionId,\n        name: message.actionName,\n        role: MessageRole.tool,\n      } satisfies LangGraphPlatformResultMessage);\n      continue;\n    }\n\n    throw new Error(`Unknown message type ${message.type}`);\n  }\n\n  return result;\n}\n\nfunction getSchemaKeys(graphSchema: GraphSchema): SchemaKeys {\n  const CONSTANT_KEYS = [\"messages\", \"copilotkit\"];\n  let configSchema = null;\n  if (graphSchema.config_schema.properties) {\n    configSchema = Object.keys(graphSchema.config_schema.properties);\n  }\n  if (!graphSchema.input_schema.properties || !graphSchema.output_schema.properties) {\n    return configSchema;\n  }\n  const inputSchema = Object.keys(graphSchema.input_schema.properties);\n  const outputSchema = Object.keys(graphSchema.output_schema.properties);\n\n  return {\n    input: inputSchema && inputSchema.length ? [...inputSchema, ...CONSTANT_KEYS] : null,\n    output: outputSchema && outputSchema.length ? [...outputSchema, ...CONSTANT_KEYS] : null,\n    config: configSchema,\n  };\n}\n\nfunction filterObjectBySchemaKeys(obj: Record<string, any>, schemaKeys: string[]) {\n  return Object.fromEntries(Object.entries(obj).filter(([key]) => schemaKeys.includes(key)));\n}\n", "import { ReplaySubject } from \"rxjs\";\n\nexport async function writeJsonLineResponseToEventStream<T>(\n  response: ReadableStream<Uint8Array>,\n  eventStream$: ReplaySubject<T>,\n) {\n  const reader = response.getReader();\n  const decoder = new TextDecoder();\n  let buffer = [];\n\n  function flushBuffer() {\n    const currentBuffer = buffer.join(\"\");\n    if (currentBuffer.trim().length === 0) {\n      return;\n    }\n    const parts = currentBuffer.split(\"\\n\");\n    if (parts.length === 0) {\n      return;\n    }\n\n    const lastPartIsComplete = currentBuffer.endsWith(\"\\n\");\n\n    // truncate buffer\n    buffer = [];\n\n    if (!lastPartIsComplete) {\n      // put back the last part\n      buffer.push(parts.pop());\n    }\n\n    parts\n      .map((part) => part.trim())\n      .filter((part) => part != \"\")\n      .forEach((part) => {\n        eventStream$.next(JSON.parse(part));\n      });\n  }\n\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n\n      if (!done) {\n        buffer.push(decoder.decode(value, { stream: true }));\n      }\n\n      flushBuffer();\n\n      if (done) {\n        break;\n      }\n    }\n  } catch (error) {\n    console.error(\"Error in stream\", error);\n    eventStream$.error(error);\n    return;\n  }\n  eventStream$.complete();\n}\n", "import { Logger } from \"pino\";\nimport { Observable } from \"rxjs\";\nimport { AgentStateInput } from \"../../graphql/inputs/agent-state.input\";\nimport { Message } from \"../../graphql/types/converted\";\nimport { RuntimeEvent } from \"../../service-adapters/events\";\nimport telemetry from \"../telemetry-client\";\nimport { RemoteAgentHandlerParams } from \"./remote-actions\";\n\nimport {\n  AssistantMessage as AgentWireAssistantMessage,\n  Message as AgentWireMessage,\n  ToolCall,\n} from \"@ag-ui/client\";\n\nimport { AbstractAgent } from \"@ag-ui/client\";\nimport { parseJson } from \"@copilotkit/shared\";\n\nexport function constructAgentWireRemoteAction({\n  logger,\n  messages,\n  agentStates,\n  agent,\n}: {\n  logger: Logger;\n  messages: Message[];\n  agentStates?: AgentStateInput[];\n  agent: AbstractAgent;\n}) {\n  const action = {\n    name: agent.agentId,\n    description: agent.description,\n    parameters: [],\n    handler: async (_args: any) => {},\n    remoteAgentHandler: async ({\n      actionInputsWithoutAgents,\n      threadId,\n    }: RemoteAgentHandlerParams): Promise<Observable<RuntimeEvent>> => {\n      logger.debug({ actionName: agent.agentId }, \"Executing remote agent\");\n\n      const agentWireMessages = convertMessagesToAgentWire(messages);\n      agent.messages = agentWireMessages;\n      agent.threadId = threadId;\n\n      telemetry.capture(\"oss.runtime.remote_action_executed\", {\n        agentExecution: true,\n        type: \"self-hosted\",\n        agentsAmount: 1,\n      });\n\n      let state = {};\n      if (agentStates) {\n        const jsonState = agentStates.find((state) => state.agentName === agent.agentId);\n        if (jsonState) {\n          state = parseJson(jsonState.state, {});\n        }\n      }\n      agent.state = state;\n\n      const tools = actionInputsWithoutAgents.map((input) => {\n        return {\n          name: input.name,\n          description: input.description,\n          parameters: JSON.parse(input.jsonSchema),\n        };\n      });\n\n      return agent.legacy_to_be_removed_runAgentBridged({\n        tools,\n      }) as Observable<RuntimeEvent>;\n    },\n  };\n  return [action];\n}\n\nexport function convertMessagesToAgentWire(messages: Message[]): AgentWireMessage[] {\n  const result: AgentWireMessage[] = [];\n\n  for (const message of messages) {\n    if (message.isTextMessage()) {\n      result.push({\n        id: message.id,\n        role: message.role as any,\n        content: message.content,\n      });\n    } else if (message.isActionExecutionMessage()) {\n      const toolCall: ToolCall = {\n        id: message.id,\n        type: \"function\",\n        function: {\n          name: message.name,\n          arguments: JSON.stringify(message.arguments),\n        },\n      };\n\n      if (message.parentMessageId && result.some((m) => m.id === message.parentMessageId)) {\n        const parentMessage: AgentWireAssistantMessage | undefined = result.find(\n          (m) => m.id === message.parentMessageId,\n        ) as AgentWireAssistantMessage;\n        if (parentMessage.toolCalls === undefined) {\n          parentMessage.toolCalls = [];\n        }\n        parentMessage.toolCalls.push(toolCall);\n      } else {\n        result.push({\n          id: message.parentMessageId ?? message.id,\n          role: \"assistant\",\n          toolCalls: [toolCall],\n        });\n      }\n    } else if (message.isResultMessage()) {\n      result.push({\n        id: message.id,\n        role: \"tool\",\n        content: message.result,\n        toolCallId: message.actionExecutionId,\n      });\n    }\n  }\n\n  return result;\n}\n", "/**\n * <Callout type=\"info\">\n *   This is the reference for the `CopilotRuntime` class. For more information and example code snippets, please see [Concept: Copilot Runtime](/concepts/copilot-runtime).\n * </Callout>\n *\n * ## Usage\n *\n * ```tsx\n * import { CopilotRuntime } from \"@copilotkit/runtime\";\n *\n * const copilotKit = new CopilotRuntime();\n * ```\n */\n\nimport {\n  Action,\n  actionParametersToJsonSchema,\n  Parameter,\n  ResolvedCopilotKitError,\n  CopilotKitApiDiscoveryError,\n  randomId,\n  CopilotKitError,\n  CopilotKitLowLevelError,\n  CopilotKitAgentDiscoveryError,\n  CopilotKitMisuseError,\n} from \"@copilotkit/shared\";\nimport {\n  CopilotServiceAdapter,\n  EmptyAdapter,\n  RemoteChain,\n  RemoteChainParameters,\n} from \"../../service-adapters\";\n\nimport { MessageInput } from \"../../graphql/inputs/message.input\";\nimport { ActionInput } from \"../../graphql/inputs/action.input\";\nimport { RuntimeEventSource, RuntimeEventTypes } from \"../../service-adapters/events\";\nimport { convertGqlInputToMessages } from \"../../service-adapters/conversion\";\nimport { Message } from \"../../graphql/types/converted\";\nimport { ForwardedParametersInput } from \"../../graphql/inputs/forwarded-parameters.input\";\n\nimport {\n  isRemoteAgentAction,\n  RemoteAgentAction,\n  EndpointType,\n  setupRemoteActions,\n  EndpointDefinition,\n  CopilotKitEndpoint,\n  LangGraphPlatformEndpoint,\n} from \"./remote-actions\";\n\nimport { GraphQLContext } from \"../integrations/shared\";\nimport { AgentSessionInput } from \"../../graphql/inputs/agent-session.input\";\nimport { from } from \"rxjs\";\nimport { AgentStateInput } from \"../../graphql/inputs/agent-state.input\";\nimport { ActionInputAvailability } from \"../../graphql/types/enums\";\nimport { createHeaders } from \"./remote-action-constructors\";\nimport { Agent } from \"../../graphql/types/agents-response.type\";\nimport { ExtensionsInput } from \"../../graphql/inputs/extensions.input\";\nimport { ExtensionsResponse } from \"../../graphql/types/extensions-response.type\";\nimport { LoadAgentStateResponse } from \"../../graphql/types/load-agent-state-response.type\";\nimport { Client as LangGraphClient } from \"@langchain/langgraph-sdk\";\nimport { langchainMessagesToCopilotKit } from \"./remote-lg-action\";\nimport { MetaEventInput } from \"../../graphql/inputs/meta-event.input\";\nimport {\n  CopilotObservabilityConfig,\n  LLMRequestData,\n  LLMResponseData,\n  LLMErrorData,\n} from \"../observability\";\nimport { AbstractAgent } from \"@ag-ui/client\";\nimport { MessageRole } from \"../../graphql/types/enums\";\n\n// +++ MCP Imports +++\nimport {\n  MCPClient,\n  MCPEndpointConfig,\n  MCPTool,\n  convertMCPToolsToActions,\n  generateMcpToolInstructions,\n} from \"./mcp-tools-utils\";\n// Define the function type alias here or import if defined elsewhere\ntype CreateMCPClientFunction = (config: MCPEndpointConfig) => Promise<MCPClient>;\n// --- MCP Imports ---\n\nexport interface CopilotRuntimeRequest {\n  serviceAdapter: CopilotServiceAdapter;\n  messages: MessageInput[];\n  actions: ActionInput[];\n  agentSession?: AgentSessionInput;\n  agentStates?: AgentStateInput[];\n  outputMessagesPromise: Promise<Message[]>;\n  threadId?: string;\n  runId?: string;\n  publicApiKey?: string;\n  graphqlContext: GraphQLContext;\n  forwardedParameters?: ForwardedParametersInput;\n  url?: string;\n  extensions?: ExtensionsInput;\n  metaEvents?: MetaEventInput[];\n}\n\ninterface CopilotRuntimeResponse {\n  threadId: string;\n  runId?: string;\n  eventSource: RuntimeEventSource;\n  serverSideActions: Action<any>[];\n  actionInputsWithoutAgents: ActionInput[];\n  extensions?: ExtensionsResponse;\n}\n\ntype ActionsConfiguration<T extends Parameter[] | [] = []> =\n  | Action<T>[]\n  | ((ctx: { properties: any; url?: string }) => Action<T>[]);\n\ninterface OnBeforeRequestOptions {\n  threadId?: string;\n  runId?: string;\n  inputMessages: Message[];\n  properties: any;\n  url?: string;\n}\n\ntype OnBeforeRequestHandler = (options: OnBeforeRequestOptions) => void | Promise<void>;\n\ninterface OnAfterRequestOptions {\n  threadId: string;\n  runId?: string;\n  inputMessages: Message[];\n  outputMessages: Message[];\n  properties: any;\n  url?: string;\n}\n\ntype OnAfterRequestHandler = (options: OnAfterRequestOptions) => void | Promise<void>;\n\ninterface Middleware {\n  /**\n   * A function that is called before the request is processed.\n   */\n  onBeforeRequest?: OnBeforeRequestHandler;\n\n  /**\n   * A function that is called after the request is processed.\n   */\n  onAfterRequest?: OnAfterRequestHandler;\n}\n\ntype AgentWithEndpoint = Agent & { endpoint: EndpointDefinition };\n\nexport interface CopilotRuntimeConstructorParams<T extends Parameter[] | [] = []> {\n  /**\n   * Middleware to be used by the runtime.\n   *\n   * ```ts\n   * onBeforeRequest: (options: {\n   *   threadId?: string;\n   *   runId?: string;\n   *   inputMessages: Message[];\n   *   properties: any;\n   * }) => void | Promise<void>;\n   * ```\n   *\n   * ```ts\n   * onAfterRequest: (options: {\n   *   threadId?: string;\n   *   runId?: string;\n   *   inputMessages: Message[];\n   *   outputMessages: Message[];\n   *   properties: any;\n   * }) => void | Promise<void>;\n   * ```\n   */\n  middleware?: Middleware;\n\n  /*\n   * A list of server side actions that can be executed. Will be ignored when remoteActions are set\n   */\n  actions?: ActionsConfiguration<T>;\n\n  /*\n   * Deprecated: Use `remoteEndpoints`.\n   */\n  remoteActions?: CopilotKitEndpoint[];\n\n  /*\n   * A list of remote actions that can be executed.\n   */\n  remoteEndpoints?: EndpointDefinition[];\n\n  /*\n   * An array of LangServer URLs.\n   */\n  langserve?: RemoteChainParameters[];\n\n  /*\n   * A map of agent names to AgentWire agents.\n   */\n  agents?: Record<string, AbstractAgent>;\n\n  /*\n   * Delegates agent state processing to the service adapter.\n   *\n   * When enabled, individual agent state requests will not be processed by the agent itself.\n   * Instead, all processing will be handled by the service adapter.\n   */\n  delegateAgentProcessingToServiceAdapter?: boolean;\n\n  /**\n   * Configuration for LLM request/response logging.\n   * Requires publicApiKey from CopilotKit component to be set:\n   *\n   * ```tsx\n   * <CopilotKit publicApiKey=\"ck_pub_...\" />\n   * ```\n   *\n   * Example logging config:\n   * ```ts\n   * logging: {\n   *   enabled: true, // Enable or disable logging\n   *   progressive: true, // Set to false for buffered logging\n   *   logger: {\n   *     logRequest: (data) => langfuse.trace({ name: \"LLM Request\", input: data }),\n   *     logResponse: (data) => langfuse.trace({ name: \"LLM Response\", output: data }),\n   *     logError: (errorData) => langfuse.trace({ name: \"LLM Error\", metadata: errorData }),\n   *   },\n   * }\n   * ```\n   */\n  observability_c?: CopilotObservabilityConfig;\n\n  /**\n   * Configuration for connecting to Model Context Protocol (MCP) servers.\n   * Allows fetching and using tools defined on external MCP-compliant servers.\n   * Requires providing the `createMCPClient` function during instantiation.\n   * @experimental\n   */\n  mcpServers?: MCPEndpointConfig[];\n\n  /**\n   * A function that creates an MCP client instance for a given endpoint configuration.\n   * This function is responsible for using the appropriate MCP client library\n   * (e.g., `@copilotkit/runtime`, `ai`) to establish a connection.\n   * Required if `mcpServers` is provided.\n   *\n   * ```typescript\n   * import { experimental_createMCPClient } from \"ai\"; // Import from vercel ai library\n   * // ...\n   * const runtime = new CopilotRuntime({\n   *   mcpServers: [{ endpoint: \"...\" }],\n   *   async createMCPClient(config) {\n   *     return await experimental_createMCPClient({\n   *       transport: {\n   *         type: \"sse\",\n   *         url: config.endpoint,\n   *         headers: config.apiKey\n   *           ? { Authorization: `Bearer ${config.apiKey}` }\n   *           : undefined,\n   *       },\n   *     });\n   *   }\n   * });\n   * ```\n   */\n  createMCPClient?: CreateMCPClientFunction;\n}\n\nexport class CopilotRuntime<const T extends Parameter[] | [] = []> {\n  public actions: ActionsConfiguration<T>;\n  public agents: Record<string, AbstractAgent>;\n  public remoteEndpointDefinitions: EndpointDefinition[];\n  private langserve: Promise<Action<any>>[] = [];\n  private onBeforeRequest?: OnBeforeRequestHandler;\n  private onAfterRequest?: OnAfterRequestHandler;\n  private delegateAgentProcessingToServiceAdapter: boolean;\n  private observability?: CopilotObservabilityConfig;\n  private availableAgents: Pick<AgentWithEndpoint, \"name\" | \"id\">[];\n\n  // +++ MCP Properties +++\n  private readonly mcpServersConfig?: MCPEndpointConfig[];\n  private mcpActionCache = new Map<string, Action<any>[]>();\n  // --- MCP Properties ---\n\n  // +++ MCP Client Factory +++\n  private readonly createMCPClientImpl?: CreateMCPClientFunction;\n  // --- MCP Client Factory ---\n\n  constructor(params?: CopilotRuntimeConstructorParams<T>) {\n    if (\n      params?.actions &&\n      params?.remoteEndpoints &&\n      params?.remoteEndpoints.some((e) => e.type === EndpointType.LangGraphPlatform)\n    ) {\n      console.warn(\"Actions set in runtime instance will not be available for the agent\");\n    }\n    this.actions = params?.actions || [];\n    this.availableAgents = [];\n\n    for (const chain of params?.langserve || []) {\n      const remoteChain = new RemoteChain(chain);\n      this.langserve.push(remoteChain.toAction());\n    }\n\n    this.remoteEndpointDefinitions = params?.remoteEndpoints ?? params?.remoteActions ?? [];\n\n    this.onBeforeRequest = params?.middleware?.onBeforeRequest;\n    this.onAfterRequest = params?.middleware?.onAfterRequest;\n    this.delegateAgentProcessingToServiceAdapter =\n      params?.delegateAgentProcessingToServiceAdapter || false;\n    this.observability = params?.observability_c;\n    this.agents = params?.agents ?? {};\n    // +++ MCP Initialization +++\n    this.mcpServersConfig = params?.mcpServers;\n    this.createMCPClientImpl = params?.createMCPClient;\n\n    // Validate: If mcpServers are provided, createMCPClient must also be provided\n    if (this.mcpServersConfig && this.mcpServersConfig.length > 0 && !this.createMCPClientImpl) {\n      throw new CopilotKitMisuseError({\n        message:\n          \"MCP Integration Error: `mcpServers` were provided, but the `createMCPClient` function was not passed to the CopilotRuntime constructor. \" +\n          \"Please provide an implementation for `createMCPClient`.\",\n      });\n    }\n\n    // Warning if actions are defined alongside LangGraph platform (potentially MCP too?)\n    if (\n      params?.actions &&\n      (params?.remoteEndpoints?.some((e) => e.type === EndpointType.LangGraphPlatform) ||\n        this.mcpServersConfig?.length)\n    ) {\n      console.warn(\n        \"Local 'actions' defined in CopilotRuntime might not be available to remote agents (LangGraph, MCP). Consider defining actions closer to the agent implementation if needed.\",\n      );\n    }\n  }\n\n  // +++ MCP Instruction Injection Method +++\n  private injectMCPToolInstructions(\n    messages: MessageInput[],\n    currentActions: Action<any>[],\n  ): MessageInput[] {\n    // Filter the *passed-in* actions for MCP tools\n    const mcpActionsForRequest = currentActions.filter((action) => (action as any)._isMCPTool);\n\n    if (!mcpActionsForRequest || mcpActionsForRequest.length === 0) {\n      return messages; // No MCP tools for this specific request\n    }\n\n    // Create a map to deduplicate tools by name (keeping the last one if duplicates exist)\n    const uniqueMcpTools = new Map<string, Action<any>>();\n\n    // Add all MCP tools to the map with their names as keys\n    mcpActionsForRequest.forEach((action) => {\n      uniqueMcpTools.set(action.name, action);\n    });\n\n    // Format instructions from the unique tools map\n    // Convert Action objects to MCPTool format for the instruction generator\n    const toolsMap: Record<string, MCPTool> = {};\n    Array.from(uniqueMcpTools.values()).forEach((action) => {\n      toolsMap[action.name] = {\n        description: action.description || \"\",\n        schema: action.parameters\n          ? {\n              parameters: {\n                properties: action.parameters.reduce(\n                  (acc, p) => ({\n                    ...acc,\n                    [p.name]: { type: p.type, description: p.description },\n                  }),\n                  {},\n                ),\n                required: action.parameters.filter((p) => p.required).map((p) => p.name),\n              },\n            }\n          : {},\n        execute: async () => ({}), // Placeholder, not used for instructions\n      };\n    });\n\n    // Generate instructions using the exported helper\n    const mcpToolInstructions = generateMcpToolInstructions(toolsMap);\n\n    if (!mcpToolInstructions) {\n      return messages; // No MCP tools to describe\n    }\n\n    const instructions =\n      \"You have access to the following tools provided by external Model Context Protocol (MCP) servers:\\n\" +\n      mcpToolInstructions +\n      \"\\nUse them when appropriate to fulfill the user's request.\";\n\n    const systemMessageIndex = messages.findIndex((msg) => msg.textMessage?.role === \"system\");\n\n    const newMessages = [...messages]; // Create a mutable copy\n\n    if (systemMessageIndex !== -1) {\n      const existingMsg = newMessages[systemMessageIndex];\n      if (existingMsg.textMessage) {\n        existingMsg.textMessage.content =\n          (existingMsg.textMessage.content ? existingMsg.textMessage.content + \"\\n\\n\" : \"\") +\n          instructions;\n      }\n    } else {\n      newMessages.unshift({\n        id: randomId(),\n        createdAt: new Date(),\n        textMessage: {\n          role: MessageRole.system,\n          content: instructions,\n        },\n        actionExecutionMessage: undefined,\n        resultMessage: undefined,\n        agentStateMessage: undefined,\n      });\n    }\n\n    return newMessages;\n  }\n  // --- MCP Instruction Injection Method ---\n\n  async processRuntimeRequest(request: CopilotRuntimeRequest): Promise<CopilotRuntimeResponse> {\n    const {\n      serviceAdapter,\n      messages: rawMessages,\n      actions: clientSideActionsInput,\n      threadId,\n      runId,\n      outputMessagesPromise,\n      graphqlContext,\n      forwardedParameters,\n      url,\n      extensions,\n      agentSession,\n      agentStates,\n      publicApiKey,\n    } = request;\n\n    const eventSource = new RuntimeEventSource();\n    // Track request start time for logging\n    const requestStartTime = Date.now();\n    // For storing streamed chunks if progressive logging is enabled\n    const streamedChunks: any[] = [];\n\n    try {\n      if (agentSession && !this.delegateAgentProcessingToServiceAdapter) {\n        return await this.processAgentRequest(request);\n      }\n      if (serviceAdapter instanceof EmptyAdapter) {\n        throw new CopilotKitMisuseError({\n          message: `Invalid adapter configuration: EmptyAdapter is only meant to be used with agent lock mode. \nFor non-agent components like useCopilotChatSuggestions, CopilotTextarea, or CopilotTask, \nplease use an LLM adapter instead.`,\n        });\n      }\n\n      // +++ Get Server Side Actions (including dynamic MCP) EARLY +++\n      const serverSideActions = await this.getServerSideActions(request);\n      // --- Get Server Side Actions (including dynamic MCP) EARLY ---\n\n      // Filter raw messages *before* injection\n      const filteredRawMessages = rawMessages.filter((message) => !message.agentStateMessage);\n\n      // +++ Inject MCP Instructions based on current actions +++\n      const messagesWithInjectedInstructions = this.injectMCPToolInstructions(\n        filteredRawMessages,\n        serverSideActions,\n      );\n      const inputMessages = convertGqlInputToMessages(messagesWithInjectedInstructions);\n      // --- Inject MCP Instructions based on current actions ---\n\n      // Log LLM request if logging is enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          const requestData: LLMRequestData = {\n            threadId,\n            runId,\n            model: forwardedParameters?.model,\n            messages: inputMessages,\n            actions: clientSideActionsInput,\n            forwardedParameters,\n            timestamp: requestStartTime,\n            provider: this.detectProvider(serviceAdapter),\n          };\n\n          await this.observability.hooks.handleRequest(requestData);\n        } catch (error) {\n          console.error(\"Error logging LLM request:\", error);\n        }\n      }\n\n      const serverSideActionsInput: ActionInput[] = serverSideActions.map((action) => ({\n        name: action.name,\n        description: action.description,\n        jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters)),\n      }));\n\n      const actionInputs = flattenToolCallsNoDuplicates([\n        ...serverSideActionsInput,\n        ...clientSideActionsInput.filter(\n          // Filter remote actions from CopilotKit core loop\n          (action) => action.available !== ActionInputAvailability.remote,\n        ),\n      ]);\n\n      await this.onBeforeRequest?.({\n        threadId,\n        runId,\n        inputMessages,\n        properties: graphqlContext.properties,\n        url,\n      });\n\n      const result = await serviceAdapter.process({\n        messages: inputMessages,\n        actions: actionInputs,\n        threadId,\n        runId,\n        eventSource,\n        forwardedParameters,\n        extensions,\n        agentSession,\n        agentStates,\n      });\n\n      // for backwards compatibility, we deal with the case that no threadId is provided\n      // by the frontend, by using the threadId from the response\n      const nonEmptyThreadId = threadId ?? result.threadId;\n\n      outputMessagesPromise\n        .then((outputMessages) => {\n          this.onAfterRequest?.({\n            threadId: nonEmptyThreadId,\n            runId: result.runId,\n            inputMessages,\n            outputMessages,\n            properties: graphqlContext.properties,\n            url,\n          });\n        })\n        .catch((_error) => {});\n\n      // After getting the response, log it if logging is enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          outputMessagesPromise\n            .then((outputMessages) => {\n              const responseData: LLMResponseData = {\n                threadId: result.threadId,\n                runId: result.runId,\n                model: forwardedParameters?.model,\n                // Use collected chunks for progressive mode or outputMessages for regular mode\n                output: this.observability.progressive ? streamedChunks : outputMessages,\n                latency: Date.now() - requestStartTime,\n                timestamp: Date.now(),\n                provider: this.detectProvider(serviceAdapter),\n                // Indicate this is the final response\n                isFinalResponse: true,\n              };\n\n              try {\n                this.observability.hooks.handleResponse(responseData);\n              } catch (logError) {\n                console.error(\"Error logging LLM response:\", logError);\n              }\n            })\n            .catch((error) => {\n              console.error(\"Failed to get output messages for logging:\", error);\n            });\n        } catch (error) {\n          console.error(\"Error setting up logging for LLM response:\", error);\n        }\n      }\n\n      // Add progressive logging if enabled\n      if (this.observability?.enabled && this.observability.progressive && publicApiKey) {\n        // Keep reference to original stream function\n        const originalStream = eventSource.stream.bind(eventSource);\n\n        // Wrap the stream function to intercept events\n        eventSource.stream = async (callback) => {\n          await originalStream(async (eventStream$) => {\n            // Create subscription to capture streaming events\n            eventStream$.subscribe({\n              next: (event) => {\n                // Only log content chunks\n                if (event.type === RuntimeEventTypes.TextMessageContent) {\n                  // Store the chunk\n                  streamedChunks.push(event.content);\n\n                  // Log each chunk separately for progressive mode\n                  try {\n                    const progressiveData: LLMResponseData = {\n                      threadId: threadId || \"\",\n                      runId,\n                      model: forwardedParameters?.model,\n                      output: event.content,\n                      latency: Date.now() - requestStartTime,\n                      timestamp: Date.now(),\n                      provider: this.detectProvider(serviceAdapter),\n                      isProgressiveChunk: true,\n                    };\n\n                    // Use Promise to handle async logger without awaiting\n                    Promise.resolve()\n                      .then(() => {\n                        this.observability.hooks.handleResponse(progressiveData);\n                      })\n                      .catch((error) => {\n                        console.error(\"Error in progressive logging:\", error);\n                      });\n                  } catch (error) {\n                    console.error(\"Error preparing progressive log data:\", error);\n                  }\n                }\n              },\n            });\n\n            // Call the original callback with the event stream\n            await callback(eventStream$);\n          });\n        };\n      }\n\n      return {\n        threadId: nonEmptyThreadId,\n        runId: result.runId,\n        eventSource,\n        serverSideActions,\n        actionInputsWithoutAgents: actionInputs.filter(\n          (action) =>\n            // TODO-AGENTS: do not exclude ALL server side actions\n            !serverSideActions.find((serverSideAction) => serverSideAction.name == action.name),\n          // !isRemoteAgentAction(\n          //   serverSideActions.find((serverSideAction) => serverSideAction.name == action.name),\n          // ),\n        ),\n        extensions: result.extensions,\n      };\n    } catch (error) {\n      // Log error if logging is enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          const errorData: LLMErrorData = {\n            threadId,\n            runId,\n            model: forwardedParameters?.model,\n            error: error instanceof Error ? error : String(error),\n            timestamp: Date.now(),\n            latency: Date.now() - requestStartTime,\n            provider: this.detectProvider(serviceAdapter),\n          };\n\n          await this.observability.hooks.handleError(errorData);\n        } catch (logError) {\n          console.error(\"Error logging LLM error:\", logError);\n        }\n      }\n\n      if (error instanceof CopilotKitError) {\n        throw error;\n      }\n      console.error(\"Error getting response:\", error);\n      eventSource.sendErrorMessageToChat();\n      throw error;\n    }\n  }\n\n  async discoverAgentsFromEndpoints(graphqlContext: GraphQLContext): Promise<AgentWithEndpoint[]> {\n    const agents: Promise<AgentWithEndpoint[]> = this.remoteEndpointDefinitions.reduce(\n      async (acc: Promise<Agent[]>, endpoint) => {\n        const agents = await acc;\n        if (endpoint.type === EndpointType.LangGraphPlatform) {\n          const propertyHeaders = graphqlContext.properties.authorization\n            ? { authorization: `Bearer ${graphqlContext.properties.authorization}` }\n            : null;\n\n          const client = new LangGraphClient({\n            apiUrl: endpoint.deploymentUrl,\n            apiKey: endpoint.langsmithApiKey,\n            defaultHeaders: { ...propertyHeaders },\n          });\n          let data: Array<{ assistant_id: string; graph_id: string }> | { detail: string } = [];\n          try {\n            data = await client.assistants.search();\n\n            if (data && \"detail\" in data && (data.detail as string).toLowerCase() === \"not found\") {\n              throw new CopilotKitAgentDiscoveryError({ availableAgents: this.availableAgents });\n            }\n          } catch (e) {\n            throw new CopilotKitMisuseError({\n              message: `\n              Failed to find or contact remote endpoint at url ${endpoint.deploymentUrl}.\n              Make sure the API is running and that it's indeed a LangGraph platform url.\n              \n              See more: https://docs.copilotkit.ai/troubleshooting/common-issues`,\n            });\n          }\n          const endpointAgents = data.map((entry) => ({\n            name: entry.graph_id,\n            id: entry.assistant_id,\n            description: \"\",\n            endpoint,\n          }));\n          return [...agents, ...endpointAgents];\n        }\n\n        interface InfoResponse {\n          agents?: Array<{\n            name: string;\n            description: string;\n          }>;\n        }\n        const cpkEndpoint = endpoint as CopilotKitEndpoint;\n        const fetchUrl = `${endpoint.url}/info`;\n        try {\n          const response = await fetch(fetchUrl, {\n            method: \"POST\",\n            headers: createHeaders(cpkEndpoint.onBeforeRequest, graphqlContext),\n            body: JSON.stringify({ properties: graphqlContext.properties }),\n          });\n          if (!response.ok) {\n            if (response.status === 404) {\n              throw new CopilotKitApiDiscoveryError({ url: fetchUrl });\n            }\n            throw new ResolvedCopilotKitError({\n              status: response.status,\n              url: fetchUrl,\n              isRemoteEndpoint: true,\n            });\n          }\n\n          const data: InfoResponse = await response.json();\n          const endpointAgents = (data?.agents ?? []).map((agent) => ({\n            name: agent.name,\n            description: agent.description ?? \"\" ?? \"\",\n            id: randomId(), // Required by Agent type\n            endpoint,\n          }));\n          return [...agents, ...endpointAgents];\n        } catch (error) {\n          if (error instanceof CopilotKitError) {\n            throw error;\n          }\n          throw new CopilotKitLowLevelError({ error: error as Error, url: fetchUrl });\n        }\n      },\n      Promise.resolve([]),\n    );\n    this.availableAgents = ((await agents) ?? []).map((a) => ({ name: a.name, id: a.id }));\n\n    return agents;\n  }\n\n  async loadAgentState(\n    graphqlContext: GraphQLContext,\n    threadId: string,\n    agentName: string,\n  ): Promise<LoadAgentStateResponse> {\n    const agentsWithEndpoints = await this.discoverAgentsFromEndpoints(graphqlContext);\n\n    const agentWithEndpoint = agentsWithEndpoints.find((agent) => agent.name === agentName);\n    if (!agentWithEndpoint) {\n      throw new Error(\"Agent not found\");\n    }\n\n    if (agentWithEndpoint.endpoint.type === EndpointType.LangGraphPlatform) {\n      const propertyHeaders = graphqlContext.properties.authorization\n        ? { authorization: `Bearer ${graphqlContext.properties.authorization}` }\n        : null;\n\n      const client = new LangGraphClient({\n        apiUrl: agentWithEndpoint.endpoint.deploymentUrl,\n        apiKey: agentWithEndpoint.endpoint.langsmithApiKey,\n        defaultHeaders: { ...propertyHeaders },\n      });\n      let state: any = {};\n      try {\n        state = (await client.threads.getState(threadId)).values as any;\n      } catch (error) {}\n\n      if (Object.keys(state).length === 0) {\n        return {\n          threadId: threadId || \"\",\n          threadExists: false,\n          state: JSON.stringify({}),\n          messages: JSON.stringify([]),\n        };\n      } else {\n        const { messages, ...stateWithoutMessages } = state;\n        const copilotkitMessages = langchainMessagesToCopilotKit(messages);\n        return {\n          threadId: threadId || \"\",\n          threadExists: true,\n          state: JSON.stringify(stateWithoutMessages),\n          messages: JSON.stringify(copilotkitMessages),\n        };\n      }\n    } else if (\n      agentWithEndpoint.endpoint.type === EndpointType.CopilotKit ||\n      !(\"type\" in agentWithEndpoint.endpoint)\n    ) {\n      const cpkEndpoint = agentWithEndpoint.endpoint as CopilotKitEndpoint;\n      const fetchUrl = `${cpkEndpoint.url}/agents/state`;\n      try {\n        const response = await fetch(fetchUrl, {\n          method: \"POST\",\n          headers: createHeaders(cpkEndpoint.onBeforeRequest, graphqlContext),\n          body: JSON.stringify({\n            properties: graphqlContext.properties,\n            threadId,\n            name: agentName,\n          }),\n        });\n        if (!response.ok) {\n          if (response.status === 404) {\n            throw new CopilotKitApiDiscoveryError({ url: fetchUrl });\n          }\n          throw new ResolvedCopilotKitError({\n            status: response.status,\n            url: fetchUrl,\n            isRemoteEndpoint: true,\n          });\n        }\n\n        const data: LoadAgentStateResponse = await response.json();\n\n        return {\n          ...data,\n          state: JSON.stringify(data.state),\n          messages: JSON.stringify(data.messages),\n        };\n      } catch (error) {\n        if (error instanceof CopilotKitError) {\n          throw error;\n        }\n        throw new CopilotKitLowLevelError({ error, url: fetchUrl });\n      }\n    } else {\n      throw new Error(`Unknown endpoint type: ${(agentWithEndpoint.endpoint as any).type}`);\n    }\n  }\n\n  private async processAgentRequest(\n    request: CopilotRuntimeRequest,\n  ): Promise<CopilotRuntimeResponse> {\n    const {\n      messages: rawMessages,\n      outputMessagesPromise,\n      graphqlContext,\n      agentSession,\n      threadId: threadIdFromRequest,\n      metaEvents,\n      publicApiKey,\n      forwardedParameters,\n    } = request;\n    const { agentName, nodeName } = agentSession;\n\n    // Track request start time for observability\n    const requestStartTime = Date.now();\n    // For storing streamed chunks if progressive logging is enabled\n    const streamedChunks: any[] = [];\n\n    // for backwards compatibility, deal with the case when no threadId is provided\n    const threadId = threadIdFromRequest ?? agentSession.threadId;\n\n    const serverSideActions = await this.getServerSideActions(request);\n\n    const messages = convertGqlInputToMessages(rawMessages);\n\n    const currentAgent = serverSideActions.find(\n      (action) => action.name === agentName && isRemoteAgentAction(action),\n    ) as RemoteAgentAction;\n\n    if (!currentAgent) {\n      throw new CopilotKitAgentDiscoveryError({ agentName, availableAgents: this.availableAgents });\n    }\n\n    // Filter actions to include:\n    // 1. Regular (non-agent) actions\n    // 2. Other agents' actions (but prevent self-calls to avoid infinite loops)\n    const availableActionsForCurrentAgent: ActionInput[] = serverSideActions\n      .filter(\n        (action) =>\n          // Case 1: Keep all regular (non-agent) actions\n          !isRemoteAgentAction(action) ||\n          // Case 2: For agent actions, keep all except self (prevent infinite loops)\n          (isRemoteAgentAction(action) && action.name !== agentName) /* prevent self-calls */,\n      )\n      .map((action) => ({\n        name: action.name,\n        description: action.description,\n        jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters)),\n      }));\n\n    const allAvailableActions = flattenToolCallsNoDuplicates([\n      ...availableActionsForCurrentAgent,\n      ...request.actions,\n    ]);\n\n    // Log agent request if observability is enabled\n    if (this.observability?.enabled && publicApiKey) {\n      try {\n        const requestData: LLMRequestData = {\n          threadId,\n          runId: undefined,\n          model: forwardedParameters?.model,\n          messages,\n          actions: allAvailableActions,\n          forwardedParameters,\n          timestamp: requestStartTime,\n          provider: \"agent\",\n          agentName, // Add agent-specific context\n          nodeName,\n        };\n\n        await this.observability.hooks.handleRequest(requestData);\n      } catch (error) {\n        console.error(\"Error logging agent request:\", error);\n      }\n    }\n\n    await this.onBeforeRequest?.({\n      threadId,\n      runId: undefined,\n      inputMessages: messages,\n      properties: graphqlContext.properties,\n    });\n\n    try {\n      const eventSource = new RuntimeEventSource();\n      const stream = await currentAgent.remoteAgentHandler({\n        name: agentName,\n        threadId,\n        nodeName,\n        metaEvents,\n        actionInputsWithoutAgents: allAvailableActions,\n      });\n\n      // Add progressive observability if enabled\n      if (this.observability?.enabled && this.observability.progressive && publicApiKey) {\n        // Wrap the stream function to intercept events for observability without changing core logic\n        const originalStream = eventSource.stream.bind(eventSource);\n\n        eventSource.stream = async (callback) => {\n          await originalStream(async (eventStream$) => {\n            // Create subscription to capture streaming events\n            eventStream$.subscribe({\n              next: (event) => {\n                // Only log content chunks\n                if (event.type === RuntimeEventTypes.TextMessageContent) {\n                  // Store the chunk\n                  streamedChunks.push(event.content);\n\n                  // Log each chunk separately for progressive mode\n                  try {\n                    const progressiveData: LLMResponseData = {\n                      threadId: threadId || \"\",\n                      runId: undefined,\n                      model: forwardedParameters?.model,\n                      output: event.content,\n                      latency: Date.now() - requestStartTime,\n                      timestamp: Date.now(),\n                      provider: \"agent\",\n                      isProgressiveChunk: true,\n                      agentName,\n                      nodeName,\n                    };\n\n                    // Use Promise to handle async logger without awaiting\n                    Promise.resolve()\n                      .then(() => {\n                        this.observability.hooks.handleResponse(progressiveData);\n                      })\n                      .catch((error) => {\n                        console.error(\"Error in progressive agent logging:\", error);\n                      });\n                  } catch (error) {\n                    console.error(\"Error preparing progressive agent log data:\", error);\n                  }\n                }\n              },\n            });\n\n            // Call the original callback with the event stream\n            await callback(eventStream$);\n          });\n        };\n      }\n\n      eventSource.stream(async (eventStream$) => {\n        from(stream).subscribe({\n          next: (event) => eventStream$.next(event),\n          error: (err) => {\n            console.error(\"Error in stream\", err);\n\n            // Log error with observability if enabled\n            if (this.observability?.enabled && publicApiKey) {\n              try {\n                const errorData: LLMErrorData = {\n                  threadId,\n                  runId: undefined,\n                  model: forwardedParameters?.model,\n                  error: err instanceof Error ? err : String(err),\n                  timestamp: Date.now(),\n                  latency: Date.now() - requestStartTime,\n                  provider: \"agent\",\n                  agentName,\n                  nodeName,\n                };\n\n                this.observability.hooks.handleError(errorData);\n              } catch (logError) {\n                console.error(\"Error logging agent error:\", logError);\n              }\n            }\n\n            eventStream$.error(err);\n            eventStream$.complete();\n          },\n          complete: () => eventStream$.complete(),\n        });\n      });\n\n      // Log final agent response when outputs are available\n      if (this.observability?.enabled && publicApiKey) {\n        outputMessagesPromise\n          .then((outputMessages) => {\n            const responseData: LLMResponseData = {\n              threadId,\n              runId: undefined,\n              model: forwardedParameters?.model,\n              // Use collected chunks for progressive mode or outputMessages for regular mode\n              output: this.observability.progressive ? streamedChunks : outputMessages,\n              latency: Date.now() - requestStartTime,\n              timestamp: Date.now(),\n              provider: \"agent\",\n              isFinalResponse: true,\n              agentName,\n              nodeName,\n            };\n\n            try {\n              this.observability.hooks.handleResponse(responseData);\n            } catch (logError) {\n              console.error(\"Error logging agent response:\", logError);\n            }\n          })\n          .catch((error) => {\n            console.error(\"Failed to get output messages for agent logging:\", error);\n          });\n      }\n\n      outputMessagesPromise\n        .then((outputMessages) => {\n          this.onAfterRequest?.({\n            threadId,\n            runId: undefined,\n            inputMessages: messages,\n            outputMessages,\n            properties: graphqlContext.properties,\n          });\n        })\n        .catch((_error) => {});\n\n      return {\n        threadId,\n        runId: undefined,\n        eventSource,\n        serverSideActions,\n        actionInputsWithoutAgents: allAvailableActions,\n      };\n    } catch (error) {\n      // Log error with observability if enabled\n      if (this.observability?.enabled && publicApiKey) {\n        try {\n          const errorData: LLMErrorData = {\n            threadId,\n            runId: undefined,\n            model: forwardedParameters?.model,\n            error: error instanceof Error ? error : String(error),\n            timestamp: Date.now(),\n            latency: Date.now() - requestStartTime,\n            provider: \"agent\",\n            agentName,\n            nodeName,\n          };\n\n          await this.observability.hooks.handleError(errorData);\n        } catch (logError) {\n          console.error(\"Error logging agent error:\", logError);\n        }\n      }\n\n      console.error(\"Error getting response:\", error);\n      throw error;\n    }\n  }\n\n  private async getServerSideActions(request: CopilotRuntimeRequest): Promise<Action<any>[]> {\n    const { graphqlContext, messages: rawMessages, agentStates, url } = request;\n\n    // --- Standard Action Fetching (unchanged) ---\n    const inputMessages = convertGqlInputToMessages(rawMessages);\n    const langserveFunctions: Action<any>[] = [];\n    for (const chainPromise of this.langserve) {\n      try {\n        const chain = await chainPromise;\n        langserveFunctions.push(chain);\n      } catch (error) {\n        console.error(\"Error loading langserve chain:\", error);\n      }\n    }\n\n    const remoteEndpointDefinitions = this.remoteEndpointDefinitions.map(\n      (endpoint) => ({ ...endpoint, type: resolveEndpointType(endpoint) }) as EndpointDefinition,\n    );\n\n    const remoteActions = await setupRemoteActions({\n      remoteEndpointDefinitions,\n      graphqlContext,\n      messages: inputMessages,\n      agentStates,\n      frontendUrl: url,\n      agents: this.agents,\n    });\n\n    const configuredActions =\n      typeof this.actions === \"function\"\n        ? this.actions({ properties: graphqlContext.properties, url })\n        : this.actions;\n    // --- Standard Action Fetching (unchanged) ---\n\n    // +++ Dynamic MCP Action Fetching +++\n    const requestSpecificMCPActions: Action<any>[] = [];\n    if (this.createMCPClientImpl) {\n      // 1. Determine effective MCP endpoints for this request\n      const baseEndpoints = this.mcpServersConfig || [];\n      // Assuming frontend passes config via properties.mcpServers\n      const requestEndpoints = (graphqlContext.properties?.mcpServers ||\n        graphqlContext.properties?.mcpEndpoints ||\n        []) as MCPEndpointConfig[];\n\n      // Merge and deduplicate endpoints based on URL\n      const effectiveEndpointsMap = new Map<string, MCPEndpointConfig>();\n\n      // First add base endpoints (from runtime configuration)\n      [...baseEndpoints].forEach((ep) => {\n        if (ep && ep.endpoint) {\n          effectiveEndpointsMap.set(ep.endpoint, ep);\n        }\n      });\n\n      // Then add request endpoints (from frontend), which will override duplicates\n      [...requestEndpoints].forEach((ep) => {\n        if (ep && ep.endpoint) {\n          effectiveEndpointsMap.set(ep.endpoint, ep);\n        }\n      });\n\n      const effectiveEndpoints = Array.from(effectiveEndpointsMap.values());\n\n      // 2. Fetch/Cache actions for effective endpoints\n      for (const config of effectiveEndpoints) {\n        const endpointUrl = config.endpoint;\n        let actionsForEndpoint: Action<any>[] | undefined = this.mcpActionCache.get(endpointUrl);\n\n        if (!actionsForEndpoint) {\n          // Not cached, fetch now\n          let client: MCPClient | null = null;\n          try {\n            client = await this.createMCPClientImpl(config);\n            const tools = await client.tools();\n            actionsForEndpoint = convertMCPToolsToActions(tools, endpointUrl);\n            this.mcpActionCache.set(endpointUrl, actionsForEndpoint); // Store in cache\n          } catch (error) {\n            console.error(\n              `MCP: Failed to fetch tools from endpoint ${endpointUrl}. Skipping. Error:`,\n              error,\n            );\n            actionsForEndpoint = []; // Assign empty array on error to prevent re-fetching constantly\n            this.mcpActionCache.set(endpointUrl, actionsForEndpoint); // Cache the failure (empty array)\n          }\n        }\n        requestSpecificMCPActions.push(...(actionsForEndpoint || []));\n      }\n    }\n    // --- Dynamic MCP Action Fetching ---\n\n    // Combine all action sources, including the dynamically fetched MCP actions\n    return [\n      ...configuredActions,\n      ...langserveFunctions,\n      ...remoteActions,\n      ...requestSpecificMCPActions,\n    ];\n  }\n\n  // Add helper method to detect provider\n  private detectProvider(serviceAdapter: CopilotServiceAdapter): string | undefined {\n    const adapterName = serviceAdapter.constructor.name;\n    if (adapterName.includes(\"OpenAI\")) return \"openai\";\n    if (adapterName.includes(\"Anthropic\")) return \"anthropic\";\n    if (adapterName.includes(\"Google\")) return \"google\";\n    if (adapterName.includes(\"Groq\")) return \"groq\";\n    if (adapterName.includes(\"LangChain\")) return \"langchain\";\n    return undefined;\n  }\n}\n\nexport function flattenToolCallsNoDuplicates(toolsByPriority: ActionInput[]): ActionInput[] {\n  let allTools: ActionInput[] = [];\n  const allToolNames: string[] = [];\n  for (const tool of toolsByPriority) {\n    if (!allToolNames.includes(tool.name)) {\n      allTools.push(tool);\n      allToolNames.push(tool.name);\n    }\n  }\n  return allTools;\n}\n\n// The two functions below are \"factory functions\", meant to create the action objects that adhere to the expected interfaces\nexport function copilotKitEndpoint(config: Omit<CopilotKitEndpoint, \"type\">): CopilotKitEndpoint {\n  return {\n    ...config,\n    type: EndpointType.CopilotKit,\n  };\n}\n\nexport function langGraphPlatformEndpoint(\n  config: Omit<LangGraphPlatformEndpoint, \"type\">,\n): LangGraphPlatformEndpoint {\n  return {\n    ...config,\n    type: EndpointType.LangGraphPlatform,\n  };\n}\n\nexport function resolveEndpointType(endpoint: EndpointDefinition) {\n  if (!endpoint.type) {\n    if (\"deploymentUrl\" in endpoint && \"agents\" in endpoint) {\n      return EndpointType.LangGraphPlatform;\n    } else {\n      return EndpointType.CopilotKit;\n    }\n  }\n\n  return endpoint.type;\n}\n", "import {\n  ActionExecutionMessage,\n  Message,\n  ResultMessage,\n  TextMessage,\n  AgentStateMessage,\n  ImageMessage,\n} from \"../graphql/types/converted\";\nimport { MessageInput } from \"../graphql/inputs/message.input\";\nimport { plainToInstance } from \"class-transformer\";\nimport { tryMap } from \"@copilotkit/shared\";\n\nexport function convertGqlInputToMessages(inputMessages: MessageInput[]): Message[] {\n  const messages = tryMap(inputMessages, (message) => {\n    if (message.textMessage) {\n      return plainToInstance(TextMessage, {\n        id: message.id,\n        createdAt: message.createdAt,\n        role: message.textMessage.role,\n        content: message.textMessage.content,\n        parentMessageId: message.textMessage.parentMessageId,\n      });\n    } else if (message.imageMessage) {\n      return plainToInstance(ImageMessage, {\n        id: message.id,\n        createdAt: message.createdAt,\n        role: message.imageMessage.role,\n        bytes: message.imageMessage.bytes,\n        format: message.imageMessage.format,\n        parentMessageId: message.imageMessage.parentMessageId,\n      });\n    } else if (message.actionExecutionMessage) {\n      return plainToInstance(ActionExecutionMessage, {\n        id: message.id,\n        createdAt: message.createdAt,\n        name: message.actionExecutionMessage.name,\n        arguments: JSON.parse(message.actionExecutionMessage.arguments),\n        parentMessageId: message.actionExecutionMessage.parentMessageId,\n      });\n    } else if (message.resultMessage) {\n      return plainToInstance(ResultMessage, {\n        id: message.id,\n        createdAt: message.createdAt,\n        actionExecutionId: message.resultMessage.actionExecutionId,\n        actionName: message.resultMessage.actionName,\n        result: message.resultMessage.result,\n      });\n    } else if (message.agentStateMessage) {\n      return plainToInstance(AgentStateMessage, {\n        id: message.id,\n        threadId: message.agentStateMessage.threadId,\n        createdAt: message.createdAt,\n        agentName: message.agentStateMessage.agentName,\n        nodeName: message.agentStateMessage.nodeName,\n        runId: message.agentStateMessage.runId,\n        active: message.agentStateMessage.active,\n        role: message.agentStateMessage.role,\n        state: JSON.parse(message.agentStateMessage.state),\n        running: message.agentStateMessage.running,\n      });\n    } else {\n      return null;\n    }\n  });\n\n  return messages.filter((m) => m);\n}\n", "import { Action, Parameter } from \"@copilotkit/shared\";\n\n/**\n * Represents a tool provided by an MCP server.\n */\nexport interface MCPTool {\n  description?: string;\n  /** Schema defining parameters, mirroring the MCP structure. */\n  schema?: {\n    parameters?: {\n      properties?: Record<string, any>;\n      required?: string[];\n      jsonSchema?: Record<string, any>;\n    };\n  };\n  /** The function to call to execute the tool on the MCP server. */\n  execute(options: { params: any }): Promise<any>;\n}\n\n/**\n * Defines the contract for *any* MCP client implementation the user might provide.\n */\nexport interface MCPClient {\n  /** A method that returns a map of tool names to MCPTool objects available from the connected MCP server. */\n  tools(): Promise<Record<string, MCPTool>>;\n  /** An optional method for cleanup if the underlying client requires explicit disconnection. */\n  close?(): Promise<void>;\n}\n\n/**\n * Configuration for connecting to an MCP endpoint.\n */\nexport interface MCPEndpointConfig {\n  endpoint: string;\n  apiKey?: string;\n}\n\n/**\n * Extracts CopilotKit-compatible parameters from an MCP tool schema.\n * @param toolOrSchema The schema object from an MCPTool or the full MCPTool object.\n * @returns An array of Parameter objects.\n */\nexport function extractParametersFromSchema(\n  toolOrSchema?: MCPTool | MCPTool[\"schema\"],\n): Parameter[] {\n  const parameters: Parameter[] = [];\n\n  // Handle either full tool object or just schema\n  const schema =\n    \"schema\" in (toolOrSchema || {})\n      ? (toolOrSchema as MCPTool).schema\n      : (toolOrSchema as MCPTool[\"schema\"]);\n\n  const toolParameters = schema?.parameters || schema?.parameters?.jsonSchema;\n  const properties = toolParameters?.properties;\n  const requiredParams = new Set(toolParameters?.required || []);\n\n  if (!properties) {\n    return parameters;\n  }\n\n  for (const paramName in properties) {\n    if (Object.prototype.hasOwnProperty.call(properties, paramName)) {\n      const paramDef = properties[paramName];\n      parameters.push({\n        name: paramName,\n        // Infer type, default to string. MCP schemas might have more complex types.\n        // This might need refinement based on common MCP schema practices.\n        type: paramDef.type || \"string\",\n        description: paramDef.description,\n        required: requiredParams.has(paramName),\n        // Attributes might not directly map, handle if necessary\n        // attributes: paramDef.attributes || undefined,\n      });\n    }\n  }\n\n  return parameters;\n}\n\n/**\n * Converts a map of MCPTools into an array of CopilotKit Actions.\n * @param mcpTools A record mapping tool names to MCPTool objects.\n * @param mcpEndpoint The endpoint URL from which these tools were fetched.\n * @returns An array of Action<any> objects.\n */\nexport function convertMCPToolsToActions(\n  mcpTools: Record<string, MCPTool>,\n  mcpEndpoint: string,\n): Action<any>[] {\n  const actions: Action<any>[] = [];\n\n  for (const [toolName, tool] of Object.entries(mcpTools)) {\n    const parameters = extractParametersFromSchema(tool);\n\n    const handler = async (params: any): Promise<any> => {\n      try {\n        const result = await tool.execute({ params });\n        // Ensure the result is a string or stringify it, as required by many LLMs.\n        // This might need adjustment depending on how different LLMs handle tool results.\n        return typeof result === \"string\" ? result : JSON.stringify(result);\n      } catch (error) {\n        console.error(\n          `Error executing MCP tool '${toolName}' from endpoint ${mcpEndpoint}:`,\n          error,\n        );\n        // Re-throw or format the error for the LLM\n        throw new Error(\n          `Execution failed for MCP tool '${toolName}': ${\n            error instanceof Error ? error.message : String(error)\n          }`,\n        );\n      }\n    };\n\n    actions.push({\n      name: toolName,\n      description: tool.description || `MCP tool: ${toolName} (from ${mcpEndpoint})`,\n      parameters: parameters,\n      handler: handler,\n      // Add metadata for easier identification/debugging\n      _isMCPTool: true,\n      _mcpEndpoint: mcpEndpoint,\n    } as Action<any> & { _isMCPTool: boolean; _mcpEndpoint: string }); // Type assertion for metadata\n  }\n\n  return actions;\n}\n\n/**\n * Generate better instructions for using MCP tools\n * This is used to enhance the system prompt with tool documentation\n */\nexport function generateMcpToolInstructions(toolsMap: Record<string, MCPTool>): string {\n  if (!toolsMap || Object.keys(toolsMap).length === 0) {\n    return \"\";\n  }\n\n  const toolEntries = Object.entries(toolsMap);\n\n  // Generate documentation for each tool\n  const toolsDoc = toolEntries\n    .map(([name, tool]) => {\n      // Extract schema information if available\n      let paramsDoc = \"    No parameters required\";\n\n      try {\n        if (tool.schema && typeof tool.schema === \"object\") {\n          const schema = tool.schema as any;\n\n          // Extract parameters from JSON Schema\n          if (schema.properties) {\n            const requiredParams = schema.required || [];\n\n            // Build parameter documentation from properties\n            const paramsList = Object.entries(schema.properties).map(([paramName, propSchema]) => {\n              const propDetails = propSchema as any;\n              const requiredMark = requiredParams.includes(paramName) ? \"*\" : \"\";\n              const typeInfo = propDetails.type || \"any\";\n              const description = propDetails.description ? ` - ${propDetails.description}` : \"\";\n\n              return `    - ${paramName}${requiredMark} (${typeInfo})${description}`;\n            });\n\n            if (paramsList.length > 0) {\n              paramsDoc = paramsList.join(\"\\n\");\n            }\n          }\n        }\n      } catch (e) {\n        console.error(`Error parsing schema for tool ${name}:`, e);\n      }\n\n      return `- ${name}: ${tool.description || \"\"}\n${paramsDoc}`;\n    })\n    .join(\"\\n\\n\");\n\n  return `You have access to the following external tools provided by Model Context Protocol (MCP) servers:\n\n${toolsDoc}\n\nWhen using these tools:\n1. Only provide valid parameters according to their type requirements\n2. Required parameters are marked with *\n3. Format API calls correctly with the expected parameter structure\n4. Always check tool responses to determine your next action`;\n}\n", "import { Field, ObjectType } from \"type-graphql\";\n\n@ObjectType()\nexport class Agent {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => String)\n  name: string;\n\n  @Field(() => String)\n  description?: string;\n}\n\n@ObjectType()\nexport class AgentsResponse {\n  @Field(() => [Agent])\n  agents: Agent[];\n}\n", "import createPinoLogger from \"pino\";\nimport pretty from \"pino-pretty\";\n\nexport type LogLevel = \"debug\" | \"info\" | \"warn\" | \"error\";\n\nexport type CopilotRuntimeLogger = ReturnType<typeof createLogger>;\n\nexport function createLogger(options?: { level?: LogLevel; component?: string }) {\n  const { level, component } = options || {};\n  const stream = pretty({ colorize: true });\n\n  const logger = createPinoLogger(\n    {\n      level: process.env.LOG_LEVEL || level || \"error\",\n      redact: {\n        paths: [\"pid\", \"hostname\"],\n        remove: true,\n      },\n    },\n    stream,\n  );\n\n  if (component) {\n    return logger.child({ component });\n  } else {\n    return logger;\n  }\n}\n", "import { Arg, Resolver } from \"type-graphql\";\nimport { Ctx } from \"type-graphql\";\nimport { Query } from \"type-graphql\";\nimport { LoadAgentStateResponse } from \"../types/load-agent-state-response.type\";\nimport type { GraphQLContext } from \"../../lib/integrations\";\nimport { LoadAgentStateInput } from \"../inputs/load-agent-state.input\";\n\n@Resolver(() => LoadAgentStateResponse)\nexport class StateResolver {\n  @Query(() => LoadAgentStateResponse)\n  async loadAgentState(@Ctx() ctx: GraphQLContext, @Arg(\"data\") data: LoadAgentStateInput) {\n    const agents = await ctx._copilotkit.runtime.discoverAgentsFromEndpoints(ctx);\n    const agent = agents.find((agent) => agent.name === data.agentName);\n\n    if (!agent) {\n      return {\n        threadId: data.threadId || \"\",\n        threadExists: false,\n        state: JSON.stringify({}),\n        messages: JSON.stringify([]),\n      };\n    }\n\n    const state = await ctx._copilotkit.runtime.loadAgentState(ctx, data.threadId, data.agentName);\n\n    return state;\n  }\n}\n", "import { Field, ObjectType } from \"type-graphql\";\nimport { BaseMessageOutput } from \"./copilot-response.type\";\n\n@ObjectType()\nexport class LoadAgentStateResponse {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => Boolean)\n  threadExists: boolean;\n\n  @Field(() => String)\n  state: string;\n\n  @Field(() => String)\n  messages: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class LoadAgentStateInput {\n  @Field(() => String)\n  threadId: string;\n\n  @Field(() => String)\n  agentName: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,UAAY;AAAA,MACZ,YAAc;AAAA,QACZ,MAAQ;AAAA,QACR,KAAO;AAAA,MACT;AAAA,MACA,eAAiB;AAAA,QACf,QAAU;AAAA,MACZ;AAAA,MACA,SAAW;AAAA,MACX,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,QAAU;AAAA,MACV,SAAW;AAAA,QACT,KAAK;AAAA,MACP;AAAA,MACA,OAAS;AAAA,MACT,SAAW;AAAA,MACX,SAAW;AAAA,QACT,OAAS;AAAA,QACT,KAAO;AAAA,QACP,MAAQ;AAAA,QACR,eAAe;AAAA,QACf,OAAS;AAAA,QACT,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAmB;AAAA,QACjB,iBAAiB;AAAA,QACjB,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,eAAe;AAAA,QACf,uBAAuB;AAAA,QACvB,QAAU;AAAA,QACV,wBAAwB;AAAA,QACxB,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAY;AAAA,QACZ,MAAQ;AAAA,QACR,YAAc;AAAA,QACd,sBAAsB;AAAA,MACxB;AAAA,MACA,cAAgB;AAAA,QACd,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qCAAqC;AAAA,QACrC,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,4BAA4B;AAAA,QAC5B,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,SAAW;AAAA,QACX,SAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,WAAa;AAAA,QACb,QAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,MAAQ;AAAA,QACR,eAAe;AAAA,QACf,oBAAoB;AAAA,QACpB,MAAQ;AAAA,QACR,gBAAgB;AAAA,QAChB,KAAO;AAAA,MACT;AAAA,MACA,kBAAoB;AAAA,QAClB,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAY;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AChGA,SAASA,kBAAkB;;;ACC3B,SAASC,uBAAuB;;;ACDhC,SAASC,KAAKC,KAAKC,UAAUC,OAAOC,gBAAgB;AACpD,SACEC,iBAAAA,gBACAC,SAEAC,QACAC,UACAC,kBAAAA,iBACAC,aACAC,WACAC,MACAC,WACAC,WACK;;;ACbP,SAASC,SAAAA,SAAOC,aAAAA,mBAAiB;;;ACAjC,SAASC,OAAOC,iBAAiB;;;ACAjC,SAASC,wBAAwB;;UAErBC,cAAAA;;;;;;GAAAA,gBAAAA,cAAAA,CAAAA,EAAAA;;UAQAC,qBAAAA;;;;;;GAAAA,uBAAAA,qBAAAA,CAAAA,EAAAA;;UAQAC,0BAAAA;;;;GAAAA,4BAAAA,0BAAAA,CAAAA,EAAAA;AAMZH,iBAAiBC,aAAa;EAC5BG,MAAM;EACNC,aAAa;AACf,CAAA;AAEAL,iBAAiBE,oBAAoB;EACnCE,MAAM;EACNC,aAAa;AACf,CAAA;AAEAL,iBAAiBG,yBAAyB;EACxCC,MAAM;EACNC,aAAa;AACf,CAAA;;;;;;;;;;;;;;;;;;;AD9BO,IAAMC,eAAN,cAA2BC,iBAAAA;EAEhCC;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AAfaN;;EACVO,MAAM,MAAMC,kBAAkB;IAAEC,UAAU;EAAK,CAAA;qCAClC,qBAAA,cAAA,SAAA,gBAAA;GAFHT,aAAAA,WAAAA,eAAAA,MAAAA;;EAIVO,MAAM,MAAMG,6BAA6B;IAAED,UAAU;EAAK,CAAA;qCAClC,gCAAA,cAAA,SAAA,2BAAA;GALdT,aAAAA,WAAAA,0BAAAA,MAAAA;;EAOVO,MAAM,MAAMI,oBAAoB;IAAEF,UAAU;EAAK,CAAA;qCAClC,uBAAA,cAAA,SAAA,kBAAA;GARLT,aAAAA,WAAAA,iBAAAA,MAAAA;;EAUVO,MAAM,MAAMK,wBAAwB;IAAEH,UAAU;EAAK,CAAA;qCAClC,2BAAA,cAAA,SAAA,sBAAA;GAXTT,aAAAA,WAAAA,qBAAAA,MAAAA;;EAaVO,MAAM,MAAMM,mBAAmB;IAAEJ,UAAU;EAAK,CAAA;qCAClC,sBAAA,cAAA,SAAA,iBAAA;GAdJT,aAAAA,WAAAA,gBAAAA,MAAAA;AAAAA,eAAAA,aAAAA;EADZc,UAAAA;GACYd,YAAAA;AAkBN,IAAMQ,mBAAN,MAAMA;EAEXO;EAGAC;EAGAC;AACF;AATaT;;EACVD,MAAM,MAAMW,MAAAA;;GADFV,iBAAAA,WAAAA,WAAAA,MAAAA;;EAIVD,MAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAJ3BD,iBAAAA,WAAAA,mBAAAA,MAAAA;;EAOVD,MAAM,MAAMY,WAAAA;qCACP,gBAAA,cAAA,SAAA,WAAA;GARKX,iBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,mBAAAA,aAAAA;EADZM,UAAAA;GACYN,gBAAAA;AAYN,IAAME,8BAAN,MAAMA;EAEXU;EAGAC;EAGAL;EAMAM;AACF;AAfaZ;;EACVH,MAAM,MAAMW,MAAAA;;GADFR,4BAAAA,WAAAA,QAAAA,MAAAA;;EAIVH,MAAM,MAAMW,MAAAA;;GAJFR,4BAAAA,WAAAA,aAAAA,MAAAA;;EAOVH,MAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAP3BC,4BAAAA,WAAAA,mBAAAA,MAAAA;;EAUVH,MAAM,MAAMW,QAAQ;IACnBT,UAAU;IACVc,mBAAmB;EACrB,CAAA;qCACQ,WAAA,cAAA,SAAA,MAAA;GAdGb,4BAAAA,WAAAA,SAAAA,MAAAA;AAAAA,8BAAAA,aAAAA;EADZI,UAAAA;GACYJ,2BAAAA;AAkBN,IAAMC,qBAAN,MAAMA;EAEXa;EAGAC;EAGAT;EAGAU;AACF;AAZaf;;EACVJ,MAAM,MAAMW,MAAAA;;GADFP,mBAAAA,WAAAA,qBAAAA,MAAAA;;EAIVJ,MAAM,MAAMW,MAAAA;;GAJFP,mBAAAA,WAAAA,cAAAA,MAAAA;;EAOVJ,MAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAP3BE,mBAAAA,WAAAA,mBAAAA,MAAAA;;EAUVJ,MAAM,MAAMW,MAAAA;;GAVFP,mBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,qBAAAA,aAAAA;EADZG,UAAAA;GACYH,kBAAAA;AAeN,IAAMC,yBAAN,MAAMA;EAEXe;EAGAC;EAGAX;EAGAY;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AAxBarB;;EACVL,MAAM,MAAMW,MAAAA;;GADFN,uBAAAA,WAAAA,YAAAA,MAAAA;;EAIVL,MAAM,MAAMW,MAAAA;;GAJFN,uBAAAA,WAAAA,aAAAA,MAAAA;;EAOVL,MAAM,MAAMY,WAAAA;qCACP,gBAAA,cAAA,SAAA,WAAA;GARKP,uBAAAA,WAAAA,QAAAA,MAAAA;;EAUVL,MAAM,MAAMW,MAAAA;;GAVFN,uBAAAA,WAAAA,SAAAA,MAAAA;;EAaVL,MAAM,MAAM2B,OAAAA;;GAbFtB,uBAAAA,WAAAA,WAAAA,MAAAA;;EAgBVL,MAAM,MAAMW,MAAAA;;GAhBFN,uBAAAA,WAAAA,YAAAA,MAAAA;;EAmBVL,MAAM,MAAMW,MAAAA;;GAnBFN,uBAAAA,WAAAA,SAAAA,MAAAA;;EAsBVL,MAAM,MAAM2B,OAAAA;;GAtBFtB,uBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,yBAAAA,aAAAA;EADZE,UAAAA;GACYF,sBAAAA;AA2BN,IAAMC,oBAAN,MAAMA;EAEXsB;EAGAC;EAGApB;EAGAC;AACF;AAZaJ;;EACVN,MAAM,MAAMW,MAAAA;;GADFL,kBAAAA,WAAAA,UAAAA,MAAAA;;EAIVN,MAAM,MAAMW,MAAAA;;GAJFL,kBAAAA,WAAAA,SAAAA,MAAAA;;EAOVN,MAAM,MAAMW,QAAQ;IAAET,UAAU;EAAK,CAAA;;GAP3BI,kBAAAA,WAAAA,mBAAAA,MAAAA;;EAUVN,MAAM,MAAMY,WAAAA;qCACP,gBAAA,cAAA,SAAA,WAAA;GAXKN,kBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,oBAAAA,aAAAA;EADZC,UAAAA;GACYD,iBAAAA;;;AEjGb,SAASwB,SAAAA,QAAOC,aAAAA,kBAAiB;;;ACAjC,SAASC,SAAAA,QAAOC,aAAAA,kBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,cAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;AACF;AAZaJ;;EACVK,OAAM,MAAMC,MAAAA;;GADFN,YAAAA,WAAAA,QAAAA,MAAAA;;EAIVK,OAAM,MAAMC,MAAAA;;GAJFN,YAAAA,WAAAA,eAAAA,MAAAA;;EAOVK,OAAM,MAAMC,MAAAA;;GAPFN,YAAAA,WAAAA,cAAAA,MAAAA;;EAUVK,OAAM,MAAME,yBAAyB;IAAEC,UAAU;EAAK,CAAA;sCAC3C,4BAAA,cAAA,SAAA,uBAAA;GAXDR,YAAAA,WAAAA,aAAAA,MAAAA;AAAAA,cAAAA,cAAAA;EADZS,WAAAA;GACYT,WAAAA;;;;;;;;;;;;;;;;;;;ADCN,IAAMU,gBAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;EACVI,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAD3BN,cAAAA,WAAAA,2BAAAA,MAAAA;;EAIVI,OAAM,MAAM;IAACG;GAAY;;GAJfP,cAAAA,WAAAA,WAAAA,MAAAA;;EAOVI,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BN,cAAAA,WAAAA,OAAAA,MAAAA;AAAAA,gBAAAA,cAAAA;EADZQ,WAAAA;GACYR,aAAAA;;;AEJb,SAASS,SAAAA,QAAOC,aAAAA,kBAAiB;;;ACAjC,SAASC,SAAAA,QAAOC,aAAAA,kBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,sBAAN,MAAMA;EAEXC,YAAuB,CAAA;EAGvBC,WAAsB,CAAA;AACxB;AANaF;;EACVG,OAAM,MAAM;IAACC;KAAS;IAAEC,UAAU;EAAK,CAAA;;GAD7BL,oBAAAA,WAAAA,aAAAA,MAAAA;;EAIVG,OAAM,MAAM;IAACC;KAAS;IAAEC,UAAU;EAAK,CAAA;;GAJ7BL,oBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,sBAAAA,cAAAA;EADZM,WAAAA;GACYN,mBAAAA;AASN,IAAMO,kBAAN,MAAMA;EAEXC;AACF;AAHaD;;EACVJ,OAAM,MAAMH,qBAAqB;IAAEK,UAAU;EAAM,CAAA;sCAC9B,wBAAA,cAAA,SAAA,mBAAA;GAFXE,gBAAAA,WAAAA,wBAAAA,MAAAA;AAAAA,kBAAAA,cAAAA;EADZD,WAAAA;GACYC,eAAAA;;;;;;;;;;;;;;;;;;;ADRN,IAAME,aAAN,MAAMA;EAEXC;AACF;AAHaD;;EACVE,OAAM,MAAMC,iBAAiB;IAAEC,UAAU;EAAK,CAAA;sCAClC,oBAAA,cAAA,SAAA,eAAA;GAFFJ,WAAAA,WAAAA,cAAAA,MAAAA;AAAAA,aAAAA,cAAAA;EADZK,WAAAA;GACYL,UAAAA;;;AEJb,SAASM,SAAAA,QAAOC,aAAAA,kBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,2BAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AAlBaN;;EACVO,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAD3BT,yBAAAA,WAAAA,SAAAA,MAAAA;;EAIVO,OAAM,MAAMG,QAAQ;IAAED,UAAU;EAAK,CAAA;;GAJ3BT,yBAAAA,WAAAA,aAAAA,MAAAA;;EAOVO,OAAM,MAAM;IAACC;KAAS;IAAEC,UAAU;EAAK,CAAA;;GAP7BT,yBAAAA,WAAAA,QAAAA,MAAAA;;EAUVO,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;sCACzB,WAAA,cAAA,SAAA,MAAA;GAXFT,yBAAAA,WAAAA,cAAAA,MAAAA;;EAaVO,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAb3BT,yBAAAA,WAAAA,0BAAAA,MAAAA;;EAgBVO,OAAM,MAAMG,QAAQ;IAAED,UAAU;EAAK,CAAA;;GAhB3BT,yBAAAA,WAAAA,eAAAA,MAAAA;AAAAA,2BAAAA,cAAAA;EADZW,WAAAA;GACYX,wBAAAA;;;ACHb,SAASY,SAAAA,QAAOC,aAAAA,kBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,oBAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;EACVI,OAAM,MAAMC,MAAAA;;GADFL,kBAAAA,WAAAA,aAAAA,MAAAA;;EAIVI,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAJ3BN,kBAAAA,WAAAA,YAAAA,MAAAA;;EAOVI,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BN,kBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,oBAAAA,cAAAA;EADZO,WAAAA;GACYP,iBAAAA;;;ACHb,SAASQ,SAAAA,QAAOC,aAAAA,kBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,kBAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;EACVI,OAAM,MAAMC,MAAAA;;GADFL,gBAAAA,WAAAA,aAAAA,MAAAA;;EAIVI,OAAM,MAAMC,MAAAA;;GAJFL,gBAAAA,WAAAA,SAAAA,MAAAA;;EAOVI,OAAM,MAAMC,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BN,gBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,kBAAAA,cAAAA;EADZO,WAAAA;GACYP,eAAAA;;;ACHb,SAASQ,SAAAA,QAAOC,aAAAA,kBAAiB;;;;;;;;;;;;;;;;;AAQ1B,IAAMC,kBAAN,MAAMA;EAEXC;AACF;AAHaD;;EACVE,OAAM,MAAMC,4BAA4B;IAAEC,UAAU;EAAK,CAAA;sCACrC,+BAAA,cAAA,SAAA,0BAAA;GAFVJ,gBAAAA,WAAAA,sBAAAA,MAAAA;AAAAA,kBAAAA,cAAAA;EADZK,WAAAA;GACYL,eAAAA;AAMN,IAAMG,6BAAN,MAAMA;EAEXG;EAGAC;AACF;AANaJ;;EACVD,OAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAD3BD,2BAAAA,WAAAA,SAAAA,MAAAA;;EAIVD,OAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAJ3BD,2BAAAA,WAAAA,YAAAA,MAAAA;AAAAA,6BAAAA,cAAAA;EADZE,WAAAA;GACYF,0BAAAA;;;ACdb,SAASM,SAAAA,SAAOC,aAAAA,mBAAiB;;;ACAjC,SAA0BC,SAAAA,SAAOC,iBAAAA,gBAAeC,cAAAA,aAAYC,oBAAAA,yBAAwB;;;ACApF,SAASC,SAAAA,SAAOC,eAAeC,cAAAA,mBAAkB;;;ACAjD,SAASC,SAAAA,SAAOC,YAAYC,iBAAiBC,oBAAAA,yBAAwB;;;;;;;;;;;;;;;;;;UAEzDC,oBAAAA;;;;GAAAA,sBAAAA,oBAAAA,CAAAA,EAAAA;AAMZC,kBAAiBD,mBAAmB;EAClCE,MAAM;AACR,CAAA;AAEA,IACMC,oBADN,6BACMA,mBAAAA;EAEJC;AACF,GAJA;;EAEGC,QAAM,MAAML,iBAAAA;;GADTG,kBAAAA,WAAAA,QAAAA,MAAAA;AAAAA,oBAAAA,eAAAA;EADLG,WAAAA;GACKH,iBAAAA;AAMC,IAAMI,uBAAN,cAAmCJ,kBAAAA;EACxCC,OAAAA;AACF;AAFaG;AAAAA,uBAAAA,eAAAA;EADZD,WAAAA;GACYC,oBAAAA;AAKN,IAAMC,uBAAN,cAAmCL,kBAAAA;EACxCC,OAAAA;AACF;AAFaI;AAAAA,uBAAAA,eAAAA;EADZF,WAAAA;GACYE,oBAAAA;AAKN,IAAMC,sBAAN,cAAkCN,kBAAAA;EACvCC,OAAAA;EAGAM;AACF;AALaD;;EAGVJ,QAAM,MAAMM,MAAAA;;GAHFF,oBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,sBAAAA,eAAAA;EADZH,WAAAA;GACYG,mBAAAA;AAON,IAAMG,qBAAqBC,gBAAgB;EAChDX,MAAM;EACNY,OAAO,MAAM;IAACP;IAAsBC;IAAsBC;;AAC5D,CAAA;;;ACvCA,SAASM,SAAAA,SAAOC,cAAAA,mBAAkB;;;;;;;;;;;;;;;;;AAU3B,IAAMC,qBAAN,MAAMA;EAEXC;AACF;AAHaD;;EACVE,QAAM,MAAMC,+BAA+B;IAAEC,UAAU;EAAK,CAAA;uCACxC,kCAAA,cAAA,SAAA,6BAAA;GAFVJ,mBAAAA,WAAAA,sBAAAA,MAAAA;AAAAA,qBAAAA,eAAAA;EADZK,YAAAA;GACYL,kBAAAA;AAMN,IAAMG,gCAAN,MAAMA;EAEXG;EAGAC;AACF;AANaJ;;EACVD,QAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAD3BD,8BAAAA,WAAAA,SAAAA,MAAAA;;EAIVD,QAAM,MAAMM,QAAQ;IAAEJ,UAAU;EAAK,CAAA;;GAJ3BD,8BAAAA,WAAAA,YAAAA,MAAAA;AAAAA,gCAAAA,eAAAA;EADZE,YAAAA;GACYF,6BAAAA;;;;;;;;;;;;;;;;;;;AFON,IAAeM,oBAAf,MAAeA;EAEpBC;EAGAC;EAGAC;AACF;AATsBH;;EACnBI,QAAM,MAAMC,MAAAA;;GADOL,kBAAAA,WAAAA,MAAAA,MAAAA;;EAInBI,QAAM,MAAME,IAAAA;uCACF,SAAA,cAAA,SAAA,IAAA;GALSN,kBAAAA,WAAAA,aAAAA,MAAAA;;EAOnBI,QAAM,MAAMG,kBAAAA;;GAPOP,kBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,oBAAAA,eAAAA;EAhBrBQ,cAAc;IACbC,YAAYC,OAAK;AACf,UAAIA,MAAMC,eAAe,SAAA,GAAY;AACnC,eAAOC;MACT,WAAWF,MAAMC,eAAe,MAAA,GAAS;AACvC,eAAOE;MACT,WAAWH,MAAMC,eAAe,QAAA,GAAW;AACzC,eAAOG;MACT,WAAWJ,MAAMC,eAAe,OAAA,GAAU;AACxC,eAAOI;MACT,WAAWL,MAAMC,eAAe,QAAA,KAAaD,MAAMC,eAAe,OAAA,GAAU;AAC1E,eAAOK;MACT;AACA,aAAOC;IACT;EACF,CAAA;GACsBjB,iBAAAA;AAYf,IAAMY,oBAAN,MAAMA;EAEXM;EAGAC;EAGAC;AACF;AATaR;;EACVR,QAAM,MAAMiB,WAAAA;uCACP,gBAAA,cAAA,SAAA,WAAA;GAFKT,kBAAAA,WAAAA,QAAAA,MAAAA;;EAIVR,QAAM,MAAM;IAACC;GAAO;;GAJVO,kBAAAA,WAAAA,WAAAA,MAAAA;;EAOVR,QAAM,MAAMC,QAAQ;IAAEiB,UAAU;EAAK,CAAA;;GAP3BV,kBAAAA,WAAAA,mBAAAA,MAAAA;AAAAA,oBAAAA,eAAAA;EADZW,YAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/BY,iBAAAA;AAYN,IAAMC,+BAAN,MAAMA;EAEXY;EAMAC;EAGAC;EAGAP;AACF;AAfaP;;EACVT,QAAM,MAAMC,MAAAA;;GADFQ,6BAAAA,WAAAA,QAAAA,MAAAA;;EAIVT,QAAM,MAAMC,QAAQ;IACnBiB,UAAU;IACVM,mBAAmB;EACrB,CAAA;;GAPWf,6BAAAA,WAAAA,SAAAA,MAAAA;;EAUVT,QAAM,MAAM;IAACC;GAAO;;GAVVQ,6BAAAA,WAAAA,aAAAA,MAAAA;;EAaVT,QAAM,MAAMC,QAAQ;IAAEiB,UAAU;EAAK,CAAA;;GAb3BT,6BAAAA,WAAAA,mBAAAA,MAAAA;AAAAA,+BAAAA,eAAAA;EADZU,YAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/Ba,4BAAAA;AAkBN,IAAMC,sBAAN,MAAMA;EAEXe;EAGAC;EAGAC;AACF;AATajB;;EACVV,QAAM,MAAMC,MAAAA;;GADFS,oBAAAA,WAAAA,qBAAAA,MAAAA;;EAIVV,QAAM,MAAMC,MAAAA;;GAJFS,oBAAAA,WAAAA,cAAAA,MAAAA;;EAOVV,QAAM,MAAMC,MAAAA;;GAPFS,oBAAAA,WAAAA,UAAAA,MAAAA;AAAAA,sBAAAA,eAAAA;EADZS,YAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/Bc,mBAAAA;AAYN,IAAMC,0BAAN,MAAMA;EAEXiB;EAGAC;EAGAC;EAGAC;EAGAC;EAGAlB;EAGAmB;EAGAC;AACF;AAxBavB;;EACVX,QAAM,MAAMC,MAAAA;;GADFU,wBAAAA,WAAAA,YAAAA,MAAAA;;EAIVX,QAAM,MAAMC,MAAAA;;GAJFU,wBAAAA,WAAAA,aAAAA,MAAAA;;EAOVX,QAAM,MAAMC,MAAAA;;GAPFU,wBAAAA,WAAAA,YAAAA,MAAAA;;EAUVX,QAAM,MAAMC,MAAAA;;GAVFU,wBAAAA,WAAAA,SAAAA,MAAAA;;EAaVX,QAAM,MAAMmC,OAAAA;;GAbFxB,wBAAAA,WAAAA,UAAAA,MAAAA;;EAgBVX,QAAM,MAAMiB,WAAAA;uCACP,gBAAA,cAAA,SAAA,WAAA;GAjBKN,wBAAAA,WAAAA,QAAAA,MAAAA;;EAmBVX,QAAM,MAAMC,MAAAA;;GAnBFU,wBAAAA,WAAAA,SAAAA,MAAAA;;EAsBVX,QAAM,MAAMmC,OAAAA;;GAtBFxB,wBAAAA,WAAAA,WAAAA,MAAAA;AAAAA,0BAAAA,eAAAA;EADZQ,YAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/Be,uBAAAA;AA2BN,IAAMC,qBAAN,MAAMA;EAEXwB;EAGAC;EAGAvB;EAGAE;AACF;AAZaJ;;EACVZ,QAAM,MAAMC,MAAAA;;GADFW,mBAAAA,WAAAA,UAAAA,MAAAA;;EAIVZ,QAAM,MAAMC,MAAAA;;GAJFW,mBAAAA,WAAAA,SAAAA,MAAAA;;EAOVZ,QAAM,MAAMiB,WAAAA;uCACP,gBAAA,cAAA,SAAA,WAAA;GARKL,mBAAAA,WAAAA,QAAAA,MAAAA;;EAUVZ,QAAM,MAAMC,QAAQ;IAAEiB,UAAU;EAAK,CAAA;;GAV3BN,mBAAAA,WAAAA,mBAAAA,MAAAA;AAAAA,qBAAAA,eAAAA;EADZO,YAAW;IAAEC,YAAYxB;EAAkB,CAAA;GAC/BgB,kBAAAA;AAeN,IAAM0B,kBAAN,MAAMA;EAEXV;EAGA7B;EAGAgC;EAGAQ;EAGAC;EAGAC;AACF;AAlBaH;;EACVtC,QAAM,MAAMC,MAAAA;;GADFqC,gBAAAA,WAAAA,YAAAA,MAAAA;;EAIVtC,QAAM,MAAM0C,mBAAAA;;GAJFJ,gBAAAA,WAAAA,UAAAA,MAAAA;;EAOVtC,QAAM;IAAEkB,UAAU;EAAK,CAAA;;GAPboB,gBAAAA,WAAAA,SAAAA,MAAAA;;EAUVtC,QAAM,MAAM;IAACJ;GAAkB;;GAVrB0C,gBAAAA,WAAAA,YAAAA,MAAAA;;EAaVtC,QAAM,MAAM2C,oBAAoB;IAAEzB,UAAU;EAAK,CAAA;uCACrC,uBAAA,cAAA,SAAA,kBAAA;GAdFoB,gBAAAA,WAAAA,cAAAA,MAAAA;;EAgBVtC,QAAM,MAAM;IAAC4C;KAAgB;IAAE1B,UAAU;EAAK,CAAA;;GAhBpCoB,gBAAAA,WAAAA,cAAAA,MAAAA;AAAAA,kBAAAA,eAAAA;EADZnB,YAAAA;GACYmB,eAAAA;;;;;;;;;;;;;;;;;;;;UD9GDO,gBAAAA;;;GAAAA,kBAAAA,gBAAAA,CAAAA,EAAAA;AAKZC,kBAAiBD,eAAe;EAC9BE,MAAM;EACNC,aAAa;AACf,CAAA;AAaO,IAAeC,gBAAf,MAAeA;EAEpBC,OAAoB;EAGpBH;AACF;AANsBE;;EACnBE,QAAM,MAAMC,MAAAA;;GADOH,cAAAA,WAAAA,QAAAA,MAAAA;;EAInBE,QAAM,MAAMN,aAAAA;;GAJOI,cAAAA,WAAAA,QAAAA,MAAAA;AAAAA,gBAAAA,eAAAA;EAXrBI,eAAc;IACbC,YAAYC,OAAK;AACf,UAAIA,MAAMR,SAAI,2BAA4C;AACxD,eAAOS;MACT,WAAWD,MAAMR,SAAI,qCAAsD;AACzE,eAAOU;MACT;AACA,aAAOC;IACT;EACF,CAAA;EACCL,eAAAA;GACqBJ,aAAAA;AASf,IAAMU,wCAAN,MAAMA;EAEXJ;EAGAK;AACF;AANaD;;EACVR,QAAM,MAAMC,MAAAA;;GADFO,sCAAAA,WAAAA,SAAAA,MAAAA;;EAIVR,QAAM,MAAM;IAACU;GAAkB;;GAJrBF,sCAAAA,WAAAA,YAAAA,MAAAA;AAAAA,wCAAAA,eAAAA;EADZG,YAAAA;GACYH,qCAAAA;AASN,IAAMH,0BAAN,MAAMA;EAEXT,OAAAA;EAGAQ;EAGAQ;AACF;AATaP;;EACVL,QAAM,MAAMN,aAAAA;uCACP,kBAAA,eAAA,QAAA,SAAA,yBAAA;GAFKW,wBAAAA,WAAAA,QAAAA,MAAAA;;EAIVL,QAAM,MAAMC,MAAAA;;GAJFI,wBAAAA,WAAAA,SAAAA,MAAAA;;EAOVL,QAAM,MAAMC,QAAQ;IAAEY,UAAU;EAAK,CAAA;;GAP3BR,wBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,0BAAAA,eAAAA;EADZM,YAAW;IAAEG,YAAYhB;EAAc,CAAA;GAC3BO,uBAAAA;AAYN,IAAMC,oCAAN,MAAMA;EAEXV,OAAAA;EAIAmB;EAGAH;AACF;AAVaN;;EACVN,QAAM,MAAMN,aAAAA;uCACP,kBAAA,eAAA,QAAA,SAAA,mCAAA;GAFKY,kCAAAA,WAAAA,QAAAA,MAAAA;;EAKVN,QAAM,MAAMQ,qCAAAA;uCACP,0CAAA,cAAA,SAAA,qCAAA;GANKF,kCAAAA,WAAAA,QAAAA,MAAAA;;EAQVN,QAAM,MAAMC,QAAQ;IAAEY,UAAU;EAAK,CAAA;;GAR3BP,kCAAAA,WAAAA,YAAAA,MAAAA;AAAAA,oCAAAA,eAAAA;EADZK,YAAW;IAAEG,YAAYhB;EAAc,CAAA;GAC3BQ,iCAAAA;;;;;;;;;;;;;;;;;;;ADvDN,IAAMU,iBAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;AACF;AAZaJ;;EACVK,QAAM,MAAMC,aAAAA;uCACP,kBAAA,cAAA,SAAA,aAAA;GAFKN,eAAAA,WAAAA,QAAAA,MAAAA;;EAIVK,QAAM,MAAME,MAAAA;;GAJFP,eAAAA,WAAAA,SAAAA,MAAAA;;EAOVK,QAAM,MAAME,QAAQ;IAAEC,UAAU;EAAK,CAAA;;GAP3BR,eAAAA,WAAAA,YAAAA,MAAAA;;EAUVK,QAAM,MAAM;IAACI;KAAe;IAAED,UAAU;EAAK,CAAA;;GAVnCR,eAAAA,WAAAA,YAAAA,MAAAA;AAAAA,iBAAAA,eAAAA;EADZU,YAAAA;GACYV,cAAAA;;;;;;;;;;;;;;;;;;;AXON,IAAMW,uCAAN,MAAMA;EAEXC;AACF;AAHaD;;EACVE,QAAM,MAAMC,oBAAoB;IAAEC,UAAU;EAAK,CAAA;uCACrC,uBAAA,cAAA,SAAA,kBAAA;GAFFJ,qCAAAA,WAAAA,eAAAA,MAAAA;AAAAA,uCAAAA,eAAAA;EADZK,YAAAA;GACYL,oCAAAA;AAMN,IAAMM,+BAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;EAGAC;AACF;AApCaZ;;EACVJ,QAAM,MAAMF,sCAAsC;IAAEI,UAAU;EAAM,CAAA;uCAC3D,yCAAA,cAAA,SAAA,oCAAA;GAFCE,6BAAAA,WAAAA,YAAAA,MAAAA;;EAIVJ,QAAM,MAAMiB,QAAQ;IAAEf,UAAU;EAAK,CAAA;;GAJ3BE,6BAAAA,WAAAA,YAAAA,MAAAA;;EAOVJ,QAAM,MAAMiB,QAAQ;IAAEf,UAAU;EAAK,CAAA;;GAP3BE,6BAAAA,WAAAA,SAAAA,MAAAA;;EAUVJ,QAAM,MAAM;IAACkB;GAAa;;GAVhBd,6BAAAA,WAAAA,YAAAA,MAAAA;;EAaVJ,QAAM,MAAMmB,aAAAA;uCACH,kBAAA,cAAA,SAAA,aAAA;GAdCf,6BAAAA,WAAAA,YAAAA,MAAAA;;EAgBVJ,QAAM,MAAMoB,YAAY;IAAElB,UAAU;EAAK,CAAA;uCAClC,eAAA,cAAA,SAAA,UAAA;GAjBGE,6BAAAA,WAAAA,SAAAA,MAAAA;;EAmBVJ,QAAM,MAAMqB,0BAA0B;IAAEnB,UAAU;EAAK,CAAA;uCAClC,6BAAA,cAAA,SAAA,wBAAA;GApBXE,6BAAAA,WAAAA,uBAAAA,MAAAA;;EAsBVJ,QAAM,MAAMsB,mBAAmB;IAAEpB,UAAU;EAAK,CAAA;uCAClC,sBAAA,cAAA,SAAA,iBAAA;GAvBJE,6BAAAA,WAAAA,gBAAAA,MAAAA;;EAyBVJ,QAAM,MAAMuB,iBAAiB;IAAErB,UAAU;EAAK,CAAA;uCAClC,oBAAA,cAAA,SAAA,eAAA;GA1BFE,6BAAAA,WAAAA,cAAAA,MAAAA;;EA4BVJ,QAAM,MAAM;IAACuB;KAAkB;IAAErB,UAAU;EAAK,CAAA;;GA5BtCE,6BAAAA,WAAAA,eAAAA,MAAAA;;EA+BVJ,QAAM,MAAMwB,iBAAiB;IAAEtB,UAAU;EAAK,CAAA;uCAClC,oBAAA,cAAA,SAAA,eAAA;GAhCFE,6BAAAA,WAAAA,cAAAA,MAAAA;;EAkCVJ,QAAM,MAAM;IAACyB;KAAiB;IAAEvB,UAAU;EAAK,CAAA;;GAlCrCE,6BAAAA,WAAAA,cAAAA,MAAAA;AAAAA,+BAAAA,eAAAA;EADZD,YAAAA;GACYC,4BAAAA;;;ADGb,SAASsB,gBAAgB;;;AiBrBzB,SAAiBC,YAAAA,iBAAgB;AACjC,SACEC,IACAC,QACAC,QAAAA,OACAC,WACAC,iBAAAA,gBAEAC,gBACAC,QAAAA,OACAC,cAAAA,aACAC,aACK;;;ACZP,SAASC,uBAAuB;;;ACAhC,SAAiBC,2BAA2B;;;ACA5C,SAASC,cAAAA,mBAAkB;;;ACA3B,SAASC,YAAYC,UAAUC,eAAeC,YAAY;;;;UCE9CC,sBAAAA;;;;;;;;;;;;;;;GAAAA,wBAAAA,sBAAAA,CAAAA,EAAAA;;UAiBAC,iBAAAA;;;GAAAA,mBAAAA,iBAAAA,CAAAA,EAAAA;;UAKAC,mBAAAA;;;;;GAAAA,qBAAAA,mBAAAA,CAAAA,EAAAA;;;ADjBZ,SAASC,gBAAgB;AAkBlB,IAAMC,6BAAN,MAAMA;EACJC,eAAe,IAAIC,cAAAA;EAElBC,mBACNC,qBACAC,cACA;AACA,QAAI,OAAOD,wBAAwB,WAAW;AAC5C,aAAOA;IACT;AACA,QAAIE,MAAMC,QAAQH,mBAAAA,GAAsB;AACtC,aAAOA,oBAAoBI,SAASH,YAAAA;IACtC;AACA,WAAOD,wBAAwBC;EACjC;EAEQI,kBAAkBC,OAAuB;AAzCnD;AA2CI,UAAMC,YAAUD,uBAAME,SAANF,mBAAYG,UAAZH,mBAAmBI,WAAnBJ,mBAA2BC,cAAWD,iBAAME,SAANF,mBAAYG,UAAZH,mBAAmBC;AAEzE,QAAI,CAACA,SAAS;AACZ,YAAMI,iBAAiB,KAAKC,yBAAyBN,KAAAA,KAAU,CAAA;AAC/D,iBAAWG,SAASE,gBAAgB;AAClC,YAAIF,MAAMI,MAAM;AACd,iBAAOJ,MAAMI;QACf;MACF;IACF;AAEA,QAAI,OAAON,YAAY,UAAU;AAC/B,aAAOA;IACT,WAAWL,MAAMC,QAAQI,OAAAA,KAAYA,QAAQO,SAAS,GAAG;AACvD,aAAOP,QAAQ,CAAA,EAAGQ;IACpB;AAEA,WAAO;EACT;EAEQC,oBAAoBV,OAAuB;AA/DrD;AAiEI,aAAOA,uBAAME,SAANF,mBAAYG,UAAZH,mBAAmBI,WAAnBJ,mBAA2BW,SAAMX,iBAAME,SAANF,mBAAYG,UAAZH,mBAAmBW;EAC7D;EAEQL,yBAAyBN,OAAuB;AApE1D;AAsEI,aAAOA,uBAAME,SAANF,mBAAYG,UAAZH,mBAAmBI,WAAnBJ,mBAA2BY,uBAAoBZ,iBAAME,SAANF,mBAAYG,UAAZH,mBAAmBY;EAC3E;EAEQC,oBAAoBb,OAAuB;AAzErD;AA2EI,aAAOA,uBAAME,SAANF,mBAAYG,UAAZH,mBAAmBI,WAAnBJ,mBAA2Bc,wBAAqBd,iBAAME,SAANF,mBAAYG,UAAZH,mBAAmBc;EAC5E;EAEAC,yBAAyB;AACvB,QAAIC,qBAAqD;AAEzD,WAAO,KAAKzB,aAAa0B,KACvBC,KACE,CAACC,KAAKnB,UAAAA;AACJ,UAAIA,MAAMA,UAAUoB,oBAAoBC,mBAAmB;AACzD,cAAMC,gBAAgBH,IAAII;AAC1BJ,YAAIK,iBAAiB,KAAKzB,kBAAkBC,KAAAA;AAC5CmB,YAAII,gBAAgB,KAAKb,oBAAoBV,KAAAA,KAAUmB,IAAII;AAC3D,cAAMlB,iBAAiB,KAAKC,yBAAyBN,KAAAA,KAAU,CAAA;AAC/D,cAAMyB,mBAAmB,KAAKZ,oBAAoBb,KAAAA;AAElD,cAAM0B,gBAAgBrB,kBAAkBA,eAAeG,SAAS;AAChE,YAAImB,iBAAgBF,qDAAkBG,mBAAkB;AAExDT,YAAIU,kBAAkBxB,eAAeyB,KAAK,CAAC3B,UAAeA,MAAM4B,QAAQ5B,MAAMQ,EAAE;AAChFQ,YAAIa,iBAAiBV,kBAAkBH,IAAII,iBAAiB,CAACJ,IAAIU;AAEjE,YAAII,2BAA2Bd,IAAIe;AACnCf,YAAIe,aAAaR;AAEjB,YAAIO,4BAA4B,CAACP,eAAe;AAC9CC,0BAAgB;QAClB;AACAR,YAAIQ,gBAAgBA;AACpBR,YAAIgB,gBAAeV,qDAAkBG,mBAAkB;AACtD,SAAA,EAAEG,MAAMZ,IAAIiB,kBAAkBzB,IAAIQ,IAAIkB,eAAc,IAAKhC,eAAeiC,KACvE,CAACnC,UAAeA,MAAM4B,QAAQ5B,MAAMQ,EAAE,KACnC;UAAEoB,MAAMZ,IAAIiB;UAAkBzB,IAAIQ,IAAIkB;QAAe;MAC5D;AACAlB,UAAInB,QAAQA;AACZgB,2BAAqBG;AACrB,aAAOA;IACT,GACA;MACEnB,OAAO;MACPgC,gBAAgB;MAChBG,cAAc;MACdN,iBAAiB;MACjBF,eAAe;MACfO,YAAY;MACZX,eAAe;MACfc,gBAAgB;MAChBD,kBAAkB;MAClBZ,gBAAgB;MAChBe,sBAAsB,oBAAIC,IAAAA;IAC5B,CAAA,GAEFC,SAAS,CAACtB,QAAAA;AACR,YAAMuB,SAAyB,CAAA;AAE/B,UAAIC,qBAAqB;AACzB,UAAIjD,sBAAmD;AAEvD,UAAIyB,IAAInB,MAAMA,SAASoB,oBAAoBC,mBAAmB;AAC5D,YAAI,iCAAiCF,IAAInB,MAAM4C,YAAY,CAAC,IAAI;AAC9DlD,gCAAsByB,IAAInB,MAAM4C,SAAS,4BAAA;QAC3C;AACA,YAAI,+BAA+BzB,IAAInB,MAAM4C,YAAY,CAAC,IAAI;AAC5DD,+BAAqBxB,IAAInB,MAAM4C,SAAS,0BAAA;QAC1C;MACF;AAEA,UAAIzB,IAAInB,MAAMA,UAAUoB,oBAAoByB,aAAa;AACvDH,eAAOI,KAAK;UACVC,MAAMC,kBAAkBC;UACxBlB,MAAMmB,qBAAqBC;UAC3BC,OAAOjC,IAAInB,MAAMoD;QACnB,CAAA;MACF;AACA,UAAIjC,IAAInB,MAAMA,UAAUoB,oBAAoBiC,uBAAuB;AACjEX,eAAOI,KAAK;UACVC,MAAMC,kBAAkBC;UACxBlB,MAAMmB,qBAAqBI;UAC3BpD,MAAMiB,IAAInB,MAAME;QAClB,CAAA;MACF;AAEA,YAAMuB,mBAAmB,KAAKZ,oBAAoBM,IAAInB,KAAK;AAG3D,UACEmB,IAAIQ,iBACJ,KAAKlC,mBAAmBC,qBAAqByB,IAAIiB,gBAAgB,KACjEjB,IAAIkB,kBACJ,CAAClB,IAAIoB,qBAAqBgB,IAAIpC,IAAIkB,cAAc,GAChD;AACAlB,YAAIoB,qBAAqBiB,IAAIrC,IAAIkB,cAAc;AAE/CK,eAAOI,KAAK;UACVC,MAAMC,kBAAkBS;UACxBC,mBAAmBvC,IAAIkB;QACzB,CAAA;MACF,YAGSZ,qDAAkBG,mBAAkB,UAAUe,oBAAoB;AACzED,eAAOI,KAAK;UACVC,MAAMC,kBAAkBW;UACxBC,WAAWzC,IAAII;QACjB,CAAA;MACF;AAEA,cAAQJ,IAAInB,MAAOA,OAAK;QAItB,KAAKoB,oBAAoByC;AAIvB,cAAI1C,IAAInB,MAAM+B,SAAS+B,iBAAiBC,+BAA+B;AACrErB,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBgB;cACxBJ,WAAWzC,IAAInB,MAAME,KAAK+D;YAC5B,CAAA;AACAvB,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBkB;cACxBN,WAAWzC,IAAInB,MAAME,KAAK+D;cAC1BhE,SAASkB,IAAInB,MAAME,KAAKiE;YAC1B,CAAA;AACAzB,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBW;cACxBC,WAAWzC,IAAInB,MAAME,KAAK+D;YAC5B,CAAA;UACF,WAIS9C,IAAInB,MAAM+B,SAAS+B,iBAAiBM,gCAAgC;AAC3E1B,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBqB;cACxBX,mBAAmBvC,IAAInB,MAAME,KAAKS;cAClC2D,YAAYnD,IAAInB,MAAME,KAAK6B;cAC3BwC,iBAAiBpD,IAAInB,MAAME,KAAKS;YAClC,CAAA;AACA+B,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBwB;cACxBd,mBAAmBvC,IAAInB,MAAME,KAAKS;cAClCJ,MAAMkE,KAAKC,UAAUvD,IAAInB,MAAME,KAAKK,IAAI;YAC1C,CAAA;AACAmC,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBS;cACxBC,mBAAmBvC,IAAInB,MAAME,KAAKS;YACpC,CAAA;UACF;AACA;QACF,KAAKS,oBAAoBuD;AACvBjC,iBAAOI,KAAK;YACVC,MAAMC,kBAAkB4B;YACxBC,UAAU1D,IAAInB,MAAM8E;YACpBC,MAAM5D,IAAInB,MAAM+E;YAChBC,WAAW7D,IAAInB,MAAMiF;YACrBC,UAAU/D,IAAInB,MAAMmF;YACpBC,OAAOjE,IAAInB,MAAMqF;YACjBC,QAAQnE,IAAInB,MAAMsF;YAClBC,OAAOd,KAAKC,UAAUvD,IAAInB,MAAMuF,KAAK;YACrCC,SAASrE,IAAInB,MAAMwF;UACrB,CAAA;AACA;QACF,KAAKpE,oBAAoBC;AACvB,cACEF,IAAIU,mBACJ,KAAKpC,mBAAmBC,qBAAqByB,IAAIiB,gBAAgB,GACjE;AACAM,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBqB;cACxBX,mBAAmBvC,IAAIkB;cACvBiC,YAAYnD,IAAIiB;cAChBmC,iBAAiBpD,IAAII;YACvB,CAAA;UACF,WAESJ,IAAIa,kBAAkBW,oBAAoB;AACjDxB,gBAAIoB,qBAAqBkD,MAAK;AAC9B/C,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBgB;cACxBJ,WAAWzC,IAAII;YACjB,CAAA;UACF;AAGA,cACEJ,IAAIe,cACJf,IAAIK,kBACJ,KAAK/B,mBAAmBC,qBAAqByB,IAAIiB,gBAAgB,GACjE;AACAM,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBwB;cACxBd,mBAAmBvC,IAAIkB;cACvB9B,MAAMY,IAAIK;YACZ,CAAA;UACF,WAES,CAACL,IAAIe,cAAcf,IAAIK,kBAAkBmB,oBAAoB;AACpED,mBAAOI,KAAK;cACVC,MAAMC,kBAAkBkB;cACxBN,WAAWzC,IAAII;cACftB,SAASkB,IAAIK;YACf,CAAA;UACF;AACA;MACJ;AACA,aAAOkB;IACT,CAAA,GACAgD,WAAW,CAACC,UAAAA;AACVC,cAAQD,MAAMA,KAAAA;AACd,YAAMjD,SAAyB,CAAA;AAE/B,WAAI1B,yDAAoBO,kBAAiB,CAACP,mBAAmBkB,YAAY;AACvEQ,eAAOI,KAAK;UACVC,MAAMC,kBAAkBW;UACxBC,WAAW5C,mBAAmBO;QAChC,CAAA;MACF;AACA,UAAIP,yDAAoBqB,gBAAgB;AACtCK,eAAOI,KAAK;UACVC,MAAMC,kBAAkBS;UACxBC,mBAAmB1C,mBAAmBqB;QACxC,CAAA;MACF;AAEA,YAAMuB,YAAYiC,SAAAA;AAElBnD,aAAOI,KAAK;QACVC,MAAMC,kBAAkBgB;QACxBJ;MACF,CAAA;AACAlB,aAAOI,KAAK;QACVC,MAAMC,kBAAkBkB;QACxBN;QACA3D,SAAS;MACX,CAAA;AACAyC,aAAOI,KAAK;QACVC,MAAMC,kBAAkBW;QACxBC;MACF,CAAA;AAEA,aAAOlB;IACT,CAAA,CAAA;EAEJ;AACF;AAxSapD;;;AEzBb,SACEwG,UAAUC,uBAIL;AACP,SAASC,kBAAkB;AAC3B,SAASC,aAAaC,kBAAkB;AACxC,SAASC,SAASC,wBAAwB;AAW1C,SAASC,WAAWC,6BAA6B;AACjD,SAASC,qBAAqB;AAiE9B,IAAIC,uBAAuB;AAE3B,eAAsBC,QAAQC,MAAmB;AAC/C,SAAO,IAAIC,eAAe;IACxB,MAAMC,MAAMC,YAAU;AACpB,UAAI;AACF,cAAMC,aAAaD,YAAYH,IAAAA;AAC/BG,mBAAWE,MAAK;MAClB,SAASC,KAAP;AAEA,cAAMC,QAAQD,2BAAKC;AAGnB,cAAMC,aAAYD,+BAAOE,UAAQH,2BAAKG;AAEtC,YAAID,cAAc,gBAAgB;AAChC,gBAAM,IAAIE,sBAAsB;YAC9BC,SAAS;;;;UAIX,CAAA;QACF,OAAO;AACL,gBAAM,IAAID,sBAAsB;YAC9BC,SAAS;2DACsCL;;;UAGjD,CAAA;QACF;MACF;IACF;EACF,CAAA;AACF;AA/BsBP;AAiCtB,eAAeK,aAAaD,YAA6CH,MAAmB;AAxH5F;AAyHE,QAAM,EACJY,eACAC,iBACAC,UAAUC,qBACVC,OACAC,UAAUC,iBACVC,OAAOC,cACPC,QAAQC,gBACRC,UACAC,SACAC,QAAAA,SACAC,YACAC,WAAU,IACR3B;AAEJ,MAAIiB,WAAWC;AACf,MAAIC,QAAQC;AACZ,QAAM,EAAEQ,MAAMC,aAAaC,mBAAkB,IAAKd;AAElD,QAAMe,kBAAkBL,WAAWM,gBAC/B;IAAEA,eAAe,UAAUN,WAAWM;EAAgB,IACtD;AAEJ,QAAMC,SAAS,IAAIC,gBAAgB;IACjCC,QAAQvB;IACRwB,QAAQvB;IACRwB,gBAAgB;MAAE,GAAGN;IAAgB;EACvC,CAAA;AAEA,MAAIjB,WAAWC,uBAAuBuB,WAAAA;AACtC,MAAIvB,uBAAuBA,oBAAoBwB,WAAW,KAAA,GAAQ;AAChEzB,eAAWC,oBAAoByB,UAAU,CAAA;EAC3C;AAEA,MAAI,CAACC,YAAY3B,QAAAA,GAAW;AAC1B4B,YAAQC,KACN,2BAA2B7B,yDAAyD;EAExF;AAEA,MAAI8B,iCAAiC;AACrC,MAAI;AACF,UAAMX,OAAOY,QAAQC,IAAIhC,QAAAA;EAC3B,SAASiC,OAAP;AACAH,qCAAiC;AACjC,UAAMX,OAAOY,QAAQG,OAAO;MAAElC;IAAS,CAAA;EACzC;AAEA,MAAImC,aAAa;IAAEC,QAAQ,CAAC;EAAE;AAC9B,MAAIN,gCAAgC;AAClCK,iBAAa,MAAMhB,OAAOY,QAAQM,SAASrC,QAAAA;EAC7C;AAEA,QAAMsC,mBAAmBH,WAAWC;AACpC/B,QAAMI,WAAW6B,iBAAiB7B;AAClC,QAAM8B,OACJvC,YAAYG,YAAY,aAAaA,YAAYqC,UAAarC,YAAY,OACtE,aACA;AACN,MAAIsC,oBAAoB,CAAA;AACxB,MAAI;AACFA,wBAAoBC,8BAA8BjC,QAAAA;EACpD,SAASkC,GAAP;AACAhC,IAAAA,QAAOsB,MAAMU,GAAG,uBAAuBA,EAAE9C,SAAS;EACpD;AACAQ,UAAQuC,2BAA2BvC,OAAOoC,mBAAmB/B,SAASI,IAAAA;AAEtE,QAAM+B,cAAcN,SAAS,UAAUlC,QAAQ;AAE/C,QAAMyC,UAAU;IACdC,OAAOF;IACPG,YAAY;MAAC;MAAU;MAAU;;IACjCC,SAAST;EACX;AAEA,QAAMU,uBAAuBrC,yCAAYsC,KACvC,CAACC,OAAOA,GAAGtC,SAASuC,cAAcC;AAEpC,MAAItE,wBAAwB,CAACkE,sBAAsB;AAEjDJ,YAAQG,UAAU;MAAEM,QAAQlD,MAAMI;IAAS;EAC7C;AACA,MAAIyC,6DAAsBM,UAAU;AAClC,QAAIA,WAAWN,qBAAqBM;AACpCV,YAAQG,UAAU;MAAEM,QAAQE,UAAUD,UAAUA,QAAAA;IAAU;EAC5D;AAEA,MAAIjB,SAAS,cAAc,CAACvD,sBAAsB;AAChD,UAAMmC,OAAOY,QAAQ2B,YAAY1D,UAAU;MAAEoC,QAAQ/B;MAAOsD,QAAQxD;IAAS,CAAA;EAC/E;AAEA,MAAIyD,aAKA;IACFC,cAAc9D,kBACV+D,WAAW,QAAA,EAAUC,OAAOhE,eAAAA,EAAiBiE,OAAO,KAAA,IACpD;EACN;AAEA,QAAMC,aAAa,MAAM9C,OAAO8C,WAAWC,OAAM;AACjD,QAAMC,qBAAqBF,WAAWd,KACpC,CAACiB,MAAMA,EAAEtD,SAASA,QAAQsD,EAAEC,iBAAiBrD,kBAAAA;AAE/C,MAAI,CAACmD,oBAAoB;AACvBG,6BAAUC,QAAQ,8CAA8C;MAC9D,GAAGX;MACH3B,OAAO,oDAAoDgC,WAAWO;IACxE,CAAA;AACA5C,YAAQK,MAAM;;;;;yCAIuBgC,WAAWQ,IAAI,CAACL,MAAM,GAAGA,EAAEtD,aAAasD,EAAEC,eAAe,EAAEK,KAAK,IAAA;OAClG;AACH,UAAM,IAAIC,MAAM,mBAAA;EAClB;AACA,QAAM5D,cAAcoD,mBAAmBE;AAEvC,QAAMO,YAAY,MAAMzD,OAAO8C,WAAWY,SAAS9D,WAAAA;AACnD,QAAM+D,cAAc,MAAM3D,OAAO8C,WAAWc,WAAWhE,WAAAA;AACvD,QAAMiE,aAAaC,cAAcH,WAAAA;AAEjC,MAAItE,gBAAgB;AAClB,QAAI0E,uBAAuBf,mBAAmB5D,OAAO4E;AACrD,QAAI3E,eAAe2E,cAAc;AAC/BD,8BAAuBF,yCAAYzE,UAC/B6E,yBAAyB5E,iDAAgB2E,cAAcH,yCAAYzE,MAAAA,IACnEC,iDAAgB2E;IACtB;AAEA,UAAME,YAAY;MAChB,GAAGlB,mBAAmB5D;MACtB,GAAGC;MACH2E,cAAcD;IAChB;AAGA,UAAMI,+BACJnB,mBAAmB5D,OAAOgF,mBAAmB,QAAQ/E,eAAe+E,oBAAoB;AAE1F,UAAMC,sBACJC,KAAKC,UAAUL,SAAAA,MAAeI,KAAKC,UAAUvB,mBAAmB5D,MAAM;AAGxE,UAAMoF,gCACJL,gCACAG,KAAKC,UAAU;MAAE,GAAGL;MAAWE,iBAAiB;IAAK,CAAA,MACnDE,KAAKC,UAAU;MAAE,GAAGvB,mBAAmB5D;MAAQgF,iBAAiB;IAAK,CAAA;AAGzE,QAAIC,uBAAuB,CAACG,+BAA+B;AACzD,YAAMxE,OAAO8C,WAAWF,OAAOhD,aAAa;QAC1CR,QAAQ8E;MACV,CAAA;IACF;EACF;AAGA,MAAIvC,QAAQC,UAASiC,yCAAYjC,QAAO;AACtCD,YAAQC,QAAQqC,yBAAyBtC,QAAQC,OAAOiC,WAAWjC,KAAK;EAC1E;AAEA,MAAI6C,0BAA0B,IAAIC,wBAAwB,CAAA,CAAE;AAC5D,MAAIC,eAAe;AACnB,MAAIC,gCAAgC;AACpC,MAAIC,aAAa;AACjB,MAAIC,gBAAgB;AAEpB,QAAMC,iBAAiB/E,OAAOgF,KAAKC,OAAOpG,UAAUe,aAAa+B,OAAAA;AAEjE,QAAMuD,OAAO,wBAACxG,YAAoBR,WAAWiH,QAAQ,IAAIC,YAAAA,EAAcC,OAAO3G,OAAAA,CAAAA,GAAjE;AAEb,MAAI4G,oBAAoB,CAAC;AACzB,MAAIC,eAAerG;AAGnB,MAAIsG,uBAAuB;AAE3B3H,yBAAuB;AACvB,MAAI;AACFsF,6BAAUC,QAAQ,8CAA8C;MAC9DV,cAAcD,WAAWC;IAC3B,CAAA;AACA,mBAAe+C,uBAAuBV,gBAAgB;AACpD,UAAI,CAAC;QAAC;QAAU;QAAU;QAAS;QAAWW,SAASD,oBAAoBE,KAAK;AAAG;AAEnF,UAAIF,oBAAoBE,UAAU,SAAS;AACzC,cAAM,IAAInC,MAAM,uBAAuBiC,oBAAoBG,KAAKlH,SAAS;MAC3E;AAUA,YAAMmH,QAAQJ;AAEd,YAAMK,kBAAkBD,MAAMD,KAAKG;AACnC,UAAID,mDAAiBzC,QAAQ;AAC3BxF,+BAAuB;AACvB,cAAMmI,iBAAiBF,mDAAkB,GAAGG;AAC5C,YACE,OAAOD,kBAAkB,YACzB,oCAAoCA,gBACpC;AACA,gBAAME,UAAUF,eAAeG;AAC/BjB,eACEZ,KAAKC,UAAU;YACboB,OAAOS,oBAAoBC;YAC3BT,MAAM;cACJK,OAAO,OAAOC,YAAY,WAAWA,UAAU5B,KAAKC,UAAU2B,OAAAA;cAC9D5G,UAAUgH,8BAA8BN,eAAeO,uBAAuB;YAChF;UACF,CAAA,IAAK,IAAA;QAET,OAAO;AACLrB,eACEZ,KAAKC,UAAU;YACboB,OAAOS,oBAAoBI;YAC3BP,OACE,OAAOD,mBAAmB,WACtBA,iBACA1B,KAAKC,UAAUyB,cAAAA;UACvB,CAAA,IAAK,IAAA;QAET;AACA;MACF;AACA,UAAIP,oBAAoBE,UAAU;AAAW;AAE7C,UAAIF,oBAAoBE,UAAU,UAAU;AAC1CL,4BAAoBO,MAAMD;AAC1B;MACF;AAEA,YAAMa,YAAYZ,MAAMD;AACxB,YAAMc,kBAAkBD,UAAUE,SAASC;AAC3C,YAAMC,YAAYJ,UAAUd;AAC5B,YAAMmB,QAAQL,UAAUE,SAASI;AACjCjC,sBAAgBgC;AAChB,YAAMH,WAAWF,UAAUE;AAC3B,YAAIF,qBAAUb,SAAVa,mBAAgBO,WAAhBP,mBAAwBQ,UAAS,UAAQR,qBAAUb,SAAVa,mBAAgBO,WAAhBP,mBAAwBQ,UAAS,IAAI;AAChFxE,mBAAWyE,YAAWT,qBAAUb,SAAVa,mBAAgBO,WAAhBP,mBAAwBQ;MAChD;AACA,UAAIN,SAASQ,kBAAkB,QAAQR,SAASQ,kBAAkB,IAAI;AACpE1E,mBAAW2E,gBAAgBT,SAASQ;MACtC;AACA,UAAIR,SAASU,qBAAqB,QAAQV,SAASU,qBAAqB,IAAI;AAC1E5E,mBAAW6E,mBAAmBX,SAASU;MACzC;AAEAxC,mBACEA,cACCgC,cAAcT,oBAAoBmB,iBACjCd,UAAU9G,SAAS6H,iBAAiBC;AAExC,YAAMC,wBAAwBf,SAAS,oCAAA;AACvC,YAAMgB,gCACJd,cAAcT,oBAAoBmB,iBAClCd,UAAU9G,SAAS6H,iBAAiBI;AAEtC,YAAMC,cACJ7I,aAAa0H,mBAAmBG,cAAcT,oBAAoB0B;AAGpE,UAAID,aAAa;AACfrC,+BAAuB;MACzB;AAIA,UAAI/B,UAAU,OAAA,EAASsE,KAAK,CAACC,SAASA,KAAKC,OAAOvB,eAAAA,GAAkB;AAClE1H,mBAAW0H;MACb;AAEAnB,qBAAeC,wBAAwBF;AAEvC,UAAI,CAACtG,UAAU;AACb;MACF;AAEA,UAAI2I,+BAA+B;AAEjCnC,+BAAuBiB,UAAUb;AACjCV,aACEgD,kBAAkB;UAChBrJ;UACAiI;UACAqB,WAAWpJ,MAAMY;UACjBX;UACAE,OAAOsG;UACP4C,SAAS;UACTC,QAAQ;UACRxE;QACF,CAAA,CAAA;AAEF;MACF;AAEA,UAAI6D,yBAAyB9C,iCAAiC,MAAM;AAClEA,wCAAgC5F;MAClC;AAEA,UAAI0I,yBAAyBb,cAAcT,oBAAoBkC,kBAAkB;AAE/E7D,kCAA0B,IAAIC,wBAAwBgD,qBAAAA;MACxD;AAEA,UAAIA,yBAAyBb,cAAcT,oBAAoBmC,mBAAmB;AAChF9D,gCAAwB+D,gBAAgB/B,SAAAA;MAC1C;AAEA,UAAI7B,kCAAkC,MAAM;AAC1CW,uBAAe;UACb,GAAGA;UACH,GAAGd,wBAAwBgE,aAAY;QACzC;MACF;AAEA,UACE,CAACf,yBACDhB,oBAAoB9B,iCACpBiC,cAAcT,oBAAoB0B,YAClC;AAEAlD,wCAAgC;MAClC;AAEA,UACEN,KAAKC,UAAUgB,YAAAA,MAAkBjB,KAAKC,UAAUrF,KAAAA,KAChDyF,gBAAgB3F,YAChB6I,aACA;AACA3I,gBAAQqG;AACRZ,uBAAe3F;AACfkG,aACEgD,kBAAkB;UAChBrJ;UACAiI;UACAqB,WAAWpJ,MAAMY;UACjBX;UACAE;UACAkJ,SAAS;UACTC,QAAQ,CAACR;UACThE;QACF,CAAA,CAAA;MAEJ;AAEAqB,WAAKZ,KAAKC,UAAUkC,SAAAA,IAAa,IAAA;IACnC;AAEAvH,YAAQ,MAAMc,OAAOY,QAAQM,SAASrC,QAAAA;AACtC,UAAM6J,cAAaxJ,iBAAMyJ,UAANzJ,mBAAc,OAAdA,mBAAkBwJ;AACrC1J,eAAW0J,aAAa1J,WAAW4J,OAAOC,KAAK3J,MAAMyH,SAASmC,MAAM,EAAE,CAAA;AACtE,UAAMC,YAAY7J,MAAM8J,KAAK3F,WAAW,KAAK,CAACqF;AAE9CvF,6BAAUC,QAAQ,4CAA4CX,UAAAA;AAE9DyC,SACEgD,kBAAkB;MAChBrJ;MACAiI,OAAOhC;MACPqD,WAAWpJ,MAAMY;MACjBX,UAAU+J,YAAY,YAAY/J;MAClCE,OAAOA,MAAM+B;MACbmH,SAAS,CAACvD;MACVwD,QAAQ;MACRY,iBAAiB;MACjBpF;IACF,CAAA,CAAA;AAGF,WAAOqF,QAAQC,QAAO;EACxB,SAAS3H,GAAP;AACAhC,IAAAA,QAAOsB,MAAMU,CAAAA;AACb2B,6BAAUC,QAAQ,8CAA8C;MAC9D,GAAGX;MACH3B,OAAOU,EAAE9C;IACX,CAAA;AACA,WAAOwK,QAAQC,QAAO;EACxB;AACF;AArYehL;AAuYf,SAAS+J,kBAAkB,EACzBrJ,UACAiI,OACAqB,WACAnJ,UACAE,OACAkJ,SACAC,QACAY,kBAAkB,OAClBpF,WAAU,GAWX;AACC,MAAI,CAACoF,iBAAiB;AACpB/J,YAAQ0J,OAAOC,KAAK3J,KAAAA,EAAOkK,OAAO,CAACC,KAAKC,QAAAA;AACtC,UAAIA,QAAQ,YAAY;AACtBD,YAAIC,GAAAA,IAAOpK,MAAMoK,GAAAA;MACnB;AACA,aAAOD;IACT,GAAG,CAAC,CAAA;EACN,OAAO;AACLnK,YAAQ;MACN,GAAGA;MACHI,UAAUgH,8BAA8BpH,MAAMI,YAAY,CAAA,CAAE;IAC9D;EACF;AAGA,MAAIuE,yCAAYmD,QAAQ;AACtB9H,YAAQ+E,yBAAyB/E,OAAO2E,WAAWmD,MAAM;EAC3D;AAEA,SACE1C,KAAKC,UAAU;IACboB,OAAOS,oBAAoBmD;IAC3BC,WAAW3K;IACXkI,QAAQD;IACR2C,YAAYtB;IACZuB,WAAW1K;IACXqJ;IACAnJ;IACAkJ;IACAuB,MAAM;EACR,CAAA,IAAK;AAET;AArDSzB;AAuDT,IAAMxD,0BAAN,6BAAMA,yBAAAA;EACIgD;EACAkC;EACAC;EACAC;EAERC,YAAYrC,uBAAiD;AAC3D,SAAKA,wBAAwBA;AAC7B,SAAKkC,iBAAiB,CAAC;AACvB,SAAKC,kBAAkB;AACvB,SAAKC,0BAA0B,CAAC;EAClC;EAEAtB,gBAAgB7C,OAEb;AACD,QAAIA,MAAMC,KAAKC,MAAMmE,iBAAiB3G,SAAS,GAAG;AAChD,YAAMwC,QAAQF,MAAMC,KAAKC,MAAMmE,iBAAiB,CAAA;AAEhD,UAAInE,MAAMlG,SAAS,QAAQkG,MAAMlG,SAAS0B,QAAW;AACnD,aAAKwI,kBAAkBhE,MAAMlG;AAC7B,aAAKiK,eAAe,KAAKC,eAAe,IAAIhE,MAAM9H;MACpD,WAAW,KAAK8L,oBAAoB,QAAQ,KAAKA,oBAAoBxI,QAAW;AAC9E,aAAKuI,eAAe,KAAKC,eAAe,KAAKhE,MAAM9H;MACrD;IACF;EACF;EAEAkM,mBAAmBC,iBAAyD;AAC1E,eAAW9K,UAAU,KAAKsI,uBAAuB;AAC/C,YAAMyC,WAAW/K,OAAO,WAAA;AACxB,YAAMgL,OAAOhL,OAAO,MAAA;AACpB,YAAMiL,eAAejL,OAAO,eAAA;AAE5B,UAAI8K,oBAAoBE,MAAM;AAC5B,eAAO;UAACC;UAAcF;;MACxB;IACF;AACA,WAAO;MAAC;MAAM;;EAChB;EAEA1B,eAAsB;AACpB,UAAMvJ,QAAe,CAAC;AAEtB,eAAW,CAACoK,KAAKrD,KAAAA,KAAU2C,OAAO0B,QAAQ,KAAKV,cAAc,GAAG;AAC9D,YAAM,CAACW,cAAcJ,QAAAA,IAAY,KAAKF,mBAAmBX,GAAAA;AAEzD,UAAIa,aAAa,MAAM;AACrB;MACF;AAEA,UAAIK;AACJ,UAAI;AACFA,sBAAcC,iBAAiBxE,KAAAA;MACjC,SAASnF,OAAP;AACA,YAAIwI,OAAO,KAAKQ,yBAAyB;AACvCU,wBAAc,KAAKV,wBAAwBR,GAAAA;QAC7C,OAAO;AACL;QACF;MACF;AAEA,WAAKQ,wBAAwBR,GAAAA,IAAOkB;AAEpC,UAAI,CAACD,cAAc;AACjBrL,cAAMiL,QAAAA,IAAYK;MACpB,OAAO;AACLtL,cAAMiL,QAAAA,IAAYK,YAAYD,YAAAA;MAChC;IACF;AAEA,WAAOrL;EACT;AACF,GAzEA;AA4EA,SAASuC,2BACPvC,OACAI,UACAC,SACA4I,WAAiB;AAEjB,MAAI7I,SAAS+D,SAAS,KAAK,UAAU/D,SAAS,CAAA,KAAMA,SAAS,CAAA,EAAGqK,SAAS,UAAU;AAEjFrK,eAAWA,SAASoL,MAAM,CAAA;EAC5B;AAGA,QAAMC,mBAA+CzL,MAAMI,YAAY,CAAA;AACvE,QAAMsL,qBAAqB,IAAIC,IAAIF,iBAAiBrH,IAAI,CAAC5E,YAAYA,QAAQuJ,EAAE,CAAA;AAC/E,QAAM6C,aAAa,IAAID,IAAIvL,SAASgE,IAAI,CAAC5E,YAAYA,QAAQuJ,EAAE,CAAA;AAE/D,MAAI8C,kBAAkB,CAAA;AACtB,MAAIzL,SAAS+D,SAASsH,iBAAiBtH,QAAQ;AAE7C0H,sBAAkBJ,iBACfK,OAAO,CAACC,MAAM,CAACH,WAAWI,IAAID,EAAEhD,EAAE,CAAA,EAClC3E,IAAI,CAAC2H,MAAM,IAAIE,cAAc;MAAElD,IAAIgD,EAAEhD;IAAG,CAAA,CAAA;EAC7C;AAEA,QAAMmD,cAAc9L,SAAS0L,OAAO,CAACtM,YAAY,CAACkM,mBAAmBM,IAAIxM,QAAQuJ,EAAE,CAAA;AAEnF,SAAO;IACL,GAAG/I;IACHI,UAAU;SAAIyL;SAAoBK;;IAClCC,YAAY;MACV9L;IACF;EACF;AACF;AAjCSkC;AAmCF,SAAS6E,8BAA8BhH,UAAe;AAC3D,QAAMgM,SAAgB,CAAA;AACtB,QAAMC,kBAA0C,CAAC;AAGjD,aAAW7M,WAAWY,UAAU;AAC9B,QAAIZ,QAAQ8M,SAAS,MAAM;AACzB,iBAAWC,aAAa/M,QAAQgN,YAAY;AAC1CH,wBAAgBE,UAAUxD,EAAE,IAAIwD,UAAU9L;MAC5C;IACF;EACF;AAEA,aAAWjB,WAAWY,UAAU;AAC9B,QAAIqM,UAAejN,QAAQiN;AAC3B,QAAIA,mBAAmBC,OAAO;AAC5BD,gBAAUA,QAAQ,CAAA;IACpB;AACA,QAAIA,mBAAmB/C,QAAQ;AAC7B+C,gBAAUA,QAAQE;IACpB;AAEA,QAAInN,QAAQ8M,SAAS,SAAS;AAC5BF,aAAOQ,KAAK;QACVnC,MAAM;QACNgC;QACA1D,IAAIvJ,QAAQuJ;MACd,CAAA;IACF,WAAWvJ,QAAQ8M,SAAS,UAAU;AACpCF,aAAOQ,KAAK;QACVnC,MAAM;QACNgC;QACA1D,IAAIvJ,QAAQuJ;MACd,CAAA;IACF,WAAWvJ,QAAQ8M,SAAS,MAAM;AAChC,UAAI9M,QAAQgN,cAAchN,QAAQgN,WAAWrI,SAAS,GAAG;AACvD,mBAAWoI,aAAa/M,QAAQgN,YAAY;AAC1CJ,iBAAOQ,KAAK;YACV7D,IAAIwD,UAAUxD;YACdtI,MAAM8L,UAAU9L;YAChBoM,WAAWN,UAAU1N;YACrBiO,iBAAiBtN,QAAQuJ;UAC3B,CAAA;QACF;MACF,OAAO;AACLqD,eAAOQ,KAAK;UACVnC,MAAM;UACNgC;UACA1D,IAAIvJ,QAAQuJ;UACZ+D,iBAAiBtN,QAAQuJ;QAC3B,CAAA;MACF;IACF,WAAWvJ,QAAQ8M,SAAS,QAAQ;AAClC,YAAMS,aAAaV,gBAAgB7M,QAAQwN,YAAY,KAAKxN,QAAQiB,QAAQ;AAC5E2L,aAAOQ,KAAK;QACVK,mBAAmBzN,QAAQwN;QAC3BD;QACAX,QAAQK;QACR1D,IAAIvJ,QAAQuJ;MACd,CAAA;IACF;EACF;AACA,QAAMmE,cAAmC,CAAC;AAC1C,aAAWC,OAAOf,QAAQ;AACxB,QAAIe,IAAIF,mBAAmB;AACzBC,kBAAYC,IAAIF,iBAAiB,IAAIE;IACvC;EACF;AAEA,QAAMC,kBAA6B,CAAA;AAEnC,aAAWD,OAAOf,QAAQ;AAExB,QAAI,EAAE,uBAAuBe,MAAM;AACjCC,sBAAgBR,KAAKO,GAAAA;IACvB;AAIA,QAAI,eAAeA,KAAK;AACtB,YAAME,QAAQF,IAAIpE;AAClB,UAAIsE,SAASH,aAAa;AACxBE,wBAAgBR,KAAKM,YAAYG,KAAAA,CAAM;MACzC;IACF;EACF;AAEA,SAAOD;AACT;AAxFgBhG;AA0FhB,SAAS/E,8BAA8BjC,UAAmB;AACxD,QAAMgM,SAAqC,CAAA;AAC3C,QAAMkB,4BAA4B,oBAAI3B,IAAAA;AAEtC,aAAWnM,WAAWY,UAAU;AAE9B,QAAIZ,QAAQ+N,cAAa,GAAI;AAC3B,UAAI/N,QAAQiL,SAAS,QAAQ;AAE3B2B,eAAOQ,KAAK;UACV,GAAGpN;UACHiL,MAAM+C,YAAYC;QACpB,CAAA;MACF,WAAWjO,QAAQiL,SAAS,UAAU;AAEpC2B,eAAOQ,KAAK;UACV,GAAGpN;UACHiL,MAAM+C,YAAYE;QACpB,CAAA;MACF,WAAWlO,QAAQiL,SAAS,aAAa;AAEvC2B,eAAOQ,KAAK;UACV,GAAGpN;UACHiL,MAAM+C,YAAYG;QACpB,CAAA;MACF;AACA;IACF;AAGA,QAAInO,QAAQoO,eAAc,GAAI;AAC5B,UAAIpO,QAAQiL,SAAS,QAAQ;AAC3B2B,eAAOQ,KAAK;UACV,GAAGpN;UACHiL,MAAM+C,YAAYC;UAClBhB,SAAS;QACX,CAAA;MACF,WAAWjN,QAAQiL,SAAS,aAAa;AACvC2B,eAAOQ,KAAK;UACV,GAAGpN;UACHiL,MAAM+C,YAAYG;UAClBlB,SAAS;QACX,CAAA;MACF;AACA;IACF;AAGA,QAAIjN,QAAQqO,yBAAwB,GAAI;AACtC,YAAMC,YAAYtO,QAAQsN,mBAAmBtN,QAAQuJ;AAGrD,UAAIuE,0BAA0BtB,IAAI8B,SAAAA,GAAY;AAC5C;MACF;AAEAR,gCAA0BS,IAAID,SAAAA;AAG9B,YAAME,0BAA0B5N,SAAS0L,OACvC,CAACC,MACCA,EAAE8B,yBAAwB,MACxB9B,EAAEe,mBAAmBf,EAAEe,oBAAoBgB,aAAc/B,EAAEhD,OAAO+E,UAAQ;AAGhF,YAAMtB,aAAyBwB,wBAAwB5J,IAAI,CAAC2H,OAAO;QACjEtL,MAAMsL,EAAEtL;QACR5B,MAAMkN,EAAEc;QACR9D,IAAIgD,EAAEhD;MACR,EAAA;AAEAqD,aAAOQ,KAAK;QACV7D,IAAI+E;QACJxB,MAAM;QACNG,SAAS;QACTD;QACA/B,MAAM+C,YAAYG;MACpB,CAAA;AAEA;IACF;AAGA,QAAInO,QAAQyO,gBAAe,GAAI;AAC7B7B,aAAOQ,KAAK;QACVN,MAAM9M,QAAQ8M;QACdG,SAASjN,QAAQ4M;QACjBrD,IAAIvJ,QAAQuJ;QACZiE,cAAcxN,QAAQyN;QACtBxM,MAAMjB,QAAQuN;QACdtC,MAAM+C,YAAYtC;MACpB,CAAA;AACA;IACF;AAEA,UAAM,IAAI5G,MAAM,wBAAwB9E,QAAQ8M,MAAM;EACxD;AAEA,SAAOF;AACT;AAnGS/J;AAqGT,SAASuC,cAAcH,aAAwB;AAC7C,QAAMyJ,gBAAgB;IAAC;IAAY;;AACnC,MAAIC,eAAe;AACnB,MAAI1J,YAAY2J,cAAc7N,YAAY;AACxC4N,mBAAezE,OAAOC,KAAKlF,YAAY2J,cAAc7N,UAAU;EACjE;AACA,MAAI,CAACkE,YAAY4J,aAAa9N,cAAc,CAACkE,YAAY6J,cAAc/N,YAAY;AACjF,WAAO4N;EACT;AACA,QAAMI,cAAc7E,OAAOC,KAAKlF,YAAY4J,aAAa9N,UAAU;AACnE,QAAMiO,eAAe9E,OAAOC,KAAKlF,YAAY6J,cAAc/N,UAAU;AAErE,SAAO;IACLmC,OAAO6L,eAAeA,YAAYpK,SAAS;SAAIoK;SAAgBL;QAAiB;IAChFpG,QAAQ0G,gBAAgBA,aAAarK,SAAS;SAAIqK;SAAiBN;QAAiB;IACpFhO,QAAQiO;EACV;AACF;AAjBSvJ;AAmBT,SAASG,yBAAyB0J,KAA0B9J,YAAoB;AAC9E,SAAO+E,OAAOgF,YAAYhF,OAAO0B,QAAQqD,GAAAA,EAAK3C,OAAO,CAAC,CAAC1B,GAAAA,MAASzF,WAAW6B,SAAS4D,GAAAA,CAAAA,CAAAA;AACtF;AAFSrF;;;AHt2BT,SAAS4J,iBAAiBC,+BAA+B;;;AIfzD,eAAsBC,mCACpBC,UACAC,cAA8B;AAE9B,QAAMC,SAASF,SAASG,UAAS;AACjC,QAAMC,UAAU,IAAIC,YAAAA;AACpB,MAAIC,SAAS,CAAA;AAEb,WAASC,cAAAA;AACP,UAAMC,gBAAgBF,OAAOG,KAAK,EAAA;AAClC,QAAID,cAAcE,KAAI,EAAGC,WAAW,GAAG;AACrC;IACF;AACA,UAAMC,QAAQJ,cAAcK,MAAM,IAAA;AAClC,QAAID,MAAMD,WAAW,GAAG;AACtB;IACF;AAEA,UAAMG,qBAAqBN,cAAcO,SAAS,IAAA;AAGlDT,aAAS,CAAA;AAET,QAAI,CAACQ,oBAAoB;AAEvBR,aAAOU,KAAKJ,MAAMK,IAAG,CAAA;IACvB;AAEAL,UACGM,IAAI,CAACC,SAASA,KAAKT,KAAI,CAAA,EACvBU,OAAO,CAACD,SAASA,QAAQ,EAAA,EACzBE,QAAQ,CAACF,SAAAA;AACRlB,mBAAaqB,KAAKC,KAAKC,MAAML,IAAAA,CAAAA;IAC/B,CAAA;EACJ;AA1BSZ;AA4BT,MAAI;AACF,WAAO,MAAM;AACX,YAAM,EAAEkB,MAAMC,MAAK,IAAK,MAAMxB,OAAOyB,KAAI;AAEzC,UAAI,CAACF,MAAM;AACTnB,eAAOU,KAAKZ,QAAQwB,OAAOF,OAAO;UAAEG,QAAQ;QAAK,CAAA,CAAA;MACnD;AAEAtB,kBAAAA;AAEA,UAAIkB,MAAM;AACR;MACF;IACF;EACF,SAASK,OAAP;AACAC,YAAQD,MAAM,mBAAmBA,KAAAA;AACjC7B,iBAAa6B,MAAMA,KAAAA;AACnB;EACF;AACA7B,eAAa+B,SAAQ;AACvB;AAxDsBjC;;;AJiBtB,SAASkC,6BAA6BC,+BAA+B;AACrE,SAASC,aAAAA,YAAWC,cAAc;AAG3B,SAASC,yBAAyB,EACvCC,UACAC,gBACAC,QAAAA,SACAC,UACAC,YAAW,GAOZ;AACC,QAAMC,SAASL,SAASK,OAAOC,IAAI,CAACC,WAAW;IAC7CC,MAAMD,MAAMC;IACZC,aAAaF,MAAME;IACnBC,YAAY,CAAA;IACZC,SAAS,OAAOC,UAAAA;IAAgB;IAChCC,oBAAoB,OAAO,EACzBL,MACAM,2BACAC,UACAC,UACAC,qBAAqB,CAAA,GACrBC,WAAU,MACe;AACzBhB,MAAAA,QAAOiB,MAAM;QAAEC,YAAYb,MAAMC;MAAK,GAAG,oCAAA;AAEzCa,+BAAUC,QAAQ,sCAAsC;QACtDC,gBAAgB;QAChBC,MAAM;QACNC,cAAczB,SAASK,OAAOqB;QAC9BC,cAAc3B,SAAS4B,kBACnBC,YAAW,QAAA,EAAUC,OAAO9B,SAAS4B,eAAe,EAAEG,OAAO,KAAA,IAC7D;MACN,CAAA;AAEA,UAAIC,QAAQ,CAAC;AACb,UAAIC,SAAS,CAAC;AACd,UAAI7B,aAAa;AACf,cAAM8B,YAAY9B,YAAY+B,KAAK,CAACH,WAAUA,OAAMI,cAAc5B,IAAAA;AAClE,YAAI0B,WAAW;AACbF,kBAAQK,WAAUH,UAAUF,OAAO,CAAC,CAAA;AACpCC,mBAASI,WAAUH,UAAUD,QAAQ,CAAC,CAAA;QACxC;MACF;AAEA,UAAI;AACF,cAAMK,WAAW,MAAMC,QAAQ;UAC7BrC,QAAQA,QAAOsC,MAAM;YAAEC,WAAW;UAA+C,CAAA;UACjFC,eAAe1C,SAAS0C;UACxBd,iBAAiB5B,SAAS4B;UAC1BrB;UACAQ;UACAC;UACAb,UAAU;eAAIA;eAAac;;UAC3Be;UACAC;UACAU,YAAY1C,eAAe0C;UAC3BC,SAASC,OAAO/B,2BAA2B,CAACgC,YAAyB;YACnEtC,MAAMsC,OAAOtC;YACbC,aAAaqC,OAAOrC;YACpBC,YAAYqC,KAAKC,MAAMF,OAAOG,UAAU;UAC1C,EAAA;UACA/B;QACF,CAAA;AAEA,cAAMgC,cAAc,IAAIC,2BAAAA;AACxBC,2CAAmCd,UAAUY,YAAYG,YAAY;AACrE,eAAOH,YAAYI,uBAAsB;MAC3C,SAASC,OAAP;AACArD,QAAAA,QAAOqD,MACL;UAAEC,KAAKxD,SAAS0C;UAAee,QAAQ;UAAKC,MAAMH,MAAMI;QAAQ,GAChE,4CAAA;AAEF,cAAM,IAAIC,MAAM,4CAAA;MAClB;IACF;EACF,EAAA;AAEA,SAAO;OAAIvD;;AACb;AAjFgBN;;UAmFJ8D,kBAAAA;;;GAAAA,oBAAAA,kBAAAA,CAAAA,EAAAA;AAKL,SAASC,uBAAuB,EACrCC,MACAP,KACAQ,iBACA/D,gBACAC,QAAAA,SACAC,UACAC,YAAW,GASZ;AACC,QAAM6D,cAAcC,MAAMC,QAAQJ,KAAK,QAAA,CAAS,IAAIA,KAAK,QAAA,EAAUrC,SAAS;AAE5E,QAAMkB,UAAUmB,KAAK,SAAA,EAAWzD,IAAI,CAACwC,YAAY;IAC/CtC,MAAMsC,OAAOtC;IACbC,aAAaqC,OAAOrC;IACpBC,YAAYoC,OAAOpC;IACnBC,SAAS,OAAOyD,SAAAA;AACdlE,MAAAA,QAAOiB,MAAM;QAAEC,YAAY0B,OAAOtC;QAAM4D;MAAK,GAAG,yBAAA;AAEhD,YAAMC,UAAUC,cAAcN,iBAAiB/D,cAAAA;AAC/CoB,+BAAUC,QAAQ,sCAAsC;QACtDC,gBAAgB;QAChBC,MAAM;QACNC,cAAcwC;MAChB,CAAA;AAEA,YAAMM,WAAW,GAAGf;AACpB,UAAI;AACF,cAAMlB,WAAW,MAAMkC,MAAMD,UAAU;UACrCE,QAAQ;UACRJ;UACAX,MAAMX,KAAK2B,UAAU;YACnBlE,MAAMsC,OAAOtC;YACbmE,WAAWP;YACXzB,YAAY1C,eAAe0C;UAC7B,CAAA;QACF,CAAA;AAEA,YAAI,CAACL,SAASsC,IAAI;AAChB1E,UAAAA,QAAOqD,MACL;YAAEC;YAAKC,QAAQnB,SAASmB;YAAQC,MAAM,MAAMpB,SAASuC,KAAI;UAAG,GAC5D,iCAAA;AAEF,cAAIvC,SAASmB,WAAW,KAAK;AAC3B,kBAAM,IAAIqB,4BAA4B;cAAEtB,KAAKe;YAAS,CAAA;UACxD;AACA,gBAAM,IAAIQ,wBAAwB;YAChCtB,QAAQnB,SAASmB;YACjBD,KAAKe;YACLS,kBAAkB;UACpB,CAAA;QACF;AAEA,cAAMC,gBAAgB,MAAM3C,SAASyB,KAAI;AAEzC,cAAMmB,SAASD,cAAc,QAAA;AAC7B/E,QAAAA,QAAOiB,MAAM;UAAEC,YAAY0B,OAAOtC;UAAM0E;QAAO,GAAG,wBAAA;AAClD,eAAOA;MACT,SAAS3B,OAAP;AACA,YAAIA,iBAAiB4B,iBAAiB;AACpC,gBAAM5B;QACR;AACA,cAAM,IAAI6B,wBAAwB;UAAE7B;UAAOC,KAAKe;QAAS,CAAA;MAC3D;IACF;EACF,EAAA;AAEA,QAAMlE,SAAS4D,cACXF,KAAK,QAAA,EAAUzD,IAAI,CAACC,WAAW;IAC7BC,MAAMD,MAAMC;IACZC,aAAaF,MAAME;IACnBC,YAAY,CAAA;IACZC,SAAS,OAAOC,UAAAA;IAAgB;IAEhCC,oBAAoB,OAAO,EACzBL,MACAM,2BACAC,UACAC,UACAC,qBAAqB,CAAA,GACrBC,WAAU,MACe;AACzBhB,MAAAA,QAAOiB,MAAM;QAAEC,YAAYb,MAAMC;MAAK,GAAG,wBAAA;AAEzC,YAAM6D,UAAUC,cAAcN,iBAAiB/D,cAAAA;AAC/CoB,+BAAUC,QAAQ,sCAAsC;QACtDC,gBAAgB;QAChBC,MAAM;QACNC,cAAcsC,KAAK,QAAA,EAAUrC;MAC/B,CAAA;AAEA,UAAIM,QAAQ,CAAC;AACb,UAAIC,SAAS,CAAC;AACd,UAAI7B,aAAa;AACf,cAAM8B,YAAY9B,YAAY+B,KAAK,CAACH,WAAUA,OAAMI,cAAc5B,IAAAA;AAClE,YAAI0B,WAAW;AACbF,kBAAQK,WAAUH,UAAUF,OAAO,CAAC,CAAA;AACpCC,mBAASI,WAAUH,UAAUD,QAAQ,CAAC,CAAA;QACxC;MACF;AAEA,YAAMsC,WAAW,GAAGf;AACpB,UAAI;AACF,cAAMlB,WAAW,MAAMkC,MAAMD,UAAU;UACrCE,QAAQ;UACRJ;UACAX,MAAMX,KAAK2B,UAAU;YACnBlE;YACAO;YACAC;YACAb,UAAU;iBAAIA;iBAAac;;YAC3Be;YACAC;YACAU,YAAY1C,eAAe0C;YAC3BC,SAASC,OAAO/B,2BAA2B,CAACgC,YAAyB;cACnEtC,MAAMsC,OAAOtC;cACbC,aAAaqC,OAAOrC;cACpBC,YAAYqC,KAAKC,MAAMF,OAAOG,UAAU;YAC1C,EAAA;YACA/B;UACF,CAAA;QACF,CAAA;AAEA,YAAI,CAACoB,SAASsC,IAAI;AAChB1E,UAAAA,QAAOqD,MACL;YAAEC;YAAKC,QAAQnB,SAASmB;YAAQC,MAAM,MAAMpB,SAASuC,KAAI;UAAG,GAC5D,gCAAA;AAEF,cAAIvC,SAASmB,WAAW,KAAK;AAC3B,kBAAM,IAAIqB,4BAA4B;cAAEtB,KAAKe;YAAS,CAAA;UACxD;AACA,gBAAM,IAAIQ,wBAAwB;YAChCtB,QAAQnB,SAASmB;YACjBD,KAAKe;YACLS,kBAAkB;UACpB,CAAA;QACF;AAEA,YAAIzE,MAAMiB,SAAI,aAAgC;AAC5C,gBAAM0B,cAAc,IAAIC,2BAAAA;AACxBC,6CAAmCd,SAASoB,MAAOR,YAAYG,YAAY;AAC3E,iBAAOH,YAAYI,uBAAsB;QAC3C,WAAW/C,MAAMiB,SAAI,UAA6B;AAChD,gBAAM6B,eAAe,IAAIgC,oBAAAA;AACzBjC,6CAAmCd,SAASoB,MAAOL,YAAAA;AACnD,iBAAOA;QACT,OAAO;AACL,gBAAM,IAAIO,MAAM,wBAAA;QAClB;MACF,SAASL,OAAP;AACA,YAAIA,iBAAiB4B,iBAAiB;AACpC,gBAAM5B;QACR;AACA,cAAM,IAAI6B,wBAAwB;UAAE7B;UAAOC,KAAKe;QAAS,CAAA;MAC3D;IACF;EACF,EAAA,IACA,CAAA;AAEJ,SAAO;OAAI3B;OAAYvC;;AACzB;AAvKgByD;AAyKT,SAASQ,cACdN,iBACA/D,gBAA8B;AAE9B,QAAMoE,UAAU;IACd,gBAAgB;EAClB;AAEA,MAAIL,iBAAiB;AACnB,UAAM,EAAEK,SAASiB,kBAAiB,IAAKtB,gBAAgB;MAAEuB,KAAKtF;IAAe,CAAA;AAC7E,QAAIqF,mBAAmB;AACrBE,aAAOC,OAAOpB,SAASiB,iBAAAA;IACzB;EACF;AAEA,SAAOjB;AACT;AAhBgBC;;;AD3QhB,SACEoB,2BAAAA,0BACAC,2BAAAA,0BACAC,mBAAAA,wBACK;;;AMFP,SAASC,aAAAA,kBAAiB;AAEnB,SAASC,+BAA+B,EAC7CC,QAAAA,SACAC,UACAC,aACAC,MAAK,GAMN;AACC,QAAMC,SAAS;IACbC,MAAMF,MAAMG;IACZC,aAAaJ,MAAMI;IACnBC,YAAY,CAAA;IACZC,SAAS,OAAOC,UAAAA;IAAgB;IAChCC,oBAAoB,OAAO,EACzBC,2BACAC,SAAQ,MACiB;AACzBb,MAAAA,QAAOc,MAAM;QAAEC,YAAYZ,MAAMG;MAAQ,GAAG,wBAAA;AAE5C,YAAMU,oBAAoBC,2BAA2BhB,QAAAA;AACrDE,YAAMF,WAAWe;AACjBb,YAAMU,WAAWA;AAEjBK,+BAAUC,QAAQ,sCAAsC;QACtDC,gBAAgB;QAChBC,MAAM;QACNC,cAAc;MAChB,CAAA;AAEA,UAAIC,QAAQ,CAAC;AACb,UAAIrB,aAAa;AACf,cAAMsB,YAAYtB,YAAYuB,KAAK,CAACF,WAAUA,OAAMG,cAAcvB,MAAMG,OAAO;AAC/E,YAAIkB,WAAW;AACbD,kBAAQI,WAAUH,UAAUD,OAAO,CAAC,CAAA;QACtC;MACF;AACApB,YAAMoB,QAAQA;AAEd,YAAMK,QAAQhB,0BAA0BiB,IAAI,CAACC,UAAAA;AAC3C,eAAO;UACLzB,MAAMyB,MAAMzB;UACZE,aAAauB,MAAMvB;UACnBC,YAAYuB,KAAKC,MAAMF,MAAMG,UAAU;QACzC;MACF,CAAA;AAEA,aAAO9B,MAAM+B,qCAAqC;QAChDN;MACF,CAAA;IACF;EACF;AACA,SAAO;IAACxB;;AACV;AAvDgBL;AAyDT,SAASkB,2BAA2BhB,UAAmB;AAC5D,QAAMkC,SAA6B,CAAA;AAEnC,aAAWC,WAAWnC,UAAU;AAC9B,QAAImC,QAAQC,cAAa,GAAI;AAC3BF,aAAOG,KAAK;QACVC,IAAIH,QAAQG;QACZC,MAAMJ,QAAQI;QACdC,SAASL,QAAQK;MACnB,CAAA;IACF,WAAWL,QAAQM,yBAAwB,GAAI;AAC7C,YAAMC,WAAqB;QACzBJ,IAAIH,QAAQG;QACZlB,MAAM;QACNuB,UAAU;UACRvC,MAAM+B,QAAQ/B;UACdwC,WAAWd,KAAKe,UAAUV,QAAQS,SAAS;QAC7C;MACF;AAEA,UAAIT,QAAQW,mBAAmBZ,OAAOa,KAAK,CAACC,MAAMA,EAAEV,OAAOH,QAAQW,eAAe,GAAG;AACnF,cAAMG,gBAAuDf,OAAOV,KAClE,CAACwB,MAAMA,EAAEV,OAAOH,QAAQW,eAAe;AAEzC,YAAIG,cAAcC,cAAcC,QAAW;AACzCF,wBAAcC,YAAY,CAAA;QAC5B;AACAD,sBAAcC,UAAUb,KAAKK,QAAAA;MAC/B,OAAO;AACLR,eAAOG,KAAK;UACVC,IAAIH,QAAQW,mBAAmBX,QAAQG;UACvCC,MAAM;UACNW,WAAW;YAACR;;QACd,CAAA;MACF;IACF,WAAWP,QAAQiB,gBAAe,GAAI;AACpClB,aAAOG,KAAK;QACVC,IAAIH,QAAQG;QACZC,MAAM;QACNC,SAASL,QAAQD;QACjBmB,YAAYlB,QAAQmB;MACtB,CAAA;IACF;EACF;AAEA,SAAOpB;AACT;AA9CgBlB;;;;UNlDJuC,eAAAA;;;GAAAA,iBAAAA,eAAAA,CAAAA,EAAAA;AA+CL,SAASC,oBAAoBC,QAAmB;AACrD,MAAI,CAACA,QAAQ;AACX,WAAO;EACT;AACA,SAAO,OAAQA,OAA6BC,uBAAuB;AACrE;AALgBF;AAOhB,eAAeG,gBAAgB,EAC7BC,KACAC,iBACAC,gBACAC,QAAAA,SACAC,YAAW,GAOZ;AACCD,EAAAA,QAAOE,MAAM;IAAEL;EAAI,GAAG,2BAAA;AACtB,QAAMM,UAAUC,cAAcN,iBAAiBC,cAAAA;AAE/C,QAAMM,WAAW,GAAGR;AACpB,MAAI;AACF,UAAMS,WAAW,MAAMC,MAAMF,UAAU;MACrCG,QAAQ;MACRL;MACAM,MAAMC,KAAKC,UAAU;QAAEC,YAAYb,eAAea;QAAYX;MAAY,CAAA;IAC5E,CAAA;AAEA,QAAI,CAACK,SAASO,IAAI;AAChBb,MAAAA,QAAOc,MACL;QAAEjB;QAAKkB,QAAQT,SAASS;QAAQN,MAAM,MAAMH,SAASU,KAAI;MAAG,GAC5D,kCAAA;AAEF,YAAM,IAAIC,yBAAwB;QAChCF,QAAQT,SAASS;QACjBlB,KAAKQ;QACLa,kBAAkB;MACpB,CAAA;IACF;AAEA,UAAMC,OAAO,MAAMb,SAASa,KAAI;AAChCnB,IAAAA,QAAOE,MAAM;MAAEiB;IAAK,GAAG,0BAAA;AACvB,WAAOA;EACT,SAASL,OAAP;AACA,QAAIA,iBAAiBM,kBAAiB;AACpC,YAAMN;IACR;AACA,UAAM,IAAIO,yBAAwB;MAAEP;MAAOjB,KAAKQ;IAAS,CAAA;EAC3D;AACF;AA7CeT;AA+Cf,eAAsB0B,mBAAmB,EACvCC,2BACAxB,gBACAyB,UACAC,aACAxB,aACAyB,OAAM,GAQP;AACC,QAAM1B,UAASD,eAAeC,OAAO2B,MAAM;IAAEC,WAAW;EAAoC,CAAA;AAC5F5B,EAAAA,QAAOE,MAAM;IAAEqB;EAA0B,GAAG,gCAAA;AAG5C,QAAMM,WAAWN,0BAA0BO,OAAO,CAACC,OAAOC,OAAOC,SAAAA;AAC/D,QAAIF,MAAMG,SAAI,sBAAqC;AACjD,aAAOH;IACT;AACA,WAAOC,UAAUC,KAAKE,UAAU,CAACC,MAA0BA,EAAEvC,QAAQkC,MAAMlC,GAAG;EAChF,CAAA;AAEA,QAAMwC,SAAS,MAAMC,QAAQC,IAC3BV,SAASW,IAAI,OAAOC,aAAAA;AAElB,QAAIA,SAASP,SAAI,sBAAqC;AACpD,aAAOQ,yBAAyB;QAC9BD;QACAjB;QACAzB;QACAC,QAAQA,QAAO2B,MAAM;UACnBC,WAAW;UACXa;QACF,CAAA;QACAhB;MACF,CAAA;IACF;AAEA,UAAMN,OAAO,MAAMvB,gBAAgB;MACjCC,KAAK4C,SAAS5C;MACdC,iBAAiB2C,SAAS3C;MAC1BC;MACAC,QAAQA,QAAO2B,MAAM;QAAEC,WAAW;QAAsCa;MAAS,CAAA;MACjFxC;IACF,CAAA;AAEA,WAAO0C,uBAAuB;MAC5BxB;MACAK;MACA3B,KAAK4C,SAAS5C;MACdC,iBAAiB2C,SAAS3C;MAC1BC;MACAC,QAAQA,QAAO2B,MAAM;QAAEC,WAAW;QAAmCa;MAAS,CAAA;MAC9EhB;IACF,CAAA;EACF,CAAA,CAAA;AAGF,aAAW,CAACmB,KAAKC,KAAAA,KAAUC,OAAOC,QAAQrB,MAAAA,GAAS;AACjD,QAAImB,MAAMG,YAAYC,UAAaJ,MAAMG,YAAYJ,KAAK;AACxD,YAAM,IAAIxB,iBAAgB;QACxB8B,SAAS,SAASN,mBAAmBC,MAAMG,wCAAwCJ;QACnFO,MAAMC,oBAAoBC;MAC5B,CAAA;IACF,WAAWR,MAAMG,YAAYC,QAAW;AACtCJ,YAAMG,UAAUJ;IAClB;AAEAP,WAAOiB,KACLC,+BAA+B;MAC7BvD,QAAAA;MACAwB;MACAC;MACAoB;IACF,CAAA,CAAA;EAEJ;AAEA,SAAOR,OAAOmB,KAAI;AACpB;AAnFsBlC;;;AD3HtB,SAASmC,cAAAA,mBAAkB;;;AQY3B,SAEEC,8BAEAC,2BAAAA,0BACAC,+BAAAA,8BACAC,YAAAA,WACAC,mBAAAA,kBACAC,2BAAAA,0BACAC,+BACAC,yBAAAA,8BACK;;;AChBP,SAASC,uBAAuB;AAChC,SAASC,UAAAA,eAAc;AAEhB,SAASC,0BAA0BC,eAA6B;AACrE,QAAMC,WAAWC,QAAOF,eAAe,CAACG,YAAAA;AACtC,QAAIA,QAAQC,aAAa;AACvB,aAAOC,gBAAgBC,aAAa;QAClCC,IAAIJ,QAAQI;QACZC,WAAWL,QAAQK;QACnBC,MAAMN,QAAQC,YAAYK;QAC1BC,SAASP,QAAQC,YAAYM;QAC7BC,iBAAiBR,QAAQC,YAAYO;MACvC,CAAA;IACF,WAAWR,QAAQS,cAAc;AAC/B,aAAOP,gBAAgBQ,cAAc;QACnCN,IAAIJ,QAAQI;QACZC,WAAWL,QAAQK;QACnBC,MAAMN,QAAQS,aAAaH;QAC3BK,OAAOX,QAAQS,aAAaE;QAC5BC,QAAQZ,QAAQS,aAAaG;QAC7BJ,iBAAiBR,QAAQS,aAAaD;MACxC,CAAA;IACF,WAAWR,QAAQa,wBAAwB;AACzC,aAAOX,gBAAgBY,wBAAwB;QAC7CV,IAAIJ,QAAQI;QACZC,WAAWL,QAAQK;QACnBU,MAAMf,QAAQa,uBAAuBE;QACrCC,WAAWC,KAAKC,MAAMlB,QAAQa,uBAAuBG,SAAS;QAC9DR,iBAAiBR,QAAQa,uBAAuBL;MAClD,CAAA;IACF,WAAWR,QAAQmB,eAAe;AAChC,aAAOjB,gBAAgBkB,eAAe;QACpChB,IAAIJ,QAAQI;QACZC,WAAWL,QAAQK;QACnBgB,mBAAmBrB,QAAQmB,cAAcE;QACzCC,YAAYtB,QAAQmB,cAAcG;QAClCC,QAAQvB,QAAQmB,cAAcI;MAChC,CAAA;IACF,WAAWvB,QAAQwB,mBAAmB;AACpC,aAAOtB,gBAAgBuB,mBAAmB;QACxCrB,IAAIJ,QAAQI;QACZsB,UAAU1B,QAAQwB,kBAAkBE;QACpCrB,WAAWL,QAAQK;QACnBsB,WAAW3B,QAAQwB,kBAAkBG;QACrCC,UAAU5B,QAAQwB,kBAAkBI;QACpCC,OAAO7B,QAAQwB,kBAAkBK;QACjCC,QAAQ9B,QAAQwB,kBAAkBM;QAClCxB,MAAMN,QAAQwB,kBAAkBlB;QAChCyB,OAAOd,KAAKC,MAAMlB,QAAQwB,kBAAkBO,KAAK;QACjDC,SAAShC,QAAQwB,kBAAkBQ;MACrC,CAAA;IACF,OAAO;AACL,aAAO;IACT;EACF,CAAA;AAEA,SAAOlC,SAASmC,OAAO,CAACC,MAAMA,CAAAA;AAChC;AAtDgBtC;;;ADwChB,SAASuC,YAAY;AAQrB,SAASC,UAAUC,wBAAuB;;;AElBnC,SAASC,4BACdC,cAA0C;AAN5C;AAQE,QAAMC,aAA0B,CAAA;AAGhC,QAAMC,SACJ,aAAaF,gBAAgB,CAAC,KACzBA,aAAyBE,SACzBF;AAEP,QAAMG,kBAAiBD,iCAAQD,iBAAcC,sCAAQD,eAARC,mBAAoBE;AACjE,QAAMC,aAAaF,iDAAgBE;AACnC,QAAMC,iBAAiB,IAAIC,KAAIJ,iDAAgBK,aAAY,CAAA,CAAE;AAE7D,MAAI,CAACH,YAAY;AACf,WAAOJ;EACT;AAEA,aAAWQ,aAAaJ,YAAY;AAClC,QAAIK,OAAOC,UAAUC,eAAeC,KAAKR,YAAYI,SAAAA,GAAY;AAC/D,YAAMK,WAAWT,WAAWI,SAAAA;AAC5BR,iBAAWc,KAAK;QACdC,MAAMP;;;QAGNQ,MAAMH,SAASG,QAAQ;QACvBC,aAAaJ,SAASI;QACtBV,UAAUF,eAAea,IAAIV,SAAAA;MAG/B,CAAA;IACF;EACF;AAEA,SAAOR;AACT;AApCgBF;AA4CT,SAASqB,yBACdC,UACAC,aAAmB;AAEnB,QAAMC,UAAyB,CAAA;AAE/B,aAAW,CAACC,UAAUC,IAAAA,KAASf,OAAOgB,QAAQL,QAAAA,GAAW;AACvD,UAAMpB,aAAaF,4BAA4B0B,IAAAA;AAE/C,UAAME,UAAU,8BAAOC,WAAAA;AACrB,UAAI;AACF,cAAMC,SAAS,MAAMJ,KAAKK,QAAQ;UAAEF;QAAO,CAAA;AAG3C,eAAO,OAAOC,WAAW,WAAWA,SAASE,KAAKC,UAAUH,MAAAA;MAC9D,SAASI,OAAP;AACAC,gBAAQD,MACN,6BAA6BT,2BAA2BF,gBACxDW,KAAAA;AAGF,cAAM,IAAIE,MACR,kCAAkCX,cAChCS,iBAAiBE,QAAQF,MAAMG,UAAUC,OAAOJ,KAAAA,GAChD;MAEN;IACF,GAlBgB;AAoBhBV,YAAQR,KAAK;MACXC,MAAMQ;MACNN,aAAaO,KAAKP,eAAe,aAAaM,kBAAkBF;MAChErB;MACA0B;;MAEAW,YAAY;MACZC,cAAcjB;IAChB,CAAA;EACF;AAEA,SAAOC;AACT;AAzCgBH;AA+CT,SAASoB,4BAA4BC,UAAiC;AAC3E,MAAI,CAACA,YAAY/B,OAAOgC,KAAKD,QAAAA,EAAUE,WAAW,GAAG;AACnD,WAAO;EACT;AAEA,QAAMC,cAAclC,OAAOgB,QAAQe,QAAAA;AAGnC,QAAMI,WAAWD,YACdE,IAAI,CAAC,CAAC9B,MAAMS,IAAAA,MAAK;AAEhB,QAAIsB,YAAY;AAEhB,QAAI;AACF,UAAItB,KAAKvB,UAAU,OAAOuB,KAAKvB,WAAW,UAAU;AAClD,cAAMA,SAASuB,KAAKvB;AAGpB,YAAIA,OAAOG,YAAY;AACrB,gBAAMC,iBAAiBJ,OAAOM,YAAY,CAAA;AAG1C,gBAAMwC,aAAatC,OAAOgB,QAAQxB,OAAOG,UAAU,EAAEyC,IAAI,CAAC,CAACrC,WAAWwC,UAAAA,MAAW;AAC/E,kBAAMC,cAAcD;AACpB,kBAAME,eAAe7C,eAAe8C,SAAS3C,SAAAA,IAAa,MAAM;AAChE,kBAAM4C,WAAWH,YAAYjC,QAAQ;AACrC,kBAAMC,cAAcgC,YAAYhC,cAAc,MAAMgC,YAAYhC,gBAAgB;AAEhF,mBAAO,SAAST,YAAY0C,iBAAiBE,YAAYnC;UAC3D,CAAA;AAEA,cAAI8B,WAAWL,SAAS,GAAG;AACzBI,wBAAYC,WAAWM,KAAK,IAAA;UAC9B;QACF;MACF;IACF,SAASC,GAAP;AACArB,cAAQD,MAAM,iCAAiCjB,SAASuC,CAAAA;IAC1D;AAEA,WAAO,KAAKvC,SAASS,KAAKP,eAAe;EAC7C6B;EACE,CAAA,EACCO,KAAK,MAAA;AAER,SAAO;;EAEPT;;;;;;;AAOF;AAtDgBL;;;AFqIT,IAAMgB,iBAAN,MAAMA;EACJC;EACAC;EACAC;EACCC,YAAoC,CAAA;EACpCC;EACAC;EACAC;EACAC;EACAC;;EAGSC;EACTC,iBAAiB,oBAAIC,IAAAA;;;EAIZC;;EAGjBC,YAAYC,QAA6C;AA9R3D;AA+RI,SACEA,iCAAQd,aACRc,iCAAQC,qBACRD,iCAAQC,gBAAgBC,KAAK,CAACC,MAAMA,EAAEC,SAASC,aAAaC,qBAC5D;AACAC,cAAQC,KAAK,qEAAA;IACf;AACA,SAAKtB,WAAUc,iCAAQd,YAAW,CAAA;AAClC,SAAKQ,kBAAkB,CAAA;AAEvB,eAAWe,UAAST,iCAAQX,cAAa,CAAA,GAAI;AAC3C,YAAMqB,cAAc,IAAIC,YAAYF,KAAAA;AACpC,WAAKpB,UAAUuB,KAAKF,YAAYG,SAAQ,CAAA;IAC1C;AAEA,SAAKzB,6BAA4BY,iCAAQC,qBAAmBD,iCAAQc,kBAAiB,CAAA;AAErF,SAAKxB,mBAAkBU,sCAAQe,eAARf,mBAAoBV;AAC3C,SAAKC,kBAAiBS,sCAAQe,eAARf,mBAAoBT;AAC1C,SAAKC,2CACHQ,iCAAQR,4CAA2C;AACrD,SAAKC,gBAAgBO,iCAAQgB;AAC7B,SAAK7B,UAASa,iCAAQb,WAAU,CAAC;AAEjC,SAAKQ,mBAAmBK,iCAAQiB;AAChC,SAAKnB,sBAAsBE,iCAAQkB;AAGnC,QAAI,KAAKvB,oBAAoB,KAAKA,iBAAiBwB,SAAS,KAAK,CAAC,KAAKrB,qBAAqB;AAC1F,YAAM,IAAIsB,uBAAsB;QAC9BC,SACE;MAEJ,CAAA;IACF;AAGA,SACErB,iCAAQd,eACPc,sCAAQC,oBAARD,mBAAyBE,KAAK,CAACC,MAAMA,EAAEC,SAASC,aAAaC,yBAC5D,UAAKX,qBAAL,mBAAuBwB,UACzB;AACAZ,cAAQC,KACN,6KAAA;IAEJ;EACF;;EAGQc,0BACNC,UACAC,gBACgB;AAEhB,UAAMC,uBAAuBD,eAAeE,OAAO,CAACC,WAAYA,OAAeC,UAAU;AAEzF,QAAI,CAACH,wBAAwBA,qBAAqBN,WAAW,GAAG;AAC9D,aAAOI;IACT;AAGA,UAAMM,iBAAiB,oBAAIhC,IAAAA;AAG3B4B,yBAAqBK,QAAQ,CAACH,WAAAA;AAC5BE,qBAAeE,IAAIJ,OAAOK,MAAML,MAAAA;IAClC,CAAA;AAIA,UAAMM,WAAoC,CAAC;AAC3CC,UAAMC,KAAKN,eAAeO,OAAM,CAAA,EAAIN,QAAQ,CAACH,WAAAA;AAC3CM,eAASN,OAAOK,IAAI,IAAI;QACtBK,aAAaV,OAAOU,eAAe;QACnCC,QAAQX,OAAOY,aACX;UACEA,YAAY;YACVC,YAAYb,OAAOY,WAAWE,OAC5B,CAACC,KAAKC,OAAO;cACX,GAAGD;cACH,CAACC,EAAEX,IAAI,GAAG;gBAAE5B,MAAMuC,EAAEvC;gBAAMiC,aAAaM,EAAEN;cAAY;YACvD,IACA,CAAC,CAAA;YAEHO,UAAUjB,OAAOY,WAAWb,OAAO,CAACiB,MAAMA,EAAEC,QAAQ,EAAEC,IAAI,CAACF,MAAMA,EAAEX,IAAI;UACzE;QACF,IACA,CAAC;QACLc,SAAS,aAAa,CAAC;MACzB;IACF,CAAA;AAGA,UAAMC,sBAAsBC,4BAA4Bf,QAAAA;AAExD,QAAI,CAACc,qBAAqB;AACxB,aAAOxB;IACT;AAEA,UAAM0B,eACJ,wGACAF,sBACA;AAEF,UAAMG,qBAAqB3B,SAAS4B,UAAU,CAACC,QAAAA;AAvYnD;AAuY2DA,wBAAIC,gBAAJD,mBAAiBE,UAAS;KAAA;AAEjF,UAAMC,cAAc;SAAIhC;;AAExB,QAAI2B,uBAAuB,IAAI;AAC7B,YAAMM,cAAcD,YAAYL,kBAAAA;AAChC,UAAIM,YAAYH,aAAa;AAC3BG,oBAAYH,YAAYI,WACrBD,YAAYH,YAAYI,UAAUD,YAAYH,YAAYI,UAAU,SAAS,MAC9ER;MACJ;IACF,OAAO;AACLM,kBAAYG,QAAQ;QAClBC,IAAIC,UAAAA;QACJC,WAAW,oBAAIC,KAAAA;QACfT,aAAa;UACXC,MAAMS,YAAYC;UAClBP,SAASR;QACX;QACAgB,wBAAwBC;QACxBC,eAAeD;QACfE,mBAAmBF;MACrB,CAAA;IACF;AAEA,WAAOX;EACT;;EAGA,MAAMc,sBAAsBC,SAAiE;AApa/F;AAqaI,UAAM,EACJC,gBACAhD,UAAUiD,aACVtF,SAASuF,wBACTC,UACAC,OACAC,uBACAC,gBACAC,qBACAC,KACAC,YACAC,cACAC,aACAC,aAAY,IACVb;AAEJ,UAAMc,cAAc,IAAIC,mBAAAA;AAExB,UAAMC,mBAAmBxB,KAAKyB,IAAG;AAEjC,UAAMC,iBAAwB,CAAA;AAE9B,QAAI;AACF,UAAIP,gBAAgB,CAAC,KAAKzF,yCAAyC;AACjE,eAAO,MAAM,KAAKiG,oBAAoBnB,OAAAA;MACxC;AACA,UAAIC,0BAA0BmB,cAAc;AAC1C,cAAM,IAAItE,uBAAsB;UAC9BC,SAAS;;;QAGX,CAAA;MACF;AAGA,YAAMsE,oBAAoB,MAAM,KAAKC,qBAAqBtB,OAAAA;AAI1D,YAAMuB,sBAAsBrB,YAAY9C,OAAO,CAACL,YAAY,CAACA,QAAQ+C,iBAAiB;AAGtF,YAAM0B,mCAAmC,KAAKxE,0BAC5CuE,qBACAF,iBAAAA;AAEF,YAAMI,gBAAgBC,0BAA0BF,gCAAAA;AAIhD,YAAI,UAAKrG,kBAAL,mBAAoBwG,YAAWd,cAAc;AAC/C,YAAI;AACF,gBAAMe,cAA8B;YAClCxB;YACAC;YACAwB,OAAOrB,2DAAqBqB;YAC5B5E,UAAUwE;YACV7G,SAASuF;YACTK;YACAsB,WAAWd;YACXe,UAAU,KAAKC,eAAe/B,cAAAA;UAChC;AAEA,gBAAM,KAAK9E,cAAc8G,MAAMC,cAAcN,WAAAA;QAC/C,SAASO,OAAP;AACAlG,kBAAQkG,MAAM,8BAA8BA,KAAAA;QAC9C;MACF;AAEA,YAAMC,yBAAwCf,kBAAkB9C,IAAI,CAAClB,YAAY;QAC/EK,MAAML,OAAOK;QACbK,aAAaV,OAAOU;QACpBsE,YAAYC,KAAKC,UAAUC,6BAA6BnF,OAAOY,UAAU,CAAA;MAC3E,EAAA;AAEA,YAAMwE,eAAeC,6BAA6B;WAC7CN;WACAjC,uBAAuB/C;;UAExB,CAACC,WAAWA,OAAOsF,cAAcC,wBAAwBC;QAAM;OAElE;AAED,cAAM,UAAK7H,oBAAL,8BAAuB;QAC3BoF;QACAC;QACAoB;QACAvD,YAAYqC,eAAerC;QAC3BuC;MACF;AAEA,YAAMqC,SAAS,MAAM7C,eAAe8C,QAAQ;QAC1C9F,UAAUwE;QACV7G,SAAS6H;QACTrC;QACAC;QACAS;QACAN;QACAE;QACAC;QACAC;MACF,CAAA;AAIA,YAAMoC,mBAAmB5C,YAAY0C,OAAO1C;AAE5CE,4BACG2C,KAAK,CAACC,mBAAAA;AAjhBf,YAAAC;AAkhBU,SAAAA,MAAA,KAAKlI,mBAAL,gBAAAkI,IAAA,WAAsB;UACpB/C,UAAU4C;UACV3C,OAAOyC,OAAOzC;UACdoB;UACAyB;UACAhF,YAAYqC,eAAerC;UAC3BuC;QACF;MACF,CAAA,EACC2C,MAAM,CAACC,WAAAA;MAAY,CAAA;AAGtB,YAAI,UAAKlI,kBAAL,mBAAoBwG,YAAWd,cAAc;AAC/C,YAAI;AACFP,gCACG2C,KAAK,CAACC,mBAAAA;AACL,kBAAMI,eAAgC;cACpClD,UAAU0C,OAAO1C;cACjBC,OAAOyC,OAAOzC;cACdwB,OAAOrB,2DAAqBqB;;cAE5B0B,QAAQ,KAAKpI,cAAcqI,cAActC,iBAAiBgC;cAC1DO,SAASjE,KAAKyB,IAAG,IAAKD;cACtBc,WAAWtC,KAAKyB,IAAG;cACnBc,UAAU,KAAKC,eAAe/B,cAAAA;;cAE9ByD,iBAAiB;YACnB;AAEA,gBAAI;AACF,mBAAKvI,cAAc8G,MAAM0B,eAAeL,YAAAA;YAC1C,SAASM,UAAP;AACA3H,sBAAQkG,MAAM,+BAA+ByB,QAAAA;YAC/C;UACF,CAAA,EACCR,MAAM,CAACjB,UAAAA;AACNlG,oBAAQkG,MAAM,8CAA8CA,KAAAA;UAC9D,CAAA;QACJ,SAASA,OAAP;AACAlG,kBAAQkG,MAAM,8CAA8CA,KAAAA;QAC9D;MACF;AAGA,YAAI,UAAKhH,kBAAL,mBAAoBwG,YAAW,KAAKxG,cAAcqI,eAAe3C,cAAc;AAEjF,cAAMgD,iBAAiB/C,YAAYgD,OAAOC,KAAKjD,WAAAA;AAG/CA,oBAAYgD,SAAS,OAAOE,aAAAA;AAC1B,gBAAMH,eAAe,OAAOI,iBAAAA;AAE1BA,yBAAaC,UAAU;cACrBC,MAAM,CAACC,UAAAA;AAEL,oBAAIA,MAAMtI,SAASuI,kBAAkBC,oBAAoB;AAEvDpD,iCAAe5E,KAAK8H,MAAMjF,OAAO;AAGjC,sBAAI;AACF,0BAAMoF,kBAAmC;sBACvCnE,UAAUA,YAAY;sBACtBC;sBACAwB,OAAOrB,2DAAqBqB;sBAC5B0B,QAAQa,MAAMjF;sBACdsE,SAASjE,KAAKyB,IAAG,IAAKD;sBACtBc,WAAWtC,KAAKyB,IAAG;sBACnBc,UAAU,KAAKC,eAAe/B,cAAAA;sBAC9BuE,oBAAoB;oBACtB;AAGAC,4BAAQC,QAAO,EACZzB,KAAK,MAAA;AACJ,2BAAK9H,cAAc8G,MAAM0B,eAAeY,eAAAA;oBAC1C,CAAA,EACCnB,MAAM,CAACjB,UAAAA;AACNlG,8BAAQkG,MAAM,iCAAiCA,KAAAA;oBACjD,CAAA;kBACJ,SAASA,OAAP;AACAlG,4BAAQkG,MAAM,yCAAyCA,KAAAA;kBACzD;gBACF;cACF;YACF,CAAA;AAGA,kBAAM6B,SAASC,YAAAA;UACjB,CAAA;QACF;MACF;AAEA,aAAO;QACL7D,UAAU4C;QACV3C,OAAOyC,OAAOzC;QACdS;QACAO;QACAsD,2BAA2BlC,aAAarF,OACtC,CAACC;;UAEC,CAACgE,kBAAkBuD,KAAK,CAACC,qBAAqBA,iBAAiBnH,QAAQL,OAAOK,IAAI;SAAA;QAKtFgD,YAAYoC,OAAOpC;MACrB;IACF,SAASyB,OAAP;AAEA,YAAI,UAAKhH,kBAAL,mBAAoBwG,YAAWd,cAAc;AAC/C,YAAI;AACF,gBAAMiE,YAA0B;YAC9B1E;YACAC;YACAwB,OAAOrB,2DAAqBqB;YAC5BM,OAAOA,iBAAiB4C,QAAQ5C,QAAQ6C,OAAO7C,KAAAA;YAC/CL,WAAWtC,KAAKyB,IAAG;YACnBwC,SAASjE,KAAKyB,IAAG,IAAKD;YACtBe,UAAU,KAAKC,eAAe/B,cAAAA;UAChC;AAEA,gBAAM,KAAK9E,cAAc8G,MAAMgD,YAAYH,SAAAA;QAC7C,SAASlB,UAAP;AACA3H,kBAAQkG,MAAM,4BAA4ByB,QAAAA;QAC5C;MACF;AAEA,UAAIzB,iBAAiB+C,kBAAiB;AACpC,cAAM/C;MACR;AACAlG,cAAQkG,MAAM,2BAA2BA,KAAAA;AACzCrB,kBAAYqE,uBAAsB;AAClC,YAAMhD;IACR;EACF;EAEA,MAAMiD,4BAA4B7E,gBAA8D;AAC9F,UAAM1F,SAAuC,KAAKC,0BAA0BqD,OAC1E,OAAOC,KAAuBiH,aAAAA;AAC5B,YAAMxK,UAAS,MAAMuD;AACrB,UAAIiH,SAASvJ,SAASC,aAAaC,mBAAmB;AACpD,cAAMsJ,kBAAkB/E,eAAerC,WAAWqH,gBAC9C;UAAEA,eAAe,UAAUhF,eAAerC,WAAWqH;QAAgB,IACrE;AAEJ,cAAMC,SAAS,IAAIC,iBAAgB;UACjCC,QAAQL,SAASM;UACjBC,QAAQP,SAASQ;UACjBC,gBAAgB;YAAE,GAAGR;UAAgB;QACvC,CAAA;AACA,YAAIS,OAA+E,CAAA;AACnF,YAAI;AACFA,iBAAO,MAAMP,OAAOQ,WAAWC,OAAM;AAErC,cAAIF,QAAQ,YAAYA,QAASA,KAAKG,OAAkBC,YAAW,MAAO,aAAa;AACrF,kBAAM,IAAIC,8BAA8B;cAAEhL,iBAAiB,KAAKA;YAAgB,CAAA;UAClF;QACF,SAASS,GAAP;AACA,gBAAM,IAAIiB,uBAAsB;YAC9BC,SAAS;iEAC0CsI,SAASM;;;;UAI9D,CAAA;QACF;AACA,cAAMU,iBAAiBN,KAAKxH,IAAI,CAAC+H,WAAW;UAC1C5I,MAAM4I,MAAMC;UACZlH,IAAIiH,MAAME;UACVzI,aAAa;UACbsH;QACF,EAAA;AACA,eAAO;aAAIxK;aAAWwL;;MACxB;AAQA,YAAMI,cAAcpB;AACpB,YAAMqB,WAAW,GAAGrB,SAAS5E;AAC7B,UAAI;AACF,cAAMkG,WAAW,MAAMC,MAAMF,UAAU;UACrCG,QAAQ;UACRC,SAASC,cAAcN,YAAYzL,iBAAiBuF,cAAAA;UACpDyG,MAAM1E,KAAKC,UAAU;YAAErE,YAAYqC,eAAerC;UAAW,CAAA;QAC/D,CAAA;AACA,YAAI,CAACyI,SAASM,IAAI;AAChB,cAAIN,SAASO,WAAW,KAAK;AAC3B,kBAAM,IAAIC,6BAA4B;cAAE1G,KAAKiG;YAAS,CAAA;UACxD;AACA,gBAAM,IAAIU,yBAAwB;YAChCF,QAAQP,SAASO;YACjBzG,KAAKiG;YACLW,kBAAkB;UACpB,CAAA;QACF;AAEA,cAAMtB,OAAqB,MAAMY,SAASW,KAAI;AAC9C,cAAMjB,mBAAkBN,6BAAMlL,WAAU,CAAA,GAAI0D,IAAI,CAACgJ,WAAW;UAC1D7J,MAAM6J,MAAM7J;UACZK,aAAawJ,MAAMxJ,eAAe,MAAM;UACxCsB,IAAIC,UAAAA;UACJ+F;QACF,EAAA;AACA,eAAO;aAAIxK;aAAWwL;;MACxB,SAASlE,OAAP;AACA,YAAIA,iBAAiB+C,kBAAiB;AACpC,gBAAM/C;QACR;AACA,cAAM,IAAIqF,yBAAwB;UAAErF;UAAuB1B,KAAKiG;QAAS,CAAA;MAC3E;IACF,GACAjC,QAAQC,QAAQ,CAAA,CAAE,CAAA;AAEpB,SAAKtJ,mBAAoB,MAAMP,UAAW,CAAA,GAAI0D,IAAI,CAACkJ,OAAO;MAAE/J,MAAM+J,EAAE/J;MAAM2B,IAAIoI,EAAEpI;IAAG,EAAA;AAEnF,WAAOxE;EACT;EAEA,MAAM6M,eACJnH,gBACAH,UACAuH,WACiC;AACjC,UAAMC,sBAAsB,MAAM,KAAKxC,4BAA4B7E,cAAAA;AAEnE,UAAMsH,oBAAoBD,oBAAoBhD,KAAK,CAAC2C,UAAUA,MAAM7J,SAASiK,SAAAA;AAC7E,QAAI,CAACE,mBAAmB;AACtB,YAAM,IAAI9C,MAAM,iBAAA;IAClB;AAEA,QAAI8C,kBAAkBxC,SAASvJ,SAASC,aAAaC,mBAAmB;AACtE,YAAMsJ,kBAAkB/E,eAAerC,WAAWqH,gBAC9C;QAAEA,eAAe,UAAUhF,eAAerC,WAAWqH;MAAgB,IACrE;AAEJ,YAAMC,SAAS,IAAIC,iBAAgB;QACjCC,QAAQmC,kBAAkBxC,SAASM;QACnCC,QAAQiC,kBAAkBxC,SAASQ;QACnCC,gBAAgB;UAAE,GAAGR;QAAgB;MACvC,CAAA;AACA,UAAIwC,QAAa,CAAC;AAClB,UAAI;AACFA,iBAAS,MAAMtC,OAAOuC,QAAQC,SAAS5H,QAAAA,GAAWtC;MACpD,SAASqE,OAAP;MAAe;AAEjB,UAAI8F,OAAOC,KAAKJ,KAAAA,EAAOjL,WAAW,GAAG;AACnC,eAAO;UACLuD,UAAUA,YAAY;UACtB+H,cAAc;UACdL,OAAOxF,KAAKC,UAAU,CAAC,CAAA;UACvBtF,UAAUqF,KAAKC,UAAU,CAAA,CAAE;QAC7B;MACF,OAAO;AACL,cAAM,EAAEtF,UAAU,GAAGmL,qBAAAA,IAAyBN;AAC9C,cAAMO,qBAAqBC,8BAA8BrL,QAAAA;AACzD,eAAO;UACLmD,UAAUA,YAAY;UACtB+H,cAAc;UACdL,OAAOxF,KAAKC,UAAU6F,oBAAAA;UACtBnL,UAAUqF,KAAKC,UAAU8F,kBAAAA;QAC3B;MACF;IACF,WACER,kBAAkBxC,SAASvJ,SAASC,aAAawM,cACjD,EAAE,UAAUV,kBAAkBxC,WAC9B;AACA,YAAMoB,cAAcoB,kBAAkBxC;AACtC,YAAMqB,WAAW,GAAGD,YAAYhG;AAChC,UAAI;AACF,cAAMkG,WAAW,MAAMC,MAAMF,UAAU;UACrCG,QAAQ;UACRC,SAASC,cAAcN,YAAYzL,iBAAiBuF,cAAAA;UACpDyG,MAAM1E,KAAKC,UAAU;YACnBrE,YAAYqC,eAAerC;YAC3BkC;YACA1C,MAAMiK;UACR,CAAA;QACF,CAAA;AACA,YAAI,CAAChB,SAASM,IAAI;AAChB,cAAIN,SAASO,WAAW,KAAK;AAC3B,kBAAM,IAAIC,6BAA4B;cAAE1G,KAAKiG;YAAS,CAAA;UACxD;AACA,gBAAM,IAAIU,yBAAwB;YAChCF,QAAQP,SAASO;YACjBzG,KAAKiG;YACLW,kBAAkB;UACpB,CAAA;QACF;AAEA,cAAMtB,OAA+B,MAAMY,SAASW,KAAI;AAExD,eAAO;UACL,GAAGvB;UACH+B,OAAOxF,KAAKC,UAAUwD,KAAK+B,KAAK;UAChC7K,UAAUqF,KAAKC,UAAUwD,KAAK9I,QAAQ;QACxC;MACF,SAASkF,OAAP;AACA,YAAIA,iBAAiB+C,kBAAiB;AACpC,gBAAM/C;QACR;AACA,cAAM,IAAIqF,yBAAwB;UAAErF;UAAO1B,KAAKiG;QAAS,CAAA;MAC3D;IACF,OAAO;AACL,YAAM,IAAI3B,MAAM,0BAA2B8C,kBAAkBxC,SAAiBvJ,MAAM;IACtF;EACF;EAEA,MAAcqF,oBACZnB,SACiC;AA50BrC;AA60BI,UAAM,EACJ/C,UAAUiD,aACVI,uBACAC,gBACAI,cACAP,UAAUoI,qBACVC,YACA5H,cACAL,oBAAmB,IACjBR;AACJ,UAAM,EAAE2H,WAAWe,SAAQ,IAAK/H;AAGhC,UAAMK,mBAAmBxB,KAAKyB,IAAG;AAEjC,UAAMC,iBAAwB,CAAA;AAG9B,UAAMd,WAAWoI,uBAAuB7H,aAAaP;AAErD,UAAMiB,oBAAoB,MAAM,KAAKC,qBAAqBtB,OAAAA;AAE1D,UAAM/C,WAAWyE,0BAA0BxB,WAAAA;AAE3C,UAAMyI,eAAetH,kBAAkBuD,KACrC,CAACvH,WAAWA,OAAOK,SAASiK,aAAaiB,oBAAoBvL,MAAAA,CAAAA;AAG/D,QAAI,CAACsL,cAAc;AACjB,YAAM,IAAIvC,8BAA8B;QAAEuB;QAAWvM,iBAAiB,KAAKA;MAAgB,CAAA;IAC7F;AAKA,UAAMyN,kCAAiDxH,kBACpDjE,OACC,CAACC;;MAEC,CAACuL,oBAAoBvL,MAAAA;MAEpBuL,oBAAoBvL,MAAAA,KAAWA,OAAOK,SAASiK;KAAAA,EAEnDpJ,IAAI,CAAClB,YAAY;MAChBK,MAAML,OAAOK;MACbK,aAAaV,OAAOU;MACpBsE,YAAYC,KAAKC,UAAUC,6BAA6BnF,OAAOY,UAAU,CAAA;IAC3E,EAAA;AAEF,UAAM6K,sBAAsBpG,6BAA6B;SACpDmG;SACA7I,QAAQpF;KACZ;AAGD,UAAI,UAAKO,kBAAL,mBAAoBwG,YAAWd,cAAc;AAC/C,UAAI;AACF,cAAMe,cAA8B;UAClCxB;UACAC,OAAOT;UACPiC,OAAOrB,2DAAqBqB;UAC5B5E;UACArC,SAASkO;UACTtI;UACAsB,WAAWd;UACXe,UAAU;UACV4F;UACAe;QACF;AAEA,cAAM,KAAKvN,cAAc8G,MAAMC,cAAcN,WAAAA;MAC/C,SAASO,OAAP;AACAlG,gBAAQkG,MAAM,gCAAgCA,KAAAA;MAChD;IACF;AAEA,YAAM,UAAKnH,oBAAL,8BAAuB;MAC3BoF;MACAC,OAAOT;MACP6B,eAAexE;MACfiB,YAAYqC,eAAerC;IAC7B;AAEA,QAAI;AACF,YAAM4C,cAAc,IAAIC,mBAAAA;AACxB,YAAM+C,SAAS,MAAM6E,aAAaI,mBAAmB;QACnDrL,MAAMiK;QACNvH;QACAsI;QACAD;QACA9D,2BAA2BmE;MAC7B,CAAA;AAGA,YAAI,UAAK3N,kBAAL,mBAAoBwG,YAAW,KAAKxG,cAAcqI,eAAe3C,cAAc;AAEjF,cAAMgD,iBAAiB/C,YAAYgD,OAAOC,KAAKjD,WAAAA;AAE/CA,oBAAYgD,SAAS,OAAOE,aAAAA;AAC1B,gBAAMH,eAAe,OAAOI,iBAAAA;AAE1BA,yBAAaC,UAAU;cACrBC,MAAM,CAACC,UAAAA;AAEL,oBAAIA,MAAMtI,SAASuI,kBAAkBC,oBAAoB;AAEvDpD,iCAAe5E,KAAK8H,MAAMjF,OAAO;AAGjC,sBAAI;AACF,0BAAMoF,kBAAmC;sBACvCnE,UAAUA,YAAY;sBACtBC,OAAOT;sBACPiC,OAAOrB,2DAAqBqB;sBAC5B0B,QAAQa,MAAMjF;sBACdsE,SAASjE,KAAKyB,IAAG,IAAKD;sBACtBc,WAAWtC,KAAKyB,IAAG;sBACnBc,UAAU;sBACVyC,oBAAoB;sBACpBmD;sBACAe;oBACF;AAGAjE,4BAAQC,QAAO,EACZzB,KAAK,MAAA;AACJ,2BAAK9H,cAAc8G,MAAM0B,eAAeY,eAAAA;oBAC1C,CAAA,EACCnB,MAAM,CAACjB,UAAAA;AACNlG,8BAAQkG,MAAM,uCAAuCA,KAAAA;oBACvD,CAAA;kBACJ,SAASA,OAAP;AACAlG,4BAAQkG,MAAM,+CAA+CA,KAAAA;kBAC/D;gBACF;cACF;YACF,CAAA;AAGA,kBAAM6B,SAASC,YAAAA;UACjB,CAAA;QACF;MACF;AAEAnD,kBAAYgD,OAAO,OAAOG,iBAAAA;AACxBpG,aAAKiG,MAAAA,EAAQI,UAAU;UACrBC,MAAM,CAACC,UAAUH,aAAaE,KAAKC,KAAAA;UACnCjC,OAAO,CAAC6G,QAAAA;AAh+BlB,gBAAA7F;AAi+BYlH,oBAAQkG,MAAM,mBAAmB6G,GAAAA;AAGjC,kBAAI7F,MAAA,KAAKhI,kBAAL,gBAAAgI,IAAoBxB,YAAWd,cAAc;AAC/C,kBAAI;AACF,sBAAMiE,YAA0B;kBAC9B1E;kBACAC,OAAOT;kBACPiC,OAAOrB,2DAAqBqB;kBAC5BM,OAAO6G,eAAejE,QAAQiE,MAAMhE,OAAOgE,GAAAA;kBAC3ClH,WAAWtC,KAAKyB,IAAG;kBACnBwC,SAASjE,KAAKyB,IAAG,IAAKD;kBACtBe,UAAU;kBACV4F;kBACAe;gBACF;AAEA,qBAAKvN,cAAc8G,MAAMgD,YAAYH,SAAAA;cACvC,SAASlB,UAAP;AACA3H,wBAAQkG,MAAM,8BAA8ByB,QAAAA;cAC9C;YACF;AAEAK,yBAAa9B,MAAM6G,GAAAA;AACnB/E,yBAAagF,SAAQ;UACvB;UACAA,UAAU,MAAMhF,aAAagF,SAAQ;QACvC,CAAA;MACF,CAAA;AAGA,YAAI,UAAK9N,kBAAL,mBAAoBwG,YAAWd,cAAc;AAC/CP,8BACG2C,KAAK,CAACC,mBAAAA;AACL,gBAAMI,eAAgC;YACpClD;YACAC,OAAOT;YACPiC,OAAOrB,2DAAqBqB;;YAE5B0B,QAAQ,KAAKpI,cAAcqI,cAActC,iBAAiBgC;YAC1DO,SAASjE,KAAKyB,IAAG,IAAKD;YACtBc,WAAWtC,KAAKyB,IAAG;YACnBc,UAAU;YACV2B,iBAAiB;YACjBiE;YACAe;UACF;AAEA,cAAI;AACF,iBAAKvN,cAAc8G,MAAM0B,eAAeL,YAAAA;UAC1C,SAASM,UAAP;AACA3H,oBAAQkG,MAAM,iCAAiCyB,QAAAA;UACjD;QACF,CAAA,EACCR,MAAM,CAACjB,UAAAA;AACNlG,kBAAQkG,MAAM,oDAAoDA,KAAAA;QACpE,CAAA;MACJ;AAEA7B,4BACG2C,KAAK,CAACC,mBAAAA;AA7hCf,YAAAC;AA8hCU,SAAAA,MAAA,KAAKlI,mBAAL,gBAAAkI,IAAA,WAAsB;UACpB/C;UACAC,OAAOT;UACP6B,eAAexE;UACfiG;UACAhF,YAAYqC,eAAerC;QAC7B;MACF,CAAA,EACCkF,MAAM,CAACC,WAAAA;MAAY,CAAA;AAEtB,aAAO;QACLjD;QACAC,OAAOT;QACPkB;QACAO;QACAsD,2BAA2BmE;MAC7B;IACF,SAAS3G,OAAP;AAEA,YAAI,UAAKhH,kBAAL,mBAAoBwG,YAAWd,cAAc;AAC/C,YAAI;AACF,gBAAMiE,YAA0B;YAC9B1E;YACAC,OAAOT;YACPiC,OAAOrB,2DAAqBqB;YAC5BM,OAAOA,iBAAiB4C,QAAQ5C,QAAQ6C,OAAO7C,KAAAA;YAC/CL,WAAWtC,KAAKyB,IAAG;YACnBwC,SAASjE,KAAKyB,IAAG,IAAKD;YACtBe,UAAU;YACV4F;YACAe;UACF;AAEA,gBAAM,KAAKvN,cAAc8G,MAAMgD,YAAYH,SAAAA;QAC7C,SAASlB,UAAP;AACA3H,kBAAQkG,MAAM,8BAA8ByB,QAAAA;QAC9C;MACF;AAEA3H,cAAQkG,MAAM,2BAA2BA,KAAAA;AACzC,YAAMA;IACR;EACF;EAEA,MAAcb,qBAAqBtB,SAAwD;AA1kC7F;AA2kCI,UAAM,EAAEO,gBAAgBtD,UAAUiD,aAAaU,aAAaH,IAAG,IAAKT;AAGpE,UAAMyB,gBAAgBC,0BAA0BxB,WAAAA;AAChD,UAAMgJ,qBAAoC,CAAA;AAC1C,eAAWC,gBAAgB,KAAKpO,WAAW;AACzC,UAAI;AACF,cAAMoB,QAAQ,MAAMgN;AACpBD,2BAAmB5M,KAAKH,KAAAA;MAC1B,SAASgG,OAAP;AACAlG,gBAAQkG,MAAM,kCAAkCA,KAAAA;MAClD;IACF;AAEA,UAAMrH,4BAA4B,KAAKA,0BAA0ByD,IAC/D,CAAC8G,cAAc;MAAE,GAAGA;MAAUvJ,MAAMsN,oBAAoB/D,QAAAA;IAAU,EAAA;AAGpE,UAAM7I,gBAAgB,MAAM6M,mBAAmB;MAC7CvO;MACAyF;MACAtD,UAAUwE;MACVb;MACA0I,aAAa7I;MACb5F,QAAQ,KAAKA;IACf,CAAA;AAEA,UAAM0O,oBACJ,OAAO,KAAK3O,YAAY,aACpB,KAAKA,QAAQ;MAAEsD,YAAYqC,eAAerC;MAAYuC;IAAI,CAAA,IAC1D,KAAK7F;AAIX,UAAM4O,4BAA2C,CAAA;AACjD,QAAI,KAAKhO,qBAAqB;AAE5B,YAAMiO,gBAAgB,KAAKpO,oBAAoB,CAAA;AAE/C,YAAMqO,qBAAoBnJ,oBAAerC,eAAfqC,mBAA2B5D,iBACnD4D,oBAAerC,eAAfqC,mBAA2BoJ,iBAC3B,CAAA;AAGF,YAAMC,wBAAwB,oBAAIrO,IAAAA;AAGlC;WAAIkO;QAAejM,QAAQ,CAACqM,OAAAA;AAC1B,YAAIA,MAAMA,GAAGxE,UAAU;AACrBuE,gCAAsBnM,IAAIoM,GAAGxE,UAAUwE,EAAAA;QACzC;MACF,CAAA;AAGA;WAAIH;QAAkBlM,QAAQ,CAACqM,OAAAA;AAC7B,YAAIA,MAAMA,GAAGxE,UAAU;AACrBuE,gCAAsBnM,IAAIoM,GAAGxE,UAAUwE,EAAAA;QACzC;MACF,CAAA;AAEA,YAAMC,qBAAqBlM,MAAMC,KAAK+L,sBAAsB9L,OAAM,CAAA;AAGlE,iBAAWiM,UAAUD,oBAAoB;AACvC,cAAME,cAAcD,OAAO1E;AAC3B,YAAI4E,qBAAgD,KAAK3O,eAAe4O,IAAIF,WAAAA;AAE5E,YAAI,CAACC,oBAAoB;AAEvB,cAAIzE,SAA2B;AAC/B,cAAI;AACFA,qBAAS,MAAM,KAAKhK,oBAAoBuO,MAAAA;AACxC,kBAAMI,QAAQ,MAAM3E,OAAO2E,MAAK;AAChCF,iCAAqBG,yBAAyBD,OAAOH,WAAAA;AACrD,iBAAK1O,eAAemC,IAAIuM,aAAaC,kBAAAA;UACvC,SAAS9H,OAAP;AACAlG,oBAAQkG,MACN,4CAA4C6H,iCAC5C7H,KAAAA;AAEF8H,iCAAqB,CAAA;AACrB,iBAAK3O,eAAemC,IAAIuM,aAAaC,kBAAAA;UACvC;QACF;AACAT,kCAA0BlN,KAAI,GAAK2N,sBAAsB,CAAA,CAAE;MAC7D;IACF;AAIA,WAAO;SACFV;SACAL;SACA1M;SACAgN;;EAEP;;EAGQxH,eAAe/B,gBAA2D;AAChF,UAAMoK,cAAcpK,eAAexE,YAAYiC;AAC/C,QAAI2M,YAAYC,SAAS,QAAA;AAAW,aAAO;AAC3C,QAAID,YAAYC,SAAS,WAAA;AAAc,aAAO;AAC9C,QAAID,YAAYC,SAAS,QAAA;AAAW,aAAO;AAC3C,QAAID,YAAYC,SAAS,MAAA;AAAS,aAAO;AACzC,QAAID,YAAYC,SAAS,WAAA;AAAc,aAAO;AAC9C,WAAO1K;EACT;AACF;AA76BajF;AA+6BN,SAAS+H,6BAA6B6H,iBAA8B;AACzE,MAAIC,WAA0B,CAAA;AAC9B,QAAMC,eAAyB,CAAA;AAC/B,aAAWC,QAAQH,iBAAiB;AAClC,QAAI,CAACE,aAAaH,SAASI,KAAKhN,IAAI,GAAG;AACrC8M,eAASlO,KAAKoO,IAAAA;AACdD,mBAAanO,KAAKoO,KAAKhN,IAAI;IAC7B;EACF;AACA,SAAO8M;AACT;AAVgB9H;AAaT,SAASiI,mBAAmBZ,QAAwC;AACzE,SAAO;IACL,GAAGA;IACHjO,MAAMC,aAAawM;EACrB;AACF;AALgBoC;AAOT,SAASC,0BACdb,QAA+C;AAE/C,SAAO;IACL,GAAGA;IACHjO,MAAMC,aAAaC;EACrB;AACF;AAPgB4O;AAST,SAASxB,oBAAoB/D,UAA4B;AAC9D,MAAI,CAACA,SAASvJ,MAAM;AAClB,QAAI,mBAAmBuJ,YAAY,YAAYA,UAAU;AACvD,aAAOtJ,aAAaC;IACtB,OAAO;AACL,aAAOD,aAAawM;IACtB;EACF;AAEA,SAAOlD,SAASvJ;AAClB;AAVgBsN;;;ARjtChB,IAAMyB,cAAcC;AAEpB,IAAMC,kBAAkB,IAAIC,gBAAgB;EAC1CC,aAAaJ,YAAYK;EACzBC,gBAAgBN,YAAYO;AAC9B,CAAA;AAEO,SAASC,gCACdC,SAAuB;AAEvB,QAAMC,gBAAgBD,QAAQE,0BAA0BC,OACtD,CAACC,KAAKC,aAAAA;AACJ,QAAIC,OAAO;MAAE,GAAGF;IAAI;AAEpB,UAAMG,eAAeC,oBAAoBH,QAAAA;AACzC,QAAI,CAACC,KAAKG,cAAcC,SAASH,YAAAA,GAAe;AAC9CD,aAAO;QACL,GAAGA;QACHG,eAAe;aAAIH,KAAKG;UAAeF;;MACzC;IACF;AAEA,QAAIA,iBAAiBI,aAAaC,mBAAmB;AAEnD,YAAMC,KAAKR;AACXC,aAAO;QACL,GAAGA;QACHQ,cAAcD,GAAGE,OAAOC;QACxBC,WAAWJ,GAAGK,kBACVC,YAAW,QAAA,EAAUC,OAAOP,GAAGK,eAAe,EAAEG,OAAO,KAAA,IACvD;MACN;IACF;AAEA,WAAOf;EACT,GACA;IAAEG,eAAe,CAAA;IAAIK,cAAc;IAAMG,WAAW;EAAK,CAAA;AAG3D,SAAO;IACLK,eAAetB,QAAQuB,QAAQP;IAC/BQ,iBAAiBxB,QAAQE,0BAA0Bc;IACnDP,eAAeR,cAAcQ;IAC7BK,cAAcb,cAAca;IAC5BW,cAAcxB,cAAcgB;EAC9B;AACF;AAvCgBlB;AAyChB,IAAA,2BAAeN;;;ADlCf,SAASiC,mBAAAA,wBAAuB;;UAEpBC,oBAAAA;;;;;;;;;;GAAAA,sBAAAA,oBAAAA,CAAAA,EAAAA;;UAYAC,uBAAAA;;;;GAAAA,yBAAAA,uBAAAA,CAAAA,EAAAA;AAqEL,IAAMC,sBAAN,cAAkCC,eAAAA;EACvCC,cAAc;AACZ,UAAK;EACP;EAEAC,qBAAqB,EACnBC,WACAC,gBAAe,GAId;AACD,SAAKC,KAAK;MAAEC,MAAI;MAAsCH;MAAWC;IAAgB,CAAA;EACnF;EAEAG,uBAAuB,EAAEJ,WAAWK,QAAO,GAA4C;AACrF,SAAKH,KAAK;MAAEC,MAAI;MAAwCE;MAASL;IAAU,CAAA;EAC7E;EAEAM,mBAAmB,EAAEN,UAAS,GAA2B;AACvD,SAAKE,KAAK;MAAEC,MAAI;MAAoCH;IAAU,CAAA;EAChE;EAEAO,gBAAgBP,WAAmBK,SAAiB;AAClD,SAAKN,qBAAqB;MAAEC;IAAU,CAAA;AACtC,SAAKI,uBAAuB;MAAEJ;MAAWK;IAAQ,CAAA;AACjD,SAAKC,mBAAmB;MAAEN;IAAU,CAAA;EACtC;EAEAQ,yBAAyB,EACvBC,mBACAC,YACAT,gBAAe,GAKd;AACD,SAAKC,KAAK;MACRC,MAAI;MACJM;MACAC;MACAT;IACF,CAAA;EACF;EAEAU,wBAAwB,EACtBF,mBACAG,KAAI,GAIH;AACD,SAAKV,KAAK;MAAEC,MAAI;MAAyCS;MAAMH;IAAkB,CAAA;EACnF;EAEAI,uBAAuB,EAAEJ,kBAAiB,GAAmC;AAC3E,SAAKP,KAAK;MAAEC,MAAI;MAAwCM;IAAkB,CAAA;EAC5E;EAEAK,oBAAoB,EAClBL,mBACAC,YACAE,MACAX,gBAAe,GAMd;AACD,SAAKO,yBAAyB;MAAEC;MAAmBC;MAAYT;IAAgB,CAAA;AAC/E,SAAKU,wBAAwB;MAAEF;MAAmBG;IAAK,CAAA;AACvD,SAAKC,uBAAuB;MAAEJ;IAAkB,CAAA;EAClD;EAEAM,0BAA0B,EACxBN,mBACAC,YACAM,QACAC,MAAK,GAMJ;AACD,SAAKf,KAAK;MACRC,MAAI;MACJO;MACAD;MACAO,QAAQE,cAAcC,aAAaH,QAAQC,KAAAA;IAC7C,CAAA;EACF;EAEAG,sBAAsB,EACpBC,UACAC,WACAC,UACAC,OACAC,QACAC,MACAC,OACAC,QAAO,GAUN;AACD,SAAK1B,KAAK;MACRC,MAAI;MACJkB;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACF,CAAA;EACF;AACF;AA9HahC;AAgIN,IAAMiC,qBAAN,MAAMA;EACHC,eAAe,IAAIlC,oBAAAA;EACnBmC;EAER,MAAMC,OAAOD,UAA8C;AACzD,SAAKA,WAAWA;EAClB;EAEAE,uBAAuBC,UAAU,wCAAwC;AACvE,UAAMC,eAAe,UAAKD;AAC1B,QAAI,CAAC,KAAKH,UAAU;AAClB,WAAKC,OAAO,OAAOF,iBAAAA;AACjBA,qBAAavB,gBAAgB6B,UAAAA,GAAYD,YAAAA;MAC3C,CAAA;IACF,OAAO;AACL,WAAKL,aAAavB,gBAAgB6B,UAAAA,GAAYD,YAAAA;IAChD;EACF;EAEAE,qBAAqB,EACnBC,mBACAC,mBACAC,2BACAnB,SAAQ,GAMP;AACD,SAAKU,SAAS,KAAKD,YAAY,EAAEW,MAAM,CAACxB,UAAAA;AACtCyB,cAAQzB,MAAM,kCAAkCA,KAAAA;AAChD,WAAKgB,uBAAsB;AAC3B,WAAKH,aAAaa,SAAQ;IAC5B,CAAA;AACA,WAAO,KAAKb,aAAac;;MAEvBC,MACE,CAACC,KAAKC,UAAAA;AAIJD,cAAM;UAAE,GAAGA;QAAI;AAEf,YAAIC,MAAM5C,SAAI,wBAA6C;AACzD2C,cAAIE,uBACFV,kBAAkBW,KAAK,CAACC,WAAWA,OAAOC,SAASJ,MAAMrC,UAAU,MAAM0C;AAC3EN,cAAIlC,OAAO;AACXkC,cAAIrC,oBAAoBsC,MAAMtC;AAC9B,cAAIqC,IAAIE,sBAAsB;AAC5BF,gBAAII,SAASZ,kBAAkBW,KAAK,CAACC,WAAWA,OAAOC,SAASJ,MAAMrC,UAAU;UAClF;AACAoC,cAAIO,iCAAiCN,MAAM9C;QAC7C,WAAW8C,MAAM5C,SAAI,uBAA4C;AAC/D2C,cAAIlC,QAAQmC,MAAMnC;QACpB;AAEAkC,YAAIC,QAAQA;AAEZ,eAAOD;MACT,GACA;QACEC,OAAO;QACPC,sBAAsB;QACtBpC,MAAM;QACNH,mBAAmB;QACnByC,QAAQ;QACRG,gCAAgC;MAClC,CAAA;MAEFC,UAAU,CAACC,mBAAAA;AACT,YACEA,eAAeR,MAAO5C,SAAI,wBAC1BoD,eAAeP,sBACf;AACA,gBAAMQ,uBAAuB,IAAI5D,oBAAAA;AACjC6D,wBACED,sBACAjB,oBAAoBA,oBAAoB,MACxCgB,eAAeL,QACfK,eAAe3C,MACf2C,eAAeF,gCACfE,eAAe9C,mBACf+B,2BACAnB,QAAAA,EACAoB,MAAM,CAACxB,UAAAA;AACPyB,oBAAQzB,MAAMA,KAAAA;UAChB,CAAA;AAEAyC,mCAAUC,QAAQ,sCAAsC,CAAC,CAAA;AACzD,iBAAOC,OAAOC,GAAGN,eAAeR,KAAK,GAAIS,oBAAAA,EAAsBZ,KAC7DkB,YAAW,CAAC7C,UAAAA;AACVyB,oBAAQzB,MAAM,6BAA6BA,KAAAA;AAC3C,iBAAKgB,uBAAsB;AAC3B,mBAAO8B;UACT,CAAA,CAAA;QAEJ,OAAO;AACL,iBAAOF,GAAGN,eAAeR,KAAK;QAChC;MACF,CAAA;IAAA;EAEJ;AACF;AAvGalB;AAyGb,eAAe4B,cACb3B,cACAS,mBACAW,QACAc,iBACAX,gCACA5C,mBACA+B,2BACAnB,UAAgB;AAvVlB;AAyVE,MAAIkB,mBAAmB;AACrB,UAAM,EAAE0B,OAAM,IAAK,MAAMC,eAAe3B,iBAAAA;AAExC,QAAI0B,WAAW,UAAU;AACvBnC,mBAAaa,SAAQ;AACrB;IACF;EACF;AAGA,MAAI/B,OAA8B,CAAA;AAClC,MAAIoD,iBAAiB;AACnB,QAAI;AACFpD,aAAOuD,KAAKC,MAAMJ,eAAAA;IACpB,SAASK,GAAP;AACA3B,cAAQzB,MAAM,8BAA8B;QAAE+C;MAAgB,CAAA;AAC9DlC,mBAAaf,0BAA0B;QACrCN;QACAC,YAAYwC,OAAOC;QACnBlC,OAAO;UACLqD,MAAM;UACNpC,SAAS;QACX;MACF,CAAA;AACA;IACF;EACF;AAGA,MAAIqC,oBAAoBrB,MAAAA,GAAS;AAC/B,UAAMlC,SAAS,GAAGkC,OAAOC;AAEzB,UAAMqB,iBAAiBC,iBAAgBC,wBAAwB;MAC7DC,IAAIlE;MACJmE,WAAW,oBAAIC,KAAAA;MACf1B,MAAMD,OAAOC;MACb2B,WAAWX,KAAKC,MAAMJ,eAAAA;MACtB/D,iBAAiBoD,kCAAkC5C;IACrD,CAAA;AAEA,UAAMsE,uBAAuBN,iBAAgBvD,eAAe;MAC1DyD,IAAI,YAAYlE;MAChBmE,WAAW,oBAAIC,KAAAA;MACfpE;MACAC,YAAYwC,OAAOC;MACnBnC;IACF,CAAA;AAEAc,iBAAaf,0BAA0B;MACrCN;MACAC,YAAYwC,OAAOC;MACnBnC;IACF,CAAA;AAEA,UAAMgB,SAAS,MAAMkB,OAAO8B,mBAAmB;MAC7C7B,MAAMD,OAAOC;MACb9B;MACAmB;MACAyC,oBAAoB;QAACT;QAAgBO;;IACvC,CAAA;AAGAG,IAAAA,MAAKlD,MAAAA,EAAQmD,UAAU;MACrBjF,MAAM,CAAC6C,UAAUjB,aAAa5B,KAAK6C,KAAAA;MACnC9B,OAAO,CAACmE,QAAAA;AACN1C,gBAAQzB,MAAM,mBAAmBmE,GAAAA;AACjCtD,qBAAaf,0BAA0B;UACrCN;UACAC,YAAYwC,OAAOC;UACnBlC,OAAO;YACLqD,MAAM;YACNpC,SAASkD,IAAIlD;UACf;QACF,CAAA;AACAJ,qBAAaa,SAAQ;MACvB;MACAA,UAAU,MAAMb,aAAaa,SAAQ;IACvC,CAAA;EACF,OAAO;AAEL,QAAI;AACF,YAAM3B,SAAS,QAAMkC,YAAOmC,YAAPnC,gCAAiBtC;AACtC,YAAM0E,wBAAwB;QAC5BtE;QACAc;QACAyD,iBAAiB;UACfpC,MAAMD,OAAOC;UACbwB,IAAIlE;QACN;MACF,CAAA;IACF,SAAS4D,GAAP;AACA3B,cAAQzB,MAAM,2BAA2BoD,CAAAA;AACzCvC,mBAAaf,0BAA0B;QACrCN;QACAC,YAAYwC,OAAOC;QACnBlC,OAAO;UACLqD,MAAM;UACNpC,SAASmC,EAAEnC;QACb;MACF,CAAA;AACAJ,mBAAaa,SAAQ;IACvB;EACF;AACF;AAjHec;;;AjB5Sf,SAAS+B,yBAAyB;AAClC,SAASC,mBAAAA,wBAAuB;AAEhC,SAASC,oBAAoB;AAe7B,SAASC,YAAAA,iBAAgB;;;A6BrDzB,SAASC,SAAAA,SAAOC,cAAAA,mBAAkB;;;;;;;;;;;;;;;;;AAG3B,IAAMC,QAAN,MAAMA;EAEXC;EAGAC;EAGAC;AACF;AATaH;;EACVI,QAAM,MAAMC,MAAAA;;GADFL,MAAAA,WAAAA,MAAAA,MAAAA;;EAIVI,QAAM,MAAMC,MAAAA;;GAJFL,MAAAA,WAAAA,QAAAA,MAAAA;;EAOVI,QAAM,MAAMC,MAAAA;;GAPFL,MAAAA,WAAAA,eAAAA,MAAAA;AAAAA,QAAAA,eAAAA;EADZM,YAAAA;GACYN,KAAAA;AAYN,IAAMO,iBAAN,MAAMA;EAEXC;AACF;AAHaD;;EACVH,QAAM,MAAM;IAACJ;GAAM;;GADTO,eAAAA,WAAAA,UAAAA,MAAAA;AAAAA,iBAAAA,eAAAA;EADZD,YAAAA;GACYC,cAAAA;;;;;;;;;;;;;;;;;;;;;;;;;A7ByCb,IAAME,mBAAmB,8BAAO,EAC9BC,SACAC,0BACAC,MACAC,UACAC,QAAO,MAOR;;AACC,MACEF,KAAKG,SAASC,YACdJ,UAAKG,SAASH,KAAKG,SAASC,SAAS,CAAA,EAAGC,gBAAxCL,mBAAqDM,UAASC,YAAYC,MAC1E;AACA,UAAML,WAAWH,KAAKG,SACnBM,OACC,CAACC,MACCA,EAAEL,gBAAgBM,WACjBD,EAAEL,YAAYC,SAASC,YAAYC,QAAQE,EAAEL,YAAYC,SAASC,YAAYK,UAAQ,EAE1FC,IAAI,CAACH,OAAO;MACXJ,MAAMI,EAAEL,YAAaC;MACrBQ,SAASJ,EAAEL,YAAYS;IACzB,EAAA;AAEF,UAAMC,cAAcZ,SAASA,SAASC,SAAS,CAAA;AAC/C,UAAMY,iBAAiBb,SAASc,MAAM,GAAG,EAAC;AAE1C,UAAMC,OAAO;MACXC,OAAOJ,YAAYD;MACnBM,aAAapB,KAAKqB,MAAMC,WAAWC,qBAAqBC;MACxDC,eAAezB,KAAKqB,MAAMC,WAAWC,qBAAqBG;MAC1DvB,UAAUa;IACZ;AAEA,UAAMW,mBAAmB,MAAMC,MAAM,GAAG9B,+BAA+B;MACrE+B,QAAQ;MACRC,SAAS;QACP,gBAAgB;QAChB,iCAAiC/B;MACnC;MACAmB,MAAMa,KAAKC,UAAUd,IAAAA;IACvB,CAAA;AAEA,QAAIS,iBAAiBM,IAAI;AACvB,YAAMC,aAA+B,MAAMP,iBAAiBQ,KAAI;AAChElC,eAASiC,UAAAA;IACX,OAAO;AACLhC,cAAQ,MAAMyB,iBAAiBQ,KAAI,CAAA;IACrC;EACF;AACF,GAtDyB;AAyDlB,IAAMC,kBAAN,MAAMA;EACX,MACMC,QAAQ;AACZ,WAAO;EACT;EAEA,MACMC,gBAAuBC,KAAqB;AAChD,QAAIC,UAASD,IAAIC,OAAOC,MAAM;MAAEC,WAAW;IAAkC,CAAA;AAE7EF,IAAAA,QAAOG,MAAM,YAAA;AACb,UAAMC,sBAAsB,MAAML,IAAIM,YAAYC,QAAQC,4BAA4BR,GAAAA;AAEtFC,IAAAA,QAAOG,MAAM,yCAAA;AAEb,WAAO;MACLK,QAAQJ,oBAAoB/B,IAC1B,CAAC,EAAEoC,UAAU,GAAGC,qBAAAA,MAA2BA,oBAAAA;IAE/C;EACF;EAEA,MACMC,wBACGZ,KACMvC,MAEboD,YACA;;AACAC,6BAAUC,QAAQ,uCAAuC;MACvD,8BAA4BtD,UAAKqB,UAALrB,mBAAYsB,gBAAeX;MACvD4C,aAAavD,KAAKwD,SAASD;IAC7B,CAAA;AAEA,QAAIf,UAASD,IAAIC,OAAOC,MAAM;MAAEC,WAAW;IAA0C,CAAA;AACrFF,IAAAA,QAAOG,MAAM;MAAE3C;IAAK,GAAG,6BAAA;AAEvB,QAAIoD,YAAY;AACdZ,MAAAA,QAAOG,MAAM,sDAAA;AACbJ,UAAIa,aAAa;QAAE,GAAGb,IAAIa;QAAY,GAAGA;MAAW;IACtD;AAEA,UAAMK,iBAAiBlB,IAAIM,YAAYC;AACvC,UAAMY,iBAAiBnB,IAAIM,YAAYa;AAEvC,QAAI3D,2BAA0C;AAC9C,QAAI4D;AAEJ,QAAI3D,KAAKqB,OAAO;AACdmB,MAAAA,UAASA,QAAOC,MAAM;QAAEpB,OAAO;MAAK,CAAA;AACpCmB,MAAAA,QAAOG,MAAM,sEAAA;AACb,YAAMiB,MAAMrB,IAAIsB,QAAQ/B,QAAQgC,IAAI,+BAAA;AACpC,UAAIF,KAAK;AACPpB,QAAAA,QAAOG,MAAM,iCAAA;AACb5C,mCAA2B6D;MAC7B,OAAO;AACLpB,QAAAA,QAAOuB,MAAM,qCAAA;AACb,cAAM,IAAIC,aAAa,kDAAA;MACzB;AAEA,UAAIC,QAAQC,IAAIC,wBAAwB;AACtCR,8BAAsBM,QAAQC,IAAIC;MACpC,YAAW5B,SAAIM,YAAYxB,UAAhBkB,mBAAuBzC,SAAS;AACzC6D,+BAAsBpB,SAAIM,YAAYxB,UAAhBkB,mBAAuBzC;MAC/C,OAAO;AACL6D,8BAAsB;MACxB;AAEAnB,MAAAA,UAASA,QAAOC,MAAM;QAAEkB;MAAoB,CAAA;IAC9C;AAEAnB,IAAAA,QAAOG,MAAM,qBAAA;AACb,UAAMyB,kBAAkB,IAAIC,eAAAA;AAC5B,UAAMC,sBAAsB,IAAID,eAAAA;AAChC,UAAME,oBAAoB,IAAIF,eAAAA;AAE9B,QAAIG,iBAA4B,CAAA;AAChC,QAAIC;AACJ,QAAIC;AAEJ,UAAMC,wBAAwB,IAAIC,QAAmB,CAACC,SAASC,WAAAA;AAC7DL,qCAA+BI;AAC/BH,oCAA8BI;IAChC,CAAA;AAEA,QAAI/E,0BAA0B;AAC5BwC,UAAIa,WAAW,0BAAA,IAA8BrD;IAC/C;AAEAyC,IAAAA,QAAOG,MAAM,YAAA;AACb,UAAM,EACJoC,aACAC,WAAWC,UAAAA,GACXC,OACAC,mBACAC,2BACAC,WAAU,IACR,MAAM5B,eAAe6B,sBAAsB;MAC7C5B;MACAvD,UAAUH,KAAKG;MACfoF,SAASvF,KAAKwF,SAASD,QAAQ9E,OAC7B,CAACgF,WAAWA,OAAOC,cAAcC,wBAAwBC,QAAQ;MAEnEZ,UAAUhF,KAAKgF;MACfE,OAAOlF,KAAKkF;MACZW,cAAc9F;MACd4E;MACAmB,gBAAgBvD;MAChBwD,qBAAqB/F,KAAK+F;MAC1BC,cAAchG,KAAKgG;MACnBC,aAAajG,KAAKiG;MAClBC,KAAKlG,KAAKwF,SAASU;MACnBb,YAAYrF,KAAKqF;MACjBc,YAAYnG,KAAKmG;IACnB,CAAA;AAEA3D,IAAAA,QAAOG,MAAM,yCAAA;AAEb,UAAMyD,cAAcrB,YACjBsB,qBAAqB;MACpBlB;MACAZ,qBAAmBvE,UAAKqB,UAALrB,mBAAYsB,cAAaiD,oBAAoB;MAChEa,2BAA2BA,0BAA0B3E;;QAEnD,CAACgF,WACC,CAACN,kBAAkBmB,KAAK,CAACC,qBAAqBA,iBAAiBC,QAAQf,OAAOe,IAAI;MAAA;MAEtFxB;IACF,CAAA,EACCyB;;;MAGCC,YAAAA;MACAC,SAAS,MAAA;AACPnE,QAAAA,QAAOG,MAAM,wBAAA;MACf,CAAA;IAAA;AAGJ,UAAMiE,WAAW;MACf5B;MACAE;MACA2B,QAAQC,gBAAe1C,eAAAA;MACvBiB;MACAc,YAAY,IAAIY,SAAS,OAAOC,MAAMC,SAAAA;AACpC,YAAIC;AAEJA,kCAA0Bd,YAAYe,UAAU;UAC9CC,MAAM,OAAOC,UAAAA;AACX,gBAAIA,MAAMC,QAAQC,kBAAkBC,WAAW;AAC7C;YACF;AACA,oBAAQH,MAAMb,MAAI;cAChB,KAAKiB,qBAAqBC;AACxBV,qBACEW,iBAAgBD,yBAAyB;kBACvCJ,MAAMD,MAAMC;kBACZd,MAAMa,MAAMb;kBACZoB,OAAOP,MAAMO;gBACf,CAAA,CAAA;AAEF;cACF,KAAKH,qBAAqBI;AACxBb,qBACEW,iBAAgBE,mCAAmC;kBACjDP,MAAMD,MAAMC;kBACZd,MAAMa,MAAMb;kBACZxG,MAAM;oBACJ4H,OAAOP,MAAMrH,KAAK4H;oBAClBzH,UAAUkH,MAAMrH,KAAKG,SAASU,IAAI,CAACiH,YAAAA;AACjC,0BACEA,QAAQR,SAAS,iBAChB,aAAaQ,WAAW,UAAUA,SACnC;AACA,+BAAOH,iBAAgBI,aAAa;0BAClCC,IAAIF,QAAQE;0BACZC,WAAW,oBAAIC,KAAAA;0BACfpH,SAAS;4BAAEgH,QAAwBhH;;0BACnCR,MAAOwH,QAAwBxH;0BAC/BuG,QAAQ,IAAIsB,qBAAAA;wBACd,CAAA;sBACF;AACA,0BAAI,eAAeL,SAAS;AAC1B,+BAAOH,iBAAgBS,wBAAwB;0BAC7C5B,MAAMsB,QAAQtB;0BACdwB,IAAIF,QAAQE;0BACZK,WAAW;4BAACtG,KAAKC,UAAU8F,QAAQO,SAAS;;0BAC5CJ,WAAW,oBAAIC,KAAAA;0BACfrB,QAAQ,IAAIsB,qBAAAA;wBACd,CAAA;sBACF;AACA,4BAAM,IAAIG,MAAM,gDAAA;oBAClB,CAAA;kBACF;gBACF,CAAA,CAAA;AAEF;YACJ;UACF;UACAvE,OAAO,CAACwE,QAAAA;AACN/F,YAAAA,QAAOuB,MAAM;cAAEwE;YAAI,GAAG,6BAAA;AACtBnE,4BAAgBgD,KACd,IAAIoB,qBAAqB;cACvBC,aAAa;YACf,CAAA,CAAA;AAEFvB,+EAAyBwB;AACzBzB,iBAAAA;UACF;UACA0B,UAAU,YAAA;AACRnG,YAAAA,QAAOG,MAAM,8BAAA;AACbyB,4BAAgBgD,KAAK,IAAIwB,sBAAAA,CAAAA;AACzB1B,+EAAyBwB;AACzBzB,iBAAAA;UACF;QACF,CAAA;MACF,CAAA;MACA9G,UAAU,IAAI4G,SAAS,OAAO8B,aAAaC,0BAAAA;;AACzCtG,QAAAA,QAAOG,MAAM,2BAAA;AAEb,aAAI3C,MAAAA,KAAKqB,UAALrB,gBAAAA,IAAYsB,YAAY;AAC1BkB,UAAAA,UAASA,QAAOC,MAAM;YAAEnB,YAAY;UAAK,CAAA;AACzCkB,UAAAA,QAAOG,MAAM,yCAAA;AAEb9C,2BAAiB;YACfC,SAAS6D;YACT5D;YACAC;YACAC,UAAU,CAAC8I,WAAAA;AACTvG,cAAAA,QAAOG,MAAM;gBAAEkE,QAAQkC,OAAOlC;cAAO,GAAG,4BAAA;AACxCtC,gCAAkB6C,KAAK2B,MAAAA;AAGvB,kBAAIA,OAAOlC,WAAW,UAAU;AAE9BzC,gCAAgBgD,KACd,IAAI4B,oCAAoC;kBAAEC,kBAAkBF,OAAOG;gBAAO,CAAA,CAAA;AAE5E5E,oCAAoB8C,KAAK;kBACvB8B,QAAQ,6DAA6DH,OAAOG;gBAC9E,CAAA;AAGA1E,iCAAiB;kBACfmD,iBAAgBI,aAAa;oBAC3BC,IAAI/C,UAAAA;oBACJgD,WAAW,oBAAIC,KAAAA;oBACfpH,SAASiI,OAAOG;oBAChB5I,MAAMC,YAAYK;kBACpB,CAAA;;AAEF6D,6CAA6BD,cAAAA;cAC/B;YACF;YACAtE,SAAS,CAACqI,QAAAA;AACR/F,cAAAA,QAAOuB,MAAM;gBAAEwE;cAAI,GAAG,gCAAA;AACtBnE,8BAAgBgD,KACd,IAAIoB,qBAAqB;gBACvBC,aAAa;cACf,CAAA,CAAA;AAEFnE,kCAAoB8C,KAAK;gBACvB8B,QAAQ;cACV,CAAA;AAGAxE,0CAA4B6D,GAAAA;YAC9B;UACF,CAAA;QACF;AAEA,YAAIrB;AAEJ1E,QAAAA,QAAOG,MAAM,mDAAA;AAEbuE,kCAA0Bd,YAAYe,UAAU;UAC9CC,MAAM,OAAOC,UAAAA;AACX,oBAAQA,MAAMC,MAAI;cAChB,KAAKC,kBAAkBC;AACrB;cAIF,KAAKD,kBAAkB4B;AAErB,sBAAMC,2BAA2BhD,YAAYK;;kBAE3C4C,UAAU,CAACC,MAAMA,MAAMjC,KAAAA;;kBAEvBkC,UACE,CAACD,MACC,EACEA,EAAEhC,SAASC,kBAAkBiC,kBAC7BF,EAAEG,aAAapC,MAAMoC,UAAQ;;kBAInChJ,OACE,CAAC6I,MACCA,EAAEhC,QAAQC,kBAAkBmC,sBAC5BJ,EAAEG,aAAapC,MAAMoC,SAAS;gBAAA;AAKpC,sBAAME,sBAAsB,IAAIC,QAAAA;AAEhC,sBAAMH,YAAYpC,MAAMoC;AAExBZ,4BAAY;kBACVb,IAAIyB;kBACJI,iBAAiBxC,MAAMwC;kBACvBhD,QAAQC,gBAAe6C,mBAAAA;kBACvB1B,WAAW,oBAAIC,KAAAA;kBACf5H,MAAMC,YAAYK;kBAClBE,SAAS,IAAIiG,SAAS,OAAO+C,eAAeC,sBAAAA;AAC1CvH,oBAAAA,QAAOG,MAAM,uCAAA;AAEb,0BAAMqH,aAAuB,CAAA;AAC7B,wBAAIC;AAEJ3F,wCACGmC,KACCC,YAAAA,GACAwD,KAAK,CAAA,GACLC,IAAI,CAAC,EAAEjB,QAAQO,WAAAA,WAAS,MAAE;AACxBjH,sBAAAA,QAAOG,MAAM;wBAAEuG;wBAAQO,WAAAA;sBAAU,GAAG,4BAAA;AAEpCE,0CAAoBvC,KAClBO,iBAAgByC,qBAAqB;wBAAElB;sBAAO,CAAA,CAAA;AAGhD9E,sCAAgBgD,KAAK,IAAIiD,iCAAiC;wBAAEZ,WAAAA;sBAAU,CAAA,CAAA;AACtEM,wCAAAA;AACAE,2EAAkBvB;oBACpB,CAAA,CAAA,EAEDvB,UAAS;AAEZ3E,oBAAAA,QAAOG,MAAM,4CAAA;AAEbsH,uCAAmBb,yBAAyBjC,UAAU;sBACpDC,MAAM,OAAOkC,MAAAA;AACX,4BAAIA,EAAEhC,QAAQC,kBAAkBmC,oBAAoB;AAClD,gCAAMI,cAAcR,EAAExI,OAAO;AAC7BkJ,qCAAWhD,KAAKsC,EAAExI,OAAO;wBAC3B;sBACF;sBACAiD,OAAO,CAACwE,QAAAA;AACN/F,wBAAAA,QAAOuB,MAAM;0BAAEwE;wBAAI,GAAG,sCAAA;AACtBjE,4CAAoB8C,KAAK;0BACvB8B,QAAQ;0BACRO;wBACF,CAAA;AACAM,0CAAAA;AACAE,6EAAkBvB;sBACpB;sBACAC,UAAU,MAAA;AACRnG,wBAAAA,QAAOG,MAAM,uCAAA;AACbgH,4CAAoBvC,KAAK,IAAIe,qBAAAA,CAAAA;AAC7B4B,0CAAAA;AACAE,6EAAkBvB;AAElBlE,uCAAewC,KACbW,iBAAgBI,aAAa;0BAC3BC,IAAIyB;0BACJxB,WAAW,oBAAIC,KAAAA;0BACfpH,SAASkJ,WAAWM,KAAK,EAAA;0BACzBhK,MAAMC,YAAYK;wBACpB,CAAA,CAAA;sBAEJ;oBACF,CAAA;kBACF,CAAA;gBACF,CAAA;AACA;cAIF,KAAK2G,kBAAkBgD;AACrB/H,gBAAAA,QAAOG,MAAM,uCAAA;AACb,sBAAM6H,gCAAgCpE,YAAYK;kBAChD4C,UAAU,CAACC,MAAMA,MAAMjC,KAAAA;;kBAEvBkC,UACE,CAACD,MACC,EACEA,EAAEhC,SAASC,kBAAkBkD,sBAC7BnB,EAAEoB,qBAAqBrD,MAAMqD,kBAAgB;;kBAInDjK,OACE,CAAC6I,MACCA,EAAEhC,QAAQC,kBAAkBoD,uBAC5BrB,EAAEoB,qBAAqBrD,MAAMqD,iBAAiB;gBAAA;AAGpD,sBAAME,2BAA2B,IAAIhB,QAAAA;AACrCf,4BAAY;kBACVb,IAAIX,MAAMqD;kBACVb,iBAAiBxC,MAAMwC;kBACvBhD,QAAQC,gBAAe8D,wBAAAA;kBACvB3C,WAAW,oBAAIC,KAAAA;kBACf1B,MAAMa,MAAMwD;kBACZxC,WAAW,IAAItB,SAAS,OAAO+D,oBAAoBC,2BAAAA;AACjDvI,oBAAAA,QAAOG,MAAM,0CAAA;AAEb,0BAAMqI,iBAA2B,CAAA;AACjC,wBAAIC;AAEJA,0DAAsCT,8BAA8BrD,UAAU;sBAC5EC,MAAM,OAAOkC,MAAAA;AACX,4BAAIA,EAAEhC,QAAQC,kBAAkBoD,qBAAqB;AACnD,gCAAMG,mBAAmBxB,EAAE4B,IAAI;AAC/BF,yCAAehE,KAAKsC,EAAE4B,IAAI;wBAC5B;sBACF;sBACAnH,OAAO,CAACwE,QAAAA;AACN/F,wBAAAA,QAAOuB,MAAM;0BAAEwE;wBAAI,GAAG,2CAAA;AACtBqC,iDAAyBxD,KACvBO,iBAAgByC,qBAAqB;0BACnClB,QACE;wBACJ,CAAA,CAAA;AAEF6B,+CAAAA;AACAE,mHAAqCvC;sBACvC;sBACAC,UAAU,MAAA;AACRnG,wBAAAA,QAAOG,MAAM,4CAAA;AACbiI,iDAAyBxD,KAAK,IAAIe,qBAAAA,CAAAA;AAClC4C,+CAAAA;AACAE,mHAAqCvC;AAErClE,uCAAewC,KACbW,iBAAgBS,wBAAwB;0BACtCJ,IAAIX,MAAMqD;0BACVzC,WAAW,oBAAIC,KAAAA;0BACf1B,MAAMa,MAAMwD;0BACZxC,WAAW2C,eAAeV,KAAK,EAAA;wBACjC,CAAA,CAAA;sBAEJ;oBACF,CAAA;kBACF,CAAA;gBACF,CAAA;AACA;cAIF,KAAK/C,kBAAkB4D;AACrB3I,gBAAAA,QAAOG,MAAM;kBAAEoG,QAAQ1B,MAAM0B;gBAAO,GAAG,wCAAA;AACvCF,4BAAY;kBACVb,IAAI,YAAYX,MAAMqD;kBACtB7D,QAAQ,IAAIsB,qBAAAA;kBACZF,WAAW,oBAAIC,KAAAA;kBACfwC,mBAAmBrD,MAAMqD;kBACzBG,YAAYxD,MAAMwD;kBAClB9B,QAAQ1B,MAAM0B;gBAChB,CAAA;AAEAvE,+BAAewC,KACbW,iBAAgByD,eAAe;kBAC7BpD,IAAI,YAAYX,MAAMqD;kBACtBzC,WAAW,oBAAIC,KAAAA;kBACfwC,mBAAmBrD,MAAMqD;kBACzBG,YAAYxD,MAAMwD;kBAClB9B,QAAQ1B,MAAM0B;gBAChB,CAAA,CAAA;AAEF;cAIF,KAAKxB,kBAAkB8D;AACrB7I,gBAAAA,QAAOG,MAAM;kBAAE0E;gBAAM,GAAG,8BAAA;AACxBwB,4BAAY;kBACVb,IAAI/C,UAAAA;kBACJ4B,QAAQ,IAAIsB,qBAAAA;kBACZnD,UAAUqC,MAAMrC;kBAChBsG,WAAWjE,MAAMiE;kBACjBC,UAAUlE,MAAMkE;kBAChBrG,OAAOmC,MAAMnC;kBACbsG,QAAQnE,MAAMmE;kBACdC,OAAOpE,MAAMoE;kBACbC,SAASrE,MAAMqE;kBACfpL,MAAMC,YAAYK;kBAClBqH,WAAW,oBAAIC,KAAAA;gBACjB,CAAA;AACA1D,+BAAewC,KACbW,iBAAgB0D,mBAAmB;kBACjCrD,IAAI/C,UAAAA;kBACJD,UAAUqC,MAAMrC;kBAChBsG,WAAWjE,MAAMiE;kBACjBC,UAAUlE,MAAMkE;kBAChBrG,OAAOmC,MAAMnC;kBACbsG,QAAQnE,MAAMmE;kBACdC,OAAOpE,MAAMoE;kBACbC,SAASrE,MAAMqE;kBACfpL,MAAMC,YAAYK;kBAClBqH,WAAW,oBAAIC,KAAAA;gBACjB,CAAA,CAAA;AAEF;YACJ;UACF;UACAnE,OAAO,CAACwE,QAAAA;AACN/F,YAAAA,QAAOuB,MAAM;cAAEwE;YAAI,GAAG,uBAAA;AACtBnE,4BAAgBgD,KACd,IAAIoB,qBAAqB;cACvBC,aAAa;YACf,CAAA,CAAA;AAEFvB,+EAAyBwB;AACzBI,kCAAAA;AAEApE,wCAA4B6D,GAAAA;UAC9B;UACAI,UAAU,YAAA;;AACRnG,YAAAA,QAAOG,MAAM,wBAAA;AACb,iBAAI3C,MAAAA,KAAKqB,UAALrB,gBAAAA,IAAYsB,YAAY;AAC1BkB,cAAAA,QAAOG,MAAM,sDAAA;AACb,oBAAMmE,gBAAevC,iBAAAA;YACvB;AACAH,4BAAgBgD,KAAK,IAAIwB,sBAAAA,CAAAA;AACzB1B,+EAAyBwB;AACzBI,kCAAAA;AAEArE,yCAA6BD,cAAAA;UAC/B;QACF,CAAA;MACF,CAAA;IACF;AAEA,WAAOoC;EACT;AACF;AAxhBaxE;;EACVuJ,MAAM,MAAMC,MAAAA;;;;GADFxJ,gBAAAA,WAAAA,SAAAA,IAAAA;;EAMVuJ,MAAM,MAAME,cAAAA;EACUC,UAAAA,GAAAA,IAAAA,CAAAA;;;WAAW,mBAAA,cAAA,SAAA;;;GAPvB1J,gBAAAA,WAAAA,mBAAAA,IAAAA;;EAsBV2J,SAAS,MAAMC,eAAAA;EAEbF,UAAAA,GAAAA,IAAAA,CAAAA;EACAG,UAAAA,GAAAA,IAAI,MAAA,CAAA;EACJA,UAAAA,GAAAA,IAAI,cAAc,MAAMC,mBAAmB;IAAEC,UAAU;EAAK,CAAA,CAAA;;;WAFjD,mBAAA,cAAA,SAAA;WACO,iCAAA,cAAA,SAAA;WAEN,oCAAA,cAAA,SAAA;;;GA3BJ/J,gBAAAA,WAAAA,2BAAAA,IAAAA;AAAAA,kBAAAA,eAAAA;EADZgK,SAAS,MAAMJ,eAAAA;GACH5J,eAAAA;;;AD9Gb,SAASiK,sBAAsB;;;A+BH/B,OAAOC,sBAAsB;AAC7B,OAAOC,YAAY;AAMZ,SAASC,aAAaC,SAAkD;AAC7E,QAAM,EAAEC,OAAOC,UAAS,IAAKF,WAAW,CAAC;AACzC,QAAMG,SAASC,OAAO;IAAEC,UAAU;EAAK,CAAA;AAEvC,QAAMC,UAASC,iBACb;IACEN,OAAOO,QAAQC,IAAIC,aAAaT,SAAS;IACzCU,QAAQ;MACNC,OAAO;QAAC;QAAO;;MACfC,QAAQ;IACV;EACF,GACAV,MAAAA;AAGF,MAAID,WAAW;AACb,WAAOI,QAAOQ,MAAM;MAAEZ;IAAU,CAAA;EAClC,OAAO;AACL,WAAOI;EACT;AACF;AApBgBP;;;ACPhB,SAASgB,OAAAA,MAAKC,YAAAA,iBAAgB;AAC9B,SAASC,OAAAA,YAAW;AACpB,SAASC,SAAAA,cAAa;;;ACFtB,SAASC,SAAAA,SAAOC,cAAAA,mBAAkB;;;;;;;;;;;;;;;;;AAI3B,IAAMC,yBAAN,MAAMA;EAEXC;EAGAC;EAGAC;EAGAC;AACF;AAZaJ;;EACVK,QAAM,MAAMC,MAAAA;;GADFN,uBAAAA,WAAAA,YAAAA,MAAAA;;EAIVK,QAAM,MAAME,OAAAA;;GAJFP,uBAAAA,WAAAA,gBAAAA,MAAAA;;EAOVK,QAAM,MAAMC,MAAAA;;GAPFN,uBAAAA,WAAAA,SAAAA,MAAAA;;EAUVK,QAAM,MAAMC,MAAAA;;GAVFN,uBAAAA,WAAAA,YAAAA,MAAAA;AAAAA,yBAAAA,eAAAA;EADZQ,YAAAA;GACYR,sBAAAA;;;ACJb,SAASS,SAAAA,SAAOC,aAAAA,mBAAiB;;;;;;;;;;;;;;;;;AAG1B,IAAMC,sBAAN,MAAMA;EAEXC;EAGAC;AACF;AANaF;;EACVG,QAAM,MAAMC,MAAAA;;GADFJ,oBAAAA,WAAAA,YAAAA,MAAAA;;EAIVG,QAAM,MAAMC,MAAAA;;GAJFJ,oBAAAA,WAAAA,aAAAA,MAAAA;AAAAA,sBAAAA,eAAAA;EADZK,YAAAA;GACYL,mBAAAA;;;;;;;;;;;;;;;;;;;;;;;;;AFKN,IAAMM,gBAAN,MAAMA;EACX,MACMC,eAAsBC,KAAkCC,MAA2B;AACvF,UAAMC,SAAS,MAAMF,IAAIG,YAAYC,QAAQC,4BAA4BL,GAAAA;AACzE,UAAMM,QAAQJ,OAAOK,KAAK,CAACD,WAAUA,OAAME,SAASP,KAAKQ,SAAS;AAElE,QAAI,CAACH,OAAO;AACV,aAAO;QACLI,UAAUT,KAAKS,YAAY;QAC3BC,cAAc;QACdC,OAAOC,KAAKC,UAAU,CAAC,CAAA;QACvBC,UAAUF,KAAKC,UAAU,CAAA,CAAE;MAC7B;IACF;AAEA,UAAMF,QAAQ,MAAMZ,IAAIG,YAAYC,QAAQL,eAAeC,KAAKC,KAAKS,UAAUT,KAAKQ,SAAS;AAE7F,WAAOG;EACT;AACF;AAnBad;;EACVkB,OAAM,MAAMC,sBAAAA;EACSC,WAAAA,GAAAA,KAAAA,CAAAA;EAA4BC,WAAAA,GAAAA,KAAI,MAAA,CAAA;;;WAArB,mBAAA,cAAA,SAAA;WAAmC,wBAAA,cAAA,SAAA;;;GAFzDrB,cAAAA,WAAAA,kBAAAA,IAAAA;AAAAA,gBAAAA,eAAAA;EADZsB,UAAS,MAAMH,sBAAAA;GACHnB,aAAAA;;;AhCGb,IAAAuB,eAA6B;AAE7B,IAAMC,SAASC,aAAAA;AAER,IAAMC,wBAAwB;EACnCC,WAAW,EAAEC,SAAQ,GAAE;AAErBA,aAASC,QAAQC,IAAI,gCAA4CC,oBAAO;EAC1E;AACF;AAwBA,eAAsBC,cACpBC,gBACAC,mBACAC,eACAC,aAA8C,CAAC,GAAC;AAEhDZ,SAAOa,MAAM;IAAEH;EAAkB,GAAG,0BAAA;AACpC,QAAMI,MAAsB;IAC1B,GAAGL;IACHM,aAAa;MACX,GAAGL;IACL;IACAE,YAAY;MAAE,GAAGA;IAAW;IAC5BZ,QAAQW;EACV;AACA,SAAOG;AACT;AAhBsBN;AAkBf,SAASQ,YACdC,UAEI,CAAC,GAAC;AAENjB,SAAOa,MAAM,4BAAA;AACb,QAAMK,SAASC,gBAAgB;IAC7BC,WAAW;MAACC;MAAiBC;;IAC7BC,gBAAgBN,QAAQM;EAC1B,CAAA;AACAvB,SAAOa,MAAM,mCAAA;AACb,SAAOK;AACT;AAZgBF;AAqBT,SAASQ,gBAAgBP,SAA0C;AAlF1E;AAmFE,QAAMQ,WAAYC,QAAQC,IAAIC,aAA2BX,QAAQQ,YAAyB;AAC1F,QAAMzB,UAASC,aAAa;IAAE4B,OAAOJ;IAAUK,WAAW;EAAkB,CAAA;AAE5E,QAAMnB,gBAAgBV,aAAa;IAAE4B,OAAOJ;EAAS,CAAA;AAErD,MAAIR,QAAQc,OAAO;AACjBC,6BAAUC,sBAAsB;MAC9BC,cAAcjB,QAAQc,MAAMG;MAC5BC,SAASlB,QAAQc,MAAMI;IACzB,CAAA;EACF;AAEA,OAAIlB,aAAQL,eAARK,mBAAoBF,aAAa;AACnCiB,6BAAUI,oBAAoB;MAC5BrB,aAAa;QACX,GAAIE,QAAQL,WAAWG;MACzB;IACF,CAAA;EACF;AAEAiB,2BAAUI,oBAAoB;IAC5BC,SAAS;MACPC,gBAAgBrB,QAAQqB,eAAeC,YAAYC;IACrD;EACF,CAAA;AAEA,SAAO;IACLC,SAASxC,aAAa;MAAE6B,WAAW;MAAgBD,OAAOJ;IAAS,CAAA;IACnEP,QAAQF,YAAAA;IACR0B,SAAS;MAACC,eAAAA;MAAkBzC;;IAC5B0C,SAAS,CAAC9B,QACRN,cAAcM,KAAKG,SAASN,eAAeM,QAAQL,UAAU;EACjE;AACF;AAlCgBY;;;AD/ET,SAASqB,+BAA+BC,SAA0C;AAJzF;AAKE,QAAMC,eAAeC,gBAAgBF,OAAAA;AAErCG,2BAAUC,oBAAoB;IAC5BC,SAAS;MACPC,WAAW;IACb;EACF,CAAA;AAEA,OAAIN,aAAQO,eAARP,mBAAoBQ,aAAa;AACnCL,6BAAUC,oBAAoB;MAC5BI,aAAaR,QAAQO,WAAWC;IAClC,CAAA;EACF;AAEAL,2BAAUM,QACR,gCACAC,gCAAgCV,QAAQK,OAAO,CAAA;AAGjD,QAAMM,UAASV,aAAaW;AAC5BD,EAAAA,QAAOE,MAAM,6BAAA;AAEb,QAAMC,OAAOC,WAAW;IACtB,GAAGd;IACHe,iBAAiBhB,QAAQiB;EAC3B,CAAA;AAEA,SAAOH;AACT;AA7BgBf;", "names": ["createYoga", "buildSchemaSync", "Arg", "Ctx", "Mutation", "Query", "Resolver", "ReplaySubject", "Subject", "filter", "finalize", "firstValueFrom", "shareReplay", "<PERSON><PERSON><PERSON><PERSON>", "take", "<PERSON><PERSON><PERSON><PERSON>", "tap", "Field", "InputType", "Field", "InputType", "registerEnumType", "MessageRole", "CopilotRequestType", "ActionInputAvailability", "name", "description", "MessageInput", "BaseMessageInput", "textMessage", "actionExecutionMessage", "resultMessage", "agentStateMessage", "imageMessage", "Field", "TextMessageInput", "nullable", "ActionExecutionMessageInput", "ResultMessageInput", "AgentStateMessageInput", "ImageMessageInput", "InputType", "content", "parentMessageId", "role", "String", "MessageRole", "name", "arguments", "scope", "deprecationReason", "actionExecutionId", "actionName", "result", "threadId", "<PERSON><PERSON><PERSON>", "state", "running", "nodeName", "runId", "active", "Boolean", "format", "bytes", "Field", "InputType", "Field", "InputType", "ActionInput", "name", "description", "jsonSchema", "available", "Field", "String", "ActionInputAvailability", "nullable", "InputType", "FrontendInput", "toDeprecate_fullContext", "actions", "url", "Field", "String", "nullable", "ActionInput", "InputType", "Field", "InputType", "Field", "InputType", "GuardrailsRuleInput", "allowList", "denyList", "Field", "String", "nullable", "InputType", "GuardrailsInput", "inputValidationRules", "CloudInput", "guardrails", "Field", "GuardrailsInput", "nullable", "InputType", "Field", "InputType", "ForwardedParametersInput", "model", "maxTokens", "stop", "toolChoice", "toolChoiceFunctionName", "temperature", "Field", "String", "nullable", "Number", "InputType", "Field", "InputType", "AgentSessionInput", "<PERSON><PERSON><PERSON>", "threadId", "nodeName", "Field", "String", "nullable", "InputType", "Field", "InputType", "AgentStateInput", "<PERSON><PERSON><PERSON>", "state", "config", "Field", "String", "nullable", "InputType", "Field", "InputType", "ExtensionsInput", "openaiAssistantAPI", "Field", "OpenAIApiAssistantAPIInput", "nullable", "InputType", "runId", "threadId", "String", "Field", "InputType", "Field", "InterfaceType", "ObjectType", "registerEnumType", "Field", "InterfaceType", "ObjectType", "Field", "ObjectType", "createUnionType", "registerEnumType", "MessageStatusCode", "registerEnumType", "name", "BaseMessageStatus", "code", "Field", "ObjectType", "PendingMessageStatus", "SuccessMessageStatus", "FailedMessageStatus", "reason", "String", "MessageStatusUnion", "createUnionType", "types", "Field", "ObjectType", "ExtensionsResponse", "openaiAssistantAPI", "Field", "OpenAIApiAssistantAPIResponse", "nullable", "ObjectType", "runId", "threadId", "String", "BaseMessageOutput", "id", "createdAt", "status", "Field", "String", "Date", "MessageStatusUnion", "InterfaceType", "resolveType", "value", "hasOwnProperty", "TextMessageOutput", "ActionExecutionMessageOutput", "ResultMessageOutput", "AgentStateMessageOutput", "ImageMessageOutput", "undefined", "role", "content", "parentMessageId", "MessageRole", "nullable", "ObjectType", "implements", "name", "scope", "arguments", "deprecationReason", "actionExecutionId", "actionName", "result", "threadId", "<PERSON><PERSON><PERSON>", "nodeName", "runId", "active", "state", "running", "Boolean", "format", "bytes", "CopilotResponse", "messages", "extensions", "metaEvents", "ResponseStatusUnion", "ExtensionsResponse", "BaseMetaEvent", "MetaEventName", "registerEnumType", "name", "description", "BaseMetaEvent", "type", "Field", "String", "InterfaceType", "resolveType", "value", "LangGraphInterruptEvent", "CopilotKitLangGraphInterruptEvent", "undefined", "CopilotKitLangGraphInterruptEventData", "messages", "BaseMessageOutput", "ObjectType", "response", "nullable", "implements", "data", "MetaEventInput", "name", "value", "response", "messages", "Field", "MetaEventName", "String", "nullable", "MessageInput", "InputType", "GenerateCopilotResponseMetadataInput", "requestType", "Field", "CopilotRequestType", "nullable", "InputType", "GenerateCopilotResponseInput", "metadata", "threadId", "runId", "messages", "frontend", "cloud", "forwardedParameters", "agentSession", "agentState", "agentStates", "extensions", "metaEvents", "String", "MessageInput", "FrontendInput", "CloudInput", "ForwardedParametersInput", "AgentSessionInput", "AgentStateInput", "ExtensionsInput", "MetaEventInput", "<PERSON><PERSON><PERSON>", "randomId", "of", "concat", "scan", "concatMap", "ReplaySubject", "firstValueFrom", "from", "catchError", "EMPTY", "TelemetryClient", "CopilotKitErrorCode", "createHash", "catchError", "mergeMap", "ReplaySubject", "scan", "LangGraphEventTypes", "MetaEventNames", "CustomEventNames", "randomId", "RemoteLangGraphEventSource", "eventStream$", "ReplaySubject", "shouldEmitToolCall", "shouldEmitToolCalls", "toolCallName", "Array", "isArray", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "content", "data", "chunk", "kwargs", "toolCallChunks", "getCurrentToolCallChunks", "args", "length", "text", "getCurrentMessageId", "id", "tool_call_chunks", "getResponseMetadata", "response_metadata", "processLangGraphEvents", "lastEventWithState", "pipe", "scan", "acc", "LangGraphEventTypes", "OnChatModelStream", "prevMessageId", "lastMessageId", "currentC<PERSON>nt", "responseMetadata", "toolCallCheck", "isToolCallEnd", "finish_reason", "isToolCallStart", "some", "name", "isMessageStart", "previousRoundHadToolCall", "isToolCall", "isMessageEnd", "lastToolCallName", "lastToolCallId", "find", "processedToolCallIds", "Set", "mergeMap", "events", "shouldEmitMessages", "metadata", "OnInterrupt", "push", "type", "RuntimeEventTypes", "MetaEvent", "RuntimeMetaEventName", "LangGraphInterruptEvent", "value", "OnCopilotKitInterrupt", "CopilotKitLangGraphInterruptEvent", "has", "add", "ActionExecutionEnd", "actionExecutionId", "TextMessageEnd", "messageId", "OnCustomEvent", "CustomEventNames", "CopilotKitManuallyEmitMessage", "TextMessageStart", "message_id", "TextMessageContent", "message", "CopilotKitManuallyEmitToolCall", "ActionExecutionStart", "actionName", "parentMessageId", "ActionExecutionArgs", "JSON", "stringify", "OnCopilotKitStateSync", "AgentStateMessage", "threadId", "thread_id", "role", "<PERSON><PERSON><PERSON>", "agent_name", "nodeName", "node_name", "runId", "run_id", "active", "state", "running", "clear", "catchError", "error", "console", "randomId", "Client", "LangGraphClient", "createHash", "isValidUUID", "randomUUID", "parse", "parsePartial<PERSON>son", "parseJson", "CopilotKitMisuseError", "RemoveMessage", "activeInterruptEvent", "execute", "args", "ReadableStream", "start", "controller", "streamEvents", "close", "err", "cause", "errorCode", "code", "CopilotKitMisuseError", "message", "deploymentUrl", "langsmithApiKey", "threadId", "argsInitialThreadId", "agent", "nodeName", "initialNodeName", "state", "initialState", "config", "explicitConfig", "messages", "actions", "logger", "properties", "metaEvents", "name", "assistantId", "initialAssistantId", "propertyHeaders", "authorization", "client", "LangGraphClient", "apiUrl", "<PERSON><PERSON><PERSON><PERSON>", "defaultHeaders", "randomUUID", "startsWith", "substring", "isValidUUID", "console", "warn", "wasInitiatedWithExistingThread", "threads", "get", "error", "create", "agentState", "values", "getState", "agentStateValues", "mode", "undefined", "formattedMessages", "copilotkitMessagesToLangChain", "e", "langGraphDefaultMergeState", "streamInput", "payload", "input", "streamMode", "command", "lgInterruptMetaEvent", "find", "ev", "MetaEventName", "LangGraphInterruptEvent", "resume", "response", "parseJson", "updateState", "asNode", "streamInfo", "hashedLgcKey", "createHash", "update", "digest", "assistants", "search", "retrievedAssistant", "a", "assistant_id", "telemetry", "capture", "length", "map", "join", "Error", "graphInfo", "getGraph", "graphSchema", "getSchemas", "schema<PERSON>eys", "getSchemaKeys", "filteredConfigurable", "configurable", "filterObjectBySchemaKeys", "newConfig", "isRecursionLimitSetToDefault", "recursion_limit", "configs<PERSON><PERSON><PERSON><PERSON>erent", "JSON", "stringify", "isOnlyRecursionLimitDifferent", "streamingStateExtractor", "StreamingStateExtractor", "prevNodeName", "emitIntermediateStateUntilEnd", "shouldExit", "externalRunId", "streamResponse", "runs", "stream", "emit", "enqueue", "TextEncoder", "encode", "latestStateValues", "updatedState", "manuallyEmittedState", "streamResponseChunk", "includes", "event", "data", "chunk", "interruptEvents", "__interrupt__", "interruptValue", "value", "evV<PERSON>ue", "__copilotkit_interrupt_value__", "LangGraphEventTypes", "OnCopilotKitInterrupt", "langchainMessagesToCopilotKit", "__copilotkit_messages__", "OnInterrupt", "chunkData", "currentNodeName", "metadata", "langgraph_node", "eventType", "runId", "run_id", "output", "model", "provider", "langgraph_host", "langGraphHost", "langgraph_version", "langGraphVersion", "OnCustomEvent", "CustomEventNames", "CopilotKitExit", "emitIntermediateState", "manuallyEmitIntermediateState", "CopilotKitManuallyEmitIntermediateState", "exitingNode", "OnChainEnd", "some", "node", "id", "getStateSyncEvent", "<PERSON><PERSON><PERSON>", "running", "active", "OnChatModelStart", "OnChatModelStream", "bufferToolCalls", "extractState", "interrupts", "tasks", "Object", "keys", "writes", "isEndNode", "next", "includeMessages", "Promise", "resolve", "reduce", "acc", "key", "OnCopilotKitStateSync", "thread_id", "agent_name", "node_name", "role", "toolCallBuffer", "currentToolCall", "previouslyParsableState", "constructor", "tool_call_chunks", "getEmitStateConfig", "currentToolName", "stateKey", "tool", "toolArgument", "entries", "argumentName", "parsedValue", "parsePartial<PERSON>son", "slice", "existingMessages", "existingMessageIds", "Set", "messageIds", "removedMessages", "filter", "m", "has", "RemoveMessage", "newMessages", "copilotkit", "result", "tool_call_names", "type", "tool_call", "tool_calls", "content", "Array", "text", "push", "arguments", "parentMessageId", "actionName", "tool_call_id", "actionExecutionId", "resultsDict", "msg", "reorderedResult", "msgId", "processedActionExecutions", "isTextMessage", "MessageRole", "user", "system", "assistant", "isImageMessage", "isActionExecutionMessage", "messageId", "add", "relatedActionExecutions", "isResultMessage", "CONSTANT_KEYS", "configSchema", "config_schema", "input_schema", "output_schema", "inputSchema", "outputSchema", "obj", "fromEntries", "CopilotKitError", "CopilotKitLowLevelError", "writeJsonLineResponseToEventStream", "response", "eventStream$", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "buffer", "flushBuffer", "current<PERSON><PERSON><PERSON>", "join", "trim", "length", "parts", "split", "lastPartIsComplete", "endsWith", "push", "pop", "map", "part", "filter", "for<PERSON>ach", "next", "JSON", "parse", "done", "value", "read", "decode", "stream", "error", "console", "complete", "CopilotKitApiDiscoveryError", "ResolvedCopilotKitError", "parseJson", "tryMap", "constructLGCRemoteAction", "endpoint", "graphqlContext", "logger", "messages", "agentStates", "agents", "map", "agent", "name", "description", "parameters", "handler", "_args", "remoteAgentHandler", "actionInputsWithoutAgents", "threadId", "nodeName", "additionalMessages", "metaEvents", "debug", "actionName", "telemetry", "capture", "agentExecution", "type", "agentsAmount", "length", "hashedLgcKey", "langsmithApiKey", "createHash", "update", "digest", "state", "config", "jsonState", "find", "<PERSON><PERSON><PERSON>", "parseJson", "response", "execute", "child", "component", "deploymentUrl", "properties", "actions", "tryMap", "action", "JSON", "parse", "jsonSchema", "eventSource", "RemoteLangGraphEventSource", "writeJsonLineResponseToEventStream", "eventStream$", "processLangGraphEvents", "error", "url", "status", "body", "message", "Error", "RemoteAgentType", "constructRemoteActions", "json", "onBeforeRequest", "totalAgents", "Array", "isArray", "args", "headers", "createHeaders", "fetchUrl", "fetch", "method", "stringify", "arguments", "ok", "text", "CopilotKitApiDiscoveryError", "ResolvedCopilotKitError", "isRemoteEndpoint", "requestResult", "result", "CopilotKitError", "CopilotKitLowLevelError", "RuntimeEventSubject", "additionalHeaders", "ctx", "Object", "assign", "CopilotKitLowLevelError", "ResolvedCopilotKitError", "CopilotKitError", "parseJson", "constructAgentWireRemoteAction", "logger", "messages", "agentStates", "agent", "action", "name", "agentId", "description", "parameters", "handler", "_args", "remoteAgentHandler", "actionInputsWithoutAgents", "threadId", "debug", "actionName", "agentWireMessages", "convertMessagesToAgentWire", "telemetry", "capture", "agentExecution", "type", "agentsAmount", "state", "jsonState", "find", "<PERSON><PERSON><PERSON>", "parseJson", "tools", "map", "input", "JSON", "parse", "jsonSchema", "legacy_to_be_removed_runAgentBridged", "result", "message", "isTextMessage", "push", "id", "role", "content", "isActionExecutionMessage", "toolCall", "function", "arguments", "stringify", "parentMessageId", "some", "m", "parentMessage", "toolCalls", "undefined", "isResultMessage", "toolCallId", "actionExecutionId", "EndpointType", "isRemoteAgentAction", "action", "remoteAgentHandler", "fetchRemoteInfo", "url", "onBeforeRequest", "graphqlContext", "logger", "frontendUrl", "debug", "headers", "createHeaders", "fetchUrl", "response", "fetch", "method", "body", "JSON", "stringify", "properties", "ok", "error", "status", "text", "ResolvedCopilotKitError", "isRemoteEndpoint", "json", "CopilotKitError", "CopilotKitLowLevelError", "setupRemoteActions", "remoteEndpointDefinitions", "messages", "agentStates", "agents", "child", "component", "filtered", "filter", "value", "index", "self", "type", "findIndex", "t", "result", "Promise", "all", "map", "endpoint", "constructLGCRemoteAction", "constructRemoteActions", "key", "agent", "Object", "entries", "agentId", "undefined", "message", "code", "CopilotKitErrorCode", "UNKNOWN", "push", "constructAgentWireRemoteAction", "flat", "createHash", "actionParametersToJsonSchema", "ResolvedCopilotKitError", "CopilotKitApiDiscoveryError", "randomId", "CopilotKitError", "CopilotKitLowLevelError", "CopilotKitAgentDiscoveryError", "CopilotKitMisuseError", "plainToInstance", "tryMap", "convertGqlInputToMessages", "inputMessages", "messages", "tryMap", "message", "textMessage", "plainToInstance", "TextMessage", "id", "createdAt", "role", "content", "parentMessageId", "imageMessage", "ImageMessage", "bytes", "format", "actionExecutionMessage", "ActionExecutionMessage", "name", "arguments", "JSON", "parse", "resultMessage", "ResultMessage", "actionExecutionId", "actionName", "result", "agentStateMessage", "AgentStateMessage", "threadId", "<PERSON><PERSON><PERSON>", "nodeName", "runId", "active", "state", "running", "filter", "m", "from", "Client", "LangGraphClient", "extractParametersFromSchema", "toolOrSchema", "parameters", "schema", "toolParameters", "jsonSchema", "properties", "requiredParams", "Set", "required", "paramName", "Object", "prototype", "hasOwnProperty", "call", "paramDef", "push", "name", "type", "description", "has", "convertMCPToolsToActions", "mcpTools", "mcpEndpoint", "actions", "toolName", "tool", "entries", "handler", "params", "result", "execute", "JSON", "stringify", "error", "console", "Error", "message", "String", "_isMCPTool", "_mcpEndpoint", "generateMcpToolInstructions", "toolsMap", "keys", "length", "toolEntries", "toolsDoc", "map", "paramsDoc", "paramsList", "propSchema", "propDetails", "requiredMark", "includes", "typeInfo", "join", "e", "CopilotRuntime", "actions", "agents", "remoteEndpointDefinitions", "langserve", "onBeforeRequest", "onAfterRequest", "delegateAgentProcessingToServiceAdapter", "observability", "availableAgents", "mcpServersConfig", "mcpActionCache", "Map", "createMCPClientImpl", "constructor", "params", "remoteEndpoints", "some", "e", "type", "EndpointType", "LangGraphPlatform", "console", "warn", "chain", "remoteChain", "Re<PERSON><PERSON><PERSON><PERSON>", "push", "toAction", "remoteActions", "middleware", "observability_c", "mcpServers", "createMCPClient", "length", "CopilotKitMisuseError", "message", "injectMCPToolInstructions", "messages", "currentActions", "mcpActionsForRequest", "filter", "action", "_isMCPTool", "uniqueMcpTools", "for<PERSON>ach", "set", "name", "toolsMap", "Array", "from", "values", "description", "schema", "parameters", "properties", "reduce", "acc", "p", "required", "map", "execute", "mcpToolInstructions", "generateMcpToolInstructions", "instructions", "systemMessageIndex", "findIndex", "msg", "textMessage", "role", "newMessages", "existingMsg", "content", "unshift", "id", "randomId", "createdAt", "Date", "MessageRole", "system", "actionExecutionMessage", "undefined", "resultMessage", "agentStateMessage", "processRuntimeRequest", "request", "serviceAdapter", "rawMessages", "clientSideActionsInput", "threadId", "runId", "outputMessagesPromise", "graphqlContext", "forwardedParameters", "url", "extensions", "agentSession", "agentStates", "publicApiKey", "eventSource", "RuntimeEventSource", "requestStartTime", "now", "streamedChunks", "processAgentRequest", "EmptyAdapter", "serverSideActions", "getServerSideActions", "filteredRawMessages", "messagesWithInjectedInstructions", "inputMessages", "convertGqlInputToMessages", "enabled", "requestData", "model", "timestamp", "provider", "detectProvider", "hooks", "handleRequest", "error", "serverSideActionsInput", "jsonSchema", "JSON", "stringify", "actionParametersToJsonSchema", "actionInputs", "flattenToolCallsNoDuplicates", "available", "ActionInputAvailability", "remote", "result", "process", "nonEmptyThreadId", "then", "outputMessages", "_a", "catch", "_error", "responseData", "output", "progressive", "latency", "isFinalResponse", "handleResponse", "logError", "originalStream", "stream", "bind", "callback", "eventStream$", "subscribe", "next", "event", "RuntimeEventTypes", "TextMessageContent", "progressiveData", "isProgressiveChunk", "Promise", "resolve", "actionInputsWithoutAgents", "find", "serverSideAction", "errorData", "Error", "String", "handleError", "CopilotKitError", "sendErrorMessageToChat", "discoverAgentsFromEndpoints", "endpoint", "propertyHeaders", "authorization", "client", "LangGraphClient", "apiUrl", "deploymentUrl", "<PERSON><PERSON><PERSON><PERSON>", "langsmithApiKey", "defaultHeaders", "data", "assistants", "search", "detail", "toLowerCase", "CopilotKitAgentDiscoveryError", "endpointAgents", "entry", "graph_id", "assistant_id", "cpkEndpoint", "fetchUrl", "response", "fetch", "method", "headers", "createHeaders", "body", "ok", "status", "CopilotKitApiDiscoveryError", "ResolvedCopilotKitError", "isRemoteEndpoint", "json", "agent", "CopilotKitLowLevelError", "a", "loadAgentState", "<PERSON><PERSON><PERSON>", "agentsWithEndpoints", "agentWithEndpoint", "state", "threads", "getState", "Object", "keys", "threadExists", "stateWithoutMessages", "copilotkitMessages", "langchainMessagesToCopilotKit", "CopilotKit", "threadIdFromRequest", "metaEvents", "nodeName", "currentAgent", "isRemoteAgentAction", "availableActionsForCurrentAgent", "allAvailableActions", "remoteAgentHandler", "err", "complete", "langserveFunctions", "chainPromise", "resolveEndpointType", "setupRemoteActions", "frontendUrl", "configuredActions", "requestSpecificMCPActions", "baseEndpoints", "requestEndpoints", "mcpEndpoints", "effectiveEndpointsMap", "ep", "effectiveEndpoints", "config", "endpointUrl", "actionsForEndpoint", "get", "tools", "convertMCPToolsToActions", "adapterName", "includes", "toolsByPriority", "allTools", "allToolNames", "tool", "copilotKitEndpoint", "langGraphPlatformEndpoint", "packageJson", "require", "telemetryClient", "TelemetryClient", "packageName", "name", "packageVersion", "version", "getRuntimeInstanceTelemetryInfo", "runtime", "endpointsInfo", "remoteEndpointDefinitions", "reduce", "acc", "endpoint", "info", "endpointType", "resolveEndpointType", "endpointTypes", "includes", "EndpointType", "LangGraphPlatform", "ep", "agentsAmount", "agents", "length", "hashed<PERSON><PERSON>", "langsmithApiKey", "createHash", "update", "digest", "actionsAmount", "actions", "endpointsAmount", "hashedLgcKey", "plainToInstance", "RuntimeEventTypes", "RuntimeMetaEventName", "RuntimeEventSubject", "ReplaySubject", "constructor", "sendTextMessageStart", "messageId", "parentMessageId", "next", "type", "sendTextMessageContent", "content", "sendTextMessageEnd", "sendTextMessage", "sendActionExecutionStart", "actionExecutionId", "actionName", "sendActionExecutionArgs", "args", "sendActionExecutionEnd", "sendActionExecution", "sendActionExecutionResult", "result", "error", "ResultMessage", "encodeResult", "sendAgentStateMessage", "threadId", "<PERSON><PERSON><PERSON>", "nodeName", "runId", "active", "role", "state", "running", "RuntimeEventSource", "eventStream$", "callback", "stream", "sendErrorMessageToChat", "message", "errorMessage", "randomId", "processRuntimeEvents", "serverSideActions", "guardrailsResult$", "actionInputsWithoutAgents", "catch", "console", "complete", "pipe", "scan", "acc", "event", "callActionServerSide", "find", "action", "name", "undefined", "actionExecutionParentMessageId", "concatMap", "eventWithState", "toolCallEventStream$", "executeAction", "telemetry", "capture", "concat", "of", "catchError", "EMPTY", "actionArguments", "status", "firstValueFrom", "JSON", "parse", "e", "code", "isRemoteAgentAction", "agentExecution", "plainToInstance", "ActionExecutionMessage", "id", "createdAt", "Date", "arguments", "agentExecutionResult", "remoteAgentHandler", "additionalMessages", "from", "subscribe", "err", "handler", "streamLangChainResponse", "actionExecution", "GraphQLJSONObject", "plainToInstance", "GraphQLError", "randomId", "Field", "ObjectType", "Agent", "id", "name", "description", "Field", "String", "ObjectType", "AgentsResponse", "agents", "invokeGuardrails", "baseUrl", "copilotCloudPublicApiKey", "data", "onResult", "onError", "messages", "length", "textMessage", "role", "MessageRole", "user", "filter", "m", "undefined", "assistant", "map", "content", "lastMessage", "restOfMessages", "slice", "body", "input", "validTopics", "cloud", "guardrails", "inputValidationRules", "allowList", "invalidTopics", "denyList", "guardrailsResult", "fetch", "method", "headers", "JSON", "stringify", "ok", "result<PERSON><PERSON>", "json", "CopilotResolver", "hello", "availableAgents", "ctx", "logger", "child", "component", "debug", "agentsWithEndpoints", "_copilotkit", "runtime", "discoverAgentsFromEndpoints", "agents", "endpoint", "agentWithoutEndpoint", "generateCopilotResponse", "properties", "telemetry", "capture", "requestType", "metadata", "copilotRuntime", "serviceAdapter", "copilotCloudBaseUrl", "key", "request", "get", "error", "GraphQLError", "process", "env", "COPILOT_CLOUD_BASE_URL", "responseStatus$", "ReplaySubject", "interruptStreaming$", "guardrailsResult$", "outputMessages", "resolveOutputMessagesPromise", "rejectOutputMessagesPromise", "outputMessagesPromise", "Promise", "resolve", "reject", "eventSource", "threadId", "randomId", "runId", "serverSideActions", "actionInputsWithoutAgents", "extensions", "processRuntimeRequest", "actions", "frontend", "action", "available", "ActionInputAvailability", "disabled", "publicApiKey", "graphqlContext", "forwardedParameters", "agentSession", "agentStates", "url", "metaEvents", "eventStream", "processRuntimeEvents", "find", "serverSideAction", "name", "pipe", "shareReplay", "finalize", "response", "status", "firstValueFrom", "<PERSON><PERSON><PERSON>", "push", "stop", "eventStreamSubscription", "subscribe", "next", "event", "type", "RuntimeEventTypes", "MetaEvent", "RuntimeMetaEventName", "LangGraphInterruptEvent", "plainToInstance", "value", "CopilotKitLangGraphInterruptEvent", "message", "TextMessage", "id", "createdAt", "Date", "SuccessMessageStatus", "ActionExecutionMessage", "arguments", "Error", "err", "UnknownErrorResponse", "description", "unsubscribe", "complete", "SuccessResponseStatus", "pushMessage", "stopStreamingMessages", "result", "GuardrailsValidationFailureResponse", "guardrailsReason", "reason", "TextMessageStart", "textMessageContentStream", "<PERSON><PERSON><PERSON><PERSON>", "e", "<PERSON><PERSON><PERSON><PERSON>", "TextMessageEnd", "messageId", "TextMessageContent", "streamingTextStatus", "Subject", "parentMessageId", "pushTextChunk", "stopStreamingText", "textChunks", "textSubscription", "take", "tap", "FailedMessageStatus", "MessageStreamInterruptedResponse", "join", "ActionExecutionStart", "actionExecutionArgumentStream", "ActionExecutionEnd", "actionExecutionId", "ActionExecutionArgs", "streamingArgumentsStatus", "actionName", "pushArgumentsChunk", "stopStreamingArguments", "argumentChunks", "actionExecutionArgumentSubscription", "args", "ActionExecutionResult", "ResultMessage", "AgentStateMessage", "<PERSON><PERSON><PERSON>", "nodeName", "active", "state", "running", "Query", "String", "AgentsResponse", "Ctx", "Mutation", "CopilotResponse", "Arg", "GraphQLJSONObject", "nullable", "Resolver", "useDeferStream", "createPinoLogger", "pretty", "createLogger", "options", "level", "component", "stream", "pretty", "colorize", "logger", "createPinoLogger", "process", "env", "LOG_LEVEL", "redact", "paths", "remove", "child", "Arg", "Resolver", "Ctx", "Query", "Field", "ObjectType", "LoadAgentStateResponse", "threadId", "threadExists", "state", "messages", "Field", "String", "Boolean", "ObjectType", "Field", "InputType", "LoadAgentStateInput", "threadId", "<PERSON><PERSON><PERSON>", "Field", "String", "InputType", "StateResolver", "loadAgentState", "ctx", "data", "agents", "_copilotkit", "runtime", "discoverAgentsFromEndpoints", "agent", "find", "name", "<PERSON><PERSON><PERSON>", "threadId", "threadExists", "state", "JSON", "stringify", "messages", "Query", "LoadAgentStateResponse", "Ctx", "Arg", "Resolver", "packageJson", "logger", "createLogger", "addCustomHeaderPlugin", "onResponse", "response", "headers", "set", "version", "createContext", "initialContext", "copilotKitContext", "contextLogger", "properties", "debug", "ctx", "_copilotkit", "buildSchema", "options", "schema", "buildSchemaSync", "resolvers", "CopilotResolver", "StateResolver", "emitSchemaFile", "getCommonConfig", "logLevel", "process", "env", "LOG_LEVEL", "level", "component", "cloud", "telemetry", "setCloudConfiguration", "publicApiKey", "baseUrl", "setGlobalProperties", "runtime", "serviceAdapter", "constructor", "name", "logging", "plugins", "useDeferStream", "context", "copilotRuntimeNodeHttpEndpoint", "options", "commonConfig", "getCommonConfig", "telemetry", "setGlobalProperties", "runtime", "framework", "properties", "_copilotkit", "capture", "getRuntimeInstanceTelemetryInfo", "logger", "logging", "debug", "yoga", "createYoga", "graphqlEndpoint", "endpoint"]}