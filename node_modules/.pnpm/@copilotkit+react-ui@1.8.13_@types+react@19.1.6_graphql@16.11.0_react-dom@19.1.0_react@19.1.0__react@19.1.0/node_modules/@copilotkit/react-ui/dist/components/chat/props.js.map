{"version": 3, "sources": ["../../../src/components/chat/props.ts"], "sourcesContent": ["import { Message } from \"@copilotkit/runtime-client-gql\";\nimport { CopilotChatSuggestion } from \"../../types/suggestions\";\nimport { ReactNode } from \"react\";\n\nexport interface ButtonProps {}\n\nexport interface WindowProps {\n  clickOutsideToClose: boolean;\n  hitEscapeToClose: boolean;\n  shortcut: string;\n  children?: React.ReactNode;\n}\n\nexport interface HeaderProps {}\n\nexport interface SuggestionsProps {\n  title: string;\n  message: string;\n  partial?: boolean;\n  className?: string;\n  onClick: (message: string) => void;\n}\n\nexport type ComponentsMap<T extends Record<string, object> = Record<string, object>> = {\n  [K in keyof T]: React.FC<{ children?: ReactNode } & T[K]>;\n};\n\nexport interface MessagesProps {\n  messages: Message[];\n  inProgress: boolean;\n  children?: React.ReactNode;\n  AssistantMessage: React.ComponentType<AssistantMessageProps>;\n  UserMessage: React.ComponentType<UserMessageProps>;\n  RenderTextMessage: React.ComponentType<RenderMessageProps>;\n  RenderActionExecutionMessage: React.ComponentType<RenderMessageProps>;\n  RenderAgentStateMessage: React.ComponentType<RenderMessageProps>;\n  RenderResultMessage: React.ComponentType<RenderMessageProps>;\n  RenderImageMessage: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * Callback function to regenerate the assistant's response\n   */\n  onRegenerate?: (messageId: string) => void;\n\n  /**\n   * Callback function when the message is copied\n   */\n  onCopy?: (message: string) => void;\n\n  /**\n   * Callback function for thumbs up feedback\n   */\n  onThumbsUp?: (message: string) => void;\n\n  /**\n   * Callback function for thumbs down feedback\n   */\n  onThumbsDown?: (message: string) => void;\n\n  /**\n   * A list of markdown components to render in assistant message.\n   * Useful when you want to render custom elements in the message (e.g a reference tag element)\n   */\n  markdownTagRenderers?: ComponentsMap;\n}\n\nexport interface Renderer {\n  content: string;\n}\n\nexport interface UserMessageProps {\n  message?: string;\n  rawData: any;\n  subComponent?: React.JSX.Element;\n}\n\nexport interface AssistantMessageProps {\n  /**\n   * The message content from the assistant\n   */\n\n  message?: string;\n\n  /**\n   * Indicates if this is the last message\n   */\n  isCurrentMessage?: boolean;\n\n  /**\n   * The raw data from the assistant's response\n   */\n  rawData: any;\n\n  /**\n   * A component that was decided to render by the LLM.\n   * When working with useCopilotActions and useCoAgentStateRender, this will be\n   * the render component that was specified.\n   */\n  subComponent?: React.JSX.Element;\n\n  /**\n   * Whether a response is loading, this is when the LLM is thinking of a response but hasn't finished yet.\n   */\n  isLoading: boolean;\n\n  /**\n   * Whether a response is generating, this is when the LLM is actively generating and streaming content.\n   */\n  isGenerating: boolean;\n\n  /**\n   * Callback function to regenerate the assistant's response\n   */\n  onRegenerate?: () => void;\n\n  /**\n   * Callback function when the message is copied\n   */\n  onCopy?: (message: string) => void;\n\n  /**\n   * Callback function for thumbs up feedback\n   */\n  onThumbsUp?: (message: string) => void;\n\n  /**\n   * Callback function for thumbs down feedback\n   */\n  onThumbsDown?: (message: string) => void;\n\n  /**\n   * A list of markdown components to render in assistant message.\n   * Useful when you want to render custom elements in the message (e.g a reference tag element)\n   */\n  markdownTagRenderers?: ComponentsMap;\n}\n\nexport interface RenderMessageProps {\n  message: Message;\n  inProgress: boolean;\n  index: number;\n  isCurrentMessage: boolean;\n  actionResult?: string;\n  AssistantMessage?: React.ComponentType<AssistantMessageProps>;\n  UserMessage?: React.ComponentType<UserMessageProps>;\n\n  /**\n   * Callback function to regenerate the assistant's response\n   */\n  onRegenerate?: (messageId: string) => void;\n\n  /**\n   * Callback function when the message is copied\n   */\n  onCopy?: (message: string) => void;\n\n  /**\n   * Callback function for thumbs up feedback\n   */\n  onThumbsUp?: (message: string) => void;\n\n  /**\n   * Callback function for thumbs down feedback\n   */\n  onThumbsDown?: (message: string) => void;\n\n  /**\n   * A list of markdown components to render in assistant message.\n   * Useful when you want to render custom elements in the message (e.g a reference tag element)\n   */\n  markdownTagRenderers?: ComponentsMap;\n}\n\nexport interface InputProps {\n  inProgress: boolean;\n  onSend: (text: string) => Promise<Message>;\n  isVisible?: boolean;\n  onStop?: () => void;\n  onUpload?: () => void;\n}\n\nexport interface RenderSuggestionsListProps {\n  suggestions: CopilotChatSuggestion[];\n  onSuggestionClick: (message: string) => void;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}