import { ActionInputAvailability } from '@copilotkit/runtime-client-gql';
import { Parameter, Action, MappedParameterTypes } from '@copilotkit/shared';
import React from 'react';

interface InProgressState<T extends Parameter[] | [] = []> {
    status: "inProgress";
    args: Partial<MappedParameterTypes<T>>;
    result: undefined;
}
interface ExecutingState<T extends Parameter[] | [] = []> {
    status: "executing";
    args: MappedParameterTypes<T>;
    result: undefined;
}
interface CompleteState<T extends Parameter[] | [] = []> {
    status: "complete";
    args: MappedParameterTypes<T>;
    result: any;
}
interface InProgressStateNoArgs<T extends Parameter[] | [] = []> {
    status: "inProgress";
    args: Partial<MappedParameterTypes<T>>;
    result: undefined;
}
interface ExecutingStateNoArgs<T extends Parameter[] | [] = []> {
    status: "executing";
    args: MappedParameterTypes<T>;
    result: undefined;
}
interface CompleteStateNoArgs<T extends Parameter[] | [] = []> {
    status: "complete";
    args: MappedParameterTypes<T>;
    result: any;
}
interface InProgressStateWait<T extends Parameter[] | [] = []> {
    status: "inProgress";
    args: Partial<MappedParameterTypes<T>>;
    /** @deprecated use respond instead */
    handler: undefined;
    respond: undefined;
    result: undefined;
}
interface ExecutingStateWait<T extends Parameter[] | [] = []> {
    status: "executing";
    args: MappedParameterTypes<T>;
    /** @deprecated use respond instead */
    handler: (result: any) => void;
    respond: (result: any) => void;
    result: undefined;
}
interface CompleteStateWait<T extends Parameter[] | [] = []> {
    status: "complete";
    args: MappedParameterTypes<T>;
    /** @deprecated use respond instead */
    handler: undefined;
    respond: undefined;
    result: any;
}
interface InProgressStateNoArgsWait<T extends Parameter[] | [] = []> {
    status: "inProgress";
    args: Partial<MappedParameterTypes<T>>;
    /** @deprecated use respond instead */
    handler: undefined;
    respond: undefined;
    result: undefined;
}
interface ExecutingStateNoArgsWait<T extends Parameter[] | [] = []> {
    status: "executing";
    args: MappedParameterTypes<T>;
    /** @deprecated use respond instead */
    handler: (result: any) => void;
    respond: (result: any) => void;
    result: undefined;
}
interface CompleteStateNoArgsWait<T extends Parameter[] | [] = []> {
    status: "complete";
    args: MappedParameterTypes<T>;
    /** @deprecated use respond instead */
    handler: undefined;
    respond: undefined;
}
type ActionRenderProps<T extends Parameter[] | [] = []> = CompleteState<T> | ExecutingState<T> | InProgressState<T>;
type ActionRenderPropsNoArgs<T extends Parameter[] | [] = []> = CompleteStateNoArgs<T> | ExecutingStateNoArgs<T> | InProgressStateNoArgs<T>;
type ActionRenderPropsWait<T extends Parameter[] | [] = []> = CompleteStateWait<T> | ExecutingStateWait<T> | InProgressStateWait<T>;
type ActionRenderPropsNoArgsWait<T extends Parameter[] | [] = []> = CompleteStateNoArgsWait<T> | ExecutingStateNoArgsWait<T> | InProgressStateNoArgsWait<T>;
type CatchAllActionRenderProps<T extends Parameter[] | [] = []> = (CompleteState<T> & {
    name: string;
}) | (ExecutingState<T> & {
    name: string;
}) | (InProgressState<T> & {
    name: string;
});
type FrontendActionAvailability = "disabled" | "enabled" | "remote" | "frontend";
type FrontendAction<T extends Parameter[] | [] = [], N extends string = string> = Action<T> & {
    name: Exclude<N, "*">;
    /**
     * @deprecated Use `available` instead.
     */
    disabled?: boolean;
    available?: FrontendActionAvailability;
    pairedAction?: string;
    followUp?: boolean;
} & ({
    render?: string | (T extends [] ? (props: ActionRenderPropsNoArgs<T>) => string | React.ReactElement : (props: ActionRenderProps<T>) => string | React.ReactElement);
    /** @deprecated use renderAndWaitForResponse instead */
    renderAndWait?: never;
    renderAndWaitForResponse?: never;
} | {
    render?: never;
    /** @deprecated use renderAndWaitForResponse instead */
    renderAndWait?: T extends [] ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement : (props: ActionRenderPropsWait<T>) => React.ReactElement;
    renderAndWaitForResponse?: T extends [] ? (props: ActionRenderPropsNoArgsWait<T>) => React.ReactElement : (props: ActionRenderPropsWait<T>) => React.ReactElement;
    handler?: never;
});
type CatchAllFrontendAction = {
    name: "*";
    render: (props: CatchAllActionRenderProps<any>) => React.ReactElement;
};
type RenderFunctionStatus = ActionRenderProps<any>["status"];
declare function processActionsForRuntimeRequest(actions: FrontendAction<any>[]): {
    name: string;
    description: string;
    jsonSchema: string;
    available: ActionInputAvailability;
}[];

export { ActionRenderProps, ActionRenderPropsNoArgs, ActionRenderPropsNoArgsWait, ActionRenderPropsWait, CatchAllActionRenderProps, CatchAllFrontendAction, FrontendAction, FrontendActionAvailability, RenderFunctionStatus, processActionsForRuntimeRequest };
