import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import { vi } from 'vitest';
import VoiceMetricsPage from '../page';

// Mock all dependencies
vi.mock('@/components/auth/SuperAdminGuard', () => {
  return {
    SuperAdminGuard: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  };
});

vi.mock('@/hooks/useRbac', () => ({
  useRbac: () => ({
    isSuperAdmin: vi.fn(() => true),
    isLoading: vi.fn(() => false),
  }),
}));

vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Voice Metrics Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
    process.env.NEXT_PUBLIC_GRAFANA_URL = 'https://grafana.test.com/d/voice-metrics';
    process.env.NEXT_PUBLIC_PROMETHEUS_PROXY_URL = '/api/prometheus';

    // Setup default successful fetch mock
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '5'] }] }
      }),
    });
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  it('displays complete dashboard with all components', async () => {
    // Mock successful API responses for all metrics
    const mockResponses = [
      { status: 'success', data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '25'] }] } },
      { status: 'success', data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '8'] }] } },
      { status: 'success', data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '2.5'] }] } },
      { status: 'success', data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '3'] }] } },
    ];

    mockFetch
      .mockResolvedValueOnce({ ok: true, json: async () => mockResponses[0] })
      .mockResolvedValueOnce({ ok: true, json: async () => mockResponses[1] })
      .mockResolvedValueOnce({ ok: true, json: async () => mockResponses[2] })
      .mockResolvedValueOnce({ ok: true, json: async () => mockResponses[3] });

    render(<VoiceMetricsPage />);

    // Check page header
    expect(screen.getByText('Voice Metrics')).toBeInTheDocument();
    expect(screen.getByText(/Real-time monitoring of voice service performance/)).toBeInTheDocument();

    // Check refresh button
    expect(screen.getByRole('button', { name: /refresh/i })).toBeInTheDocument();

    // Wait for metrics to load
    await waitFor(() => {
      // Check all metric cards are present
      expect(screen.getByText('Active Calls')).toBeInTheDocument();
      expect(screen.getByText('Queue Depth')).toBeInTheDocument();
      expect(screen.getByText('P95 Latency')).toBeInTheDocument();
      expect(screen.getByText('Call Rejects')).toBeInTheDocument();

      // Check metric values
      expect(screen.getByText('25')).toBeInTheDocument(); // active calls
      expect(screen.getByText('8')).toBeInTheDocument(); // queue depth
      expect(screen.getByText('2500ms')).toBeInTheDocument(); // latency
      expect(screen.getByText('3')).toBeInTheDocument(); // rejects
    });

    // Check status indicators
    expect(screen.getByText('Connected to Prometheus')).toBeInTheDocument();
    expect(screen.getByText(/Last updated:/)).toBeInTheDocument();

    // Check Grafana iframe
    const iframe = screen.getByTitle('Voice Metrics Grafana Dashboard');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', expect.stringContaining('grafana.test.com'));
  });

  it('handles auto-refresh functionality', async () => {
    // Simplified test - just verify that setInterval is called
    const setIntervalSpy = vi.spyOn(global, 'setInterval');

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    // Wait for component to mount and verify interval is set
    await waitFor(() => {
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 15000);
    }, { timeout: 2000 });

    setIntervalSpy.mockRestore();
  });

  it('displays appropriate status badges for different metric values', async () => {
    // Test high active calls scenario
    mockFetch
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '60'] }] }
      }) })
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '15'] }] }
      }) })
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '6.0'] }] }
      }) })
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [Date.now() / 1000, '8'] }] }
      }) });

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    await waitFor(() => {
      expect(screen.getByText('60')).toBeInTheDocument(); // active calls
      expect(screen.getByText('15')).toBeInTheDocument(); // queue depth
      expect(screen.getAllByText('High')).toHaveLength(3); // For active calls, queue depth, and rejects
      expect(screen.getByText('Slow')).toBeInTheDocument(); // For latency
    }, { timeout: 3000 });
  });

  it('handles error states gracefully', async () => {
    // Mock API failure
    mockFetch.mockRejectedValue(new Error('Network error'));

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    await waitFor(() => {
      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(screen.getByText('Configuration Error')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('works without Grafana URL configured', async () => {
    process.env.NEXT_PUBLIC_GRAFANA_URL = '';

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    await waitFor(() => {
      expect(screen.getByText('Configure NEXT_PUBLIC_GRAFANA_URL to display the embedded dashboard')).toBeInTheDocument();
      expect(screen.queryByTitle('Voice Metrics Grafana Dashboard')).not.toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('formats time and values correctly', async () => {
    const now = new Date('2024-01-15T10:30:00Z');
    const mockDate = vi.spyOn(global, 'Date').mockImplementation(() => now as any);
    // Preserve Date.now functionality
    (global.Date as any).now = vi.fn(() => now.getTime());

    // Mock all 4 API calls with specific values
    mockFetch
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [now.getTime() / 1000, '5'] }] }
      }) })
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [now.getTime() / 1000, '3'] }] }
      }) })
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [now.getTime() / 1000, '1.234'] }] }
      }) })
      .mockResolvedValueOnce({ ok: true, json: async () => ({
        status: 'success',
        data: { resultType: 'vector', result: [{ metric: {}, value: [now.getTime() / 1000, '1'] }] }
      }) });

    await act(async () => {
      render(<VoiceMetricsPage />);
    });

    await waitFor(() => {
      // Check that latency is converted to milliseconds and rounded
      expect(screen.getByText('1234ms')).toBeInTheDocument();

      // Check time formatting
      expect(screen.getByText(/Last updated: \d{1,2}:\d{2}:\d{2}/)).toBeInTheDocument();
    }, { timeout: 2000 });

    mockDate.mockRestore();
  });
});
