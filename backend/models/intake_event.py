"""
SQLAlchemy model for intake events.

This module defines the IntakeEvent model for storing webhook events
from the Voice service.
"""

from datetime import datetime
from typing import Any, Dict
from uuid import UUID, uuid4

from sqlalchemy import <PERSON>olean, DateTime, Index
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base


class IntakeEvent(Base):
    """
    Model for storing intake webhook events from the Voice service.
    
    This table stores raw webhook payloads for processing by background workers.
    """
    
    __tablename__ = "intake_events"
    __table_args__ = (
        Index("ix_tenants_intake_events_tenant_id", "tenant_id"),
        Index("ix_tenants_intake_events_received_at", "received_at"),
        Index("ix_tenants_intake_events_processed", "processed"),
        {"schema": "tenants"},
    )
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default="gen_random_uuid()"
    )
    
    # Tenant isolation
    tenant_id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        nullable=False,
        index=True
    )
    
    # Webhook payload (raw JSON from Voice service)
    payload: Mapped[Dict[str, Any]] = mapped_column(
        JSONB,
        nullable=False
    )
    
    # Timestamp when webhook was received
    received_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default="now()"
    )
    
    # Processing status flag
    processed: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        server_default="false"
    )
    
    def __repr__(self) -> str:
        return f"<IntakeEvent(id={self.id}, tenant_id={self.tenant_id}, processed={self.processed})>"
