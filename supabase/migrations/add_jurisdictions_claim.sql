-- Migration: Add jurisdictions claim to JWT tokens
-- This adds a jurisdictions array to user metadata that will be included in JWT tokens

-- Function to update user metadata with jurisdictions
CREATE OR REPLACE FUNCTION add_jurisdictions_to_user_metadata()
RETURNS TRIGGER AS $$
BEGIN
  -- Add jurisdictions array to raw_user_meta_data if it doesn't exist
  IF NEW.raw_user_meta_data IS NULL THEN
    NEW.raw_user_meta_data = '{}';
  END IF;
  
  -- Check if jurisdictions already exists
  IF NOT (NEW.raw_user_meta_data ? 'jurisdictions') THEN
    -- Default jurisdictions based on user role or other criteria
    -- For now, we'll default to Texas for all users
    -- This can be customized based on business logic
    NEW.raw_user_meta_data = jsonb_set(
      NEW.raw_user_meta_data::jsonb,
      '{jurisdictions}',
      '["tx"]'::jsonb
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically add jurisdictions on user creation/update
DROP TRIGGER IF EXISTS add_jurisdictions_trigger ON auth.users;
CREATE TRIGGER add_jurisdictions_trigger
  BEFORE INSERT OR UPDATE ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION add_jurisdictions_to_user_metadata();

-- Update existing users to have jurisdictions claim
UPDATE auth.users 
SET raw_user_meta_data = jsonb_set(
  COALESCE(raw_user_meta_data, '{}')::jsonb,
  '{jurisdictions}',
  '["tx"]'::jsonb
)
WHERE raw_user_meta_data IS NULL 
   OR NOT (raw_user_meta_data::jsonb ? 'jurisdictions');

-- Function to update user jurisdictions (for admin use)
CREATE OR REPLACE FUNCTION update_user_jurisdictions(
  user_id UUID,
  new_jurisdictions TEXT[]
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE auth.users 
  SET raw_user_meta_data = jsonb_set(
    COALESCE(raw_user_meta_data, '{}')::jsonb,
    '{jurisdictions}',
    to_jsonb(new_jurisdictions)
  )
  WHERE id = user_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_user_jurisdictions(UUID, TEXT[]) TO service_role;

-- Example usage:
-- SELECT update_user_jurisdictions('user-uuid-here', ARRAY['tx', 'ny']);

-- Verify the migration worked
-- SELECT id, email, raw_user_meta_data->'jurisdictions' as jurisdictions 
-- FROM auth.users 
-- LIMIT 5;
