import {
  AssistantMessage
} from "./chunk-XMNTLIK5.mjs";
import {
  UserMessage
} from "./chunk-HWMFMBJC.mjs";
import {
  __objRest
} from "./chunk-MRXNTQOX.mjs";

// src/components/chat/messages/RenderTextMessage.tsx
import { jsx } from "react/jsx-runtime";
function RenderTextMessage(_a) {
  var _b = _a, {
    UserMessage: UserMessage2 = UserMessage,
    AssistantMessage: AssistantMessage2 = AssistantMessage
  } = _b, props = __objRest(_b, [
    "UserMessage",
    "AssistantMessage"
  ]);
  const {
    message,
    inProgress,
    index,
    isCurrentMessage,
    onRegenerate,
    onCopy,
    onThumbsUp,
    onThumbsDown,
    markdownTagRenderers
  } = props;
  if (message.isTextMessage()) {
    if (message.role === "user") {
      return /* @__PURE__ */ jsx(
        UserMessage2,
        {
          "data-message-role": "user",
          message: message.content,
          rawData: message
        },
        index
      );
    } else if (message.role == "assistant") {
      return /* @__PURE__ */ jsx(
        AssistantMessage2,
        {
          "data-message-role": "assistant",
          message: message.content,
          rawData: message,
          isLoading: inProgress && isCurrentMessage && !message.content,
          isGenerating: inProgress && isCurrentMessage && !!message.content,
          isCurrentMessage,
          onRegenerate: () => onRegenerate == null ? void 0 : onRegenerate(message.id),
          onCopy,
          onThumbsUp,
          onThumbsDown,
          markdownTagRenderers
        },
        index
      );
    }
  }
}

export {
  RenderTextMessage
};
//# sourceMappingURL=chunk-5INP2EWU.mjs.map