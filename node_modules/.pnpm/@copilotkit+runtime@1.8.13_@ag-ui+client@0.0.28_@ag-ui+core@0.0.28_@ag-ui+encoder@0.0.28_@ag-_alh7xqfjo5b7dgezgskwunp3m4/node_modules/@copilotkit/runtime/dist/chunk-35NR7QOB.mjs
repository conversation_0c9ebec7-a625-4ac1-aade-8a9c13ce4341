import {
  copilotRuntimeNodeHttpEndpoint,
  getRuntimeInstanceTelemetryInfo,
  telemetry_client_default
} from "./chunk-XOH4CYV6.mjs";
import {
  __name
} from "./chunk-FHD4JECV.mjs";

// src/lib/integrations/nest/index.ts
function copilotRuntimeNestEndpoint(options) {
  telemetry_client_default.setGlobalProperties({
    runtime: {
      framework: "nest"
    }
  });
  telemetry_client_default.capture("oss.runtime.instance_created", getRuntimeInstanceTelemetryInfo(options.runtime));
  return copilotRuntimeNodeHttpEndpoint(options);
}
__name(copilotRuntimeNestEndpoint, "copilotRuntimeNestEndpoint");

export {
  copilotRuntimeNestEndpoint
};
//# sourceMappingURL=chunk-35NR7QOB.mjs.map