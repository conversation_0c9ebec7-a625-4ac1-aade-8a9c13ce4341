{"version": 3, "sources": ["../../../../src/graphql/types/converted/index.ts", "../../../../src/graphql/types/base/index.ts"], "sourcesContent": ["import {\n  ActionExecutionMessageInput,\n  ResultMessageInput,\n  TextMessageInput,\n  AgentStateMessageInput,\n  ImageMessageInput,\n} from \"../../inputs/message.input\";\nimport { BaseMessageInput } from \"../base\";\nimport { MessageRole } from \"../enums\";\n\nexport type MessageType =\n  | \"TextMessage\"\n  | \"ActionExecutionMessage\"\n  | \"ResultMessage\"\n  | \"AgentStateMessage\"\n  | \"ImageMessage\";\n\nexport class Message extends BaseMessageInput {\n  type: MessageType;\n\n  isTextMessage(): this is TextMessage {\n    return this.type === \"TextMessage\";\n  }\n\n  isActionExecutionMessage(): this is ActionExecutionMessage {\n    return this.type === \"ActionExecutionMessage\";\n  }\n\n  isResultMessage(): this is ResultMessage {\n    return this.type === \"ResultMessage\";\n  }\n\n  isAgentStateMessage(): this is AgentStateMessage {\n    return this.type === \"AgentStateMessage\";\n  }\n\n  isImageMessage(): this is ImageMessage {\n    return this.type === \"ImageMessage\";\n  }\n}\n\nexport class TextMessage extends Message implements TextMessageInput {\n  type: MessageType = \"TextMessage\";\n  content: string;\n  role: MessageRole;\n  parentMessageId?: string;\n}\n\nexport class ActionExecutionMessage\n  extends Message\n  implements Omit<ActionExecutionMessageInput, \"arguments\" | \"scope\">\n{\n  type: MessageType = \"ActionExecutionMessage\";\n  name: string;\n  arguments: Record<string, any>;\n  parentMessageId?: string;\n}\n\nexport class ResultMessage extends Message implements ResultMessageInput {\n  type: MessageType = \"ResultMessage\";\n  actionExecutionId: string;\n  actionName: string;\n  result: string;\n\n  static encodeResult(\n    result: any,\n    error?: { code: string; message: string } | string | Error,\n  ): string {\n    const errorObj = error\n      ? typeof error === \"string\"\n        ? { code: \"ERROR\", message: error }\n        : error instanceof Error\n          ? { code: \"ERROR\", message: error.message }\n          : error\n      : undefined;\n\n    if (errorObj) {\n      return JSON.stringify({\n        error: errorObj,\n        result: result || \"\",\n      });\n    }\n    if (result === undefined) {\n      return \"\";\n    }\n    return typeof result === \"string\" ? result : JSON.stringify(result);\n  }\n\n  static decodeResult(result: string): {\n    error?: { code: string; message: string };\n    result: string;\n  } {\n    if (!result) {\n      return { result: \"\" };\n    }\n    try {\n      const parsed = JSON.parse(result);\n      if (parsed && typeof parsed === \"object\") {\n        if (\"error\" in parsed) {\n          return {\n            error: parsed.error,\n            result: parsed.result || \"\",\n          };\n        }\n        return { result: JSON.stringify(parsed) };\n      }\n      return { result };\n    } catch (e) {\n      return { result };\n    }\n  }\n\n  hasError(): boolean {\n    try {\n      const { error } = ResultMessage.decodeResult(this.result);\n      return !!error;\n    } catch {\n      return false;\n    }\n  }\n\n  getError(): { code: string; message: string } | undefined {\n    try {\n      const { error } = ResultMessage.decodeResult(this.result);\n      return error;\n    } catch {\n      return undefined;\n    }\n  }\n}\n\nexport class AgentStateMessage extends Message implements Omit<AgentStateMessageInput, \"state\"> {\n  type: MessageType = \"AgentStateMessage\";\n  threadId: string;\n  agentName: string;\n  nodeName: string;\n  runId: string;\n  active: boolean;\n  role: MessageRole;\n  state: any;\n  running: boolean;\n}\n\nexport class ImageMessage extends Message implements ImageMessageInput {\n  type: MessageType = \"ImageMessage\";\n  format: string;\n  bytes: string;\n  role: MessageRole;\n  parentMessageId?: string;\n}\n", "import { Field, InputType } from \"type-graphql\";\n\n@InputType()\nexport class BaseMessageInput {\n  @Field(() => String)\n  id: string;\n\n  @Field(() => Date)\n  createdAt: Date;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAOA;;;;;;;;;;;;ACPA,0BAAiC;;;;;;;;;;;;;;;;;AAG1B,IAAMA,mBAAN,MAAMA;EAEXC;EAGAC;AACF;AANaF;;MACVG,2BAAM,MAAMC,MAAAA;;GADFJ,iBAAAA,WAAAA,MAAAA,MAAAA;;MAIVG,2BAAM,MAAME,IAAAA;qCACF,SAAA,cAAA,SAAA,IAAA;GALAL,iBAAAA,WAAAA,aAAAA,MAAAA;AAAAA,mBAAAA,aAAAA;MADZM,+BAAAA;GACYN,gBAAAA;;;ADcN,IAAMO,UAAN,cAAsBC,iBAAAA;EAC3BC;EAEAC,gBAAqC;AACnC,WAAO,KAAKD,SAAS;EACvB;EAEAE,2BAA2D;AACzD,WAAO,KAAKF,SAAS;EACvB;EAEAG,kBAAyC;AACvC,WAAO,KAAKH,SAAS;EACvB;EAEAI,sBAAiD;AAC/C,WAAO,KAAKJ,SAAS;EACvB;EAEAK,iBAAuC;AACrC,WAAO,KAAKL,SAAS;EACvB;AACF;AAtBaF;AAwBN,IAAMQ,cAAN,cAA0BR,QAAAA;EAC/BE,OAAoB;EACpBO;EACAC;EACAC;AACF;AALaH;AAON,IAAMI,yBAAN,cACGZ,QAAAA;EAGRE,OAAoB;EACpBW;EACAC;EACAH;AACF;AARaC;AAUN,IAAMG,gBAAN,cAA4Bf,QAAAA;EACjCE,OAAoB;EACpBc;EACAC;EACAC;EAEA,OAAOC,aACLD,QACAE,OACQ;AACR,UAAMC,WAAWD,QACb,OAAOA,UAAU,WACf;MAAEE,MAAM;MAASC,SAASH;IAAM,IAChCA,iBAAiBI,QACf;MAAEF,MAAM;MAASC,SAASH,MAAMG;IAAQ,IACxCH,QACJK;AAEJ,QAAIJ,UAAU;AACZ,aAAOK,KAAKC,UAAU;QACpBP,OAAOC;QACPH,QAAQA,UAAU;MACpB,CAAA;IACF;AACA,QAAIA,WAAWO,QAAW;AACxB,aAAO;IACT;AACA,WAAO,OAAOP,WAAW,WAAWA,SAASQ,KAAKC,UAAUT,MAAAA;EAC9D;EAEA,OAAOU,aAAaV,QAGlB;AACA,QAAI,CAACA,QAAQ;AACX,aAAO;QAAEA,QAAQ;MAAG;IACtB;AACA,QAAI;AACF,YAAMW,SAASH,KAAKI,MAAMZ,MAAAA;AAC1B,UAAIW,UAAU,OAAOA,WAAW,UAAU;AACxC,YAAI,WAAWA,QAAQ;AACrB,iBAAO;YACLT,OAAOS,OAAOT;YACdF,QAAQW,OAAOX,UAAU;UAC3B;QACF;AACA,eAAO;UAAEA,QAAQQ,KAAKC,UAAUE,MAAAA;QAAQ;MAC1C;AACA,aAAO;QAAEX;MAAO;IAClB,SAASa,GAAP;AACA,aAAO;QAAEb;MAAO;IAClB;EACF;EAEAc,WAAoB;AAClB,QAAI;AACF,YAAM,EAAEZ,MAAK,IAAKL,cAAca,aAAa,KAAKV,MAAM;AACxD,aAAO,CAAC,CAACE;IACX,QAAE;AACA,aAAO;IACT;EACF;EAEAa,WAA0D;AACxD,QAAI;AACF,YAAM,EAAEb,MAAK,IAAKL,cAAca,aAAa,KAAKV,MAAM;AACxD,aAAOE;IACT,QAAE;AACA,aAAOK;IACT;EACF;AACF;AAvEaV;AAyEN,IAAMmB,oBAAN,cAAgClC,QAAAA;EACrCE,OAAoB;EACpBiC;EACAC;EACAC;EACAC;EACAC;EACA7B;EACA8B;EACAC;AACF;AAVaP;AAYN,IAAMQ,eAAN,cAA2B1C,QAAAA;EAChCE,OAAoB;EACpByC;EACAC;EACAlC;EACAC;AACF;AANa+B;", "names": ["BaseMessageInput", "id", "createdAt", "Field", "String", "Date", "InputType", "Message", "BaseMessageInput", "type", "isTextMessage", "isActionExecutionMessage", "isResultMessage", "isAgentStateMessage", "isImageMessage", "TextMessage", "content", "role", "parentMessageId", "ActionExecutionMessage", "name", "arguments", "ResultMessage", "actionExecutionId", "actionName", "result", "encodeResult", "error", "errorObj", "code", "message", "Error", "undefined", "JSON", "stringify", "decodeResult", "parsed", "parse", "e", "<PERSON><PERSON><PERSON><PERSON>", "getError", "AgentStateMessage", "threadId", "<PERSON><PERSON><PERSON>", "nodeName", "runId", "active", "state", "running", "ImageMessage", "format", "bytes"]}