"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractExtensionsFromSchema = exports.extendResolversFromInterfaces = exports.checkForResolveTypeResolver = exports.addResolversToSchema = exports.chainResolvers = exports.assertResolversPresent = void 0;
const tslib_1 = require("tslib");
var assertResolversPresent_js_1 = require("./assertResolversPresent.js");
Object.defineProperty(exports, "assertResolversPresent", { enumerable: true, get: function () { return assertResolversPresent_js_1.assertResolversPresent; } });
var chainResolvers_js_1 = require("./chainResolvers.js");
Object.defineProperty(exports, "chainResolvers", { enumerable: true, get: function () { return chainResolvers_js_1.chainResolvers; } });
var addResolversToSchema_js_1 = require("./addResolversToSchema.js");
Object.defineProperty(exports, "addResolversToSchema", { enumerable: true, get: function () { return addResolversToSchema_js_1.addResolversToSchema; } });
var checkForResolveTypeResolver_js_1 = require("./checkForResolveTypeResolver.js");
Object.defineProperty(exports, "checkForResolveTypeResolver", { enumerable: true, get: function () { return checkForResolveTypeResolver_js_1.checkForResolveTypeResolver; } });
var extendResolversFromInterfaces_js_1 = require("./extendResolversFromInterfaces.js");
Object.defineProperty(exports, "extendResolversFromInterfaces", { enumerable: true, get: function () { return extendResolversFromInterfaces_js_1.extendResolversFromInterfaces; } });
tslib_1.__exportStar(require("./makeExecutableSchema.js"), exports);
tslib_1.__exportStar(require("./types.js"), exports);
tslib_1.__exportStar(require("./merge-schemas.js"), exports);
var utils_1 = require("@graphql-tools/utils");
Object.defineProperty(exports, "extractExtensionsFromSchema", { enumerable: true, get: function () { return utils_1.extractExtensionsFromSchema; } });
