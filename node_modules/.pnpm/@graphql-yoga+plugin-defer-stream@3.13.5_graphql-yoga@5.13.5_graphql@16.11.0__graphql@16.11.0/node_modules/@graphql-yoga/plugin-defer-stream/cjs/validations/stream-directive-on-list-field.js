"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamDirectiveOnListFieldRule = StreamDirectiveOnListFieldRule;
const graphql_1 = require("graphql");
const graphql_yoga_1 = require("graphql-yoga");
const utils_1 = require("@graphql-tools/utils");
/**
 * Stream directive on list field
 *
 * A GraphQL document is only valid if stream directives are used on list fields.
 */
function StreamDirectiveOnListFieldRule(context) {
    return {
        Directive(node) {
            const fieldDef = context.getFieldDef();
            const parentType = context.getParentType();
            if (fieldDef &&
                parentType &&
                node.name.value === utils_1.GraphQLStreamDirective.name &&
                !((0, graphql_1.isListType)(fieldDef.type) ||
                    ((0, graphql_1.isWrappingType)(fieldDef.type) && (0, graphql_1.isListType)(fieldDef.type.ofType)))) {
                context.reportError((0, graphql_yoga_1.createGraphQLError)(`Stream directive cannot be used on non-list field "${fieldDef.name}" on type "${parentType.name}".`, { nodes: node }));
            }
        },
    };
}
