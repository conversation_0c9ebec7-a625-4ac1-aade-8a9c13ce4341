"use strict";
"use client";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __restKey = (key) => typeof key === "symbol" ? key : key + "";
var __objRest = (source, exclude) => {
  var target = {};
  for (var prop in source)
    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)
      target[prop] = source[prop];
  if (source != null && __getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(source)) {
      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))
        target[prop] = source[prop];
    }
  return target;
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/index.tsx
var src_exports = {};
__export(src_exports, {
  CopilotContext: () => CopilotContext,
  CopilotKit: () => CopilotKit,
  CopilotMessagesContext: () => CopilotMessagesContext,
  CopilotTask: () => CopilotTask,
  defaultCopilotContextCategories: () => defaultCopilotContextCategories,
  extract: () => extract,
  runAgent: () => runAgent,
  shouldShowDevConsole: () => shouldShowDevConsole,
  startAgent: () => startAgent,
  stopAgent: () => stopAgent,
  useCoAgent: () => useCoAgent,
  useCoAgentStateRender: () => useCoAgentStateRender,
  useCopilotAction: () => useCopilotAction,
  useCopilotAdditionalInstructions: () => useCopilotAdditionalInstructions,
  useCopilotAuthenticatedAction_c: () => useCopilotAuthenticatedAction_c,
  useCopilotChat: () => useCopilotChat,
  useCopilotContext: () => useCopilotContext,
  useCopilotMessagesContext: () => useCopilotMessagesContext,
  useCopilotReadable: () => useCopilotReadable,
  useCopilotRuntimeClient: () => useCopilotRuntimeClient,
  useLangGraphInterrupt: () => useLangGraphInterrupt,
  useLangGraphInterruptRender: () => useLangGraphInterruptRender,
  useMakeCopilotDocumentReadable: () => useMakeCopilotDocumentReadable
});
module.exports = __toCommonJS(src_exports);

// src/components/copilot-provider/copilotkit.tsx
var import_react10 = require("react");

// src/context/copilot-context.tsx
var import_react = __toESM(require("react"));
var emptyCopilotContext = {
  actions: {},
  setAction: () => {
  },
  removeAction: () => {
  },
  coAgentStateRenders: {},
  setCoAgentStateRender: () => {
  },
  removeCoAgentStateRender: () => {
  },
  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },
  getContextString: (documents, categories) => returnAndThrowInDebug(""),
  addContext: () => "",
  removeContext: () => {
  },
  getFunctionCallHandler: () => returnAndThrowInDebug(() => __async(void 0, null, function* () {
  })),
  isLoading: false,
  setIsLoading: () => returnAndThrowInDebug(false),
  chatInstructions: "",
  setChatInstructions: () => returnAndThrowInDebug(""),
  additionalInstructions: [],
  setAdditionalInstructions: () => returnAndThrowInDebug([]),
  getDocumentsContext: (categories) => returnAndThrowInDebug([]),
  addDocumentContext: () => returnAndThrowInDebug(""),
  removeDocumentContext: () => {
  },
  runtimeClient: {},
  copilotApiConfig: new class {
    get chatApiEndpoint() {
      throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
    }
    get headers() {
      return {};
    }
    get body() {
      return {};
    }
  }(),
  chatSuggestionConfiguration: {},
  addChatSuggestionConfiguration: () => {
  },
  removeChatSuggestionConfiguration: () => {
  },
  showDevConsole: "auto",
  coagentStates: {},
  setCoagentStates: () => {
  },
  coagentStatesRef: { current: {} },
  setCoagentStatesWithRef: () => {
  },
  agentSession: null,
  setAgentSession: () => {
  },
  forwardedParameters: {},
  agentLock: null,
  threadId: "",
  setThreadId: () => {
  },
  runId: null,
  setRunId: () => {
  },
  chatAbortControllerRef: { current: null },
  availableAgents: [],
  extensions: {},
  setExtensions: () => {
  },
  langGraphInterruptAction: null,
  setLangGraphInterruptAction: () => null,
  removeLangGraphInterruptAction: () => null
};
var CopilotContext = import_react.default.createContext(emptyCopilotContext);
function useCopilotContext() {
  const context = import_react.default.useContext(CopilotContext);
  if (context === emptyCopilotContext) {
    throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
  }
  return context;
}
function returnAndThrowInDebug(_value) {
  throw new Error("Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!");
}

// src/hooks/use-tree.ts
var import_shared = require("@copilotkit/shared");
var import_react2 = require("react");
var removeNode = (nodes, id) => {
  return nodes.reduce((result, node) => {
    if (node.id !== id) {
      const newNode = __spreadProps(__spreadValues({}, node), { children: removeNode(node.children, id) });
      result.push(newNode);
    }
    return result;
  }, []);
};
var addNode = (nodes, newNode, parentId) => {
  if (!parentId) {
    return [...nodes, newNode];
  }
  return nodes.map((node) => {
    if (node.id === parentId) {
      return __spreadProps(__spreadValues({}, node), { children: [...node.children, newNode] });
    } else if (node.children.length) {
      return __spreadProps(__spreadValues({}, node), { children: addNode(node.children, newNode, parentId) });
    }
    return node;
  });
};
var treeIndentationRepresentation = (index, indentLevel) => {
  if (indentLevel === 0) {
    return (index + 1).toString();
  } else if (indentLevel === 1) {
    return String.fromCharCode(65 + index);
  } else if (indentLevel === 2) {
    return String.fromCharCode(97 + index);
  } else {
    return "-";
  }
};
var printNode = (node, prefix = "", indentLevel = 0) => {
  const indent = " ".repeat(3).repeat(indentLevel);
  const prefixPlusIndentLength = prefix.length + indent.length;
  const subsequentLinesPrefix = " ".repeat(prefixPlusIndentLength);
  const valueLines = node.value.split("\n");
  const outputFirstLine = `${indent}${prefix}${valueLines[0]}`;
  const outputSubsequentLines = valueLines.slice(1).map((line) => `${subsequentLinesPrefix}${line}`).join("\n");
  let output = `${outputFirstLine}
`;
  if (outputSubsequentLines) {
    output += `${outputSubsequentLines}
`;
  }
  const childPrePrefix = " ".repeat(prefix.length);
  node.children.forEach(
    (child, index) => output += printNode(
      child,
      `${childPrePrefix}${treeIndentationRepresentation(index, indentLevel + 1)}. `,
      indentLevel + 1
    )
  );
  return output;
};
function treeReducer(state, action) {
  switch (action.type) {
    case "ADD_NODE": {
      const { value, parentId, id: newNodeId } = action;
      const newNode = {
        id: newNodeId,
        value,
        children: [],
        categories: new Set(action.categories)
      };
      try {
        return addNode(state, newNode, parentId);
      } catch (error) {
        console.error(`Error while adding node with id ${newNodeId}: ${error}`);
        return state;
      }
    }
    case "REMOVE_NODE":
      return removeNode(state, action.id);
    default:
      return state;
  }
}
var useTree = () => {
  const [tree, dispatch] = (0, import_react2.useReducer)(treeReducer, []);
  const addElement = (0, import_react2.useCallback)(
    (value, categories, parentId) => {
      const newNodeId = (0, import_shared.randomId)();
      dispatch({
        type: "ADD_NODE",
        value,
        parentId,
        id: newNodeId,
        categories
      });
      return newNodeId;
    },
    []
  );
  const removeElement = (0, import_react2.useCallback)((id) => {
    dispatch({ type: "REMOVE_NODE", id });
  }, []);
  const printTree = (0, import_react2.useCallback)(
    (categories) => {
      const categoriesSet = new Set(categories);
      let output = "";
      tree.forEach((node, index) => {
        if (!setsHaveIntersection(categoriesSet, node.categories)) {
          return;
        }
        if (index !== 0) {
          output += "\n";
        }
        output += printNode(node, `${treeIndentationRepresentation(index, 0)}. `);
      });
      return output;
    },
    [tree]
  );
  return { tree, addElement, printTree, removeElement };
};
var use_tree_default = useTree;
function setsHaveIntersection(setA, setB) {
  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];
  for (let item of smallerSet) {
    if (largerSet.has(item)) {
      return true;
    }
  }
  return false;
}

// src/components/copilot-provider/copilotkit.tsx
var import_react_dom = require("react-dom");
var import_shared8 = require("@copilotkit/shared");

// src/hooks/use-flat-category-store.ts
var import_react3 = require("react");
var import_shared2 = require("@copilotkit/shared");
var useFlatCategoryStore = () => {
  const [elements, dispatch] = (0, import_react3.useReducer)(flatCategoryStoreReducer, /* @__PURE__ */ new Map());
  const addElement = (0, import_react3.useCallback)((value, categories) => {
    const newId = (0, import_shared2.randomId)();
    dispatch({
      type: "ADD_ELEMENT",
      value,
      id: newId,
      categories
    });
    return newId;
  }, []);
  const removeElement = (0, import_react3.useCallback)((id) => {
    dispatch({ type: "REMOVE_ELEMENT", id });
  }, []);
  const allElements = (0, import_react3.useCallback)(
    (categories) => {
      const categoriesSet = new Set(categories);
      const result = [];
      elements.forEach((element) => {
        if (setsHaveIntersection2(categoriesSet, element.categories)) {
          result.push(element.value);
        }
      });
      return result;
    },
    [elements]
  );
  return { addElement, removeElement, allElements };
};
var use_flat_category_store_default = useFlatCategoryStore;
function flatCategoryStoreReducer(state, action) {
  switch (action.type) {
    case "ADD_ELEMENT": {
      const { value, id, categories } = action;
      const newElement = {
        id,
        value,
        categories: new Set(categories)
      };
      const newState = new Map(state);
      newState.set(id, newElement);
      return newState;
    }
    case "REMOVE_ELEMENT": {
      const newState = new Map(state);
      newState.delete(action.id);
      return newState;
    }
    default:
      return state;
  }
}
function setsHaveIntersection2(setA, setB) {
  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];
  for (let item of smallerSet) {
    if (largerSet.has(item)) {
      return true;
    }
  }
  return false;
}

// src/components/copilot-provider/copilot-messages.tsx
var import_react5 = require("react");

// src/context/copilot-messages-context.tsx
var import_react4 = __toESM(require("react"));
var emptyCopilotContext2 = {
  messages: [],
  setMessages: () => []
};
var CopilotMessagesContext = import_react4.default.createContext(emptyCopilotContext2);
function useCopilotMessagesContext() {
  const context = import_react4.default.useContext(CopilotMessagesContext);
  if (context === emptyCopilotContext2) {
    throw new Error(
      "A messages consuming component was not wrapped with `<CopilotMessages> {...} </CopilotMessages>`"
    );
  }
  return context;
}

// src/components/copilot-provider/copilot-messages.tsx
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_jsx_runtime = require("react/jsx-runtime");
function CopilotMessages({ children }) {
  const [messages, setMessages] = (0, import_react5.useState)([]);
  const lastLoadedThreadId = (0, import_react5.useRef)();
  const lastLoadedAgentName = (0, import_react5.useRef)();
  const lastLoadedMessages = (0, import_react5.useRef)();
  const { threadId, agentSession, runtimeClient } = useCopilotContext();
  (0, import_react5.useEffect)(() => {
    if (!threadId || threadId === lastLoadedThreadId.current)
      return;
    if (threadId === lastLoadedThreadId.current && (agentSession == null ? void 0 : agentSession.agentName) === lastLoadedAgentName.current) {
      return;
    }
    const fetchMessages = () => __async(this, null, function* () {
      var _a, _b, _c, _d;
      if (!(agentSession == null ? void 0 : agentSession.agentName))
        return;
      const result = yield runtimeClient.loadAgentState({
        threadId,
        agentName: agentSession == null ? void 0 : agentSession.agentName
      });
      const newMessages = (_b = (_a = result.data) == null ? void 0 : _a.loadAgentState) == null ? void 0 : _b.messages;
      if (newMessages === lastLoadedMessages.current)
        return;
      if ((_d = (_c = result.data) == null ? void 0 : _c.loadAgentState) == null ? void 0 : _d.threadExists) {
        lastLoadedMessages.current = newMessages;
        lastLoadedThreadId.current = threadId;
        lastLoadedAgentName.current = agentSession == null ? void 0 : agentSession.agentName;
        const messages2 = (0, import_runtime_client_gql.loadMessagesFromJsonRepresentation)(JSON.parse(newMessages || "[]"));
        setMessages(messages2);
      }
    });
    void fetchMessages();
  }, [threadId, agentSession == null ? void 0 : agentSession.agentName]);
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(
    CopilotMessagesContext.Provider,
    {
      value: {
        messages,
        setMessages
      },
      children
    }
  );
}

// src/components/toast/toast-provider.tsx
var import_react7 = require("react");

// src/components/error-boundary/error-utils.tsx
var import_react6 = require("react");

// src/components/toast/exclamation-mark-icon.tsx
var import_jsx_runtime2 = require("react/jsx-runtime");
var ExclamationMarkIcon = ({
  className,
  style
}) => /* @__PURE__ */ (0, import_jsx_runtime2.jsxs)(
  "svg",
  {
    xmlns: "http://www.w3.org/2000/svg",
    width: "24",
    height: "24",
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: "2",
    strokeLinecap: "round",
    strokeLinejoin: "round",
    className: `lucide lucide-circle-alert ${className ? className : ""}`,
    style,
    children: [
      /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("circle", { cx: "12", cy: "12", r: "10" }),
      /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("line", { x1: "12", x2: "12", y1: "8", y2: "12" }),
      /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("line", { x1: "12", x2: "12.01", y1: "16", y2: "16" })
    ]
  }
);

// src/components/error-boundary/error-utils.tsx
var import_react_markdown = __toESM(require("react-markdown"));
var import_jsx_runtime3 = require("react/jsx-runtime");
function ErrorToast({ errors }) {
  const errorsToRender = errors.map((error, idx) => {
    var _a, _b, _c;
    const originalError = "extensions" in error ? (_a = error.extensions) == null ? void 0 : _a.originalError : {};
    const message = (_b = originalError == null ? void 0 : originalError.message) != null ? _b : error.message;
    const code = "extensions" in error ? (_c = error.extensions) == null ? void 0 : _c.code : null;
    return /* @__PURE__ */ (0, import_jsx_runtime3.jsxs)(
      "div",
      {
        style: {
          marginTop: idx === 0 ? 0 : 10,
          marginBottom: 14
        },
        children: [
          /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(ExclamationMarkIcon, { style: { marginBottom: 4 } }),
          code && /* @__PURE__ */ (0, import_jsx_runtime3.jsxs)(
            "div",
            {
              style: {
                fontWeight: "600",
                marginBottom: 4
              },
              children: [
                "Copilot Cloud Error:",
                " ",
                /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("span", { style: { fontFamily: "monospace", fontWeight: "normal" }, children: code })
              ]
            }
          ),
          /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(import_react_markdown.default, { children: message })
        ]
      },
      idx
    );
  });
  return /* @__PURE__ */ (0, import_jsx_runtime3.jsxs)(
    "div",
    {
      style: {
        fontSize: "13px",
        maxWidth: "600px"
      },
      children: [
        errorsToRender,
        /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("div", { style: { fontSize: "11px", opacity: 0.75 }, children: "NOTE: This error only displays during local development." })
      ]
    }
  );
}
function useErrorToast() {
  const { addToast } = useToast();
  return (0, import_react6.useCallback)(
    (error) => {
      const errorId = error.map((err) => {
        var _a, _b;
        const message = "extensions" in err ? ((_b = (_a = err.extensions) == null ? void 0 : _a.originalError) == null ? void 0 : _b.message) || err.message : err.message;
        const stack = err.stack || "";
        return btoa(message + stack).slice(0, 32);
      }).join("|");
      addToast({
        type: "error",
        id: errorId,
        // Toast libraries typically dedupe by id
        message: /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(ErrorToast, { errors: error })
      });
    },
    [addToast]
  );
}
function useAsyncCallback(callback, deps) {
  const addErrorToast = useErrorToast();
  return (0, import_react6.useCallback)((...args) => __async(this, null, function* () {
    try {
      return yield callback(...args);
    } catch (error) {
      console.error("Error in async callback:", error);
      addErrorToast([error]);
      throw error;
    }
  }), deps);
}

// src/components/toast/toast-provider.tsx
var import_jsx_runtime4 = require("react/jsx-runtime");
var ToastContext = (0, import_react7.createContext)(void 0);
function useToast() {
  const context = (0, import_react7.useContext)(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}
function ToastProvider({
  enabled,
  children
}) {
  const [toasts, setToasts] = (0, import_react7.useState)([]);
  const addToast = (0, import_react7.useCallback)(
    (toast) => {
      var _a;
      if (!enabled) {
        return;
      }
      const id = (_a = toast.id) != null ? _a : Math.random().toString(36).substring(2, 9);
      setToasts((currentToasts) => {
        if (currentToasts.find((toast2) => toast2.id === id))
          return currentToasts;
        return [...currentToasts, __spreadProps(__spreadValues({}, toast), { id })];
      });
      if (toast.duration) {
        setTimeout(() => {
          removeToast(id);
        }, toast.duration);
      }
    },
    [enabled]
  );
  const addGraphQLErrorsToast = (0, import_react7.useCallback)((errors) => {
    addToast({
      type: "error",
      message: /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(ErrorToast, { errors })
    });
  }, []);
  const removeToast = (0, import_react7.useCallback)((id) => {
    setToasts((currentToasts) => currentToasts.filter((toast) => toast.id !== id));
  }, []);
  const value = {
    toasts,
    addToast,
    addGraphQLErrorsToast,
    removeToast,
    enabled
  };
  return /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(ToastContext.Provider, { value, children: [
    /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(
      "div",
      {
        style: {
          position: "fixed",
          bottom: "1rem",
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: 50,
          display: "flex",
          flexDirection: "column",
          gap: "0.5rem"
        },
        children: [
          toasts.length > 1 && /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { style: { textAlign: "right" }, children: /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(
            "button",
            {
              onClick: () => setToasts([]),
              style: {
                padding: "4px 8px",
                fontSize: "12px",
                cursor: "pointer",
                background: "white",
                border: "1px solid rgba(0,0,0,0.2)",
                borderRadius: "4px"
              },
              children: "Close All"
            }
          ) }),
          toasts.map((toast) => /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(
            Toast,
            {
              message: toast.message,
              type: toast.type,
              onClose: () => removeToast(toast.id)
            },
            toast.id
          ))
        ]
      }
    ),
    children
  ] });
}
function Toast({
  message,
  type = "info",
  onClose
}) {
  const bgColors = {
    info: "#3b82f6",
    success: "#22c55e",
    warning: "#eab308",
    error: "#ef4444"
  };
  return /* @__PURE__ */ (0, import_jsx_runtime4.jsxs)(
    "div",
    {
      style: {
        backgroundColor: bgColors[type],
        color: "white",
        padding: "0.5rem 1.5rem",
        borderRadius: "0.25rem",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        position: "relative",
        minWidth: "200px"
      },
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime4.jsx)("div", { children: message }),
        /* @__PURE__ */ (0, import_jsx_runtime4.jsx)(
          "button",
          {
            onClick: onClose,
            style: {
              position: "absolute",
              top: "0",
              right: "0",
              background: "none",
              border: "none",
              color: "white",
              cursor: "pointer",
              padding: "0.5rem",
              fontSize: "1rem"
            },
            children: "\u2715"
          }
        )
      ]
    }
  );
}

// src/hooks/use-copilot-runtime-client.ts
var import_runtime_client_gql2 = require("@copilotkit/runtime-client-gql");
var import_react8 = require("react");
var useCopilotRuntimeClient = (options) => {
  const { addGraphQLErrorsToast } = useToast();
  const addErrorToast = useErrorToast();
  const { addToast } = useToast();
  const runtimeClient = (0, import_react8.useMemo)(() => {
    return new import_runtime_client_gql2.CopilotRuntimeClient(__spreadProps(__spreadValues({}, options), {
      handleGQLErrors: (error) => {
        if (error.graphQLErrors.length) {
          addGraphQLErrorsToast(error.graphQLErrors);
        } else {
          addErrorToast([error]);
        }
      },
      handleGQLWarning: (message) => {
        console.warn(message);
        addToast({ type: "warning", message });
      }
    }));
  }, [options, addGraphQLErrorsToast, addToast]);
  return runtimeClient;
};

// src/utils/extract.ts
var import_shared3 = require("@copilotkit/shared");
var import_runtime_client_gql3 = require("@copilotkit/runtime-client-gql");
var import_runtime_client_gql4 = require("@copilotkit/runtime-client-gql");
function extract(_0) {
  return __async(this, arguments, function* ({
    context,
    instructions,
    parameters,
    include,
    data,
    abortSignal,
    stream,
    requestType = import_runtime_client_gql3.CopilotRequestType.Task,
    forwardedParameters
  }) {
    var _a, _b;
    const { messages } = context;
    const action = {
      name: "extract",
      description: instructions,
      parameters,
      handler: (args) => {
      }
    };
    const includeReadable = (_a = include == null ? void 0 : include.readable) != null ? _a : false;
    const includeMessages = (_b = include == null ? void 0 : include.messages) != null ? _b : false;
    let contextString = "";
    if (data) {
      contextString = (typeof data === "string" ? data : JSON.stringify(data)) + "\n\n";
    }
    if (includeReadable) {
      contextString += context.getContextString([], defaultCopilotContextCategories);
    }
    const systemMessage = new import_runtime_client_gql3.TextMessage({
      content: makeSystemMessage(contextString, instructions),
      role: import_runtime_client_gql3.Role.System
    });
    const instructionsMessage = new import_runtime_client_gql3.TextMessage({
      content: makeInstructionsMessage(instructions),
      role: import_runtime_client_gql3.Role.User
    });
    const response = context.runtimeClient.asStream(
      context.runtimeClient.generateCopilotResponse({
        data: {
          frontend: {
            actions: [
              {
                name: action.name,
                description: action.description || "",
                jsonSchema: JSON.stringify((0, import_shared3.actionParametersToJsonSchema)(action.parameters || []))
              }
            ],
            url: window.location.href
          },
          messages: (0, import_runtime_client_gql4.convertMessagesToGqlInput)(
            includeMessages ? [systemMessage, instructionsMessage, ...(0, import_runtime_client_gql4.filterAgentStateMessages)(messages)] : [systemMessage, instructionsMessage]
          ),
          metadata: {
            requestType
          },
          forwardedParameters: __spreadProps(__spreadValues({}, forwardedParameters != null ? forwardedParameters : {}), {
            toolChoice: "function",
            toolChoiceFunctionName: action.name
          })
        },
        properties: context.copilotApiConfig.properties,
        signal: abortSignal
      })
    );
    const reader = response.getReader();
    let isInitial = true;
    let actionExecutionMessage = void 0;
    while (true) {
      const { done, value } = yield reader.read();
      if (done) {
        break;
      }
      if (abortSignal == null ? void 0 : abortSignal.aborted) {
        throw new Error("Aborted");
      }
      actionExecutionMessage = (0, import_runtime_client_gql3.convertGqlOutputToMessages)(
        value.generateCopilotResponse.messages
      ).find((msg) => msg.isActionExecutionMessage());
      if (!actionExecutionMessage) {
        continue;
      }
      stream == null ? void 0 : stream({
        status: isInitial ? "initial" : "inProgress",
        args: actionExecutionMessage.arguments
      });
      isInitial = false;
    }
    if (!actionExecutionMessage) {
      throw new Error("extract() failed: No function call occurred");
    }
    stream == null ? void 0 : stream({
      status: "complete",
      args: actionExecutionMessage.arguments
    });
    return actionExecutionMessage.arguments;
  });
}
function makeInstructionsMessage(instructions) {
  return `
The user has given you the following task to complete:

\`\`\`
${instructions}
\`\`\`

Any additional messages provided are for providing context only and should not be used to ask questions or engage in conversation.
`;
}
function makeSystemMessage(contextString, instructions) {
  return `
Please act as an efficient, competent, conscientious, and industrious professional assistant.

Help the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.
Always be polite and respectful, and prefer brevity over verbosity.

The user has provided you with the following context:
\`\`\`
${contextString}
\`\`\`

They have also provided you with a function called extract you MUST call to initiate actions on their behalf.

Please assist them as best you can.

This is not a conversation, so please do not ask questions. Just call the function without saying anything else.
`;
}

// src/utils/dev-console.ts
function shouldShowDevConsole(showDevConsole) {
  if (typeof showDevConsole === "boolean") {
    return showDevConsole;
  }
  return getHostname() === "localhost" || getHostname() === "127.0.0.1" || getHostname() === "0.0.0.0" || getHostname() === "::1";
}
function getHostname() {
  if (typeof window !== "undefined" && window.location) {
    return window.location.hostname;
  }
  return "";
}

// src/components/error-boundary/error-boundary.tsx
var import_react9 = __toESM(require("react"));
var import_shared6 = require("@copilotkit/shared");

// src/lib/status-checker.ts
var import_shared4 = require("@copilotkit/shared");
var STATUS_CHECK_INTERVAL = 1e3 * 60 * 5;
var StatusChecker = class {
  constructor() {
    this.activeKey = null;
    this.intervalId = null;
    this.instanceCount = 0;
    this.lastResponse = null;
  }
  start(publicApiKey, onUpdate) {
    return __async(this, null, function* () {
      this.instanceCount++;
      if (this.activeKey === publicApiKey)
        return;
      if (this.intervalId)
        clearInterval(this.intervalId);
      const checkStatus = () => __async(this, null, function* () {
        try {
          const response = yield fetch(`${import_shared4.COPILOT_CLOUD_API_URL}/ciu`, {
            method: "GET",
            headers: {
              [import_shared4.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey
            }
          }).then((response2) => response2.json());
          this.lastResponse = response;
          onUpdate == null ? void 0 : onUpdate(response);
          return response;
        } catch (error) {
          return null;
        }
      });
      const initialResponse = yield checkStatus();
      this.intervalId = setInterval(checkStatus, STATUS_CHECK_INTERVAL);
      this.activeKey = publicApiKey;
      return initialResponse;
    });
  }
  getLastResponse() {
    return this.lastResponse;
  }
  stop() {
    this.instanceCount--;
    if (this.instanceCount === 0) {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        this.activeKey = null;
        this.lastResponse = null;
      }
    }
  }
};

// src/components/usage-banner.tsx
var import_shared5 = require("@copilotkit/shared");
var import_jsx_runtime5 = require("react/jsx-runtime");
var defaultIcons = {
  [import_shared5.Severity.Error]: /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)(
    "svg",
    {
      viewBox: "0 0 24 24",
      width: "20",
      height: "20",
      stroke: "currentColor",
      strokeWidth: "2",
      fill: "none",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      children: [
        /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("circle", { cx: "12", cy: "12", r: "10" }),
        /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("line", { x1: "15", y1: "9", x2: "9", y2: "15" }),
        /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("line", { x1: "9", y1: "9", x2: "15", y2: "15" })
      ]
    }
  )
};
function UsageBanner({
  severity = import_shared5.Severity.Error,
  message = "",
  icon,
  actions
}) {
  if (!message || !severity) {
    return null;
  }
  const Icon = icon || defaultIcons[severity];
  const bgColor = {
    info: "#dbeafe",
    warning: "#fef3c7",
    error: "#fee2e2"
  }[severity];
  const textColor = {
    info: "#1e40af",
    warning: "#854d0e",
    error: "#991b1b"
  }[severity];
  const iconColor = {
    info: "#3b82f6",
    warning: "#eab308",
    error: "#ef4444"
  }[severity];
  const primaryButtonColor = {
    info: "#3b82f6",
    warning: "#eab308",
    error: "#ef4444"
  }[severity];
  const primaryButtonHoverColor = {
    info: "#2563eb",
    warning: "#ca8a04",
    error: "#dc2626"
  }[severity];
  return /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
    "div",
    {
      style: {
        position: "fixed",
        bottom: "16px",
        left: "50%",
        transform: "translateX(-50%)",
        maxWidth: "90%",
        zIndex: 9999
      },
      children: /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)(
        "div",
        {
          style: {
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            gap: "12px",
            borderRadius: "9999px",
            border: "1px solid #e5e7eb",
            backgroundColor: bgColor,
            padding: "8px 16px",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
          },
          children: [
            /* @__PURE__ */ (0, import_jsx_runtime5.jsx)("div", { style: { color: iconColor }, children: Icon }),
            /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
              "span",
              {
                style: {
                  flex: 1,
                  fontSize: "14px",
                  fontWeight: 500,
                  color: textColor,
                  whiteSpace: "normal",
                  wordBreak: "break-word"
                },
                children: message
              }
            ),
            /* @__PURE__ */ (0, import_jsx_runtime5.jsxs)(
              "div",
              {
                style: {
                  display: "flex",
                  gap: "8px",
                  flexWrap: "wrap"
                },
                children: [
                  (actions == null ? void 0 : actions.secondary) && /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
                    "button",
                    {
                      onClick: actions.secondary.onClick,
                      style: {
                        borderRadius: "9999px",
                        padding: "4px 12px",
                        fontSize: "14px",
                        fontWeight: 500,
                        color: textColor,
                        backgroundColor: "transparent",
                        border: "none",
                        cursor: "pointer",
                        transition: "background-color 0.2s"
                      },
                      onMouseOver: (e) => e.currentTarget.style.backgroundColor = "rgba(255,255,255,0.5)",
                      onMouseOut: (e) => e.currentTarget.style.backgroundColor = "transparent",
                      children: actions.secondary.label
                    }
                  ),
                  (actions == null ? void 0 : actions.primary) && /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
                    "button",
                    {
                      onClick: actions.primary.onClick,
                      style: {
                        borderRadius: "9999px",
                        padding: "4px 12px",
                        fontSize: "14px",
                        fontWeight: 500,
                        color: "#fff",
                        backgroundColor: primaryButtonColor,
                        border: "none",
                        cursor: "pointer",
                        transition: "background-color 0.2s"
                      },
                      onMouseOver: (e) => e.currentTarget.style.backgroundColor = primaryButtonHoverColor,
                      onMouseOut: (e) => e.currentTarget.style.backgroundColor = primaryButtonColor,
                      children: actions.primary.label
                    }
                  )
                ]
              }
            )
          ]
        }
      )
    }
  );
}
function renderCopilotKitUsage(error) {
  switch (error.name) {
    case import_shared5.ERROR_NAMES.CONFIGURATION_ERROR:
      return /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(UsageBanner, { severity: error.severity, message: error.message });
    case import_shared5.ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR:
      return /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
        UsageBanner,
        {
          severity: error.severity,
          message: error.message,
          actions: {
            primary: {
              label: "Sign In",
              onClick: () => {
                window.location.href = "https://cloud.copilotkit.ai";
              }
            }
          }
        }
      );
    case import_shared5.ERROR_NAMES.UPGRADE_REQUIRED_ERROR:
      return /* @__PURE__ */ (0, import_jsx_runtime5.jsx)(
        UsageBanner,
        {
          severity: error.severity,
          message: error.message,
          actions: {
            primary: {
              label: "Upgrade",
              onClick: () => {
                window.location.href = "https://copilotkit.ai/";
              }
            }
          }
        }
      );
  }
}

// src/components/error-boundary/error-boundary.tsx
var import_shared7 = require("@copilotkit/shared");
var import_jsx_runtime6 = require("react/jsx-runtime");
var statusChecker = new StatusChecker();
var CopilotErrorBoundary = class extends import_react9.default.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false
    };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  componentDidMount() {
    if (this.props.publicApiKey) {
      statusChecker.start(this.props.publicApiKey, (newStatus) => {
        this.setState((prevState) => {
          var _a;
          if ((newStatus == null ? void 0 : newStatus.severity) !== ((_a = prevState.status) == null ? void 0 : _a.severity)) {
            return { status: newStatus != null ? newStatus : void 0 };
          }
          return null;
        });
      });
    }
  }
  componentWillUnmount() {
    statusChecker.stop();
  }
  componentDidCatch(error, errorInfo) {
    console.error("CopilotKit Error:", error, errorInfo);
  }
  render() {
    var _a, _b;
    if (this.state.hasError) {
      if (this.state.error instanceof import_shared6.CopilotKitError) {
        if (import_shared7.COPILOT_CLOUD_ERROR_NAMES.includes(this.state.error.name)) {
          return /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(ErrorToast2, { error: this.state.error, children: renderCopilotKitUsage(this.state.error) });
        }
        return /* @__PURE__ */ (0, import_jsx_runtime6.jsxs)(import_jsx_runtime6.Fragment, { children: [
          this.props.children,
          this.props.showUsageBanner && /* @__PURE__ */ (0, import_jsx_runtime6.jsx)(
            UsageBanner,
            {
              severity: (_a = this.state.status) == null ? void 0 : _a.severity,
              message: (_b = this.state.status) == null ? void 0 : _b.message
            }
          )
        ] });
      }
      throw this.state.error;
    }
    return this.props.children;
  }
};
function ErrorToast2({ error, children }) {
  const addErrorToast = useErrorToast();
  (0, import_react9.useEffect)(() => {
    if (error) {
      addErrorToast([error]);
    }
  }, [error, addErrorToast]);
  if (!error)
    throw error;
  return children;
}

// src/components/copilot-provider/copilotkit.tsx
var import_jsx_runtime7 = require("react/jsx-runtime");
function CopilotKit(_a) {
  var _b = _a, { children } = _b, props = __objRest(_b, ["children"]);
  const showDevConsole = props.showDevConsole === void 0 ? "auto" : props.showDevConsole;
  const enabled = shouldShowDevConsole(showDevConsole);
  return /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(ToastProvider, { enabled, children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(CopilotErrorBoundary, { publicApiKey: props.publicApiKey, showUsageBanner: enabled, children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(CopilotKitInternal, __spreadProps(__spreadValues({}, props), { children })) }) });
}
function CopilotKitInternal(cpkProps) {
  const _a = cpkProps, { children } = _a, props = __objRest(_a, ["children"]);
  validateProps(cpkProps);
  const chatApiEndpoint = props.runtimeUrl || import_shared8.COPILOT_CLOUD_CHAT_URL;
  const [actions, setActions] = (0, import_react10.useState)({});
  const [coAgentStateRenders, setCoAgentStateRenders] = (0, import_react10.useState)({});
  const chatComponentsCache = (0, import_react10.useRef)({
    actions: {},
    coAgentStateRenders: {}
  });
  const { addElement, removeElement, printTree } = use_tree_default();
  const [isLoading, setIsLoading] = (0, import_react10.useState)(false);
  const [chatInstructions, setChatInstructions] = (0, import_react10.useState)("");
  const [authStates, setAuthStates] = (0, import_react10.useState)({});
  const [extensions, setExtensions] = (0, import_react10.useState)({});
  const [additionalInstructions, setAdditionalInstructions] = (0, import_react10.useState)([]);
  const {
    addElement: addDocument,
    removeElement: removeDocument,
    allElements: allDocuments
  } = use_flat_category_store_default();
  const setAction = (0, import_react10.useCallback)((id, action) => {
    setActions((prevPoints) => {
      return __spreadProps(__spreadValues({}, prevPoints), {
        [id]: action
      });
    });
  }, []);
  const removeAction = (0, import_react10.useCallback)((id) => {
    setActions((prevPoints) => {
      const newPoints = __spreadValues({}, prevPoints);
      delete newPoints[id];
      return newPoints;
    });
  }, []);
  const setCoAgentStateRender = (0, import_react10.useCallback)((id, stateRender) => {
    setCoAgentStateRenders((prevPoints) => {
      return __spreadProps(__spreadValues({}, prevPoints), {
        [id]: stateRender
      });
    });
  }, []);
  const removeCoAgentStateRender = (0, import_react10.useCallback)((id) => {
    setCoAgentStateRenders((prevPoints) => {
      const newPoints = __spreadValues({}, prevPoints);
      delete newPoints[id];
      return newPoints;
    });
  }, []);
  const getContextString = (0, import_react10.useCallback)(
    (documents, categories) => {
      const documentsString = documents.map((document) => {
        return `${document.name} (${document.sourceApplication}):
${document.getContents()}`;
      }).join("\n\n");
      const nonDocumentStrings = printTree(categories);
      return `${documentsString}

${nonDocumentStrings}`;
    },
    [printTree]
  );
  const addContext = (0, import_react10.useCallback)(
    (context, parentId, categories = defaultCopilotContextCategories) => {
      return addElement(context, categories, parentId);
    },
    [addElement]
  );
  const removeContext = (0, import_react10.useCallback)(
    (id) => {
      removeElement(id);
    },
    [removeElement]
  );
  const getFunctionCallHandler = (0, import_react10.useCallback)(
    (customEntryPoints) => {
      return entryPointsToFunctionCallHandler(Object.values(customEntryPoints || actions));
    },
    [actions]
  );
  const getDocumentsContext = (0, import_react10.useCallback)(
    (categories) => {
      return allDocuments(categories);
    },
    [allDocuments]
  );
  const addDocumentContext = (0, import_react10.useCallback)(
    (documentPointer, categories = defaultCopilotContextCategories) => {
      return addDocument(documentPointer, categories);
    },
    [addDocument]
  );
  const removeDocumentContext = (0, import_react10.useCallback)(
    (documentId) => {
      removeDocument(documentId);
    },
    [removeDocument]
  );
  const copilotApiConfig = (0, import_react10.useMemo)(() => {
    var _a2, _b;
    let cloud = void 0;
    if (props.publicApiKey) {
      cloud = {
        guardrails: {
          input: {
            restrictToTopic: {
              enabled: Boolean(props.guardrails_c),
              validTopics: ((_a2 = props.guardrails_c) == null ? void 0 : _a2.validTopics) || [],
              invalidTopics: ((_b = props.guardrails_c) == null ? void 0 : _b.invalidTopics) || []
            }
          }
        }
      };
    }
    return __spreadProps(__spreadValues({
      publicApiKey: props.publicApiKey
    }, cloud ? { cloud } : {}), {
      chatApiEndpoint,
      headers: props.headers || {},
      properties: props.properties || {},
      transcribeAudioUrl: props.transcribeAudioUrl,
      textToSpeechUrl: props.textToSpeechUrl,
      credentials: props.credentials
    });
  }, [
    props.publicApiKey,
    props.headers,
    props.properties,
    props.transcribeAudioUrl,
    props.textToSpeechUrl,
    props.credentials,
    props.cloudRestrictToTopic,
    props.guardrails_c
  ]);
  const headers = (0, import_react10.useMemo)(() => {
    const authHeaders = Object.values(authStates || {}).reduce((acc, state) => {
      if (state.status === "authenticated" && state.authHeaders) {
        return __spreadValues(__spreadValues({}, acc), Object.entries(state.authHeaders).reduce(
          (headers2, [key, value]) => __spreadProps(__spreadValues({}, headers2), {
            [key.startsWith("X-Custom-") ? key : `X-Custom-${key}`]: value
          }),
          {}
        ));
      }
      return acc;
    }, {});
    return __spreadValues(__spreadValues(__spreadValues({}, copilotApiConfig.headers || {}), copilotApiConfig.publicApiKey ? { [import_shared8.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: copilotApiConfig.publicApiKey } : {}), authHeaders);
  }, [copilotApiConfig.headers, copilotApiConfig.publicApiKey, authStates]);
  const runtimeClient = useCopilotRuntimeClient({
    url: copilotApiConfig.chatApiEndpoint,
    publicApiKey: copilotApiConfig.publicApiKey,
    headers,
    credentials: copilotApiConfig.credentials
  });
  const [chatSuggestionConfiguration, setChatSuggestionConfiguration] = (0, import_react10.useState)({});
  const addChatSuggestionConfiguration = (id, suggestion) => {
    setChatSuggestionConfiguration((prev) => __spreadProps(__spreadValues({}, prev), { [id]: suggestion }));
  };
  const removeChatSuggestionConfiguration = (id) => {
    setChatSuggestionConfiguration((prev) => {
      const _a2 = prev, { [id]: _ } = _a2, rest = __objRest(_a2, [__restKey(id)]);
      return rest;
    });
  };
  const [availableAgents, setAvailableAgents] = (0, import_react10.useState)([]);
  const [coagentStates, setCoagentStates] = (0, import_react10.useState)({});
  const coagentStatesRef = (0, import_react10.useRef)({});
  const setCoagentStatesWithRef = (0, import_react10.useCallback)(
    (value) => {
      const newValue = typeof value === "function" ? value(coagentStatesRef.current) : value;
      coagentStatesRef.current = newValue;
      setCoagentStates((prev) => {
        return newValue;
      });
    },
    []
  );
  const hasLoadedAgents = (0, import_react10.useRef)(false);
  (0, import_react10.useEffect)(() => {
    if (hasLoadedAgents.current)
      return;
    const fetchData = () => __async(this, null, function* () {
      var _a2;
      const result = yield runtimeClient.availableAgents();
      if ((_a2 = result.data) == null ? void 0 : _a2.availableAgents) {
        setAvailableAgents(result.data.availableAgents.agents);
      }
      hasLoadedAgents.current = true;
    });
    void fetchData();
  }, []);
  let initialAgentSession = null;
  if (props.agent) {
    initialAgentSession = {
      agentName: props.agent
    };
  }
  const [agentSession, setAgentSession] = (0, import_react10.useState)(initialAgentSession);
  (0, import_react10.useEffect)(() => {
    if (props.agent) {
      setAgentSession({
        agentName: props.agent
      });
    } else {
      setAgentSession(null);
    }
  }, [props.agent]);
  const [internalThreadId, setInternalThreadId] = (0, import_react10.useState)(props.threadId || (0, import_shared8.randomUUID)());
  const setThreadId = (0, import_react10.useCallback)(
    (value) => {
      if (props.threadId) {
        throw new Error("Cannot call setThreadId() when threadId is provided via props.");
      }
      setInternalThreadId(value);
    },
    [props.threadId]
  );
  (0, import_react10.useEffect)(() => {
    if (props.threadId !== void 0) {
      setInternalThreadId(props.threadId);
    }
  }, [props.threadId]);
  const [runId, setRunId] = (0, import_react10.useState)(null);
  const chatAbortControllerRef = (0, import_react10.useRef)(null);
  const showDevConsole = props.showDevConsole === void 0 ? "auto" : props.showDevConsole;
  const [langGraphInterruptAction, _setLangGraphInterruptAction] = (0, import_react10.useState)(null);
  const setLangGraphInterruptAction = (0, import_react10.useCallback)((action) => {
    _setLangGraphInterruptAction((prev) => {
      if (prev == null)
        return action;
      if (action == null)
        return null;
      let event = prev.event;
      if (action.event) {
        event = __spreadValues(__spreadValues({}, prev.event), action.event);
      }
      return __spreadProps(__spreadValues(__spreadValues({}, prev), action), { event });
    });
  }, []);
  const removeLangGraphInterruptAction = (0, import_react10.useCallback)(() => {
    setLangGraphInterruptAction(null);
  }, []);
  return /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(
    CopilotContext.Provider,
    {
      value: {
        actions,
        chatComponentsCache,
        getFunctionCallHandler,
        setAction,
        removeAction,
        coAgentStateRenders,
        setCoAgentStateRender,
        removeCoAgentStateRender,
        getContextString,
        addContext,
        removeContext,
        getDocumentsContext,
        addDocumentContext,
        removeDocumentContext,
        copilotApiConfig,
        isLoading,
        setIsLoading,
        chatSuggestionConfiguration,
        addChatSuggestionConfiguration,
        removeChatSuggestionConfiguration,
        chatInstructions,
        setChatInstructions,
        additionalInstructions,
        setAdditionalInstructions,
        showDevConsole,
        coagentStates,
        setCoagentStates,
        coagentStatesRef,
        setCoagentStatesWithRef,
        agentSession,
        setAgentSession,
        runtimeClient,
        forwardedParameters: props.forwardedParameters || {},
        agentLock: props.agent || null,
        threadId: internalThreadId,
        setThreadId,
        runId,
        setRunId,
        chatAbortControllerRef,
        availableAgents,
        authConfig_c: props.authConfig_c,
        authStates_c: authStates,
        setAuthStates_c: setAuthStates,
        extensions,
        setExtensions,
        langGraphInterruptAction,
        setLangGraphInterruptAction,
        removeLangGraphInterruptAction
      },
      children: /* @__PURE__ */ (0, import_jsx_runtime7.jsx)(CopilotMessages, { children })
    }
  );
}
var defaultCopilotContextCategories = ["global"];
function entryPointsToFunctionCallHandler(actions) {
  return (_0) => __async(this, [_0], function* ({ name, args }) {
    let actionsByFunctionName = {};
    for (let action2 of actions) {
      actionsByFunctionName[action2.name] = action2;
    }
    const action = actionsByFunctionName[name];
    let result = void 0;
    if (action) {
      yield new Promise((resolve, reject) => {
        (0, import_react_dom.flushSync)(() => __async(this, null, function* () {
          var _a;
          try {
            result = yield (_a = action.handler) == null ? void 0 : _a.call(action, args);
            resolve();
          } catch (error) {
            reject(error);
          }
        }));
      });
      yield new Promise((resolve) => setTimeout(resolve, 20));
    }
    return result;
  });
}
function formatFeatureName(featureName) {
  return featureName.replace(/_c$/, "").split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(" ");
}
function validateProps(props) {
  const cloudFeatures = Object.keys(props).filter((key) => key.endsWith("_c"));
  if (!props.runtimeUrl && !props.publicApiKey) {
    throw new import_shared8.ConfigurationError("Missing required prop: 'runtimeUrl' or 'publicApiKey'");
  }
  if (cloudFeatures.length > 0 && !props.publicApiKey) {
    throw new import_shared8.MissingPublicApiKeyError(
      `Missing required prop: 'publicApiKey' to use cloud features: ${cloudFeatures.map(formatFeatureName).join(", ")}`
    );
  }
}

// src/hooks/use-copilot-chat.ts
var import_react12 = require("react");
var import_runtime_client_gql7 = require("@copilotkit/runtime-client-gql");

// src/hooks/use-chat.ts
var import_react11 = require("react");
var import_shared10 = require("@copilotkit/shared");
var import_runtime_client_gql6 = require("@copilotkit/runtime-client-gql");

// src/types/frontend-action.ts
var import_runtime_client_gql5 = require("@copilotkit/runtime-client-gql");
var import_shared9 = require("@copilotkit/shared");
function processActionsForRuntimeRequest(actions) {
  const filteredActions = actions.filter(
    (action) => action.available !== import_runtime_client_gql5.ActionInputAvailability.Disabled && action.disabled !== true && action.name !== "*" && action.available != "frontend" && !action.pairedAction
  ).map((action) => {
    let available = import_runtime_client_gql5.ActionInputAvailability.Enabled;
    if (action.disabled) {
      available = import_runtime_client_gql5.ActionInputAvailability.Disabled;
    } else if (action.available === "disabled") {
      available = import_runtime_client_gql5.ActionInputAvailability.Disabled;
    } else if (action.available === "remote") {
      available = import_runtime_client_gql5.ActionInputAvailability.Remote;
    }
    return {
      name: action.name,
      description: action.description || "",
      jsonSchema: JSON.stringify((0, import_shared9.actionParametersToJsonSchema)(action.parameters || [])),
      available
    };
  });
  return filteredActions;
}

// src/hooks/use-chat.ts
function useChat(options) {
  const {
    messages,
    setMessages,
    makeSystemMessageCallback,
    copilotConfig,
    setIsLoading,
    initialMessages,
    isLoading,
    actions,
    onFunctionCall,
    onCoAgentStateRender,
    setCoagentStatesWithRef,
    coagentStatesRef,
    agentSession,
    setAgentSession,
    threadId,
    setThreadId,
    runId,
    setRunId,
    chatAbortControllerRef,
    agentLock,
    extensions,
    setExtensions,
    langGraphInterruptAction,
    setLangGraphInterruptAction
  } = options;
  const runChatCompletionRef = (0, import_react11.useRef)();
  const addErrorToast = useErrorToast();
  const agentSessionRef = (0, import_react11.useRef)(agentSession);
  agentSessionRef.current = agentSession;
  const runIdRef = (0, import_react11.useRef)(runId);
  runIdRef.current = runId;
  const extensionsRef = (0, import_react11.useRef)(extensions);
  extensionsRef.current = extensions;
  const publicApiKey = copilotConfig.publicApiKey;
  const headers = __spreadValues(__spreadValues({}, copilotConfig.headers || {}), publicApiKey ? { [import_shared10.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey } : {});
  const runtimeClient = useCopilotRuntimeClient({
    url: copilotConfig.chatApiEndpoint,
    publicApiKey: copilotConfig.publicApiKey,
    headers,
    credentials: copilotConfig.credentials
  });
  const runChatCompletion = useAsyncCallback(
    (previousMessages) => __async(this, null, function* () {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o;
      setIsLoading(true);
      const interruptEvent = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event;
      if ((interruptEvent == null ? void 0 : interruptEvent.name) === import_runtime_client_gql6.MetaEventName.LangGraphInterruptEvent && (interruptEvent == null ? void 0 : interruptEvent.value) && !(interruptEvent == null ? void 0 : interruptEvent.response) && agentSessionRef.current) {
        addErrorToast([
          new Error(
            "A message was sent while interrupt is active. This will cause failure on the agent side"
          )
        ]);
      }
      let newMessages = [
        new import_runtime_client_gql6.TextMessage({
          content: "",
          role: import_runtime_client_gql6.Role.Assistant
        })
      ];
      chatAbortControllerRef.current = new AbortController();
      setMessages([...previousMessages, ...newMessages]);
      const systemMessage = makeSystemMessageCallback();
      const messagesWithContext = [systemMessage, ...initialMessages || [], ...previousMessages];
      const finalProperties = __spreadValues({}, copilotConfig.properties || {});
      let mcpServersToUse = null;
      if (copilotConfig.mcpServers && Array.isArray(copilotConfig.mcpServers) && copilotConfig.mcpServers.length > 0) {
        mcpServersToUse = copilotConfig.mcpServers;
      } else if (((_a = copilotConfig.properties) == null ? void 0 : _a.mcpServers) && Array.isArray(copilotConfig.properties.mcpServers) && copilotConfig.properties.mcpServers.length > 0) {
        mcpServersToUse = copilotConfig.properties.mcpServers;
      }
      if (mcpServersToUse) {
        finalProperties.mcpServers = mcpServersToUse;
        copilotConfig.mcpServers = mcpServersToUse;
      }
      const isAgentRun = agentSessionRef.current !== null;
      const stream = runtimeClient.asStream(
        runtimeClient.generateCopilotResponse({
          data: __spreadProps(__spreadValues(__spreadProps(__spreadValues({
            frontend: {
              actions: processActionsForRuntimeRequest(actions),
              url: window.location.href
            },
            threadId,
            runId: runIdRef.current,
            extensions: extensionsRef.current,
            metaEvents: composeAndFlushMetaEventsInput([langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event]),
            messages: (0, import_runtime_client_gql6.convertMessagesToGqlInput)((0, import_runtime_client_gql6.filterAgentStateMessages)(messagesWithContext))
          }, copilotConfig.cloud ? {
            cloud: __spreadValues({}, ((_d = (_c = (_b = copilotConfig.cloud.guardrails) == null ? void 0 : _b.input) == null ? void 0 : _c.restrictToTopic) == null ? void 0 : _d.enabled) ? {
              guardrails: {
                inputValidationRules: {
                  allowList: copilotConfig.cloud.guardrails.input.restrictToTopic.validTopics,
                  denyList: copilotConfig.cloud.guardrails.input.restrictToTopic.invalidTopics
                }
              }
            } : {})
          } : {}), {
            metadata: {
              requestType: import_runtime_client_gql6.CopilotRequestType.Chat
            }
          }), agentSessionRef.current ? {
            agentSession: agentSessionRef.current
          } : {}), {
            agentStates: Object.values(coagentStatesRef.current).map((state) => {
              const stateObject = {
                agentName: state.name,
                state: JSON.stringify(state.state)
              };
              if (state.config !== void 0) {
                stateObject.config = JSON.stringify(state.config);
              }
              return stateObject;
            }),
            forwardedParameters: options.forwardedParameters || {}
          }),
          properties: finalProperties,
          signal: (_e = chatAbortControllerRef.current) == null ? void 0 : _e.signal
        })
      );
      const guardrailsEnabled = ((_h = (_g = (_f = copilotConfig.cloud) == null ? void 0 : _f.guardrails) == null ? void 0 : _g.input) == null ? void 0 : _h.restrictToTopic.enabled) || false;
      const reader = stream.getReader();
      let executedCoAgentStateRenders = [];
      let followUp = void 0;
      let messages2 = [];
      let syncedMessages = [];
      let interruptMessages = [];
      try {
        while (true) {
          let done, value;
          try {
            const readResult = yield reader.read();
            done = readResult.done;
            value = readResult.value;
          } catch (readError) {
            break;
          }
          if (done) {
            if (chatAbortControllerRef.current.signal.aborted) {
              return [];
            }
            break;
          }
          if (!(value == null ? void 0 : value.generateCopilotResponse)) {
            continue;
          }
          runIdRef.current = value.generateCopilotResponse.runId || null;
          extensionsRef.current = import_runtime_client_gql6.CopilotRuntimeClient.removeGraphQLTypename(
            value.generateCopilotResponse.extensions || {}
          );
          setRunId(runIdRef.current);
          setExtensions(extensionsRef.current);
          let rawMessagesResponse = value.generateCopilotResponse.messages;
          const metaEvents = (_j = (_i = value.generateCopilotResponse) == null ? void 0 : _i.metaEvents) != null ? _j : [];
          (metaEvents != null ? metaEvents : []).forEach((ev) => {
            if (ev.name === import_runtime_client_gql6.MetaEventName.LangGraphInterruptEvent) {
              let eventValue = (0, import_runtime_client_gql6.langGraphInterruptEvent)(ev).value;
              eventValue = (0, import_shared10.parseJson)(eventValue, eventValue);
              setLangGraphInterruptAction({
                event: __spreadProps(__spreadValues({}, (0, import_runtime_client_gql6.langGraphInterruptEvent)(ev)), {
                  value: eventValue
                })
              });
            }
            if (ev.name === import_runtime_client_gql6.MetaEventName.CopilotKitLangGraphInterruptEvent) {
              const data = ev.data;
              rawMessagesResponse = [...rawMessagesResponse, ...data.messages];
              interruptMessages = (0, import_runtime_client_gql6.convertGqlOutputToMessages)(
                // @ts-ignore
                (0, import_runtime_client_gql6.filterAdjacentAgentStateMessages)(data.messages)
              );
            }
          });
          messages2 = (0, import_runtime_client_gql6.convertGqlOutputToMessages)(
            (0, import_runtime_client_gql6.filterAdjacentAgentStateMessages)(rawMessagesResponse)
          );
          if (messages2.length === 0) {
            continue;
          }
          newMessages = [];
          if (((_k = value.generateCopilotResponse.status) == null ? void 0 : _k.__typename) === "FailedResponseStatus" && value.generateCopilotResponse.status.reason === "GUARDRAILS_VALIDATION_FAILED") {
            newMessages = [
              new import_runtime_client_gql6.TextMessage({
                role: import_runtime_client_gql6.MessageRole.Assistant,
                content: ((_l = value.generateCopilotResponse.status.details) == null ? void 0 : _l.guardrailsReason) || ""
              })
            ];
            setMessages([...previousMessages, ...newMessages]);
            break;
          } else {
            newMessages = [...messages2];
            for (const message of messages2) {
              if (message.isAgentStateMessage() && !message.active && !executedCoAgentStateRenders.includes(message.id) && onCoAgentStateRender) {
                if (guardrailsEnabled && value.generateCopilotResponse.status === void 0) {
                  break;
                }
                yield onCoAgentStateRender({
                  name: message.agentName,
                  nodeName: message.nodeName,
                  state: message.state
                });
                executedCoAgentStateRenders.push(message.id);
              }
            }
            const lastAgentStateMessage = [...messages2].reverse().find((message) => message.isAgentStateMessage());
            if (lastAgentStateMessage) {
              if (lastAgentStateMessage.state.messages && lastAgentStateMessage.state.messages.length > 0) {
                syncedMessages = (0, import_runtime_client_gql6.loadMessagesFromJsonRepresentation)(
                  lastAgentStateMessage.state.messages
                );
              }
              setCoagentStatesWithRef((prevAgentStates) => __spreadProps(__spreadValues({}, prevAgentStates), {
                [lastAgentStateMessage.agentName]: {
                  name: lastAgentStateMessage.agentName,
                  state: lastAgentStateMessage.state,
                  running: lastAgentStateMessage.running,
                  active: lastAgentStateMessage.active,
                  threadId: lastAgentStateMessage.threadId,
                  nodeName: lastAgentStateMessage.nodeName,
                  runId: lastAgentStateMessage.runId
                }
              }));
              if (lastAgentStateMessage.running) {
                setAgentSession({
                  threadId: lastAgentStateMessage.threadId,
                  agentName: lastAgentStateMessage.agentName,
                  nodeName: lastAgentStateMessage.nodeName
                });
              } else {
                if (agentLock) {
                  setAgentSession({
                    threadId: (0, import_shared10.randomId)(),
                    agentName: agentLock,
                    nodeName: void 0
                  });
                } else {
                  setAgentSession(null);
                }
              }
            }
          }
          if (newMessages.length > 0) {
            setMessages([...previousMessages, ...newMessages]);
          }
        }
        let finalMessages = constructFinalMessages(
          [...syncedMessages, ...interruptMessages],
          previousMessages,
          newMessages
        );
        let didExecuteAction = false;
        if (onFunctionCall) {
          const lastMessages = [];
          for (let i = finalMessages.length - 1; i >= 0; i--) {
            const message = finalMessages[i];
            if ((message.isActionExecutionMessage() || message.isResultMessage()) && message.status.code !== import_runtime_client_gql6.MessageStatusCode.Pending) {
              lastMessages.unshift(message);
            } else if (!message.isAgentStateMessage()) {
              break;
            }
          }
          for (const message of lastMessages) {
            setMessages(finalMessages);
            const action = actions.find(
              (action2) => action2.name === message.name
            );
            const currentResultMessagePairedFeAction = message.isResultMessage() ? getPairedFeAction(actions, message) : null;
            const executeActionFromMessage = (action2, message2) => __async(this, null, function* () {
              var _a2;
              const isInterruptAction = interruptMessages.find((m) => m.id === message2.id);
              followUp = (_a2 = action2 == null ? void 0 : action2.followUp) != null ? _a2 : !isInterruptAction;
              const resultMessage = yield executeAction({
                onFunctionCall,
                previousMessages,
                message: message2,
                chatAbortControllerRef,
                onError: (error) => {
                  addErrorToast([error]);
                  console.error(`Failed to execute action ${message2.name}: ${error}`);
                }
              });
              didExecuteAction = true;
              const messageIndex = finalMessages.findIndex((msg) => msg.id === message2.id);
              finalMessages.splice(messageIndex + 1, 0, resultMessage);
              return resultMessage;
            });
            if (action && message.isActionExecutionMessage()) {
              const resultMessage = yield executeActionFromMessage(action, message);
              const pairedFeAction = getPairedFeAction(actions, resultMessage);
              if (pairedFeAction) {
                const newExecutionMessage = new import_runtime_client_gql6.ActionExecutionMessage({
                  name: pairedFeAction.name,
                  arguments: (0, import_shared10.parseJson)(resultMessage.result, resultMessage.result),
                  status: message.status,
                  createdAt: message.createdAt,
                  parentMessageId: message.parentMessageId
                });
                yield executeActionFromMessage(pairedFeAction, newExecutionMessage);
              }
            } else if (message.isResultMessage() && currentResultMessagePairedFeAction) {
              const newExecutionMessage = new import_runtime_client_gql6.ActionExecutionMessage({
                name: currentResultMessagePairedFeAction.name,
                arguments: (0, import_shared10.parseJson)(message.result, message.result),
                status: message.status,
                createdAt: message.createdAt
              });
              finalMessages.push(newExecutionMessage);
              yield executeActionFromMessage(
                currentResultMessagePairedFeAction,
                newExecutionMessage
              );
            }
          }
          setMessages(finalMessages);
        }
        if (
          // if followUp is not explicitly false
          followUp !== false && // and we executed an action
          (didExecuteAction || // the last message is a server side result
          !isAgentRun && finalMessages.length && finalMessages[finalMessages.length - 1].isResultMessage()) && // the user did not stop generation
          !((_m = chatAbortControllerRef.current) == null ? void 0 : _m.signal.aborted)
        ) {
          yield new Promise((resolve) => setTimeout(resolve, 10));
          return yield runChatCompletionRef.current(finalMessages);
        } else if ((_n = chatAbortControllerRef.current) == null ? void 0 : _n.signal.aborted) {
          const repairedMessages = finalMessages.filter((message, actionExecutionIndex) => {
            if (message.isActionExecutionMessage()) {
              return finalMessages.find(
                (msg, resultIndex) => msg.isResultMessage() && msg.actionExecutionId === message.id && resultIndex === actionExecutionIndex + 1
              );
            }
            return true;
          });
          const repairedMessageIds = repairedMessages.map((message) => message.id);
          setMessages(repairedMessages);
          if ((_o = agentSessionRef.current) == null ? void 0 : _o.nodeName) {
            setAgentSession({
              threadId: agentSessionRef.current.threadId,
              agentName: agentSessionRef.current.agentName,
              nodeName: "__end__"
            });
          }
          return newMessages.filter((message) => repairedMessageIds.includes(message.id));
        } else {
          return newMessages.slice();
        }
      } finally {
        setIsLoading(false);
      }
    }),
    [
      messages,
      setMessages,
      makeSystemMessageCallback,
      copilotConfig,
      setIsLoading,
      initialMessages,
      isLoading,
      actions,
      onFunctionCall,
      onCoAgentStateRender,
      setCoagentStatesWithRef,
      coagentStatesRef,
      agentSession,
      setAgentSession
    ]
  );
  runChatCompletionRef.current = runChatCompletion;
  const runChatCompletionAndHandleFunctionCall = useAsyncCallback(
    (messages2) => __async(this, null, function* () {
      yield runChatCompletionRef.current(messages2);
    }),
    [messages]
  );
  const composeAndFlushMetaEventsInput = (0, import_react11.useCallback)(
    (metaEvents) => {
      return metaEvents.reduce((acc, event) => {
        if (!event)
          return acc;
        switch (event.name) {
          case import_runtime_client_gql6.MetaEventName.LangGraphInterruptEvent:
            if (event.response) {
              setLangGraphInterruptAction(null);
              const value = event.value;
              return [
                ...acc,
                {
                  name: event.name,
                  value: typeof value === "string" ? value : JSON.stringify(value),
                  response: typeof event.response === "string" ? event.response : JSON.stringify(event.response)
                }
              ];
            }
            return acc;
          default:
            return acc;
        }
      }, []);
    },
    [setLangGraphInterruptAction]
  );
  const append = useAsyncCallback(
    (message, options2) => __async(this, null, function* () {
      var _a;
      if (isLoading) {
        return;
      }
      const newMessages = [...messages, message];
      setMessages(newMessages);
      const followUp = (_a = options2 == null ? void 0 : options2.followUp) != null ? _a : true;
      if (followUp) {
        return runChatCompletionAndHandleFunctionCall(newMessages);
      }
    }),
    [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]
  );
  const reload = useAsyncCallback(
    (messageId) => __async(this, null, function* () {
      if (isLoading || messages.length === 0) {
        return;
      }
      const index = messages.findIndex((msg) => msg.id === messageId);
      if (index === -1) {
        console.warn(`Message with id ${messageId} not found`);
        return;
      }
      let newMessages = messages.slice(0, index);
      if (newMessages.length > 0 && newMessages[newMessages.length - 1].isAgentStateMessage()) {
        newMessages = newMessages.slice(0, newMessages.length - 1);
      }
      setMessages(newMessages);
      return runChatCompletionAndHandleFunctionCall(newMessages);
    }),
    [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]
  );
  const stop = () => {
    var _a;
    (_a = chatAbortControllerRef.current) == null ? void 0 : _a.abort("Stop was called");
  };
  return {
    append,
    reload,
    stop,
    runChatCompletion: () => runChatCompletionRef.current(messages)
  };
}
function constructFinalMessages(syncedMessages, previousMessages, newMessages) {
  const finalMessages = syncedMessages.length > 0 ? [...syncedMessages] : [...previousMessages, ...newMessages];
  if (syncedMessages.length > 0) {
    const messagesWithAgentState = [...previousMessages, ...newMessages];
    let previousMessageId = void 0;
    for (const message of messagesWithAgentState) {
      if (message.isAgentStateMessage()) {
        const index = finalMessages.findIndex((msg) => msg.id === previousMessageId);
        if (index !== -1) {
          finalMessages.splice(index + 1, 0, message);
        }
      }
      previousMessageId = message.id;
    }
  }
  return finalMessages;
}
function executeAction(_0) {
  return __async(this, arguments, function* ({
    onFunctionCall,
    previousMessages,
    message,
    chatAbortControllerRef,
    onError
  }) {
    let result;
    let error = null;
    try {
      result = yield Promise.race([
        onFunctionCall({
          messages: previousMessages,
          name: message.name,
          args: message.arguments
        }),
        new Promise(
          (resolve) => {
            var _a;
            return (_a = chatAbortControllerRef.current) == null ? void 0 : _a.signal.addEventListener(
              "abort",
              () => resolve("Operation was aborted by the user")
            );
          }
        ),
        // if the user stopped generation, we also abort consecutive actions
        new Promise((resolve) => {
          var _a;
          if ((_a = chatAbortControllerRef.current) == null ? void 0 : _a.signal.aborted) {
            resolve("Operation was aborted by the user");
          }
        })
      ]);
    } catch (e) {
      onError(e);
    }
    return new import_runtime_client_gql6.ResultMessage({
      id: "result-" + message.id,
      result: import_runtime_client_gql6.ResultMessage.encodeResult(
        error ? {
          content: result,
          error: JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error)))
        } : result
      ),
      actionExecutionId: message.id,
      actionName: message.name
    });
  });
}
function getPairedFeAction(actions, message) {
  let actionName = null;
  if (message.isActionExecutionMessage()) {
    actionName = message.name;
  } else if (message.isResultMessage()) {
    actionName = message.actionName;
  }
  return actions.find(
    (action) => action.name === actionName && action.available === "frontend" || action.pairedAction === actionName
  );
}

// src/hooks/use-copilot-chat.ts
function useCopilotChat(_a = {}) {
  var _b = _a, {
    makeSystemMessage: makeSystemMessage2
  } = _b, options = __objRest(_b, [
    "makeSystemMessage"
  ]);
  const {
    getContextString,
    getFunctionCallHandler,
    copilotApiConfig,
    isLoading,
    setIsLoading,
    chatInstructions,
    actions,
    coagentStatesRef,
    setCoagentStatesWithRef,
    coAgentStateRenders,
    agentSession,
    setAgentSession,
    forwardedParameters,
    agentLock,
    threadId,
    setThreadId,
    runId,
    setRunId,
    chatAbortControllerRef,
    extensions,
    setExtensions,
    langGraphInterruptAction,
    setLangGraphInterruptAction
  } = useCopilotContext();
  const { messages, setMessages } = useCopilotMessagesContext();
  const [mcpServers, setLocalMcpServers] = (0, import_react12.useState)([]);
  (0, import_react12.useEffect)(() => {
    if (mcpServers.length > 0) {
      const serversCopy = [...mcpServers];
      copilotApiConfig.mcpServers = serversCopy;
      if (!copilotApiConfig.properties) {
        copilotApiConfig.properties = {};
      }
      copilotApiConfig.properties.mcpServers = serversCopy;
    }
  }, [mcpServers, copilotApiConfig]);
  const setMcpServers = (0, import_react12.useCallback)((servers) => {
    setLocalMcpServers(servers);
  }, []);
  const onCoAgentStateRender = useAsyncCallback(
    (args) => __async(this, null, function* () {
      var _a2;
      const { name, nodeName, state } = args;
      let action = Object.values(coAgentStateRenders).find(
        (action2) => action2.name === name && action2.nodeName === nodeName
      );
      if (!action) {
        action = Object.values(coAgentStateRenders).find(
          (action2) => action2.name === name && !action2.nodeName
        );
      }
      if (action) {
        yield (_a2 = action.handler) == null ? void 0 : _a2.call(action, { state, nodeName });
      }
    }),
    [coAgentStateRenders]
  );
  const makeSystemMessageCallback = (0, import_react12.useCallback)(() => {
    const systemMessageMaker = makeSystemMessage2 || defaultSystemMessage;
    const contextString = getContextString([], defaultCopilotContextCategories);
    return new import_runtime_client_gql7.TextMessage({
      content: systemMessageMaker(contextString, chatInstructions),
      role: import_runtime_client_gql7.Role.System
    });
  }, [getContextString, makeSystemMessage2, chatInstructions]);
  const deleteMessage = (0, import_react12.useCallback)(
    (messageId) => {
      setMessages((prev) => prev.filter((message) => message.id !== messageId));
    },
    [setMessages]
  );
  const { append, reload, stop, runChatCompletion } = useChat(__spreadProps(__spreadValues({}, options), {
    actions: Object.values(actions),
    copilotConfig: copilotApiConfig,
    initialMessages: options.initialMessages || [],
    onFunctionCall: getFunctionCallHandler(),
    onCoAgentStateRender,
    messages,
    setMessages,
    makeSystemMessageCallback,
    isLoading,
    setIsLoading,
    coagentStatesRef,
    setCoagentStatesWithRef,
    agentSession,
    setAgentSession,
    forwardedParameters,
    threadId,
    setThreadId,
    runId,
    setRunId,
    chatAbortControllerRef,
    agentLock,
    extensions,
    setExtensions,
    langGraphInterruptAction,
    setLangGraphInterruptAction
  }));
  const latestAppend = useUpdatedRef(append);
  const latestAppendFunc = useAsyncCallback(
    (message, options2) => __async(this, null, function* () {
      return yield latestAppend.current(message, options2);
    }),
    [latestAppend]
  );
  const latestReload = useUpdatedRef(reload);
  const latestReloadFunc = useAsyncCallback(
    (messageId) => __async(this, null, function* () {
      return yield latestReload.current(messageId);
    }),
    [latestReload]
  );
  const latestStop = useUpdatedRef(stop);
  const latestStopFunc = (0, import_react12.useCallback)(() => {
    return latestStop.current();
  }, [latestStop]);
  const latestDelete = useUpdatedRef(deleteMessage);
  const latestDeleteFunc = (0, import_react12.useCallback)(
    (messageId) => {
      return latestDelete.current(messageId);
    },
    [latestDelete]
  );
  const latestSetMessages = useUpdatedRef(setMessages);
  const latestSetMessagesFunc = (0, import_react12.useCallback)(
    (messages2) => {
      return latestSetMessages.current(messages2);
    },
    [latestSetMessages]
  );
  const latestRunChatCompletion = useUpdatedRef(runChatCompletion);
  const latestRunChatCompletionFunc = useAsyncCallback(() => __async(this, null, function* () {
    return yield latestRunChatCompletion.current();
  }), [latestRunChatCompletion]);
  const reset = (0, import_react12.useCallback)(() => {
    latestStopFunc();
    setMessages([]);
    setRunId(null);
    setCoagentStatesWithRef({});
    let initialAgentSession = null;
    if (agentLock) {
      initialAgentSession = {
        agentName: agentLock
      };
    }
    setAgentSession(initialAgentSession);
  }, [
    latestStopFunc,
    setMessages,
    setThreadId,
    setCoagentStatesWithRef,
    setAgentSession,
    agentLock
  ]);
  const latestReset = useUpdatedRef(reset);
  const latestResetFunc = (0, import_react12.useCallback)(() => {
    return latestReset.current();
  }, [latestReset]);
  return {
    visibleMessages: messages,
    appendMessage: latestAppendFunc,
    setMessages: latestSetMessagesFunc,
    reloadMessages: latestReloadFunc,
    stopGeneration: latestStopFunc,
    reset: latestResetFunc,
    deleteMessage: latestDeleteFunc,
    runChatCompletion: latestRunChatCompletionFunc,
    isLoading,
    mcpServers,
    setMcpServers
  };
}
function useUpdatedRef(value) {
  const ref = (0, import_react12.useRef)(value);
  (0, import_react12.useEffect)(() => {
    ref.current = value;
  }, [value]);
  return ref;
}
function defaultSystemMessage(contextString, additionalInstructions) {
  return `
Please act as an efficient, competent, conscientious, and industrious professional assistant.

Help the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.
Always be polite and respectful, and prefer brevity over verbosity.

The user has provided you with the following context:
\`\`\`
${contextString}
\`\`\`

They have also provided you with functions you can call to initiate actions on their behalf, or functions you can call to receive more information.

Please assist them as best you can.

You can ask them for clarifying questions if needed, but don't be annoying about it. If you can reasonably 'fill in the blanks' yourself, do so.

If you would like to call a function, call it without saying anything else.
In case of a function error:
- If this error stems from incorrect function parameters or syntax, you may retry with corrected arguments.
- If the error's source is unclear or seems unrelated to your input, do not attempt further retries.
` + (additionalInstructions ? `

${additionalInstructions}` : "");
}

// src/hooks/use-copilot-action.ts
var import_shared11 = require("@copilotkit/shared");
var import_react13 = require("react");
function useCopilotAction(action, dependencies) {
  const { setAction, removeAction, actions, chatComponentsCache } = useCopilotContext();
  const idRef = (0, import_react13.useRef)((0, import_shared11.randomId)());
  const renderAndWaitRef = (0, import_react13.useRef)(null);
  const { addToast } = useToast();
  action = __spreadValues({}, action);
  if (
    // renderAndWaitForResponse is not available for catch all actions
    isFrontendAction(action) && // check if renderAndWaitForResponse is set
    (action.renderAndWait || action.renderAndWaitForResponse)
  ) {
    const renderAndWait = action.renderAndWait || action.renderAndWaitForResponse;
    action.renderAndWait = void 0;
    action.renderAndWaitForResponse = void 0;
    action.handler = useAsyncCallback(() => __async(this, null, function* () {
      let resolve;
      let reject;
      const promise = new Promise((resolvePromise, rejectPromise) => {
        resolve = resolvePromise;
        reject = rejectPromise;
      });
      renderAndWaitRef.current = { promise, resolve, reject };
      return yield promise;
    }), []);
    action.render = (props) => {
      let status = props.status;
      if (props.status === "executing" && !renderAndWaitRef.current) {
        status = "inProgress";
      }
      const waitProps = {
        status,
        args: props.args,
        result: props.result,
        handler: status === "executing" ? renderAndWaitRef.current.resolve : void 0,
        respond: status === "executing" ? renderAndWaitRef.current.resolve : void 0
      };
      const isNoArgsRenderWait = (_fn) => {
        var _a;
        return ((_a = action.parameters) == null ? void 0 : _a.length) === 0;
      };
      if (renderAndWait) {
        if (isNoArgsRenderWait(renderAndWait)) {
          return renderAndWait(waitProps);
        } else {
          return renderAndWait(waitProps);
        }
      }
      return (0, import_react13.createElement)(import_react13.Fragment);
    };
  }
  if (dependencies === void 0) {
    if (actions[idRef.current]) {
      if (isFrontendAction(action)) {
        actions[idRef.current].handler = action.handler;
      }
      if (typeof action.render === "function") {
        if (chatComponentsCache.current !== null) {
          chatComponentsCache.current.actions[action.name] = action.render;
        }
      }
    }
  }
  (0, import_react13.useEffect)(() => {
    const hasDuplicate = Object.values(actions).some(
      (otherAction) => otherAction.name === action.name && otherAction !== actions[idRef.current]
    );
    if (hasDuplicate) {
      addToast({
        type: "warning",
        message: `Found an already registered action with name ${action.name}.`,
        id: `dup-action-${action.name}`
      });
    }
  }, [actions]);
  (0, import_react13.useEffect)(() => {
    setAction(idRef.current, action);
    if (chatComponentsCache.current !== null && action.render !== void 0) {
      chatComponentsCache.current.actions[action.name] = action.render;
    }
    return () => {
      removeAction(idRef.current);
    };
  }, [
    setAction,
    removeAction,
    isFrontendAction(action) ? action.description : void 0,
    action.name,
    isFrontendAction(action) ? action.disabled : void 0,
    isFrontendAction(action) ? action.available : void 0,
    // This should be faster than deep equality checking
    // In addition, all major JS engines guarantee the order of object keys
    JSON.stringify(isFrontendAction(action) ? action.parameters : []),
    // include render only if it's a string
    typeof action.render === "string" ? action.render : void 0,
    // dependencies set by the developer
    ...dependencies || []
  ]);
}
function isFrontendAction(action) {
  return action.name !== "*";
}

// src/hooks/use-coagent-state-render.ts
var import_react14 = require("react");
var import_shared12 = require("@copilotkit/shared");
function useCoAgentStateRender(action, dependencies) {
  const {
    setCoAgentStateRender,
    removeCoAgentStateRender,
    coAgentStateRenders,
    chatComponentsCache,
    availableAgents
  } = (0, import_react14.useContext)(CopilotContext);
  const idRef = (0, import_react14.useRef)((0, import_shared12.randomId)());
  const { addToast } = useToast();
  (0, import_react14.useEffect)(() => {
    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === action.name)) {
      const message = `(useCoAgentStateRender): Agent "${action.name}" not found. Make sure the agent exists and is properly configured.`;
      addToast({ type: "warning", message });
    }
  }, [availableAgents]);
  const key = `${action.name}-${action.nodeName || "global"}`;
  if (dependencies === void 0) {
    if (coAgentStateRenders[idRef.current]) {
      coAgentStateRenders[idRef.current].handler = action.handler;
      if (typeof action.render === "function") {
        if (chatComponentsCache.current !== null) {
          chatComponentsCache.current.coAgentStateRenders[key] = action.render;
        }
      }
    }
  }
  (0, import_react14.useEffect)(() => {
    const currentId = idRef.current;
    const hasDuplicate = Object.entries(coAgentStateRenders).some(([id, otherAction]) => {
      if (id === currentId)
        return false;
      if (otherAction.name !== action.name)
        return false;
      const hasNodeName = !!action.nodeName;
      const hasOtherNodeName = !!otherAction.nodeName;
      if (!hasNodeName && !hasOtherNodeName)
        return true;
      if (hasNodeName !== hasOtherNodeName)
        return false;
      return action.nodeName === otherAction.nodeName;
    });
    if (hasDuplicate) {
      const message = action.nodeName ? `Found multiple state renders for agent ${action.name} and node ${action.nodeName}. State renders might get overridden` : `Found multiple state renders for agent ${action.name}. State renders might get overridden`;
      addToast({
        type: "warning",
        message,
        id: `dup-action-${action.name}`
      });
    }
  }, [coAgentStateRenders]);
  (0, import_react14.useEffect)(() => {
    setCoAgentStateRender(idRef.current, action);
    if (chatComponentsCache.current !== null && action.render !== void 0) {
      chatComponentsCache.current.coAgentStateRenders[key] = action.render;
    }
    return () => {
      removeCoAgentStateRender(idRef.current);
    };
  }, [
    setCoAgentStateRender,
    removeCoAgentStateRender,
    action.name,
    // include render only if it's a string
    typeof action.render === "string" ? action.render : void 0,
    // dependencies set by the developer
    ...dependencies || []
  ]);
}

// src/hooks/use-make-copilot-document-readable.ts
var import_react15 = require("react");
function useMakeCopilotDocumentReadable(document, categories, dependencies = []) {
  const { addDocumentContext, removeDocumentContext } = useCopilotContext();
  const idRef = (0, import_react15.useRef)();
  (0, import_react15.useEffect)(() => {
    const id = addDocumentContext(document, categories);
    idRef.current = id;
    return () => {
      removeDocumentContext(id);
    };
  }, [addDocumentContext, removeDocumentContext, ...dependencies]);
  return idRef.current;
}

// src/hooks/use-copilot-readable.ts
var import_react16 = require("react");
function convertToJSON(description, value) {
  return `${description}: ${typeof value === "string" ? value : JSON.stringify(value)}`;
}
function useCopilotReadable({
  description,
  value,
  parentId,
  categories,
  convert,
  available = "enabled"
}, dependencies) {
  const { addContext, removeContext } = useCopilotContext();
  const idRef = (0, import_react16.useRef)();
  convert = convert || convertToJSON;
  const information = convert(description, value);
  (0, import_react16.useEffect)(() => {
    if (available === "disabled")
      return;
    const id = addContext(information, parentId, categories);
    idRef.current = id;
    return () => {
      removeContext(id);
    };
  }, [available, information, parentId, addContext, removeContext, ...dependencies || []]);
  return idRef.current;
}

// src/hooks/use-coagent.ts
var import_react17 = require("react");
var import_shared13 = require("@copilotkit/shared");
function useCoAgent(options) {
  const generalContext = useCopilotContext();
  const { availableAgents } = generalContext;
  const { addToast } = useToast();
  const lastLoadedThreadId = (0, import_react17.useRef)();
  const lastLoadedState = (0, import_react17.useRef)();
  const { name } = options;
  (0, import_react17.useEffect)(() => {
    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === name)) {
      const message = `(useCoAgent): Agent "${name}" not found. Make sure the agent exists and is properly configured.`;
      console.warn(message);
      addToast({ type: "warning", message });
    }
  }, [availableAgents]);
  const messagesContext = useCopilotMessagesContext();
  const context = __spreadValues(__spreadValues({}, generalContext), messagesContext);
  const { coagentStates, coagentStatesRef, setCoagentStatesWithRef, threadId, copilotApiConfig } = context;
  const { appendMessage, runChatCompletion } = useCopilotChat();
  const runtimeClient = useCopilotRuntimeClient({
    url: copilotApiConfig.chatApiEndpoint,
    publicApiKey: copilotApiConfig.publicApiKey,
    credentials: copilotApiConfig.credentials
  });
  const setState = (0, import_react17.useCallback)(
    (newState) => {
      let coagentState = getCoagentState({ coagentStates, name, options });
      const updatedState = typeof newState === "function" ? newState(coagentState.state) : newState;
      setCoagentStatesWithRef(__spreadProps(__spreadValues({}, coagentStatesRef.current), {
        [name]: __spreadProps(__spreadValues({}, coagentState), {
          state: updatedState
        })
      }));
    },
    [coagentStates, name]
  );
  (0, import_react17.useEffect)(() => {
    const fetchAgentState = () => __async(this, null, function* () {
      var _a, _b, _c, _d;
      if (!threadId || threadId === lastLoadedThreadId.current)
        return;
      const result = yield runtimeClient.loadAgentState({
        threadId,
        agentName: name
      });
      const newState = (_b = (_a = result.data) == null ? void 0 : _a.loadAgentState) == null ? void 0 : _b.state;
      if (newState === lastLoadedState.current)
        return;
      if (((_d = (_c = result.data) == null ? void 0 : _c.loadAgentState) == null ? void 0 : _d.threadExists) && newState && newState != "{}") {
        lastLoadedState.current = newState;
        lastLoadedThreadId.current = threadId;
        const fetchedState = (0, import_shared13.parseJson)(newState, {});
        isExternalStateManagement(options) ? options.setState(fetchedState) : setState(fetchedState);
      }
    });
    void fetchAgentState();
  }, [threadId]);
  (0, import_react17.useEffect)(() => {
    if (isExternalStateManagement(options)) {
      setState(options.state);
    } else if (coagentStates[name] === void 0) {
      setState(options.initialState === void 0 ? {} : options.initialState);
    }
  }, [
    isExternalStateManagement(options) ? JSON.stringify(options.state) : void 0,
    // reset initialstate on reset
    coagentStates[name] === void 0
  ]);
  const runAgentCallback = useAsyncCallback(
    (hint) => __async(this, null, function* () {
      yield runAgent(name, context, appendMessage, runChatCompletion, hint);
    }),
    [name, context, appendMessage, runChatCompletion]
  );
  return (0, import_react17.useMemo)(() => {
    const coagentState = getCoagentState({ coagentStates, name, options });
    return {
      name,
      nodeName: coagentState.nodeName,
      threadId: coagentState.threadId,
      running: coagentState.running,
      state: coagentState.state,
      setState: isExternalStateManagement(options) ? options.setState : setState,
      start: () => startAgent(name, context),
      stop: () => stopAgent(name, context),
      run: runAgentCallback
    };
  }, [name, coagentStates, options, setState, runAgentCallback]);
}
function startAgent(name, context) {
  const { setAgentSession } = context;
  setAgentSession({
    agentName: name
  });
}
function stopAgent(name, context) {
  const { agentSession, setAgentSession } = context;
  if (agentSession && agentSession.agentName === name) {
    setAgentSession(null);
    context.setCoagentStates((prevAgentStates) => {
      return __spreadProps(__spreadValues({}, prevAgentStates), {
        [name]: __spreadProps(__spreadValues({}, prevAgentStates[name]), {
          running: false,
          active: false,
          threadId: void 0,
          nodeName: void 0,
          runId: void 0
        })
      });
    });
  } else {
    console.warn(`No agent session found for ${name}`);
  }
}
function runAgent(name, context, appendMessage, runChatCompletion, hint) {
  return __async(this, null, function* () {
    var _a, _b;
    const { agentSession, setAgentSession } = context;
    if (!agentSession || agentSession.agentName !== name) {
      setAgentSession({
        agentName: name
      });
    }
    let previousState = null;
    for (let i = context.messages.length - 1; i >= 0; i--) {
      const message = context.messages[i];
      if (message.isAgentStateMessage() && message.agentName === name) {
        previousState = message.state;
      }
    }
    let state = ((_b = (_a = context.coagentStatesRef.current) == null ? void 0 : _a[name]) == null ? void 0 : _b.state) || {};
    if (hint) {
      const hintMessage = hint({ previousState, currentState: state });
      if (hintMessage) {
        yield appendMessage(hintMessage);
      } else {
        yield runChatCompletion();
      }
    } else {
      yield runChatCompletion();
    }
  });
}
var isExternalStateManagement = (options) => {
  return "state" in options && "setState" in options;
};
var isInternalStateManagementWithInitial = (options) => {
  return "initialState" in options;
};
var getCoagentState = ({
  coagentStates,
  name,
  options
}) => {
  if (coagentStates[name]) {
    return coagentStates[name];
  } else {
    return {
      name,
      state: isInternalStateManagementWithInitial(options) ? options.initialState : {},
      config: options.config ? options.config : options.configurable ? { configurable: options.configurable } : {},
      running: false,
      active: false,
      threadId: void 0,
      nodeName: void 0,
      runId: void 0
    };
  }
};

// src/hooks/use-copilot-authenticated-action.ts
var import_react18 = require("react");
var import_react19 = __toESM(require("react"));
function useCopilotAuthenticatedAction_c(action, dependencies) {
  const { authConfig_c, authStates_c, setAuthStates_c } = useCopilotContext();
  const pendingActionRef = (0, import_react18.useRef)(null);
  const executeAction2 = (0, import_react18.useCallback)(
    (props) => {
      if (typeof action.render === "function") {
        return action.render(props);
      }
      return action.render || import_react19.default.createElement(import_react18.Fragment);
    },
    [action]
  );
  const wrappedRender = (0, import_react18.useCallback)(
    (props) => {
      const isAuthenticated = Object.values(authStates_c || {}).some(
        (state) => state.status === "authenticated"
      );
      if (!isAuthenticated) {
        pendingActionRef.current = props;
        return (authConfig_c == null ? void 0 : authConfig_c.SignInComponent) ? import_react19.default.createElement(authConfig_c.SignInComponent, {
          onSignInComplete: (authState) => {
            setAuthStates_c == null ? void 0 : setAuthStates_c((prev) => __spreadProps(__spreadValues({}, prev), { [action.name]: authState }));
            if (pendingActionRef.current) {
              executeAction2(pendingActionRef.current);
              pendingActionRef.current = null;
            }
          }
        }) : import_react19.default.createElement(import_react18.Fragment);
      }
      return executeAction2(props);
    },
    [action, authStates_c, setAuthStates_c]
  );
  useCopilotAction(
    __spreadProps(__spreadValues({}, action), {
      render: wrappedRender
    }),
    dependencies
  );
}

// src/hooks/use-langgraph-interrupt.ts
var import_react20 = require("react");
var import_shared14 = require("@copilotkit/shared");
function useLangGraphInterrupt(action, dependencies) {
  var _a;
  const { setLangGraphInterruptAction, removeLangGraphInterruptAction, langGraphInterruptAction } = (0, import_react20.useContext)(CopilotContext);
  const { runChatCompletion } = useCopilotChat();
  const { addToast } = useToast();
  const actionId = (0, import_shared14.dataToUUID)(JSON.stringify(action), "lgAction");
  const hasAction = (0, import_react20.useMemo)(
    () => Boolean(langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.id),
    [langGraphInterruptAction]
  );
  const isCurrentAction = (0, import_react20.useMemo)(
    () => (langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.id) && (langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.id) === actionId,
    [langGraphInterruptAction]
  );
  (0, import_react20.useEffect)(() => {
    var _a2;
    if (hasAction && isCurrentAction && ((_a2 = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event) == null ? void 0 : _a2.response)) {
      runChatCompletion();
    }
  }, [(_a = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event) == null ? void 0 : _a.response, runChatCompletion, hasAction, isCurrentAction]);
  (0, import_react20.useEffect)(() => {
    if (!action)
      return;
    if (hasAction && !isCurrentAction && !action.enabled) {
      addToast({
        type: "warning",
        message: "An action is already registered for the interrupt event"
      });
      return;
    }
    if (hasAction && isCurrentAction) {
      return;
    }
    setLangGraphInterruptAction(__spreadProps(__spreadValues({}, action), { id: actionId }));
  }, [
    action,
    hasAction,
    isCurrentAction,
    setLangGraphInterruptAction,
    removeLangGraphInterruptAction,
    ...dependencies || []
  ]);
}

// src/hooks/use-langgraph-interrupt-render.ts
var import_react21 = __toESM(require("react"));
var InterruptRenderer = ({ event, result, render, resolve }) => {
  return render({ event, result, resolve });
};
function useLangGraphInterruptRender() {
  const { langGraphInterruptAction, setLangGraphInterruptAction, agentSession } = useCopilotContext();
  const responseRef = import_react21.default.useRef();
  const resolveInterrupt = (0, import_react21.useCallback)(
    (response) => {
      responseRef.current = response;
      setTimeout(() => {
        setLangGraphInterruptAction({ event: { response } });
      }, 0);
    },
    [setLangGraphInterruptAction]
  );
  if (!langGraphInterruptAction || !langGraphInterruptAction.event || !langGraphInterruptAction.render)
    return null;
  const { render, handler, event, enabled } = langGraphInterruptAction;
  const conditionsMet = !agentSession || !enabled ? true : enabled({ eventValue: event.value, agentMetadata: agentSession });
  if (!conditionsMet) {
    return null;
  }
  let result = null;
  if (handler) {
    result = handler({
      event,
      resolve: resolveInterrupt
    });
  }
  return import_react21.default.createElement(InterruptRenderer, {
    event,
    result,
    render,
    resolve: resolveInterrupt
  });
}

// src/hooks/use-copilot-additional-instructions.ts
var import_react22 = require("react");
function useCopilotAdditionalInstructions({ instructions, available = "enabled" }, dependencies) {
  const { setAdditionalInstructions } = useCopilotContext();
  (0, import_react22.useEffect)(() => {
    if (available === "disabled")
      return;
    setAdditionalInstructions((prevInstructions) => [...prevInstructions || [], instructions]);
    return () => {
      setAdditionalInstructions(
        (prevInstructions) => (prevInstructions == null ? void 0 : prevInstructions.filter((instruction) => instruction !== instructions)) || []
      );
    };
  }, [available, instructions, setAdditionalInstructions, ...dependencies || []]);
}

// src/lib/copilot-task.ts
var import_runtime_client_gql8 = require("@copilotkit/runtime-client-gql");
var CopilotTask = class {
  constructor(config) {
    this.instructions = config.instructions;
    this.actions = config.actions || [];
    this.includeCopilotReadable = config.includeCopilotReadable !== false;
    this.includeCopilotActions = config.includeCopilotActions !== false;
    this.forwardedParameters = config.forwardedParameters;
  }
  /**
   * Run the task.
   * @param context The CopilotContext to use for the task. Use `useCopilotContext` to obtain the current context.
   * @param data The data to use for the task.
   */
  run(context, data) {
    return __async(this, null, function* () {
      var _a, _b, _c;
      const actions = this.includeCopilotActions ? Object.assign({}, context.actions) : {};
      for (const fn of this.actions) {
        actions[fn.name] = fn;
      }
      let contextString = "";
      if (data) {
        contextString = (typeof data === "string" ? data : JSON.stringify(data)) + "\n\n";
      }
      if (this.includeCopilotReadable) {
        contextString += context.getContextString([], defaultCopilotContextCategories);
      }
      const systemMessage = new import_runtime_client_gql8.TextMessage({
        content: taskSystemMessage(contextString, this.instructions),
        role: import_runtime_client_gql8.Role.System
      });
      const messages = [systemMessage];
      const runtimeClient = new import_runtime_client_gql8.CopilotRuntimeClient({
        url: context.copilotApiConfig.chatApiEndpoint,
        publicApiKey: context.copilotApiConfig.publicApiKey,
        headers: context.copilotApiConfig.headers,
        credentials: context.copilotApiConfig.credentials
      });
      const response = yield runtimeClient.generateCopilotResponse({
        data: {
          frontend: {
            actions: processActionsForRuntimeRequest(Object.values(actions)),
            url: window.location.href
          },
          messages: (0, import_runtime_client_gql8.convertMessagesToGqlInput)((0, import_runtime_client_gql8.filterAgentStateMessages)(messages)),
          metadata: {
            requestType: import_runtime_client_gql8.CopilotRequestType.Task
          },
          forwardedParameters: __spreadProps(__spreadValues({}, (_a = this.forwardedParameters) != null ? _a : {}), {
            toolChoice: "required"
          })
        },
        properties: context.copilotApiConfig.properties
      }).toPromise();
      const functionCallHandler = context.getFunctionCallHandler(actions);
      const functionCalls = (0, import_runtime_client_gql8.convertGqlOutputToMessages)(
        ((_c = (_b = response.data) == null ? void 0 : _b.generateCopilotResponse) == null ? void 0 : _c.messages) || []
      ).filter((m) => m.isActionExecutionMessage());
      for (const functionCall of functionCalls) {
        yield functionCallHandler({
          messages,
          name: functionCall.name,
          args: functionCall.arguments
        });
      }
    });
  }
};
function taskSystemMessage(contextString, instructions) {
  return `
Please act as an efficient, competent, conscientious, and industrious professional assistant.

Help the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.
Always be polite and respectful, and prefer brevity over verbosity.

The user has provided you with the following context:
\`\`\`
${contextString}
\`\`\`

They have also provided you with functions you can call to initiate actions on their behalf.

Please assist them as best you can.

This is not a conversation, so please do not ask questions. Just call a function without saying anything else.

The user has given you the following task to complete:

\`\`\`
${instructions}
\`\`\`
`;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  CopilotContext,
  CopilotKit,
  CopilotMessagesContext,
  CopilotTask,
  defaultCopilotContextCategories,
  extract,
  runAgent,
  shouldShowDevConsole,
  startAgent,
  stopAgent,
  useCoAgent,
  useCoAgentStateRender,
  useCopilotAction,
  useCopilotAdditionalInstructions,
  useCopilotAuthenticatedAction_c,
  useCopilotChat,
  useCopilotContext,
  useCopilotMessagesContext,
  useCopilotReadable,
  useCopilotRuntimeClient,
  useLangGraphInterrupt,
  useLangGraphInterruptRender,
  useMakeCopilotDocumentReadable
});
//# sourceMappingURL=index.js.map