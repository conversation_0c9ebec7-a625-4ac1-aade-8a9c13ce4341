{"version": 3, "sources": ["../../../../src/components/chat/messages/UserMessage.tsx"], "sourcesContent": ["import { UserMessageProps } from \"../props\";\n\nexport const UserMessage = (props: UserMessageProps) => {\n  return (\n    <div className=\"copilotKitMessage copilotKitUserMessage\">\n      {props.subComponent || props.message}\n    </div>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAII;AAFG,IAAM,cAAc,CAAC,UAA4B;AACtD,SACE,4CAAC,SAAI,WAAU,2CACZ,gBAAM,gBAAgB,MAAM,SAC/B;AAEJ;", "names": []}