-- Migration: Create prospects and marketing schema for waitlist/newsletter signups
-- This handles pre-authentication prospect data collection

-- Create prospects table in public schema (pre-tenant assignment)
CREATE TABLE IF NOT EXISTS public.prospects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Contact Information
    email TEXT NOT NULL UNIQUE,
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    
    -- Signup Context
    signup_source TEXT NOT NULL DEFAULT 'website' CHECK (signup_source IN (
        'website', 'landing_page', 'referral', 'social_media', 'advertisement', 'other'
    )),
    signup_page TEXT, -- URL where they signed up
    utm_source TEXT,
    utm_medium TEXT,
    utm_campaign TEXT,
    utm_content TEXT,
    utm_term TEXT,
    
    -- Preferences
    newsletter_subscribed BOOLEAN NOT NULL DEFAULT true,
    marketing_consent BOOLEAN NOT NULL DEFAULT false,
    communication_preferences JSONB DEFAULT '{
        "email": true,
        "sms": false,
        "phone": false
    }'::jsonb,
    
    -- Legal Practice Interest
    practice_area_interest TEXT[] DEFAULT '{}', -- ['personal_injury', 'criminal_defense', 'family_law']
    case_urgency TEXT CHECK (case_urgency IN ('immediate', 'within_month', 'within_quarter', 'planning_ahead')),
    estimated_case_value TEXT CHECK (estimated_case_value IN ('under_10k', '10k_50k', '50k_100k', 'over_100k', 'unknown')),
    
    -- Status Tracking
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN (
        'active',           -- Active prospect
        'converted',        -- Converted to authenticated user
        'unsubscribed',     -- Opted out
        'bounced',          -- Email bounced
        'spam_complaint',   -- Marked as spam
        'archived'          -- Archived/inactive
    )),
    
    -- Conversion Tracking
    converted_to_user_id UUID, -- References auth.users(id) when converted
    converted_to_tenant_id UUID, -- References tenants.users(tenant_id) when converted
    converted_at TIMESTAMPTZ,
    
    -- Email Verification
    email_verified BOOLEAN NOT NULL DEFAULT false,
    email_verification_token TEXT UNIQUE,
    email_verification_sent_at TIMESTAMPTZ,
    email_verified_at TIMESTAMPTZ,
    
    -- GDPR Compliance
    gdpr_consent BOOLEAN NOT NULL DEFAULT false,
    gdpr_consent_date TIMESTAMPTZ,
    gdpr_consent_ip TEXT,
    gdpr_consent_user_agent TEXT,
    data_retention_until DATE, -- Auto-delete after this date if not converted
    
    -- Metadata
    ip_address INET,
    user_agent TEXT,
    referrer_url TEXT,
    browser_fingerprint TEXT,
    timezone TEXT,
    locale TEXT DEFAULT 'en-US',
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_contacted_at TIMESTAMPTZ,
    
    -- Soft Delete
    deleted_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT gdpr_consent_required CHECK (
        (gdpr_consent = true AND gdpr_consent_date IS NOT NULL) OR 
        (gdpr_consent = false)
    ),
    CONSTRAINT conversion_data_consistency CHECK (
        (status = 'converted' AND converted_to_user_id IS NOT NULL AND converted_at IS NOT NULL) OR
        (status != 'converted' AND converted_to_user_id IS NULL AND converted_at IS NULL)
    )
);

-- Create prospect interactions table for tracking engagement
CREATE TABLE IF NOT EXISTS public.prospect_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prospect_id UUID NOT NULL REFERENCES public.prospects(id) ON DELETE CASCADE,
    
    -- Interaction Details
    interaction_type TEXT NOT NULL CHECK (interaction_type IN (
        'email_sent', 'email_opened', 'email_clicked', 'email_bounced', 'email_unsubscribed',
        'website_visit', 'form_submission', 'download', 'video_watched',
        'phone_call', 'meeting_scheduled', 'consultation_requested',
        'newsletter_signup', 'content_engagement', 'referral_made'
    )),
    
    -- Context
    subject TEXT, -- Email subject, page title, etc.
    content_id TEXT, -- Newsletter ID, page slug, etc.
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Tracking
    ip_address INET,
    user_agent TEXT,
    referrer_url TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create prospect notes table for manual tracking
CREATE TABLE IF NOT EXISTS public.prospect_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    prospect_id UUID NOT NULL REFERENCES public.prospects(id) ON DELETE CASCADE,
    
    -- Note Content
    note TEXT NOT NULL,
    note_type TEXT NOT NULL DEFAULT 'general' CHECK (note_type IN (
        'general', 'follow_up', 'qualification', 'objection', 'interest', 'technical'
    )),
    
    -- Attribution
    created_by_email TEXT, -- Email of person who created note (before conversion)
    created_by_user_id UUID, -- References auth.users(id) if created by authenticated user
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_prospects_email ON public.prospects(email);
CREATE INDEX IF NOT EXISTS idx_prospects_status ON public.prospects(status);
CREATE INDEX IF NOT EXISTS idx_prospects_signup_source ON public.prospects(signup_source);
CREATE INDEX IF NOT EXISTS idx_prospects_created_at ON public.prospects(created_at);
CREATE INDEX IF NOT EXISTS idx_prospects_converted_user ON public.prospects(converted_to_user_id);
CREATE INDEX IF NOT EXISTS idx_prospects_email_verified ON public.prospects(email_verified);
CREATE INDEX IF NOT EXISTS idx_prospects_gdpr_consent ON public.prospects(gdpr_consent);
CREATE INDEX IF NOT EXISTS idx_prospects_data_retention ON public.prospects(data_retention_until);

CREATE INDEX IF NOT EXISTS idx_prospect_interactions_prospect_id ON public.prospect_interactions(prospect_id);
CREATE INDEX IF NOT EXISTS idx_prospect_interactions_type ON public.prospect_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_prospect_interactions_created_at ON public.prospect_interactions(created_at);

CREATE INDEX IF NOT EXISTS idx_prospect_notes_prospect_id ON public.prospect_notes(prospect_id);
CREATE INDEX IF NOT EXISTS idx_prospect_notes_type ON public.prospect_notes(note_type);

-- Updated at trigger for prospects
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_prospects_updated_at 
    BEFORE UPDATE ON public.prospects 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prospect_notes_updated_at 
    BEFORE UPDATE ON public.prospect_notes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
