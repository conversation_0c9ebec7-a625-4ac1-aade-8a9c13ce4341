{"version": 3, "sources": ["../src/components/chat/messages/RenderActionExecutionMessage.tsx"], "sourcesContent": ["import { MessageStatusCode } from \"@copilotkit/runtime-client-gql\";\nimport { RenderMessageProps } from \"../props\";\nimport { RenderFunctionStatus, useCopilotContext } from \"@copilotkit/react-core\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderActionExecutionMessage({\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const { chatComponentsCache } = useCopilotContext();\n  const { message, inProgress, index, isCurrentMessage, actionResult } = props;\n\n  if (message.isActionExecutionMessage()) {\n    if (\n      chatComponentsCache.current !== null &&\n      (chatComponentsCache.current.actions[message.name] ||\n        chatComponentsCache.current.actions[\"*\"])\n    ) {\n      const render =\n        chatComponentsCache.current.actions[message.name] ||\n        chatComponentsCache.current.actions[\"*\"];\n      // render a static string\n      if (typeof render === \"string\") {\n        // when render is static, we show it only when in progress\n        if (isCurrentMessage && inProgress) {\n          return (\n            <AssistantMessage\n              rawData={message}\n              key={index}\n              data-message-role=\"assistant\"\n              isLoading={false}\n              isGenerating={true}\n              message={render}\n            />\n          );\n        }\n        // Done - silent by default to avoid a series of \"done\" messages\n        else {\n          return null;\n        }\n      }\n      // render is a function\n      else {\n        const args = message.arguments;\n\n        let status: RenderFunctionStatus = \"inProgress\";\n\n        if (actionResult !== undefined) {\n          status = \"complete\";\n        } else if (message.status.code !== MessageStatusCode.Pending) {\n          status = \"executing\";\n        }\n\n        try {\n          const toRender = render({\n            status: status as any,\n            args,\n            result: actionResult,\n            name: message.name,\n          });\n          // No result and complete: stay silent\n          if (!toRender && status === \"complete\") {\n            return null;\n          }\n          if (typeof toRender === \"string\") {\n            return (\n              <AssistantMessage\n                rawData={message}\n                data-message-role=\"assistant\"\n                key={index}\n                isLoading={false}\n                isGenerating={false}\n                message={toRender}\n              />\n            );\n          } else {\n            return (\n              <AssistantMessage\n                rawData={message}\n                data-message-role=\"action-render\"\n                key={index}\n                isLoading={false}\n                isGenerating={false}\n                subComponent={toRender}\n              />\n            );\n          }\n        } catch (e) {\n          console.error(`Error executing render function for action ${message.name}: ${e}`);\n          return (\n            <AssistantMessage\n              rawData={message}\n              data-message-role=\"assistant\"\n              key={index}\n              isLoading={false}\n              isGenerating={false}\n              subComponent={\n                <div className=\"copilotKitMessage copilotKitAssistantMessage\">\n                  <b>❌ Error executing render function for action {message.name}:</b>\n                  <pre>{e instanceof Error ? e.message : String(e)}</pre>\n                </div>\n              }\n            />\n          );\n        }\n      }\n    }\n    // No render function found- show the default message\n    else if (!inProgress || !isCurrentMessage) {\n      // Done - silent by default to avoid a series of \"done\" messages\n      return null;\n    } else {\n      // In progress\n      return (\n        <AssistantMessage\n          rawData={message}\n          key={index}\n          data-message-role=\"assistant\"\n          isLoading={true}\n          isGenerating={true}\n        />\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;;;AAAA,SAAS,yBAAyB;AAElC,SAA+B,yBAAyB;AAwB5C,cAwEM,YAxEN;AArBL,SAAS,6BAA6B,IAGtB;AAHsB,eAC3C;AAAA,sBAAAA,oBAAmB;AAAA,EANrB,IAK6C,IAExC,kBAFwC,IAExC;AAAA,IADH;AAAA;AAGA,QAAM,EAAE,oBAAoB,IAAI,kBAAkB;AAClD,QAAM,EAAE,SAAS,YAAY,OAAO,kBAAkB,aAAa,IAAI;AAEvE,MAAI,QAAQ,yBAAyB,GAAG;AACtC,QACE,oBAAoB,YAAY,SAC/B,oBAAoB,QAAQ,QAAQ,QAAQ,IAAI,KAC/C,oBAAoB,QAAQ,QAAQ,GAAG,IACzC;AACA,YAAM,SACJ,oBAAoB,QAAQ,QAAQ,QAAQ,IAAI,KAChD,oBAAoB,QAAQ,QAAQ,GAAG;AAEzC,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,oBAAoB,YAAY;AAClC,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cAET,qBAAkB;AAAA,cAClB,WAAW;AAAA,cACX,cAAc;AAAA,cACd,SAAS;AAAA;AAAA,YAJJ;AAAA,UAKP;AAAA,QAEJ,OAEK;AACH,iBAAO;AAAA,QACT;AAAA,MACF,OAEK;AACH,cAAM,OAAO,QAAQ;AAErB,YAAI,SAA+B;AAEnC,YAAI,iBAAiB,QAAW;AAC9B,mBAAS;AAAA,QACX,WAAW,QAAQ,OAAO,SAAS,kBAAkB,SAAS;AAC5D,mBAAS;AAAA,QACX;AAEA,YAAI;AACF,gBAAM,WAAW,OAAO;AAAA,YACtB;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,MAAM,QAAQ;AAAA,UAChB,CAAC;AAED,cAAI,CAAC,YAAY,WAAW,YAAY;AACtC,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,aAAa,UAAU;AAChC,mBACE;AAAA,cAACA;AAAA,cAAA;AAAA,gBACC,SAAS;AAAA,gBACT,qBAAkB;AAAA,gBAElB,WAAW;AAAA,gBACX,cAAc;AAAA,gBACd,SAAS;AAAA;AAAA,cAHJ;AAAA,YAIP;AAAA,UAEJ,OAAO;AACL,mBACE;AAAA,cAACA;AAAA,cAAA;AAAA,gBACC,SAAS;AAAA,gBACT,qBAAkB;AAAA,gBAElB,WAAW;AAAA,gBACX,cAAc;AAAA,gBACd,cAAc;AAAA;AAAA,cAHT;AAAA,YAIP;AAAA,UAEJ;AAAA,QACF,SAAS,GAAP;AACA,kBAAQ,MAAM,8CAA8C,QAAQ,SAAS,GAAG;AAChF,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,qBAAkB;AAAA,cAElB,WAAW;AAAA,cACX,cAAc;AAAA,cACd,cACE,qBAAC,SAAI,WAAU,gDACb;AAAA,qCAAC,OAAE;AAAA;AAAA,kBAA8C,QAAQ;AAAA,kBAAK;AAAA,mBAAC;AAAA,gBAC/D,oBAAC,SAAK,uBAAa,QAAQ,EAAE,UAAU,OAAO,CAAC,GAAE;AAAA,iBACnD;AAAA;AAAA,YAPG;AAAA,UASP;AAAA,QAEJ;AAAA,MACF;AAAA,IACF,WAES,CAAC,cAAc,CAAC,kBAAkB;AAEzC,aAAO;AAAA,IACT,OAAO;AAEL,aACE;AAAA,QAACA;AAAA,QAAA;AAAA,UACC,SAAS;AAAA,UAET,qBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,cAAc;AAAA;AAAA,QAHT;AAAA,MAIP;AAAA,IAEJ;AAAA,EACF;AACF;", "names": ["AssistantM<PERSON><PERSON>"]}