/**
 * Supabase Server Client Factory
 * Server-side client creation for authentication and database operations
 */

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '../supabase/database.types';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Creates a Supabase client for server-side operations with user authentication
 * Uses the anon key and respects RLS policies
 *
 * @returns A Supabase client for authenticated operations
 */
export async function createClient(): Promise<SupabaseClient<Database>> {
  const cookieStore = await cookies();

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async get(name: string): Promise<string> {
          const cookie = await cookieStore.get(name);
          return cookie?.value || '';
        },
        async set(name: string, value: string, options: Record<string, unknown>): Promise<void> {
          try {
            await cookieStore.set(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        async remove(name: string): Promise<void> {
          try {
            await cookieStore.delete(name);
          } catch (error) {
            console.error('Error deleting cookie:', error);
          }
        }
      }
    }
  );
}

/**
 * Creates a Supabase client for server-side operations with synchronous cookies
 * Used in API routes where cookies() is available synchronously
 *
 * @returns A Supabase client for API route operations
 */
export function createServerClientForUser(): SupabaseClient<Database> {
  const cookieStore = cookies();

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // @ts-expect-error - We know this is a ReadonlyRequestCookies
          const cookie = cookieStore.get?.(name);
          return cookie?.value || '';
        },
        set(name: string, value: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.set?.(name, value, options);
          } catch (error) {
            console.error('Error setting cookie:', error);
          }
        },
        remove(name: string, options: any) {
          try {
            // @ts-expect-error - We know this is a ReadonlyRequestCookies
            cookieStore.delete?.(name);
          } catch (error) {
            console.error('Error deleting cookie:', error);
          }
        }
      }
    }
  );
}

/**
 * Creates a Supabase client with service role for admin operations
 * Bypasses RLS policies - use with extreme caution
 *
 * @returns A Supabase client with service role privileges
 */
export function createServiceClient(): SupabaseClient<Database> {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      cookies: {
        get: (_name: string) => undefined,
        set: (_name: string, _value: string, _options: any) => {},
        remove: (_name: string, _options: any) => {}
      }
    }
  );
}
