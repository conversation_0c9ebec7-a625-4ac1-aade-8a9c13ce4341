import * as _oclif_core_interfaces from '@oclif/core/interfaces';
import { Config } from '@oclif/core';
import { AuthService } from '../services/auth.service.js';
import { TunnelService } from '../services/tunnel.service.js';
import { BaseCommand } from './base-command.js';
import 'localtunnel';

declare class Dev extends BaseCommand {
    private authService;
    private tunnelService;
    static flags: {
        port: _oclif_core_interfaces.OptionFlag<string, _oclif_core_interfaces.CustomOptions>;
        project: _oclif_core_interfaces.OptionFlag<string | undefined, _oclif_core_interfaces.CustomOptions>;
    };
    static description: string;
    static examples: string[];
    private trpcClient;
    private copilotCloudTunnelId;
    constructor(argv: string[], config: Config, authService?: AuthService, tunnelService?: TunnelService);
    private pingTunnelRecursively;
    run(): Promise<void>;
    private setupTunnel;
}

export { Dev as default };
