import { ForwardedParametersInput } from '@copilotkit/runtime-client-gql';
import { FrontendAction } from '../types/frontend-action.js';
import { a as CopilotContextParams } from '../copilot-context-8fb74a85.js';
import '@copilotkit/shared';
import 'react';
import '../hooks/use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';

/**
 * This class is used to execute one-off tasks, for example on button press. It can use the context available via [useCopilotReadable](/reference/hooks/useCopilotReadable) and the actions provided by [useCopilotAction](/reference/hooks/useCopilotAction), or you can provide your own context and actions.
 *
 * ## Example
 * In the simplest case, use CopilotTask in the context of your app by giving it instructions on what to do.
 *
 * ```tsx
 * import { CopilotTask, useCopilotContext } from "@copilotkit/react-core";
 *
 * export function MyComponent() {
 *   const context = useCopilotContext();
 *
 *   const task = new CopilotTask({
 *     instructions: "Set a random message",
 *     actions: [
 *       {
 *         name: "setMessage",
 *       description: "Set the message.",
 *       argumentAnnotations: [
 *         {
 *           name: "message",
 *           type: "string",
 *           description:
 *             "A message to display.",
 *           required: true,
 *         },
 *       ],
 *      }
 *     ]
 *   });
 *
 *   const executeTask = async () => {
 *     await task.run(context, action);
 *   }
 *
 *   return (
 *     <>
 *       <button onClick={executeTask}>
 *         Execute task
 *       </button>
 *     </>
 *   )
 * }
 * ```
 *
 * Have a look at the [Presentation Example App](https://github.com/CopilotKit/CopilotKit/blob/main/CopilotKit/examples/next-openai/src/app/presentation/page.tsx) for a more complete example.
 */

interface CopilotTaskConfig {
    /**
     * The instructions to be given to the assistant.
     */
    instructions: string;
    /**
     * An array of action definitions that can be called.
     */
    actions?: FrontendAction<any>[];
    /**
     * Whether to include the copilot readable context in the task.
     */
    includeCopilotReadable?: boolean;
    /**
     * Whether to include actions defined via useCopilotAction in the task.
     */
    includeCopilotActions?: boolean;
    /**
     * The forwarded parameters to use for the task.
     */
    forwardedParameters?: ForwardedParametersInput;
}
declare class CopilotTask<T = any> {
    private instructions;
    private actions;
    private includeCopilotReadable;
    private includeCopilotActions;
    private forwardedParameters?;
    constructor(config: CopilotTaskConfig);
    /**
     * Run the task.
     * @param context The CopilotContext to use for the task. Use `useCopilotContext` to obtain the current context.
     * @param data The data to use for the task.
     */
    run(context: CopilotContextParams, data?: T): Promise<void>;
}

export { CopilotTask, CopilotTaskConfig };
