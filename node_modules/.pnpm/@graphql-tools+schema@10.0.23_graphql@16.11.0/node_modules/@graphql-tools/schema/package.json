{"name": "@graphql-tools/schema", "version": "10.0.23", "description": "A set of utils for faster development of GraphQL tools", "sideEffects": false, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}, "dependencies": {"@graphql-tools/merge": "^9.0.24", "@graphql-tools/utils": "^10.8.6", "tslib": "^2.4.0"}, "repository": {"type": "git", "url": "ardatan/graphql-tools", "directory": "packages/schema"}, "license": "MIT", "engines": {"node": ">=16.0.0"}, "main": "cjs/index.js", "module": "esm/index.js", "typings": "typings/index.d.ts", "typescript": {"definition": "typings/index.d.ts"}, "type": "module", "exports": {".": {"require": {"types": "./typings/index.d.cts", "default": "./cjs/index.js"}, "import": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}, "default": {"types": "./typings/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./typings/*.d.cts", "default": "./cjs/*.js"}, "import": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}, "default": {"types": "./typings/*.d.ts", "default": "./esm/*.js"}}, "./package.json": "./package.json"}}