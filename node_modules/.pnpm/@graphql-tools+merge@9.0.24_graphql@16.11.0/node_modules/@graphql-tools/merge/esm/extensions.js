import { mergeDeep } from '@graphql-tools/utils';
export { extractExtensionsFromSchema } from '@graphql-tools/utils';
export function mergeExtensions(extensions) {
    return mergeDeep(extensions, false, true);
}
function applyExtensionObject(obj, extensions) {
    if (!obj || !extensions || extensions === obj.extensions) {
        return;
    }
    if (!obj.extensions) {
        obj.extensions = extensions;
        return;
    }
    obj.extensions = mergeDeep([obj.extensions, extensions], false, true);
}
export function applyExtensions(schema, extensions) {
    applyExtensionObject(schema, extensions.schemaExtensions);
    for (const [typeName, data] of Object.entries(extensions.types || {})) {
        const type = schema.getType(typeName);
        if (type) {
            applyExtensionObject(type, data.extensions);
            if (data.type === 'object' || data.type === 'interface') {
                for (const [fieldName, fieldData] of Object.entries(data.fields)) {
                    const field = type.getFields()[fieldName];
                    if (field) {
                        applyExtensionObject(field, fieldData.extensions);
                        for (const [arg, argData] of Object.entries(fieldData.arguments)) {
                            applyExtensionObject(field.args.find(a => a.name === arg), argData);
                        }
                    }
                }
            }
            else if (data.type === 'input') {
                for (const [fieldName, fieldData] of Object.entries(data.fields)) {
                    const field = type.getFields()[fieldName];
                    applyExtensionObject(field, fieldData.extensions);
                }
            }
            else if (data.type === 'enum') {
                for (const [valueName, valueData] of Object.entries(data.values)) {
                    const value = type.getValue(valueName);
                    applyExtensionObject(value, valueData);
                }
            }
        }
    }
    return schema;
}
