// Copyright 2021-2025 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import { fileDesc } from "../../../../codegenv2/file.js";
import { messageDesc } from "../../../../codegenv2/message.js";
/**
 * Describes the file google/protobuf/wrappers.proto.
 */
export const file_google_protobuf_wrappers = /*@__PURE__*/ fileDesc("Ch5nb29nbGUvcHJvdG9idWYvd3JhcHBlcnMucHJvdG8SD2dvb2dsZS5wcm90b2J1ZiIcCgtEb3VibGVWYWx1ZRINCgV2YWx1ZRgBIAEoASIbCgpGbG9hdFZhbHVlEg0KBXZhbHVlGAEgASgCIhsKCkludDY0VmFsdWUSDQoFdmFsdWUYASABKAMiHAoLVUludDY0VmFsdWUSDQoFdmFsdWUYASABKAQiGwoKSW50MzJWYWx1ZRINCgV2YWx1ZRgBIAEoBSIcCgtVSW50MzJWYWx1ZRINCgV2YWx1ZRgBIAEoDSIaCglCb29sVmFsdWUSDQoFdmFsdWUYASABKAgiHAoLU3RyaW5nVmFsdWUSDQoFdmFsdWUYASABKAkiGwoKQnl0ZXNWYWx1ZRINCgV2YWx1ZRgBIAEoDEKDAQoTY29tLmdvb2dsZS5wcm90b2J1ZkINV3JhcHBlcnNQcm90b1ABWjFnb29nbGUuZ29sYW5nLm9yZy9wcm90b2J1Zi90eXBlcy9rbm93bi93cmFwcGVyc3Bi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM");
/**
 * Describes the message google.protobuf.DoubleValue.
 * Use `create(DoubleValueSchema)` to create a new message.
 */
export const DoubleValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 0);
/**
 * Describes the message google.protobuf.FloatValue.
 * Use `create(FloatValueSchema)` to create a new message.
 */
export const FloatValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 1);
/**
 * Describes the message google.protobuf.Int64Value.
 * Use `create(Int64ValueSchema)` to create a new message.
 */
export const Int64ValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 2);
/**
 * Describes the message google.protobuf.UInt64Value.
 * Use `create(UInt64ValueSchema)` to create a new message.
 */
export const UInt64ValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 3);
/**
 * Describes the message google.protobuf.Int32Value.
 * Use `create(Int32ValueSchema)` to create a new message.
 */
export const Int32ValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 4);
/**
 * Describes the message google.protobuf.UInt32Value.
 * Use `create(UInt32ValueSchema)` to create a new message.
 */
export const UInt32ValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 5);
/**
 * Describes the message google.protobuf.BoolValue.
 * Use `create(BoolValueSchema)` to create a new message.
 */
export const BoolValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 6);
/**
 * Describes the message google.protobuf.StringValue.
 * Use `create(StringValueSchema)` to create a new message.
 */
export const StringValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 7);
/**
 * Describes the message google.protobuf.BytesValue.
 * Use `create(BytesValueSchema)` to create a new message.
 */
export const BytesValueSchema = /*@__PURE__*/ messageDesc(file_google_protobuf_wrappers, 8);
