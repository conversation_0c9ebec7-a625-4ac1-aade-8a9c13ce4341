/**
 * Escapes a string for use in an XPath expression.
 * Handles special characters, including single and double quotes.
 * @param value - The string to escape.
 * @returns The escaped string safe for XPath.
 */
export declare function escapeXPathString(value: string): string;
/**
 * Generates both a complicated XPath and a standard XPath for a given DOM element.
 * @param element - The target DOM element.
 * @param documentOverride - Optional document override.
 * @returns An object containing both XPaths.
 */
export declare function generateXPathsForElement(element: ChildNode): Promise<string[]>;
