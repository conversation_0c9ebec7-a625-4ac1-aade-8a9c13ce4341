"""
Avricons intake webhook router.

This module provides the webhook endpoint for receiving finished-call JSON
from the Voice service.
"""

import json
import os
from typing import Optional
from uuid import uuid4

from fastapi import APIRouter, Depends, Header, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from backend.api.schemas.intake_webhook import IntakeEventRequest, IntakeEventResponse
from backend.db.session import get_db_session
from backend.models.intake_event import IntakeEvent
from backend.utils.hmac_utils import verify_hmac_signature
from backend.utils.logging import get_logger

# Configure logging
logger = get_logger(__name__)

# Environment variables
USE_REDIS_QUEUE = os.getenv("USE_REDIS_QUEUE", "false").lower() == "true"

# Create router
router = APIRouter(prefix="/api/v1/avricons", tags=["avricons", "webhooks"])


@router.post("/intake", status_code=202, response_model=IntakeEventResponse)
async def intake_webhook(
    event: IntakeEventRequest,
    request: Request,
    db: AsyncSession = Depends(get_db_session),
    x_avr_signature: Optional[str] = Header(None, alias="X-Avr-Signature")
) -> IntakeEventResponse:
    """
    Receive intake webhook events from the Voice service.
    
    This endpoint receives finished-call JSON from the Voice service and stores it
    for background processing. The payload is verified using HMAC signature.
    
    Args:
        event: The intake event payload
        request: The FastAPI request object
        db: Database session
        x_avr_signature: HMAC signature header for verification
    
    Returns:
        IntakeEventResponse: Acknowledgment response with event ID
    
    Raises:
        HTTPException: 401 if HMAC verification fails, 403 if feature not enabled
    """
    # Get raw request body for HMAC verification
    raw_body = await request.body()
    
    # Verify HMAC signature
    if not verify_hmac_signature(raw_body, x_avr_signature or ""):
        logger.warning(
            "Invalid HMAC signature for intake webhook",
            extra={
                "tenant_id": str(event.tenant_id),
                "call_id": event.call_id,
                "signature_provided": bool(x_avr_signature)
            }
        )
        raise HTTPException(status_code=401, detail="Invalid signature")
    
    # TODO: Check if tenant has voice_intake feature enabled
    # This would typically involve checking tenant_subscriptions table
    # For now, we'll skip this check but log it for future implementation
    logger.info(
        "Received intake webhook event",
        extra={
            "tenant_id": str(event.tenant_id),
            "call_id": event.call_id,
            "call_status": event.call_status,
            "call_duration": event.call_duration
        }
    )
    
    # Generate unique event ID
    event_id = uuid4()
    
    try:
        if USE_REDIS_QUEUE:
            # TODO: Implement Redis queue integration
            # import redis
            # redis_client = get_redis_client()
            # await redis_client.xadd(
            #     "core:intake_queue",
            #     {
            #         "event_id": str(event_id),
            #         "tenant_id": str(event.tenant_id),
            #         "payload": event.model_dump_json()
            #     }
            # )
            logger.info(
                "Redis queue integration not yet implemented, falling back to database storage",
                extra={"event_id": str(event_id)}
            )
        
        # Store in database (either as primary storage or fallback)
        intake_event = IntakeEvent(
            id=event_id,
            tenant_id=event.tenant_id,
            payload=event.model_dump(),
            processed=False
        )
        
        db.add(intake_event)
        await db.commit()
        await db.refresh(intake_event)
        
        logger.info(
            "Intake event stored successfully",
            extra={
                "event_id": str(event_id),
                "tenant_id": str(event.tenant_id),
                "call_id": event.call_id
            }
        )
        
        return IntakeEventResponse(
            status="queued",
            event_id=event_id,
            message="Event queued for processing"
        )
        
    except Exception as e:
        logger.error(
            "Error storing intake event",
            extra={
                "event_id": str(event_id),
                "tenant_id": str(event.tenant_id),
                "call_id": event.call_id,
                "error": str(e)
            },
            exc_info=True
        )
        
        # Rollback the transaction
        await db.rollback()
        
        raise HTTPException(
            status_code=500,
            detail="Internal server error while processing webhook"
        )
