"use strict";
/**
 * (C) Copyright IBM Corp. 2019, 2020.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAuthenticatorFromEnvironment = exports.Cp4dTokenManager = exports.IamTokenManager = exports.CloudPakForDataAuthenticator = exports.IamAuthenticator = exports.BearerTokenAuthenticator = exports.BasicAuthenticator = exports.NoAuthAuthenticator = void 0;
var ibm_cloud_sdk_core_1 = require("ibm-cloud-sdk-core");
Object.defineProperty(exports, "NoAuthAuthenticator", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.NoAuthAuthenticator; } });
Object.defineProperty(exports, "BasicAuthenticator", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.BasicAuthenticator; } });
Object.defineProperty(exports, "BearerTokenAuthenticator", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.BearerTokenAuthenticator; } });
Object.defineProperty(exports, "IamAuthenticator", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.IamAuthenticator; } });
Object.defineProperty(exports, "CloudPakForDataAuthenticator", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.CloudPakForDataAuthenticator; } });
Object.defineProperty(exports, "IamTokenManager", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.IamTokenManager; } });
Object.defineProperty(exports, "Cp4dTokenManager", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.Cp4dTokenManager; } });
Object.defineProperty(exports, "getAuthenticatorFromEnvironment", { enumerable: true, get: function () { return ibm_cloud_sdk_core_1.getAuthenticatorFromEnvironment; } });
//# sourceMappingURL=index.js.map