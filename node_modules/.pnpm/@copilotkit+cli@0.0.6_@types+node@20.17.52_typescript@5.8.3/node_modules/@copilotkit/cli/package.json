{"name": "@copilotkit/cli", "description": "CopilotKit CLI", "version": "0.0.6", "author": "CopilotKit", "bin": {"copilotkit": "./bin/run.js", "cpk": "./bin/run.js"}, "bugs": "https://github.com/copilotkit/cli/issues", "scripts": {"clean": "shx rm -rf dist", "extract-version": "node -p \"'// This is auto generated!\\nexport const LIB_VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > src/utils/version.ts", "prebuild": "pnpm run clean && pnpm run extract-version", "build": "tsup", "lint": "echo oops", "generate-manifest": "oclif manifest", "preparez": "pnpm run build && pnpm run generate-manifest", "prepublishOnly": "pnpm run build && pnpm run generate-manifest", "test": "mocha --forbid-only --reporter spec \"test/**/*.test.ts\"", "version": "oclif readme && git add README.md", "validate": "node validate-package.js"}, "dependencies": {"@oclif/core": "^4.2.0", "@paralleldrive/cuid2": "^2.2.2", "@segment/analytics-node": "^2.1.2", "@sentry/node": "^7.116.0", "@trpc/client": "^11.0.0-rc.666", "@trpc/server": "^11.0.0-rc.666", "ansis": "^3.3.2", "axios": "^1.7.8", "chalk": "^5.3.0", "conf": "^13.1.0", "cors": "^2.8.5", "express": "^4.21.2", "get-port": "^7.1.0", "inquirer": "^12.3.0", "localtunnel": "^2.0.2", "open": "^10.1.0", "ora": "^8.1.1", "superjson": "^2.2.1", "zod": "^3.22.4"}, "devDependencies": {"@oclif/prettier-config": "^0.2.1", "@oclif/test": "^4", "@types/chai": "^4.3.20", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/localtunnel": "^2.0.4", "@types/mocha": "^10", "@types/node": "^18", "@types/sinon": "^17.0.3", "@types/sinon-chai": "^4.0.0", "chai": "^4", "eslint": "^8", "eslint-config-oclif": "^5", "eslint-config-oclif-typescript": "^3", "eslint-config-prettier": "^9", "mocha": "^10", "oclif": "^4", "shx": "^0.3.3", "sinon": "^19.0.2", "sinon-chai": "^4.0.0", "ts-node": "^10", "tsup": "^8.0.2", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}, "files": ["/bin", "/dist", "/oclif.manifest.json"], "homepage": "https://github.com/copilotkit/cli", "keywords": ["oclif"], "license": "MIT", "main": "dist/index.js", "type": "module", "oclif": {"bin": "copilotkit", "dirname": "copilotkit", "commands": "./dist/commands", "topicSeparator": " ", "topics": {"tunnel": {"description": "Create a local tunnel to expose your agent to the internet"}, "login": {"description": "Authenticate with Copilot Cloud"}}}, "repository": "https://github.com/copilotkit/cli", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}}