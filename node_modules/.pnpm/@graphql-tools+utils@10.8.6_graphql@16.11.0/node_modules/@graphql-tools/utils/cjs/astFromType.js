"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.astFromType = astFromType;
const cross_inspect_1 = require("cross-inspect");
const graphql_1 = require("graphql");
function astFromType(type) {
    if ((0, graphql_1.isNonNullType)(type)) {
        const innerType = astFromType(type.ofType);
        if (innerType.kind === graphql_1.Kind.NON_NULL_TYPE) {
            throw new Error(`Invalid type node ${(0, cross_inspect_1.inspect)(type)}. Inner type of non-null type cannot be a non-null type.`);
        }
        return {
            kind: graphql_1.Kind.NON_NULL_TYPE,
            type: innerType,
        };
    }
    else if ((0, graphql_1.isListType)(type)) {
        return {
            kind: graphql_1.Kind.LIST_TYPE,
            type: astFromType(type.ofType),
        };
    }
    return {
        kind: graphql_1.Kind.NAMED_TYPE,
        name: {
            kind: graphql_1.Kind.NAME,
            value: type.name,
        },
    };
}
