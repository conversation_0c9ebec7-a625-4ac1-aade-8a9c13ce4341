{"version": 3, "sources": ["../../../src/components/copilot-provider/copilotkit-props.tsx"], "sourcesContent": ["import { ForwardedParametersInput } from \"@copilotkit/runtime-client-gql\";\nimport { ReactNode } from \"react\";\nimport { AuthState } from \"../../context/copilot-context\";\n/**\n * Props for CopilotKit.\n */\n\nexport interface CopilotKitProps {\n  /**\n   *  Your Copilot Cloud API key. Don't have it yet? Go to https://cloud.copilotkit.ai and get one for free.\n   */\n  publicApiKey?: string;\n\n  /**\n   * Restrict input to a specific topic.\n   * @deprecated Use `guardrails_c` instead to control input restrictions\n   */\n  cloudRestrictToTopic?: {\n    validTopics?: string[];\n    invalidTopics?: string[];\n  };\n\n  /**\n   * Restrict input to specific topics using guardrails.\n   * @remarks\n   *\n   * This feature is only available when using CopilotKit's hosted cloud service. To use this feature, sign up at https://cloud.copilotkit.ai to get your publicApiKey. The feature allows restricting chat conversations to specific topics.\n   */\n  guardrails_c?: {\n    validTopics?: string[];\n    invalidTopics?: string[];\n  };\n\n  /**\n   * The endpoint for the Copilot Runtime instance. [Click here for more information](/concepts/copilot-runtime).\n   */\n  runtimeUrl?: string;\n\n  /**\n   * The endpoint for the Copilot transcribe audio service.\n   */\n  transcribeAudioUrl?: string;\n\n  /**\n   * The endpoint for the Copilot text to speech service.\n   */\n  textToSpeechUrl?: string;\n\n  /**\n   * Additional headers to be sent with the request.\n   *\n   * For example:\n   * ```json\n   * {\n   *   \"Authorization\": \"Bearer X\"\n   * }\n   * ```\n   */\n  headers?: Record<string, string>;\n\n  /**\n   * The children to be rendered within the CopilotKit.\n   */\n  children: ReactNode;\n\n  /**\n   * Custom properties to be sent with the request\n   * For example:\n   * ```js\n   * {\n   *   'user_id': 'users_id',\n   * }\n   * ```\n   */\n  properties?: Record<string, any>;\n\n  /**\n   * Indicates whether the user agent should send or receive cookies from the other domain\n   * in the case of cross-origin requests.\n   */\n  credentials?: RequestCredentials;\n\n  /**\n   * Whether to show the dev console.\n   *\n   * If set to \"auto\", the dev console will be show on localhost only.\n   */\n  showDevConsole?: boolean | \"auto\";\n\n  /**\n   * The name of the agent to use.\n   */\n  agent?: string;\n\n  /**\n   * The forwarded parameters to use for the task.\n   */\n  forwardedParameters?: Pick<ForwardedParametersInput, \"temperature\">;\n\n  /**\n   * The auth config to use for the CopilotKit.\n   * @remarks\n   *\n   * This feature is only available when using CopilotKit's hosted cloud service. To use this feature, sign up at https://cloud.copilotkit.ai to get your publicApiKey. The feature allows restricting chat conversations to specific topics.\n   */\n  authConfig_c?: {\n    SignInComponent: React.ComponentType<{\n      onSignInComplete: (authState: AuthState) => void;\n    }>;\n  };\n\n  /**\n   * The thread id to use for the CopilotKit.\n   */\n  threadId?: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;", "names": []}