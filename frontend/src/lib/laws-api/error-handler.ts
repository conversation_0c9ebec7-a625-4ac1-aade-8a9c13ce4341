/**
 * Laws-API Enhanced Error Handler
 * Handles specific error codes from laws-API with user-friendly messages and retry logic
 */

import { toast } from 'sonner';
import { LawsApiException } from '@/types/laws-api';
import { errorHandler } from '@/lib/error-handling/error-handler';
import { ErrorType, ErrorSeverity } from '@/lib/error-handling/error-types';

export interface LawsApiErrorHandlerOptions {
  showToast?: boolean;
  enableRetry?: boolean;
  context?: Record<string, any>;
}

export class LawsApiErrorHandler {
  private static instance: LawsApiErrorHandler;
  private retryCountdowns: Map<string, NodeJS.Timeout> = new Map();

  private constructor() {}

  public static getInstance(): LawsApiErrorHandler {
    if (!LawsApiErrorHandler.instance) {
      LawsApiErrorHandler.instance = new LawsApiErrorHandler();
    }
    return LawsApiErrorHandler.instance;
  }

  /**
   * Handle laws-API specific errors with enhanced UX
   */
  public handleLawsApiError(
    error: unknown,
    options: LawsApiErrorHandlerOptions = {}
  ): LawsApiException {
    const { showToast = true, enableRetry = true, context = {} } = options;

    // Convert to LawsApiException if needed
    const lawsApiError = this.normalizeLawsApiError(error);

    // Handle specific error codes
    switch (lawsApiError.status) {
      case 401:
        this.handleAuthenticationError(lawsApiError, showToast);
        break;
      case 403:
        this.handleAuthorizationError(lawsApiError, showToast);
        break;
      case 429:
        this.handleRateLimitError(lawsApiError, showToast, enableRetry);
        break;
      default:
        this.handleGenericError(lawsApiError, showToast);
        break;
    }

    // Report to global error handler
    errorHandler.handleError(lawsApiError, {
      ...context,
      source: 'laws-api',
      status: lawsApiError.status,
      code: lawsApiError.code
    });

    return lawsApiError;
  }

  /**
   * Handle 401 Authentication errors
   */
  private handleAuthenticationError(error: LawsApiException, showToast: boolean): void {
    if (showToast) {
      toast.error('Authentication Required', {
        description: 'Please log in to access legal search features.',
        action: {
          label: 'Login',
          onClick: () => {
            // Redirect to login page
            window.location.href = '/login';
          }
        },
        duration: 10000
      });
    }
  }

  /**
   * Handle 403 Authorization errors (jurisdiction restrictions)
   */
  private handleAuthorizationError(error: LawsApiException, showToast: boolean): void {
    if (showToast) {
      const isJurisdictionError = error.message.toLowerCase().includes('jurisdiction');
      
      toast.error('Access Restricted', {
        description: isJurisdictionError 
          ? 'You do not have access to search laws in the requested jurisdiction.'
          : 'You do not have permission to perform this action.',
        action: isJurisdictionError ? {
          label: 'Contact Support',
          onClick: () => {
            // Open support contact
            window.open('mailto:<EMAIL>?subject=Jurisdiction Access Request', '_blank');
          }
        } : undefined,
        duration: 8000
      });
    }
  }

  /**
   * Handle 429 Rate limit errors with countdown
   */
  private handleRateLimitError(
    error: LawsApiException, 
    showToast: boolean, 
    enableRetry: boolean
  ): void {
    const retryAfter = error.retryAfter || 60; // Default to 60 seconds
    
    if (showToast) {
      const toastId = `rate-limit-${Date.now()}`;
      
      // Clear any existing countdown for this type
      this.clearRetryCountdown('rate-limit');
      
      // Show initial toast with countdown
      this.showRateLimitToast(toastId, retryAfter, enableRetry);
      
      // Start countdown
      if (enableRetry) {
        this.startRetryCountdown('rate-limit', retryAfter, () => {
          toast.success('Rate limit cleared', {
            description: 'You can now make requests again.',
            duration: 3000
          });
        });
      }
    }
  }

  /**
   * Handle generic errors
   */
  private handleGenericError(error: LawsApiException, showToast: boolean): void {
    if (showToast) {
      toast.error('Search Error', {
        description: error.message || 'An unexpected error occurred while searching.',
        duration: 5000
      });
    }
  }

  /**
   * Show rate limit toast with countdown
   */
  private showRateLimitToast(toastId: string, retryAfter: number, enableRetry: boolean): void {
    let remainingTime = retryAfter;
    
    const updateToast = () => {
      toast.error('Rate Limit Exceeded', {
        id: toastId,
        description: `Too many requests. Please wait ${remainingTime} seconds before trying again.`,
        action: enableRetry ? {
          label: `Retry in ${remainingTime}s`,
          onClick: () => {
            // Disabled until countdown finishes
          }
        } : undefined,
        duration: Infinity // Keep toast visible during countdown
      });
    };

    // Initial toast
    updateToast();

    // Update countdown every second
    const countdownInterval = setInterval(() => {
      remainingTime--;
      if (remainingTime > 0) {
        updateToast();
      } else {
        clearInterval(countdownInterval);
        toast.dismiss(toastId);
      }
    }, 1000);
  }

  /**
   * Start retry countdown
   */
  private startRetryCountdown(
    key: string, 
    seconds: number, 
    onComplete: () => void
  ): void {
    const timeout = setTimeout(() => {
      this.retryCountdowns.delete(key);
      onComplete();
    }, seconds * 1000);
    
    this.retryCountdowns.set(key, timeout);
  }

  /**
   * Clear retry countdown
   */
  private clearRetryCountdown(key: string): void {
    const timeout = this.retryCountdowns.get(key);
    if (timeout) {
      clearTimeout(timeout);
      this.retryCountdowns.delete(key);
    }
  }

  /**
   * Normalize error to LawsApiException
   */
  private normalizeLawsApiError(error: unknown): LawsApiException {
    if (error instanceof LawsApiException) {
      return error;
    }

    if (error instanceof Error) {
      // Try to extract status code from error message or properties
      const statusMatch = error.message.match(/status:?\s*(\d+)/i);
      const status = statusMatch ? parseInt(statusMatch[1], 10) : 500;
      
      return new LawsApiException(
        error.message,
        'UNKNOWN_ERROR',
        status
      );
    }

    return new LawsApiException(
      String(error),
      'UNKNOWN_ERROR',
      500
    );
  }

  /**
   * Check if error is retryable
   */
  public isRetryable(error: LawsApiException): boolean {
    // Don't retry auth errors or client errors
    if (error.status >= 400 && error.status < 500) {
      return error.status === 429; // Only retry rate limits
    }
    
    // Retry server errors
    return error.status >= 500;
  }

  /**
   * Get retry delay for error
   */
  public getRetryDelay(error: LawsApiException, attempt: number): number {
    if (error.status === 429 && error.retryAfter) {
      return error.retryAfter * 1000; // Convert to milliseconds
    }
    
    // Exponential backoff for server errors
    return Math.min(1000 * Math.pow(2, attempt), 30000); // Max 30 seconds
  }
}

// Export singleton instance
export const lawsApiErrorHandler = LawsApiErrorHandler.getInstance();

// Convenience function for handling laws-API errors
export function handleLawsApiError(
  error: unknown,
  options?: LawsApiErrorHandlerOptions
): LawsApiException {
  return lawsApiErrorHandler.handleLawsApiError(error, options);
}
