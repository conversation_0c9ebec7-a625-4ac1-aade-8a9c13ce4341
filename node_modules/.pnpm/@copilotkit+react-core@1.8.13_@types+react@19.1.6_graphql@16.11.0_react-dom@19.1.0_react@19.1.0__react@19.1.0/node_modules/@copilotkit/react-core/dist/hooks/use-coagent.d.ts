import { a as CopilotContextParams } from '../copilot-context-8fb74a85.js';
import { CopilotMessagesContextParams } from '../context/copilot-messages-context.js';
import { Message } from '@copilotkit/runtime-client-gql';
import '@copilotkit/shared';
import '../types/frontend-action.js';
import 'react';
import './use-tree.js';
import '../types/document-pointer.js';
import '../types/chat-suggestion-configuration.js';
import '../types/coagent-action.js';
import '../types/coagent-state.js';

/**
 * <Callout type="info">
 *   Usage of this hook assumes some additional setup in your application, for more information
 *   on that see the CoAgents <span className="text-blue-500">[getting started guide](/coagents/quickstart/langgraph)</span>.
 * </Callout>
 * <Frame className="my-12">
 *   <img
 *     src="/images/coagents/SharedStateCoAgents.gif"
 *     alt="CoAgents demonstration"
 *     className="w-auto"
 *   />
 * </Frame>
 *
 * This hook is used to integrate an agent into your application. With its use, you can
 * render and update the state of an agent, allowing for a dynamic and interactive experience.
 * We call these shared state experiences agentic copilots, or CoAgents for short.
 *
 * ## Usage
 *
 * ### Simple Usage
 *
 * ```tsx
 * import { useCoAgent } from "@copilotkit/react-core";
 *
 * type AgentState = {
 *   count: number;
 * }
 *
 * const agent = useCoAgent<AgentState>({
 *   name: "my-agent",
 *   initialState: {
 *     count: 0,
 *   },
 * });
 *
 * ```
 *
 * `useCoAgent` returns an object with the following properties:
 *
 * ```tsx
 * const {
 *   name,     // The name of the agent currently being used.
 *   nodeName, // The name of the current LangGraph node.
 *   state,    // The current state of the agent.
 *   setState, // A function to update the state of the agent.
 *   running,  // A boolean indicating if the agent is currently running.
 *   start,    // A function to start the agent.
 *   stop,     // A function to stop the agent.
 *   run,      // A function to re-run the agent. Takes a HintFunction to inform the agent why it is being re-run.
 * } = agent;
 * ```
 *
 * Finally we can leverage these properties to create reactive experiences with the agent!
 *
 * ```tsx
 * const { state, setState } = useCoAgent<AgentState>({
 *   name: "my-agent",
 *   initialState: {
 *     count: 0,
 *   },
 * });
 *
 * return (
 *   <div>
 *     <p>Count: {state.count}</p>
 *     <button onClick={() => setState({ count: state.count + 1 })}>Increment</button>
 *   </div>
 * );
 * ```
 *
 * This reactivity is bidirectional, meaning that changes to the state from the agent will be reflected in the UI and vice versa.
 *
 * ## Parameters
 * <PropertyReference name="options" type="UseCoagentOptions<T>" required>
 *   The options to use when creating the coagent.
 *   <PropertyReference name="name" type="string" required>
 *     The name of the agent to use.
 *   </PropertyReference>
 *   <PropertyReference name="initialState" type="T | any">
 *     The initial state of the agent.
 *   </PropertyReference>
 *   <PropertyReference name="state" type="T | any">
 *     State to manage externally if you are using this hook with external state management.
 *   </PropertyReference>
 *   <PropertyReference name="setState" type="(newState: T | ((prevState: T | undefined) => T)) => void">
 *     A function to update the state of the agent if you are using this hook with external state management.
 *   </PropertyReference>
 * </PropertyReference>
 */

interface UseCoagentOptionsBase {
    /**
     * The name of the agent being used.
     */
    name: string;
    /**
     * @deprecated - use "config.configurable"
     * Config to pass to a LangGraph Agent
     */
    configurable?: Record<string, any>;
    /**
     * Config to pass to a LangGraph Agent
     */
    config?: {
        configurable?: Record<string, any>;
        [key: string]: any;
    };
}
interface WithInternalStateManagementAndInitial<T> extends UseCoagentOptionsBase {
    /**
     * The initial state of the agent.
     */
    initialState: T;
}
interface WithInternalStateManagement extends UseCoagentOptionsBase {
    /**
     * Optional initialState with default type any
     */
    initialState?: any;
}
interface WithExternalStateManagement<T> extends UseCoagentOptionsBase {
    /**
     * The current state of the agent.
     */
    state: T;
    /**
     * A function to update the state of the agent.
     */
    setState: (newState: T | ((prevState: T | undefined) => T)) => void;
}
type UseCoagentOptions<T> = WithInternalStateManagementAndInitial<T> | WithInternalStateManagement | WithExternalStateManagement<T>;
interface UseCoagentReturnType<T> {
    /**
     * The name of the agent being used.
     */
    name: string;
    /**
     * The name of the current LangGraph node.
     */
    nodeName?: string;
    /**
     * The ID of the thread the agent is running in.
     */
    threadId?: string;
    /**
     * A boolean indicating if the agent is currently running.
     */
    running: boolean;
    /**
     * The current state of the agent.
     */
    state: T;
    /**
     * A function to update the state of the agent.
     */
    setState: (newState: T | ((prevState: T | undefined) => T)) => void;
    /**
     * A function to start the agent.
     */
    start: () => void;
    /**
     * A function to stop the agent.
     */
    stop: () => void;
    /**
     * A function to re-run the agent. The hint function can be used to provide a hint to the agent
     * about why it is being re-run again.
     */
    run: (hint?: HintFunction) => Promise<void>;
}
interface HintFunctionParams {
    /**
     * The previous state of the agent.
     */
    previousState: any;
    /**
     * The current state of the agent.
     */
    currentState: any;
}
type HintFunction = (params: HintFunctionParams) => Message | undefined;
/**
 * This hook is used to integrate an agent into your application. With its use, you can
 * render and update the state of the agent, allowing for a dynamic and interactive experience.
 * We call these shared state experiences "agentic copilots". To get started using agentic copilots, which
 * we refer to as CoAgents, checkout the documentation at https://docs.copilotkit.ai/coagents/quickstart/langgraph.
 */
declare function useCoAgent<T = any>(options: UseCoagentOptions<T>): UseCoagentReturnType<T>;
declare function startAgent(name: string, context: CopilotContextParams): void;
declare function stopAgent(name: string, context: CopilotContextParams): void;
declare function runAgent(name: string, context: CopilotContextParams & CopilotMessagesContextParams, appendMessage: (message: Message) => Promise<void>, runChatCompletion: () => Promise<Message[]>, hint?: HintFunction): Promise<void>;

export { HintFunction, HintFunctionParams, UseCoagentReturnType, runAgent, startAgent, stopAgent, useCoAgent };
