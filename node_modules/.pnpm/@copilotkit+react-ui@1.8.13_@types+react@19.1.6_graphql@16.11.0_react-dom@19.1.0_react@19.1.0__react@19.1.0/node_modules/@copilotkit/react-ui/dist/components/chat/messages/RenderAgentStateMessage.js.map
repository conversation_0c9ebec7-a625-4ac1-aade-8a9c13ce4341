{"version": 3, "sources": ["../../../../src/components/chat/messages/RenderAgentStateMessage.tsx", "../../../../src/components/chat/ChatContext.tsx", "../../../../src/components/chat/Icons.tsx", "../../../../src/components/chat/Markdown.tsx", "../../../../src/components/chat/CodeBlock.tsx", "../../../../src/hooks/use-copy-to-clipboard.tsx", "../../../../src/components/chat/messages/AssistantMessage.tsx"], "sourcesContent": ["import { RenderMessageProps } from \"../props\";\nimport { CoagentInChatRenderFunction, useCopilotContext } from \"@copilotkit/react-core\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderAgentStateMessage({\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const { chatComponentsCache } = useCopilotContext();\n  const { message, inProgress, index, isCurrentMessage } = props;\n\n  if (message.isAgentStateMessage()) {\n    let render: string | CoagentInChatRenderFunction | undefined;\n\n    if (chatComponentsCache.current !== null) {\n      render =\n        chatComponentsCache.current.coAgentStateRenders[\n          `${message.agentName}-${message.nodeName}`\n        ] || chatComponentsCache.current.coAgentStateRenders[`${message.agentName}-global`];\n    }\n\n    if (render) {\n      // render a static string\n      if (typeof render === \"string\") {\n        // when render is static, we show it only when in progress\n        if (isCurrentMessage && inProgress) {\n          return (\n            <AssistantMessage\n              rawData={message}\n              message={render}\n              data-message-role=\"assistant\"\n              key={index}\n              isLoading={true}\n              isGenerating={true}\n            />\n          );\n        }\n        // Done - silent by default to avoid a series of \"done\" messages\n        else {\n          return null;\n        }\n      }\n      // render is a function\n      else {\n        const state = message.state;\n\n        let status = message.active ? \"inProgress\" : \"complete\";\n\n        const toRender = render({\n          status: status as any,\n          state,\n          nodeName: message.nodeName,\n        });\n\n        // No result and complete: stay silent\n        if (!toRender && status === \"complete\") {\n          return null;\n        }\n\n        if (!toRender && isCurrentMessage && inProgress) {\n          return (\n            <AssistantMessage\n              data-message-role=\"assistant\"\n              key={index}\n              rawData={message}\n              isLoading={true}\n              isGenerating={true}\n            />\n          );\n        } else if (!toRender) {\n          return null;\n        }\n\n        if (typeof toRender === \"string\") {\n          return (\n            <AssistantMessage\n              rawData={message}\n              message={toRender}\n              isLoading={true}\n              isGenerating={true}\n              data-message-role=\"assistant\"\n              key={index}\n            />\n          );\n        } else {\n          return (\n            <AssistantMessage\n              rawData={message}\n              data-message-role=\"agent-state-render\"\n              key={index}\n              isLoading={false}\n              isGenerating={false}\n              subComponent={toRender}\n            />\n          );\n        }\n      }\n    }\n    // No render function found- show the default message\n    else if (!inProgress || !isCurrentMessage) {\n      // Done - silent by default to avoid a series of \"done\" messages\n      return null;\n    } else {\n      // In progress\n      return (\n        <AssistantMessage\n          rawData={message}\n          isLoading={true}\n          isGenerating={true}\n          data-message-role=\"assistant\"\n          key={index}\n        />\n      );\n    }\n  }\n}\n", "import React, { use<PERSON>em<PERSON>, useState } from \"react\";\nimport * as DefaultIcons from \"./Icons\";\nimport { ThumbsDownIcon, ThumbsUpIcon } from \"./Icons\";\n\n/**\n * Icons for CopilotChat component.\n */\nexport interface CopilotChatIcons {\n  /**\n   * The icon to use for the open chat button.\n   * @default <OpenIcon />\n   */\n  openIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the close chat button.\n   * @default <CloseIcon />\n   */\n  closeIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the close chat button in the header.\n   * @default <HeaderCloseIcon />\n   */\n  headerCloseIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the send button.\n   * @default <SendIcon />\n   */\n  sendIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the activity indicator.\n   * @default <ActivityIcon />\n   */\n  activityIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the spinner.\n   * @default <SpinnerIcon />\n   */\n  spinnerIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the stop button.\n   * @default <StopIcon />\n   */\n  stopIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the regenerate button.\n   * @default <RegenerateIcon />\n   */\n  regenerateIcon?: React.ReactNode;\n\n  /**\n   * The icons to use for push to talk.\n   * @default <PushToTalkIcon />\n   */\n\n  pushToTalkIcon?: React.ReactNode;\n\n  /**\n   * The icons to use for copy assistant response\n   * @default <CopyIcon />\n   */\n\n  copyIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for thumbs up/response approval.\n   * @default <ThumbsUpIcon />\n   */\n\n  thumbsUpIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for thumbs down/response rejection.\n   * @default <ThumbsDownIcon />\n   */\n\n  thumbsDownIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the upload button.\n   * @default <UploadIcon />\n   */\n  uploadIcon?: React.ReactNode;\n}\n\n/**\n * Labels for CopilotChat component.\n */\nexport interface CopilotChatLabels {\n  /**\n   * The initial message(s) to display in the chat window.\n   */\n  initial?: string | string[];\n\n  /**\n   * The title to display in the header.\n   * @default \"CopilotKit\"\n   */\n  title?: string;\n\n  /**\n   * The placeholder to display in the input.\n   * @default \"Type a message...\"\n   */\n  placeholder?: string;\n\n  /**\n   * The message to display when an error occurs.\n   * @default \"❌ An error occurred. Please try again.\"\n   */\n  error?: string;\n\n  /**\n   * The label to display on the stop button.\n   * @default \"Stop generating\"\n   */\n  stopGenerating?: string;\n\n  /**\n   * The label to display on the regenerate button.\n   * @default \"Regenerate response\"\n   */\n  regenerateResponse?: string;\n\n  /**\n   * The label for the copy button.\n   * @default \"Copy to clipboard\"\n   */\n  copyToClipboard?: string;\n\n  /**\n   * The label for the thumbs up button.\n   * @default \"Thumbs up\"\n   */\n  thumbsUp?: string;\n\n  /**\n   * The label for the thumbs down button.\n   * @default \"Thumbs down\"\n   */\n  thumbsDown?: string;\n\n  /**\n   * The text to display when content is copied.\n   * @default \"Copied!\"\n   */\n  copied?: string;\n}\n\ninterface ChatContext {\n  labels: Required<CopilotChatLabels>;\n  icons: Required<CopilotChatIcons>;\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nexport const ChatContext = React.createContext<ChatContext | undefined>(undefined);\n\nexport function useChatContext(): ChatContext {\n  const context = React.useContext(ChatContext);\n  if (context === undefined) {\n    throw new Error(\n      \"Context not found. Did you forget to wrap your app in a <ChatContextProvider> component?\",\n    );\n  }\n  return context;\n}\n\ninterface ChatContextProps {\n  // temperature?: number;\n  // instructions?: string;\n  // maxFeedback?: number;\n  labels?: CopilotChatLabels;\n  icons?: CopilotChatIcons;\n  children?: React.ReactNode;\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nexport const ChatContextProvider = ({\n  // temperature,\n  // instructions,\n  // maxFeedback,\n  labels,\n  icons,\n  children,\n  open,\n  setOpen,\n}: ChatContextProps) => {\n  const memoizedLabels = useMemo(\n    () => ({\n      ...{\n        initial: \"\",\n        title: \"CopilotKit\",\n        placeholder: \"Type a message...\",\n        error: \"❌ An error occurred. Please try again.\",\n        stopGenerating: \"Stop generating\",\n        regenerateResponse: \"Regenerate response\",\n        copyToClipboard: \"Copy to clipboard\",\n        thumbsUp: \"Thumbs up\",\n        thumbsDown: \"Thumbs down\",\n        copied: \"Copied!\",\n      },\n      ...labels,\n    }),\n    [labels],\n  );\n\n  const memoizedIcons = useMemo(\n    () => ({\n      ...{\n        openIcon: DefaultIcons.OpenIcon,\n        closeIcon: DefaultIcons.CloseIcon,\n        headerCloseIcon: DefaultIcons.HeaderCloseIcon,\n        sendIcon: DefaultIcons.SendIcon,\n        activityIcon: DefaultIcons.ActivityIcon,\n        spinnerIcon: DefaultIcons.SpinnerIcon,\n        stopIcon: DefaultIcons.StopIcon,\n        regenerateIcon: DefaultIcons.RegenerateIcon,\n        pushToTalkIcon: DefaultIcons.MicrophoneIcon,\n        copyIcon: DefaultIcons.CopyIcon,\n        thumbsUpIcon: DefaultIcons.ThumbsUpIcon,\n        thumbsDownIcon: DefaultIcons.ThumbsDownIcon,\n        uploadIcon: DefaultIcons.UploadIcon,\n      },\n      ...icons,\n    }),\n    [icons],\n  );\n\n  const context = useMemo(\n    () => ({\n      labels: memoizedLabels,\n      icons: memoizedIcons,\n      open,\n      setOpen,\n    }),\n    [memoizedLabels, memoizedIcons, open, setOpen],\n  );\n\n  return <ChatContext.Provider value={context}>{children}</ChatContext.Provider>;\n};\n", "import React from \"react\";\n\nexport const OpenIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <g transform=\"translate(24, 0) scale(-1, 1)\">\n      <path\n        fillRule=\"evenodd\"\n        d=\"M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z\"\n        clipRule=\"evenodd\"\n      />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\n  </svg>\n);\n\nexport const HeaderCloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const SendIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 19V5m0 0l-7 7m7-7l7 7\" />\n  </svg>\n);\n\nexport const MicrophoneIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z\"\n    />\n  </svg>\n);\n\nexport const StopIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z\"\n    />\n  </svg>\n);\n\nexport const RegenerateIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n    />\n  </svg>\n);\n\nexport const CopyIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\n    />\n  </svg>\n);\n\nexport const SmallSpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"13px\", height: \"13px\" }}></span>\n);\n\nexport const SpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"24px\", height: \"24px\" }}></span>\n);\n\nexport const ActivityIcon = (\n  <div style={{ display: \"flex\", alignItems: \"center\", gap: \"4px\" }}>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.2s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.4s\" }}></span>\n  </div>\n);\n\nexport const ThumbsUpIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z\"\n    />\n  </svg>\n);\n\nexport const ThumbsDownIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384\"\n    />\n  </svg>\n);\n\nexport const DownloadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\n    />\n  </svg>\n);\n\nexport const UploadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n  </svg>\n);\n", "import { FC, memo } from \"react\";\nimport ReactMarkdown, { Options, Components } from \"react-markdown\";\nimport { CodeBlock } from \"./CodeBlock\";\nimport remarkGfm from \"remark-gfm\";\nimport remarkMath from \"remark-math\";\nimport rehypeRaw from \"rehype-raw\";\n\nconst defaultComponents: Components = {\n  a({ children, ...props }) {\n    return (\n      <a className=\"copilotKitMarkdownElement\" {...props} target=\"_blank\" rel=\"noopener noreferrer\">\n        {children}\n      </a>\n    );\n  },\n  // @ts-expect-error -- inline\n  code({ children, className, inline, ...props }) {\n    if (Array.isArray(children) && children.length) {\n      if (children[0] == \"▍\") {\n        return (\n          <span\n            style={{\n              animation: \"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite\",\n              marginTop: \"0.25rem\",\n            }}\n          >\n            ▍\n          </span>\n        );\n      }\n\n      children[0] = (children?.[0] as string).replace(\"`▍`\", \"▍\");\n    }\n\n    const match = /language-(\\w+)/.exec(className || \"\");\n\n    if (inline) {\n      return (\n        <code className={className} {...props}>\n          {children}\n        </code>\n      );\n    }\n\n    return (\n      <CodeBlock\n        key={Math.random()}\n        language={(match && match[1]) || \"\"}\n        value={String(children).replace(/\\n$/, \"\")}\n        {...props}\n      />\n    );\n  },\n  h1: ({ children, ...props }) => (\n    <h1 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h1>\n  ),\n  h2: ({ children, ...props }) => (\n    <h2 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h2>\n  ),\n  h3: ({ children, ...props }) => (\n    <h3 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h3>\n  ),\n  h4: ({ children, ...props }) => (\n    <h4 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h4>\n  ),\n  h5: ({ children, ...props }) => (\n    <h5 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h5>\n  ),\n  h6: ({ children, ...props }) => (\n    <h6 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h6>\n  ),\n  p: ({ children, ...props }) => (\n    <p className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </p>\n  ),\n  pre: ({ children, ...props }) => (\n    <pre className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </pre>\n  ),\n  blockquote: ({ children, ...props }) => (\n    <blockquote className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </blockquote>\n  ),\n  ul: ({ children, ...props }) => (\n    <ul className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </ul>\n  ),\n  li: ({ children, ...props }) => (\n    <li className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </li>\n  ),\n};\n\nconst MemoizedReactMarkdown: FC<Options> = memo(\n  ReactMarkdown,\n  (prevProps, nextProps) =>\n    prevProps.children === nextProps.children && prevProps.components === nextProps.components,\n);\n\ntype MarkdownProps = {\n  content: string;\n  components?: Components;\n};\n\nexport const Markdown = ({ content, components }: MarkdownProps) => {\n  return (\n    <div className=\"copilotKitMarkdown\">\n      <MemoizedReactMarkdown\n        components={{ ...defaultComponents, ...components }}\n        remarkPlugins={[remarkGfm, remarkMath]}\n        rehypePlugins={[rehypeRaw]}\n      >\n        {content}\n      </MemoizedReactMarkdown>\n    </div>\n  );\n};\n", "import { FC, memo } from \"react\";\nimport { Prism as Synta<PERSON><PERSON><PERSON><PERSON>er } from \"react-syntax-highlighter\";\nimport { useCopyToClipboard } from \"../../hooks/use-copy-to-clipboard\";\nimport { CheckIcon, CopyIcon, DownloadIcon } from \"./Icons\";\n\ninterface CodeActionButtonProps {\n  onClick: () => void;\n  children: React.ReactNode;\n}\n\ninterface Props {\n  language: string;\n  value: string;\n}\n\ninterface languageMap {\n  [key: string]: string | undefined;\n}\n\nexport const programmingLanguages: languageMap = {\n  javascript: \".js\",\n  python: \".py\",\n  java: \".java\",\n  c: \".c\",\n  cpp: \".cpp\",\n  \"c++\": \".cpp\",\n  \"c#\": \".cs\",\n  ruby: \".rb\",\n  php: \".php\",\n  swift: \".swift\",\n  \"objective-c\": \".m\",\n  kotlin: \".kt\",\n  typescript: \".ts\",\n  go: \".go\",\n  perl: \".pl\",\n  rust: \".rs\",\n  scala: \".scala\",\n  haskell: \".hs\",\n  lua: \".lua\",\n  shell: \".sh\",\n  sql: \".sql\",\n  html: \".html\",\n  css: \".css\",\n  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component\n};\n\nexport const generateRandomString = (length: number, lowercase = false) => {\n  const chars = \"ABCDEFGHJKLMNPQRSTUVWXY3456789\"; // excluding similar looking characters like Z, 2, I, 1, O, 0\n  let result = \"\";\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return lowercase ? result.toLowerCase() : result;\n};\n\nconst CodeBlock: FC<Props> = memo(({ language, value }) => {\n  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 2000 });\n\n  const downloadAsFile = () => {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n    const fileExtension = programmingLanguages[language] || \".file\";\n    const suggestedFileName = `file-${generateRandomString(3, true)}${fileExtension}`;\n    const fileName = window.prompt(\"Enter file name\" || \"\", suggestedFileName);\n\n    if (!fileName) {\n      // User pressed cancel on prompt.\n      return;\n    }\n\n    const blob = new Blob([value], { type: \"text/plain\" });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.download = fileName;\n    link.href = url;\n    link.style.display = \"none\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const onCopy = () => {\n    if (isCopied) return;\n    copyToClipboard(value);\n  };\n\n  return (\n    <div className=\"copilotKitCodeBlock\">\n      <div className=\"copilotKitCodeBlockToolbar\">\n        <span className=\"copilotKitCodeBlockToolbarLanguage\">{language}</span>\n        <div className=\"copilotKitCodeBlockToolbarButtons\">\n          <button className=\"copilotKitCodeBlockToolbarButton\" onClick={downloadAsFile}>\n            {DownloadIcon}\n          </button>\n          <button className=\"copilotKitCodeBlockToolbarButton\" onClick={onCopy}>\n            {isCopied ? CheckIcon : CopyIcon}\n          </button>\n        </div>\n      </div>\n      <SyntaxHighlighter\n        language={language}\n        style={highlightStyle}\n        PreTag=\"div\"\n        customStyle={{\n          margin: 0,\n          borderBottomLeftRadius: \"0.375rem\",\n          borderBottomRightRadius: \"0.375rem\",\n        }}\n      >\n        {value}\n      </SyntaxHighlighter>\n    </div>\n  );\n});\nCodeBlock.displayName = \"CodeBlock\";\n\nexport { CodeBlock };\n\n// import { vscDarkPlus as highlightStyle } from \"react-syntax-highlighter/dist/esm/styles/prism\";\n// As a workaround, we inline the vscDarkPlus from react-syntax-highlighter.\n// Importing it as recommended in the documentation leads to build errors in the non app router\n// (Next.js classic) setup.\nconst highlightStyle: any = {\n  'pre[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n    padding: \"1em\",\n    margin: \".5em 0\",\n    overflow: \"auto\",\n    background: \"#1e1e1e\",\n  },\n  'code[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n  },\n  'pre[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'code[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'pre[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'code[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    padding: \".1em .3em\",\n    borderRadius: \".3em\",\n    color: \"#db4c69\",\n    background: \"#1e1e1e\",\n  },\n  \".namespace\": {\n    Opacity: \".7\",\n  },\n  \"doctype.doctype-tag\": {\n    color: \"#569CD6\",\n  },\n  \"doctype.name\": {\n    color: \"#9cdcfe\",\n  },\n  comment: {\n    color: \"#6a9955\",\n  },\n  prolog: {\n    color: \"#6a9955\",\n  },\n  punctuation: {\n    color: \"#d4d4d4\",\n  },\n  \".language-html .language-css .token.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  \".language-html .language-javascript .token.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  property: {\n    color: \"#9cdcfe\",\n  },\n  tag: {\n    color: \"#569cd6\",\n  },\n  boolean: {\n    color: \"#569cd6\",\n  },\n  number: {\n    color: \"#b5cea8\",\n  },\n  constant: {\n    color: \"#9cdcfe\",\n  },\n  symbol: {\n    color: \"#b5cea8\",\n  },\n  inserted: {\n    color: \"#b5cea8\",\n  },\n  unit: {\n    color: \"#b5cea8\",\n  },\n  selector: {\n    color: \"#d7ba7d\",\n  },\n  \"attr-name\": {\n    color: \"#9cdcfe\",\n  },\n  string: {\n    color: \"#ce9178\",\n  },\n  char: {\n    color: \"#ce9178\",\n  },\n  builtin: {\n    color: \"#ce9178\",\n  },\n  deleted: {\n    color: \"#ce9178\",\n  },\n  \".language-css .token.string.url\": {\n    textDecoration: \"underline\",\n  },\n  operator: {\n    color: \"#d4d4d4\",\n  },\n  entity: {\n    color: \"#569cd6\",\n  },\n  \"operator.arrow\": {\n    color: \"#569CD6\",\n  },\n  atrule: {\n    color: \"#ce9178\",\n  },\n  \"atrule.rule\": {\n    color: \"#c586c0\",\n  },\n  \"atrule.url\": {\n    color: \"#9cdcfe\",\n  },\n  \"atrule.url.function\": {\n    color: \"#dcdcaa\",\n  },\n  \"atrule.url.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  keyword: {\n    color: \"#569CD6\",\n  },\n  \"keyword.module\": {\n    color: \"#c586c0\",\n  },\n  \"keyword.control-flow\": {\n    color: \"#c586c0\",\n  },\n  function: {\n    color: \"#dcdcaa\",\n  },\n  \"function.maybe-class-name\": {\n    color: \"#dcdcaa\",\n  },\n  regex: {\n    color: \"#d16969\",\n  },\n  important: {\n    color: \"#569cd6\",\n  },\n  italic: {\n    fontStyle: \"italic\",\n  },\n  \"class-name\": {\n    color: \"#4ec9b0\",\n  },\n  \"maybe-class-name\": {\n    color: \"#4ec9b0\",\n  },\n  console: {\n    color: \"#9cdcfe\",\n  },\n  parameter: {\n    color: \"#9cdcfe\",\n  },\n  interpolation: {\n    color: \"#9cdcfe\",\n  },\n  \"punctuation.interpolation-punctuation\": {\n    color: \"#569cd6\",\n  },\n  variable: {\n    color: \"#9cdcfe\",\n  },\n  \"imports.maybe-class-name\": {\n    color: \"#9cdcfe\",\n  },\n  \"exports.maybe-class-name\": {\n    color: \"#9cdcfe\",\n  },\n  escape: {\n    color: \"#d7ba7d\",\n  },\n  \"tag.punctuation\": {\n    color: \"#808080\",\n  },\n  cdata: {\n    color: \"#808080\",\n  },\n  \"attr-value\": {\n    color: \"#ce9178\",\n  },\n  \"attr-value.punctuation\": {\n    color: \"#ce9178\",\n  },\n  \"attr-value.punctuation.attr-equals\": {\n    color: \"#d4d4d4\",\n  },\n  namespace: {\n    color: \"#4ec9b0\",\n  },\n  'pre[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-css\"]': {\n    color: \"#ce9178\",\n  },\n  'code[class*=\"language-css\"]': {\n    color: \"#ce9178\",\n  },\n  'pre[class*=\"language-html\"]': {\n    color: \"#d4d4d4\",\n  },\n  'code[class*=\"language-html\"]': {\n    color: \"#d4d4d4\",\n  },\n  \".language-regex .token.anchor\": {\n    color: \"#dcdcaa\",\n  },\n  \".language-html .token.punctuation\": {\n    color: \"#808080\",\n  },\n  'pre[class*=\"language-\"] > code[class*=\"language-\"]': {\n    position: \"relative\",\n    zIndex: \"1\",\n  },\n  \".line-highlight.line-highlight\": {\n    background: \"#f7ebc6\",\n    boxShadow: \"inset 5px 0 0 #f7d87c\",\n    zIndex: \"0\",\n  },\n};\n", "import * as React from \"react\";\n\nexport interface useCopyToClipboardProps {\n  timeout?: number;\n}\n\nexport function useCopyToClipboard({ timeout = 2000 }: useCopyToClipboardProps) {\n  const [isCopied, setIsCopied] = React.useState<Boolean>(false);\n\n  const copyToClipboard = (value: string) => {\n    if (typeof window === \"undefined\" || !navigator.clipboard?.writeText) {\n      return;\n    }\n\n    if (!value) {\n      return;\n    }\n\n    navigator.clipboard.writeText(value).then(() => {\n      setIsCopied(true);\n\n      setTimeout(() => {\n        setIsCopied(false);\n      }, timeout);\n    });\n  };\n\n  return { isCopied, copyToClipboard };\n}\n", "import { AssistantMessageProps } from \"../props\";\nimport { useChatContext } from \"../ChatContext\";\nimport { Markdown } from \"../Markdown\";\nimport { useState } from \"react\";\n\nexport const AssistantMessage = (props: AssistantMessageProps) => {\n  const { icons, labels } = useChatContext();\n  const {\n    message,\n    isLoading,\n    subComponent,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n    isCurrentMessage,\n    markdownTagRenderers,\n  } = props;\n  const [copied, setCopied] = useState(false);\n\n  const handleCopy = () => {\n    if (message && onCopy) {\n      navigator.clipboard.writeText(message);\n      setCopied(true);\n      onCopy(message);\n      setTimeout(() => setCopied(false), 2000);\n    } else if (message) {\n      navigator.clipboard.writeText(message);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    }\n  };\n\n  const handleRegenerate = () => {\n    if (onRegenerate) {\n      onRegenerate();\n    }\n  };\n\n  const handleThumbsUp = () => {\n    if (onThumbsUp && message) {\n      onThumbsUp(message);\n    }\n  };\n\n  const handleThumbsDown = () => {\n    if (onThumbsDown && message) {\n      onThumbsDown(message);\n    }\n  };\n\n  const LoadingIcon = () => <span>{icons.activityIcon}</span>;\n\n  return (\n    <>\n      {(message || isLoading) && (\n        <div className=\"copilotKitMessage copilotKitAssistantMessage\">\n          {message && <Markdown content={message || \"\"} components={markdownTagRenderers} />}\n          {isLoading && <LoadingIcon />}\n\n          {message && !isLoading && (\n            <div\n              className={`copilotKitMessageControls ${isCurrentMessage ? \"currentMessage\" : \"\"}`}\n            >\n              <button\n                className=\"copilotKitMessageControlButton\"\n                onClick={handleRegenerate}\n                aria-label={labels.regenerateResponse}\n                title={labels.regenerateResponse}\n              >\n                {icons.regenerateIcon}\n              </button>\n              <button\n                className=\"copilotKitMessageControlButton\"\n                onClick={handleCopy}\n                aria-label={labels.copyToClipboard}\n                title={labels.copyToClipboard}\n              >\n                {copied ? (\n                  <span style={{ fontSize: \"10px\", fontWeight: \"bold\" }}>✓</span>\n                ) : (\n                  icons.copyIcon\n                )}\n              </button>\n              {onThumbsUp && (\n                <button\n                  className=\"copilotKitMessageControlButton\"\n                  onClick={handleThumbsUp}\n                  aria-label={labels.thumbsUp}\n                  title={labels.thumbsUp}\n                >\n                  {icons.thumbsUpIcon}\n                </button>\n              )}\n              {onThumbsDown && (\n                <button\n                  className=\"copilotKitMessageControlButton\"\n                  onClick={handleThumbsDown}\n                  aria-label={labels.thumbsDown}\n                  title={labels.thumbsDown}\n                >\n                  {icons.thumbsDownIcon}\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n      <div style={{ marginBottom: \"0.5rem\" }}>{subComponent}</div>\n    </>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,wBAA+D;;;ACD/D,mBAAyC;;;ACWnC;AA0GC,IAAM,WACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAyDK,IAAM,eACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAiBK,IAAM,YACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,yBAAwB;AAAA;AAC/E;;;ADWO,IAAAA,sBAAA;AApFF,IAAM,cAAc,aAAAC,QAAM,cAAuC,MAAS;AAE1E,SAAS,iBAA8B;AAC5C,QAAM,UAAU,aAAAA,QAAM,WAAW,WAAW;AAC5C,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AE5KA,IAAAC,gBAAyB;AACzB,4BAAmD;;;ACDnD,IAAAC,gBAAyB;AACzB,sCAA2C;;;ACD3C,IAAAC,SAAuB;AAMhB,SAAS,mBAAmB,EAAE,UAAU,IAAK,GAA4B;AAC9E,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAkB,KAAK;AAE7D,QAAM,kBAAkB,CAAC,UAAkB;AAT7C;AAUI,QAAI,OAAO,WAAW,eAAe,GAAC,eAAU,cAAV,mBAAqB,YAAW;AACpE;AAAA,IACF;AAEA,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAEA,cAAU,UAAU,UAAU,KAAK,EAAE,KAAK,MAAM;AAC9C,kBAAY,IAAI;AAEhB,iBAAW,MAAM;AACf,oBAAY,KAAK;AAAA,MACnB,GAAG,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAO,EAAE,UAAU,gBAAgB;AACrC;;;AD+DQ,IAAAC,sBAAA;AAxED,IAAM,uBAAoC;AAAA,EAC/C,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,GAAG;AAAA,EACH,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA;AAEP;AAEO,IAAM,uBAAuB,CAAC,QAAgB,YAAY,UAAU;AACzE,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO,YAAY,OAAO,YAAY,IAAI;AAC5C;AAEA,IAAM,gBAAuB,oBAAK,CAAC,EAAE,UAAU,MAAM,MAAM;AACzD,QAAM,EAAE,UAAU,gBAAgB,IAAI,mBAAmB,EAAE,SAAS,IAAK,CAAC;AAE1E,QAAM,iBAAiB,MAAM;AAC3B,QAAI,OAAO,WAAW,aAAa;AACjC;AAAA,IACF;AACA,UAAM,gBAAgB,qBAAqB,QAAQ,KAAK;AACxD,UAAM,oBAAoB,QAAQ,qBAAqB,GAAG,IAAI,IAAI;AAClE,UAAM,WAAW,OAAO,OAAO,mBAAyB,iBAAiB;AAEzE,QAAI,CAAC,UAAU;AAEb;AAAA,IACF;AAEA,UAAM,OAAO,IAAI,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,aAAa,CAAC;AACrD,UAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,UAAM,OAAO,SAAS,cAAc,GAAG;AACvC,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,MAAM,UAAU;AACrB,aAAS,KAAK,YAAY,IAAI;AAC9B,SAAK,MAAM;AACX,aAAS,KAAK,YAAY,IAAI;AAC9B,QAAI,gBAAgB,GAAG;AAAA,EACzB;AAEA,QAAM,SAAS,MAAM;AACnB,QAAI;AAAU;AACd,oBAAgB,KAAK;AAAA,EACvB;AAEA,SACE,8CAAC,SAAI,WAAU,uBACb;AAAA,kDAAC,SAAI,WAAU,8BACb;AAAA,mDAAC,UAAK,WAAU,sCAAsC,oBAAS;AAAA,MAC/D,8CAAC,SAAI,WAAU,qCACb;AAAA,qDAAC,YAAO,WAAU,oCAAmC,SAAS,gBAC3D,wBACH;AAAA,QACA,6CAAC,YAAO,WAAU,oCAAmC,SAAS,QAC3D,qBAAW,YAAY,UAC1B;AAAA,SACF;AAAA,OACF;AAAA,IACA;AAAA,MAAC,gCAAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,OAAO;AAAA,QACP,QAAO;AAAA,QACP,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,wBAAwB;AAAA,UACxB,yBAAyB;AAAA,QAC3B;AAAA,QAEC;AAAA;AAAA,IACH;AAAA,KACF;AAEJ,CAAC;AACD,UAAU,cAAc;AAQxB,IAAM,iBAAsB;AAAA,EAC1B,2BAA2B;AAAA,IACzB,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,sCAAsC;AAAA,IACpC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACrC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,wCAAwC;AAAA,IACtC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,yCAAyC;AAAA,IACvC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,wCAAwC;AAAA,IACtC,SAAS;AAAA,IACT,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,mDAAmD;AAAA,IACjD,OAAO;AAAA,EACT;AAAA,EACA,0DAA0D;AAAA,IACxD,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,mCAAmC;AAAA,IACjC,gBAAgB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,IACrB,OAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AAAA,IACtB,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,6BAA6B;AAAA,IAC3B,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,yCAAyC;AAAA,IACvC,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,gCAAgC;AAAA,IAC9B,OAAO;AAAA,EACT;AAAA,EACA,iCAAiC;AAAA,IAC/B,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sDAAsD;AAAA,IACpD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,kCAAkC;AAAA,IAChC,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,EACV;AACF;;;ADlZA,wBAAsB;AACtB,yBAAuB;AACvB,wBAAsB;AAKhB,IAAAC,sBAAA;AAHN,IAAM,oBAAgC;AAAA,EACpC,EAAE,IAAwB;AAAxB,iBAAE,WARN,IAQI,IAAe,kBAAf,IAAe,CAAb;AACF,WACE,6CAAC,oCAAE,WAAU,+BAAgC,QAA5C,EAAmD,QAAO,UAAS,KAAI,uBACrE,WACH;AAAA,EAEJ;AAAA;AAAA,EAEA,KAAK,IAA2C;AAA3C,iBAAE,YAAU,WAAW,OAhB9B,IAgBO,IAAkC,kBAAlC,IAAkC,CAAhC,YAAU,aAAW;AAC1B,QAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AAC9C,UAAI,SAAS,CAAC,KAAK,UAAK;AACtB,eACE;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,cACL,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,YACD;AAAA;AAAA,QAED;AAAA,MAEJ;AAEA,eAAS,CAAC,KAAK,qCAAW,IAAc,QAAQ,YAAO,QAAG;AAAA,IAC5D;AAEA,UAAM,QAAQ,iBAAiB,KAAK,aAAa,EAAE;AAEnD,QAAI,QAAQ;AACV,aACE,6CAAC,uCAAK,aAA0B,QAA/B,EACE,WACH;AAAA,IAEJ;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QAEC,UAAW,SAAS,MAAM,CAAC,KAAM;AAAA,QACjC,OAAO,OAAO,QAAQ,EAAE,QAAQ,OAAO,EAAE;AAAA,SACrC;AAAA,MAHC,KAAK,OAAO;AAAA,IAInB;AAAA,EAEJ;AAAA,EACA,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WArDT,IAqDO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA1DT,IA0DO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA/DT,IA+DO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WApET,IAoEO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAzET,IAyEO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA9ET,IA8EO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,GAAG,CAAC,OAAwB;AAAxB,iBAAE,WAnFR,IAmFM,IAAe,kBAAf,IAAe,CAAb;AACJ,wDAAC,oCAAE,WAAU,+BAAgC,QAA5C,EACE,WACH;AAAA;AAAA,EAEF,KAAK,CAAC,OAAwB;AAAxB,iBAAE,WAxFV,IAwFQ,IAAe,kBAAf,IAAe,CAAb;AACN,wDAAC,sCAAI,WAAU,+BAAgC,QAA9C,EACE,WACH;AAAA;AAAA,EAEF,YAAY,CAAC,OAAwB;AAAxB,iBAAE,WA7FjB,IA6Fe,IAAe,kBAAf,IAAe,CAAb;AACb,wDAAC,6CAAW,WAAU,+BAAgC,QAArD,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAlGT,IAkGO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAvGT,IAuGO,IAAe,kBAAf,IAAe,CAAb;AACL,wDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAEJ;AAEA,IAAM,4BAAqC;AAAA,EACzC,sBAAAC;AAAA,EACA,CAAC,WAAW,cACV,UAAU,aAAa,UAAU,YAAY,UAAU,eAAe,UAAU;AACpF;AAOO,IAAM,WAAW,CAAC,EAAE,SAAS,WAAW,MAAqB;AAClE,SACE,6CAAC,SAAI,WAAU,sBACb;AAAA,IAAC;AAAA;AAAA,MACC,YAAY,kCAAK,oBAAsB;AAAA,MACvC,eAAe,CAAC,kBAAAC,SAAW,mBAAAC,OAAU;AAAA,MACrC,eAAe,CAAC,kBAAAC,OAAS;AAAA,MAExB;AAAA;AAAA,EACH,GACF;AAEJ;;;AGlIA,IAAAC,gBAAyB;AAgDG,IAAAC,sBAAA;AA9CrB,IAAM,mBAAmB,CAAC,UAAiC;AAChE,QAAM,EAAE,OAAO,OAAO,IAAI,eAAe;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,QAAQ,SAAS,QAAI,wBAAS,KAAK;AAE1C,QAAM,aAAa,MAAM;AACvB,QAAI,WAAW,QAAQ;AACrB,gBAAU,UAAU,UAAU,OAAO;AACrC,gBAAU,IAAI;AACd,aAAO,OAAO;AACd,iBAAW,MAAM,UAAU,KAAK,GAAG,GAAI;AAAA,IACzC,WAAW,SAAS;AAClB,gBAAU,UAAU,UAAU,OAAO;AACrC,gBAAU,IAAI;AACd,iBAAW,MAAM,UAAU,KAAK,GAAG,GAAI;AAAA,IACzC;AAAA,EACF;AAEA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,cAAc;AAChB,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,cAAc,SAAS;AACzB,iBAAW,OAAO;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,gBAAgB,SAAS;AAC3B,mBAAa,OAAO;AAAA,IACtB;AAAA,EACF;AAEA,QAAM,cAAc,MAAM,6CAAC,UAAM,gBAAM,cAAa;AAEpD,SACE,8EACI;AAAA,gBAAW,cACX,8CAAC,SAAI,WAAU,gDACZ;AAAA,iBAAW,6CAAC,YAAS,SAAS,WAAW,IAAI,YAAY,sBAAsB;AAAA,MAC/E,aAAa,6CAAC,eAAY;AAAA,MAE1B,WAAW,CAAC,aACX;AAAA,QAAC;AAAA;AAAA,UACC,WAAW,6BAA6B,mBAAmB,mBAAmB;AAAA,UAE9E;AAAA;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,gBAAM;AAAA;AAAA,YACT;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,mBACC,6CAAC,UAAK,OAAO,EAAE,UAAU,QAAQ,YAAY,OAAO,GAAG,oBAAC,IAExD,MAAM;AAAA;AAAA,YAEV;AAAA,YACC,cACC;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,gBAAM;AAAA;AAAA,YACT;AAAA,YAED,gBACC;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,gBAAM;AAAA;AAAA,YACT;AAAA;AAAA;AAAA,MAEJ;AAAA,OAEJ;AAAA,IAEF,6CAAC,SAAI,OAAO,EAAE,cAAc,SAAS,GAAI,wBAAa;AAAA,KACxD;AAEJ;;;ANpFY,IAAAC,sBAAA;AAvBL,SAAS,wBAAwB,IAGjB;AAHiB,eACtC;AAAA,sBAAAC,oBAAmB;AAAA,EALrB,IAIwC,IAEnC,kBAFmC,IAEnC;AAAA,IADH;AAAA;AAGA,QAAM,EAAE,oBAAoB,QAAI,qCAAkB;AAClD,QAAM,EAAE,SAAS,YAAY,OAAO,iBAAiB,IAAI;AAEzD,MAAI,QAAQ,oBAAoB,GAAG;AACjC,QAAI;AAEJ,QAAI,oBAAoB,YAAY,MAAM;AACxC,eACE,oBAAoB,QAAQ,oBAC1B,GAAG,QAAQ,aAAa,QAAQ,UAClC,KAAK,oBAAoB,QAAQ,oBAAoB,GAAG,QAAQ,kBAAkB;AAAA,IACtF;AAEA,QAAI,QAAQ;AAEV,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,oBAAoB,YAAY;AAClC,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,SAAS;AAAA,cACT,qBAAkB;AAAA,cAElB,WAAW;AAAA,cACX,cAAc;AAAA;AAAA,YAFT;AAAA,UAGP;AAAA,QAEJ,OAEK;AACH,iBAAO;AAAA,QACT;AAAA,MACF,OAEK;AACH,cAAM,QAAQ,QAAQ;AAEtB,YAAI,SAAS,QAAQ,SAAS,eAAe;AAE7C,cAAM,WAAW,OAAO;AAAA,UACtB;AAAA,UACA;AAAA,UACA,UAAU,QAAQ;AAAA,QACpB,CAAC;AAGD,YAAI,CAAC,YAAY,WAAW,YAAY;AACtC,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,YAAY,oBAAoB,YAAY;AAC/C,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,qBAAkB;AAAA,cAElB,SAAS;AAAA,cACT,WAAW;AAAA,cACX,cAAc;AAAA;AAAA,YAHT;AAAA,UAIP;AAAA,QAEJ,WAAW,CAAC,UAAU;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,aAAa,UAAU;AAChC,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,SAAS;AAAA,cACT,WAAW;AAAA,cACX,cAAc;AAAA,cACd,qBAAkB;AAAA;AAAA,YACb;AAAA,UACP;AAAA,QAEJ,OAAO;AACL,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,qBAAkB;AAAA,cAElB,WAAW;AAAA,cACX,cAAc;AAAA,cACd,cAAc;AAAA;AAAA,YAHT;AAAA,UAIP;AAAA,QAEJ;AAAA,MACF;AAAA,IACF,WAES,CAAC,cAAc,CAAC,kBAAkB;AAEzC,aAAO;AAAA,IACT,OAAO;AAEL,aACE;AAAA,QAACA;AAAA,QAAA;AAAA,UACC,SAAS;AAAA,UACT,WAAW;AAAA,UACX,cAAc;AAAA,UACd,qBAAkB;AAAA;AAAA,QACb;AAAA,MACP;AAAA,IAEJ;AAAA,EACF;AACF;", "names": ["import_jsx_runtime", "React", "import_react", "import_react", "React", "import_jsx_runtime", "Syntax<PERSON><PERSON><PERSON><PERSON>", "import_jsx_runtime", "ReactMarkdown", "remarkGfm", "remarkMath", "rehypeRaw", "import_react", "import_jsx_runtime", "import_jsx_runtime", "AssistantM<PERSON><PERSON>"]}