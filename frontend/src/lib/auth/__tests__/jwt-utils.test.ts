/**
 * Comprehensive JWT Utilities Tests
 * 
 * Tests for JWT token extraction, validation, and utility functions
 * covering security scenarios, edge cases, and error handling.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest } from 'next/server';
import { SignJWT, jwtVerify, importJWK } from 'jose';
import {
  extractJwtFromRequest,
  validateJwt,
  extractOrganizationId,
  extractUserId,
  generateCopilotAuthHeader,
  JWTValidationError,
  type JWTPayload
} from '../jwt-utils';

// Mock environment variables
const mockEnv = {
  SUPABASE_JWT_SECRET: 'test-jwt-secret-key-32-characters-long',
};

describe('JWT Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Set up environment variables
    process.env.SUPABASE_JWT_SECRET = mockEnv.SUPABASE_JWT_SECRET;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('extractJwtFromRequest', () => {
    it('should extract JWT from valid Authorization header', () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token'
        }
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBe('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token');
    });

    it('should return undefined when Authorization header is missing', () => {
      const request = new NextRequest('https://example.com');
      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });

    it('should return undefined when Authorization header is not Bearer type', () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'Authorization': 'Basic dXNlcjpwYXNzd29yZA=='
        }
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });

    it('should return undefined when Authorization header is malformed', () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'Authorization': 'Bearer'
        }
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });

    it('should handle empty Authorization header', () => {
      const request = new NextRequest('https://example.com', {
        headers: {
          'Authorization': ''
        }
      });

      const token = extractJwtFromRequest(request);
      expect(token).toBeUndefined();
    });
  });

  describe('validateJwt', () => {
    it('should throw JWTValidationError for malformed token', async () => {
      await expect(validateJwt('invalid.token.here')).rejects.toThrow('Invalid JWT token');
    });

    it('should throw JWTValidationError when JWT secret is missing', async () => {
      delete process.env.SUPABASE_JWT_SECRET;

      await expect(validateJwt('any.token.here')).rejects.toThrow(
        new JWTValidationError('JWT secret not configured', 500)
      );
    });

    it('should throw JWTValidationError for empty token', async () => {
      await expect(validateJwt('')).rejects.toThrow('Invalid JWT token');
    });

    it('should throw JWTValidationError for null token', async () => {
      await expect(validateJwt(null as any)).rejects.toThrow('Invalid JWT token');
    });

    it('should throw JWTValidationError for undefined token', async () => {
      await expect(validateJwt(undefined as any)).rejects.toThrow('Invalid JWT token');
    });

    // Note: Testing with actual valid/invalid JWT tokens would require
    // complex setup with proper key management. For now, we test the
    // error handling paths which are most critical for security.
  });

  describe('extractOrganizationId', () => {
    it('should extract organization ID from tenant_id field', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600,
        tenant_id: 'tenant-456'
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBe('tenant-456');
    });

    it('should extract organization ID from org_id field when tenant_id is missing', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600,
        org_id: 'org-789'
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBe('org-789');
    });

    it('should extract organization ID from user_metadata when other fields are missing', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600,
        user_metadata: {
          organization_id: 'meta-org-123'
        }
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBe('meta-org-123');
    });

    it('should prioritize tenant_id over other fields', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600,
        tenant_id: 'tenant-456',
        org_id: 'org-789',
        user_metadata: {
          organization_id: 'meta-org-123'
        }
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBe('tenant-456');
    });

    it('should return undefined when no organization ID is found', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600
      };

      const orgId = extractOrganizationId(payload);
      expect(orgId).toBeUndefined();
    });
  });

  describe('extractUserId', () => {
    it('should extract user ID from sub field', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600
      };

      const userId = extractUserId(payload);
      expect(userId).toBe('user-123');
    });

    it('should handle UUID format user IDs', () => {
      const payload: JWTPayload = {
        sub: '550e8400-e29b-41d4-a716-************',
        iat: Date.now(),
        exp: Date.now() + 3600
      };

      const userId = extractUserId(payload);
      expect(userId).toBe('550e8400-e29b-41d4-a716-************');
    });
  });

  describe('generateCopilotAuthHeader', () => {
    it('should generate auth header with organization and user context', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600,
        tenant_id: 'tenant-456',
        user_metadata: {
          role: 'attorney'
        }
      };

      const authHeader = generateCopilotAuthHeader(payload);
      
      expect(authHeader).toContain('Bearer');
      
      // Parse the JSON context from the header
      const contextJson = authHeader.replace('Bearer ', '');
      const context = JSON.parse(contextJson);
      
      expect(context.user_id).toBe('user-123');
      expect(context.organization_id).toBe('tenant-456');
      expect(context.role).toBe('attorney');
      expect(context.authenticated).toBe(true);
    });

    it('should use default role when not specified', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600,
        tenant_id: 'tenant-456'
      };

      const authHeader = generateCopilotAuthHeader(payload);
      const contextJson = authHeader.replace('Bearer ', '');
      const context = JSON.parse(contextJson);
      
      expect(context.role).toBe('user');
    });

    it('should handle missing organization ID gracefully', () => {
      const payload: JWTPayload = {
        sub: 'user-123',
        iat: Date.now(),
        exp: Date.now() + 3600
      };

      const authHeader = generateCopilotAuthHeader(payload);
      const contextJson = authHeader.replace('Bearer ', '');
      const context = JSON.parse(contextJson);
      
      expect(context.user_id).toBe('user-123');
      expect(context.organization_id).toBeUndefined();
      expect(context.authenticated).toBe(true);
    });
  });

  describe('JWTValidationError', () => {
    it('should create error with default status 401', () => {
      const error = new JWTValidationError('Test error');
      
      expect(error.message).toBe('Test error');
      expect(error.status).toBe(401);
      expect(error.name).toBe('JWTValidationError');
    });

    it('should create error with custom status', () => {
      const error = new JWTValidationError('Server error', 500);
      
      expect(error.message).toBe('Server error');
      expect(error.status).toBe(500);
      expect(error.name).toBe('JWTValidationError');
    });
  });
});
