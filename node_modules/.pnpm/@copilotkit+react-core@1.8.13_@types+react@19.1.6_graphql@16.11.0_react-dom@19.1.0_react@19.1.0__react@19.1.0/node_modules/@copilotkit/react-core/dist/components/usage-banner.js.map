{"version": 3, "sources": ["../../src/components/usage-banner.tsx"], "sourcesContent": ["import { Severity, CopilotKitError, ERROR_NAMES } from \"@copilotkit/shared\";\n\ninterface UsageBannerProps {\n  severity?: Severity;\n  message?: string;\n  icon?: React.ReactNode;\n  actions?: {\n    primary?: {\n      label: string;\n      onClick: () => void;\n    };\n    secondary?: {\n      label: string;\n      onClick: () => void;\n    };\n  };\n}\n\nconst defaultIcons: Record<Severity, JSX.Element> = {\n  [Severity.Error]: (\n    <svg\n      viewBox=\"0 0 24 24\"\n      width=\"20\"\n      height=\"20\"\n      stroke=\"currentColor\"\n      strokeWidth=\"2\"\n      fill=\"none\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" />\n      <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" />\n      <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" />\n    </svg>\n  ),\n};\n\nexport function UsageBanner({\n  severity = Severity.Error,\n  message = \"\",\n  icon,\n  actions,\n}: UsageBannerProps) {\n  if (!message || !severity) {\n    return null;\n  }\n\n  const Icon = icon || defaultIcons[severity];\n\n  const bgColor = {\n    info: \"#dbeafe\",\n    warning: \"#fef3c7\",\n    error: \"#fee2e2\",\n  }[severity];\n\n  const textColor = {\n    info: \"#1e40af\",\n    warning: \"#854d0e\",\n    error: \"#991b1b\",\n  }[severity];\n\n  const iconColor = {\n    info: \"#3b82f6\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  }[severity];\n\n  const primaryButtonColor = {\n    info: \"#3b82f6\",\n    warning: \"#eab308\",\n    error: \"#ef4444\",\n  }[severity];\n\n  const primaryButtonHoverColor = {\n    info: \"#2563eb\",\n    warning: \"#ca8a04\",\n    error: \"#dc2626\",\n  }[severity];\n\n  return (\n    <div\n      style={{\n        position: \"fixed\",\n        bottom: \"16px\",\n        left: \"50%\",\n        transform: \"translateX(-50%)\",\n        maxWidth: \"90%\",\n        zIndex: 9999,\n      }}\n    >\n      <div\n        style={{\n          display: \"flex\",\n          flexWrap: \"wrap\",\n          alignItems: \"center\",\n          gap: \"12px\",\n          borderRadius: \"9999px\",\n          border: \"1px solid #e5e7eb\",\n          backgroundColor: bgColor,\n          padding: \"8px 16px\",\n          boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\",\n        }}\n      >\n        <div style={{ color: iconColor }}>{Icon}</div>\n        <span\n          style={{\n            flex: 1,\n            fontSize: \"14px\",\n            fontWeight: 500,\n            color: textColor,\n            whiteSpace: \"normal\",\n            wordBreak: \"break-word\",\n          }}\n        >\n          {message}\n        </span>\n        <div\n          style={{\n            display: \"flex\",\n            gap: \"8px\",\n            flexWrap: \"wrap\",\n          }}\n        >\n          {actions?.secondary && (\n            <button\n              onClick={actions.secondary.onClick}\n              style={{\n                borderRadius: \"9999px\",\n                padding: \"4px 12px\",\n                fontSize: \"14px\",\n                fontWeight: 500,\n                color: textColor,\n                backgroundColor: \"transparent\",\n                border: \"none\",\n                cursor: \"pointer\",\n                transition: \"background-color 0.2s\",\n              }}\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = \"rgba(255,255,255,0.5)\")}\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = \"transparent\")}\n            >\n              {actions.secondary.label}\n            </button>\n          )}\n          {actions?.primary && (\n            <button\n              onClick={actions.primary.onClick}\n              style={{\n                borderRadius: \"9999px\",\n                padding: \"4px 12px\",\n                fontSize: \"14px\",\n                fontWeight: 500,\n                color: \"#fff\",\n                backgroundColor: primaryButtonColor,\n                border: \"none\",\n                cursor: \"pointer\",\n                transition: \"background-color 0.2s\",\n              }}\n              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = primaryButtonHoverColor)}\n              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = primaryButtonColor)}\n            >\n              {actions.primary.label}\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport function renderCopilotKitUsage(error: CopilotKitError) {\n  switch (error.name) {\n    case ERROR_NAMES.CONFIGURATION_ERROR:\n      return <UsageBanner severity={error.severity} message={error.message} />;\n    case ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR:\n      return (\n        <UsageBanner\n          severity={error.severity}\n          message={error.message}\n          actions={{\n            primary: {\n              label: \"Sign In\",\n              onClick: () => {\n                window.location.href = \"https://cloud.copilotkit.ai\";\n              },\n            },\n          }}\n        />\n      );\n    case ERROR_NAMES.UPGRADE_REQUIRED_ERROR:\n      return (\n        <UsageBanner\n          severity={error.severity}\n          message={error.message}\n          actions={{\n            primary: {\n              label: \"Upgrade\",\n              onClick: () => {\n                window.location.href = \"https://copilotkit.ai/\";\n              },\n            },\n          }}\n        />\n      );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAuD;AAoBnD;AAFJ,IAAM,eAA8C;AAAA,EAClD,CAAC,uBAAS,KAAK,GACb;AAAA,IAAC;AAAA;AAAA,MACC,SAAQ;AAAA,MACR,OAAM;AAAA,MACN,QAAO;AAAA,MACP,QAAO;AAAA,MACP,aAAY;AAAA,MACZ,MAAK;AAAA,MACL,eAAc;AAAA,MACd,gBAAe;AAAA,MAEf;AAAA,oDAAC,YAAO,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK;AAAA,QAC/B,4CAAC,UAAK,IAAG,MAAK,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK;AAAA,QACpC,4CAAC,UAAK,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,MAAK;AAAA;AAAA;AAAA,EACtC;AAEJ;AAEO,SAAS,YAAY;AAAA,EAC1B,WAAW,uBAAS;AAAA,EACpB,UAAU;AAAA,EACV;AAAA,EACA;AACF,GAAqB;AACnB,MAAI,CAAC,WAAW,CAAC,UAAU;AACzB,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,QAAQ,aAAa,QAAQ;AAE1C,QAAM,UAAU;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,qBAAqB;AAAA,IACzB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,QAAM,0BAA0B;AAAA,IAC9B,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,EACT,EAAE,QAAQ;AAEV,SACE;AAAA,IAAC;AAAA;AAAA,MACC,OAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,MAEA;AAAA,QAAC;AAAA;AAAA,UACC,OAAO;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,KAAK;AAAA,YACL,cAAc;AAAA,YACd,QAAQ;AAAA,YACR,iBAAiB;AAAA,YACjB,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UAEA;AAAA,wDAAC,SAAI,OAAO,EAAE,OAAO,UAAU,GAAI,gBAAK;AAAA,YACxC;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,MAAM;AAAA,kBACN,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,YAAY;AAAA,kBACZ,WAAW;AAAA,gBACb;AAAA,gBAEC;AAAA;AAAA,YACH;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,OAAO;AAAA,kBACL,SAAS;AAAA,kBACT,KAAK;AAAA,kBACL,UAAU;AAAA,gBACZ;AAAA,gBAEC;AAAA,sDAAS,cACR;AAAA,oBAAC;AAAA;AAAA,sBACC,SAAS,QAAQ,UAAU;AAAA,sBAC3B,OAAO;AAAA,wBACL,cAAc;AAAA,wBACd,SAAS;AAAA,wBACT,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO;AAAA,wBACP,iBAAiB;AAAA,wBACjB,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA,aAAa,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAC7D,YAAY,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAE3D,kBAAQ,UAAU;AAAA;AAAA,kBACrB;AAAA,mBAED,mCAAS,YACR;AAAA,oBAAC;AAAA;AAAA,sBACC,SAAS,QAAQ,QAAQ;AAAA,sBACzB,OAAO;AAAA,wBACL,cAAc;AAAA,wBACd,SAAS;AAAA,wBACT,UAAU;AAAA,wBACV,YAAY;AAAA,wBACZ,OAAO;AAAA,wBACP,iBAAiB;AAAA,wBACjB,QAAQ;AAAA,wBACR,QAAQ;AAAA,wBACR,YAAY;AAAA,sBACd;AAAA,sBACA,aAAa,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAC7D,YAAY,CAAC,MAAO,EAAE,cAAc,MAAM,kBAAkB;AAAA,sBAE3D,kBAAQ,QAAQ;AAAA;AAAA,kBACnB;AAAA;AAAA;AAAA,YAEJ;AAAA;AAAA;AAAA,MACF;AAAA;AAAA,EACF;AAEJ;AAEO,SAAS,sBAAsB,OAAwB;AAC5D,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK,0BAAY;AACf,aAAO,4CAAC,eAAY,UAAU,MAAM,UAAU,SAAS,MAAM,SAAS;AAAA,IACxE,KAAK,0BAAY;AACf,aACE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,YACP,SAAS;AAAA,cACP,OAAO;AAAA,cACP,SAAS,MAAM;AACb,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,IAEJ,KAAK,0BAAY;AACf,aACE;AAAA,QAAC;AAAA;AAAA,UACC,UAAU,MAAM;AAAA,UAChB,SAAS,MAAM;AAAA,UACf,SAAS;AAAA,YACP,SAAS;AAAA,cACP,OAAO;AAAA,cACP,SAAS,MAAM;AACb,uBAAO,SAAS,OAAO;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,EAEN;AACF;", "names": []}