# Prospect Management Implementation Guide

This guide provides comprehensive instructions for implementing the prospect management system for your PI Lawyer AI website's waitlist/newsletter signup functionality.

## Overview

The prospect management system handles the complete lifecycle from anonymous website visitors to authenticated users, maintaining GDPR compliance and seamless integration with your existing authentication system.

### Key Features

- **Pre-authentication data collection** via public schema
- **GDPR-compliant data handling** with consent tracking
- **Email verification workflows** for data quality
- **Automatic conversion** from prospect to authenticated user
- **Comprehensive tracking** of prospect interactions
- **Admin dashboard** for prospect management
- **Secure API endpoints** with proper validation

## Database Schema Implementation

### 1. Run Database Migrations

Execute the migrations in order on your Supabase project:

```bash
# 1. Create the prospects schema and tables
supabase db push --file supabase/migrations/20250127_create_prospects_schema.sql

# 2. Set up Row Level Security policies
supabase db push --file supabase/migrations/20250127_create_prospects_rls.sql

# 3. Create conversion functions and triggers
supabase db push --file supabase/migrations/20250127_create_prospect_conversion_functions.sql
```

### 2. Verify Schema Creation

Connect to your Supabase database and verify the tables exist:

```sql
-- Check if tables were created
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('prospects', 'prospect_interactions', 'prospect_notes');

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename LIKE 'prospect%';
```

## API Endpoints Implementation

### 1. Prospect Signup Endpoint

**File**: `frontend/src/app/api/prospects/signup/route.ts`

**Usage**:
```typescript
// Frontend form submission
const response = await fetch('/api/prospects/signup', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '+1234567890',
    signupSource: 'website',
    practiceAreaInterest: ['personal_injury'],
    caseUrgency: 'within_month',
    gdprConsent: true,
    turnstileToken: 'turnstile-token-here'
  })
});
```

### 2. Email Verification Endpoints

**File**: `frontend/src/app/api/prospects/verify-email/route.ts`

**Send Verification**:
```typescript
// Send verification email
const response = await fetch('/api/prospects/verify-email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: '<EMAIL>' })
});
```

**Verify Email**:
```typescript
// Verify email with token
const response = await fetch('/api/prospects/verify-email', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ token: 'verification-token' })
});
```

### 3. Prospect Management (Admin)

**File**: `frontend/src/app/api/prospects/manage/route.ts`

**List Prospects**:
```typescript
// Get prospects with filtering
const response = await fetch('/api/prospects/manage?page=1&limit=20&status=active&emailVerified=true');
```

## Environment Variables

Add these variables to your `.env.local` and Vercel deployment:

```bash
# Required for prospect management
NEXT_PUBLIC_SITE_URL=https://your-website.com
PROSPECT_EMAIL_SERVICE=sendgrid
SENDGRID_API_KEY=your_sendgrid_api_key

# GDPR Compliance
GDPR_DATA_RETENTION_DAYS=365
AUTO_CLEANUP_PROSPECTS=true

# Existing variables (ensure these are set)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key
```

## Frontend Integration

### 1. Create Signup Form Component

```typescript
// components/ProspectSignupForm.tsx
import { useState } from 'react';
import { z } from 'zod';

const signupSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  gdprConsent: z.boolean().refine(val => val === true),
});

export function ProspectSignupForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    
    try {
      const data = {
        email: formData.get('email'),
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        practiceAreaInterest: ['personal_injury'],
        gdprConsent: formData.get('gdprConsent') === 'on',
        signupSource: 'website',
      };

      const response = await fetch('/api/prospects/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage('Thank you! Please check your email to verify your address.');
      } else {
        setMessage(result.error || 'Something went wrong');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form action={handleSubmit} className="space-y-4">
      <input
        type="email"
        name="email"
        placeholder="Email address"
        required
        className="w-full p-3 border rounded"
      />
      <input
        type="text"
        name="firstName"
        placeholder="First name"
        required
        className="w-full p-3 border rounded"
      />
      <input
        type="text"
        name="lastName"
        placeholder="Last name"
        required
        className="w-full p-3 border rounded"
      />
      <label className="flex items-center space-x-2">
        <input type="checkbox" name="gdprConsent" required />
        <span>I consent to receive marketing communications</span>
      </label>
      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-blue-600 text-white p-3 rounded disabled:opacity-50"
      >
        {isSubmitting ? 'Signing up...' : 'Join Waitlist'}
      </button>
      {message && <p className="text-sm text-gray-600">{message}</p>}
    </form>
  );
}
```

### 2. Email Verification Page

```typescript
// app/verify-email/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';

export default function VerifyEmailPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (!token) {
      setStatus('error');
      setMessage('Invalid verification link');
      return;
    }

    const verifyEmail = async () => {
      try {
        const response = await fetch('/api/prospects/verify-email', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token }),
        });

        const result = await response.json();

        if (result.success) {
          setStatus('success');
          setMessage('Email verified successfully! You can now close this page.');
        } else {
          setStatus('error');
          setMessage(result.error || 'Verification failed');
        }
      } catch (error) {
        setStatus('error');
        setMessage('Network error during verification');
      }
    };

    verifyEmail();
  }, [token]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow">
        <h1 className="text-2xl font-bold mb-4">Email Verification</h1>
        
        {status === 'loading' && (
          <p>Verifying your email address...</p>
        )}
        
        {status === 'success' && (
          <div className="text-green-600">
            <p>{message}</p>
          </div>
        )}
        
        {status === 'error' && (
          <div className="text-red-600">
            <p>{message}</p>
          </div>
        )}
      </div>
    </div>
  );
}
```

## Security Considerations

### 1. Data Protection

- **Encryption**: All PII is stored with proper constraints and validation
- **Access Control**: RLS policies ensure proper data isolation
- **GDPR Compliance**: Automatic data retention and cleanup
- **Audit Trail**: All interactions are logged for compliance

### 2. API Security

- **Rate Limiting**: Implement rate limiting on signup endpoints
- **CAPTCHA**: Turnstile integration prevents bot signups
- **Input Validation**: Zod schemas validate all inputs
- **CORS**: Configure proper CORS headers for production

### 3. Email Security

- **Verification**: Email verification prevents fake signups
- **Token Expiry**: Verification tokens expire after 24 hours
- **Secure Headers**: Use proper email security headers

## Conversion Flow

### Automatic Conversion

When a prospect registers as an authenticated user:

1. **Trigger**: `auth.users` INSERT trigger fires
2. **Function**: `handle_prospect_conversion()` executes
3. **Lookup**: Finds prospect by email
4. **Update**: Marks prospect as converted
5. **Create**: Creates `tenants.users` record with prospect data
6. **Log**: Records conversion in interactions table

### Manual Conversion

For admin-initiated conversions:

```sql
-- Convert prospect to user manually
SELECT public.convert_prospect_to_user(
  '<EMAIL>',
  'auth-user-uuid',
  'tenant-uuid'
);
```

## Monitoring and Maintenance

### 1. Regular Cleanup

Set up a cron job to clean up expired prospects:

```sql
-- Run daily to clean up expired prospects
SELECT public.cleanup_expired_prospects();
```

### 2. Analytics Queries

```sql
-- Prospect conversion rate
SELECT 
  COUNT(*) FILTER (WHERE status = 'converted') * 100.0 / COUNT(*) as conversion_rate
FROM public.prospects 
WHERE created_at >= NOW() - INTERVAL '30 days';

-- Signup sources performance
SELECT 
  signup_source,
  COUNT(*) as total_signups,
  COUNT(*) FILTER (WHERE status = 'converted') as conversions,
  COUNT(*) FILTER (WHERE email_verified = true) as verified
FROM public.prospects 
GROUP BY signup_source;
```

## Deployment Checklist

- [ ] Database migrations applied
- [ ] Environment variables configured
- [ ] Email service configured (SendGrid/Resend)
- [ ] Turnstile keys configured
- [ ] CORS settings updated
- [ ] Rate limiting configured
- [ ] Monitoring dashboards set up
- [ ] GDPR compliance verified
- [ ] Backup procedures in place
- [ ] Security audit completed

## Troubleshooting

### Common Issues

1. **Prospect not converting**: Check email match and trigger function
2. **Email verification failing**: Verify token generation and expiry
3. **RLS blocking access**: Check policies and user roles
4. **GDPR compliance**: Ensure consent tracking is working

### Debug Queries

```sql
-- Check prospect status
SELECT * FROM public.prospects WHERE email = '<EMAIL>';

-- Check conversion triggers
SELECT * FROM pg_trigger WHERE tgname = 'trigger_prospect_conversion';

-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'prospects';
```
